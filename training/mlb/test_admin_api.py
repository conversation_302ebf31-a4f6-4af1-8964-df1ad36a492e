#!/usr/bin/env python3
"""
測試管理後台API
調查「Load failed」錯誤原因
"""

from app import create_app
import json
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_admin_fetch_api():
    """測試管理後台的自定義抓取API"""
    print("🔍 測試管理後台自定義抓取API...")
    print("=" * 80)
    
    app = create_app()
    
    with app.test_client() as client:
        
        # 測試自定義範圍抓取API
        print("\n📊 測試自定義範圍抓取API...")
        
        test_data = {
            "start_date": "2025-08-29",
            "end_date": "2025-08-30",
            "market_type": "both"
        }
        
        response = client.post('/admin/api/fetch-custom-range',
                             json=test_data,
                             content_type='application/json')
        
        print(f"   狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                print(f"   響應數據: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('success'):
                    print(f"   ✅ API調用成功")
                    print(f"      抓取記錄數: {data.get('total_records', 0)}")
                    print(f"      日期範圍: {data.get('date_range', 'N/A')}")
                else:
                    print(f"   ❌ API調用失敗: {data.get('message', '未知錯誤')}")
            except Exception as e:
                print(f"   ❌ 響應數據解析失敗: {e}")
                print(f"   原始響應: {response.get_data(as_text=True)}")
        else:
            print(f"   ❌ API無法訪問: {response.status_code}")
            try:
                error_data = response.get_json()
                print(f"   錯誤詳情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"   原始錯誤響應: {response.get_data(as_text=True)}")

def test_real_betting_odds_fetcher():
    """測試RealBettingOddsFetcher是否正常工作"""
    print("\n🎯 測試RealBettingOddsFetcher...")
    
    try:
        from models.real_betting_odds_fetcher import RealBettingOddsFetcher
        from datetime import date
        
        fetcher = RealBettingOddsFetcher()
        print("   ✅ RealBettingOddsFetcher實例化成功")
        
        # 測試獲取今天的數據
        test_date = date(2025, 8, 29)
        print(f"   🔍 測試獲取 {test_date} 的數據...")
        
        result = fetcher.get_mlb_odds_today(test_date)
        print(f"   結果類型: {type(result)}")
        print(f"   結果鍵: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            print(f"      成功: {result.get('success', 'N/A')}")
            print(f"      遊戲數: {len(result.get('games', []))}")
            
            if result.get('games'):
                first_game = result.get('games')[0]
                print(f"      第一場比賽示例: {first_game}")
            
        else:
            print(f"   ⚠️ 返回結果不是字典類型: {result}")
            
    except ImportError as e:
        print(f"   ❌ 無法導入RealBettingOddsFetcher: {e}")
    except Exception as e:
        print(f"   ❌ 測試RealBettingOddsFetcher失敗: {e}")

def test_database_connection():
    """測試數據庫連接和BettingOdds表"""
    print("\n🗄️ 測試數據庫連接...")
    
    app = create_app()
    
    with app.app_context():
        try:
            from models.database import db, BettingOdds
            
            # 測試數據庫連接
            result = db.session.execute(db.text("SELECT 1")).fetchone()
            print("   ✅ 數據庫連接正常")
            
            # 測試BettingOdds表
            count = BettingOdds.query.count()
            print(f"   📊 BettingOdds表記錄數: {count}")
            
            # 測試插入一條測試記錄
            from datetime import datetime, date
            
            test_odds = BettingOdds(
                game_id='TEST_GAME_20250830',
                bookmaker='test_bookmaker',
                market_type='spreads',
                home_spread_point=1.5,
                away_spread_point=-1.5,
                home_spread_price=110,
                away_spread_price=-110,
                data_source='test',
                odds_time=datetime.now(),
                created_at=datetime.now()
            )
            
            db.session.add(test_odds)
            db.session.commit()
            print("   ✅ 測試記錄插入成功")
            
            # 清理測試記錄
            BettingOdds.query.filter_by(game_id='TEST_GAME_20250830').delete()
            db.session.commit()
            print("   🧹 測試記錄已清理")
            
        except Exception as e:
            print(f"   ❌ 數據庫測試失敗: {e}")
            try:
                db.session.rollback()
            except:
                pass

if __name__ == "__main__":
    test_admin_fetch_api()
    test_real_betting_odds_fetcher()
    test_database_connection()