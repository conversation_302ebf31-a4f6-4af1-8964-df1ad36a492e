#!/usr/bin/env python3
"""
測試球隊統計修復
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import TeamStats, Team, db
from models.ml_predictor import MLBPredictor
from datetime import date
import pandas as pd

def test_team_stats_fix():
    """測試球隊統計修復"""
    app = create_app()
    with app.app_context():
        print("🔧 測試球隊統計修復")
        print("=" * 50)
        
        # 1. 檢查TeamStats數據
        print("\n📊 檢查TeamStats數據:")
        total_stats = TeamStats.query.count()
        print(f"總統計記錄數: {total_stats}")
        
        if total_stats == 0:
            print("❌ 沒有TeamStats數據!")
            return False
        
        # 檢查不同球隊的統計
        sample_teams = ['LAD', 'NYY', 'OAK', 'MIA', 'HOU']
        print(f"\n🏟️ 檢查樣本球隊統計:")
        
        team_data = []
        for team_code in sample_teams:
            team = Team.query.filter_by(team_code=team_code).first()
            if team:
                stats = TeamStats.query.filter_by(team_id=team.team_id).first()
                if stats:
                    team_data.append({
                        'team': team_code,
                        'runs_scored': stats.runs_scored,
                        'era': stats.era,
                        'win_pct': stats.win_percentage
                    })
                    print(f"{team_code}: 得分={stats.runs_scored:.2f}, ERA={stats.era:.2f}, 勝率={stats.win_percentage:.3f}")
                else:
                    print(f"{team_code}: ❌ 沒有統計數據")
            else:
                print(f"{team_code}: ❌ 找不到球隊")
        
        # 分析統計差異
        if team_data:
            runs_list = [t['runs_scored'] for t in team_data]
            era_list = [t['era'] for t in team_data]
            
            print(f"\n📈 統計差異分析:")
            print(f"得分範圍: {min(runs_list):.2f} - {max(runs_list):.2f} (差距: {max(runs_list) - min(runs_list):.2f})")
            print(f"ERA範圍: {min(era_list):.2f} - {max(era_list):.2f} (差距: {max(era_list) - min(era_list):.2f})")
            
            if max(runs_list) - min(runs_list) < 0.5:
                print("⚠️  警告: 球隊得分差異很小")
            else:
                print("✅ 球隊統計有合理差異")
        
        # 2. 測試MLBPredictor的特徵提取
        print(f"\n🤖 測試MLBPredictor特徵提取:")
        try:
            predictor = MLBPredictor()
            
            # 創建測試比賽數據
            test_games = pd.DataFrame([
                {
                    'game_id': 'test1',
                    'home_team': 'LAD',
                    'away_team': 'OAK', 
                    'date': date(2025, 6, 29)
                },
                {
                    'game_id': 'test2',
                    'home_team': 'NYY',
                    'away_team': 'MIA',
                    'date': date(2025, 6, 29)
                }
            ])
            
            print("測試比賽:")
            for _, game in test_games.iterrows():
                print(f"  {game['away_team']} @ {game['home_team']}")
            
            # 提取特徵
            features_df = predictor.extract_features(test_games)
            
            if not features_df.empty:
                print(f"\n✅ 特徵提取成功! 特徵數量: {len(features_df.columns)}")
                
                # 檢查關鍵特徵
                key_features = [
                    'home_runs_scored_avg', 'away_runs_scored_avg',
                    'home_era', 'away_era',
                    'home_win_pct', 'away_win_pct'
                ]
                
                print(f"\n🔍 關鍵特徵檢查:")
                for feature in key_features:
                    if feature in features_df.columns:
                        values = features_df[feature].tolist()
                        print(f"{feature}: {values}")
                        
                        # 檢查是否所有值都相同
                        unique_values = len(set(f"{v:.3f}" for v in values))
                        if unique_values == 1:
                            print(f"  ❌ 所有值都相同!")
                        else:
                            print(f"  ✅ 有 {unique_values} 個不同值")
                    else:
                        print(f"{feature}: ❌ 特徵不存在")
                
                # 3. 測試實際預測
                print(f"\n🎯 測試實際預測:")
                for _, game in test_games.iterrows():
                    prediction = predictor.predict_game(
                        game['home_team'], 
                        game['away_team'], 
                        game['date']
                    )
                    
                    if prediction:
                        print(f"{game['away_team']} @ {game['home_team']}: {prediction['away_score']:.1f} - {prediction['home_score']:.1f}")
                    else:
                        print(f"{game['away_team']} @ {game['home_team']}: ❌ 預測失敗")
                
            else:
                print("❌ 特徵提取失敗!")
                return False
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print(f"\n✅ 測試完成!")
        return True

if __name__ == "__main__":
    test_team_stats_fix()
