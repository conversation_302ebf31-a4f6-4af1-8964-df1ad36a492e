from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime
import atexit
import os

from config import config
from models.database import db

def create_app(config_name=None):
    """應用工廠函數"""
    app = Flask(__name__)
    
    # 選擇配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app.config.from_object(config[config_name])
    
    # 確保data目錄存在
    data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    os.makedirs(data_dir, exist_ok=True)
    
    # 初始化擴展
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # 註冊藍圖（稍後實現）
    register_blueprints(app)
    
    # 創建數據庫表
    with app.app_context():
        db.create_all()
    
    # 設置定時任務
    setup_scheduler(app)

    # 添加自定義模板過濾器
    setup_template_filters(app)
    
    return app

def register_blueprints(app):
    """註冊藍圖"""
    from views.dashboard import dashboard_bp
    from views.teams import teams_bp
    from views.games import games_bp
    from views.predictions import predictions_bp
    from views.players import players_bp
    from views.admin import admin_bp
    from views.analysis import analysis_bp
    from views.unified_predictions import unified_predictions_bp
    from views.simulation import simulation_bp
    from views.intelligent_prediction import intelligent_bp
    from views.fast_prediction import bp as fast_prediction_bp

    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(teams_bp, url_prefix='/teams')
    app.register_blueprint(games_bp, url_prefix='/games')
    app.register_blueprint(predictions_bp, url_prefix='/predictions')
    app.register_blueprint(players_bp, url_prefix='/players')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(analysis_bp, url_prefix='/analysis')
    app.register_blueprint(unified_predictions_bp, url_prefix='/unified')
    app.register_blueprint(simulation_bp, url_prefix='/simulation')
    app.register_blueprint(intelligent_bp, url_prefix='/intelligent')
    app.register_blueprint(fast_prediction_bp, url_prefix='/fast')

    # 添加API路由
    @app.route('/api/health')
    def api_health():
        """API健康檢查"""
        from models.database import db
        from sqlalchemy import text
        try:
            # 測試數據庫連接
            db.session.execute(text('SELECT 1'))
            db.session.commit()
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'database': 'connected'
            })
        except Exception as e:
            return jsonify({
                'status': 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'database': 'disconnected',
                'error': str(e)
            }), 500

def setup_scheduler(app):
    """設置背景定時任務"""
    try:
        from models.automated_predictor import automated_predictor

        # 在應用上下文中啟動自動化預測器
        with app.app_context():
            automated_predictor.start_scheduler()

        # 註冊關閉時停止調度器
        atexit.register(lambda: automated_predictor.stop_scheduler())

    except Exception as e:
        print(f"設置調度器失敗: {e}")

def update_daily_data():
    """每日數據更新任務"""
    try:
        from models.automated_predictor import automated_predictor
        automated_predictor._daily_data_update()
    except Exception as e:
        print(f"每日數據更新失敗: {e}")

def update_predictions():
    """更新比賽預測"""
    try:
        from models.automated_predictor import automated_predictor
        automated_predictor._daily_prediction_generation()
    except Exception as e:
        print(f"預測更新失敗: {e}")

def setup_template_filters(app):
    """設置自定義模板過濾器"""
    import json
    from datetime import datetime, date, timedelta

    @app.template_filter('from_json')
    def from_json_filter(value):
        """將JSON字符串轉換為Python對象"""
        if value:
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}

    # 添加全局模板變量
    @app.context_processor
    def inject_datetime():
        """注入datetime對象到模板"""
        return {
            'datetime': datetime,
            'date': date,
            'timedelta': timedelta
        }

# 創建應用實例
app = create_app()

# 主頁路由
@app.route('/')
def index():
    """重定向到儀表板"""
    from flask import redirect, url_for
    return redirect(url_for('dashboard.dashboard'))

@app.route('/api')
def api_index():
    return jsonify({
        'message': 'MLB 預測系統 API',
        'status': 'running',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/health')
def health_check():
    """健康檢查端點"""
    return jsonify({
        'status': 'healthy',
        'database': 'connected',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5500)
