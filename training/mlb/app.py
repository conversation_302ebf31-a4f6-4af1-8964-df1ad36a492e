from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime
import os
import logging

from config import config
from models.database import db
from utils.app_utils import setup_scheduler, setup_template_filters # 導入輔助函數

# 配置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_app(config_name=None):
    """應用工廠函數"""
    app = Flask(__name__)
    
    # 選擇配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app.config.from_object(config[config_name])
    
    # 確保data目錄存在
    data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    os.makedirs(data_dir, exist_ok=True)
    
    # 初始化擴展
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # 註冊藍圖（稍後實現）
    register_blueprints(app)
    
    # 創建數據庫表
    with app.app_context():
        db.create_all()
    
    # 設置定時任務
    setup_scheduler(app)

    # 添加自定義模板過濾器
    setup_template_filters(app)
    
    return app

def register_blueprints(app):
    """註冊藍圖"""
    from views.dashboard import dashboard_bp
    from views.teams import teams_bp
    from views.games import games_bp
    from views.predictions import predictions_bp
    from views.players import players_bp
    from views.admin import admin_bp
    from views.analysis import analysis_bp
    from views.unified_predictions import unified_predictions_bp
    from views.simulation import simulation_bp
    from views.intelligent_prediction import intelligent_bp
    from views.fast_prediction import bp as fast_prediction_bp
    from views.backtest import backtest_bp

    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(teams_bp, url_prefix='/teams')
    app.register_blueprint(games_bp, url_prefix='/games')
    app.register_blueprint(predictions_bp, url_prefix='/predictions')
    app.register_blueprint(players_bp, url_prefix='/players')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(analysis_bp, url_prefix='/analysis')
    app.register_blueprint(unified_predictions_bp, url_prefix='/unified')
    app.register_blueprint(simulation_bp, url_prefix='/simulation')
    app.register_blueprint(intelligent_bp, url_prefix='/intelligent')
    app.register_blueprint(fast_prediction_bp, url_prefix='/fast')
    app.register_blueprint(backtest_bp, url_prefix='/backtest')

    # 添加API路由
    @app.route('/api/health')
    def api_health():
        """API健康檢查"""
        from models.database import db
        from sqlalchemy import text
        try:
            # 測試數據庫連接
            db.session.execute(text('SELECT 1'))
            db.session.commit()
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'database': 'connected'
            })
        except Exception as e:
            logger.error(f"數據庫健康檢查失敗: {e}", exc_info=True)
            return jsonify({
                'status': 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'database': 'disconnected',
                'error': str(e)
            }), 500

# 創建應用實例
app = create_app()

# 主頁路由
@app.route('/')
def index():
    """重定向到儀表板"""
    from flask import redirect, url_for
    return redirect(url_for('dashboard.dashboard'))

@app.route('/api')
def api_index():
    return jsonify({
        'message': 'MLB 預測系統 API',
        'status': 'running',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/health')
def health_check():
    """健康檢查端點"""
    return jsonify({
        'status': 'healthy',
        'database': 'connected',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5500)