#!/usr/bin/env python3
"""
修復MLB預測結果頁面缺少大小分和讓分盤顯示的問題

問題：
1. 預測頁面只顯示預測分數和勝率，但沒有顯示大小分線和讓分盤
2. 用戶無法知道預測是否打大分還是小分
3. 用戶無法知道主隊是讓分還是受讓

解決方案：
1. 在predictions.html模板中加入大小分和讓分盤的顯示
2. 從BettingOdds表獲取相關數據
3. 清楚標示主隊讓分/受讓狀態
"""

import sys
sys.path.append('.')

from app import app
from models.database import db, Game, Prediction, BettingOdds
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PredictionDisplayFixer:
    """預測顯示修復器"""
    
    def __init__(self):
        self.app = app
    
    def analyze_current_display(self):
        """分析當前顯示狀況"""
        with self.app.app_context():
            logger.info("🔍 分析預測顯示數據...")
            
            # 獲取今天的預測
            today = datetime.now().strftime('%Y-%m-%d')
            predictions = db.session.query(Prediction, Game, BettingOdds)\
                .join(Game, Prediction.game_id == Game.game_id)\
                .outerjoin(BettingOdds, Game.game_id == BettingOdds.game_id)\
                .filter(Game.date >= today)\
                .limit(5).all()
            
            logger.info(f"找到 {len(predictions)} 筆預測")
            
            for pred, game, odds in predictions:
                logger.info(f"\n比賽: {game.away_team} @ {game.home_team}")
                logger.info(f"  預測分數: {pred.predicted_away_score:.1f} - {pred.predicted_home_score:.1f}")
                
                if odds:
                    logger.info(f"  大小分線: {odds.total_point or 'N/A'}")
                    logger.info(f"  主隊讓分: {odds.home_spread_point or 'N/A'}")
                else:
                    logger.info("  ❌ 缺少賠率數據")
    
    def generate_enhanced_template_snippet(self):
        """生成增強的模板代碼片段"""
        
        template_code = '''
<!-- 在預測分數下方加入盤口信息 -->
<div class="card-body">
    <!-- 現有的預測分數顯示 -->
    <div class="row text-center mb-3">
        <!-- ... 預測分數部分保持不變 ... -->
    </div>
    
    <!-- 新增：盤口信息區塊 -->
    <div class="border-top pt-3 mb-3">
        <div class="row">
            <!-- 大小分顯示 -->
            <div class="col-6">
                <div class="text-center">
                    <small class="text-muted d-block">大小分</small>
                    {% if prediction.over_under_line %}
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="badge bg-info me-1">{{ prediction.over_under_line }}</span>
                            {% set predicted_total = prediction.predicted_home_score + prediction.predicted_away_score %}
                            {% if predicted_total > prediction.over_under_line %}
                                <span class="badge bg-warning text-dark">預測大分</span>
                            {% else %}
                                <span class="badge bg-secondary">預測小分</span>
                            {% endif %}
                        </div>
                    {% else %}
                        <span class="text-muted small">無數據</span>
                    {% endif %}
                </div>
            </div>
            
            <!-- 讓分盤顯示 -->
            <div class="col-6">
                <div class="text-center">
                    <small class="text-muted d-block">讓分盤</small>
                    {% if game.betting_odds and game.betting_odds[0] %}
                        {% set odds = game.betting_odds[0] %}
                        {% if odds.home_spread_point %}
                            <div class="d-flex justify-content-center align-items-center">
                                {% if odds.home_spread_point > 0 %}
                                    <span class="badge bg-danger me-1">主隊受讓 {{ odds.home_spread_point }}</span>
                                {% else %}
                                    <span class="badge bg-success me-1">主隊讓 {{ -odds.home_spread_point }}</span>
                                {% endif %}
                                
                                {% set spread_diff = (prediction.predicted_home_score - prediction.predicted_away_score) + odds.home_spread_point %}
                                {% if spread_diff > 0 %}
                                    <span class="badge bg-primary">薦買主隊</span>
                                {% else %}
                                    <span class="badge bg-info">薦買客隊</span>
                                {% endif %}
                            </div>
                        {% else %}
                            <span class="text-muted small">無數據</span>
                        {% endif %}
                    {% else %}
                        <span class="text-muted small">無數據</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 預測信心度和詳細分析按鈕 -->
    <div class="text-center mb-3">
        <!-- ... 保持原有內容 ... -->
    </div>
</div>
'''
        return template_code
    
    def update_predictions_view(self):
        """更新預測視圖以包含賠率數據"""
        view_code = '''
# 在 views/predictions.py 中更新查詢

@predictions_bp.route('/')
def predictions_list():
    """顯示預測列表"""
    # 獲取查詢參數
    selected_date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
    
    # 查詢預測並關聯賠率數據
    predictions = db.session.query(Prediction, Game)\
        .join(Game, Prediction.game_id == Game.game_id)\
        .filter(Game.date == selected_date)\
        .order_by(Game.date, Game.game_id)\
        .all()
    
    # 為每個預測獲取賠率數據
    for prediction, game in predictions:
        # 獲取最新的賠率數據
        betting_odds = BettingOdds.query.filter_by(game_id=game.game_id)\
            .order_by(BettingOdds.odds_time.desc())\
            .first()
        
        if betting_odds:
            # 將賠率數據附加到game對象
            game.betting_odds = [betting_odds]
            
            # 如果預測模型沒有大小分線，使用賠率數據的
            if not prediction.over_under_line and betting_odds.total_point:
                prediction.over_under_line = betting_odds.total_point
    
    return render_template('predictions.html',
                         predictions=predictions,
                         selected_date=selected_date)
'''
        return view_code
    
    def create_fix_script(self):
        """創建修復腳本"""
        logger.info("\n📝 創建修復方案...")
        
        # 生成模板代碼
        template_snippet = self.generate_enhanced_template_snippet()
        
        # 生成視圖代碼
        view_code = self.update_predictions_view()
        
        logger.info("✅ 修復方案已生成")
        logger.info("\n需要修改的文件：")
        logger.info("1. templates/predictions.html - 添加盤口顯示區塊")
        logger.info("2. views/predictions.py - 更新查詢以包含賠率數據")
        
        return {
            'template_snippet': template_snippet,
            'view_code': view_code
        }

def main():
    """主函數"""
    fixer = PredictionDisplayFixer()
    
    logger.info("="*50)
    logger.info("MLB預測顯示修復工具")
    logger.info("="*50)
    
    # 分析當前狀況
    fixer.analyze_current_display()
    
    # 生成修復方案
    fix_solution = fixer.create_fix_script()
    
    logger.info("\n" + "="*50)
    logger.info("修復完成！請根據上述方案更新相應文件")
    logger.info("="*50)

if __name__ == "__main__":
    main()