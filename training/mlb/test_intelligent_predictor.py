#!/usr/bin/env python3
"""
測試智能預測系統
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game
from models.intelligent_predictor import IntelligentPredictor

def test_intelligent_predictions():
    """測試智能預測系統"""
    app = create_app()
    
    with app.app_context():
        print("🧠 測試智能預測系統")
        print("=" * 80)
        
        predictor = IntelligentPredictor()
        
        # 測試案例
        test_cases = [
            {
                'game_id': '777122',
                'description': 'CHC @ NYY - 應該自動判斷投手質量並選擇策略'
            },
            {
                'game_id': '777170',
                'description': 'TB @ BOS - 測試不同投手組合的策略選擇'
            },
            {
                'game_id': '777165',
                'description': 'NYM @ BAL - 測試王牌對決或普通對戰'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🎯 測試案例 {i}: {test_case['description']}")
            print("-" * 60)
            
            # 查找比賽
            game = Game.query.filter_by(game_id=test_case['game_id']).first()
            
            if not game:
                print(f"❌ 找不到比賽 ID: {test_case['game_id']}")
                continue
                
            print(f"📅 比賽: {game.away_team} @ {game.home_team} ({game.date})")
            print(f"🏆 實際比分: {game.away_score} - {game.home_score}")
            
            try:
                # 執行智能預測
                result = predictor.predict_game_intelligent(
                    home_team=game.home_team,
                    away_team=game.away_team,
                    game_date=game.date,
                    game_id=test_case['game_id']
                )
                
                # 顯示結果
                print_prediction_result(result, game)
                
            except Exception as e:
                print(f"❌ 智能預測失敗: {e}")

def print_prediction_result(result: dict, game):
    """打印預測結果"""
    pitcher_analysis = result['pitcher_analysis']
    strategy = result['strategy']
    
    print(f"\n📊 投手分析:")
    print(f"   主隊投手: {pitcher_analysis['home_pitcher']['name']} "
          f"(ERA: {pitcher_analysis['home_pitcher']['era']:.2f}, "
          f"等級: {pitcher_analysis['home_pitcher']['strength']})")
    print(f"   客隊投手: {pitcher_analysis['away_pitcher']['name']} "
          f"(ERA: {pitcher_analysis['away_pitcher']['era']:.2f}, "
          f"等級: {pitcher_analysis['away_pitcher']['strength']})")
    print(f"   對戰類型: {pitcher_analysis['matchup_type']}")
    
    print(f"\n🧠 選擇策略: {strategy['name']}")
    print(f"   策略類型: {strategy['type']}")
    print(f"   目標總分: {strategy['target_total']}分")
    print(f"   策略說明: {strategy['description']}")
    
    print(f"\n📈 智能預測結果:")
    print(f"   預測比分: {result['predicted_away_score']:.1f} - {result['predicted_home_score']:.1f}")
    print(f"   預測總分: {result['total_runs']:.1f}分")
    print(f"   信心度: {result['confidence']:.1%}")
    
    # 與實際結果比較
    if game.away_score is not None and game.home_score is not None:
        actual_total = game.away_score + game.home_score
        predicted_total = result['total_runs']
        total_diff = abs(predicted_total - actual_total)
        
        print(f"\n🎯 預測準確性:")
        print(f"   實際總分: {actual_total}分")
        print(f"   總分差異: {total_diff:.1f}分")
        
        if total_diff <= 2.0:
            print(f"   ✅ 預測準確 (差異 ≤ 2分)")
        elif total_diff <= 4.0:
            print(f"   ⚠️  預測尚可 (差異 ≤ 4分)")
        else:
            print(f"   ❌ 預測偏差較大 (差異 > 4分)")
    
    # 策略合理性分析
    analyze_strategy_reasonableness(pitcher_analysis, strategy, result)

def analyze_strategy_reasonableness(pitcher_analysis: dict, strategy: dict, result: dict):
    """分析策略合理性"""
    print(f"\n🔍 策略合理性分析:")
    
    matchup_type = pitcher_analysis['matchup_type']
    predicted_total = result['total_runs']
    
    if matchup_type == "王牌對決":
        if predicted_total <= 8.0:
            print(f"   ✅ 策略合理: 王牌對決預測低分 ({predicted_total:.1f}分)")
        else:
            print(f"   ⚠️  策略可疑: 王牌對決但預測高分 ({predicted_total:.1f}分)")
            
    elif matchup_type == "打擊戰":
        if predicted_total >= 11.0:
            print(f"   ✅ 策略合理: 打擊戰預測高分 ({predicted_total:.1f}分)")
        else:
            print(f"   ⚠️  策略可疑: 打擊戰但預測低分 ({predicted_total:.1f}分)")
            
    elif matchup_type == "強弱對戰":
        if 8.0 <= predicted_total <= 11.0:
            print(f"   ✅ 策略合理: 強弱對戰預測中等分數 ({predicted_total:.1f}分)")
        else:
            print(f"   ⚠️  策略可疑: 強弱對戰但預測極端分數 ({predicted_total:.1f}分)")
            
    else:  # 普通對戰
        if 8.0 <= predicted_total <= 11.0:
            print(f"   ✅ 策略合理: 普通對戰預測標準分數 ({predicted_total:.1f}分)")
        else:
            print(f"   ⚠️  策略可疑: 普通對戰但預測極端分數 ({predicted_total:.1f}分)")

def test_strategy_selection():
    """測試策略選擇邏輯"""
    print(f"\n🧪 測試策略選擇邏輯")
    print("=" * 80)
    
    predictor = IntelligentPredictor()
    
    # 模擬不同投手組合
    test_scenarios = [
        {
            'name': '王牌對決測試',
            'home_pitcher': {'era': 2.20, 'quality': 85, 'strength': '王牌'},
            'away_pitcher': {'era': 2.50, 'quality': 82, 'strength': '王牌'},
            'expected_strategy': 'low_scoring'
        },
        {
            'name': '打擊戰測試',
            'home_pitcher': {'era': 5.50, 'quality': 30, 'strength': '弱勢'},
            'away_pitcher': {'era': 6.00, 'quality': 25, 'strength': '弱勢'},
            'expected_strategy': 'high_scoring'
        },
        {
            'name': '強弱對戰測試',
            'home_pitcher': {'era': 2.80, 'quality': 78, 'strength': '優秀'},
            'away_pitcher': {'era': 5.20, 'quality': 35, 'strength': '弱勢'},
            'expected_strategy': 'unbalanced'
        },
        {
            'name': '普通對戰測試',
            'home_pitcher': {'era': 4.20, 'quality': 55, 'strength': '普通'},
            'away_pitcher': {'era': 4.10, 'quality': 58, 'strength': '普通'},
            'expected_strategy': 'standard'
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🎯 {scenario['name']}:")
        
        # 模擬投手分析
        pitcher_analysis = {
            'home_pitcher': scenario['home_pitcher'],
            'away_pitcher': scenario['away_pitcher'],
            'matchup_type': predictor._determine_matchup_type(
                scenario['home_pitcher']['strength'],
                scenario['away_pitcher']['strength']
            ),
            'average_era': (scenario['home_pitcher']['era'] + scenario['away_pitcher']['era']) / 2,
            'average_quality': (scenario['home_pitcher']['quality'] + scenario['away_pitcher']['quality']) / 2
        }
        
        strategy = predictor._determine_prediction_strategy(pitcher_analysis)
        
        print(f"   投手組合: {scenario['home_pitcher']['strength']} vs {scenario['away_pitcher']['strength']}")
        print(f"   對戰類型: {pitcher_analysis['matchup_type']}")
        print(f"   選擇策略: {strategy['name']} ({strategy['type']})")
        print(f"   目標總分: {strategy['target_total']}分")
        
        if strategy['type'] == scenario['expected_strategy']:
            print(f"   ✅ 策略選擇正確")
        else:
            print(f"   ❌ 策略選擇錯誤 (期望: {scenario['expected_strategy']})")

if __name__ == "__main__":
    test_intelligent_predictions()
    test_strategy_selection()
