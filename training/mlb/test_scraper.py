#!/usr/bin/env python3
"""
測試 SportsBookReview 網頁抓取器
"""

import sys
import os
from datetime import date, datetime, timedelta
import logging

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_scraper():
    """測試網頁抓取器"""
    try:
        from models.sportsbookreview_scraper import SportsBookReviewScraper
        
        # 創建抓取器實例
        scraper = SportsBookReviewScraper()
        
        # 測試日期：2021-04-04（用戶提供的示例日期）
        test_date = date(2021, 4, 4)
        
        logger.info(f"開始測試抓取 {test_date} 的盤口數據...")
        
        # 抓取歷史盤口數據
        result = scraper.fetch_historical_odds(test_date, bookmaker='bet365')
        
        if result['success']:
            logger.info(f"✅ 抓取成功！")
            logger.info(f"📅 日期: {result['date']}")
            logger.info(f"🏢 博彩商: {result['bookmaker']}")
            logger.info(f"🎮 總遊戲數: {result['total_games']}")
            
            # 顯示前3場遊戲的詳細信息
            games = result.get('games', [])
            for i, game in enumerate(games[:3]):
                logger.info(f"\n🏟️ 遊戲 {i+1}: {game.get('away_team', 'N/A')} @ {game.get('home_team', 'N/A')}")
                
                # 顯示讓分盤
                if 'spreads' in game:
                    spreads = game['spreads']
                    logger.info(f"   📊 讓分盤:")
                    logger.info(f"      客隊: {spreads.get('away_spread_point', 'N/A')} ({spreads.get('away_spread_odds', 'N/A')})")
                    logger.info(f"      主隊: {spreads.get('home_spread_point', 'N/A')} ({spreads.get('home_spread_odds', 'N/A')})")
                
                # 顯示總分盤
                if 'totals' in game:
                    totals = game['totals']
                    logger.info(f"   🎯 總分盤:")
                    logger.info(f"      總分: {totals.get('total_point', 'N/A')}")
                    logger.info(f"      Over: {totals.get('over_odds', 'N/A')}")
                    logger.info(f"      Under: {totals.get('under_odds', 'N/A')}")
            
            if len(games) > 3:
                logger.info(f"\n... 還有 {len(games) - 3} 場遊戲")
                
        else:
            logger.error(f"❌ 抓取失敗: {result.get('error', '未知錯誤')}")
            
    except Exception as e:
        logger.error(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_recent_date():
    """測試最近日期的數據"""
    try:
        from models.sportsbookreview_scraper import SportsBookReviewScraper
        
        scraper = SportsBookReviewScraper()
        
        # 測試最近的日期：今天往前3天
        test_date = date.today() - timedelta(days=3)
        
        logger.info(f"\n開始測試抓取 {test_date} 的盤口數據...")
        
        result = scraper.fetch_historical_odds(test_date, bookmaker='bet365')
        
        if result['success']:
            logger.info(f"✅ 抓取成功！")
            logger.info(f"📅 日期: {result['date']}")
            logger.info(f"🎮 總遊戲數: {result['total_games']}")
            
            # 顯示所有遊戲的基本信息
            games = result.get('games', [])
            for i, game in enumerate(games):
                logger.info(f"🏟️ 遊戲 {i+1}: {game.get('away_team', 'N/A')} @ {game.get('home_team', 'N/A')}")
                
        else:
            logger.error(f"❌ 抓取失敗: {result.get('error', '未知錯誤')}")
            
    except Exception as e:
        logger.error(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    print("🧪 開始測試 SportsBookReview 網頁抓取器...")
    print("=" * 60)
    
    # 測試歷史日期
    test_scraper()
    
    # 測試最近日期
    test_recent_date()
    
    print("\n" + "=" * 60)
    print("🏁 測試完成")
