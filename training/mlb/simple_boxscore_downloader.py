#!/usr/bin/env python3
"""
簡化Box Score下載工具
直接從MLB API獲取並保存詳細統計數據
"""

import sys
sys.path.append('.')

import os
import json
import requests
from datetime import datetime
from app import app

def download_boxscores_for_date(target_date):
    """下載指定日期的所有Box Score數據"""
    
    print(f"📊 下載 {target_date} 的 Box Score 數據")
    print("=" * 60)
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    with app.app_context():
        try:
            from models.database import db, Game
            from boxscore_data_collector import BoxscoreDataCollector
            
            # 初始化收集器
            collector = BoxscoreDataCollector()
            
            # 查詢該日期的已完成比賽
            games = db.session.query(Game).filter(
                Game.date == target_date,
                Game.game_status == 'completed'
            ).all()
            
            print(f"🎯 找到 {len(games)} 場已完成比賽")
            
            if not games:
                print(f"❌ 未找到 {target_date} 的已完成比賽")
                return False
            
            # 創建boxscore數據表（如果不存在）
            create_boxscore_table()
            
            success_count = 0
            failed_count = 0
            
            for i, game in enumerate(games, 1):
                try:
                    print(f"\n🔄 處理第 {i}/{len(games)} 場比賽:")
                    print(f"   {game.away_team} @ {game.home_team} (ID: {game.game_id})")
                    print(f"   比分: {game.away_score} - {game.home_score}")
                    
                    # 檢查是否已有Box Score數據
                    existing = check_existing_boxscore(game.game_id)
                    if existing:
                        print(f"   ℹ️ 已有Box Score數據，跳過")
                        success_count += 1
                        continue
                    
                    # 獲取Box Score數據
                    boxscore_data = collector.get_game_boxscore(game.game_id)
                    
                    if boxscore_data:
                        # 保存Box Score數據
                        if save_boxscore_data(game.game_id, boxscore_data):
                            print(f"   ✅ Box Score數據下載並保存成功")
                            
                            # 提取並顯示關鍵統計
                            stats_summary = extract_key_stats(boxscore_data)
                            print(f"   📊 統計數據: {stats_summary}")
                            
                            success_count += 1
                        else:
                            print(f"   ❌ Box Score數據保存失敗")
                            failed_count += 1
                    else:
                        print(f"   ❌ 無法獲取Box Score數據")
                        failed_count += 1
                
                except Exception as e:
                    print(f"   ❌ 處理失敗: {e}")
                    failed_count += 1
                    continue
            
            print(f"\n📈 Box Score 下載結果:")
            print(f"   ✅ 成功: {success_count} 場")
            print(f"   ❌ 失敗: {failed_count} 場")
            total = success_count + failed_count
            if total > 0:
                print(f"   📊 成功率: {success_count/total*100:.1f}%")
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 下載過程失敗: {e}")
            import traceback
            traceback.print_exc()
            return False

def create_boxscore_table():
    """創建Box Score數據表"""
    try:
        from models.database import db
        from sqlalchemy import text
        
        # 創建boxscore_data表
        db.session.execute(text("""
            CREATE TABLE IF NOT EXISTS boxscore_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT NOT NULL UNIQUE,
                boxscore_json TEXT NOT NULL,
                player_count INTEGER DEFAULT 0,
                pitcher_count INTEGER DEFAULT 0,
                home_team_stats TEXT,
                away_team_stats TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """))
        
        db.session.commit()
        return True
        
    except Exception as e:
        print(f"創建Box Score表失敗: {e}")
        db.session.rollback()
        return False

def check_existing_boxscore(game_id):
    """檢查是否已有Box Score數據"""
    try:
        from models.database import db
        from sqlalchemy import text
        
        result = db.session.execute(text("""
            SELECT COUNT(*) as count FROM boxscore_data WHERE game_id = :game_id
        """), {"game_id": game_id}).fetchone()
        
        return result[0] > 0 if result else False
        
    except Exception as e:
        return False

def save_boxscore_data(game_id, boxscore_data):
    """保存Box Score數據到數據庫"""
    try:
        from models.database import db
        from sqlalchemy import text
        
        # 提取統計數據
        stats = extract_detailed_stats(boxscore_data)
        
        # 保存到數據庫
        db.session.execute(text("""
            INSERT OR REPLACE INTO boxscore_data 
            (game_id, boxscore_json, player_count, pitcher_count, home_team_stats, away_team_stats, updated_at)
            VALUES (:game_id, :boxscore_json, :player_count, :pitcher_count, :home_stats, :away_stats, :updated_at)
        """), {
            "game_id": game_id,
            "boxscore_json": json.dumps(boxscore_data),
            "player_count": stats['player_count'],
            "pitcher_count": stats['pitcher_count'],
            "home_stats": json.dumps(stats['home_team']),
            "away_stats": json.dumps(stats['away_team']),
            "updated_at": datetime.now().isoformat()
        })
        
        db.session.commit()
        return True
        
    except Exception as e:
        print(f"保存Box Score數據失敗: {e}")
        db.session.rollback()
        return False

def extract_detailed_stats(boxscore_data):
    """提取詳細統計數據"""
    stats = {
        'player_count': 0,
        'pitcher_count': 0,
        'home_team': {},
        'away_team': {}
    }
    
    try:
        teams = boxscore_data.get('teams', {})
        
        for team_side in ['home', 'away']:
            team_data = teams.get(team_side, {})
            team_stats = {
                'players': [],
                'pitchers': [],
                'team_batting': {},
                'team_pitching': {}
            }
            
            # 處理球員數據
            players = team_data.get('players', {})
            for player_id, player_data in players.items():
                person = player_data.get('person', {})
                batting_stats = player_data.get('stats', {}).get('batting')
                pitching_stats = player_data.get('stats', {}).get('pitching')
                
                if batting_stats:
                    team_stats['players'].append({
                        'id': player_id,
                        'name': person.get('fullName', ''),
                        'position': player_data.get('position', {}).get('abbreviation', ''),
                        'at_bats': batting_stats.get('atBats', 0),
                        'runs': batting_stats.get('runs', 0),
                        'hits': batting_stats.get('hits', 0),
                        'rbi': batting_stats.get('rbi', 0),
                        'home_runs': batting_stats.get('homeRuns', 0),
                        'avg': batting_stats.get('avg', '0.000')
                    })
                    stats['player_count'] += 1
                
                if pitching_stats:
                    team_stats['pitchers'].append({
                        'id': player_id,
                        'name': person.get('fullName', ''),
                        'innings_pitched': pitching_stats.get('inningsPitched', '0'),
                        'hits': pitching_stats.get('hits', 0),
                        'runs': pitching_stats.get('runs', 0),
                        'earned_runs': pitching_stats.get('earnedRuns', 0),
                        'strikeouts': pitching_stats.get('strikeOuts', 0),
                        'walks': pitching_stats.get('baseOnBalls', 0),
                        'era': pitching_stats.get('era', '0.00')
                    })
                    stats['pitcher_count'] += 1
            
            # 團隊統計
            team_stats['team_batting'] = team_data.get('teamStats', {}).get('batting', {})
            team_stats['team_pitching'] = team_data.get('teamStats', {}).get('pitching', {})
            
            stats[f'{team_side}_team'] = team_stats
    
    except Exception as e:
        print(f"提取統計數據失敗: {e}")
    
    return stats

def extract_key_stats(boxscore_data):
    """提取關鍵統計數據用於顯示"""
    try:
        teams = boxscore_data.get('teams', {})
        home_players = len(teams.get('home', {}).get('players', {}))
        away_players = len(teams.get('away', {}).get('players', {}))
        
        return f"{away_players + home_players} 球員記錄"
    except:
        return "統計數據獲取中..."

def show_boxscore_summary(target_date):
    """顯示Box Score數據總結"""
    
    print(f"\n📊 {target_date} Box Score 數據總結")
    print("-" * 50)
    
    try:
        from models.database import db
        from sqlalchemy import text
        
        result = db.session.execute(text("""
            SELECT 
                COUNT(*) as total_games,
                SUM(player_count) as total_players,
                SUM(pitcher_count) as total_pitchers,
                AVG(player_count) as avg_players_per_game
            FROM boxscore_data 
            WHERE date(created_at) = :target_date
        """), {"target_date": target_date}).fetchone()
        
        if result and result[0] > 0:
            print(f"   📈 Box Score記錄: {result[0]} 場比賽")
            print(f"   👥 球員統計: {result[1]} 位球員")
            print(f"   ⚾ 投手統計: {result[2]} 位投手")
            print(f"   📊 平均每場: {result[3]:.1f} 位球員")
            
            # 顯示一些樣本數據
            samples = db.session.execute(text("""
                SELECT game_id, player_count, pitcher_count
                FROM boxscore_data 
                WHERE date(created_at) = :target_date
                LIMIT 5
            """), {"target_date": target_date}).fetchall()
            
            print(f"\n   🎯 樣本數據:")
            for sample in samples:
                print(f"      {sample[0]}: {sample[1]} 球員, {sample[2]} 投手")
                
        else:
            print(f"   ❌ 沒有找到 {target_date} 的Box Score數據")
            
    except Exception as e:
        print(f"   ❌ 總結生成失敗: {e}")

def main():
    """主函數"""
    
    print("📊 MLB Box Score 補充下載工具")
    print("補充缺失的詳細球員統計數據以提升預測準確性")
    print()
    
    # 下載8/29的Box Score
    target_date = '2025-08-29'
    
    success = download_boxscores_for_date(target_date)
    
    if success:
        print(f"\n🎉 Box Score 數據下載完成！")
        
        # 顯示數據總結
        show_boxscore_summary(target_date)
        
        print(f"\n✅ 現在系統有了詳細的球員統計數據:")
        print(f"   📊 每位球員的打擊數據 (打數、安打、打點等)")
        print(f"   ⚾ 每位投手的投球數據 (局數、失分、三振等)")
        print(f"   🎯 這些數據將大幅提升預測準確性")
        
    else:
        print(f"\n⚠️ Box Score 數據下載未完全成功")
        print(f"   請檢查網絡連接和MLB API狀態")
    
    print(f"\n📋 完成後建議:")
    print(f"   1. 重新運行預測模型")
    print(f"   2. 比較預測準確性的改善")
    print(f"   3. 定期下載新比賽的Box Score數據")

if __name__ == "__main__":
    main()