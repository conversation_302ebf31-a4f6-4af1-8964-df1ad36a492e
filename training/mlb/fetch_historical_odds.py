#!/usr/bin/env python3
"""
抓取最近已完成比賽的歷史賠率數據
專門抓取 spreads (讓分盤) 和 totals (大小分) 數據
"""

import sys
import os
from datetime import date, datetime, timedelta

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, BettingOdds
from models.odds_data_fetcher import OddsDataFetcher

def fetch_historical_odds():
    """抓取最近已完成比賽的歷史賠率"""
    print("🎯 抓取最近已完成比賽的歷史賠率")
    print("=" * 80)
    
    # 定義要抓取的日期 (最近已完成的比賽)
    target_dates = [
        date(2025, 7, 1),   # 11場已完成
        date(2025, 6, 29),  # 15場已完成  
        date(2025, 6, 27),  # 15場已完成
        date(2025, 6, 25),  # 15場已完成
        date(2025, 6, 24),  # 15場已完成
        date(2025, 6, 22),  # 15場已完成
        date(2025, 6, 21),  # 15場已完成
        date(2025, 6, 20),  # 15場已完成
    ]
    
    app = create_app()
    
    with app.app_context():
        fetcher = OddsDataFetcher(app)
        
        # 1. 檢查API狀態
        print("🔑 檢查API連接狀態...")
        status = fetcher.check_api_status()
        
        if not status['api_accessible']:
            print(f"❌ API連接失敗: {status['message']}")
            return
        
        print(f"✅ API連接正常，剩餘請求次數: {status.get('remaining_requests', 'N/A')}")
        
        # 2. 統計要處理的比賽
        total_games = 0
        for target_date in target_dates:
            completed_games = Game.query.filter(
                Game.date == target_date,
                Game.game_status == 'completed'
            ).count()
            total_games += completed_games
            print(f"📅 {target_date}: {completed_games} 場已完成比賽")
        
        print(f"\n📊 總計: {len(target_dates)} 個日期, {total_games} 場已完成比賽")
        
        # 3. 開始抓取每個日期的賠率
        total_success = 0
        total_failed = 0
        all_results = []
        
        for i, target_date in enumerate(target_dates, 1):
            print(f"\n{'='*60}")
            print(f"🔄 [{i}/{len(target_dates)}] 抓取 {target_date} 的賠率數據")
            print(f"{'='*60}")
            
            try:
                # 檢查該日期是否已有賠率數據
                existing_odds = db.session.query(BettingOdds)\
                    .join(Game, BettingOdds.game_id == Game.game_id)\
                    .filter(Game.date == target_date).count()
                
                if existing_odds > 0:
                    print(f"⚠️  該日期已有 {existing_odds} 筆賠率記錄，跳過...")
                    continue
                
                # 抓取賠率數據
                odds_result = fetcher.fetch_odds_for_games(target_date)
                
                if odds_result['success']:
                    print(f"✅ 成功抓取賠率數據")
                    print(f"   匹配比賽: {odds_result['matched_games']}/{odds_result['total_games']}")
                    print(f"   匹配率: {odds_result['summary']['match_rate']:.1f}%")
                    
                    # 保存到數據庫
                    if odds_result['matched_games'] > 0:
                        save_result = fetcher.save_odds_to_database(odds_result)
                        
                        if save_result['success']:
                            print(f"💾 數據保存成功")
                            print(f"   新增記錄: {save_result['saved_count']}")
                            print(f"   更新記錄: {save_result['updated_count']}")
                            total_success += 1
                            
                            all_results.append({
                                'date': target_date,
                                'status': 'success',
                                'matched_games': odds_result['matched_games'],
                                'saved_records': save_result['total_processed']
                            })
                        else:
                            print(f"❌ 數據保存失敗: {save_result['error']}")
                            total_failed += 1
                    else:
                        print(f"⚠️  沒有匹配的比賽，跳過保存")
                        
                else:
                    print(f"❌ 抓取賠率失敗: {odds_result['error']}")
                    total_failed += 1
                    
                    all_results.append({
                        'date': target_date,
                        'status': 'failed',
                        'error': odds_result['error']
                    })
                
                # 短暫延遲避免API限制
                import time
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ 處理 {target_date} 時發生錯誤: {e}")
                total_failed += 1
                
                all_results.append({
                    'date': target_date,
                    'status': 'error',
                    'error': str(e)
                })
        
        # 4. 總結報告
        print(f"\n{'='*80}")
        print(f"📊 歷史賠率抓取完成報告")
        print(f"{'='*80}")
        
        print(f"✅ 成功處理: {total_success} 個日期")
        print(f"❌ 失敗處理: {total_failed} 個日期")
        print(f"📈 成功率: {total_success/(total_success+total_failed)*100:.1f}%" if (total_success+total_failed) > 0 else "N/A")
        
        # 詳細結果
        print(f"\n📋 詳細結果:")
        for result in all_results:
            if result['status'] == 'success':
                print(f"✅ {result['date']}: {result['matched_games']} 場比賽, {result['saved_records']} 筆記錄")
            else:
                print(f"❌ {result['date']}: {result.get('error', '未知錯誤')}")
        
        # 5. 最終數據庫統計
        print(f"\n💾 最終數據庫統計:")
        total_odds_after = BettingOdds.query.count()
        spreads_count = BettingOdds.query.filter_by(market_type='spreads').count()
        totals_count = BettingOdds.query.filter_by(market_type='totals').count()
        
        print(f"   總賠率記錄: {total_odds_after:,}")
        print(f"   讓分盤記錄: {spreads_count:,}")
        print(f"   大小分記錄: {totals_count:,}")
        
        # 檢查覆蓋的日期
        covered_dates = db.session.query(Game.date)\
            .join(BettingOdds, Game.game_id == BettingOdds.game_id)\
            .distinct().count()
        
        print(f"   覆蓋日期數: {covered_dates} 天")
        
        print(f"\n🎉 歷史賠率抓取任務完成！")

if __name__ == "__main__":
    fetch_historical_odds()
