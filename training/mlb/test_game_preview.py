"""
測試比賽預覽功能
驗證MLB官網風格的比賽預覽頁面功能
"""

import requests
import json
from app import app
from models.game_preview import GamePreview
from models.database import Game, GameDetail, Team

def test_game_preview_backend():
    """測試比賽預覽後端功能"""
    print("=== 測試比賽預覽後端功能 ===")
    
    with app.app_context():
        preview_service = GamePreview()
        
        # 找有先發投手數據的比賽
        games_with_pitchers = Game.query.join(GameDetail).filter(
            GameDetail.home_starting_pitcher.isnot(None),
            GameDetail.away_starting_pitcher.isnot(None)
        ).limit(3).all()
        
        print(f"找到 {len(games_with_pitchers)} 場有先發投手數據的比賽")
        
        for i, game in enumerate(games_with_pitchers, 1):
            print(f"\n--- 測試比賽 {i}: {game.game_id} ---")
            print(f"日期: {game.date}")
            print(f"對戰: {game.away_team} @ {game.home_team}")
            
            try:
                preview_data = preview_service.get_game_preview(game.game_id)
                
                # 檢查基本數據結構
                required_keys = ['game', 'teams', 'probable_pitchers', 'matchups']
                missing_keys = [key for key in required_keys if key not in preview_data]
                
                if missing_keys:
                    print(f"❌ 缺少必要數據: {missing_keys}")
                    continue
                
                print("✅ 基本數據結構完整")
                
                # 檢查先發投手數據
                away_pitcher = preview_data['probable_pitchers'].get('away')
                home_pitcher = preview_data['probable_pitchers'].get('home')
                
                if away_pitcher:
                    print(f"✅ 客隊投手: {away_pitcher['name']} ({away_pitcher['record']}, {away_pitcher['era']} ERA, {away_pitcher['strikeouts']} K)")
                else:
                    print("❌ 客隊投手數據缺失")
                
                if home_pitcher:
                    print(f"✅ 主隊投手: {home_pitcher['name']} ({home_pitcher['record']}, {home_pitcher['era']} ERA, {home_pitcher['strikeouts']} K)")
                else:
                    print("❌ 主隊投手數據缺失")
                
                # 檢查對戰統計
                away_matchups = preview_data['matchups'].get('away_vs_home_pitcher', [])
                home_matchups = preview_data['matchups'].get('home_vs_away_pitcher', [])
                
                print(f"✅ 客隊打線對戰數據: {len(away_matchups)} 名打者")
                print(f"✅ 主隊打線對戰數據: {len(home_matchups)} 名打者")
                
            except Exception as e:
                print(f"❌ 測試失敗: {e}")

def test_game_preview_web():
    """測試比賽預覽web頁面"""
    print("\n=== 測試比賽預覽Web頁面 ===")
    
    base_url = "http://127.0.0.1:5500"
    
    with app.app_context():
        # 找一個測試比賽
        test_game = Game.query.join(GameDetail).filter(
            GameDetail.home_starting_pitcher.isnot(None),
            GameDetail.away_starting_pitcher.isnot(None)
        ).first()
        
        if not test_game:
            print("❌ 沒有找到測試比賽")
            return
        
        print(f"測試比賽: {test_game.game_id}")
        
        # 測試比賽預覽頁面
        preview_url = f"{base_url}/games/{test_game.game_id}/preview"
        
        try:
            response = requests.get(preview_url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ 比賽預覽頁面正常: {preview_url}")
                
                # 檢查頁面內容
                content = response.text
                
                # 檢查關鍵元素
                checks = [
                    ("比賽預覽", "頁面標題"),
                    ("Probable Pitchers", "先發投手區塊"),
                    ("Matchups", "對戰統計區塊"),
                    ("VS", "對戰指示符"),
                    ("ERA", "投手統計"),
                    ("W-L", "投手戰績")
                ]
                
                for keyword, description in checks:
                    if keyword in content:
                        print(f"✅ {description}存在")
                    else:
                        print(f"❌ {description}缺失")
                        
            else:
                print(f"❌ 頁面請求失敗: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Web測試失敗: {e}")
        
        # 測試比賽詳細頁面的預覽按鈕
        detail_url = f"{base_url}/games/{test_game.game_id}"
        
        try:
            response = requests.get(detail_url, timeout=10)
            
            if response.status_code == 200:
                content = response.text
                if "比賽預覽" in content:
                    print("✅ 比賽詳細頁面包含預覽按鈕")
                else:
                    print("❌ 比賽詳細頁面缺少預覽按鈕")
            else:
                print(f"❌ 比賽詳細頁面請求失敗: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 詳細頁面測試失敗: {e}")

def test_pitcher_statistics_integration():
    """測試投手統計整合"""
    print("\n=== 測試投手統計整合 ===")
    
    with app.app_context():
        preview_service = GamePreview()
        
        # 測試投手統計查詢
        test_pitchers = [
            "Jose Quintana",
            "Jake Irvin", 
            "Spencer Strider",
            "Max Fried"
        ]
        
        for pitcher_name in test_pitchers:
            print(f"\n測試投手: {pitcher_name}")
            
            try:
                pitcher_stats = preview_service._get_pitcher_stats(pitcher_name, "ATL")
                
                if pitcher_stats:
                    print(f"✅ 統計數據: {pitcher_stats['record']}, {pitcher_stats['era']} ERA, {pitcher_stats['strikeouts']} K")
                    print(f"   WHIP: {pitcher_stats['whip']}, 局數: {pitcher_stats['innings_pitched']}")
                else:
                    print("❌ 無統計數據")
                    
            except Exception as e:
                print(f"❌ 查詢失敗: {e}")

def test_matchup_data():
    """測試對戰數據"""
    print("\n=== 測試對戰數據 ===")
    
    with app.app_context():
        preview_service = GamePreview()
        
        # 測試球隊對投手統計
        test_teams = ["ATL", "NYM", "WSH", "PHI"]
        test_pitcher = "Spencer Strider"
        
        for team_code in test_teams:
            print(f"\n測試 {team_code} 對 {test_pitcher}")
            
            try:
                matchup_stats = preview_service._get_team_vs_pitcher_stats(team_code, test_pitcher)
                
                if matchup_stats:
                    print(f"✅ 找到 {len(matchup_stats)} 名打者數據")
                    
                    # 顯示前3名打者
                    for i, batter in enumerate(matchup_stats[:3]):
                        print(f"   {i+1}. {batter['player_name']}: {batter['at_bats']} AB, {batter['hits']} H, {batter['batting_avg']:.3f} AVG")
                else:
                    print("❌ 無對戰數據")
                    
            except Exception as e:
                print(f"❌ 查詢失敗: {e}")

def main():
    """主測試函數"""
    print("🏀 MLB比賽預覽功能測試")
    print("=" * 50)
    
    # 測試後端功能
    test_game_preview_backend()
    
    # 測試Web頁面
    test_game_preview_web()
    
    # 測試投手統計整合
    test_pitcher_statistics_integration()
    
    # 測試對戰數據
    test_matchup_data()
    
    print("\n" + "=" * 50)
    print("✅ 比賽預覽功能測試完成")

if __name__ == "__main__":
    main()
