#!/usr/bin/env python3
"""
測試打線順序分析系統
"""

import sys
import os
from datetime import date, timedelta

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.batting_order_analyzer import BattingOrderAnalyzer

def test_batting_order_analyzer():
    """測試打線順序分析系統"""
    print("=" * 80)
    print("⚾ 測試打線順序分析系統")
    print("=" * 80)
    
    analyzer = BattingOrderAnalyzer()
    
    # 測試1: 獲取比賽打線特徵
    print("\n📊 測試1: 獲取比賽打線特徵")
    print("-" * 50)
    
    # 測試今日和昨日的比賽
    today = date.today().strftime('%Y-%m-%d')
    yesterday = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
    
    test_matchups = [
        ("BOS", "NYY", "波士頓紅襪 vs 紐約洋基"),
        ("LAD", "HOU", "洛杉磯道奇 vs 休士頓太空人"),
        ("CHC", "STL", "芝加哥小熊 vs 聖路易紅雀")
    ]
    
    for test_date in [today, yesterday]:
        print(f"\n📅 測試日期: {test_date}")
        
        for home_team, away_team, description in test_matchups:
            print(f"\n🏟️  {description} ({home_team} vs {away_team})")
            
            lineup_features = analyzer.get_lineup_features_for_game(home_team, away_team, test_date)
            
            print(f"主隊打線確認: {'✅' if lineup_features['home_lineup_confirmed'] else '❌'}")
            print(f"客隊打線確認: {'✅' if lineup_features['away_lineup_confirmed'] else '❌'}")
            print(f"主隊打線深度: {lineup_features['home_lineup_depth_score']:.1f}")
            print(f"客隊打線深度: {lineup_features['away_lineup_depth_score']:.1f}")
            print(f"主隊位置強度: {lineup_features['home_position_strength']:.1f}")
            print(f"客隊位置強度: {lineup_features['away_position_strength']:.1f}")
            print(f"主隊核心打者: {lineup_features['home_core_hitters_strength']:.2f}")
            print(f"客隊核心打者: {lineup_features['away_core_hitters_strength']:.2f}")
            print(f"主隊指定打擊: {'✅' if lineup_features['home_has_dh'] else '❌'}")
            print(f"客隊指定打擊: {'✅' if lineup_features['away_has_dh'] else '❌'}")
            print(f"打線優勢: {lineup_features['lineup_advantage']:.1f} (正值=主隊優勢)")
            
            # 如果有確認的打線，顯示更多詳細信息
            if lineup_features['home_lineup_confirmed'] or lineup_features['away_lineup_confirmed']:
                print(f"主隊打線平衡: {lineup_features['home_lineup_balance']:.1f}")
                print(f"客隊打線平衡: {lineup_features['away_lineup_balance']:.1f}")
                print(f"主隊打線優化: {lineup_features['home_batting_order_optimization']:.1f}")
                print(f"客隊打線優化: {lineup_features['away_batting_order_optimization']:.1f}")
    
    # 測試2: 獲取詳細打線摘要
    print(f"\n📊 測試2: 獲取詳細打線摘要")
    print("-" * 50)
    
    for test_date in [today, yesterday]:
        print(f"\n📅 測試日期: {test_date}")
        
        # 只測試前2場比賽
        for home_team, away_team, description in test_matchups[:2]:
            print(f"\n🏟️  {description}")
            
            summary = analyzer.get_lineup_summary_for_display(home_team, away_team, test_date)
            
            if 'error' in summary:
                print(f"❌ {summary['error']}")
                continue
            
            print(f"\n主隊 ({summary['home_team']['code']}):")
            print(f"  打線確認: {'✅' if summary['home_team']['lineup_confirmed'] else '❌'}")
            print(f"  深度分數: {summary['home_team']['depth_score']:.1f}")
            print(f"  核心強度: {summary['home_team']['core_strength']:.2f}")
            print(f"  指定打擊: {'✅' if summary['home_team']['has_dh'] else '❌'}")
            
            # 顯示先發投手
            if summary['home_team']['starting_pitcher']:
                pitcher = summary['home_team']['starting_pitcher']
                print(f"  先發投手: {pitcher.get('pitcher_name', 'Unknown')}")
            
            # 顯示打線（如果確認）
            if summary['home_team']['lineup_confirmed'] and summary['home_team']['lineup']:
                print("  打線:")
                for player in summary['home_team']['lineup'][:5]:  # 只顯示前5棒
                    print(f"    {player.get('batting_order', '?')}. {player.get('player_name', 'Unknown')} ({player.get('position', 'Unknown')})")
            
            print(f"\n客隊 ({summary['away_team']['code']}):")
            print(f"  打線確認: {'✅' if summary['away_team']['lineup_confirmed'] else '❌'}")
            print(f"  深度分數: {summary['away_team']['depth_score']:.1f}")
            print(f"  核心強度: {summary['away_team']['core_strength']:.2f}")
            print(f"  指定打擊: {'✅' if summary['away_team']['has_dh'] else '❌'}")
            
            # 顯示先發投手
            if summary['away_team']['starting_pitcher']:
                pitcher = summary['away_team']['starting_pitcher']
                print(f"  先發投手: {pitcher.get('pitcher_name', 'Unknown')}")
            
            # 顯示打線（如果確認）
            if summary['away_team']['lineup_confirmed'] and summary['away_team']['lineup']:
                print("  打線:")
                for player in summary['away_team']['lineup'][:5]:  # 只顯示前5棒
                    print(f"    {player.get('batting_order', '?')}. {player.get('player_name', 'Unknown')} ({player.get('position', 'Unknown')})")
            
            print(f"\n📈 比賽分析:")
            print(f"  優勢情況: {summary['advantage']}")
            
            if summary['analysis']:
                print("  分析要點:")
                for point in summary['analysis']:
                    print(f"    - {point}")
    
    # 測試3: 打線特徵完整性檢查
    print(f"\n📊 測試3: 打線特徵完整性檢查")
    print("-" * 50)
    
    # 使用第一場比賽測試
    home_team, away_team = "BOS", "NYY"
    lineup_features = analyzer.get_lineup_features_for_game(home_team, away_team, today)
    
    print(f"打線特徵總數: {len(lineup_features)}")
    print("\n所有打線特徵:")
    
    feature_categories = {
        '基本狀態': ['home_lineup_confirmed', 'away_lineup_confirmed'],
        '深度分析': [k for k in lineup_features.keys() if 'depth' in k or 'strength' in k],
        '順序優化': [k for k in lineup_features.keys() if 'optimization' in k or 'balance' in k],
        '核心分析': [k for k in lineup_features.keys() if 'core' in k or 'bottom' in k],
        '特殊配置': [k for k in lineup_features.keys() if 'dh' in k],
        '比較分析': [k for k in lineup_features.keys() if 'advantage' in k]
    }
    
    for category, features in feature_categories.items():
        print(f"\n{category}:")
        for feature in features:
            if feature in lineup_features:
                value = lineup_features[feature]
                print(f"  {feature}: {value}")
    
    # 測試4: 數值特徵驗證
    print(f"\n📊 測試4: 數值特徵驗證")
    print("-" * 50)
    
    print("檢查所有特徵都是數值型:")
    
    all_numeric = True
    for key, value in lineup_features.items():
        if not isinstance(value, (int, float)):
            print(f"❌ 非數值特徵: {key} = {value} ({type(value)})")
            all_numeric = False
    
    if all_numeric:
        print("✅ 所有特徵都是數值型，可直接用於ML模型")
    
    # 檢查特徵範圍
    print(f"\n特徵值範圍檢查:")
    range_issues = []
    
    for key, value in lineup_features.items():
        if isinstance(value, (int, float)):
            if value < 0 and 'advantage' not in key:  # advantage可以為負
                range_issues.append(f"負值特徵: {key} = {value}")
            elif value > 200:  # 設定合理上限
                range_issues.append(f"異常大值: {key} = {value}")
    
    if range_issues:
        for issue in range_issues:
            print(f"⚠️  {issue}")
    else:
        print("✅ 所有特徵值都在合理範圍內")
    
    # 測試5: 權重系統驗證
    print(f"\n📊 測試5: 權重系統驗證")
    print("-" * 50)
    
    print("位置權重系統:")
    for position, weight in analyzer.position_weights.items():
        print(f"  {position}: {weight}")
    
    print(f"\n打線順序權重系統:")
    for order, weight in analyzer.batting_order_weights.items():
        print(f"  第{order}棒: {weight}")
    
    print(f"\n✅ 打線順序分析系統測試完成")
    print(f"📈 系統可以為每場比賽提供 {len(lineup_features)} 個打線相關特徵")
    print(f"🎯 這些特徵將進一步提升預測準確性")

def main():
    """主函數"""
    test_batting_order_analyzer()

if __name__ == "__main__":
    main()
