#!/usr/bin/env python3
"""
調試球隊名稱匹配問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from models.real_betting_odds_fetcher import RealBettingOddsFetcher

def debug_team_matching():
    """調試球隊名稱匹配"""

    # 創建Flask應用
    app = Flask(__name__)

    with app.app_context():
        print("🔍 調試球隊名稱匹配問題")
        print("=" * 60)

        # 1. 獲取博彩API中的比賽
        odds_fetcher = RealBettingOddsFetcher(app)
        odds_data = odds_fetcher.get_mlb_odds_today()

        print(f"🎯 博彩API中的比賽 (前5場):")
        for i, game in enumerate(odds_data.get('games', [])[:5]):
            print(f"  {game.get('away_team')} @ {game.get('home_team')}")

        print()

        # 2. 測試標準化函數
        print("🔧 測試球隊名稱標準化:")
        test_teams = [
            "TOR", "Toronto Blue Jays",
            "BOS", "Boston Red Sox",
            "PHI", "Philadelphia Phillies",
            "ATL", "Atlanta Braves",
            "CHC", "Chicago Cubs",
            "HOU", "Houston Astros"
        ]

        for team in test_teams:
            normalized = odds_fetcher._normalize_team_name(team)
            print(f"  {team} -> {normalized}")

        print()

        # 3. 測試實際匹配
        print("🎯 測試實際匹配:")

        # 模擬數據庫中的球隊代碼
        test_cases = [
            ("TOR", "BOS"),  # Toronto Blue Jays @ Boston Red Sox
            ("PHI", "ATL"),  # Philadelphia Phillies @ Atlanta Braves
            ("CHC", "HOU")   # Chicago Cubs @ Houston Astros
        ]

        for away_team, home_team in test_cases:
            print(f"測試比賽: {away_team} @ {home_team}")

            # 嘗試匹配
            matched_odds = odds_fetcher.get_game_odds_by_teams(home_team, away_team)

            if matched_odds:
                print("✅ 成功匹配到博彩盤口!")
                print(f"  大小分: {matched_odds.get('total', {}).get('line', 'N/A')}")
            else:
                print("❌ 無法匹配博彩盤口")

                # 顯示詳細匹配過程
                print("詳細匹配過程:")
                home_normalized = odds_fetcher._normalize_team_name(home_team)
                away_normalized = odds_fetcher._normalize_team_name(away_team)
                print(f"  數據庫: {away_team} ({away_normalized}) @ {home_team} ({home_normalized})")

                print("  博彩API中的比賽:")
                for game in odds_data.get('games', []):
                    game_home = odds_fetcher._normalize_team_name(game.get('home_team', ''))
                    game_away = odds_fetcher._normalize_team_name(game.get('away_team', ''))
                    print(f"    {game.get('away_team')} ({game_away}) @ {game.get('home_team')} ({game_home})")

            print()

if __name__ == "__main__":
    debug_team_matching()
