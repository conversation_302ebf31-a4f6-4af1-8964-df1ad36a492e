#!/usr/bin/env python3
"""
隊伍特定主客場與投手ERA細化分析系統
分析特定隊伍對戰組合的詳細表現，包括投手主客場ERA差異
"""

import sys
sys.path.append('.')

import numpy as np
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from collections import defaultdict
import json

# Import removed to avoid circular dependency - using current_app in functions
from models.database import db, Game, Prediction, BettingOdds, PredictionHistory
from models.sbr_pitcher_scraper import SBRPitcherScraper

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TeamPitcherDetailedAnalyzer:
    """隊伍投手細化分析器"""
    
    def __init__(self):
        self.team_matchup_stats = {}
        self.pitcher_home_away_stats = {}
        self.team_vs_pitcher_stats = {}
        self.scraper = SBRPitcherScraper()
        
    def analyze_team_matchup_patterns(self, team1: str, team2: str, days_back: int = 365) -> Dict:
        """分析特定隊伍對戰模式"""
        from flask import current_app
        with current_app.app_context():
            cutoff_date = datetime.now().date() - timedelta(days=days_back)
            
            # 獲取兩隊對戰歷史
            matchups = db.session.query(
                Game.home_team,
                Game.away_team,
                Game.home_score,
                Game.away_score,
                Game.date,
                Game.venue,
                Prediction.predicted_home_score,
                Prediction.predicted_away_score
            ).outerjoin(
                Prediction, Game.game_id == Prediction.game_id
            ).filter(
                Game.date >= cutoff_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None),
                db.or_(
                    db.and_(Game.home_team == team1, Game.away_team == team2),
                    db.and_(Game.home_team == team2, Game.away_team == team1)
                )
            ).order_by(Game.date.desc()).all()
            
            if not matchups:
                return {'error': f'沒有找到 {team1} vs {team2} 的對戰記錄'}
            
            # 分析主客場表現
            home_stats = defaultdict(list)
            away_stats = defaultdict(list)
            
            for game in matchups:
                # 從team1的視角分析
                if game.home_team == team1:
                    # team1主場
                    home_stats[team1].append({
                        'score': game.home_score,
                        'opponent_score': game.away_score,
                        'date': game.date,
                        'venue': game.venue,
                        'predicted': float(game.predicted_home_score) if game.predicted_home_score else None
                    })
                elif game.away_team == team1:
                    # team1客場
                    away_stats[team1].append({
                        'score': game.away_score,
                        'opponent_score': game.home_score,
                        'date': game.date,
                        'venue': game.venue,
                        'predicted': float(game.predicted_away_score) if game.predicted_away_score else None
                    })
                
                # 從team2的視角分析
                if game.home_team == team2:
                    # team2主場
                    home_stats[team2].append({
                        'score': game.home_score,
                        'opponent_score': game.away_score,
                        'date': game.date,
                        'venue': game.venue,
                        'predicted': float(game.predicted_home_score) if game.predicted_home_score else None
                    })
                elif game.away_team == team2:
                    # team2客場
                    away_stats[team2].append({
                        'score': game.away_score,
                        'opponent_score': game.home_score,
                        'date': game.date,
                        'venue': game.venue,
                        'predicted': float(game.predicted_away_score) if game.predicted_away_score else None
                    })
            
            # 計算統計數據
            analysis = {
                'matchup': f'{team1} vs {team2}',
                'total_games': len(matchups),
                'date_range': f"{matchups[-1].date} to {matchups[0].date}",
                'teams': {}
            }
            
            for team in [team1, team2]:
                if home_stats[team]:
                    home_scores = [g['score'] for g in home_stats[team]]
                    home_against = [g['opponent_score'] for g in home_stats[team]]
                    home_predicted = [g['predicted'] for g in home_stats[team] if g['predicted'] is not None]
                else:
                    home_scores = home_against = home_predicted = []
                
                if away_stats[team]:
                    away_scores = [g['score'] for g in away_stats[team]]
                    away_against = [g['opponent_score'] for g in away_stats[team]]
                    away_predicted = [g['predicted'] for g in away_stats[team] if g['predicted'] is not None]
                else:
                    away_scores = away_against = away_predicted = []
                
                analysis['teams'][team] = {
                    'home_performance': {
                        'games': len(home_scores),
                        'avg_score': np.mean(home_scores) if home_scores else 0,
                        'std_score': np.std(home_scores) if home_scores else 0,
                        'min_score': min(home_scores) if home_scores else 0,
                        'max_score': max(home_scores) if home_scores else 0,
                        'avg_against': np.mean(home_against) if home_against else 0,
                        'avg_predicted': np.mean(home_predicted) if home_predicted else None,
                        'prediction_error': np.mean([abs(home_predicted[i] - home_scores[i]) 
                                                   for i in range(min(len(home_predicted), len(home_scores)))]) if home_predicted and home_scores else None
                    },
                    'away_performance': {
                        'games': len(away_scores),
                        'avg_score': np.mean(away_scores) if away_scores else 0,
                        'std_score': np.std(away_scores) if away_scores else 0,
                        'min_score': min(away_scores) if away_scores else 0,
                        'max_score': max(away_scores) if away_scores else 0,
                        'avg_against': np.mean(away_against) if away_against else 0,
                        'avg_predicted': np.mean(away_predicted) if away_predicted else None,
                        'prediction_error': np.mean([abs(away_predicted[i] - away_scores[i]) 
                                                   for i in range(min(len(away_predicted), len(away_scores)))]) if away_predicted and away_scores else None
                    }
                }
                
                # 計算主客場差異
                if home_scores and away_scores:
                    analysis['teams'][team]['home_away_difference'] = {
                        'score_diff': np.mean(home_scores) - np.mean(away_scores),
                        'variance_diff': np.std(home_scores) - np.std(away_scores),
                        'home_advantage': np.mean(home_scores) > np.mean(away_scores)
                    }
            
            return analysis
    
    def analyze_pitcher_home_away_performance(self, pitcher_name: str, days_back: int = 365) -> Dict:
        """分析投手主客場表現差異"""
        from flask import current_app
        with current_app.app_context():
            cutoff_date = datetime.now().date() - timedelta(days=days_back)
            
            # 從預測歷史中獲取投手數據
            pitcher_games = db.session.query(
                PredictionHistory.starting_pitcher_home,
                PredictionHistory.starting_pitcher_away,
                PredictionHistory.actual_home_score,
                PredictionHistory.actual_away_score,
                PredictionHistory.predicted_home_score,
                PredictionHistory.predicted_away_score,
                Game.home_team,
                Game.away_team,
                Game.date,
                Game.venue
            ).join(
                Game, PredictionHistory.game_id == Game.game_id
            ).filter(
                Game.date >= cutoff_date,
                PredictionHistory.actual_home_score.isnot(None),
                PredictionHistory.actual_away_score.isnot(None),
                db.or_(
                    PredictionHistory.starting_pitcher_home.like(f'%{pitcher_name}%'),
                    PredictionHistory.starting_pitcher_away.like(f'%{pitcher_name}%')
                )
            ).all()
            
            home_starts = []
            away_starts = []
            
            for game in pitcher_games:
                if game.starting_pitcher_home and pitcher_name in game.starting_pitcher_home:
                    # 投手主場先發
                    runs_allowed = game.actual_away_score
                    home_starts.append({
                        'date': game.date,
                        'team': game.home_team,
                        'opponent': game.away_team,
                        'venue': game.venue,
                        'runs_allowed': runs_allowed,
                        'team_runs': game.actual_home_score,
                        'predicted_allowed': float(game.predicted_away_score) if game.predicted_away_score else None
                    })
                elif game.starting_pitcher_away and pitcher_name in game.starting_pitcher_away:
                    # 投手客場先發
                    runs_allowed = game.actual_home_score
                    away_starts.append({
                        'date': game.date,
                        'team': game.away_team,
                        'opponent': game.home_team,
                        'venue': game.venue,
                        'runs_allowed': runs_allowed,
                        'team_runs': game.actual_away_score,
                        'predicted_allowed': float(game.predicted_home_score) if game.predicted_home_score else None
                    })
            
            if not home_starts and not away_starts:
                return {'error': f'沒有找到投手 {pitcher_name} 的比賽記錄'}
            
            analysis = {
                'pitcher': pitcher_name,
                'total_starts': len(home_starts) + len(away_starts),
                'date_range': f"{cutoff_date} to {datetime.now().date()}"
            }
            
            # 主場先發分析
            if home_starts:
                home_runs_allowed = [g['runs_allowed'] for g in home_starts]
                home_team_runs = [g['team_runs'] for g in home_starts]
                
                analysis['home_starts'] = {
                    'games': len(home_starts),
                    'era_equivalent': np.mean(home_runs_allowed) * 9 / 9,  # 簡化ERA計算
                    'avg_runs_allowed': np.mean(home_runs_allowed),
                    'std_runs_allowed': np.std(home_runs_allowed),
                    'min_runs_allowed': min(home_runs_allowed),
                    'max_runs_allowed': max(home_runs_allowed),
                    'team_avg_runs': np.mean(home_team_runs),
                    'recent_games': home_starts[:5]  # 最近5場
                }
            
            # 客場先發分析
            if away_starts:
                away_runs_allowed = [g['runs_allowed'] for g in away_starts]
                away_team_runs = [g['team_runs'] for g in away_starts]
                
                analysis['away_starts'] = {
                    'games': len(away_starts),
                    'era_equivalent': np.mean(away_runs_allowed) * 9 / 9,
                    'avg_runs_allowed': np.mean(away_runs_allowed),
                    'std_runs_allowed': np.std(away_runs_allowed),
                    'min_runs_allowed': min(away_runs_allowed),
                    'max_runs_allowed': max(away_runs_allowed),
                    'team_avg_runs': np.mean(away_team_runs),
                    'recent_games': away_starts[:5]  # 最近5場
                }
            
            # 主客場差異分析
            if home_starts and away_starts:
                home_era = analysis['home_starts']['era_equivalent']
                away_era = analysis['away_starts']['era_equivalent']
                
                analysis['home_away_difference'] = {
                    'era_difference': home_era - away_era,
                    'performance_advantage': 'home' if home_era < away_era else 'away',
                    'advantage_magnitude': abs(home_era - away_era),
                    'consistency_home': 1 / analysis['home_starts']['std_runs_allowed'] if analysis['home_starts']['std_runs_allowed'] > 0 else float('inf'),
                    'consistency_away': 1 / analysis['away_starts']['std_runs_allowed'] if analysis['away_starts']['std_runs_allowed'] > 0 else float('inf')
                }
            
            return analysis
    
    def analyze_team_vs_specific_pitcher(self, team: str, pitcher_name: str, days_back: int = 730) -> Dict:
        """分析特定隊伍對戰特定投手的歷史表現"""
        from flask import current_app
        with current_app.app_context():
            cutoff_date = datetime.now().date() - timedelta(days=days_back)
            
            # 查找隊伍對戰該投手的記錄
            games = db.session.query(
                Game.home_team,
                Game.away_team,
                Game.home_score,
                Game.away_score,
                Game.date,
                Game.venue,
                PredictionHistory.starting_pitcher_home,
                PredictionHistory.starting_pitcher_away,
                PredictionHistory.predicted_home_score,
                PredictionHistory.predicted_away_score
            ).outerjoin(
                PredictionHistory, Game.game_id == PredictionHistory.game_id
            ).filter(
                Game.date >= cutoff_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None),
                db.or_(
                    # team作為主隊對戰該投手
                    db.and_(
                        Game.home_team == team,
                        PredictionHistory.starting_pitcher_away.like(f'%{pitcher_name}%')
                    ),
                    # team作為客隊對戰該投手
                    db.and_(
                        Game.away_team == team,
                        PredictionHistory.starting_pitcher_home.like(f'%{pitcher_name}%')
                    )
                )
            ).all()
            
            if not games:
                return {'error': f'沒有找到 {team} 對戰 {pitcher_name} 的記錄'}
            
            team_vs_pitcher_stats = []
            
            for game in games:
                if game.home_team == team:
                    # team主場對戰該投手
                    team_vs_pitcher_stats.append({
                        'date': game.date,
                        'location': 'home',
                        'team_score': game.home_score,
                        'pitcher_runs_allowed': game.home_score,
                        'opponent_score': game.away_score,
                        'venue': game.venue,
                        'predicted_team_score': float(game.predicted_home_score) if game.predicted_home_score else None,
                        'pitcher': game.starting_pitcher_away
                    })
                else:
                    # team客場對戰該投手
                    team_vs_pitcher_stats.append({
                        'date': game.date,
                        'location': 'away',
                        'team_score': game.away_score,
                        'pitcher_runs_allowed': game.away_score,
                        'opponent_score': game.home_score,
                        'venue': game.venue,
                        'predicted_team_score': float(game.predicted_away_score) if game.predicted_away_score else None,
                        'pitcher': game.starting_pitcher_home
                    })
            
            # 分析統計數據
            team_scores = [g['team_score'] for g in team_vs_pitcher_stats]
            pitcher_runs_allowed = [g['pitcher_runs_allowed'] for g in team_vs_pitcher_stats]
            
            home_games = [g for g in team_vs_pitcher_stats if g['location'] == 'home']
            away_games = [g for g in team_vs_pitcher_stats if g['location'] == 'away']
            
            analysis = {
                'team': team,
                'pitcher': pitcher_name,
                'total_matchups': len(team_vs_pitcher_stats),
                'overall_stats': {
                    'avg_team_score': np.mean(team_scores),
                    'std_team_score': np.std(team_scores),
                    'min_team_score': min(team_scores),
                    'max_team_score': max(team_scores),
                    'pitcher_avg_allowed': np.mean(pitcher_runs_allowed),
                    'pitcher_era_vs_team': np.mean(pitcher_runs_allowed) * 9 / 9
                }
            }
            
            # 主場表現
            if home_games:
                home_scores = [g['team_score'] for g in home_games]
                analysis['home_vs_pitcher'] = {
                    'games': len(home_games),
                    'avg_score': np.mean(home_scores),
                    'std_score': np.std(home_scores),
                    'pitcher_era_allowed': np.mean(home_scores) * 9 / 9,
                    'recent_games': home_games[:3]
                }
            
            # 客場表現
            if away_games:
                away_scores = [g['team_score'] for g in away_games]
                analysis['away_vs_pitcher'] = {
                    'games': len(away_games),
                    'avg_score': np.mean(away_scores),
                    'std_score': np.std(away_scores),
                    'pitcher_era_allowed': np.mean(away_scores) * 9 / 9,
                    'recent_games': away_games[:3]
                }
            
            # 主客場差異
            if home_games and away_games:
                home_avg = analysis['home_vs_pitcher']['avg_score']
                away_avg = analysis['away_vs_pitcher']['avg_score']
                analysis['home_away_split'] = {
                    'home_advantage': home_avg - away_avg,
                    'era_difference': analysis['away_vs_pitcher']['pitcher_era_allowed'] - analysis['home_vs_pitcher']['pitcher_era_allowed'],
                    'better_location': 'home' if home_avg > away_avg else 'away'
                }
            
            return analysis
    
    def get_pitcher_era_by_venue_type(self, pitcher_name: str, days_back: int = 365) -> Dict:
        """獲取投手在不同球場類型的ERA表現"""
        from flask import current_app
        with current_app.app_context():
            cutoff_date = datetime.now().date() - timedelta(days=days_back)
            
            # 從SBR獲取投手詳細數據
            try:
                pitcher_data = self.scraper.get_pitcher_career_stats(pitcher_name)
                if pitcher_data and 'splits' in pitcher_data:
                    splits = pitcher_data['splits']
                    
                    era_analysis = {
                        'pitcher': pitcher_name,
                        'home_era': splits.get('home_era'),
                        'away_era': splits.get('away_era'),
                        'era_difference': splits.get('away_era', 0) - splits.get('home_era', 0) if splits.get('home_era') and splits.get('away_era') else None,
                        'home_games': splits.get('home_games', 0),
                        'away_games': splits.get('away_games', 0)
                    }
                    
                    # 加入球場特定數據
                    if 'ballpark_stats' in pitcher_data:
                        era_analysis['ballpark_performance'] = pitcher_data['ballpark_stats']
                    
                    return era_analysis
                    
            except Exception as e:
                logger.warning(f"無法從SBR獲取 {pitcher_name} 數據: {e}")
            
            # 備用方案：從本地數據庫分析
            pitcher_starts = db.session.query(
                Game.home_team,
                Game.away_team,
                Game.home_score,
                Game.away_score,
                Game.venue,
                Game.date,
                PredictionHistory.starting_pitcher_home,
                PredictionHistory.starting_pitcher_away
            ).join(
                PredictionHistory, Game.game_id == PredictionHistory.game_id
            ).filter(
                Game.date >= cutoff_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None),
                db.or_(
                    PredictionHistory.starting_pitcher_home.like(f'%{pitcher_name}%'),
                    PredictionHistory.starting_pitcher_away.like(f'%{pitcher_name}%')
                )
            ).all()
            
            home_starts_data = []
            away_starts_data = []
            
            for game in pitcher_starts:
                if game.starting_pitcher_home and pitcher_name in game.starting_pitcher_home:
                    # 主場先發
                    home_starts_data.append({
                        'runs_allowed': game.away_score,
                        'team': game.home_team,
                        'venue': game.venue,
                        'date': game.date
                    })
                elif game.starting_pitcher_away and pitcher_name in game.starting_pitcher_away:
                    # 客場先發
                    away_starts_data.append({
                        'runs_allowed': game.home_score,
                        'team': game.away_team,
                        'venue': game.venue,
                        'date': game.date
                    })
            
            if not home_starts_data and not away_starts_data:
                return {'error': f'沒有找到投手 {pitcher_name} 的先發記錄'}
            
            analysis = {
                'pitcher': pitcher_name,
                'total_starts': len(home_starts_data) + len(away_starts_data)
            }
            
            if home_starts_data:
                home_runs = [g['runs_allowed'] for g in home_starts_data]
                analysis['home_starts'] = {
                    'games': len(home_runs),
                    'era_equivalent': np.mean(home_runs) * 9 / 9,
                    'avg_runs_allowed': np.mean(home_runs),
                    'std_runs_allowed': np.std(home_runs),
                    'best_start': min(home_runs),
                    'worst_start': max(home_runs)
                }
            
            if away_starts_data:
                away_runs = [g['runs_allowed'] for g in away_starts_data]
                analysis['away_starts'] = {
                    'games': len(away_runs),
                    'era_equivalent': np.mean(away_runs) * 9 / 9,
                    'avg_runs_allowed': np.mean(away_runs),
                    'std_runs_allowed': np.std(away_runs),
                    'best_start': min(away_runs),
                    'worst_start': max(away_runs)
                }
            
            # 主客場ERA差異
            if home_starts_data and away_starts_data:
                home_era = analysis['home_starts']['era_equivalent']
                away_era = analysis['away_starts']['era_equivalent']
                
                analysis['era_splits'] = {
                    'era_difference': away_era - home_era,
                    'better_location': 'home' if home_era < away_era else 'away',
                    'consistency_home': analysis['home_starts']['std_runs_allowed'],
                    'consistency_away': analysis['away_starts']['std_runs_allowed'],
                    'more_consistent': 'home' if analysis['home_starts']['std_runs_allowed'] < analysis['away_starts']['std_runs_allowed'] else 'away'
                }
            
            return analysis

def analyze_specific_matchup(team1: str = "NYY", team2: str = "BOS", pitcher: str = "Cole"):
    """分析特定對戰組合範例：NYY vs BOS with Cole pitching"""
    
    analyzer = TeamPitcherDetailedAnalyzer()
    
    print(f"🔍 分析 {team1} vs {team2} 對戰模式...")
    matchup_analysis = analyzer.analyze_team_matchup_patterns(team1, team2)
    
    if 'error' not in matchup_analysis:
        print(f"\n📊 {team1} vs {team2} 對戰分析 ({matchup_analysis['total_games']}場比賽):")
        
        for team in [team1, team2]:
            team_stats = matchup_analysis['teams'][team]
            home_perf = team_stats['home_performance']
            away_perf = team_stats['away_performance']
            
            print(f"\n🏠 {team} 主場對戰 {team2 if team == team1 else team1}:")
            print(f"   場次: {home_perf['games']} | 平均得分: {home_perf['avg_score']:.2f}±{home_perf['std_score']:.2f}")
            print(f"   分數範圍: {home_perf['min_score']}-{home_perf['max_score']} | 失分: {home_perf['avg_against']:.2f}")
            if home_perf['prediction_error']:
                print(f"   預測誤差: {home_perf['prediction_error']:.2f}")
            
            print(f"\n✈️  {team} 客場對戰 {team2 if team == team1 else team1}:")
            print(f"   場次: {away_perf['games']} | 平均得分: {away_perf['avg_score']:.2f}±{away_perf['std_score']:.2f}")
            print(f"   分數範圍: {away_perf['min_score']}-{away_perf['max_score']} | 失分: {away_perf['avg_against']:.2f}")
            if away_perf['prediction_error']:
                print(f"   預測誤差: {away_perf['prediction_error']:.2f}")
            
            if 'home_away_difference' in team_stats:
                diff = team_stats['home_away_difference']
                print(f"\n📈 {team} 主客場差異:")
                print(f"   得分差: {diff['score_diff']:.2f} ({'主場優勢' if diff['home_advantage'] else '客場更佳'})")
                print(f"   穩定性差: {diff['variance_diff']:.2f}")
    
    print(f"\n🎯 分析投手 {pitcher} 主客場ERA表現...")
    pitcher_analysis = analyzer.analyze_pitcher_home_away_performance(pitcher)
    
    if 'error' not in pitcher_analysis:
        print(f"\n⚾ {pitcher} 投手分析 ({pitcher_analysis['total_starts']}次先發):")
        
        if 'home_starts' in pitcher_analysis:
            home_stats = pitcher_analysis['home_starts']
            print(f"\n🏠 主場先發 ({home_stats['games']}場):")
            print(f"   ERA: {home_stats['era_equivalent']:.2f} | 平均失分: {home_stats['avg_runs_allowed']:.2f}±{home_stats['std_runs_allowed']:.2f}")
            print(f"   失分範圍: {home_stats['best_start']}-{home_stats['worst_start']} | 隊友得分: {home_stats['team_avg_runs']:.2f}")
        
        if 'away_starts' in pitcher_analysis:
            away_stats = pitcher_analysis['away_starts']
            print(f"\n✈️  客場先發 ({away_stats['games']}場):")
            print(f"   ERA: {away_stats['era_equivalent']:.2f} | 平均失分: {away_stats['avg_runs_allowed']:.2f}±{away_stats['std_runs_allowed']:.2f}")
            print(f"   失分範圍: {away_stats['best_start']}-{away_stats['worst_start']} | 隊友得分: {away_stats['team_avg_runs']:.2f}")
        
        if 'era_splits' in pitcher_analysis:
            splits = pitcher_analysis['era_splits']
            print(f"\n📊 主客場ERA差異:")
            print(f"   ERA差距: {splits['era_difference']:.2f} ({splits['better_location']}場較佳)")
            print(f"   穩定性: 主場±{splits['consistency_home']:.2f} vs 客場±{splits['consistency_away']:.2f}")
    
    print(f"\n🎲 分析 {team1} 對戰投手 {pitcher} 的歷史表現...")
    team_vs_pitcher = analyzer.analyze_team_vs_specific_pitcher(team1, pitcher)
    
    if 'error' not in team_vs_pitcher:
        print(f"\n🆚 {team1} vs {pitcher} 對戰記錄 ({team_vs_pitcher['total_matchups']}場):")
        print(f"   整體: 平均得分 {team_vs_pitcher['overall_stats']['avg_team_score']:.2f}, 投手ERA {team_vs_pitcher['overall_stats']['pitcher_era_vs_team']:.2f}")
        
        if 'home_vs_pitcher' in team_vs_pitcher:
            home_vs = team_vs_pitcher['home_vs_pitcher']
            print(f"   主場對戰: {home_vs['games']}場, 平均得分 {home_vs['avg_score']:.2f}, 投手ERA {home_vs['pitcher_era_allowed']:.2f}")
        
        if 'away_vs_pitcher' in team_vs_pitcher:
            away_vs = team_vs_pitcher['away_vs_pitcher']
            print(f"   客場對戰: {away_vs['games']}場, 平均得分 {away_vs['avg_score']:.2f}, 投手ERA {away_vs['pitcher_era_allowed']:.2f}")

def main():
    """主函數 - 執行細化分析"""
    print("🎯 執行隊伍投手細化分析...")
    
    # 分析範例：NYY vs BOS with Cole pitching
    analyze_specific_matchup("NYY", "BOS", "Cole")
    
    print(f"\n" + "="*60)
    
    # 分析其他常見對戰
    common_matchups = [
        ("LAD", "SF", "Kershaw"),
        ("HOU", "TEX", "Verlander"),
        ("ATL", "PHI", "Fried")
    ]
    
    for team1, team2, pitcher in common_matchups:
        print(f"\n🔄 快速分析 {team1} vs {team2} with {pitcher}...")
        try:
            analyze_specific_matchup(team1, team2, pitcher)
        except Exception as e:
            print(f"   ⚠️ 分析失敗: {e}")

if __name__ == "__main__":
    main()