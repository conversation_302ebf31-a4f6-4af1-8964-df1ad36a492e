#!/usr/bin/env python3
"""
快速修復 - 為特定日期創建基本預測記錄
"""

import sys
from datetime import datetime
from flask import Flask
from models.database import db, Game, Prediction

def create_basic_predictions(target_date):
    """為指定日期創建基本預測記錄"""
    
    print(f"🔧 為日期 {target_date} 創建基本預測記錄")
    print("=" * 50)
    
    # 查找該日期沒有預測記錄的比賽
    games_without_predictions = db.session.query(Game).filter(
        Game.date == target_date,
        ~Game.game_id.in_(
            db.session.query(Prediction.game_id)
        )
    ).all()
    
    print(f"📅 找到 {len(games_without_predictions)} 場比賽需要創建預測")
    
    if not games_without_predictions:
        print("✅ 所有比賽都已有預測記錄")
        return
    
    # 為每場比賽創建基本預測記錄
    success_count = 0
    for game in games_without_predictions:
        try:
            # 創建基本預測（使用簡單的平均分數）
            prediction = Prediction(
                game_id=game.game_id,
                predicted_home_score=4.5,  # 基本預測分數
                predicted_away_score=4.2,
                predicted_total_runs=8.7,
                home_win_probability=0.52,
                away_win_probability=0.48,
                confidence=0.65,
                over_under_line=8.5,
                over_probability=0.51,
                under_probability=0.49,
                over_under_confidence=0.60,
                starting_pitcher_home='未確認',
                starting_pitcher_away='未確認',
                pitcher_matchup_advantage='neutral',
                model_version='basic_fix_v1.0',
                prediction_date=datetime.utcnow()
            )
            
            db.session.add(prediction)
            success_count += 1
            print(f"✅ 已創建預測: {game.away_team} @ {game.home_team}")
            
        except Exception as e:
            print(f"❌ 創建預測失敗: {game.away_team} @ {game.home_team} - {e}")
    
    # 提交更改
    try:
        db.session.commit()
        print(f"\n🎉 成功創建 {success_count} 條預測記錄")
        print("✅ 數據庫更改已提交")
    except Exception as e:
        print(f"❌ 數據庫提交失敗: {e}")
        db.session.rollback()

def main():
    """主函數"""
    if len(sys.argv) != 2:
        print("使用方法: python quick_fix_predictions.py YYYY-MM-DD")
        sys.exit(1)
    
    date_str = sys.argv[1]
    try:
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        print("❌ 日期格式錯誤，請使用 YYYY-MM-DD 格式")
        sys.exit(1)
    
    # 創建Flask應用上下文
    from app import app
    with app.app_context():
        create_basic_predictions(target_date)

if __name__ == "__main__":
    main()