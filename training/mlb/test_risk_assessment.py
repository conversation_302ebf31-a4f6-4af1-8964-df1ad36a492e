#!/usr/bin/env python3
"""
測試風險評估系統
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction, BettingOdds
from models.risk_assessment_predictor import get_risk_assessor
import pandas as pd

def test_risk_assessment():
    """測試風險評估系統"""
    app = create_app()
    
    with app.app_context():
        print("🧪 測試風險評估系統...")
        
        # 獲取最近的比賽數據
        query = """
        SELECT 
            g.game_id,
            g.date,
            g.home_team,
            g.away_team,
            g.home_score,
            g.away_score,
            (g.home_score + g.away_score) as actual_total,
            p.predicted_home_score,
            p.predicted_away_score,
            (p.predicted_home_score + p.predicted_away_score) as predicted_total,
            p.confidence,
            bo.total_point as betting_line
        FROM games g
        JOIN predictions p ON g.game_id = p.game_id
        LEFT JOIN betting_odds bo ON g.game_id = bo.game_id
        WHERE g.home_score IS NOT NULL 
        AND g.away_score IS NOT NULL
        AND p.predicted_home_score IS NOT NULL
        AND p.predicted_away_score IS NOT NULL
        AND g.date >= DATE('now', '-7 days')
        ORDER BY g.date DESC
        LIMIT 20
        """
        
        results = db.session.execute(db.text(query)).fetchall()
        
        columns = ['game_id', 'date', 'home_team', 'away_team', 'home_score', 'away_score', 
                  'actual_total', 'predicted_home_score', 'predicted_away_score', 'predicted_total', 
                  'confidence', 'betting_line']
        
        df = pd.DataFrame(results, columns=columns)
        
        print(f"📊 測試 {len(df)} 場比賽")
        
        # 獲取風險評估器
        risk_assessor = get_risk_assessor()
        
        # 準備測試數據
        test_games = []
        for _, row in df.iterrows():
            game_data = {
                'game_id': row['game_id'],
                'home_team': row['home_team'],
                'away_team': row['away_team'],
                'date': row['date'],
                'betting_line': row['betting_line']
            }
            
            prediction_data = {
                'predicted_total': row['predicted_total'],
                'predicted_home_score': row['predicted_home_score'],
                'predicted_away_score': row['predicted_away_score'],
                'confidence': row['confidence']
            }
            
            test_games.append((game_data, prediction_data))
        
        # 批量評估風險
        assessment_results = risk_assessor.batch_assess_games(test_games)
        
        # 顯示結果
        print(f"\n📋 風險評估結果:")
        print(f"{'日期':<12} {'比賽':<15} {'預測':<8} {'實際':<8} {'信心度':<12} {'風險等級':<10} {'建議':<10}")
        print("-" * 85)
        
        for i, result in enumerate(assessment_results):
            row = df.iloc[i]
            date_str = str(row['date'])[:10]
            teams = f"{row['away_team']}@{row['home_team']}"
            predicted = f"{result['predicted_total']:.1f}"
            actual = f"{row['actual_total']:.0f}"
            confidence = f"{result['original_confidence']:.2f}→{result['adjusted_confidence']:.2f}"
            risk_level = result['risk_level']
            action = result['betting_action'][:6]
            
            print(f"{date_str:<12} {teams:<15} {predicted:<8} {actual:<8} {confidence:<12} {risk_level:<10} {action:<10}")
            
            if result['risk_factors']:
                print(f"             風險因子: {', '.join(result['risk_factors'])}")
        
        # 生成風險報告
        risk_report = risk_assessor.generate_risk_report(assessment_results)
        
        print(f"\n📊 風險評估報告:")
        print(f"  總比賽數: {risk_report['total_games']}")
        print(f"  排除比賽: {risk_report['excluded_games']} ({risk_report['exclusion_rate']:.1f}%)")
        print(f"  高風險比賽: {risk_report['high_risk_games']} ({risk_report['high_risk_rate']:.1f}%)")
        
        print(f"\n  風險分布:")
        for risk_level, count in risk_report['risk_distribution'].items():
            percentage = count / risk_report['total_games'] * 100
            print(f"    {risk_level}: {count} 場 ({percentage:.1f}%)")
        
        print(f"\n  投注建議:")
        print(f"    安全投注: {risk_report['recommendations']['safe_to_bet']} 場")
        print(f"    謹慎投注: {risk_report['recommendations']['cautious_betting']} 場")
        print(f"    避免投注: {risk_report['recommendations']['avoid_betting']} 場")
        
        # 測試特定極端案例
        test_extreme_cases(risk_assessor)

def test_extreme_cases(risk_assessor):
    """測試極端案例"""
    print(f"\n🚨 測試極端案例:")
    
    # 案例1: 低分隊伍預測高分
    case1_game = {
        'game_id': 'test_001',
        'home_team': 'CWS',  # 低分隊伍
        'away_team': 'SD',   # 低分隊伍
        'betting_line': 8.5
    }
    case1_pred = {
        'predicted_total': 15.0,  # 預測高分
        'confidence': 0.8
    }
    
    result1 = risk_assessor.get_prediction_recommendation(case1_game, case1_pred)
    print(f"  案例1 - 低分隊伍預測高分:")
    print(f"    風險等級: {result1['risk_assessment']['risk_level']}")
    print(f"    信心度調整: {result1['confidence_adjustment']['original_confidence']:.2f} → {result1['confidence_adjustment']['adjusted_confidence']:.2f}")
    print(f"    投注建議: {result1['betting_recommendation']['action']}")
    print(f"    風險因子: {', '.join(result1['risk_assessment']['risk_factors'])}")
    
    # 案例2: 高分隊伍預測低分
    case2_game = {
        'game_id': 'test_002',
        'home_team': 'BOS',  # 高分隊伍
        'away_team': 'WSH',  # 高分隊伍
        'betting_line': 9.5
    }
    case2_pred = {
        'predicted_total': 6.0,  # 預測低分
        'confidence': 0.7
    }
    
    result2 = risk_assessor.get_prediction_recommendation(case2_game, case2_pred)
    print(f"\n  案例2 - 高分隊伍預測低分:")
    print(f"    風險等級: {result2['risk_assessment']['risk_level']}")
    print(f"    信心度調整: {result2['confidence_adjustment']['original_confidence']:.2f} → {result2['confidence_adjustment']['adjusted_confidence']:.2f}")
    print(f"    投注建議: {result2['betting_recommendation']['action']}")
    print(f"    風險因子: {', '.join(result2['risk_assessment']['risk_factors'])}")
    
    # 案例3: 穩定隊伍正常預測
    case3_game = {
        'game_id': 'test_003',
        'home_team': 'NYY',  # 穩定隊伍
        'away_team': 'LAA',  # 穩定隊伍
        'betting_line': 9.0
    }
    case3_pred = {
        'predicted_total': 9.2,  # 接近盤口
        'confidence': 0.6
    }
    
    result3 = risk_assessor.get_prediction_recommendation(case3_game, case3_pred)
    print(f"\n  案例3 - 穩定隊伍正常預測:")
    print(f"    風險等級: {result3['risk_assessment']['risk_level']}")
    print(f"    信心度調整: {result3['confidence_adjustment']['original_confidence']:.2f} → {result3['confidence_adjustment']['adjusted_confidence']:.2f}")
    print(f"    投注建議: {result3['betting_recommendation']['action']}")
    print(f"    風險因子: {', '.join(result3['risk_assessment']['risk_factors']) if result3['risk_assessment']['risk_factors'] else '無'}")
    
    # 案例4: 極端偏離盤口
    case4_game = {
        'game_id': 'test_004',
        'home_team': 'TEX',  # 不穩定隊伍
        'away_team': 'BAL',  # 不穩定隊伍
        'betting_line': 8.0
    }
    case4_pred = {
        'predicted_total': 15.0,  # 嚴重偏離盤口
        'confidence': 0.9
    }
    
    result4 = risk_assessor.get_prediction_recommendation(case4_game, case4_pred)
    print(f"\n  案例4 - 極端偏離盤口:")
    print(f"    風險等級: {result4['risk_assessment']['risk_level']}")
    print(f"    信心度調整: {result4['confidence_adjustment']['original_confidence']:.2f} → {result4['confidence_adjustment']['adjusted_confidence']:.2f}")
    print(f"    投注建議: {result4['betting_recommendation']['action']}")
    print(f"    是否排除: {'是' if result4['should_exclude'] else '否'}")
    print(f"    風險因子: {', '.join(result4['risk_assessment']['risk_factors'])}")

if __name__ == "__main__":
    print("🚀 開始測試風險評估系統...")
    
    test_risk_assessment()
    
    print("\n✅ 測試完成！")
