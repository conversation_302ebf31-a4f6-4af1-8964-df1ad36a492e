#!/usr/bin/env python3
"""
檢查數據庫結構和數據
"""

import sqlite3
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, TeamStats, Team

def check_db_structure():
    """檢查數據庫結構"""
    print("🔍 檢查數據庫結構和數據")
    print("=" * 50)
    
    # 1. 檢查SQLite數據庫結構
    print("📊 SQLite數據庫結構:")
    conn = sqlite3.connect('instance/mlb_data.db')
    cursor = conn.cursor()
    
    # 檢查team_stats表結構
    cursor.execute("PRAGMA table_info(team_stats);")
    columns = cursor.fetchall()
    print("\nteam_stats表結構:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # 檢查teams表結構
    cursor.execute("PRAGMA table_info(teams);")
    columns = cursor.fetchall()
    print("\nteams表結構:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # 檢查數據樣本
    print("\n📈 數據樣本:")
    cursor.execute("SELECT COUNT(*) FROM team_stats;")
    count = cursor.fetchone()[0]
    print(f"team_stats記錄數: {count}")
    
    cursor.execute("SELECT COUNT(*) FROM teams;")
    count = cursor.fetchone()[0]
    print(f"teams記錄數: {count}")
    
    # 檢查team_stats數據樣本
    cursor.execute("SELECT * FROM team_stats LIMIT 3;")
    rows = cursor.fetchall()
    print("\nteam_stats樣本數據:")
    for row in rows:
        print(f"  {row}")
    
    # 檢查teams數據樣本
    cursor.execute("SELECT team_id, team_code, team_name FROM teams LIMIT 5;")
    rows = cursor.fetchall()
    print("\nteams樣本數據:")
    for row in rows:
        print(f"  {row}")
    
    conn.close()
    
    # 2. 檢查Flask ORM
    print("\n🔧 Flask ORM檢查:")
    app = create_app()
    with app.app_context():
        # 檢查TeamStats
        team_stats_count = TeamStats.query.count()
        print(f"TeamStats ORM記錄數: {team_stats_count}")
        
        # 檢查Teams
        teams_count = Team.query.count()
        print(f"Team ORM記錄數: {teams_count}")
        
        # 檢查特定球隊
        test_teams = ['LAD', 'NYY', 'OAK', 'MIA']
        print("\n🏟️ 測試球隊查詢:")
        for team_code in test_teams:
            team = Team.query.filter_by(team_code=team_code).first()
            if team:
                stats = TeamStats.query.filter_by(team_id=team.team_id).first()
                if stats:
                    print(f"  {team_code} (ID:{team.team_id}): ✅ 有統計數據")
                    print(f"    得分: {stats.runs_scored:.2f}, ERA: {stats.era:.2f}")
                else:
                    print(f"  {team_code} (ID:{team.team_id}): ❌ 無統計數據")
            else:
                print(f"  {team_code}: ❌ 找不到球隊")

if __name__ == "__main__":
    check_db_structure()
