#!/usr/bin/env python3
"""
測試covers.com抓取器功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.covers_scraper import CoversMLBScraper
from datetime import date, timedelta
import json

def test_covers_scraper():
    """測試covers.com抓取器"""
    print("=== 測試 Covers.com MLB 抓取器 ===")
    
    scraper = CoversMLBScraper()
    
    # 測試日期：2024年7月7日（確保有比賽數據）
    test_date = date(2024, 7, 7)
    
    print(f"測試日期: {test_date}")
    print("正在抓取數據...")
    
    try:
        result = scraper.fetch_mlb_games_for_date(test_date)
        
        print(f"抓取結果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result['success']:
            print(f"✅ 成功抓取到 {result['total_games']} 場比賽")
            
            # 顯示比賽詳情
            for i, game in enumerate(result['games'], 1):
                print(f"\n比賽 {i}:")
                print(f"  客隊: {game['away_team']}")
                print(f"  主隊: {game['home_team']}")
                print(f"  客隊分數: {game.get('away_score', 'N/A')}")
                print(f"  主隊分數: {game.get('home_score', 'N/A')}")
                print(f"  狀態: {game.get('status', 'N/A')}")
                
                if game.get('odds'):
                    print(f"  賠率信息: {game['odds']}")
        else:
            print(f"❌ 抓取失敗: {result.get('error', '未知錯誤')}")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_dates():
    """測試多個日期"""
    print("\n=== 測試多個日期 ===")
    
    scraper = CoversMLBScraper()
    
    # 測試最近幾天
    test_dates = [
        date(2024, 7, 7),
        date(2024, 7, 6),
        date(2024, 7, 5),
    ]
    
    for test_date in test_dates:
        print(f"\n測試日期: {test_date}")
        
        try:
            result = scraper.fetch_mlb_games_for_date(test_date)
            
            if result['success']:
                print(f"✅ {test_date}: {result['total_games']} 場比賽")
            else:
                print(f"❌ {test_date}: {result.get('error', '失敗')}")
                
        except Exception as e:
            print(f"❌ {test_date}: 異常 - {e}")
        
        # 添加延遲
        scraper.add_delay(1.0)

def test_team_mapping():
    """測試球隊名稱映射"""
    print("\n=== 測試球隊名稱映射 ===")
    
    scraper = CoversMLBScraper()
    
    test_names = [
        "NY Yankees",
        "LA Dodgers", 
        "Boston",
        "Chi. Cubs",
        "Tampa Bay"
    ]
    
    for name in test_names:
        abbr = scraper.get_team_abbreviation(name)
        print(f"{name} -> {abbr}")

if __name__ == "__main__":
    test_covers_scraper()
    test_multiple_dates()
    test_team_mapping()
    
    print("\n=== 測試完成 ===")
