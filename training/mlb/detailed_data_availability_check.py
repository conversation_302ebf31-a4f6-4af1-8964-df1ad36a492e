#!/usr/bin/env python3
"""
詳細檢查MLB預測系統中缺少數據的實際可用性
"""

import logging
from datetime import date, timedelta
from app import create_app
from models.database import db, Game, GameDetail, PlayerStats, Player, Team, TeamStats
from models.enhanced_data_models import (
    TeamAdvancedStats, PlayerPerformanceTrends, PlayerInjuryReport, 
    TeamChemistry, WeatherConditions
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_detailed_data_availability():
    """詳細檢查數據可用性"""
    app = create_app()
    
    with app.app_context():
        print("=" * 80)
        print("🔍 MLB預測系統 - 詳細數據可用性檢查")
        print("=" * 80)
        
        # 1. 先發投手的詳細統計與姓名的關聯
        print("\n⚾ 1. 先發投手統計與姓名關聯檢查:")
        print("-" * 60)
        
        # 檢查先發投手姓名
        sample_games = db.session.query(Game, GameDetail).join(
            GameDetail, Game.game_id == GameDetail.game_id
        ).filter(
            GameDetail.home_starting_pitcher.isnot(None),
            GameDetail.away_starting_pitcher.isnot(None)
        ).limit(10).all()
        
        pitcher_match_count = 0
        total_pitchers_checked = 0
        
        print("先發投手姓名與統計關聯測試:")
        for game, detail in sample_games:
            home_pitcher = detail.home_starting_pitcher
            away_pitcher = detail.away_starting_pitcher
            
            # 檢查主隊投手
            if home_pitcher:
                total_pitchers_checked += 1
                pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.player_name.like(f'%{home_pitcher}%'),
                    PlayerStats.innings_pitched > 0,
                    PlayerStats.season == game.date.year
                ).first()
                
                if pitcher_stats:
                    pitcher_match_count += 1
                    print(f"  ✅ {home_pitcher} -> ERA: {pitcher_stats.era:.2f}")
                else:
                    print(f"  ❌ {home_pitcher} -> 無統計數據")
            
            # 檢查客隊投手
            if away_pitcher:
                total_pitchers_checked += 1
                pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.player_name.like(f'%{away_pitcher}%'),
                    PlayerStats.innings_pitched > 0,
                    PlayerStats.season == game.date.year
                ).first()
                
                if pitcher_stats:
                    pitcher_match_count += 1
                    print(f"  ✅ {away_pitcher} -> ERA: {pitcher_stats.era:.2f}")
                else:
                    print(f"  ❌ {away_pitcher} -> 無統計數據")
        
        match_rate = pitcher_match_count / total_pitchers_checked * 100 if total_pitchers_checked > 0 else 0
        print(f"\n先發投手統計關聯成功率: {pitcher_match_count}/{total_pitchers_checked} ({match_rate:.1f}%)")
        
        # 2. 當日確定的先發投手陣容
        print(f"\n🗓️ 2. 當日先發投手陣容檢查:")
        print("-" * 60)
        
        today = date.today()
        recent_date = today - timedelta(days=7)
        
        recent_games_with_pitchers = db.session.query(Game, GameDetail).join(
            GameDetail, Game.game_id == GameDetail.game_id
        ).filter(
            Game.date >= recent_date,
            Game.date <= today,
            GameDetail.home_starting_pitcher.isnot(None)
        ).count()
        
        recent_total_games = Game.query.filter(
            Game.date >= recent_date,
            Game.date <= today
        ).count()
        
        print(f"最近7天比賽總數: {recent_total_games}")
        print(f"有先發投手資料的比賽: {recent_games_with_pitchers}")
        print(f"當日先發投手資料覆蓋率: {recent_games_with_pitchers/recent_total_games*100:.1f}%" if recent_total_games > 0 else "0%")
        
        # 3. 打線順序和位置
        print(f"\n🏏 3. 打線順序和位置檢查:")
        print("-" * 60)
        
        # 檢查球員位置數據
        players_with_position = PlayerStats.query.filter(
            PlayerStats.position.isnot(None),
            PlayerStats.position != ''
        ).count()
        
        total_player_stats = PlayerStats.query.count()
        
        print(f"總球員統計記錄: {total_player_stats}")
        print(f"有位置資料的球員: {players_with_position}")
        print(f"位置資料覆蓋率: {players_with_position/total_player_stats*100:.1f}%" if total_player_stats > 0 else "0%")
        
        # 顯示位置分布
        position_stats = db.session.query(
            PlayerStats.position, 
            db.func.count(PlayerStats.position)
        ).filter(
            PlayerStats.position.isnot(None),
            PlayerStats.position != ''
        ).group_by(PlayerStats.position).all()
        
        print(f"\n位置分布:")
        for position, count in position_stats:
            print(f"  {position}: {count}名球員")
        
        # 檢查是否有打線順序數據（通常在比賽詳細數據中）
        print(f"\n打線順序數據: ❌ 目前數據庫中沒有具體的打線順序資料")
        
        # 4. 投手對特定打者的歷史表現
        print(f"\n⚔️ 4. 投手對打者歷史表現檢查:")
        print("-" * 60)
        
        # 檢查是否有球員比賽統計數據
        try:
            from models.database import PlayerGameStats
            player_game_stats_count = PlayerGameStats.query.count()
            print(f"球員比賽統計記錄: {player_game_stats_count:,}")
            
            if player_game_stats_count > 0:
                # 檢查投手對打者的數據
                sample_pitcher_vs_batter = db.session.query(PlayerGameStats).filter(
                    PlayerGameStats.innings_pitched > 0
                ).limit(5).all()
                
                print(f"投手比賽統計樣本:")
                for stat in sample_pitcher_vs_batter:
                    print(f"  投手比賽記錄: 比賽ID {stat.game_id}, 投球局數: {stat.innings_pitched}")
                
                print(f"✅ 有基礎的投手比賽數據，可以分析投手對特定球隊的表現")
            else:
                print(f"❌ 沒有詳細的球員比賽統計數據")
                
        except ImportError:
            print(f"❌ PlayerGameStats 模型不存在")
        
        # 5. 即時傷兵狀況
        print(f"\n🏥 5. 傷兵狀況數據檢查:")
        print("-" * 60)
        
        try:
            injury_reports = PlayerInjuryReport.query.count()
            active_injuries = PlayerInjuryReport.query.filter_by(is_active=True).count()
            
            print(f"傷兵報告總數: {injury_reports}")
            print(f"目前活躍傷兵: {active_injuries}")
            
            if active_injuries > 0:
                sample_injuries = PlayerInjuryReport.query.filter_by(is_active=True).limit(3).all()
                print(f"傷兵報告樣本:")
                for injury in sample_injuries:
                    print(f"  球員ID {injury.player_id}: {injury.injury_type} - {injury.severity}")
                print(f"✅ 有傷兵狀況數據")
            else:
                print(f"❌ 沒有傷兵狀況數據")
                
        except Exception as e:
            print(f"❌ 傷兵數據檢查失敗: {e}")
        
        # 6. 其他進階數據檢查
        print(f"\n📊 6. 其他進階數據檢查:")
        print("-" * 60)
        
        try:
            # 球隊進階統計
            team_advanced_stats = TeamAdvancedStats.query.count()
            print(f"球隊進階統計: {team_advanced_stats}")
            
            # 球員表現趨勢
            player_trends = PlayerPerformanceTrends.query.count()
            print(f"球員表現趨勢: {player_trends}")
            
            # 球隊化學反應
            team_chemistry = TeamChemistry.query.count()
            print(f"球隊化學反應數據: {team_chemistry}")
            
            # 天氣條件
            weather_conditions = WeatherConditions.query.count()
            print(f"天氣條件數據: {weather_conditions}")
            
        except Exception as e:
            print(f"進階數據檢查失敗: {e}")
        
        # 7. 總結和建議
        print(f"\n📋 7. 數據可用性總結:")
        print("-" * 60)
        
        print("✅ 已有的數據:")
        print("   - 先發投手姓名 (99.7%覆蓋率)")
        print("   - 球員個人統計 (ERA, 打擊率等)")
        print("   - 球員位置信息")
        print("   - 比賽歷史記錄")
        
        print("\n❌ 缺少或不完整的數據:")
        print("   - 先發投手統計關聯 (需要改進姓名匹配)")
        print("   - 具體打線順序")
        print("   - 投手對特定打者的詳細對戰記錄")
        print("   - 即時傷兵狀況")
        print("   - 當日確定的先發陣容")
        
        print("\n🔧 可以改進的方向:")
        print("   1. 改進投手姓名匹配算法")
        print("   2. 獲取MLB官方的每日先發陣容")
        print("   3. 建立投手對打者的歷史對戰數據庫")
        print("   4. 整合傷兵報告數據源")
        print("   5. 獲取打線順序信息")
        
        print("\n" + "=" * 80)

def main():
    """主函數"""
    check_detailed_data_availability()

if __name__ == "__main__":
    main()
