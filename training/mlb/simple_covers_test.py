#!/usr/bin/env python3
"""
簡單測試covers.com響應
"""

import requests
from bs4 import BeautifulSoup

def test_covers_response():
    """測試covers.com響應"""
    print("=== 簡單測試 Covers.com 響應 ===")
    
    url = "https://www.covers.com/sports/mlb/matchups?selectedDate=2024-07-07"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'identity',
        'Connection': 'keep-alive',
    }
    
    try:
        print(f"請求URL: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"狀態碼: {response.status_code}")
        print(f"響應頭: {dict(response.headers)}")
        print(f"編碼: {response.encoding}")
        print(f"內容長度: {len(response.content)}")
        print(f"文本長度: {len(response.text)}")
        
        # 嘗試不同的解碼方式
        print("\n=== 嘗試不同解碼方式 ===")
        
        # 方式1：使用response.text
        text1 = response.text
        print(f"response.text 前100字符: {repr(text1[:100])}")
        
        # 方式2：手動解碼為UTF-8
        try:
            text2 = response.content.decode('utf-8')
            print(f"UTF-8解碼前100字符: {repr(text2[:100])}")
        except Exception as e:
            print(f"UTF-8解碼失敗: {e}")
        
        # 方式3：手動解碼為latin-1
        try:
            text3 = response.content.decode('latin-1')
            print(f"Latin-1解碼前100字符: {repr(text3[:100])}")
        except Exception as e:
            print(f"Latin-1解碼失敗: {e}")
            
        # 檢查是否包含HTML標籤
        if '<html' in response.text.lower():
            print("✅ 響應包含HTML內容")
            
            # 使用BeautifulSoup解析
            soup = BeautifulSoup(response.text, 'html.parser')
            title = soup.find('title')
            if title:
                print(f"頁面標題: {title.get_text()}")
                
            # 查找包含MLB的文本
            mlb_text = soup.find_all(text=lambda text: text and 'MLB' in text)
            print(f"找到 {len(mlb_text)} 個包含'MLB'的文本片段")
            
            for i, text in enumerate(mlb_text[:5]):
                print(f"  {i+1}: {text.strip()}")
                
        else:
            print("❌ 響應不包含HTML內容")
            
    except Exception as e:
        print(f"請求失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_covers_response()
