import atexit
import json
from datetime import datetime, date, timedelta
import logging

# 配置日誌
logger = logging.getLogger(__name__)

def setup_scheduler(app):
    """設置背景定時任務"""
    try:
        from models.automated_predictor import automated_predictor

        # 在應用上下文中啟動自動化預測器
        with app.app_context():
            automated_predictor.start_scheduler()

        # 註冊關閉時停止調度器
        atexit.register(lambda: automated_predictor.stop_scheduler())

    except Exception as e:
        logger.error(f"設置調度器失敗: {e}", exc_info=True)

def update_daily_data():
    """每日數據更新任務"""
    try:
        from models.automated_predictor import automated_predictor
        automated_predictor._daily_data_update()
    except Exception as e:
        logger.error(f"每日數據更新失敗: {e}", exc_info=True)

def update_predictions():
    """更新比賽預測"""
    try:
        from models.automated_predictor import automated_predictor
        automated_predictor._daily_prediction_generation()
    except Exception as e:
        logger.error(f"預測更新失敗: {e}", exc_info=True)

def setup_template_filters(app):
    """設置自定義模板過濾器"""
    @app.template_filter('from_json')
    def from_json_filter(value):
        """將JSON字符串轉換為Python對象"""
        if value:
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}

    # 添加全局模板變量
    @app.context_processor
    def inject_datetime():
        """注入datetime對象到模板"""
        return {
            'datetime': datetime,
            'date': date,
            'timedelta': timedelta
        }
