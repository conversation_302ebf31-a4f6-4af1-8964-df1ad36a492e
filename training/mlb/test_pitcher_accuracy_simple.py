#!/usr/bin/env python3
"""
簡化版投手因素增強預測準確度測試
"""

import sys
import os
from datetime import date
import numpy as np

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.improved_predictor import ImprovedMLBPredictor
from models.enhanced_feature_engineer import EnhancedFeatureEngineer
from models.database import Game

def test_pitcher_enhanced_predictions():
    """測試投手因素增強後的預測準確度"""
    app = create_app()
    
    with app.app_context():
        print("🎯 投手因素增強預測準確度測試")
        print("=" * 60)
        
        # 初始化預測器和特徵工程器
        predictor = ImprovedMLBPredictor()
        feature_engineer = EnhancedFeatureEngineer()
        
        # 獲取測試數據 (6月27日)
        test_date = date(2025, 6, 27)
        test_games = Game.query.filter(
            Game.date == test_date,
            Game.home_score.isnot(None),
            Game.away_score.isnot(None),
            Game.game_status == 'completed'
        ).all()
        
        print(f"📅 測試日期: {test_date}")
        print(f"🎯 測試比賽數: {len(test_games)}")
        
        if not test_games:
            print("❌ 找不到測試比賽")
            return
        
        # 統計變量
        correct_predictions = 0
        total_predictions = 0
        score_errors = []
        ace_duel_analysis = []
        pitcher_advantage_analysis = []
        
        print(f"\n🔍 逐場分析:")
        
        for i, game in enumerate(test_games, 1):
            try:
                print(f"\n{i}. {game.away_team} @ {game.home_team}")
                print(f"   實際比分: {game.away_score}-{game.home_score}")
                
                # 提取投手因素特徵
                features = feature_engineer.extract_comprehensive_features(
                    game.home_team, game.away_team, game.date
                )
                
                if not features:
                    print("   ❌ 特徵提取失敗")
                    continue
                
                # 分析投手因素
                ace_duel = features.get('ace_pitcher_duel', 0)
                pitcher_advantage = features.get('pitcher_matchup_advantage', 0)
                home_quality = features.get('home_pitcher_quality', 50)
                away_quality = features.get('away_pitcher_quality', 50)
                home_has_ace = features.get('home_has_ace', 0)
                away_has_ace = features.get('away_has_ace', 0)
                
                print(f"   投手分析: 主隊{home_quality:.1f} vs 客隊{away_quality:.1f}")
                
                if ace_duel:
                    print("   🌟 王牌投手對決")
                elif home_has_ace:
                    print("   🏠 主隊有王牌")
                elif away_has_ace:
                    print("   ✈️ 客隊有王牌")
                
                if abs(pitcher_advantage) > 0.2:
                    team = "主隊" if pitcher_advantage > 0 else "客隊"
                    print(f"   ⚖️ {team}投手優勢 ({pitcher_advantage:.2f})")
                
                # 進行預測
                try:
                    prediction = predictor.predict_game(game.home_team, game.away_team, game.date)
                    
                    if prediction and 'predicted_home_score' in prediction:
                        pred_home = round(prediction['predicted_home_score'])
                        pred_away = round(prediction['predicted_away_score'])
                        
                        print(f"   預測比分: {pred_away}-{pred_home}")
                        
                        # 計算勝負準確度
                        actual_winner = 'home' if game.home_score > game.away_score else 'away'
                        pred_winner = 'home' if pred_home > pred_away else 'away'
                        
                        is_correct = actual_winner == pred_winner
                        if is_correct:
                            correct_predictions += 1
                            print("   ✅ 勝負預測正確")
                        else:
                            print("   ❌ 勝負預測錯誤")
                        
                        total_predictions += 1
                        
                        # 計算得分誤差
                        home_error = abs(pred_home - game.home_score)
                        away_error = abs(pred_away - game.away_score)
                        total_error = home_error + away_error
                        score_errors.append(total_error)
                        
                        print(f"   得分誤差: {total_error:.1f}")
                        
                        # 記錄投手因素分析
                        total_runs = game.home_score + game.away_score
                        
                        if ace_duel:
                            ace_duel_analysis.append({
                                'total_runs': total_runs,
                                'prediction_error': total_error,
                                'correct_prediction': is_correct
                            })
                        
                        if abs(pitcher_advantage) > 0.3:
                            pitcher_advantage_analysis.append({
                                'advantage': pitcher_advantage,
                                'prediction_error': total_error,
                                'correct_prediction': is_correct,
                                'total_runs': total_runs
                            })
                    
                    else:
                        print("   ❌ 預測失敗")
                
                except Exception as e:
                    print(f"   ❌ 預測錯誤: {e}")
                
            except Exception as e:
                print(f"   ❌ 分析錯誤: {e}")
        
        # 計算總體結果
        if total_predictions > 0:
            accuracy = (correct_predictions / total_predictions) * 100
            avg_score_error = np.mean(score_errors) if score_errors else 0
            
            print(f"\n📈 總體結果:")
            print(f"  勝負預測準確度: {accuracy:.1f}% ({correct_predictions}/{total_predictions})")
            print(f"  平均得分誤差: {avg_score_error:.1f} 分")
            
            # 分析王牌投手對決效果
            if ace_duel_analysis:
                ace_accuracy = sum(1 for a in ace_duel_analysis if a['correct_prediction']) / len(ace_duel_analysis) * 100
                ace_avg_error = np.mean([a['prediction_error'] for a in ace_duel_analysis])
                ace_avg_runs = np.mean([a['total_runs'] for a in ace_duel_analysis])
                
                print(f"\n🌟 王牌投手對決分析 ({len(ace_duel_analysis)} 場):")
                print(f"  預測準確度: {ace_accuracy:.1f}%")
                print(f"  平均得分誤差: {ace_avg_error:.1f} 分")
                print(f"  平均總得分: {ace_avg_runs:.1f} 分")
            
            # 分析投手優勢效果
            if pitcher_advantage_analysis:
                adv_accuracy = sum(1 for a in pitcher_advantage_analysis if a['correct_prediction']) / len(pitcher_advantage_analysis) * 100
                adv_avg_error = np.mean([a['prediction_error'] for a in pitcher_advantage_analysis])
                
                print(f"\n⚖️ 明顯投手優勢分析 ({len(pitcher_advantage_analysis)} 場):")
                print(f"  預測準確度: {adv_accuracy:.1f}%")
                print(f"  平均得分誤差: {adv_avg_error:.1f} 分")
            
            # 與之前的準確度比較
            print(f"\n📊 準確度提升分析:")
            if accuracy > 60:
                print(f"  ✅ 當前準確度 {accuracy:.1f}% 超過之前的60%！")
                print(f"  🎯 投手因素成功提升了預測準確度")
            elif accuracy > 50:
                print(f"  📈 當前準確度 {accuracy:.1f}% 超過基準50%")
                print(f"  🔄 投手因素有一定效果，可進一步優化")
            else:
                print(f"  📉 當前準確度 {accuracy:.1f}% 需要進一步改進")
                print(f"  🔧 建議調整投手因素權重或特徵選擇")

def analyze_pitcher_factor_impact():
    """分析投手因素對預測的具體影響"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("🔬 投手因素影響深度分析")
        print("=" * 60)
        
        feature_engineer = EnhancedFeatureEngineer()
        
        # 分析最近比賽的投手因素分布
        recent_games = Game.query.filter(
            Game.date >= date(2025, 6, 25),
            Game.date <= date(2025, 6, 28),
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).all()
        
        ace_games = []
        normal_games = []
        high_advantage_games = []
        
        for game in recent_games:
            try:
                features = feature_engineer.extract_comprehensive_features(
                    game.home_team, game.away_team, game.date
                )
                
                if features:
                    total_runs = game.home_score + game.away_score
                    ace_duel = features.get('ace_pitcher_duel', 0)
                    pitcher_advantage = abs(features.get('pitcher_matchup_advantage', 0))
                    
                    if ace_duel:
                        ace_games.append(total_runs)
                    else:
                        normal_games.append(total_runs)
                    
                    if pitcher_advantage > 0.4:
                        high_advantage_games.append(total_runs)
                        
            except Exception as e:
                continue
        
        print(f"📊 投手因素統計:")
        print(f"  王牌對決比賽: {len(ace_games)} 場")
        print(f"  普通比賽: {len(normal_games)} 場")
        print(f"  明顯投手優勢: {len(high_advantage_games)} 場")
        
        if ace_games and normal_games:
            ace_avg = np.mean(ace_games)
            normal_avg = np.mean(normal_games)
            difference = normal_avg - ace_avg
            
            print(f"\n🎯 得分壓制效果:")
            print(f"  王牌對決平均得分: {ace_avg:.1f}")
            print(f"  普通比賽平均得分: {normal_avg:.1f}")
            print(f"  得分差距: {difference:.1f} 分")
            
            if difference > 2:
                print("  ✅ 王牌投手顯著壓制得分！")
            elif difference > 1:
                print("  📈 王牌投手有一定壓制效果")
            else:
                print("  📊 王牌投手效果不明顯")

if __name__ == "__main__":
    print("⚾ 投手因素增強預測系統準確度測試")
    print("=" * 60)
    
    # 測試預測準確度
    test_pitcher_enhanced_predictions()
    
    # 分析投手因素影響
    analyze_pitcher_factor_impact()
    
    print("\n✅ 測試完成")
