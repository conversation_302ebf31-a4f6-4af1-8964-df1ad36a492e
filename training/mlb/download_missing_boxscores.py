#!/usr/bin/env python3
"""
補充下載缺失的Box Score數據
專門用於下載比賽的詳細球員統計數據
"""

import sys
sys.path.append('.')

import os
import json
import requests
from datetime import datetime, timedelta
from app import app

def download_boxscore_for_date(target_date):
    """下載指定日期所有比賽的Box Score數據"""
    
    print(f"📊 下載 {target_date} 的 Box Score 數據")
    print("=" * 60)
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    with app.app_context():
        try:
            from models.database import db, Game
            from boxscore_data_collector import BoxscoreDataCollector
            
            # 初始化收集器
            collector = BoxscoreDataCollector()
            
            # 查詢該日期的所有比賽
            games = db.session.query(Game).filter(
                Game.date == target_date,
                Game.game_status == 'completed'  # 只下載已完成的比賽
            ).all()
            
            print(f"🎯 找到 {len(games)} 場已完成比賽")
            
            if not games:
                print(f"❌ 未找到 {target_date} 的已完成比賽")
                return False
            
            success_count = 0
            failed_count = 0
            
            for i, game in enumerate(games, 1):
                try:
                    print(f"\n🔄 處理第 {i}/{len(games)} 場比賽:")
                    print(f"   {game.away_team} @ {game.home_team} ({game.game_id})")
                    print(f"   比分: {game.away_score} - {game.home_score}")
                    
                    # 嘗試從MLB API獲取Box Score
                    boxscore_data = collector.fetch_boxscore_data(game.game_id, game.date.strftime('%Y-%m-%d'))
                    
                    if boxscore_data and 'success' in boxscore_data and boxscore_data['success']:
                        print(f"   ✅ Box Score 數據獲取成功")
                        
                        # 保存到數據庫（如果collector有保存方法）
                        if hasattr(collector, 'save_boxscore_to_database'):
                            result = collector.save_boxscore_to_database(game.game_id, boxscore_data)
                            if result:
                                print(f"   💾 數據保存成功")
                                success_count += 1
                            else:
                                print(f"   ❌ 數據保存失敗")
                                failed_count += 1
                        else:
                            # 手動保存邏輯
                            if save_boxscore_manually(game.game_id, boxscore_data):
                                success_count += 1
                                print(f"   💾 手動保存成功")
                            else:
                                failed_count += 1
                                print(f"   ❌ 手動保存失敗")
                    else:
                        print(f"   ❌ Box Score 數據獲取失敗")
                        failed_count += 1
                
                except Exception as e:
                    print(f"   ❌ 處理失敗: {e}")
                    failed_count += 1
                    continue
            
            print(f"\n📈 Box Score 下載結果:")
            print(f"   ✅ 成功: {success_count} 場")
            print(f"   ❌ 失敗: {failed_count} 場")
            print(f"   📊 成功率: {success_count/(success_count + failed_count)*100:.1f}%")
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 下載過程失敗: {e}")
            import traceback
            traceback.print_exc()
            return False

def save_boxscore_manually(game_id, boxscore_data):
    """手動保存Box Score數據到數據庫"""
    
    try:
        from models.database import db
        
        # 這裡需要根據實際的數據結構來保存
        # 由於我們沒有完整的Box Score表結構，先記錄JSON格式
        
        # 檢查是否已經有Box Score表
        try:
            # 嘗試創建一個簡單的Box Score記錄表
            db.session.execute("""
                CREATE TABLE IF NOT EXISTS boxscore_data (
                    id INTEGER PRIMARY KEY,
                    game_id TEXT NOT NULL,
                    boxscore_json TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(game_id)
                )
            """)
            
            # 插入或更新數據
            db.session.execute("""
                INSERT OR REPLACE INTO boxscore_data (game_id, boxscore_json)
                VALUES (?, ?)
            """, (game_id, json.dumps(boxscore_data)))
            
            db.session.commit()
            return True
            
        except Exception as e:
            print(f"   保存Box Score JSON失敗: {e}")
            db.session.rollback()
            return False
            
    except Exception as e:
        print(f"   手動保存失敗: {e}")
        return False

def batch_download_boxscores(start_date, end_date):
    """批量下載多天的Box Score數據"""
    
    print(f"🚀 批量下載 {start_date} 到 {end_date} 的 Box Score 數據")
    print("=" * 70)
    
    from datetime import datetime, timedelta
    
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    total_success = 0
    total_processed = 0
    
    current_date = start
    while current_date <= end:
        date_str = current_date.strftime('%Y-%m-%d')
        print(f"\n📅 處理日期: {date_str}")
        
        success = download_boxscore_for_date(date_str)
        total_processed += 1
        if success:
            total_success += 1
        
        current_date += timedelta(days=1)
    
    print(f"\n🎯 批量下載總結:")
    print(f"   處理天數: {total_processed}")
    print(f"   成功天數: {total_success}")
    print(f"   成功率: {total_success/total_processed*100:.1f}%" if total_processed > 0 else "0%")

def check_existing_boxscore_data(target_date):
    """檢查現有Box Score數據"""
    
    print(f"🔍 檢查 {target_date} 現有 Box Score 數據")
    print("-" * 50)
    
    with app.app_context():
        try:
            from models.database import db
            
            # 檢查是否有Box Score數據表
            try:
                result = db.session.execute("""
                    SELECT COUNT(*) as count 
                    FROM boxscore_data 
                    WHERE date(created_at) = ?
                """, (target_date,)).fetchone()
                
                count = result[0] if result else 0
                print(f"   📊 現有Box Score記錄: {count} 條")
                
                if count > 0:
                    # 顯示一些樣本數據
                    samples = db.session.execute("""
                        SELECT game_id, substr(boxscore_json, 1, 100) as preview
                        FROM boxscore_data 
                        WHERE date(created_at) = ?
                        LIMIT 3
                    """, (target_date,)).fetchall()
                    
                    print(f"   📋 樣本數據:")
                    for sample in samples:
                        print(f"      {sample[0]}: {sample[1]}...")
                
                return count
                
            except Exception as e:
                print(f"   ❌ Box Score表不存在或查詢失敗: {e}")
                return 0
                
        except Exception as e:
            print(f"   ❌ 檢查失敗: {e}")
            return 0

def main():
    """主函數"""
    
    print("📊 MLB Box Score 補充下載工具")
    print("解決預測準確性問題，補充缺失的詳細統計數據")
    print()
    
    # 檢查8/29的現有數據
    target_date = '2025-08-29'
    existing_count = check_existing_boxscore_data(target_date)
    
    # 下載8/29的Box Score
    print(f"\n🎯 開始下載 {target_date} 的 Box Score 數據...")
    success = download_boxscore_for_date(target_date)
    
    if success:
        print(f"\n🎉 Box Score 數據下載完成！")
        print(f"   ✅ 現在預測系統應該有更準確的數據了")
        print(f"   📊 建議重新運行預測以獲得更好的準確性")
    else:
        print(f"\n⚠️ Box Score 數據下載未完全成功")
        print(f"   🔧 建議檢查網絡連接和MLB API狀態")
    
    print(f"\n📋 下一步:")
    print(f"   1. 檢查數據庫中的Box Score記錄")
    print(f"   2. 整合到Web界面供日常使用")
    print(f"   3. 重新訓練預測模型以提升準確性")

if __name__ == "__main__":
    main()