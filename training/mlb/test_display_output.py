#!/usr/bin/env python3
"""
直接測試模板輸出和顯示效果
"""

import sys
sys.path.append('.')

from app import app
from models.database import db, Game, Prediction, BettingOdds
from flask import render_template_string
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_template_snippet():
    """測試模板片段渲染"""
    with app.app_context():
        # 獲取測試數據
        game = Game.query.filter(Game.game_id.like('TEST_%')).first()
        prediction = Prediction.query.filter(Prediction.game_id.like('TEST_%')).first()
        
        if not game or not prediction:
            logger.warning("沒有找到測試數據，創建新的...")
            # 創建測試數據...
            return
        
        # 添加賠率數據
        betting_odds = BettingOdds.query.filter_by(game_id=game.game_id).first()
        if betting_odds:
            game.spread_line = betting_odds.home_spread_point
        
        # 測試模板片段
        template_snippet = '''
<div class="border-top pt-3 mb-3">
    <div class="row">
        <!-- 大小分顯示 -->
        <div class="col-6">
            <div class="text-center">
                <small class="text-muted d-block">大小分</small>
                {% if prediction.over_under_line %}
                    <div class="d-flex justify-content-center align-items-center">
                        <span class="badge bg-info me-1">{{ "%.1f"|format(prediction.over_under_line) }}</span>
                        {% set predicted_total = prediction.predicted_home_score + prediction.predicted_away_score %}
                        {% if predicted_total > prediction.over_under_line %}
                            <span class="badge bg-warning text-dark">大</span>
                        {% else %}
                            <span class="badge bg-secondary">小</span>
                        {% endif %}
                    </div>
                {% else %}
                    <span class="text-muted small">無數據</span>
                {% endif %}
            </div>
        </div>
        
        <!-- 讓分盤顯示 -->
        <div class="col-6">
            <div class="text-center">
                <small class="text-muted d-block">讓分盤</small>
                {% if game.spread_line is defined and game.spread_line %}
                    <div class="d-flex justify-content-center align-items-center">
                        {% if game.spread_line > 0 %}
                            <span class="badge bg-danger me-1">受讓 {{ "%.1f"|format(game.spread_line) }}</span>
                        {% else %}
                            <span class="badge bg-success me-1">讓 {{ "%.1f"|format(-game.spread_line) }}</span>
                        {% endif %}
                        
                        {% set spread_diff = (prediction.predicted_home_score - prediction.predicted_away_score) + (game.spread_line|default(0)) %}
                        {% if spread_diff > 0 %}
                            <span class="badge bg-primary">主</span>
                        {% else %}
                            <span class="badge bg-info">客</span>
                        {% endif %}
                    </div>
                {% else %}
                    <span class="text-muted small">無數據</span>
                {% endif %}
            </div>
        </div>
    </div>
</div>
'''
        
        # 渲染模板
        rendered = render_template_string(template_snippet, 
                                        game=game, 
                                        prediction=prediction)
        
        logger.info("\n渲染結果:")
        logger.info("=" * 50)
        logger.info(rendered)
        logger.info("=" * 50)
        
        # 檢查關鍵內容
        checks = [
            ('大小分線數值', str(prediction.over_under_line) in rendered),
            ('大小分推薦', ('大' in rendered or '小' in rendered)),
            ('讓分資訊', ('讓' in rendered or '受讓' in rendered)),
            ('推薦標籤', ('badge' in rendered)),
        ]
        
        logger.info("\n功能檢查:")
        for check_name, result in checks:
            status = "✅" if result else "❌"
            logger.info(f"  {status} {check_name}")

def main():
    test_template_snippet()

if __name__ == "__main__":
    main()