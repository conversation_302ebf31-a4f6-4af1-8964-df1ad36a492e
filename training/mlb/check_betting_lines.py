#!/usr/bin/env python3
"""
檢查數據庫中的博彩盤口數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, BettingOdds, Prediction
import pandas as pd

def check_col_cin_betting_line():
    """檢查 COL @ CIN 比賽的盤口"""
    app = create_app()
    
    with app.app_context():
        print("🔍 查找 COL @ CIN 比賽的盤口數據...")
        
        # 查找最近的 COL @ CIN 比賽
        query = """
        SELECT 
            g.game_id,
            g.date,
            g.home_team,
            g.away_team,
            g.home_score,
            g.away_score,
            (g.home_score + g.away_score) as actual_total,
            p.predicted_home_score,
            p.predicted_away_score,
            (p.predicted_home_score + p.predicted_away_score) as predicted_total,
            p.confidence,
            bo.total_point as betting_line,
            bo.bookmaker,
            bo.over_price,
            bo.under_price
        FROM games g
        LEFT JOIN predictions p ON g.game_id = p.game_id
        LEFT JOIN betting_odds bo ON g.game_id = bo.game_id
        WHERE g.away_team = 'COL' 
        AND g.home_team = 'CIN'
        AND g.date >= DATE('now', '-30 days')
        ORDER BY g.date DESC
        """
        
        results = db.session.execute(db.text(query)).fetchall()
        
        if not results:
            print("❌ 未找到 COL @ CIN 的比賽記錄")
            return
        
        print(f"📊 找到 {len(results)} 場 COL @ CIN 比賽:")
        
        for result in results:
            print(f"\n📅 {result.date} - COL @ CIN")
            print(f"  實際比分: {result.away_score} - {result.home_score} (總分 {result.actual_total})")
            
            if result.predicted_total:
                print(f"  預測總分: {result.predicted_total:.1f}")
                print(f"  預測信心: {result.confidence:.1%}")
            
            if result.betting_line:
                print(f"  博彩盤口: {result.betting_line} ({result.bookmaker})")
                
                # 分析大小分
                if result.predicted_total and result.actual_total:
                    predicted_over = result.predicted_total > result.betting_line
                    actual_over = result.actual_total > result.betting_line
                    
                    print(f"  預測方向: {'大分' if predicted_over else '小分'}")
                    print(f"  實際方向: {'大分' if actual_over else '小分'}")
                    print(f"  預測結果: {'✅ 正確' if predicted_over == actual_over else '❌ 錯誤'}")
                    
                    # 計算偏離程度
                    pred_deviation = abs(result.predicted_total - result.betting_line)
                    actual_deviation = abs(result.actual_total - result.betting_line)
                    
                    print(f"  預測偏離: {pred_deviation:.1f} 分")
                    print(f"  實際偏離: {actual_deviation:.1f} 分")
            else:
                print(f"  ❌ 無盤口數據")

def check_recent_games_with_lines():
    """檢查最近有盤口數據的比賽"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔍 檢查最近有盤口數據的比賽...")
        
        query = """
        SELECT 
            g.game_id,
            g.date,
            g.home_team,
            g.away_team,
            g.home_score,
            g.away_score,
            (g.home_score + g.away_score) as actual_total,
            p.predicted_home_score,
            p.predicted_away_score,
            (p.predicted_home_score + p.predicted_away_score) as predicted_total,
            p.confidence,
            bo.total_point as betting_line,
            bo.bookmaker
        FROM games g
        JOIN predictions p ON g.game_id = p.game_id
        JOIN betting_odds bo ON g.game_id = bo.game_id
        WHERE g.home_score IS NOT NULL 
        AND g.away_score IS NOT NULL
        AND bo.total_point IS NOT NULL
        AND g.date >= DATE('now', '-7 days')
        ORDER BY g.date DESC, g.game_id
        LIMIT 10
        """
        
        results = db.session.execute(db.text(query)).fetchall()
        
        if not results:
            print("❌ 最近7天沒有找到有盤口數據的比賽")
            return
        
        print(f"📊 最近有盤口數據的比賽 (前10場):")
        print(f"{'日期':<12} {'比賽':<15} {'實際':<8} {'預測':<8} {'盤口':<6} {'預測':<6} {'實際':<6} {'結果':<6}")
        print("-" * 75)
        
        correct_predictions = 0
        total_predictions = 0
        
        for result in results:
            date_str = str(result.date)[:10]
            teams = f"{result.away_team}@{result.home_team}"
            actual_total = f"{result.actual_total:.0f}"
            predicted_total = f"{result.predicted_total:.1f}"
            betting_line = f"{result.betting_line:.1f}"
            
            # 計算大小分方向
            predicted_over = result.predicted_total > result.betting_line
            actual_over = result.actual_total > result.betting_line
            is_correct = predicted_over == actual_over
            
            pred_direction = "大分" if predicted_over else "小分"
            actual_direction = "大分" if actual_over else "小分"
            result_icon = "✅" if is_correct else "❌"
            
            print(f"{date_str:<12} {teams:<15} {actual_total:<8} {predicted_total:<8} {betting_line:<6} {pred_direction:<6} {actual_direction:<6} {result_icon:<6}")
            
            if is_correct:
                correct_predictions += 1
            total_predictions += 1
        
        if total_predictions > 0:
            accuracy = correct_predictions / total_predictions * 100
            print(f"\n📈 大小分預測準確率: {accuracy:.1f}% ({correct_predictions}/{total_predictions})")

def analyze_betting_line_accuracy():
    """分析博彩盤口的準確性"""
    app = create_app()
    
    with app.app_context():
        print(f"\n📊 分析博彩盤口準確性...")
        
        query = """
        SELECT 
            g.date,
            g.home_team,
            g.away_team,
            (g.home_score + g.away_score) as actual_total,
            bo.total_point as betting_line,
            bo.bookmaker,
            ABS((g.home_score + g.away_score) - bo.total_point) as line_error
        FROM games g
        JOIN betting_odds bo ON g.game_id = bo.game_id
        WHERE g.home_score IS NOT NULL 
        AND g.away_score IS NOT NULL
        AND bo.total_point IS NOT NULL
        AND g.date >= DATE('now', '-30 days')
        ORDER BY line_error DESC
        """
        
        results = db.session.execute(db.text(query)).fetchall()
        
        if not results:
            print("❌ 沒有找到有盤口數據的比賽")
            return
        
        # 轉換為 DataFrame 進行分析
        columns = ['date', 'home_team', 'away_team', 'actual_total', 'betting_line', 'bookmaker', 'line_error']
        df = pd.DataFrame(results, columns=columns)
        
        print(f"  分析 {len(df)} 場有盤口數據的比賽")
        print(f"  平均盤口誤差: {df['line_error'].mean():.1f} 分")
        print(f"  盤口誤差標準差: {df['line_error'].std():.1f}")
        print(f"  最大盤口誤差: {df['line_error'].max():.1f} 分")
        
        # 大分命中率
        df['actual_over'] = df['actual_total'] > df['betting_line']
        over_rate = df['actual_over'].mean() * 100
        print(f"  大分命中率: {over_rate:.1f}% (理論應接近50%)")
        
        # 顯示誤差最大的比賽
        print(f"\n  盤口誤差最大的比賽:")
        worst_predictions = df.nlargest(5, 'line_error')
        for _, row in worst_predictions.iterrows():
            print(f"    {row['date']} {row['away_team']}@{row['home_team']}: 實際{row['actual_total']:.0f} vs 盤口{row['betting_line']:.1f} (誤差{row['line_error']:.1f})")

def search_specific_game():
    """搜索特定比賽"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔍 搜索最近的 COL @ CIN 比賽...")
        
        # 搜索最近30天的所有 COL 客場比賽
        query = """
        SELECT 
            g.game_id,
            g.date,
            g.home_team,
            g.away_team,
            g.home_score,
            g.away_score,
            (g.home_score + g.away_score) as actual_total
        FROM games g
        WHERE g.away_team = 'COL'
        AND g.date >= DATE('now', '-30 days')
        ORDER BY g.date DESC
        """
        
        results = db.session.execute(db.text(query)).fetchall()
        
        print(f"📊 最近30天 COL 的客場比賽:")
        for result in results:
            if result.home_score is not None:
                print(f"  {result.date} COL@{result.home_team}: {result.away_score}-{result.home_score} (總分{result.actual_total})")
            else:
                print(f"  {result.date} COL@{result.home_team}: 未完成")

if __name__ == "__main__":
    print("🚀 檢查博彩盤口數據...")
    
    # 1. 檢查 COL @ CIN 的盤口
    check_col_cin_betting_line()
    
    # 2. 檢查最近有盤口數據的比賽
    check_recent_games_with_lines()
    
    # 3. 分析盤口準確性
    analyze_betting_line_accuracy()
    
    # 4. 搜索特定比賽
    search_specific_game()
    
    print("\n✅ 檢查完成！")
