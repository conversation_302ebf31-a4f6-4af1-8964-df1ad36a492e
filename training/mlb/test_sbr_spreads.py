#!/usr/bin/env python3
"""
測試修復後的sportsbookreview讓分盤抓取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from models.modern_sbr_scraper import ModernSBRScraper

def test_sbr_spreads():
    """測試sportsbookreview讓分盤抓取"""
    try:
        # 測試日期
        test_date = date(2025, 7, 7)
        
        print(f"🧪 測試sportsbookreview讓分盤抓取 - {test_date}")
        
        # 創建抓取器
        scraper = ModernSBRScraper()
        
        # 抓取數據
        result = scraper.fetch_historical_odds(test_date, bookmaker='bet365')
        
        print(f"🌐 抓取結果:")
        print(f"  成功: {result.get('success')}")
        print(f"  日期: {result.get('date')}")
        print(f"  博彩商: {result.get('bookmaker')}")
        print(f"  總比賽數: {result.get('total_games', 0)}")
        
        if result.get('success'):
            games = result.get('games', [])
            print(f"\n📋 比賽詳情:")
            
            for i, game in enumerate(games[:5]):  # 只顯示前5場
                print(f"\n  比賽 {i+1}:")
                print(f"    客隊: {game.get('away_team')}")
                print(f"    主隊: {game.get('home_team')}")
                print(f"    博彩商: {game.get('bookmaker')}")
                
                # 檢查總分盤數據
                totals = game.get('totals', {})
                if totals:
                    print(f"    總分盤:")
                    print(f"      總分線: {totals.get('total_point')}")
                    print(f"      大分賠率: {totals.get('over_price')}")
                    print(f"      小分賠率: {totals.get('under_price')}")
                else:
                    print(f"    總分盤: ❌ 無數據")
                
                # 檢查讓分盤數據
                spreads = game.get('spreads', {})
                if spreads:
                    print(f"    讓分盤:")
                    print(f"      主隊讓分: {spreads.get('home_spread_point')}")
                    print(f"      主隊賠率: {spreads.get('home_spread_price')}")
                    print(f"      客隊讓分: {spreads.get('away_spread_point')}")
                    print(f"      客隊賠率: {spreads.get('away_spread_price')}")
                else:
                    print(f"    讓分盤: ❌ 無數據")
            
            # 統計數據
            total_games = len(games)
            games_with_totals = sum(1 for g in games if g.get('totals'))
            games_with_spreads = sum(1 for g in games if g.get('spreads'))
            
            print(f"\n📊 數據統計:")
            print(f"  總比賽數: {total_games}")
            print(f"  有總分盤數據: {games_with_totals}")
            print(f"  有讓分盤數據: {games_with_spreads}")
            print(f"  總分盤覆蓋率: {games_with_totals/total_games*100:.1f}%" if total_games > 0 else "  總分盤覆蓋率: 0%")
            print(f"  讓分盤覆蓋率: {games_with_spreads/total_games*100:.1f}%" if total_games > 0 else "  讓分盤覆蓋率: 0%")
            
        else:
            print(f"❌ 抓取失敗: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sbr_spreads()
