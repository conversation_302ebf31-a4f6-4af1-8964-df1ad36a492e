#!/usr/bin/env python3
"""
簡單診斷腳本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, TeamStats, Team

def simple_diagnosis():
    """簡單診斷"""
    app = create_app()
    with app.app_context():
        print("🔍 簡單診斷")
        print("=" * 30)
        
        # 1. 檢查總數
        team_count = Team.query.count()
        stats_count = TeamStats.query.count()
        print(f"球隊數量: {team_count}")
        print(f"統計數量: {stats_count}")
        
        # 2. 檢查具體球隊
        test_teams = ['LAD', 'NYY', 'OAK']
        for team_code in test_teams:
            team = Team.query.filter_by(team_code=team_code).first()
            if team:
                print(f"\n{team_code}:")
                print(f"  team_id: {team.team_id}")
                print(f"  team_name: {team.team_name}")
                
                stats = TeamStats.query.filter_by(team_id=team.team_id).first()
                if stats:
                    print(f"  ✅ 有統計數據")
                    print(f"  runs_scored: {stats.runs_scored}")
                    print(f"  era: {stats.era}")
                    print(f"  wins: {stats.wins}")
                else:
                    print(f"  ❌ 無統計數據")
            else:
                print(f"\n{team_code}: ❌ 找不到球隊")
        
        # 3. 檢查所有統計數據
        print(f"\n📊 所有統計數據:")
        all_stats = TeamStats.query.all()
        for stats in all_stats[:5]:  # 只顯示前5個
            team = Team.query.filter_by(team_id=stats.team_id).first()
            team_code = team.team_code if team else "Unknown"
            print(f"  {team_code}: 得分={stats.runs_scored:.2f}, ERA={stats.era:.2f}")

if __name__ == "__main__":
    simple_diagnosis()
