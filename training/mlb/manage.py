#!/usr/bin/env python3
"""
MLB預測系統管理腳本
"""

import sys
import argparse
from datetime import date, datetime, timedelta
from app import create_app
from models.data_fetcher import MLBDataFetcher
from models.detailed_data_fetcher import DetailedDataFetcher
from models.database import Game, BoxScore, db
import logging

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def update_date_data(target_date):
    """更新指定日期的數據"""
    app = create_app()
    with app.app_context():
        print(f'🔄 更新 {target_date} 的數據...')
        
        try:
            # 1. 更新比賽狀態和比分
            print('步驟1: 更新比賽狀態和比分...')
            game_fetcher = MLBDataFetcher()
            game_fetcher.update_games_for_date(target_date)
            
            # 2. 下載Box Score
            print('步驟2: 下載Box Score...')
            detailed_fetcher = DetailedDataFetcher()
            boxscore_result = detailed_fetcher.fetch_completed_games_boxscores(target_date)
            
            print(f'✅ {target_date} 數據更新完成！')
            return True
            
        except Exception as e:
            print(f'❌ 更新失敗: {e}')
            return False

def check_data_status(target_date=None):
    """檢查數據狀態"""
    app = create_app()
    with app.app_context():
        if target_date is None:
            target_date = date.today()
        
        print(f'📊 檢查 {target_date} 的數據狀態...')
        
        # 檢查比賽數據
        games = Game.query.filter(Game.date == target_date).all()
        completed_games = [g for g in games if g.game_status == 'completed']
        
        print(f'總比賽數: {len(games)}')
        print(f'已完成比賽: {len(completed_games)}')
        
        # 檢查Box Score
        boxscore_count = 0
        for game in games:
            boxscore = BoxScore.query.filter_by(game_id=game.game_id).first()
            if boxscore:
                boxscore_count += 1
        
        print(f'Box Score覆蓋: {boxscore_count}/{len(games)}')
        
        # 顯示比賽詳情
        if games:
            print('\n比賽詳情:')
            for game in games:
                boxscore = BoxScore.query.filter_by(game_id=game.game_id).first()
                status = '✅' if boxscore else '❌'
                score_info = f'({game.away_score or 0}-{game.home_score or 0})' if game.game_status == 'completed' else ''
                print(f'{status} {game.away_team} @ {game.home_team} {score_info} ({game.game_status})')

def daily_update():
    """每日數據更新"""
    today = date.today()
    yesterday = today - timedelta(days=1)
    
    print("🏀 MLB每日數據更新")
    print("=" * 50)
    
    # 更新昨天和今天的數據
    for target_date in [yesterday, today]:
        print(f"\n📅 更新 {target_date} 的數據...")
        update_date_data(target_date)

def main():
    parser = argparse.ArgumentParser(description='MLB預測系統管理工具')
    parser.add_argument('command', choices=['update', 'check', 'daily'], 
                       help='執行的命令')
    parser.add_argument('--date', type=str, 
                       help='指定日期 (YYYY-MM-DD格式)')
    
    args = parser.parse_args()
    
    setup_logging()
    
    # 解析日期
    target_date = None
    if args.date:
        try:
            target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
        except ValueError:
            print("❌ 日期格式錯誤，請使用 YYYY-MM-DD 格式")
            sys.exit(1)
    
    # 執行命令
    if args.command == 'update':
        if target_date is None:
            target_date = date.today()
        update_date_data(target_date)
    
    elif args.command == 'check':
        check_data_status(target_date)
    
    elif args.command == 'daily':
        daily_update()

if __name__ == "__main__":
    main()
