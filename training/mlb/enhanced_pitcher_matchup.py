#!/usr/bin/env python3
"""
增強的投手對戰分析系統
利用 Box Score 數據提供準確的先發投手信息
"""

import sys
sys.path.append('.')

import json
from datetime import datetime
from typing import Dict, Optional, Tuple
from flask import current_app
from models.database import db, Game, Team, Player, PlayerStats
from sqlalchemy import text, func
import logging

logger = logging.getLogger(__name__)

class EnhancedPitcherMatchup:
    """增強的投手對戰分析器"""
    
    def __init__(self):
        self.logger = logger
        
    def get_pitcher_matchup_data(self, game: Game) -> Dict:
        """獲取增強的投手對戰數據"""
        try:
            # 獲取球隊信息
            home_team = Team.query.filter_by(team_code=game.home_team).first()
            away_team = Team.query.filter_by(team_code=game.away_team).first()

            # 嘗試從 Box Score 獲取先發投手信息
            home_pitcher = self.get_starting_pitcher_from_boxscore(game.game_id, True)
            away_pitcher = self.get_starting_pitcher_from_boxscore(game.game_id, False)
            
            # 如果 Box Score 沒有數據，嘗試其他方法
            if not home_pitcher:
                home_pitcher = self.get_starting_pitcher_fallback(game.game_id, game.home_team, True)
            if not away_pitcher:
                away_pitcher = self.get_starting_pitcher_fallback(game.game_id, game.away_team, False)

            matchup_data = {
                'game_info': {
                    'game_id': game.game_id,
                    'date': game.date.strftime('%Y-%m-%d'),
                    'status': game.game_status,
                    'venue': game.venue or 'Unknown'
                },
                'teams': {
                    'home': {
                        'code': game.home_team,
                        'name': home_team.team_name if home_team else game.home_team
                    },
                    'away': {
                        'code': game.away_team,
                        'name': away_team.team_name if away_team else game.away_team
                    }
                },
                'pitchers': {
                    'home': home_pitcher,
                    'away': away_pitcher
                },
                'matchup_analysis': self.analyze_pitcher_matchup(home_pitcher, away_pitcher)
            }

            return matchup_data

        except Exception as e:
            self.logger.error(f"獲取投手對戰數據失敗: {e}")
            return self.create_error_response(game, str(e))
    
    def get_starting_pitcher_from_boxscore(self, game_id: str, is_home: bool) -> Optional[Dict]:
        """從 Box Score 數據獲取先發投手信息"""
        try:
            # 查詢 Box Score 數據
            result = db.session.execute(text("""
                SELECT boxscore_json, home_team_stats, away_team_stats
                FROM boxscore_data 
                WHERE game_id = :game_id
            """), {"game_id": game_id}).fetchone()
            
            if not result:
                self.logger.info(f"沒有找到 {game_id} 的 Box Score 數據")
                return None
            
            # 解析 Box Score JSON 數據
            boxscore_data = json.loads(result[0])
            team_side = 'home' if is_home else 'away'
            
            # 從 Box Score 找先發投手
            teams_data = boxscore_data.get('teams', {})
            team_data = teams_data.get(team_side, {})
            players = team_data.get('players', {})
            
            starting_pitcher = None
            max_innings = 0
            
            # 找投球局數最多的投手（通常是先發）
            for player_id, player_data in players.items():
                pitching_stats = player_data.get('stats', {}).get('pitching')
                if pitching_stats:
                    innings_str = pitching_stats.get('inningsPitched', '0')
                    # 將投球局數轉換為數字（例如 "6.1" -> 6.33）
                    innings = self.parse_innings(innings_str)
                    
                    if innings > max_innings:
                        max_innings = innings
                        starting_pitcher = {
                            'player_id': player_id,
                            'name': player_data.get('person', {}).get('fullName', ''),
                            'stats': pitching_stats
                        }
            
            if starting_pitcher:
                return self.format_pitcher_info(starting_pitcher, is_home)
            
            return None
            
        except Exception as e:
            self.logger.error(f"從 Box Score 獲取先發投手失敗 ({game_id}): {e}")
            return None
    
    def parse_innings(self, innings_str: str) -> float:
        """解析投球局數字串（例如 "6.1" -> 6.33）"""
        try:
            if '.' in innings_str:
                whole, fraction = innings_str.split('.')
                # 棒球中的小數點後數字代表1/3局
                return float(whole) + float(fraction) / 3
            else:
                return float(innings_str)
        except:
            return 0.0
    
    def format_pitcher_info(self, pitcher_data: Dict, is_home: bool) -> Dict:
        """格式化投手信息"""
        try:
            stats = pitcher_data['stats']
            name = pitcher_data['name']
            
            # 從統計數據提取信息
            innings_pitched = self.parse_innings(stats.get('inningsPitched', '0'))
            earned_runs = stats.get('earnedRuns', 0)
            hits = stats.get('hits', 0)
            walks = stats.get('baseOnBalls', 0)
            strikeouts = stats.get('strikeOuts', 0)
            
            # 計算 ERA（本場比賽的）
            game_era = (earned_runs * 9 / innings_pitched) if innings_pitched > 0 else 0
            
            # 計算 WHIP（本場比賽的）
            game_whip = ((hits + walks) / innings_pitched) if innings_pitched > 0 else 0
            
            # 嘗試獲取賽季統計數據
            season_stats = self.get_season_pitcher_stats(name)
            
            return {
                'name': name,
                'player_id': pitcher_data.get('player_id'),
                'era': season_stats.get('era', game_era),
                'wins': season_stats.get('wins', 0),
                'losses': season_stats.get('losses', 0),
                'saves': season_stats.get('saves', 0),
                'strikeouts': strikeouts,  # 使用本場數據
                'walks': walks,  # 使用本場數據
                'innings_pitched': innings_pitched,  # 使用本場數據
                'whip': season_stats.get('whip', game_whip),
                'record': f"{season_stats.get('wins', 0)}-{season_stats.get('losses', 0)}",
                'strength': self.get_pitcher_strength_level(season_stats.get('era', game_era)),
                'team': pitcher_data.get('team', ''),
                'season': 2024,
                'data_source': 'boxscore + season_stats',
                'game_performance': {
                    'innings': innings_pitched,
                    'earned_runs': earned_runs,
                    'hits': hits,
                    'walks': walks,
                    'strikeouts': strikeouts,
                    'game_era': round(game_era, 2),
                    'game_whip': round(game_whip, 2)
                }
            }
            
        except Exception as e:
            self.logger.error(f"格式化投手信息失敗: {e}")
            return self.create_default_pitcher_info(pitcher_data.get('name', 'Unknown'))
    
    def get_season_pitcher_stats(self, pitcher_name: str) -> Dict:
        """獲取投手賽季統計數據"""
        try:
            # 嘗試從 PlayerStats 表查詢
            pitcher_stats = PlayerStats.query.filter(
                PlayerStats.player_name.like(f'%{pitcher_name}%'),
                PlayerStats.position == 'P',
                PlayerStats.era.isnot(None)
            ).order_by(PlayerStats.season.desc()).first()
            
            if pitcher_stats:
                return {
                    'era': float(pitcher_stats.era),
                    'wins': pitcher_stats.wins or 0,
                    'losses': pitcher_stats.losses or 0,
                    'saves': pitcher_stats.saves or 0,
                    'whip': float(pitcher_stats.whip) if pitcher_stats.whip else 1.50
                }
            else:
                return {'era': 4.50, 'wins': 0, 'losses': 0, 'saves': 0, 'whip': 1.50}
                
        except Exception as e:
            self.logger.error(f"查詢賽季統計失敗 ({pitcher_name}): {e}")
            return {'era': 4.50, 'wins': 0, 'losses': 0, 'saves': 0, 'whip': 1.50}
    
    def get_starting_pitcher_fallback(self, game_id: str, team_code: str, is_home: bool) -> Optional[Dict]:
        """備用方法：從其他來源獲取先發投手"""
        try:
            from models.database import Game
            
            # 檢查比賽狀態，如果是 scheduled 則嘗試預測
            game = Game.query.filter_by(game_id=game_id).first()
            if game and game.game_status == 'scheduled':
                self.logger.info(f"比賽狀態為 scheduled，嘗試預測先發投手")
                
                # 使用可能先發投手預測器
                try:
                    from probable_pitcher_predictor import ProbablePitcherPredictor
                    predictor = ProbablePitcherPredictor()
                    predicted_pitcher = predictor.predict_starting_pitcher(game, is_home)
                    
                    if predicted_pitcher and predicted_pitcher.get('data_source') != 'predicted_team_average':
                        return predicted_pitcher
                        
                except ImportError:
                    self.logger.warning("可能先發投手預測器不可用")
                except Exception as e:
                    self.logger.error(f"投手預測失敗: {e}")
            
            # 方法1：查詢比賽統計中投球局數最多的投手（已完成比賽）
            team_obj = Team.query.filter_by(team_code=team_code).first()
            if team_obj:
                # 這裡可以添加其他查詢邏輯
                pass
            
            # 返回球隊平均投手數據作為最後備選
            return self.get_team_average_pitcher_stats(team_code)
            
        except Exception as e:
            self.logger.error(f"備用投手查詢失敗 ({team_code}): {e}")
            return self.get_team_average_pitcher_stats(team_code)
    
    def get_team_average_pitcher_stats(self, team_code: str) -> Dict:
        """獲取球隊平均投手統計"""
        try:
            team_obj = Team.query.filter_by(team_code=team_code).first()
            team_name = team_obj.team_name if team_obj else team_code
            
            return {
                'name': f'{team_name} 先發投手',
                'player_id': None,
                'era': 4.50,
                'wins': 0,
                'losses': 0,
                'saves': 0,
                'strikeouts': 0,
                'walks': 0,
                'innings_pitched': 0.0,
                'whip': 1.50,
                'record': '0-0',
                'strength': '普通',
                'team': team_code,
                'season': 2024,
                'data_source': 'team_average'
            }
            
        except Exception as e:
            self.logger.error(f"獲取球隊平均統計失敗 ({team_code}): {e}")
            return self.create_default_pitcher_info('未知投手')
    
    def create_default_pitcher_info(self, name: str) -> Dict:
        """創建默認投手信息"""
        return {
            'name': name,
            'player_id': None,
            'era': 4.50,
            'wins': 0,
            'losses': 0,
            'saves': 0,
            'strikeouts': 0,
            'walks': 0,
            'innings_pitched': 0.0,
            'whip': 1.50,
            'record': '0-0',
            'strength': '普通',
            'team': '',
            'season': 2024,
            'data_source': 'default'
        }
    
    def get_pitcher_strength_level(self, era: float) -> str:
        """根據 ERA 判斷投手等級"""
        if era <= 2.50:
            return '王牌'
        elif era <= 3.50:
            return '優秀'
        elif era <= 4.50:
            return '普通'
        else:
            return '掙扎中'
    
    def analyze_pitcher_matchup(self, home_pitcher: Optional[Dict], away_pitcher: Optional[Dict]) -> Dict:
        """分析投手對戰"""
        try:
            if not home_pitcher or not away_pitcher:
                return {
                    'matchup_type': '數據不足',
                    'advantage': '無法分析',
                    'prediction': '需要更多投手數據',
                    'key_points': ['投手信息不完整', '建議刷新數據']
                }
            
            home_era = home_pitcher.get('era', 4.50)
            away_era = away_pitcher.get('era', 4.50)
            
            era_diff = abs(home_era - away_era)
            
            if era_diff <= 0.50:
                matchup_type = '勢均力敵'
                if home_era < away_era:
                    advantage = '主隊投手略佔優勢'
                elif away_era < home_era:
                    advantage = '客隊投手略佔優勢'
                else:
                    advantage = '投手實力相當'
            elif home_era < away_era:
                matchup_type = '主隊優勢'
                advantage = f'主隊投手 ERA 領先 {era_diff:.2f}'
            else:
                matchup_type = '客隊優勢'
                advantage = f'客隊投手 ERA 領先 {era_diff:.2f}'
            
            # 生成關鍵分析點
            key_points = []
            if home_pitcher.get('data_source') == 'boxscore + season_stats':
                key_points.append(f"主隊: {home_pitcher['name']} (ERA: {home_era:.2f})")
            if away_pitcher.get('data_source') == 'boxscore + season_stats':
                key_points.append(f"客隊: {away_pitcher['name']} (ERA: {away_era:.2f})")
            
            # 添加本場表現分析
            if 'game_performance' in home_pitcher:
                perf = home_pitcher['game_performance']
                key_points.append(f"主隊本場: {perf['innings']}局 {perf['earned_runs']}失分")
            
            if 'game_performance' in away_pitcher:
                perf = away_pitcher['game_performance']
                key_points.append(f"客隊本場: {perf['innings']}局 {perf['earned_runs']}失分")
            
            return {
                'matchup_type': matchup_type,
                'advantage': advantage,
                'prediction': f'預期比賽型態: {matchup_type}',
                'key_points': key_points
            }
            
        except Exception as e:
            self.logger.error(f"投手對戰分析失敗: {e}")
            return {
                'matchup_type': '分析失敗',
                'advantage': str(e),
                'prediction': '請重新整理頁面',
                'key_points': []
            }
    
    def create_error_response(self, game: Game, error_msg: str) -> Dict:
        """創建錯誤響應"""
        return {
            'error': error_msg,
            'game_info': {
                'game_id': game.game_id,
                'date': game.date.strftime('%Y-%m-%d'),
                'status': game.game_status
            },
            'teams': {
                'home': {'code': game.home_team, 'name': game.home_team},
                'away': {'code': game.away_team, 'name': game.away_team}
            },
            'pitchers': {
                'home': self.create_default_pitcher_info('數據載入中...'),
                'away': self.create_default_pitcher_info('數據載入中...')
            },
            'matchup_analysis': {
                'matchup_type': '數據載入中',
                'advantage': '正在分析...',
                'prediction': '請稍候刷新',
                'key_points': ['系統正在載入投手數據']
            }
        }

def test_enhanced_pitcher_matchup():
    """測試增強投手對戰系統"""
    print("🧪 測試增強投手對戰系統")
    print("=" * 50)
    
    from app import app
    with app.app_context():
        try:
            # 查找一場有 Box Score 數據的比賽
            result = db.session.execute(text("""
                SELECT game_id FROM boxscore_data LIMIT 1
            """)).fetchone()
            
            if result:
                game_id = result[0]
                print(f"🎯 測試比賽: {game_id}")
                
                game = Game.query.filter_by(game_id=game_id).first()
                if game:
                    analyzer = EnhancedPitcherMatchup()
                    matchup_data = analyzer.get_pitcher_matchup_data(game)
                    
                    print(f"✅ 對戰分析成功")
                    print(f"   主隊投手: {matchup_data['pitchers']['home']['name']}")
                    print(f"   客隊投手: {matchup_data['pitchers']['away']['name']}")
                    print(f"   對戰類型: {matchup_data['matchup_analysis']['matchup_type']}")
                    
                else:
                    print(f"❌ 找不到比賽記錄")
            else:
                print("❌ 沒有 Box Score 數據可測試")
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_pitcher_matchup()