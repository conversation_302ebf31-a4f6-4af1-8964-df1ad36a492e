#!/usr/bin/env python3
"""
集成賠率系統
將增強型抓取器與現有系統整合，確保數據正確保存到數據庫
"""

import logging
from datetime import date, datetime
from typing import List, Dict, Optional
from enhanced_sbr_scraper import EnhancedSBRScraper

logger = logging.getLogger(__name__)

class IntegratedOddsSystem:
    """集成賠率系統"""
    
    def __init__(self):
        self.scraper = EnhancedSBRScraper()
        
    def fetch_and_save_odds(self, target_date: date = None, market_type: str = 'both') -> Dict:
        """抓取並保存賠率數據"""
        if target_date is None:
            target_date = date.today()
            
        logger.info(f"🎯 開始抓取並保存 {target_date} 的賠率數據")
        
        try:
            # 1. 抓取原始數據
            raw_result = self.scraper.fetch_current_odds(target_date)
            raw_games = raw_result.get('games', [])
            
            if not raw_games:
                logger.warning("❌ 沒有抓取到任何比賽數據")
                return {
                    'success': False,
                    'message': '沒有抓取到比賽數據',
                    'total_saved': 0
                }
            
            logger.info(f"📊 原始數據：{len(raw_games)} 場比賽")
            
            # 2. 轉換數據格式以適配現有保存函數
            converted_games = self._convert_to_legacy_format(raw_games)
            
            logger.info(f"🔄 格式轉換完成：{len(converted_games)} 場比賽")
            
            if not converted_games:
                logger.warning("⚠️ 格式轉換後沒有有效的比賽數據")
                return {
                    'success': False,
                    'message': f'格式轉換失敗，原始數據 {len(raw_games)} 場比賽，轉換後 0 場',
                    'total_saved': 0,
                    'debug_info': {
                        'raw_games_sample': raw_games[:1] if raw_games else [],
                        'conversion_issues': 'No valid odds data found after conversion'
                    }
                }
            
            # 3. 保存到數據庫
            from views.admin import save_betting_odds_to_db
            saved_count = save_betting_odds_to_db(converted_games, target_date, market_type)
            
            # 4. 同時保存增強格式的數據
            enhanced_saved = self._save_enhanced_format(raw_games, target_date)
            
            return {
                'success': True,
                'message': f'成功保存 {saved_count} 條傳統格式記錄，{enhanced_saved} 條增強格式記錄',
                'total_saved': saved_count + enhanced_saved,
                'legacy_saved': saved_count,
                'enhanced_saved': enhanced_saved,
                'games_processed': len(raw_games)
            }
            
        except Exception as e:
            logger.error(f"❌ 抓取保存失敗: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'message': f'抓取保存失敗: {str(e)}',
                'total_saved': 0
            }
    
    def _convert_to_legacy_format(self, raw_games: List[Dict]) -> List[Dict]:
        """轉換為舊版本格式以適配現有保存函數"""
        converted_games = []
        
        for game in raw_games:
            try:
                # 提取基本信息
                away_team = game.get('away_team', 'N/A')
                home_team = game.get('home_team', 'N/A')
                
                if away_team == 'N/A' or home_team == 'N/A':
                    continue
                
                # 提取賠率數據（取第一個博彩商作為主要數據）
                odds_data = game.get('odds', {})
                
                if not odds_data:
                    continue
                
                # 選擇第一個有效的博彩商數據
                main_bookmaker = None
                main_odds = None
                
                for bookmaker, odds in odds_data.items():
                    if odds and isinstance(odds, dict):
                        main_bookmaker = bookmaker
                        main_odds = odds
                        break
                
                if not main_odds:
                    continue
                
                # 構造舊格式的odds字典
                legacy_odds = {}
                
                # 處理讓分盤
                if 'spread' in main_odds:
                    spread_data = main_odds['spread']
                    away_spread = spread_data.get('away_spread')
                    home_spread = spread_data.get('home_spread')
                    
                    if away_spread is not None:
                        legacy_odds['spread_line'] = abs(float(away_spread))
                        legacy_odds['spread_away_odds'] = spread_data.get('away_odds', 100)
                        legacy_odds['spread_home_odds'] = spread_data.get('home_odds', 100)
                
                # 處理大小分
                if 'totals' in main_odds:
                    totals_data = main_odds['totals']
                    total_line = totals_data.get('total_line')
                    
                    if total_line is not None:
                        legacy_odds['total_line'] = float(total_line)
                        legacy_odds['over_odds'] = totals_data.get('over_odds', 100)
                        legacy_odds['under_odds'] = totals_data.get('under_odds', 100)
                
                # 處理勝負盤
                if 'moneyline' in main_odds:
                    ml_data = main_odds['moneyline']
                    legacy_odds['home_ml_odds'] = ml_data.get('home_odds', 100)
                    legacy_odds['away_ml_odds'] = ml_data.get('away_odds', 100)
                
                converted_game = {
                    'away_team': away_team,
                    'home_team': home_team,
                    'game_time': game.get('game_time', ''),
                    'venue': game.get('venue', ''),
                    'odds': legacy_odds,
                    'bookmaker': main_bookmaker,
                    'enhanced_data': game  # 保留原始數據供參考
                }
                
                converted_games.append(converted_game)
                logger.debug(f"✅ 轉換成功: {away_team} @ {home_team}")
                
            except Exception as e:
                logger.warning(f"⚠️ 轉換比賽失敗: {e}")
                continue
        
        return converted_games
    
    def _save_enhanced_format(self, raw_games: List[Dict], target_date: date) -> int:
        """保存增強格式的數據（支援多博彩商）"""
        try:
            from models.database import db, BettingOdds
            
            saved_count = 0
            
            for game in raw_games:
                away_team = game.get('away_team', 'N/A')
                home_team = game.get('home_team', 'N/A')
                
                if away_team == 'N/A' or home_team == 'N/A':
                    continue
                
                game_id = game.get('game_id') or f"{away_team}_{home_team}_{target_date.strftime('%Y%m%d')}"
                
                # 遍歷所有博彩商
                odds_data = game.get('odds', {})
                
                for bookmaker, odds in odds_data.items():
                    try:
                        # 保存讓分盤
                        if 'spread' in odds:
                            spread_data = odds['spread']
                            away_spread = spread_data.get('away_spread')
                            home_spread = spread_data.get('home_spread')
                            
                            if away_spread is not None and home_spread is not None:
                                spread_odds = BettingOdds(
                                    game_id=game_id,
                                    bookmaker=bookmaker,
                                    market_type='spreads',
                                    home_spread_point=float(home_spread),
                                    away_spread_point=float(away_spread),
                                    home_spread_price=spread_data.get('home_odds', 100),
                                    away_spread_price=spread_data.get('away_odds', 100),
                                    is_real=True,
                                    data_source='enhanced_sbr_scraper',
                                    odds_time=datetime.combine(target_date, datetime.min.time()),
                                    created_at=datetime.utcnow()
                                )
                                db.session.add(spread_odds)
                                saved_count += 1
                        
                        # 保存大小分
                        if 'totals' in odds:
                            totals_data = odds['totals']
                            total_line = totals_data.get('total_line')
                            
                            if total_line is not None:
                                total_odds = BettingOdds(
                                    game_id=game_id,
                                    bookmaker=bookmaker,
                                    market_type='totals',
                                    total_point=float(total_line),
                                    over_price=totals_data.get('over_odds', 100),
                                    under_price=totals_data.get('under_odds', 100),
                                    is_real=True,
                                    data_source='enhanced_sbr_scraper',
                                    odds_time=datetime.combine(target_date, datetime.min.time()),
                                    created_at=datetime.utcnow()
                                )
                                db.session.add(total_odds)
                                saved_count += 1
                        
                    except Exception as e:
                        logger.warning(f"⚠️ 保存 {bookmaker} 賠率失敗: {e}")
                        continue
            
            db.session.commit()
            return saved_count
            
        except Exception as e:
            logger.error(f"❌ 保存增強格式失敗: {e}")
            db.session.rollback()
            return 0


def test_integrated_system():
    """測試集成系統（帶Flask應用上下文）"""
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # 創建Flask應用上下文
    from app import create_app
    app = create_app()
    
    with app.app_context():
        system = IntegratedOddsSystem()
        result = system.fetch_and_save_odds(date(2025, 7, 10), 'both')
        
        print("🎯 集成賠率系統測試結果:")
        print(f"✅ 成功: {result.get('success')}")
        print(f"📊 處理比賽: {result.get('games_processed', 0)}")
        print(f"💾 傳統格式保存: {result.get('legacy_saved', 0)}")
        print(f"🔄 增強格式保存: {result.get('enhanced_saved', 0)}")
        print(f"📈 總計保存: {result.get('total_saved', 0)}")
        print(f"📝 消息: {result.get('message', '')}")


if __name__ == "__main__":
    test_integrated_system()