#!/usr/bin/env python3
"""
測試預測歷史記錄保存功能
驗證預測生成後是否能正確查詢結果
"""

import requests
import json
from datetime import date, datetime
import time

def test_prediction_history_integration():
    """測試預測→歷史記錄→查詢的完整流程"""
    
    base_url = "http://localhost:5500"
    
    print("🧪 開始測試預測歷史記錄集成...")
    print("=" * 60)
    
    # 1. 測試今日預測生成（含歷史記錄保存）
    print("\n📊 步驟 1: 生成今日預測並保存到歷史記錄")
    
    today = date.today().isoformat()
    
    prediction_data = {
        "date": today,
        "save_to_history": True
    }
    
    try:
        print(f"發送預測請求到: {base_url}/unified/api/predict/enhanced_daily")
        print(f"請求數據: {json.dumps(prediction_data, indent=2)}")
        
        response = requests.post(
            f"{base_url}/unified/api/predict/enhanced_daily", 
            json=prediction_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 預測生成成功:")
            print(f"   - 總場次: {result.get('total_games', 0)}")
            print(f"   - 成功預測: {result.get('successful_predictions', 0)}")
            print(f"   - 保存到歷史: {result.get('saved_to_history', 0)}")
            print(f"   - 引擎: {result.get('engine_info', {}).get('engine_type', 'Unknown')}")
            
            prediction_success = True
            saved_count = result.get('saved_to_history', 0)
            
        else:
            print(f"❌ 預測請求失敗: HTTP {response.status_code}")
            print(f"響應: {response.text}")
            prediction_success = False
            saved_count = 0
            
    except Exception as e:
        print(f"❌ 預測請求異常: {e}")
        prediction_success = False
        saved_count = 0
    
    # 2. 等待數據庫提交
    if prediction_success and saved_count > 0:
        print(f"\n⏳ 等待數據庫提交... (3秒)")
        time.sleep(3)
    
    # 3. 測試查詢預測結果
    print(f"\n🔍 步驟 2: 查詢今日預測結果")
    
    try:
        query_url = f"{base_url}/unified/query"
        print(f"查詢URL: {query_url}?date={today}")
        
        response = requests.get(f"{query_url}?date={today}")
        
        if response.status_code == 200:
            print("✅ 查詢頁面載入成功")
            
            # 檢查頁面內容是否包含預測結果
            content = response.text
            if "沒有找到預測記錄" in content:
                print("❌ 查詢結果: 沒有找到預測記錄")
                query_success = False
            elif "場比賽" in content and "預測結果" in content:
                print("✅ 查詢結果: 找到預測記錄")
                query_success = True
            else:
                print("⚠️ 查詢結果: 頁面載入但內容待確認")
                query_success = False
        else:
            print(f"❌ 查詢頁面載入失敗: HTTP {response.status_code}")
            query_success = False
            
    except Exception as e:
        print(f"❌ 查詢請求異常: {e}")
        query_success = False
    
    # 4. 測試批次預測系統
    print(f"\n🔄 步驟 3: 測試批次預測系統連結")
    
    try:
        batch_url = f"{base_url}/batch_system/batch_system"
        print(f"批次系統URL: {batch_url}")
        
        response = requests.get(batch_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ 批次預測系統頁面可訪問")
            batch_system_success = True
        else:
            print(f"❌ 批次預測系統頁面失敗: HTTP {response.status_code}")
            batch_system_success = False
            
    except Exception as e:
        print(f"❌ 批次系統請求異常: {e}")
        batch_system_success = False
    
    # 5. 測試預測效果分析系統
    print(f"\n📈 步驟 4: 測試預測效果分析系統")
    
    try:
        analysis_url = f"{base_url}/prediction_analysis/analysis"
        print(f"分析系統URL: {analysis_url}")
        
        response = requests.get(analysis_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ 預測效果分析頁面可訪問")
            analysis_system_success = True
        else:
            print(f"❌ 預測效果分析頁面失敗: HTTP {response.status_code}")
            analysis_system_success = False
            
    except Exception as e:
        print(f"❌ 分析系統請求異常: {e}")
        analysis_system_success = False
    
    # 6. 總結測試結果
    print(f"\n📋 測試總結")
    print("=" * 60)
    
    results = [
        ("預測生成", prediction_success),
        ("歷史記錄保存", saved_count > 0),
        ("預測結果查詢", query_success),
        ("批次預測系統", batch_system_success),
        ("預測效果分析", analysis_system_success)
    ]
    
    success_count = sum(1 for _, success in results if success)
    
    for name, success in results:
        status = "✅ 通過" if success else "❌ 失敗"
        print(f"  {name}: {status}")
    
    print(f"\n整體測試結果: {success_count}/{len(results)} 通過")
    
    if success_count == len(results):
        print("🎉 所有測試通過！預測歷史記錄系統運作正常。")
        print("\n✅ 用戶問題已解決:")
        print("   - 預測結果現在會自動保存到歷史記錄")
        print("   - 可以通過查詢功能查看預測結果")  
        print("   - 批次預測系統可正常訪問")
        print("   - 預測效果分析系統可用於評估準確性")
        
    elif success_count >= 3:
        print("⚠️ 大部分功能正常，部分功能需要調整。")
        
    else:
        print("❌ 系統存在重大問題，需要進一步調試。")
        
    return success_count == len(results)

if __name__ == "__main__":
    test_prediction_history_integration()