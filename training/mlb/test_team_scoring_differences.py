#!/usr/bin/env python3
"""
測試球隊得分能力差異
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import Prediction, Game, TeamStats, db
from models.ml_predictor import MLBPredictor
from datetime import date, timedelta
import pandas as pd

def test_team_scoring_differences():
    """測試球隊得分能力差異"""
    app = create_app()
    with app.app_context():
        print("🔍 檢查球隊得分能力差異問題")
        print("=" * 60)
        
        # 1. 檢查最近的預測結果
        print("\n📊 最近10個預測結果:")
        recent_predictions = Prediction.query.join(Game).order_by(Game.date.desc()).limit(10).all()
        
        prediction_scores = []
        for pred in recent_predictions:
            game = Game.query.filter_by(game_id=pred.game_id).first()
            if game:
                away_score = pred.predicted_away_score
                home_score = pred.predicted_home_score
                prediction_scores.append((away_score, home_score))
                print(f"{game.away_team} @ {game.home_team}")
                print(f"  預測: {away_score:.1f} - {home_score:.1f}")
                print(f"  日期: {game.date}")
                print()
        
        # 2. 分析預測分數的變異性
        if prediction_scores:
            away_scores = [s[0] for s in prediction_scores]
            home_scores = [s[1] for s in prediction_scores]
            
            print(f"\n📈 預測分數統計:")
            print(f"客隊得分範圍: {min(away_scores):.1f} - {max(away_scores):.1f}")
            print(f"主隊得分範圍: {min(home_scores):.1f} - {max(home_scores):.1f}")
            print(f"客隊平均: {sum(away_scores)/len(away_scores):.1f}")
            print(f"主隊平均: {sum(home_scores)/len(home_scores):.1f}")
            
            # 檢查是否所有預測都相同
            unique_away = len(set(f"{s:.1f}" for s in away_scores))
            unique_home = len(set(f"{s:.1f}" for s in home_scores))
            
            print(f"客隊得分種類數: {unique_away}")
            print(f"主隊得分種類數: {unique_home}")
            
            if unique_away == 1 and unique_home == 1:
                print("❌ 問題發現: 所有預測分數都相同!")
                return False
            elif unique_away <= 2 and unique_home <= 2:
                print("⚠️  警告: 預測分數變異性很低")
            else:
                print("✅ 預測分數有合理變異性")
        
        # 3. 檢查球隊統計差異
        print(f"\n🏟️ 球隊統計差異:")
        teams = TeamStats.query.order_by(TeamStats.runs_per_game.desc()).limit(10).all()
        
        if teams:
            print("球隊得分能力排名 (前10名):")
            team_stats = []
            for i, team in enumerate(teams, 1):
                team_stats.append({
                    'team': team.team_id,
                    'runs_per_game': team.runs_per_game,
                    'era': team.era,
                    'win_pct': team.win_percentage
                })
                print(f"{i:2d}. {team.team_id}: {team.runs_per_game:.2f}分/場, ERA={team.era:.2f}, 勝率={team.win_percentage:.3f}")
            
            # 分析統計差異
            runs_list = [t['runs_per_game'] for t in team_stats]
            era_list = [t['era'] for t in team_stats]
            
            print(f"\n📊 球隊統計變異性:")
            print(f"得分範圍: {min(runs_list):.2f} - {max(runs_list):.2f} (差距: {max(runs_list) - min(runs_list):.2f})")
            print(f"ERA範圍: {min(era_list):.2f} - {max(era_list):.2f} (差距: {max(era_list) - min(era_list):.2f})")
            
            if max(runs_list) - min(runs_list) < 1.0:
                print("❌ 問題: 球隊得分能力差異太小")
            else:
                print("✅ 球隊統計有合理差異")
        else:
            print("❌ 沒有找到球隊統計數據")
            return False
        
        # 4. 測試模型對不同球隊的預測
        print(f"\n🤖 測試模型對不同實力球隊的預測:")
        try:
            predictor = MLBPredictor()
            
            # 測試強隊 vs 弱隊的組合
            test_cases = [
                {'home_team': 'LAD', 'away_team': 'OAK', 'desc': '強隊(LAD) vs 弱隊(OAK)'},
                {'home_team': 'NYY', 'away_team': 'HOU', 'desc': '強隊(NYY) vs 強隊(HOU)'},
                {'home_team': 'OAK', 'away_team': 'LAD', 'desc': '弱隊(OAK) vs 強隊(LAD)'},
                {'home_team': 'MIA', 'away_team': 'COL', 'desc': '弱隊(MIA) vs 弱隊(COL)'}
            ]
            
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n測試 {i}: {test_case['desc']}")
                
                prediction = predictor.predict_game(
                    test_case['home_team'], 
                    test_case['away_team'], 
                    date.today()
                )
                
                if prediction:
                    away_score = prediction['away_score']
                    home_score = prediction['home_score']
                    print(f"  預測結果: {away_score:.1f} - {home_score:.1f}")
                    
                    # 檢查球隊統計
                    home_stats = TeamStats.query.filter_by(team_id=test_case['home_team']).first()
                    away_stats = TeamStats.query.filter_by(team_id=test_case['away_team']).first()
                    
                    if home_stats and away_stats:
                        print(f"  {test_case['home_team']} 統計: {home_stats.runs_per_game:.2f}分/場, ERA={home_stats.era:.2f}")
                        print(f"  {test_case['away_team']} 統計: {away_stats.runs_per_game:.2f}分/場, ERA={away_stats.era:.2f}")
                else:
                    print("  ❌ 預測失敗")
                    
        except Exception as e:
            print(f"❌ 模型測試失敗: {e}")
            import traceback
            traceback.print_exc()
        
        return True

if __name__ == "__main__":
    test_team_scoring_differences()
