#!/usr/bin/env python3
"""
診斷投手數據問題
檢查已完成比賽的投手信息可用性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.comprehensive_pitcher_extractor import comprehensive_pitcher_extractor

def diagnose_pitcher_data():
    """診斷投手數據問題"""
    app = create_app()
    
    with app.app_context():
        print("🔍 診斷投手數據問題")
        print("=" * 80)
        
        # 測試已完成的比賽
        test_games = [
            {
                'game_id': '777122',
                'description': 'CHC @ NYY - 您提到的比賽'
            },
            {
                'game_id': '777170', 
                'description': 'TB @ BOS - 另一場比賽'
            }
        ]
        
        for i, test_case in enumerate(test_games, 1):
            print(f"\n🎯 測試案例 {i}: {test_case['description']}")
            print("-" * 60)
            
            # 診斷數據可用性
            diagnosis = comprehensive_pitcher_extractor.diagnose_pitcher_data_availability(test_case['game_id'])
            
            if 'error' in diagnosis:
                print(f"❌ 診斷失敗: {diagnosis['error']}")
                continue
            
            # 顯示比賽信息
            if diagnosis['game_exists']:
                game_info = diagnosis['game_info']
                print(f"📅 比賽信息:")
                print(f"   {game_info['away_team']} @ {game_info['home_team']}")
                print(f"   日期: {game_info['date']}")
                print(f"   狀態: {game_info['status']}")
                print(f"   比分: {game_info['score']}")
            else:
                print(f"❌ 比賽不存在")
                continue
            
            # 顯示GameDetail表投手信息
            print(f"\n📊 GameDetail表投手信息:")
            pitchers = diagnosis['game_detail_pitchers']
            print(f"   主隊先發: {pitchers['home_starting'] or '❌ 無'}")
            print(f"   客隊先發: {pitchers['away_starting'] or '❌ 無'}")
            print(f"   勝利投手: {pitchers['winning'] or '❌ 無'}")
            print(f"   敗戰投手: {pitchers['losing'] or '❌ 無'}")
            print(f"   救援投手: {pitchers['save'] or '❌ 無'}")
            
            # 顯示PlayerGameStats投手數據
            print(f"\n📈 PlayerGameStats投手數據:")
            print(f"   投手統計數量: {diagnosis['player_game_stats_pitchers']}")
            
            # 顯示建議
            print(f"\n💡 建議:")
            if diagnosis['recommendations']:
                for rec in diagnosis['recommendations']:
                    print(f"   • {rec}")
            else:
                print(f"   ✅ 數據完整，無需特別處理")

def test_comprehensive_extraction():
    """測試全面投手信息提取"""
    print(f"\n\n🚀 測試全面投手信息提取")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        # 測試CHC @ NYY比賽
        game_id = '777122'
        home_team = 'NYY'
        away_team = 'CHC'
        
        print(f"🎯 測試比賽: {away_team} @ {home_team} (ID: {game_id})")
        
        try:
            # 使用全面投手提取器
            analysis = comprehensive_pitcher_extractor.get_pitcher_info_comprehensive(
                game_id, home_team, away_team
            )
            
            print(f"\n✅ 全面投手分析成功!")
            print(f"📊 投手分析結果:")
            print(f"   主隊投手: {analysis['home_pitcher']['name']} "
                  f"(ERA: {analysis['home_pitcher']['era']:.2f}, "
                  f"等級: {analysis['home_pitcher']['strength']}, "
                  f"來源: {analysis['home_pitcher']['source']})")
            print(f"   客隊投手: {analysis['away_pitcher']['name']} "
                  f"(ERA: {analysis['away_pitcher']['era']:.2f}, "
                  f"等級: {analysis['away_pitcher']['strength']}, "
                  f"來源: {analysis['away_pitcher']['source']})")
            print(f"   對戰類型: {analysis['matchup_type']}")
            print(f"   平均ERA: {analysis['average_era']:.2f}")
            
            print(f"\n📋 數據來源:")
            sources = analysis['data_sources']
            print(f"   主隊數據來源: {sources['home']}")
            print(f"   客隊數據來源: {sources['away']}")
            
            # 解釋數據來源
            source_explanations = {
                'game_table_starting': 'Game表先發投手字段',
                'game_stats': 'PlayerGameStats投手統計',
                'winning_pitcher': '勝利投手信息',
                'losing_pitcher': '敗戰投手信息',
                'team_default': '球隊默認投手',
                'default': '系統默認投手'
            }
            
            print(f"\n📝 數據來源說明:")
            for team, source in sources.items():
                explanation = source_explanations.get(source, '未知來源')
                print(f"   {team}: {explanation}")
            
        except Exception as e:
            print(f"❌ 全面投手分析失敗: {e}")
            import traceback
            traceback.print_exc()

def test_multiple_completed_games():
    """測試多場已完成比賽的投手數據"""
    print(f"\n\n📊 測試多場已完成比賽的投手數據")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        from models.database import Game
        
        # 獲取最近5場已完成的比賽
        completed_games = Game.query.filter(
            Game.game_status == 'completed',
            Game.home_score.isnot(None)
        ).order_by(Game.date.desc()).limit(5).all()
        
        if not completed_games:
            print("❌ 找不到已完成的比賽")
            return
        
        print(f"📊 找到 {len(completed_games)} 場已完成的比賽")
        
        success_count = 0
        partial_success_count = 0
        failure_count = 0
        
        for i, game in enumerate(completed_games, 1):
            print(f"\n{i}. {game.away_team} @ {game.home_team} ({game.date})")
            print(f"   比分: {game.away_score} - {game.home_score}")
            
            try:
                # 診斷數據可用性
                diagnosis = comprehensive_pitcher_extractor.diagnose_pitcher_data_availability(game.game_id)
                
                # 檢查數據完整性
                game_pitchers = diagnosis['game_detail_pitchers']
                has_starting_pitchers = bool(game_pitchers['home_starting'] and game_pitchers['away_starting'])
                has_win_loss_pitchers = bool(game_pitchers['winning'] and game_pitchers['losing'])
                has_game_stats = diagnosis['player_game_stats_pitchers'] > 0
                
                if has_starting_pitchers:
                    print(f"   ✅ 有先發投手信息")
                    success_count += 1
                elif has_win_loss_pitchers or has_game_stats:
                    print(f"   ⚠️  部分投手信息 (勝負投手: {has_win_loss_pitchers}, 統計: {has_game_stats})")
                    partial_success_count += 1
                else:
                    print(f"   ❌ 缺少投手信息")
                    failure_count += 1
                
                # 測試全面提取
                analysis = comprehensive_pitcher_extractor.get_pitcher_info_comprehensive(
                    game.game_id, game.home_team, game.away_team
                )
                
                print(f"   📊 提取結果: {analysis['home_pitcher']['source']} vs {analysis['away_pitcher']['source']}")
                
            except Exception as e:
                print(f"   ❌ 分析失敗: {e}")
                failure_count += 1
        
        # 統計結果
        print(f"\n📈 統計結果:")
        print(f"   完整數據: {success_count} 場")
        print(f"   部分數據: {partial_success_count} 場")
        print(f"   缺少數據: {failure_count} 場")
        
        total = len(completed_games)
        if total > 0:
            success_rate = (success_count + partial_success_count) / total * 100
            print(f"   數據可用率: {success_rate:.1f}%")
            
            if success_rate >= 80:
                print(f"   ✅ 數據質量良好")
            elif success_rate >= 60:
                print(f"   ⚠️  數據質量一般，建議補充")
            else:
                print(f"   ❌ 數據質量較差，需要重新下載")

def provide_solutions():
    """提供解決方案"""
    print(f"\n\n💡 解決方案建議")
    print("=" * 80)
    
    print("🎯 針對您的問題：「已發生的比賽找不到投手資料」")
    print()
    print("📋 問題分析:")
    print("1. 已完成比賽應該有完整的投手信息")
    print("2. 如果Game表缺少先發投手，應該從PlayerGameStats提取")
    print("3. 如果PlayerGameStats也沒有，可以使用勝負投手信息")
    print("4. BoxScore數據應該包含詳細的投手統計")
    print()
    print("🔧 解決方案:")
    print("1. ✅ 已實現：全面投手信息提取器")
    print("   - 多數據源策略：Game表 → PlayerGameStats → 勝負投手 → 默認")
    print("   - 確保已完成比賽都能找到投手信息")
    print()
    print("2. 🔄 建議：數據補充策略")
    print("   - 對於缺少投手信息的已完成比賽，重新下載BoxScore數據")
    print("   - 確保PlayerGameStats表有完整的投手統計")
    print()
    print("3. 📊 監控：數據質量檢查")
    print("   - 定期檢查已完成比賽的投手數據完整性")
    print("   - 對於數據缺失的比賽，自動標記並補充")
    print()
    print("🚀 使用新的全面投手提取器:")
    print("```python")
    print("from models.comprehensive_pitcher_extractor import comprehensive_pitcher_extractor")
    print("analysis = comprehensive_pitcher_extractor.get_pitcher_info_comprehensive(")
    print("    game_id, home_team, away_team")
    print(")")
    print("```")

if __name__ == "__main__":
    diagnose_pitcher_data()
    test_comprehensive_extraction()
    test_multiple_completed_games()
    provide_solutions()
