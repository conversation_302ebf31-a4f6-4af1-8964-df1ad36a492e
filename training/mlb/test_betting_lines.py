#!/usr/bin/env python3
"""
測試博彩盤口數據獲取和顯示
"""

import sys
import os
from datetime import date, datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.unified_betting_predictor import UnifiedBettingPredictor

def test_betting_lines():
    """測試博彩盤口數據獲取"""
    print("=== 測試博彩盤口數據獲取 ===")
    
    app = create_app()
    
    with app.app_context():
        # 初始化預測器
        predictor = UnifiedBettingPredictor(app)
        
        # 測試日期
        test_date = date(2025, 7, 12)
        
        print(f"\n測試日期: {test_date}")
        
        # 創建虛擬比賽對象
        class TestGame:
            def __init__(self, away_team, home_team):
                self.away_team = away_team
                self.home_team = home_team
                self.game_id = f"test_{away_team}_{home_team}"
        
        # 測試比賽
        test_games = [
            TestGame('CHC', 'NYY'),
            TestGame('SEA', 'DET'),
            TestGame('PIT', 'MIN'),
            TestGame('ATL', 'STL'),
            TestGame('LAD', 'SF')
        ]
        
        for game in test_games:
            print(f"\n--- {game.away_team} @ {game.home_team} ---")
            
            # 獲取博彩盤口數據
            betting_lines = predictor._get_betting_lines(game, test_date)
            
            print(f"大小分: {betting_lines['over_under']}")
            print(f"讓分盤: {betting_lines['run_line_spread']}")
            print(f"主隊賠率: {betting_lines['moneyline_home']}")
            print(f"客隊賠率: {betting_lines['moneyline_away']}")
            print(f"數據來源: {betting_lines['data_source']}")
            
            # 測試預測和建議生成
            prediction = {
                'predicted_home_score': 5.0,
                'predicted_away_score': 4.0,
                'predicted_total_runs': 9.0,
                'confidence': 0.75,
                'home_team': game.home_team,
                'away_team': game.away_team
            }
            
            # 生成博彩建議
            prediction_with_recommendations = predictor._generate_betting_recommendations(prediction, betting_lines)
            
            if 'betting_recommendations' in prediction_with_recommendations:
                rec = prediction_with_recommendations['betting_recommendations']
                
                print("\n博彩建議:")
                if 'over_under' in rec:
                    ou = rec['over_under']
                    print(f"  大小分: {ou['recommendation']} (預測: {ou['predicted_total']:.1f}, 盤口: {ou['line']}, 差距: {ou['difference']:.1f})")
                
                if 'run_line' in rec:
                    rl = rec['run_line']
                    print(f"  讓分盤: {rl['recommendation']} (預測差距: {rl['predicted_margin']:.1f}, 讓分: {rl['spread']})")
                
                if 'moneyline' in rec:
                    ml = rec['moneyline']
                    print(f"  勝負盤: {ml['predicted_winner']} (勝率: {ml['win_probability']:.1%})")

def test_comprehensive_prediction():
    """測試完整預測功能"""
    print("\n\n=== 測試完整預測功能 ===")
    
    app = create_app()
    
    with app.app_context():
        predictor = UnifiedBettingPredictor(app)
        
        # 測試 CHC @ NYY (Boyd 壓制情況)
        game_id = "custom_CHC_NYY_20250712"
        target_date = date(2025, 7, 12)
        
        print(f"\n測試比賽: {game_id}")
        print(f"目標日期: {target_date}")
        
        # 測試不同模型版本
        model_versions = ['improved_v3', 'boyd_suppression']
        
        for version in model_versions:
            print(f"\n--- 模型版本: {version} ---")
            
            options = {
                'model_version_suffix': version,
                'use_improved_logic': True
            }
            
            result = predictor.predict_game_comprehensive(
                game_id=game_id,
                target_date=target_date,
                options=options
            )
            
            if result.get('success'):
                print(f"預測比分: {result['predicted_away_score']:.1f} - {result['predicted_home_score']:.1f}")
                print(f"總分: {result['predicted_total_runs']:.1f}")
                print(f"信心度: {result['confidence']:.1%}")
                
                if 'betting_lines' in result:
                    bl = result['betting_lines']
                    print(f"博彩盤口: O/U {bl['over_under']}, RL {bl['run_line_spread']}")
                
                if 'betting_recommendations' in result:
                    rec = result['betting_recommendations']
                    if 'over_under' in rec:
                        ou = rec['over_under']
                        print(f"大小分建議: {ou['recommendation']} (優勢: {ou['edge']:.1f})")
                    
                    if 'run_line' in rec:
                        rl = rec['run_line']
                        print(f"讓分盤建議: {rl['recommendation']}")
            else:
                print(f"預測失敗: {result.get('error', '未知錯誤')}")

if __name__ == "__main__":
    test_betting_lines()
    test_comprehensive_prediction()
    print("\n=== 測試完成 ===")
