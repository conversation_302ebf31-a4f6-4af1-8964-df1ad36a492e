#!/usr/bin/env python3
"""
測試預測修復 - 驗證球隊得分差異
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import TeamStats, Team, db
from models.ml_predictor import MLBPredictor
from datetime import date
import pandas as pd

def test_prediction_fix():
    """測試預測修復"""
    app = create_app()
    with app.app_context():
        print("🔧 測試預測修復 - 驗證球隊得分差異")
        print("=" * 60)
        
        # 1. 檢查TeamStats數據差異
        print("\n📊 檢查TeamStats數據差異:")
        
        # 選擇實力差距明顯的球隊
        strong_teams = ['LAD', 'NYY', 'HOU']  # 強隊
        weak_teams = ['OAK', 'MIA', 'COL']    # 弱隊
        
        print("強隊統計:")
        strong_stats = []
        for team_code in strong_teams:
            team = Team.query.filter_by(team_code=team_code).first()
            if team:
                stats = TeamStats.query.filter_by(team_id=team.team_id).first()
                if stats:
                    strong_stats.append(stats.runs_scored)
                    print(f"  {team_code}: 得分={stats.runs_scored:.2f}, ERA={stats.era:.2f}, 勝率={stats.win_percentage:.3f}")
        
        print("\n弱隊統計:")
        weak_stats = []
        for team_code in weak_teams:
            team = Team.query.filter_by(team_code=team_code).first()
            if team:
                stats = TeamStats.query.filter_by(team_id=team.team_id).first()
                if stats:
                    weak_stats.append(stats.runs_scored)
                    print(f"  {team_code}: 得分={stats.runs_scored:.2f}, ERA={stats.era:.2f}, 勝率={stats.win_percentage:.3f}")
        
        if strong_stats and weak_stats:
            strong_avg = sum(strong_stats) / len(strong_stats)
            weak_avg = sum(weak_stats) / len(weak_stats)
            print(f"\n📈 統計對比:")
            print(f"強隊平均得分: {strong_avg:.2f}")
            print(f"弱隊平均得分: {weak_avg:.2f}")
            print(f"得分差距: {strong_avg - weak_avg:.2f}")
        
        # 2. 測試特徵提取
        print(f"\n🔍 測試特徵提取:")
        try:
            predictor = MLBPredictor()
            
            # 測試強隊 vs 弱隊
            test_matchups = [
                ('LAD', 'OAK'),  # 強隊 vs 弱隊
                ('NYY', 'MIA'),  # 強隊 vs 弱隊
                ('HOU', 'COL'),  # 強隊 vs 弱隊
                ('OAK', 'LAD'),  # 弱隊 vs 強隊 (主客場對調)
            ]
            
            print("測試對戰:")
            feature_results = []
            
            for home_team, away_team in test_matchups:
                features = predictor._extract_simple_features_for_game(
                    home_team, away_team, date(2025, 6, 29)
                )
                
                if features:
                    feature_results.append({
                        'matchup': f"{away_team} @ {home_team}",
                        'home_runs_scored': features['home_runs_scored'],
                        'away_runs_scored': features['away_runs_scored'],
                        'home_era': features['home_era'],
                        'away_era': features['away_era']
                    })
                    print(f"  {away_team} @ {home_team}: 主隊得分={features['home_runs_scored']:.2f}, 客隊得分={features['away_runs_scored']:.2f}")
                else:
                    print(f"  {away_team} @ {home_team}: ❌ 特徵提取失敗")
            
            # 檢查特徵差異
            if len(feature_results) >= 2:
                print(f"\n🔍 特徵差異分析:")
                home_scores = [r['home_runs_scored'] for r in feature_results]
                away_scores = [r['away_runs_scored'] for r in feature_results]
                
                print(f"主隊得分範圍: {min(home_scores):.2f} - {max(home_scores):.2f}")
                print(f"客隊得分範圍: {min(away_scores):.2f} - {max(away_scores):.2f}")
                
                if max(home_scores) - min(home_scores) > 0.5:
                    print("✅ 主隊特徵有合理差異")
                else:
                    print("❌ 主隊特徵差異太小")
                    
                if max(away_scores) - min(away_scores) > 0.5:
                    print("✅ 客隊特徵有合理差異")
                else:
                    print("❌ 客隊特徵差異太小")
        
        except Exception as e:
            print(f"❌ 特徵提取測試失敗: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 3. 測試實際預測
        print(f"\n🎯 測試實際預測:")
        try:
            prediction_results = []
            
            for home_team, away_team in test_matchups:
                prediction = predictor.predict_game(home_team, away_team, date(2025, 6, 29))
                
                if prediction:
                    prediction_results.append({
                        'matchup': f"{away_team} @ {home_team}",
                        'home_score': prediction['home_score'],
                        'away_score': prediction['away_score'],
                        'total': prediction['home_score'] + prediction['away_score']
                    })
                    print(f"  {away_team} @ {home_team}: {prediction['away_score']:.1f} - {prediction['home_score']:.1f} (總分: {prediction['home_score'] + prediction['away_score']:.1f})")
                else:
                    print(f"  {away_team} @ {home_team}: ❌ 預測失敗")
            
            # 分析預測差異
            if len(prediction_results) >= 2:
                print(f"\n📊 預測差異分析:")
                home_scores = [r['home_score'] for r in prediction_results]
                away_scores = [r['away_score'] for r in prediction_results]
                totals = [r['total'] for r in prediction_results]
                
                print(f"主隊得分範圍: {min(home_scores):.1f} - {max(home_scores):.1f} (差距: {max(home_scores) - min(home_scores):.1f})")
                print(f"客隊得分範圍: {min(away_scores):.1f} - {max(away_scores):.1f} (差距: {max(away_scores) - min(away_scores):.1f})")
                print(f"總分範圍: {min(totals):.1f} - {max(totals):.1f} (差距: {max(totals) - min(totals):.1f})")
                
                # 判斷修復是否成功
                home_diff = max(home_scores) - min(home_scores)
                away_diff = max(away_scores) - min(away_scores)
                total_diff = max(totals) - min(totals)
                
                if home_diff > 0.5 or away_diff > 0.5 or total_diff > 1.0:
                    print("✅ 預測有合理差異 - 修復成功!")
                    print(f"   主隊差異: {home_diff:.1f}, 客隊差異: {away_diff:.1f}, 總分差異: {total_diff:.1f}")
                else:
                    print("❌ 預測差異仍然太小 - 需要進一步調查")
                    print(f"   主隊差異: {home_diff:.1f}, 客隊差異: {away_diff:.1f}, 總分差異: {total_diff:.1f}")
                    
                    # 檢查是否所有預測都相同
                    if len(set(f"{s:.1f}" for s in home_scores)) == 1:
                        print("⚠️  所有主隊預測都相同!")
                    if len(set(f"{s:.1f}" for s in away_scores)) == 1:
                        print("⚠️  所有客隊預測都相同!")
                        
        except Exception as e:
            print(f"❌ 預測測試失敗: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print(f"\n✅ 測試完成!")
        return True

if __name__ == "__main__":
    test_prediction_fix()
