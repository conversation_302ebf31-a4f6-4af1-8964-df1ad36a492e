#!/usr/bin/env python3
"""
最終boxscore數據驗證
確認所有修復工作都成功完成
"""

import sys
sys.path.append('.')

import sqlite3
from datetime import datetime, timed<PERSON><PERSON>

def final_verification():
    """最終驗證boxscore數據狀況"""
    
    print("🎯 最終boxscore數據驗證")
    print("=" * 60)
    
    try:
        # 連接數據庫
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        print("✅ 已連接數據庫: instance/mlb_data.db")
        
        # 1. 總體統計
        print(f"\n📊 總體數據統計:")
        
        cursor.execute("""
            SELECT 
                COUNT(*) as total_games,
                SUM(CASE WHEN game_status = 'completed' THEN 1 ELSE 0 END) as completed_games,
                SUM(CASE WHEN game_status = 'completed' AND home_score IS NOT NULL AND away_score IS NOT NULL THEN 1 ELSE 0 END) as complete_scores,
                SUM(CASE WHEN game_status = 'completed' AND (home_score IS NOT NULL OR away_score IS NOT NULL) THEN 1 ELSE 0 END) as any_scores
            FROM games
        """)
        
        stats = cursor.fetchone()
        total, completed, complete_scores, any_scores = stats
        
        if completed > 0:
            complete_rate = (complete_scores / completed * 100)
            partial_rate = (any_scores / completed * 100)
            
            print(f"   📋 總比賽數: {total:,}")
            print(f"   ✅ 已完成比賽: {completed:,}")
            print(f"   🎯 完整比分: {complete_scores:,} ({complete_rate:.1f}%)")
            print(f"   📊 部分比分: {any_scores:,} ({partial_rate:.1f}%)")
            print(f"   ❌ 完全缺失: {completed - any_scores:,}")
        
        # 2. 最近數據質量檢查
        print(f"\n📅 最近數據質量 (8月份):")
        
        cursor.execute("""
            SELECT 
                COUNT(*) as august_completed,
                SUM(CASE WHEN home_score IS NOT NULL AND away_score IS NOT NULL THEN 1 ELSE 0 END) as august_complete
            FROM games 
            WHERE date LIKE '2025-08-%' 
            AND game_status = 'completed'
        """)
        
        august_stats = cursor.fetchone()
        if august_stats:
            aug_total, aug_complete = august_stats
            if aug_total > 0:
                aug_rate = (aug_complete / aug_total * 100)
                print(f"   📊 8月已完成: {aug_total} 場")
                print(f"   ✅ 有完整比分: {aug_complete} 場 ({aug_rate:.1f}%)")
            else:
                print(f"   ⚠️ 8月沒有已完成的比賽")
        
        # 3. Box_scores表驗證
        print(f"\n📦 Box_scores表驗證:")
        
        cursor.execute("SELECT COUNT(*) FROM box_scores")
        total_box_scores = cursor.fetchone()[0]
        print(f"   📊 總box_scores記錄: {total_box_scores:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT game_id) FROM box_scores")
        unique_games_with_box = cursor.fetchone()[0]
        print(f"   🎯 有box_scores的比賽: {unique_games_with_box:,}")
        
        # 4. 最新比賽狀態
        print(f"\n🏟️ 最近比賽狀態 (前5場已完成):")
        
        cursor.execute("""
            SELECT game_id, date, home_team, away_team, home_score, away_score, game_status
            FROM games 
            WHERE game_status = 'completed'
            AND home_score IS NOT NULL 
            AND away_score IS NOT NULL
            ORDER BY date DESC, id DESC
            LIMIT 5
        """)
        
        recent_games = cursor.fetchall()
        for game in recent_games:
            game_id, date, home, away, h_score, a_score, status = game
            print(f"   🏟️ {date} | {away} {a_score}-{h_score} {home} ✅")
        
        # 5. 系統狀態評估
        print(f"\n🎯 系統狀態評估:")
        
        if complete_rate >= 90:
            print(f"   ✅ 系統狀態: 優秀 (完整比分覆蓋率 {complete_rate:.1f}%)")
        elif complete_rate >= 80:
            print(f"   ⚠️ 系統狀態: 良好 (完整比分覆蓋率 {complete_rate:.1f}%)")
        else:
            print(f"   ❌ 系統狀態: 需要改善 (完整比分覆蓋率 {complete_rate:.1f}%)")
        
        if partial_rate >= 95:
            print(f"   ✅ 數據完整性: 優秀 (部分比分覆蓋率 {partial_rate:.1f}%)")
        else:
            print(f"   ⚠️ 數據完整性: 需要關注")
        
        # 6. 建議後續動作
        print(f"\n💡 建議後續動作:")
        
        if complete_rate < 95:
            missing_complete = completed - complete_scores
            print(f"   🔧 還有 {missing_complete} 場比賽需要完整比分")
            print(f"   📡 建議定期運行boxscore修復程序")
        
        print(f"   🔄 建議啟用自動boxscore下載功能")
        print(f"   📊 建議設置數據品質監控")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        return False

if __name__ == "__main__":
    print("🎯 MLB Boxscore 數據最終驗證")
    print("確認所有修復工作的效果")
    print("")
    
    success = final_verification()
    
    if success:
        print(f"\n✅ 驗證完成")
        print(f"🎉 比賽結果更新系統已恢復正常運行")
    else:
        print(f"\n❌ 驗證失敗")