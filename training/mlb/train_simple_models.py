#!/usr/bin/env python3
"""
簡化的模型訓練腳本
專注於解決模型訓練不充分的問題
"""

import sys
import os
from datetime import date, timedelta
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
import joblib
import json

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import Game, TeamStats

def collect_simple_features(games):
    """收集簡化的特徵"""
    print("🔧 提取簡化特徵...")
    
    features_list = []
    targets_list = []
    
    for i, game in enumerate(games):
        if i % 100 == 0:
            print(f"處理進度: {i}/{len(games)}")
        
        try:
            # 獲取球隊統計
            home_stats = TeamStats.query.filter_by(team_id=game.home_team).first()
            away_stats = TeamStats.query.filter_by(team_id=game.away_team).first()
            
            if not home_stats or not away_stats:
                continue
            
            # 簡化特徵：只使用基本統計
            features = [
                # 主隊特徵
                home_stats.wins or 0,
                home_stats.losses or 0,
                home_stats.runs_scored or 0,
                home_stats.runs_allowed or 0,
                home_stats.batting_avg or 0.250,
                home_stats.era or 4.50,
                home_stats.home_wins or 0,
                home_stats.home_losses or 0,
                
                # 客隊特徵
                away_stats.wins or 0,
                away_stats.losses or 0,
                away_stats.runs_scored or 0,
                away_stats.runs_allowed or 0,
                away_stats.batting_avg or 0.250,
                away_stats.era or 4.50,
                away_stats.away_wins or 0,
                away_stats.away_losses or 0,
                
                # 對戰特徵
                (home_stats.runs_scored or 0) - (away_stats.runs_allowed or 0),
                (away_stats.runs_scored or 0) - (home_stats.runs_allowed or 0),
                (home_stats.batting_avg or 0.250) - (away_stats.era or 4.50) / 10,
                (away_stats.batting_avg or 0.250) - (home_stats.era or 4.50) / 10,
            ]
            
            # 確保所有特徵都是數值
            features = [float(f) if f is not None else 0.0 for f in features]
            
            if len(features) == 20:  # 確保特徵數量正確
                features_list.append(features)
                targets_list.append({
                    'home_score': game.home_score,
                    'away_score': game.away_score,
                    'home_win': 1 if game.home_score > game.away_score else 0
                })
        
        except Exception as e:
            print(f"處理比賽 {game.game_id} 時出錯: {e}")
            continue
    
    print(f"成功提取 {len(features_list)} 個訓練樣本")
    return np.array(features_list), targets_list

def train_simple_models(X, y_list):
    """訓練簡化模型"""
    print("🎯 開始訓練簡化模型...")
    
    # 準備目標變量
    y_home_score = np.array([y['home_score'] for y in y_list])
    y_away_score = np.array([y['away_score'] for y in y_list])
    y_home_win = np.array([y['home_win'] for y in y_list])
    
    # 分割訓練和測試數據
    X_train, X_test, y_home_train, y_home_test = train_test_split(
        X, y_home_score, test_size=0.2, random_state=42
    )
    _, _, y_away_train, y_away_test = train_test_split(
        X, y_away_score, test_size=0.2, random_state=42
    )
    _, _, y_win_train, y_win_test = train_test_split(
        X, y_home_win, test_size=0.2, random_state=42
    )
    
    # 特徵縮放
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    models = {}
    performance = {}
    
    # 1. 主隊得分模型
    print("訓練主隊得分模型...")
    home_model = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1
    )
    home_model.fit(X_train_scaled, y_home_train)
    
    y_home_pred = home_model.predict(X_test_scaled)
    home_mae = mean_absolute_error(y_home_test, y_home_pred)
    home_r2 = r2_score(y_home_test, y_home_pred)
    
    models['home_score'] = home_model
    performance['home_score'] = {'mae': home_mae, 'r2': home_r2}
    print(f"主隊得分模型 - MAE: {home_mae:.3f}, R²: {home_r2:.3f}")
    
    # 2. 客隊得分模型
    print("訓練客隊得分模型...")
    away_model = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1
    )
    away_model.fit(X_train_scaled, y_away_train)
    
    y_away_pred = away_model.predict(X_test_scaled)
    away_mae = mean_absolute_error(y_away_test, y_away_pred)
    away_r2 = r2_score(y_away_test, y_away_pred)
    
    models['away_score'] = away_model
    performance['away_score'] = {'mae': away_mae, 'r2': away_r2}
    print(f"客隊得分模型 - MAE: {away_mae:.3f}, R²: {away_r2:.3f}")
    
    # 3. 勝負預測模型
    print("訓練勝負預測模型...")
    win_model = RandomForestRegressor(
        n_estimators=100,
        max_depth=8,
        random_state=42,
        n_jobs=-1
    )
    win_model.fit(X_train_scaled, y_win_train)
    
    y_win_pred = win_model.predict(X_test_scaled)
    win_mae = mean_absolute_error(y_win_test, y_win_pred)
    win_r2 = r2_score(y_win_test, y_win_pred)
    
    models['win_probability'] = win_model
    performance['win_probability'] = {'mae': win_mae, 'r2': win_r2}
    print(f"勝負預測模型 - MAE: {win_mae:.3f}, R²: {win_r2:.3f}")
    
    return models, scaler, performance

def save_simple_models(models, scaler, performance):
    """保存簡化模型"""
    print("💾 保存模型...")
    
    model_dir = 'models/saved'
    os.makedirs(model_dir, exist_ok=True)
    
    # 保存模型
    for name, model in models.items():
        filename = f'{name}_model.joblib'
        joblib.dump(model, os.path.join(model_dir, filename))
        print(f"保存 {filename}")
    
    # 保存縮放器
    joblib.dump(scaler, os.path.join(model_dir, 'feature_scaler.joblib'))
    print("保存 feature_scaler.joblib")
    
    # 保存特徵列名（簡化版本）
    feature_names = [
        'home_wins', 'home_losses', 'home_runs_scored', 'home_runs_allowed',
        'home_batting_avg', 'home_era', 'home_home_wins', 'home_home_losses',
        'away_wins', 'away_losses', 'away_runs_scored', 'away_runs_allowed',
        'away_batting_avg', 'away_era', 'away_away_wins', 'away_away_losses',
        'home_offense_vs_away_defense', 'away_offense_vs_home_defense',
        'home_batting_vs_away_pitching', 'away_batting_vs_home_pitching'
    ]
    
    with open(os.path.join(model_dir, 'feature_columns.json'), 'w') as f:
        json.dump(feature_names, f)
    print("保存 feature_columns.json")
    
    # 保存性能指標
    with open(os.path.join(model_dir, 'model_performance.json'), 'w') as f:
        json.dump(performance, f, indent=2)
    print("保存 model_performance.json")

def test_simple_prediction():
    """測試簡化預測"""
    print("🧪 測試簡化預測...")
    
    try:
        # 直接載入模型進行測試
        model_dir = 'models/saved'
        
        home_model = joblib.load(os.path.join(model_dir, 'home_score_model.joblib'))
        away_model = joblib.load(os.path.join(model_dir, 'away_score_model.joblib'))
        win_model = joblib.load(os.path.join(model_dir, 'win_probability_model.joblib'))
        scaler = joblib.load(os.path.join(model_dir, 'feature_scaler.joblib'))
        
        # 創建測試特徵（模擬比賽）
        test_features = np.array([[
            50, 30, 400, 350, 0.270, 3.80, 25, 15,  # 主隊
            45, 35, 380, 370, 0.260, 4.20, 20, 20,  # 客隊
            50, 10, 0.02, -0.01  # 對戰特徵
        ]])
        
        test_features_scaled = scaler.transform(test_features)
        
        home_pred = home_model.predict(test_features_scaled)[0]
        away_pred = away_model.predict(test_features_scaled)[0]
        win_prob = win_model.predict(test_features_scaled)[0]
        
        print(f"測試預測結果:")
        print(f"  主隊得分: {home_pred:.1f}")
        print(f"  客隊得分: {away_pred:.1f}")
        print(f"  主隊勝率: {win_prob:.3f}")
        
        if 0 <= home_pred <= 20 and 0 <= away_pred <= 20 and 0 <= win_prob <= 1:
            print("✅ 預測結果合理")
            return True
        else:
            print("❌ 預測結果異常")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 開始簡化模型訓練")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 收集訓練數據
            end_date = date.today()
            start_date = end_date - timedelta(days=90)
            
            games = Game.query.filter(
                Game.date >= start_date,
                Game.date < end_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None),
                Game.game_status == 'completed'
            ).all()
            
            print(f"找到 {len(games)} 場已完成比賽")
            
            if len(games) < 100:
                print("❌ 訓練數據不足")
                return
            
            # 2. 提取簡化特徵
            X, y_list = collect_simple_features(games)
            
            if len(X) < 50:
                print("❌ 有效特徵數據不足")
                return
            
            print(f"特徵維度: {X.shape}")
            
            # 3. 訓練模型
            models, scaler, performance = train_simple_models(X, y_list)
            
            # 4. 保存模型
            save_simple_models(models, scaler, performance)
            
            # 5. 測試預測
            success = test_simple_prediction()
            
            if success:
                print("\n🎉 簡化模型訓練完成！")
                print("📈 性能指標:")
                for model_name, perf in performance.items():
                    print(f"  {model_name}: MAE={perf['mae']:.3f}, R²={perf['r2']:.3f}")
                
                print("\n💡 建議:")
                print("1. 重啟應用程序以載入新模型")
                print("2. 測試新模型的預測效果")
                print("3. 如果效果好，可以逐步增加特徵複雜度")
            else:
                print("\n❌ 模型訓練完成但測試失敗")
        
        except Exception as e:
            print(f"\n❌ 訓練過程中出錯: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
