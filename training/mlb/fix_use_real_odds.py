#!/usr/bin/env python3
"""
修復預測系統使用真實博彩盤口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from datetime import datetime, date

def update_predictions_with_real_odds():
    """更新預測記錄使用真實博彩盤口"""
    print("🔧 更新預測系統使用真實博彩盤口")
    print("=" * 60)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查找有真實博彩數據的比賽
        cursor.execute("""
            SELECT DISTINCT
                p.game_id,
                g.date,
                g.away_team || '@' || g.home_team as matchup,
                bo.total_point as real_line,
                bo.bookmaker,
                p.over_under_line as current_line
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            JOIN betting_odds bo ON p.game_id = bo.game_id
            WHERE bo.bookmaker IN ('covers.com', 'bet365')
            AND bo.market_type = 'totals'
            AND g.date >= '2025-07-01'
            ORDER BY g.date, g.game_id
        """)
        
        results = cursor.fetchall()
        
        if not results:
            print("❌ 沒有找到可更新的記錄")
            conn.close()
            return
        
        print(f"找到 {len(results)} 個可更新的記錄")
        
        updated_count = 0
        
        for game_id, date_str, matchup, real_line, bookmaker, current_line in results:
            # 更新預測記錄使用真實盤口
            cursor.execute("""
                UPDATE predictions 
                SET over_under_line = ?,
                    updated_at = ?
                WHERE game_id = ?
            """, (real_line, datetime.now().isoformat(), game_id))
            
            updated_count += 1
            print(f"✅ [{date_str}] {matchup}: 盤口 {current_line} -> {real_line} ({bookmaker})")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 成功更新 {updated_count} 個預測記錄使用真實博彩盤口")
        
    except Exception as e:
        print(f"❌ 更新失敗: {e}")
        import traceback
        traceback.print_exc()

def verify_real_odds_usage():
    """驗證真實博彩盤口的使用情況"""
    print("\n🔍 驗證真實博彩盤口使用情況")
    print("=" * 60)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 統計各日期的盤口使用情況
        cursor.execute("""
            SELECT 
                g.date,
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN p.over_under_line > 0 THEN 1 END) as with_real_odds,
                AVG(p.over_under_line) as avg_line,
                MIN(p.over_under_line) as min_line,
                MAX(p.over_under_line) as max_line
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-07-01'
            GROUP BY g.date
            ORDER BY g.date
        """)
        
        results = cursor.fetchall()
        
        print("\n📊 各日期盤口使用統計:")
        print("日期       | 總預測 | 真實盤口 | 平均盤口 | 盤口範圍")
        print("-" * 60)
        
        total_predictions = 0
        total_with_real_odds = 0
        
        for date_str, total, with_real, avg_line, min_line, max_line in results:
            total_predictions += total
            total_with_real_odds += with_real
            
            coverage = (with_real / total * 100) if total > 0 else 0
            status = "✅" if coverage > 80 else "⚠️" if coverage > 50 else "❌"
            
            print(f"{date_str} | {total:6d} | {with_real:8d} | {avg_line:8.1f} | {min_line:.1f}-{max_line:.1f} {status}")
        
        overall_coverage = (total_with_real_odds / total_predictions * 100) if total_predictions > 0 else 0
        print(f"\n📈 總體覆蓋率: {total_with_real_odds}/{total_predictions} ({overall_coverage:.1f}%)")
        
        # 檢查具體的真實vs估算對比
        cursor.execute("""
            SELECT 
                g.date,
                g.away_team || '@' || g.home_team as matchup,
                p.over_under_line as prediction_line,
                bo.total_point as real_line,
                bo.bookmaker
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            LEFT JOIN betting_odds bo ON p.game_id = bo.game_id 
                AND bo.bookmaker IN ('covers.com', 'bet365')
                AND bo.market_type = 'totals'
            WHERE g.date = '2025-07-08'
            ORDER BY g.game_id
        """)
        
        results = cursor.fetchall()
        
        print(f"\n🔍 2025-07-08 詳細對比:")
        for date_str, matchup, pred_line, real_line, bookmaker in results:
            if real_line:
                diff = abs(pred_line - real_line) if pred_line and real_line else 0
                status = "✅" if diff < 0.5 else "⚠️" if diff < 1.0 else "❌"
                print(f"  {matchup}: 預測 {pred_line} vs 真實 {real_line} ({bookmaker}) {status}")
            else:
                print(f"  {matchup}: 預測 {pred_line} vs 無真實數據 ❌")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")

def download_more_historical_odds():
    """下載更多歷史博彩數據"""
    print("\n📥 下載更多歷史博彩數據")
    print("=" * 60)
    
    try:
        from models.sportsbookreview_scraper import SportsBookReviewScraper
        
        scraper = SportsBookReviewScraper()
        
        # 下載2025-07-07到2025-07-10的數據
        target_dates = [
            date(2025, 7, 7),
            date(2025, 7, 8),
            date(2025, 7, 9),
            date(2025, 7, 10)
        ]
        
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.join(basedir, "instance", "mlb_data.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        total_saved = 0
        
        for target_date in target_dates:
            print(f"\n📅 下載 {target_date} 的數據...")
            
            try:
                historical_data = scraper.fetch_historical_odds(target_date, bookmaker='covers.com')
                
                if historical_data and historical_data.get('games'):
                    games = historical_data.get('games', [])
                    print(f"   獲取到 {len(games)} 場比賽")
                    
                    saved_count = 0
                    for game in games:
                        home_team = game.get('home_team', '')
                        away_team = game.get('away_team', '')
                        
                        if not home_team or not away_team:
                            continue
                        
                        # 查找對應的game_id
                        cursor.execute("""
                            SELECT game_id FROM games 
                            WHERE away_team = ? AND home_team = ? AND date = ?
                        """, (away_team, home_team, str(target_date)))
                        
                        result = cursor.fetchone()
                        if not result:
                            continue
                        
                        game_id = result[0]
                        
                        # 保存總分盤數據
                        totals = game.get('totals', {})
                        if totals and totals.get('line'):
                            try:
                                total_point = float(totals.get('line'))
                                
                                cursor.execute("""
                                    INSERT OR REPLACE INTO betting_odds 
                                    (game_id, bookmaker, market_type, total_point, odds_time, created_at, updated_at)
                                    VALUES (?, ?, 'totals', ?, ?, ?, ?)
                                """, (
                                    game_id,
                                    'covers.com',
                                    total_point,
                                    datetime.now().isoformat(),
                                    datetime.now().isoformat(),
                                    datetime.now().isoformat()
                                ))
                                saved_count += 1
                                
                            except ValueError:
                                pass
                    
                    print(f"   💾 保存 {saved_count} 個盤口")
                    total_saved += saved_count
                else:
                    print(f"   ❌ 沒有數據")
                
                # 避免請求過快
                import time
                time.sleep(3)
                
            except Exception as e:
                print(f"   ❌ 下載失敗: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 總共下載並保存了 {total_saved} 個歷史博彩盤口")
        
    except Exception as e:
        print(f"❌ 下載失敗: {e}")

if __name__ == '__main__':
    download_more_historical_odds()
    update_predictions_with_real_odds()
    verify_real_odds_usage()
