#!/usr/bin/env python3
"""
使用直接SQL創建先發投手記錄表
"""

import sqlite3
import os

def create_starting_pitcher_table_sql():
    """使用SQL直接創建先發投手記錄表"""
    db_path = 'instance/mlb_data.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 數據庫文件不存在: {db_path}")
        return
    
    try:
        print("🏗️ 使用SQL創建先發投手記錄表...")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表是否已存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='starting_pitcher_records'
        """)
        
        if cursor.fetchone():
            print("⚠️ 表 'starting_pitcher_records' 已存在")
        else:
            # 創建表
            create_table_sql = """
            CREATE TABLE starting_pitcher_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id VARCHAR(50) NOT NULL,
                game_date DATE NOT NULL,
                home_team VARCHAR(10) NOT NULL,
                away_team VARCHAR(10) NOT NULL,
                home_starting_pitcher VARCHAR(100),
                away_starting_pitcher VARCHAR(100),
                home_pitcher_id INTEGER,
                away_pitcher_id INTEGER,
                data_source VARCHAR(50) NOT NULL,
                confidence_level VARCHAR(20) DEFAULT 'medium',
                is_verified BOOLEAN DEFAULT 0,
                verification_source VARCHAR(50),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(game_id) REFERENCES games(game_id)
            )
            """
            
            cursor.execute(create_table_sql)
            
            # 創建索引
            cursor.execute("CREATE INDEX idx_spr_game_id ON starting_pitcher_records(game_id)")
            cursor.execute("CREATE INDEX idx_spr_game_date ON starting_pitcher_records(game_date)")
            
            conn.commit()
            print("✅ 先發投手記錄表創建成功！")
        
        # 檢查表結構
        cursor.execute("PRAGMA table_info(starting_pitcher_records)")
        columns = cursor.fetchall()
        
        print(f"\n📋 表結構 (共 {len(columns)} 個欄位):")
        for col in columns:
            col_id, name, col_type, not_null, default, pk = col
            print(f"  - {name} ({col_type})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 創建表失敗: {e}")

if __name__ == "__main__":
    create_starting_pitcher_table_sql()
