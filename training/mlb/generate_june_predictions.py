#!/usr/bin/env python3
"""
專門生成6月的預測數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from datetime import datetime, date, <PERSON><PERSON><PERSON>

def generate_june_predictions():
    """生成6月的預測數據"""
    print("🔄 生成6月的預測數據")
    print("=" * 70)
    
    from models.unified_betting_predictor import UnifiedBettingPredictor
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        # 獲取6月所有有真實博彩盤口的比賽
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT DISTINCT 
                g.game_id, 
                g.date, 
                g.home_team, 
                g.away_team,
                bo.total_point,
                g.home_score,
                g.away_score
            FROM games g
            JOIN betting_odds bo ON g.game_id = bo.game_id
            LEFT JOIN predictions p ON g.game_id = p.game_id 
                AND p.model_version = 'unified_v1.0'
            WHERE bo.market_type = 'totals'
            AND bo.bookmaker = 'bet365'
            AND g.date >= '2025-06-01' 
            AND g.date < '2025-07-01'
            AND p.game_id IS NULL
            ORDER BY g.date, g.game_id
        """)
        
        june_games = cursor.fetchall()
        conn.close()
        
        print(f"   找到 {len(june_games)} 場6月需要生成預測的比賽")
        
        if len(june_games) == 0:
            print("   ✅ 6月所有比賽都已有預測")
            return
        
        predictor = UnifiedBettingPredictor()
        
        # 按日期分組處理
        games_by_date = {}
        for game_data in june_games:
            game_date = game_data[1]
            if game_date not in games_by_date:
                games_by_date[game_date] = []
            games_by_date[game_date].append(game_data)
        
        total_generated = 0
        
        for game_date_str in sorted(games_by_date.keys()):
            games = games_by_date[game_date_str]
            game_date = datetime.strptime(game_date_str, '%Y-%m-%d').date()
            training_end_date = game_date - timedelta(days=1)
            
            print(f"\n📅 處理日期: {game_date} ({len(games)} 場比賽)")
            
            for game_id, _, home_team, away_team, real_line, home_score, away_score in games:
                try:
                    # 生成預測
                    result = predictor.predict_game_comprehensive(
                        game_id,
                        target_date=game_date,
                        training_end_date=training_end_date
                    )
                    
                    if result:
                        total_generated += 1
                        print(f"   ✅ {away_team}@{home_team}: 盤口 {real_line}")
                    else:
                        print(f"   ❌ {away_team}@{home_team}: 預測失敗")
                        
                except Exception as e:
                    print(f"   ⚠️  {away_team}@{home_team} 預測失敗: {e}")
                    continue
        
        print(f"\n✅ 6月預測生成完成: {total_generated} 個新預測")
        
    except Exception as e:
        print(f"❌ 生成6月預測失敗: {e}")
        import traceback
        traceback.print_exc()

def check_june_coverage():
    """檢查6月數據覆蓋情況"""
    print("\n📊 檢查6月數據覆蓋情況")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查6月每日的數據情況
        cursor.execute("""
            SELECT 
                g.date,
                COUNT(DISTINCT g.game_id) as total_games,
                COUNT(DISTINCT bo.game_id) as games_with_odds,
                COUNT(DISTINCT p.game_id) as games_with_predictions,
                COUNT(CASE WHEN g.home_score IS NOT NULL THEN 1 END) as completed_games
            FROM games g
            LEFT JOIN betting_odds bo ON g.game_id = bo.game_id 
                AND bo.market_type = 'totals' AND bo.bookmaker = 'bet365'
            LEFT JOIN predictions p ON g.game_id = p.game_id 
                AND p.model_version = 'unified_v1.0'
            WHERE g.date >= '2025-06-01' AND g.date < '2025-07-01'
            GROUP BY g.date
            ORDER BY g.date
        """)
        
        results = cursor.fetchall()
        
        print("日期       | 總比賽 | 有盤口 | 有預測 | 已完成 | 覆蓋率")
        print("-" * 60)
        
        total_games = 0
        total_with_odds = 0
        total_with_predictions = 0
        total_completed = 0
        
        for date_str, games, with_odds, with_predictions, completed in results:
            coverage = (with_predictions / with_odds * 100) if with_odds > 0 else 0
            
            print(f"{date_str} | {games:6d} | {with_odds:6d} | {with_predictions:6d} | {completed:6d} | {coverage:6.1f}%")
            
            total_games += games
            total_with_odds += with_odds
            total_with_predictions += with_predictions
            total_completed += completed
        
        overall_coverage = (total_with_predictions / total_with_odds * 100) if total_with_odds > 0 else 0
        
        print("-" * 60)
        print(f"總計       | {total_games:6d} | {total_with_odds:6d} | {total_with_predictions:6d} | {total_completed:6d} | {overall_coverage:6.1f}%")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

def final_june_summary():
    """6月數據最終總結"""
    print("\n📈 6月數據最終總結")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 6月整體統計
        cursor.execute("""
            SELECT 
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN p.is_correct = 1 THEN 1 END) as correct,
                COUNT(CASE WHEN p.is_correct IS NOT NULL THEN 1 END) as with_results,
                AVG(p.over_under_line) as avg_line,
                COUNT(CASE WHEN p.actual_total_runs > p.over_under_line THEN 1 END) as actual_overs,
                COUNT(CASE WHEN p.over_probability > 0.5 THEN 1 END) as predicted_overs
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-06-01' AND g.date < '2025-07-01'
            AND p.model_version = 'unified_v1.0'
        """)
        
        result = cursor.fetchone()
        
        if result and result[0] > 0:
            total, correct, with_results, avg_line, actual_overs, predicted_overs = result
            accuracy = (correct / with_results * 100) if with_results > 0 else 0
            
            print(f"📊 6月預測統計:")
            print(f"   總預測數: {total}")
            print(f"   有結果數: {with_results}")
            print(f"   正確預測: {correct}")
            print(f"   準確率: {accuracy:.1f}%")
            print(f"   平均盤口: {avg_line:.1f}")
            print(f"   實際Over: {actual_overs}")
            print(f"   預測Over: {predicted_overs}")
            
            # Over/Under 分析
            over_accuracy = actual_overs / with_results * 100 if with_results > 0 else 0
            print(f"   實際Over率: {over_accuracy:.1f}%")
            
        else:
            print("❌ 6月沒有預測數據")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 總結失敗: {e}")

if __name__ == '__main__':
    check_june_coverage()
    generate_june_predictions()
    final_june_summary()
    
    print("\n" + "=" * 70)
    print("🎯 6月預測數據生成完成！")
    print("現在有完整的6月到7/10預測數據可用於分析")
    print("=" * 70)
