#!/usr/bin/env python3
"""
快速預測視圖
優先從數據庫獲取數據，避免網絡請求，提高預測速度
"""

from flask import Blueprint, render_template, request, jsonify, current_app
from datetime import datetime, timedelta
import time
import logging
from models.database import db, Game, Prediction
from models.fast_intelligent_predictor import fast_intelligent_predictor

# 創建藍圖
bp = Blueprint('fast_prediction', __name__, url_prefix='/fast')
logger = logging.getLogger(__name__)

@bp.route('/')
def index():
    """快速預測首頁"""
    return render_template('fast_prediction/dashboard.html')

@bp.route('/api/predict/<game_id>')
def predict_game(game_id):
    """快速預測單場比賽"""
    try:
        start_time = time.time()
        
        # 從數據庫獲取比賽信息
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({'success': False, 'error': f'找不到比賽 ID: {game_id}'})
        
        # 執行快速智能預測
        prediction = fast_intelligent_predictor.predict_game_intelligent_fast(
            home_team=game.home_team,
            away_team=game.away_team,
            game_date=game.date,
            game_id=game.game_id
        )
        
        # 添加比賽信息
        prediction['game_id'] = game.game_id
        prediction['home_team'] = game.home_team
        prediction['away_team'] = game.away_team
        prediction['date'] = game.date.isoformat()
        prediction['time'] = game.time
        prediction['venue'] = game.venue
        prediction['game_status'] = game.game_status
        
        # 添加實際結果（如果有）
        if game.home_score is not None and game.away_score is not None:
            prediction['actual_home_score'] = game.home_score
            prediction['actual_away_score'] = game.away_score
            prediction['actual_total'] = game.home_score + game.away_score
        
        # 計算預測時間
        elapsed_time = time.time() - start_time
        prediction['prediction_time'] = round(elapsed_time, 2)
        
        return jsonify({
            'success': True,
            'prediction': prediction,
            'elapsed_time': round(elapsed_time, 2)
        })
        
    except Exception as e:
        logger.error(f"快速預測失敗: {e}", exc_info=True)
        return jsonify({'success': False, 'error': f'預測失敗: {str(e)}'})

@bp.route('/api/predict/date/<date_str>')
def predict_date(date_str):
    """快速預測指定日期的所有比賽"""
    try:
        start_time = time.time()
        
        # 解析日期
        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'error': '日期格式無效，請使用YYYY-MM-DD格式'})
        
        # 從數據庫獲取該日期的比賽
        games = Game.query.filter_by(date=target_date).all()
        if not games:
            return jsonify({'success': False, 'error': f'找不到{date_str}的比賽'})
        
        # 排除延期/取消的比賽
        exclude_postponed = request.args.get('exclude_postponed', 'true').lower() == 'true'
        if exclude_postponed:
            games = [g for g in games if g.game_status not in ['postponed', 'cancelled', 'rescheduled']]
        
        # 執行快速預測
        predictions = []
        for game in games:
            try:
                # 執行快速智能預測
                prediction = fast_intelligent_predictor.predict_game_intelligent_fast(
                    home_team=game.home_team,
                    away_team=game.away_team,
                    game_date=game.date,
                    game_id=game.game_id
                )
                
                # 添加比賽信息
                prediction['game_id'] = game.game_id
                prediction['home_team'] = game.home_team
                prediction['away_team'] = game.away_team
                prediction['date'] = game.date.isoformat()
                prediction['time'] = game.time
                prediction['venue'] = game.venue
                prediction['game_status'] = game.game_status
                
                # 添加實際結果（如果有）
                if game.home_score is not None and game.away_score is not None:
                    prediction['actual_home_score'] = game.home_score
                    prediction['actual_away_score'] = game.away_score
                    prediction['actual_total'] = game.home_score + game.away_score
                
                predictions.append(prediction)
                
            except Exception as e:
                logger.error(f"預測比賽 {game.game_id} 失敗: {e}")
                # 繼續處理下一場比賽
        
        # 計算總耗時
        elapsed_time = time.time() - start_time
        
        return jsonify({
            'success': True,
            'date': date_str,
            'predictions': predictions,
            'count': len(predictions),
            'elapsed_time': round(elapsed_time, 2)
        })
        
    except Exception as e:
        logger.error(f"快速預測日期失敗: {e}", exc_info=True)
        return jsonify({'success': False, 'error': f'預測失敗: {str(e)}'})

@bp.route('/api/batch_predict', methods=['POST'])
def batch_predict():
    """批量快速預測"""
    try:
        start_time = time.time()
        data = request.json
        
        if not data or 'date_range' not in data:
            return jsonify({'success': False, 'error': '缺少日期範圍參數'})
        
        date_range = data['date_range']
        start_date_str = date_range.get('start')
        end_date_str = date_range.get('end')
        
        if not start_date_str or not end_date_str:
            return jsonify({'success': False, 'error': '缺少開始或結束日期'})
        
        # 解析日期
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'error': '日期格式無效，請使用YYYY-MM-DD格式'})
        
        # 檢查日期範圍
        if (end_date - start_date).days > 30:
            return jsonify({'success': False, 'error': '日期範圍不能超過30天'})
        
        # 獲取日期範圍內的所有比賽
        games = Game.query.filter(Game.date >= start_date, Game.date <= end_date).all()
        
        # 排除延期/取消的比賽
        exclude_postponed = data.get('exclude_postponed', True)
        if exclude_postponed:
            games = [g for g in games if g.game_status not in ['postponed', 'cancelled', 'rescheduled']]
        
        # 執行快速預測
        results = []
        for game in games:
            try:
                # 執行快速智能預測
                prediction = fast_intelligent_predictor.predict_game_intelligent_fast(
                    home_team=game.home_team,
                    away_team=game.away_team,
                    game_date=game.date,
                    game_id=game.game_id
                )
                
                # 添加比賽信息
                prediction['game_id'] = game.game_id
                prediction['home_team'] = game.home_team
                prediction['away_team'] = game.away_team
                prediction['date'] = game.date.isoformat()
                
                # 添加實際結果（如果有）
                if game.home_score is not None and game.away_score is not None:
                    prediction['actual_home_score'] = game.home_score
                    prediction['actual_away_score'] = game.away_score
                    prediction['actual_total'] = game.home_score + game.away_score
                
                results.append(prediction)
                
            except Exception as e:
                logger.error(f"批量預測比賽 {game.game_id} 失敗: {e}")
                # 繼續處理下一場比賽
        
        # 計算總耗時
        elapsed_time = time.time() - start_time
        
        return jsonify({
            'success': True,
            'date_range': {'start': start_date_str, 'end': end_date_str},
            'predictions': results,
            'count': len(results),
            'elapsed_time': round(elapsed_time, 2)
        })
        
    except Exception as e:
        logger.error(f"批量快速預測失敗: {e}", exc_info=True)
        return jsonify({'success': False, 'error': f'批量預測失敗: {str(e)}'})

# 註冊藍圖
def register_blueprint(app):
    app.register_blueprint(bp)
    logger.info("快速預測藍圖已註冊")
