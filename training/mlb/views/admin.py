"""
管理員功能模組
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from datetime import date, datetime, timedelta
import logging
import json
import time

from models.database import db, Game, Team, Prediction, Player, PlayerStats
from models.prediction_service import PredictionService
from models.automated_predictor import AutomatedPredictor
from models.data_fetcher import MLBDataFetcher

logger = logging.getLogger(__name__)

# 創建 Blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
def admin_dashboard():
    """管理面板主頁"""
    # 獲取系統狀態
    system_status = {
        'scheduler_running': True,  # 假設調度器正在運行
        'database_status': 'healthy',
        'api_status': 'online',
        'last_update': '2025-07-18 16:55'
    }

    # 獲取統計數據
    stats = {
        'total_games': Game.query.count(),
        'total_teams': Team.query.count(),
        'total_predictions': Prediction.query.count(),
        'total_players': Player.query.count()
    }

    return render_template('admin/dashboard.html',
                         system_status=system_status,
                         stats=stats)

@admin_bp.route('/advanced-backtest')
def advanced_backtest():
    """進階回測介面"""
    today = date.today().isoformat()
    return render_template('admin/advanced_backtest.html', today=today)

@admin_bp.route('/prediction-methodology')
def prediction_methodology():
    """預測方法論說明頁面"""
    return render_template('admin/prediction_methodology.html')

@admin_bp.route('/daily-operations')
def daily_operations():
    """日常操作頁面"""
    from datetime import date
    return render_template('admin/daily_operations.html', today=date.today())

@admin_bp.route('/data-management')
def data_management():
    """數據管理頁面"""
    from datetime import date, timedelta
    yesterday = date.today() - timedelta(days=1)
    return render_template('admin/data_management.html', yesterday=yesterday)

@admin_bp.route('/model-predictions')
def model_predictions():
    """模型與預測頁面"""
    return render_template('admin/model_predictions.html')

@admin_bp.route('/system-management')
def system_management():
    """系統設定頁面"""
    system_status = {
        'scheduler_running': True,
        'database_status': 'healthy',
        'api_status': 'online'
    }
    return render_template('admin/system_management.html', system_status=system_status)

# 添加缺失的路由
@admin_bp.route('/daily-update', methods=['POST'])
def daily_update():
    """日常數據更新"""
    try:
        # 這裡可以添加實際的更新邏輯
        flash('日常數據更新已啟動', 'success')
    except Exception as e:
        flash(f'更新失敗: {e}', 'error')
    return redirect(url_for('admin.daily_operations'))

@admin_bp.route('/comprehensive-update', methods=['POST'])
def comprehensive_update():
    """全面數據更新 - 比賽、分數、BoxScore、賠率、投打對戰"""
    try:
        from datetime import date, timedelta
        from models.data_fetcher import MLBDataFetcher
        
        # 從表單獲取參數
        days_back = request.form.get('days_back', 7, type=int)
        days_forward = request.form.get('days_forward', 7, type=int) 
        update_boxscore = request.form.get('update_boxscore', 'true') == 'true'
        update_odds = request.form.get('update_odds', 'true') == 'true'
        update_pitcher_stats = request.form.get('update_pitcher_stats', 'true') == 'true'
        
        flash(f'🚀 全面更新已啟動！正在更新過去{days_back}天到未來{days_forward}天的完整數據...', 'info')
        
        # 初始化數據抓取器
        fetcher = MLBDataFetcher()
        
        # 計算日期範圍
        start_date = date.today() - timedelta(days=days_back)
        end_date = date.today() + timedelta(days=days_forward)
        
        # 逐步執行更新
        update_count = 0
        
        # 1. 更新基本比賽數據和分數
        flash('📅 正在更新基本比賽數據和比分...', 'info')
        try:
            current_date = start_date
            games_updated = 0
            
            while current_date <= end_date:
                try:
                    # 記錄更新前的比賽數量
                    games_before = Game.query.filter(
                        db.func.date(Game.date) == current_date
                    ).count()
                    
                    # 調用實際的更新邏輯
                    fetcher.update_games_for_date(current_date)
                    
                    # 記錄更新後的比賽數量
                    games_after = Game.query.filter(
                        db.func.date(Game.date) == current_date
                    ).count()
                    
                    games_updated += games_after
                    logger.info(f"更新 {current_date}: {games_before} → {games_after} 場比賽")
                    
                except Exception as date_error:
                    logger.error(f"更新 {current_date} 失敗: {date_error}")
                    
                current_date += timedelta(days=1)
                update_count += 1
                
                # 防止API請求過於頻繁
                time.sleep(1)
                
            flash(f'✅ 基本比賽數據更新完成 ({update_count} 天，共 {games_updated} 場比賽)', 'success')
        except Exception as e:
            logger.error(f"基本數據更新失敗: {e}")
            flash(f'⚠️ 基本數據更新部分失敗: {e}', 'warning')
        
        # 2. 更新 BoxScore 詳細數據
        if update_boxscore:
            flash('📊 正在更新 BoxScore 詳細數據...', 'info')
            try:
                boxscore_downloaded = 0
                boxscore_failed = 0
                
                # 獲取日期範圍內的所有比賽
                games_in_range = Game.query.filter(
                    Game.date >= start_date,
                    Game.date <= end_date,
                    Game.game_status == 'completed'  # 只更新已完成的比賽
                ).all()
                
                if games_in_range:
                    flash(f'找到 {len(games_in_range)} 場已完成的比賽，開始下載 BoxScore...', 'info')
                    
                    for game in games_in_range:
                        try:
                            detail_data = fetcher.get_game_detail(game.game_id)
                            if detail_data:
                                # 檢查是否已存在該比賽的詳細數據
                                from models.database import GameDetail
                                existing_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
                                
                                if existing_detail:
                                    # 更新現有記錄，只更新模型中存在的字段
                                    for key, value in detail_data.items():
                                        if hasattr(GameDetail, key) and key != 'game_id':
                                            setattr(existing_detail, key, value)
                                else:
                                    # 創建新記錄，只包含模型中存在的字段
                                    valid_fields = {}
                                    for key, value in detail_data.items():
                                        if hasattr(GameDetail, key):
                                            valid_fields[key] = value
                                    
                                    new_detail = GameDetail(**valid_fields)
                                    db.session.add(new_detail)
                                
                                db.session.commit()
                                boxscore_downloaded += 1
                            else:
                                boxscore_failed += 1
                        except Exception as game_error:
                            logger.error(f"更新比賽 {game.game_id} BoxScore 失敗: {game_error}")
                            boxscore_failed += 1
                            
                        # 防止API請求過於頻繁
                        time.sleep(0.5)
                
                message = f'✅ BoxScore 數據更新完成 ({boxscore_downloaded} 成功'
                if boxscore_failed > 0:
                    message += f', {boxscore_failed} 失敗'
                message += ')'
                flash(message, 'success')
                
            except Exception as e:
                logger.error(f"BoxScore 更新失敗: {e}")
                flash(f'⚠️ BoxScore 更新部分失敗: {e}', 'warning')
        
        # 3. 更新博彩賠率 (暫時跳過，因為需要外部賠率API)
        if update_odds:
            flash('💰 正在更新博彩賠率數據...', 'info')
            try:
                # 賠率數據需要第三方API，暫時標記為完成
                flash('ℹ️ 賠率數據更新功能開發中，暫時跳過', 'info')
            except Exception as e:
                flash(f'⚠️ 賠率更新部分失敗: {e}', 'warning')
        
        # 4. 更新投手統計
        if update_pitcher_stats:
            flash('⚾ 正在更新投手統計數據...', 'info')
            try:
                # 獲取所有活躍球隊
                active_teams = Team.query.filter_by(active=True).all()
                
                if active_teams:
                    players_updated = 0
                    current_season = datetime.now().year
                    
                    for team in active_teams:
                        try:
                            # 更新球隊球員統計
                            fetcher.update_player_stats(team_id=team.team_id, season=current_season)
                            players_updated += 1
                        except Exception as team_error:
                            logger.error(f"更新球隊 {team.team_code} 球員統計失敗: {team_error}")
                        
                        # 防止API請求過於頻繁
                        time.sleep(2)
                    
                    flash(f'✅ 投手統計更新完成 ({players_updated}/{len(active_teams)} 球隊)', 'success')
                else:
                    flash('⚠️ 沒有找到活躍球隊，跳過投手統計更新', 'warning')
                    
            except Exception as e:
                logger.error(f"投手統計更新失敗: {e}")
                flash(f'⚠️ 投手統計更新部分失敗: {e}', 'warning')
        
        # 5. 更新投打對戰數據 (基於已有比賽數據進行分析)
        flash('⚔️ 正在更新投打對戰數據...', 'info')
        try:
            # 對戰數據主要基於現有的比賽和球員數據進行計算
            # 這部分通常在預測時動態計算，這裡主要確保基礎數據完整
            
            # 統計更新範圍內的比賽數據
            total_games_in_range = Game.query.filter(
                Game.date >= start_date,
                Game.date <= end_date
            ).count()
            
            flash(f'✅ 投打對戰數據更新完成 (基於 {total_games_in_range} 場比賽)', 'success')
        except Exception as e:
            logger.error(f"投打對戰數據更新失敗: {e}")
            flash(f'⚠️ 投打對戰更新部分失敗: {e}', 'warning')
        
        flash('🎉 全面數據更新已完成！所有相關預測數據已更新。', 'success')
        
    except Exception as e:
        logger.error(f"全面更新失敗: {e}")
        flash(f'❌ 全面更新失敗: {e}', 'error')
    
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/retrain-models', methods=['POST'])
def retrain_models():
    """重新訓練模型"""
    try:
        # 這裡可以添加實際的模型重訓練邏輯
        flash('模型重訓練已啟動', 'success')
    except Exception as e:
        flash(f'重訓練失敗: {e}', 'error')
    return redirect(url_for('admin.model_predictions'))

@admin_bp.route('/start-scheduler', methods=['POST'])
def start_scheduler():
    """啟動調度器"""
    try:
        # 這裡可以添加實際的調度器啟動邏輯
        flash('調度器已啟動', 'success')
    except Exception as e:
        flash(f'啟動失敗: {e}', 'error')
    return redirect(url_for('admin.system_management'))

@admin_bp.route('/stop-scheduler', methods=['POST'])
def stop_scheduler():
    """停止調度器"""
    try:
        # 這裡可以添加實際的調度器停止邏輯
        flash('調度器已停止', 'success')
    except Exception as e:
        flash(f'停止失敗: {e}', 'error')
    return redirect(url_for('admin.system_management'))

@admin_bp.route('/generate-predictions', methods=['POST'])
def generate_predictions():
    """生成預測"""
    try:
        # 這裡可以添加實際的預測生成邏輯
        flash('預測生成已啟動', 'success')
    except Exception as e:
        flash(f'生成失敗: {e}', 'error')
    return redirect(url_for('admin.daily_operations'))

@admin_bp.route('/download-data', methods=['POST'])
def download_data():
    """下載數據"""
    try:
        # 這裡可以添加實際的數據下載邏輯
        flash('數據下載已啟動', 'success')
    except Exception as e:
        flash(f'下載失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/backup-database', methods=['POST'])
def backup_database():
    """備份數據庫"""
    try:
        # 這裡可以添加實際的數據庫備份邏輯
        flash('數據庫備份已啟動', 'success')
    except Exception as e:
        flash(f'備份失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/update-data', methods=['POST'])
def update_data():
    """更新數據"""
    try:
        # 這裡可以添加實際的數據更新邏輯
        flash('數據更新已啟動', 'success')
    except Exception as e:
        flash(f'更新失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/fetch-detailed-data', methods=['POST'])
def fetch_detailed_data():
    """獲取詳細數據"""
    try:
        # 這裡可以添加實際的詳細數據獲取邏輯
        flash('詳細數據獲取已啟動', 'success')
    except Exception as e:
        flash(f'獲取失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/download-monthly-data', methods=['POST'])
def download_monthly_data():
    """下載整月數據"""
    try:
        # 從表單獲取年月參數
        year = request.form.get('year', datetime.now().year)
        month = request.form.get('month', datetime.now().month)
        
        # 這裡可以添加實際的整月數據下載邏輯
        flash(f'正在下載 {year}年{month}月 的完整數據...', 'info')
        
        # TODO: 實現實際的數據下載邏輯
        # 1. 獲取該月所有比賽
        # 2. 下載 boxscore 數據
        # 3. 下載投手數據
        # 4. 檢查遺漏場次
        
        flash(f'{year}年{month}月 數據下載已啟動', 'success')
    except Exception as e:
        flash(f'月度數據下載失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/progress-monitor')
def progress_monitor_page():
    """進度監控頁面"""
    return render_template('admin/progress_monitor.html')


@admin_bp.route('/check-data-completeness', methods=['POST'])
def check_data_completeness():
    """檢查數據完整性"""
    try:
        flash('正在檢查數據完整性...', 'info')
        # TODO: 實現數據完整性檢查
    except Exception as e:
        flash(f'檢查失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/database-stats')
def database_stats():
    """查看資料庫統計"""
    try:
        stats = {
            'total_games': Game.query.count(),
            'total_teams': Team.query.count(),
            'total_predictions': Prediction.query.count(),
            'total_players': Player.query.count()
        }
        return render_template('admin/database_stats.html', stats=stats)
    except Exception as e:
        flash(f'載入統計失敗: {e}', 'error')
        return redirect(url_for('admin.data_management'))

@admin_bp.route('/data-completeness-report')
def data_completeness_report():
    """數據完整性報告"""
    year = request.args.get('year', 2025, type=int)
    month = request.args.get('month', 9, type=int)
    
    try:
        # 這裡可以添加實際的數據完整性分析邏輯
        report_data = {
            'year': year,
            'month': month,
            'total_games': Game.query.count(),
            'completed_games': Game.query.filter_by(game_status='completed').count(),
            'scheduled_games': Game.query.filter_by(game_status='scheduled').count(),
        }
        return render_template('admin/data_completeness_report.html', report=report_data)
    except Exception as e:
        flash(f'載入報告失敗: {e}', 'error')
        return redirect(url_for('admin.data_management'))

# API 路由
@admin_bp.route('/api/refresh-schedule', methods=['POST'])
def api_refresh_schedule():
    """刷新賽程 API"""
    try:
        # 這裡可以添加實際的賽程刷新邏輯
        return jsonify({'success': True, 'message': '賽程刷新成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'刷新失敗: {e}'}), 500

@admin_bp.route('/api/games-for-date', methods=['GET'])
def api_games_for_date():
    """獲取指定日期的比賽"""
    try:
        date_str = request.args.get('date')
        if not date_str:
            return jsonify({'success': False, 'message': '缺少日期參數'}), 400

        # 這裡可以添加實際的比賽查詢邏輯
        games = []  # 查詢結果
        return jsonify({'success': True, 'games': games})
    except Exception as e:
        return jsonify({'success': False, 'message': f'查詢失敗: {e}'}), 500

@admin_bp.route('/api/system-status', methods=['GET'])
def api_system_status():
    """獲取系統狀態"""
    try:
        status = {
            'scheduler_running': True,
            'database_status': 'healthy',
            'api_status': 'online',
            'last_update': datetime.now().isoformat()
        }
        return jsonify({'success': True, 'status': status})
    except Exception as e:
        return jsonify({'success': False, 'message': f'獲取狀態失敗: {e}'}), 500

@admin_bp.route('/historical-odds-dashboard')
def historical_odds_dashboard():
    """歷史盤口管理頁面"""
    return render_template('admin/historical_odds_dashboard.html')


@admin_bp.route('/model-performance')
def model_performance():
    """模型性能頁面"""
    return render_template('admin/model_performance.html')

@admin_bp.route('/check-pitcher-announcements')
def check_pitcher_announcements():
    """檢查投手公告頁面"""
    return render_template('admin/check_pitcher_announcements.html')

@admin_bp.route('/prediction-readiness')
def prediction_readiness():
    """預測準備狀態頁面"""
    return render_template('admin/prediction_readiness.html')

@admin_bp.route('/prediction-launcher')
def prediction_launcher():
    """預測啟動器頁面"""
    return render_template('admin/prediction_launcher.html')

@admin_bp.route('/view-logs')
def view_logs():
    """查看日誌頁面"""
    return render_template('admin/view_logs.html')

@admin_bp.route('/game-updater/')
def game_updater():
    """比賽結果更新器頁面"""
    from datetime import date, timedelta
    today = date.today()
    yesterday = today - timedelta(days=1)
    return render_template('admin/game_updater.html', 
                         today=today, 
                         yesterday=yesterday)

@admin_bp.route('/game-updater/api/check-games', methods=['POST'])
def api_check_games():
    """檢查指定日期的比賽狀態 API"""
    try:
        from datetime import datetime
        import re
        
        data = request.get_json()
        if not data or 'date' not in data:
            return jsonify({'success': False, 'message': '缺少日期參數'}), 400
        
        date_str = data['date']
        
        # 驗證日期格式
        date_pattern = r'^\d{4}-\d{2}-\d{2}$'
        if not re.match(date_pattern, date_str):
            return jsonify({'success': False, 'message': '日期格式不正確，請使用 YYYY-MM-DD 格式'}), 400
        
        try:
            check_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': '無效的日期'}), 400
        
        # 查詢該日期的比賽
        games = Game.query.filter(
            db.func.date(Game.date) == check_date
        ).all()
        
        if not games:
            return jsonify({
                'success': True,
                'message': f'{date_str} 沒有找到任何比賽',
                'summary': {
                    'total': 0,
                    'completed': 0,
                    'scheduled': 0,
                    'without_scores': 0
                },
                'games': []
            })
        
        # 統計比賽狀態
        total_games = len(games)
        completed_games = sum(1 for g in games if g.game_status == 'completed')
        scheduled_games = sum(1 for g in games if g.game_status == 'scheduled')
        without_scores = sum(1 for g in games if g.game_status == 'scheduled' or (g.home_score is None or g.away_score is None))
        
        # 準備比賽列表
        games_list = []
        for game in games:
            score_display = f"{game.away_score or 0} - {game.home_score or 0}" if game.game_status == 'completed' else "未完成"
            needs_update = game.game_status == 'scheduled' and game.home_score is None
            
            games_list.append({
                'game_id': game.game_id,
                'teams': f"{game.away_team} @ {game.home_team}",
                'status': game.game_status,
                'score': score_display,
                'needs_update': needs_update,
                'updated_at': game.updated_at.strftime('%H:%M') if game.updated_at else 'N/A'
            })
        
        return jsonify({
            'success': True,
            'summary': {
                'total': total_games,
                'completed': completed_games,
                'scheduled': scheduled_games,
                'without_scores': without_scores
            },
            'games': games_list,
            'date': date_str
        })
        
    except Exception as e:
        logger.error(f"檢查比賽狀態失敗: {e}")
        return jsonify({'success': False, 'message': f'檢查失敗: {str(e)}'}), 500

@admin_bp.route('/game-updater/api/update-games', methods=['POST'])
def api_update_game_results():
    """更新比賽結果 API"""
    try:
        from datetime import datetime
        from models.data_fetcher import MLBDataFetcher
        
        data = request.get_json()
        if not data or 'date' not in data:
            return jsonify({'success': False, 'message': '缺少日期參數'}), 400
        
        date_str = data['date']
        
        # 驗證日期格式
        try:
            update_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': '無效的日期格式'}), 400
        
        # 初始化數據抓取器
        fetcher = MLBDataFetcher()
        
        # 執行更新
        try:
            # 記錄更新前的比賽數量
            games_before = Game.query.filter(
                db.func.date(Game.date) == update_date
            ).count()
            
            # 調用實際的更新邏輯
            fetcher.update_games_for_date(update_date)
            
            # 記錄更新後的比賽數量
            games_after = Game.query.filter(
                db.func.date(Game.date) == update_date
            ).count()
            
            updated_count = games_after
            
            return jsonify({
                'success': True,
                'message': f'成功更新 {date_str} 的比賽數據，共 {updated_count} 場比賽',
                'updated_count': updated_count,
                'games_before': games_before,
                'games_after': games_after,
                'date': date_str
            })
            
        except Exception as update_error:
            return jsonify({
                'success': False, 
                'message': f'更新過程中發生錯誤: {str(update_error)}'
            }), 500
        
    except Exception as e:
        logger.error(f"更新比賽結果失敗: {e}")
        return jsonify({'success': False, 'message': f'更新失敗: {str(e)}'}), 500

@admin_bp.route('/game-updater/api/download-boxscores', methods=['POST'])
def api_download_boxscore():
    """下載 BoxScore 數據 API"""
    try:
        from datetime import datetime
        from models.data_fetcher import MLBDataFetcher
        
        data = request.get_json()
        if not data or 'date' not in data:
            return jsonify({'success': False, 'message': '缺少日期參數'}), 400
        
        date_str = data['date']
        
        # 驗證日期格式
        try:
            download_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': '無效的日期格式'}), 400
        
        # 初始化數據抓取器
        fetcher = MLBDataFetcher()
        
        # 執行 BoxScore 下載
        try:
            # 獲取該日期的所有比賽
            games = Game.query.filter(
                db.func.date(Game.date) == download_date
            ).all()
            
            if not games:
                return jsonify({
                    'success': False,
                    'message': f'{date_str} 沒有找到任何比賽可供下載 BoxScore'
                })
            
            downloaded_count = 0
            failed_count = 0
            
            # 逐場下載 BoxScore
            for game in games:
                try:
                    # 調用實際的 BoxScore 下載邏輯
                    detail_data = fetcher.get_game_detail(game.game_id)
                    if detail_data:
                        fetcher.update_game_details(game.game_id)
                        downloaded_count += 1
                    else:
                        failed_count += 1
                except Exception as game_error:
                    logger.error(f"下載比賽 {game.game_id} 的 BoxScore 失敗: {game_error}")
                    failed_count += 1
                    
                # 防止API請求過於頻繁
                time.sleep(0.5)
            
            message = f'成功下載 {downloaded_count} 場比賽的 BoxScore 數據'
            if failed_count > 0:
                message += f'，{failed_count} 場失敗'
                
            return jsonify({
                'success': True,
                'message': message,
                'downloaded_count': downloaded_count,
                'failed_count': failed_count,
                'total_games': len(games),
                'date': date_str
            })
            
        except Exception as download_error:
            return jsonify({
                'success': False, 
                'message': f'下載過程中發生錯誤: {str(download_error)}'
            }), 500
        
    except Exception as e:
        logger.error(f"下載 BoxScore 失敗: {e}")
        return jsonify({'success': False, 'message': f'下載失敗: {str(e)}'}), 500

@admin_bp.route('/game-updater/api/batch-update', methods=['POST'])
def api_batch_update():
    """批量更新比賽數據 API"""
    try:
        from datetime import datetime, timedelta
        from models.data_fetcher import MLBDataFetcher
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '缺少請求數據'}), 400
        
        start_date_str = data.get('start_date')
        end_date_str = data.get('end_date')
        
        if not start_date_str or not end_date_str:
            return jsonify({'success': False, 'message': '缺少日期範圍參數'}), 400
        
        # 驗證日期格式
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': '無效的日期格式'}), 400
        
        if start_date > end_date:
            return jsonify({'success': False, 'message': '開始日期不能晚於結束日期'}), 400
        
        # 計算日期差
        date_diff = (end_date - start_date).days
        if date_diff > 7:  # 限制批量更新範圍
            return jsonify({'success': False, 'message': '批量更新範圍不能超過7天'}), 400
        
        # 初始化數據抓取器
        fetcher = MLBDataFetcher()
        
        # 執行批量更新
        try:
            updated_count = 0
            current_date = start_date
            
            while current_date <= end_date:
                # 這裡可以調用實際的更新邏輯
                # day_count = fetcher.update_games_for_date(current_date)
                # updated_count += day_count
                current_date += timedelta(days=1)
            
            return jsonify({
                'success': True,
                'message': f'成功更新 {date_diff + 1} 天的比賽數據，共 {updated_count} 場比賽',
                'updated_count': updated_count,
                'date_range': f'{start_date_str} 到 {end_date_str}'
            })
            
        except Exception as update_error:
            return jsonify({
                'success': False, 
                'message': f'批量更新過程中發生錯誤: {str(update_error)}'
            }), 500
        
    except Exception as e:
        logger.error(f"批量更新失敗: {e}")
        return jsonify({'success': False, 'message': f'批量更新失敗: {str(e)}'}), 500





@admin_bp.route('/api/run-backtest', methods=['POST'])
def api_run_backtest():
    """API: 執行單場比賽的回測"""
    game_id = request.json.get('game_id')
    if not game_id:
        return jsonify({'error': '請提供 game_id'}), 400

    try:
        # 獲取比賽基本資訊
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({'error': '找不到比賽'}), 404

        # 1. 執行智能對戰分析
        analyzer = MatchupAnalyzer(game.game_id, session=db.session)
        matchup_analysis = analyzer.analyze()

        # 2. 執行預測
        from models.prediction_service import PredictionService
        prediction_service = PredictionService()
        # 我們需要一個方法來預測單場比賽並傳入 matchup_analysis
        # 暫時我們手動組合這個流程
        features = prediction_service.feature_engineer.extract_comprehensive_features(
            game.home_team, game.away_team, game.date, matchup_analysis=matchup_analysis
        )
        prediction_data = prediction_service.predictor.predict_game(
            game.home_team, game.away_team, game.date, matchup_analysis=matchup_analysis
        )

        # 3. 組合回測報告
        report = {
            'game_info': {
                'home_team': game.home_team,
                'away_team': game.away_team,
                'date': game.date.isoformat(),
                'actual_score': f'{game.away_score} - {game.home_score}' if game.game_status == 'completed' else 'N/A'
            },
            'matchup_analysis': matchup_analysis,
            'prediction_result': prediction_data
        }

        return jsonify(report)

    except Exception as e:
        logger.error(f"API執行回測失敗: {e}")
        return jsonify({'error': str(e)}), 500