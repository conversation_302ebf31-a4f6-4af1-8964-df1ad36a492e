"""
管理員功能模組
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from datetime import date, datetime, timedelta
import logging
import json

from models.database import db, Game, Team, Prediction, Player, PlayerStats
from models.prediction_service import PredictionService
from models.automated_predictor import AutomatedPredictor
from models.data_fetcher import MLBDataFetcher

logger = logging.getLogger(__name__)

# 創建 Blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
def admin_dashboard():
    """管理面板主頁"""
    # 獲取系統狀態
    system_status = {
        'scheduler_running': True,  # 假設調度器正在運行
        'database_status': 'healthy',
        'api_status': 'online',
        'last_update': '2025-07-18 16:55'
    }

    # 獲取統計數據
    stats = {
        'total_games': Game.query.count(),
        'total_teams': Team.query.count(),
        'total_predictions': Prediction.query.count(),
        'total_players': Player.query.count()
    }

    return render_template('admin/dashboard.html',
                         system_status=system_status,
                         stats=stats)

@admin_bp.route('/advanced-backtest')
def advanced_backtest():
    """進階回測介面"""
    today = date.today().isoformat()
    return render_template('admin/advanced_backtest.html', today=today)

@admin_bp.route('/prediction-methodology')
def prediction_methodology():
    """預測方法論說明頁面"""
    return render_template('admin/prediction_methodology.html')

@admin_bp.route('/daily-operations')
def daily_operations():
    """日常操作頁面"""
    from datetime import date
    return render_template('admin/daily_operations.html', today=date.today())

@admin_bp.route('/data-management')
def data_management():
    """數據管理頁面"""
    from datetime import date, timedelta
    yesterday = date.today() - timedelta(days=1)
    return render_template('admin/data_management.html', yesterday=yesterday)

@admin_bp.route('/model-predictions')
def model_predictions():
    """模型與預測頁面"""
    return render_template('admin/model_predictions.html')

@admin_bp.route('/system-management')
def system_management():
    """系統設定頁面"""
    system_status = {
        'scheduler_running': True,
        'database_status': 'healthy',
        'api_status': 'online'
    }
    return render_template('admin/system_management.html', system_status=system_status)

# 添加缺失的路由
@admin_bp.route('/daily-update', methods=['POST'])
def daily_update():
    """日常數據更新"""
    try:
        # 這裡可以添加實際的更新邏輯
        flash('日常數據更新已啟動', 'success')
    except Exception as e:
        flash(f'更新失敗: {e}', 'error')
    return redirect(url_for('admin.daily_operations'))

@admin_bp.route('/comprehensive-update', methods=['POST'])
def comprehensive_update():
    """全面數據更新"""
    try:
        # 這裡可以添加實際的全面更新邏輯
        flash('全面數據更新已啟動', 'success')
    except Exception as e:
        flash(f'更新失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/retrain-models', methods=['POST'])
def retrain_models():
    """重新訓練模型"""
    try:
        # 這裡可以添加實際的模型重訓練邏輯
        flash('模型重訓練已啟動', 'success')
    except Exception as e:
        flash(f'重訓練失敗: {e}', 'error')
    return redirect(url_for('admin.model_predictions'))

@admin_bp.route('/start-scheduler', methods=['POST'])
def start_scheduler():
    """啟動調度器"""
    try:
        # 這裡可以添加實際的調度器啟動邏輯
        flash('調度器已啟動', 'success')
    except Exception as e:
        flash(f'啟動失敗: {e}', 'error')
    return redirect(url_for('admin.system_management'))

@admin_bp.route('/stop-scheduler', methods=['POST'])
def stop_scheduler():
    """停止調度器"""
    try:
        # 這裡可以添加實際的調度器停止邏輯
        flash('調度器已停止', 'success')
    except Exception as e:
        flash(f'停止失敗: {e}', 'error')
    return redirect(url_for('admin.system_management'))

@admin_bp.route('/generate-predictions', methods=['POST'])
def generate_predictions():
    """生成預測"""
    try:
        # 這裡可以添加實際的預測生成邏輯
        flash('預測生成已啟動', 'success')
    except Exception as e:
        flash(f'生成失敗: {e}', 'error')
    return redirect(url_for('admin.daily_operations'))

@admin_bp.route('/download-data', methods=['POST'])
def download_data():
    """下載數據"""
    try:
        # 這裡可以添加實際的數據下載邏輯
        flash('數據下載已啟動', 'success')
    except Exception as e:
        flash(f'下載失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/backup-database', methods=['POST'])
def backup_database():
    """備份數據庫"""
    try:
        # 這裡可以添加實際的數據庫備份邏輯
        flash('數據庫備份已啟動', 'success')
    except Exception as e:
        flash(f'備份失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/update-data', methods=['POST'])
def update_data():
    """更新數據"""
    try:
        # 這裡可以添加實際的數據更新邏輯
        flash('數據更新已啟動', 'success')
    except Exception as e:
        flash(f'更新失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

@admin_bp.route('/fetch-detailed-data', methods=['POST'])
def fetch_detailed_data():
    """獲取詳細數據"""
    try:
        # 這裡可以添加實際的詳細數據獲取邏輯
        flash('詳細數據獲取已啟動', 'success')
    except Exception as e:
        flash(f'獲取失敗: {e}', 'error')
    return redirect(url_for('admin.data_management'))

# API 路由
@admin_bp.route('/api/refresh-schedule', methods=['POST'])
def api_refresh_schedule():
    """刷新賽程 API"""
    try:
        # 這裡可以添加實際的賽程刷新邏輯
        return jsonify({'success': True, 'message': '賽程刷新成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'刷新失敗: {e}'}), 500

@admin_bp.route('/api/games-for-date', methods=['GET'])
def api_games_for_date():
    """獲取指定日期的比賽"""
    try:
        date_str = request.args.get('date')
        if not date_str:
            return jsonify({'success': False, 'message': '缺少日期參數'}), 400

        # 這裡可以添加實際的比賽查詢邏輯
        games = []  # 查詢結果
        return jsonify({'success': True, 'games': games})
    except Exception as e:
        return jsonify({'success': False, 'message': f'查詢失敗: {e}'}), 500

@admin_bp.route('/api/system-status', methods=['GET'])
def api_system_status():
    """獲取系統狀態"""
    try:
        status = {
            'scheduler_running': True,
            'database_status': 'healthy',
            'api_status': 'online',
            'last_update': datetime.now().isoformat()
        }
        return jsonify({'success': True, 'status': status})
    except Exception as e:
        return jsonify({'success': False, 'message': f'獲取狀態失敗: {e}'}), 500

@admin_bp.route('/historical-odds-dashboard')
def historical_odds_dashboard():
    """歷史盤口管理頁面"""
    return render_template('admin/historical_odds_dashboard.html')

@admin_bp.route('/database-stats')
def database_stats():
    """數據庫統計頁面"""
    return render_template('admin/database_stats.html')

@admin_bp.route('/model-performance')
def model_performance():
    """模型性能頁面"""
    return render_template('admin/model_performance.html')

@admin_bp.route('/check-pitcher-announcements')
def check_pitcher_announcements():
    """檢查投手公告頁面"""
    return render_template('admin/check_pitcher_announcements.html')

@admin_bp.route('/prediction-readiness')
def prediction_readiness():
    """預測準備狀態頁面"""
    return render_template('admin/prediction_readiness.html')

@admin_bp.route('/prediction-launcher')
def prediction_launcher():
    """預測啟動器頁面"""
    return render_template('admin/prediction_launcher.html')

@admin_bp.route('/view-logs')
def view_logs():
    """查看日誌頁面"""
    return render_template('admin/view_logs.html')





@admin_bp.route('/api/run-backtest', methods=['POST'])
def api_run_backtest():
    """API: 執行單場比賽的回測"""
    game_id = request.json.get('game_id')
    if not game_id:
        return jsonify({'error': '請提供 game_id'}), 400

    try:
        # 獲取比賽基本資訊
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({'error': '找不到比賽'}), 404

        # 1. 執行智能對戰分析
        analyzer = MatchupAnalyzer(game.game_id, session=db.session)
        matchup_analysis = analyzer.analyze()

        # 2. 執行預測
        from models.prediction_service import PredictionService
        prediction_service = PredictionService()
        # 我們需要一個方法來預測單場比賽並傳入 matchup_analysis
        # 暫時我們手動組合這個流程
        features = prediction_service.feature_engineer.extract_comprehensive_features(
            game.home_team, game.away_team, game.date, matchup_analysis=matchup_analysis
        )
        prediction_data = prediction_service.predictor.predict_game(
            game.home_team, game.away_team, game.date, matchup_analysis=matchup_analysis
        )

        # 3. 組合回測報告
        report = {
            'game_info': {
                'home_team': game.home_team,
                'away_team': game.away_team,
                'date': game.date.isoformat(),
                'actual_score': f'{game.away_score} - {game.home_score}' if game.game_status == 'completed' else 'N/A'
            },
            'matchup_analysis': matchup_analysis,
            'prediction_result': prediction_data
        }

        return jsonify(report)

    except Exception as e:
        logger.error(f"API執行回測失敗: {e}")
        return jsonify({'error': str(e)}), 500