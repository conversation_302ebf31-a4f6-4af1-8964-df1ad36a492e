from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from datetime import datetime, date, timedelta
import logging

logger = logging.getLogger(__name__)

admin_bp = Blueprint('admin', __name__)

# 導入全面數據管理相關模組
try:
    from models.comprehensive_data_fetcher import ComprehensiveDataFetcher
    from models.modern_sbr_scraper import ModernSBRScraper
    from models.enhanced_data_models import (
        TeamAdvancedStats, PlayerInjuryReport, PlayerPerformanceTrends,
        WeatherConditions, TeamChemistry
    )
    from comprehensive_data_manager import ComprehensiveDataManager
    COMPREHENSIVE_DATA_AVAILABLE = True
except ImportError as e:
    logger.warning(f"全面數據模組導入失敗: {e}")
    COMPREHENSIVE_DATA_AVAILABLE = False

# 導入2025年預測相關模組 - 延遲導入以避免循環導入
PREDICTION_2025_AVAILABLE = False
DailyPitcherAnnouncementChecker = None
MLBPredictionLauncher2025 = None

def _import_2025_modules():
    """延遲導入2025年預測模組"""
    global PREDICTION_2025_AVAILABLE, DailyPitcherAnnouncementChecker, MLBPredictionLauncher2025
    if not PREDICTION_2025_AVAILABLE:
        try:
            from models.daily_pitcher_announcements import DailyPitcherAnnouncementChecker as DPAC
            from models.prediction_launcher_2025 import MLBPredictionLauncher2025 as MPL2025
            DailyPitcherAnnouncementChecker = DPAC
            MLBPredictionLauncher2025 = MPL2025
            PREDICTION_2025_AVAILABLE = True
        except ImportError as e:
            logger.warning(f"2025年預測模組導入失敗: {e}")
            PREDICTION_2025_AVAILABLE = False
    return PREDICTION_2025_AVAILABLE

@admin_bp.route('/')
def admin_dashboard():
    """管理員儀表板"""
    try:
        from models.automated_predictor import automated_predictor

        # 獲取系統狀態
        system_status = automated_predictor.get_system_status()

        # 準備日期相關變量
        today = date.today()
        yesterday = today - timedelta(days=1)
        next_week = today + timedelta(days=7)

        return render_template('admin/dashboard.html',
                             system_status=system_status,
                             today=today,
                             yesterday=yesterday,
                             next_week=next_week)
    except Exception as e:
        logger.error(f"獲取管理員儀表板失敗: {e}")
        flash(f'獲取系統狀態失敗: {e}', 'error')

        # 即使出錯也要提供日期變量
        today = date.today()
        yesterday = today - timedelta(days=1)
        next_week = today + timedelta(days=7)

        return render_template('admin/dashboard.html',
                             system_status={'error': str(e)},
                             today=today,
                             yesterday=yesterday,
                             next_week=next_week)

@admin_bp.route('/predictions/generate', methods=['POST'])
def generate_predictions():
    """手動生成預測"""
    try:
        from models.automated_predictor import automated_predictor
        
        target_date_str = request.form.get('date')
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()
        
        result = automated_predictor.manual_generate_predictions(target_date)
        
        if 'error' in result:
            flash(f'生成預測失敗: {result["error"]}', 'error')
        else:
            flash(f'成功生成 {result.get("successful_predictions", 0)} 場比賽的預測', 'success')
        
    except Exception as e:
        logger.error(f"手動生成預測失敗: {e}")
        flash(f'生成預測失敗: {e}', 'error')
    
    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/models/retrain', methods=['POST'])
def retrain_models():
    """手動重新訓練模型"""
    try:
        from models.automated_predictor import automated_predictor
        
        days_back = request.form.get('days_back', 60, type=int)
        
        result = automated_predictor.manual_retrain_models(days_back)
        
        if 'error' in result:
            flash(f'模型重新訓練失敗: {result["error"]}', 'error')
        else:
            flash('模型重新訓練完成', 'success')
        
    except Exception as e:
        logger.error(f"手動重新訓練模型失敗: {e}")
        flash(f'模型重新訓練失敗: {e}', 'error')
    
    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/data/update', methods=['POST'])
def update_data():
    """手動更新數據"""
    try:
        from models.automated_predictor import automated_predictor

        days_back = request.form.get('days_back', 3, type=int)

        result = automated_predictor.force_data_update(days_back)

        if 'error' in result:
            flash(f'數據更新失敗: {result["error"]}', 'error')
        else:
            flash(f'成功更新 {len(result.get("updated_dates", []))} 天的數據', 'success')

    except Exception as e:
        logger.error(f"手動更新數據失敗: {e}")
        flash(f'數據更新失敗: {e}', 'error')

    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/data/detailed', methods=['POST'])
def fetch_detailed_data():
    """獲取詳細比賽數據"""
    try:
        from models.automated_predictor import automated_predictor

        target_date_str = request.form.get('date')
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today() - timedelta(days=1)

        result = automated_predictor.fetch_detailed_data_for_date(target_date)

        if 'error' in result:
            flash(f'獲取詳細數據失敗: {result["error"]}', 'error')
        else:
            flash(f'成功獲取 {target_date} 的詳細比賽數據', 'success')

    except Exception as e:
        logger.error(f"獲取詳細數據失敗: {e}")
        flash(f'獲取詳細數據失敗: {e}', 'error')

    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/data/daily-update', methods=['POST'])
def daily_update():
    """每日數據更新 - 更新比賽行程和下載Box Score"""
    try:
        from models.data_fetcher import MLBDataFetcher
        from models.detailed_data_fetcher import DetailedDataFetcher

        target_date_str = request.form.get('date')
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()

        # 使用修復後的MLBDataFetcher更新比賽行程
        game_fetcher = MLBDataFetcher()
        detail_fetcher = DetailedDataFetcher()

        # 1. 更新比賽行程（使用修復後的API）
        schedule_result = game_fetcher.fetch_games_by_date(target_date)

        # 2. 下載已完成比賽的Box Score
        boxscore_result = detail_fetcher.fetch_completed_games_boxscores(target_date)

        if schedule_result and boxscore_result:
            flash(f'成功完成 {target_date} 的每日更新', 'success')
        elif schedule_result:
            flash(f'成功更新 {target_date} 的比賽行程，但Box Score下載失敗', 'warning')
        elif boxscore_result:
            flash(f'成功下載 {target_date} 的Box Score，但比賽行程更新失敗', 'warning')
        else:
            flash(f'{target_date} 的每日更新失敗', 'error')

    except Exception as e:
        logger.error(f"每日更新失敗: {e}")
        flash(f'每日更新失敗: {e}', 'error')

    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/data/comprehensive-update', methods=['POST'])
def comprehensive_update():
    """全面數據更新 - 更新最近所有資料並刷新未來七天行程"""
    try:
        from models.data_fetcher import MLBDataFetcher
        from models.detailed_data_fetcher import DetailedDataFetcher
        from datetime import timedelta

        game_fetcher = MLBDataFetcher()
        detail_fetcher = DetailedDataFetcher()

        today = date.today()

        # 更新範圍：過去7天到未來7天
        start_date = today - timedelta(days=7)
        end_date = today + timedelta(days=7)

        total_days = (end_date - start_date).days + 1
        success_count = 0
        error_count = 0

        logger.info(f"開始全面數據更新: {start_date} 到 {end_date}")

        # 逐日更新數據
        current_date = start_date
        while current_date <= end_date:
            try:
                logger.info(f"正在更新 {current_date} 的數據...")

                # 1. 更新比賽行程和狀態
                schedule_success = game_fetcher.fetch_games_by_date(current_date)

                # 2. 如果是過去的日期，嘗試下載Box Score
                if current_date <= today:
                    boxscore_success = detail_fetcher.fetch_completed_games_boxscores(current_date)
                else:
                    boxscore_success = True  # 未來日期不需要Box Score

                if schedule_success:
                    success_count += 1
                    logger.info(f"✅ {current_date} 數據更新成功")
                else:
                    error_count += 1
                    logger.warning(f"⚠️ {current_date} 數據更新失敗")

            except Exception as e:
                error_count += 1
                logger.error(f"❌ {current_date} 數據更新異常: {e}")

            current_date += timedelta(days=1)

        # 生成結果消息
        if error_count == 0:
            flash(f'🎉 全面數據更新完成！成功更新 {success_count}/{total_days} 天的數據', 'success')
        elif success_count > error_count:
            flash(f'✅ 全面數據更新基本完成！成功 {success_count} 天，失敗 {error_count} 天', 'warning')
        else:
            flash(f'⚠️ 全面數據更新遇到問題！成功 {success_count} 天，失敗 {error_count} 天', 'error')

        logger.info(f"全面數據更新完成: 成功 {success_count}/{total_days} 天")

    except Exception as e:
        logger.error(f"全面數據更新失敗: {e}")
        flash(f'❌ 全面數據更新失敗: {e}', 'error')

    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/api/refresh-schedule', methods=['POST'])
def api_refresh_schedule():
    """刷新未來七天比賽行程API"""
    try:
        from models.data_fetcher import MLBDataFetcher
        from datetime import timedelta

        game_fetcher = MLBDataFetcher()
        today = date.today()

        # 刷新未來7天的行程
        success_count = 0
        total_days = 7

        for i in range(total_days):
            future_date = today + timedelta(days=i)
            try:
                success = game_fetcher.fetch_games_by_date(future_date)
                if success:
                    success_count += 1
                logger.info(f"刷新 {future_date} 行程: {'成功' if success else '失敗'}")
            except Exception as e:
                logger.error(f"刷新 {future_date} 行程失敗: {e}")

        if success_count == total_days:
            return jsonify({
                'success': True,
                'message': f'成功刷新未來 {total_days} 天的比賽行程',
                'updated_days': success_count
            })
        elif success_count > 0:
            return jsonify({
                'success': True,
                'message': f'部分成功：刷新了 {success_count}/{total_days} 天的行程',
                'updated_days': success_count
            })
        else:
            return jsonify({
                'success': False,
                'error': '未能刷新任何行程數據'
            })

    except Exception as e:
        logger.error(f"刷新行程API失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@admin_bp.route('/data/boxscore-today', methods=['POST'])
def download_today_boxscores():
    """下載今天已完成比賽的Box Score"""
    try:
        from models.detailed_data_fetcher import DetailedDataFetcher
        from models.database import db, Game, BoxScore

        today = date.today()
        fetcher = DetailedDataFetcher()

        # 查找今天已完成但沒有Box Score的比賽
        completed_games = db.session.query(Game).filter(
            Game.date == today,
            Game.game_status == 'completed',
            ~Game.game_id.in_(
                db.session.query(BoxScore.game_id).distinct()
            )
        ).all()

        if not completed_games:
            flash('今天沒有需要下載Box Score的已完成比賽', 'info')
            return redirect(url_for('admin.admin_dashboard'))

        success_count = 0
        for game in completed_games:
            try:
                if fetcher.fetch_game_boxscore(game.game_id):
                    success_count += 1
            except Exception as e:
                logger.error(f"下載比賽 {game.game_id} Box Score失敗: {e}")

        db.session.commit()
        flash(f'成功下載 {success_count}/{len(completed_games)} 場比賽的Box Score', 'success')

    except Exception as e:
        logger.error(f"下載今天Box Score失敗: {e}")
        flash(f'下載今天Box Score失敗: {e}', 'error')

    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/data/schedule-refresh', methods=['POST'])
def refresh_schedule():
    """刷新比賽行程 - 處理比賽時間變更"""
    try:
        from models.data_fetcher import MLBDataFetcher

        # 獲取日期範圍
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')

        if start_date_str and end_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        else:
            # 默認刷新今天到未來7天的行程
            start_date = date.today()
            end_date = start_date + timedelta(days=7)

        # 使用修復後的MLBDataFetcher
        fetcher = MLBDataFetcher()
        updated_days = 0

        current_date = start_date
        while current_date <= end_date:
            try:
                if fetcher.fetch_games_by_date(current_date):
                    updated_days += 1
            except Exception as e:
                logger.error(f"刷新 {current_date} 行程失敗: {e}")

            current_date += timedelta(days=1)

        flash(f'成功刷新 {updated_days} 天的比賽行程', 'success')

    except Exception as e:
        logger.error(f"刷新比賽行程失敗: {e}")
        flash(f'刷新比賽行程失敗: {e}', 'error')

    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/scheduler/start', methods=['POST'])
def start_scheduler():
    """啟動調度器"""
    try:
        from models.automated_predictor import automated_predictor
        
        automated_predictor.start_scheduler()
        flash('調度器已啟動', 'success')
        
    except Exception as e:
        logger.error(f"啟動調度器失敗: {e}")
        flash(f'啟動調度器失敗: {e}', 'error')
    
    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/scheduler/stop', methods=['POST'])
def stop_scheduler():
    """停止調度器"""
    try:
        from models.automated_predictor import automated_predictor
        
        automated_predictor.stop_scheduler()
        flash('調度器已停止', 'success')
        
    except Exception as e:
        logger.error(f"停止調度器失敗: {e}")
        flash(f'停止調度器失敗: {e}', 'error')
    
    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/api/status')
def api_system_status():
    """API端點：獲取系統狀態"""
    try:
        from models.automated_predictor import automated_predictor
        
        status = automated_predictor.get_system_status()
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"獲取系統狀態API失敗: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/predictions/generate', methods=['POST'])
def api_generate_predictions():
    """API端點：生成預測"""
    try:
        from models.automated_predictor import automated_predictor
        
        data = request.get_json() or {}
        target_date_str = data.get('date')
        
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()
        
        result = automated_predictor.manual_generate_predictions(target_date)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API生成預測失敗: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/models/retrain', methods=['POST'])
def api_retrain_models():
    """API端點：重新訓練模型"""
    try:
        from models.automated_predictor import automated_predictor
        
        data = request.get_json() or {}
        days_back = data.get('days_back', 60)
        
        result = automated_predictor.manual_retrain_models(days_back)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API重新訓練模型失敗: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/logs')
def view_logs():
    """查看系統日誌"""
    try:
        # 這裡可以實現日誌查看功能
        # 目前返回簡單的日誌信息
        logs = [
            {
                'timestamp': datetime.now().isoformat(),
                'level': 'INFO',
                'message': '系統正常運行'
            }
        ]
        
        return render_template('admin/logs.html', logs=logs)
        
    except Exception as e:
        logger.error(f"查看日誌失敗: {e}")
        flash(f'查看日誌失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/models/performance')
def model_performance():
    """查看模型性能"""
    try:
        from models.prediction_service import PredictionService
        
        prediction_service = PredictionService()
        
        # 獲取不同時間段的準確率統計
        stats_7d = prediction_service.get_prediction_accuracy_stats(7)
        stats_30d = prediction_service.get_prediction_accuracy_stats(30)
        stats_90d = prediction_service.get_prediction_accuracy_stats(90)
        
        performance_data = {
            '7_days': stats_7d,
            '30_days': stats_30d,
            '90_days': stats_90d
        }
        
        return render_template('admin/model_performance.html', 
                             performance_data=performance_data)
        
    except Exception as e:
        logger.error(f"查看模型性能失敗: {e}")
        flash(f'查看模型性能失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/database/stats')
def database_stats():
    """查看數據庫統計"""
    try:
        from models.database import db, Game, Team, Prediction, PlayerStats
        
        # 獲取數據庫統計信息
        stats = {
            'games_total': Game.query.count(),
            'games_completed': Game.query.filter_by(game_status='completed').count(),
            'teams_total': Team.query.count(),
            'predictions_total': Prediction.query.count(),
            'predictions_verified': Prediction.query.filter(Prediction.is_correct.isnot(None)).count(),
            'player_stats_total': PlayerStats.query.count()
        }
        
        # 獲取最近的數據
        recent_games = Game.query.order_by(Game.date.desc()).limit(10).all()
        recent_predictions = Prediction.query.order_by(Prediction.prediction_date.desc()).limit(10).all()
        
        return render_template('admin/database_stats.html',
                             stats=stats,
                             recent_games=recent_games,
                             recent_predictions=recent_predictions)
        
    except Exception as e:
        logger.error(f"查看數據庫統計失敗: {e}")
        flash(f'查看數據庫統計失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/test/prediction')
def test_prediction():
    """測試預測功能"""
    try:
        from models.prediction_service import PredictionService
        from models.database import Game
        
        # 獲取一場即將進行的比賽進行測試
        upcoming_game = Game.query.filter(
            Game.date >= date.today(),
            Game.game_status.in_(['scheduled', 'pre-game'])
        ).first()
        
        if not upcoming_game:
            flash('沒有找到即將進行的比賽進行測試', 'warning')
            return redirect(url_for('admin.admin_dashboard'))
        
        prediction_service = PredictionService()
        
        # 確保模型已準備就緒
        if not prediction_service.ensure_models_ready():
            flash('模型未就緒，無法進行測試', 'error')
            return redirect(url_for('admin.admin_dashboard'))
        
        # 獲取預測
        prediction_data = prediction_service.get_prediction_for_game(upcoming_game.game_id)
        
        if prediction_data:
            flash('預測測試成功', 'success')
            return render_template('admin/test_prediction.html',
                                 game=upcoming_game,
                                 prediction=prediction_data)
        else:
            flash('預測測試失敗', 'error')
            return redirect(url_for('admin.admin_dashboard'))
        
    except Exception as e:
        logger.error(f"測試預測失敗: {e}")
        flash(f'測試預測失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

# ===== 全面數據管理路由 =====

@admin_bp.route('/comprehensive-data')
def comprehensive_data_dashboard():
    """全面數據管理儀表板"""
    if not COMPREHENSIVE_DATA_AVAILABLE:
        flash('全面數據功能不可用', 'error')
        return redirect(url_for('admin.admin_dashboard'))

    try:
        manager = ComprehensiveDataManager()

        # 獲取數據狀態
        stats = manager.check_data_status()

        # 獲取球隊列表
        from models.database import Team
        teams = Team.query.filter_by(active=True).order_by(Team.team_code).all()

        return render_template('admin/comprehensive_data.html',
                             stats=stats,
                             teams=teams,
                             comprehensive_available=COMPREHENSIVE_DATA_AVAILABLE)

    except Exception as e:
        logger.error(f"獲取全面數據儀表板失敗: {e}")
        flash(f'獲取全面數據狀態失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/comprehensive-data/fetch-all', methods=['POST'])
def fetch_all_comprehensive_data():
    """獲取所有球隊的全面數據"""
    if not COMPREHENSIVE_DATA_AVAILABLE:
        return jsonify({'success': False, 'message': '全面數據功能不可用'})

    try:
        season = request.form.get('season', date.today().year)
        season = int(season)

        manager = ComprehensiveDataManager()
        results = manager.fetch_all_teams_data(season)

        if results:
            message = f"全面數據獲取完成: {results['successful_teams']}/{results['total_teams']} 成功"
            return jsonify({
                'success': True,
                'message': message,
                'results': results
            })
        else:
            return jsonify({'success': False, 'message': '全面數據獲取失敗'})

    except Exception as e:
        logger.error(f"獲取全面數據失敗: {e}")
        return jsonify({'success': False, 'message': f'錯誤: {str(e)}'})

@admin_bp.route('/comprehensive-data/fetch-team', methods=['POST'])
def fetch_team_comprehensive_data():
    """獲取單個球隊的全面數據"""
    if not COMPREHENSIVE_DATA_AVAILABLE:
        return jsonify({'success': False, 'message': '全面數據功能不可用'})

    try:
        team_code = request.form.get('team_code')
        season = request.form.get('season', date.today().year)
        season = int(season)

        if not team_code:
            return jsonify({'success': False, 'message': '請選擇球隊'})

        manager = ComprehensiveDataManager()
        success = manager.fetch_single_team_data(team_code, season)

        if success:
            return jsonify({
                'success': True,
                'message': f'球隊 {team_code} 全面數據獲取成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'球隊 {team_code} 全面數據獲取失敗'
            })

    except Exception as e:
        logger.error(f"獲取球隊全面數據失敗: {e}")
        return jsonify({'success': False, 'message': f'錯誤: {str(e)}'})

@admin_bp.route('/comprehensive-data/update-trends', methods=['POST'])
def update_player_trends():
    """更新球員表現趨勢"""
    if not COMPREHENSIVE_DATA_AVAILABLE:
        return jsonify({'success': False, 'message': '全面數據功能不可用'})

    try:
        season = request.form.get('season', date.today().year)
        season = int(season)

        manager = ComprehensiveDataManager()
        success_count = manager.update_player_trends(season)

        return jsonify({
            'success': True,
            'message': f'球員趨勢更新完成: {success_count} 位球員成功'
        })

    except Exception as e:
        logger.error(f"更新球員趨勢失敗: {e}")
        return jsonify({'success': False, 'message': f'錯誤: {str(e)}'})

@admin_bp.route('/comprehensive-data/status')
def get_comprehensive_data_status():
    """獲取全面數據狀態"""
    if not COMPREHENSIVE_DATA_AVAILABLE:
        return jsonify({'success': False, 'message': '全面數據功能不可用'})

    try:
        manager = ComprehensiveDataManager()
        stats = manager.check_data_status()

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"獲取全面數據狀態失敗: {e}")
        return jsonify({'success': False, 'message': f'錯誤: {str(e)}'})

@admin_bp.route('/comprehensive-data/cleanup', methods=['POST'])
def cleanup_old_comprehensive_data():
    """清理舊的全面數據"""
    if not COMPREHENSIVE_DATA_AVAILABLE:
        return jsonify({'success': False, 'message': '全面數據功能不可用'})

    try:
        days = request.form.get('days', 30)
        days = int(days)

        manager = ComprehensiveDataManager()
        manager.cleanup_old_data(days)

        return jsonify({
            'success': True,
            'message': f'清理 {days} 天前的舊數據完成'
        })

    except Exception as e:
        logger.error(f"清理舊數據失敗: {e}")
        return jsonify({'success': False, 'message': f'錯誤: {str(e)}'})

@admin_bp.route('/check_pitcher_announcements')
def check_pitcher_announcements():
    """檢查每日投手公告"""
    if not _import_2025_modules():
        flash('2025年預測模組不可用', 'error')
        return redirect(url_for('admin.admin_dashboard'))

    try:
        checker = DailyPitcherAnnouncementChecker()
        announcement_status = checker.check_today_pitcher_announcements()
        timing_rec = checker.get_prediction_timing_recommendation()

        return render_template('admin/pitcher_announcements.html',
                             announcement_status=announcement_status,
                             timing_recommendation=timing_rec)
    except Exception as e:
        logger.error(f"檢查投手公告失敗: {e}")
        flash(f'檢查投手公告失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/prediction_launcher')
def prediction_launcher():
    """2025年預測啟動器頁面"""
    if not _import_2025_modules():
        flash('2025年預測模組不可用', 'error')
        return redirect(url_for('admin.admin_dashboard'))

    try:
        from flask import current_app
        launcher = MLBPredictionLauncher2025(current_app)
        report = launcher.get_daily_prediction_report()

        return render_template('admin/prediction_launcher.html', report=report)
    except Exception as e:
        logger.error(f"獲取預測報告失敗: {e}")
        flash(f'獲取預測報告失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/prediction_readiness')
def prediction_readiness():
    """預測準備狀況檢查"""
    try:
        from models.prediction_readiness_checker import PredictionReadinessChecker

        checker = PredictionReadinessChecker()
        readiness_status = checker.check_prediction_readiness()
        lineup_schedule = checker.get_lineup_update_schedule()

        return render_template('admin/prediction_readiness.html',
                             readiness=readiness_status,
                             schedule=lineup_schedule)
    except Exception as e:
        logger.error(f"檢查預測準備狀況失敗: {e}")
        flash(f'檢查預測準備狀況失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/api/refresh_lineup_status')
def refresh_lineup_status():
    """刷新陣容狀況API"""
    try:
        from models.prediction_readiness_checker import PredictionReadinessChecker

        checker = PredictionReadinessChecker()
        readiness_status = checker.check_prediction_readiness()

        return jsonify({
            'success': True,
            'data': readiness_status,
            'updated_at': readiness_status['checked_at']
        })
    except Exception as e:
        logger.error(f"刷新陣容狀況失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/launch_predictions', methods=['POST'])
def launch_predictions():
    """啟動預測"""
    if not _import_2025_modules():
        return jsonify({'success': False, 'error': '2025年預測模組不可用'})

    try:
        target_date_str = request.json.get('date', date.today().strftime('%Y-%m-%d'))
        force = request.json.get('force', False)

        target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()

        from flask import current_app
        launcher = MLBPredictionLauncher2025(current_app)
        result = launcher.launch_predictions(target_date, force)

        return jsonify(result)
    except Exception as e:
        logger.error(f"啟動預測失敗: {e}")
        return jsonify({'success': False, 'error': str(e)})

@admin_bp.route('/pitcher_details/<pitcher_name>')
def pitcher_details(pitcher_name):
    """獲取投手詳細統計信息"""
    try:
        from models.pitcher_detailed_stats import PitcherDetailedStats

        pitcher_id = request.args.get('pitcher_id', type=int)

        stats_analyzer = PitcherDetailedStats()
        pitcher_stats = stats_analyzer.get_pitcher_comprehensive_stats(pitcher_name, pitcher_id)

        return render_template('admin/pitcher_details.html',
                             pitcher_stats=pitcher_stats,
                             pitcher_name=pitcher_name)
    except Exception as e:
        logger.error(f"獲取投手詳細信息失敗: {e}")
        flash(f'獲取投手詳細信息失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/api/pitcher_stats/<pitcher_name>')
def api_pitcher_stats(pitcher_name):
    """API端點：獲取投手統計數據"""
    try:
        from models.pitcher_detailed_stats import PitcherDetailedStats

        pitcher_id = request.args.get('pitcher_id', type=int)

        stats_analyzer = PitcherDetailedStats()
        pitcher_stats = stats_analyzer.get_pitcher_comprehensive_stats(pitcher_name, pitcher_id)

        return jsonify(pitcher_stats)
    except Exception as e:
        logger.error(f"API獲取投手統計失敗: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/prediction_methodology')
def prediction_methodology():
    """預測方法論說明頁面"""
    try:
        return render_template('admin/prediction_methodology.html')
    except Exception as e:
        logger.error(f"載入預測方法論頁面失敗: {e}")
        flash(f'載入失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

# ===== 歷史盤口數據管理 =====

@admin_bp.route('/historical_odds')
def historical_odds_dashboard():
    """歷史盤口數據管理儀表板"""
    try:
        from models.database import db, BettingOdds, Game

        # 獲取數據庫中的盤口數據統計
        total_odds = BettingOdds.query.count()

        # 修正統計邏輯：根據實際數據字段統計
        spreads_count = BettingOdds.query.filter(
            (BettingOdds.home_spread_point.isnot(None)) |
            (BettingOdds.away_spread_point.isnot(None))
        ).count()

        totals_count = BettingOdds.query.filter(
            BettingOdds.total_point.isnot(None)
        ).count()

        # 獲取覆蓋的日期範圍
        covered_dates = db.session.query(Game.date)\
            .join(BettingOdds, Game.game_id == BettingOdds.game_id)\
            .distinct().count()

        # 最新和最舊的數據日期
        latest_date = db.session.query(Game.date)\
            .join(BettingOdds, Game.game_id == BettingOdds.game_id)\
            .order_by(Game.date.desc()).first()

        earliest_date = db.session.query(Game.date)\
            .join(BettingOdds, Game.game_id == BettingOdds.game_id)\
            .order_by(Game.date.asc()).first()

        stats = {
            'total_odds': total_odds,
            'spreads_count': spreads_count,
            'totals_count': totals_count,
            'covered_dates': covered_dates,
            'latest_date': latest_date[0] if latest_date else None,
            'earliest_date': earliest_date[0] if earliest_date else None
        }

        return render_template('admin/historical_odds.html', stats=stats)

    except Exception as e:
        logger.error(f"載入歷史盤口儀表板失敗: {e}")
        flash(f'載入失敗: {e}', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/api/fetch_historical_odds', methods=['POST'])
def api_fetch_historical_odds():
    """API端點：觸發歷史盤口數據下載（從今天往回下載，包含數據一致性檢查）"""
    try:
        from models.odds_data_fetcher import OddsDataFetcher
        from models.sportsbookreview_scraper import SportsBookReviewScraper
        from models.covers_scraper import CoversMLBScraper
        from models.database import db, BettingOdds, Game
        from datetime import datetime, timedelta, date

        # 獲取參數
        data = request.get_json()
        mode = data.get('mode', 'download')  # download 或 verify
        days_back = data.get('days_back', 7)  # 從今天往回幾天

        if days_back < 1 or days_back > 60:
            return jsonify({
                'success': False,
                'error': '天數範圍必須在1-60之間'
            }), 400

        # 從今天開始往回計算日期
        end_date = date.today()
        start_date = end_date - timedelta(days=days_back - 1)

        # 初始化數據獲取器和網頁抓取器
        from flask import current_app
        fetcher = OddsDataFetcher(current_app)

        # 選擇抓取器：優先使用現代化SBR爬蟲，covers.com作為備用
        scraper_type = data.get('scraper_type', 'modern_sbr')  # 默認使用現代化SBR

        if scraper_type == 'modern_sbr':
            try:
                scraper = ModernSBRScraper()
                logger.info("使用現代化SportsBookReview爬蟲")
            except Exception as e:
                logger.warning(f"現代化SBR爬蟲初始化失敗，使用covers.com備用: {e}")
                scraper = CoversMLBScraper()
                scraper_type = 'covers'
        else:
            scraper = CoversMLBScraper()  # 使用covers.com抓取器
            logger.info("使用covers.com爬蟲")

        # 優先使用網頁抓取器，API作為備用
        use_scraper = True
        api_status = {'api_accessible': False, 'message': '使用網頁抓取器'}

        # 如果需要，也可以檢查API狀態作為備用
        try:
            api_backup_status = fetcher.check_api_status()
        except Exception as e:
            logger.warning(f"API狀態檢查失敗，將使用網頁抓取器: {e}")
            api_backup_status = {'api_accessible': False, 'message': '使用網頁抓取器'}

        # 生成日期列表（從最新到最舊）
        target_dates = []
        current_date = end_date
        while current_date >= start_date:
            target_dates.append(current_date)
            current_date -= timedelta(days=1)

        # 統計變量
        results = []
        download_count = 0
        verify_count = 0
        inconsistent_count = 0
        redownload_count = 0

        for target_date in target_dates:
            try:
                # 檢查該日期的比賽
                games = Game.query.filter_by(date=target_date).all()
                if not games:
                    results.append({
                        'date': target_date.isoformat(),
                        'status': 'no_games',
                        'message': '沒有比賽記錄'
                    })
                    continue

                # 檢查現有盤口數據
                existing_odds = db.session.query(BettingOdds)\
                    .join(Game, BettingOdds.game_id == Game.game_id)\
                    .filter(Game.date == target_date).all()

                if mode == 'verify' and existing_odds:
                    # 驗證模式：檢查數據一致性
                    verify_result = _verify_odds_consistency(target_date, existing_odds, fetcher)
                    verify_count += 1

                    if verify_result['inconsistent']:
                        inconsistent_count += 1
                        # 重新下載不一致的數據
                        if verify_result.get('redownload'):
                            redownload_result = _redownload_odds(target_date, fetcher, scraper)
                            if redownload_result['success']:
                                redownload_count += 1
                                results.append({
                                    'date': target_date.isoformat(),
                                    'status': 'redownloaded',
                                    'games_count': len(games),
                                    'inconsistencies': verify_result['inconsistencies'],
                                    'redownload_records': redownload_result['records']
                                })
                            else:
                                results.append({
                                    'date': target_date.isoformat(),
                                    'status': 'redownload_failed',
                                    'error': redownload_result['error']
                                })
                        else:
                            results.append({
                                'date': target_date.isoformat(),
                                'status': 'inconsistent',
                                'games_count': len(games),
                                'inconsistencies': verify_result['inconsistencies']
                            })
                    else:
                        results.append({
                            'date': target_date.isoformat(),
                            'status': 'consistent',
                            'games_count': len(games),
                            'odds_count': len(existing_odds)
                        })

                elif not existing_odds:
                    # 下載模式：沒有數據時下載
                    # 優先使用網頁抓取器
                    odds_result = _fetch_odds_with_scraper(target_date, scraper, games)

                    # 如果網頁抓取失敗，嘗試API備用
                    if not odds_result['success'] and api_backup_status.get('api_accessible'):
                        logger.info(f"網頁抓取失敗，嘗試API備用: {target_date}")
                        odds_result = fetcher.fetch_odds_for_games(target_date)

                    if odds_result['success'] and odds_result.get('matched_games', 0) > 0:
                        save_result = _save_scraper_odds_to_database(odds_result, target_date, games)

                        if save_result['success']:
                            download_count += 1
                            results.append({
                                'date': target_date.isoformat(),
                                'status': 'downloaded',
                                'games_count': len(games),
                                'matched_games': odds_result['matched_games'],
                                'saved_records': save_result['total_processed']
                            })
                        else:
                            results.append({
                                'date': target_date.isoformat(),
                                'status': 'save_failed',
                                'error': save_result['error']
                            })
                    else:
                        results.append({
                            'date': target_date.isoformat(),
                            'status': 'no_odds',
                            'games_count': len(games),
                            'error': odds_result.get('error', '沒有匹配的賠率')
                        })
                else:
                    # 已有數據，跳過
                    results.append({
                        'date': target_date.isoformat(),
                        'status': 'exists',
                        'games_count': len(games),
                        'odds_count': len(existing_odds)
                    })

            except Exception as e:
                results.append({
                    'date': target_date.isoformat(),
                    'status': 'error',
                    'error': str(e)
                })

            # 添加延遲以避免被網站封鎖
            if use_scraper:
                # 兼容不同爬蟲的延遲方法
                if hasattr(scraper, 'add_delay'):
                    scraper.add_delay(2.0)  # covers scraper使用單一參數
                else:
                    # 現代化爬蟲使用time.sleep
                    import time
                    time.sleep(2.0)

        return jsonify({
            'success': True,
            'mode': mode,
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat(),
                'days': days_back
            },
            'summary': {
                'total_dates': len(target_dates),
                'download_count': download_count,
                'verify_count': verify_count,
                'inconsistent_count': inconsistent_count,
                'redownload_count': redownload_count
            },
            'results': results
        })

    except Exception as e:
        logger.error(f"歷史盤口處理失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def _verify_odds_consistency(target_date, existing_odds, fetcher):
    """驗證盤口數據一致性"""
    try:
        # 獲取最新的API數據
        fresh_odds = fetcher.fetch_odds_for_games(target_date)

        if not fresh_odds['success']:
            return {
                'inconsistent': False,
                'error': '無法獲取新數據進行比較'
            }

        inconsistencies = []

        # 比較每場比賽的盤口數據
        for game_data in fresh_odds.get('games', []):
            game_id = game_data['game_id']
            fresh_odds_info = game_data['odds']

            # 查找現有數據
            existing_game_odds = [o for o in existing_odds if o.game_id == game_id]

            # 檢查讓分盤
            if fresh_odds_info.get('spreads'):
                for bookmaker, spreads_data in fresh_odds_info['spreads'].items():
                    existing_spread = next((o for o in existing_game_odds
                                          if o.bookmaker == bookmaker and o.market_type == 'spreads'), None)

                    if existing_spread:
                        # 比較數據
                        fresh_home_point = None
                        fresh_away_point = None
                        for team, data in spreads_data.items():
                            if data.get('point', 0) > 0:
                                fresh_away_point = data.get('point')
                            else:
                                fresh_home_point = data.get('point')

                        if (abs((existing_spread.home_spread_point or 0) - (fresh_home_point or 0)) > 0.1 or
                            abs((existing_spread.away_spread_point or 0) - (fresh_away_point or 0)) > 0.1):
                            inconsistencies.append({
                                'game_id': game_id,
                                'bookmaker': bookmaker,
                                'market': 'spreads',
                                'existing': {
                                    'home_point': existing_spread.home_spread_point,
                                    'away_point': existing_spread.away_spread_point
                                },
                                'fresh': {
                                    'home_point': fresh_home_point,
                                    'away_point': fresh_away_point
                                }
                            })

            # 檢查大小分
            if fresh_odds_info.get('totals'):
                for bookmaker, totals_data in fresh_odds_info['totals'].items():
                    existing_total = next((o for o in existing_game_odds
                                         if o.bookmaker == bookmaker and o.market_type == 'totals'), None)

                    if existing_total:
                        fresh_total_point = totals_data.get('Over', {}).get('point') or totals_data.get('Under', {}).get('point')

                        if abs((existing_total.total_point or 0) - (fresh_total_point or 0)) > 0.1:
                            inconsistencies.append({
                                'game_id': game_id,
                                'bookmaker': bookmaker,
                                'market': 'totals',
                                'existing': {'total_point': existing_total.total_point},
                                'fresh': {'total_point': fresh_total_point}
                            })

        return {
            'inconsistent': len(inconsistencies) > 0,
            'inconsistencies': inconsistencies,
            'redownload': len(inconsistencies) > 0
        }

    except Exception as e:
        return {
            'inconsistent': False,
            'error': str(e)
        }

def _redownload_odds(target_date, fetcher, scraper=None):
    """重新下載盤口數據"""
    try:
        from models.database import db, BettingOdds, Game

        # 刪除現有數據
        existing_odds = db.session.query(BettingOdds)\
            .join(Game, BettingOdds.game_id == Game.game_id)\
            .filter(Game.date == target_date).all()

        for odds in existing_odds:
            db.session.delete(odds)

        # 重新下載 - 優先使用網頁抓取器
        if scraper:
            # 獲取該日期的遊戲
            games = Game.query.filter(Game.game_date == target_date).all()
            odds_result = _fetch_odds_with_scraper(target_date, scraper, games)

            if odds_result['success']:
                save_result = _save_scraper_odds_to_database(odds_result, target_date, games)
            else:
                # 網頁抓取失敗，嘗試API
                odds_result = fetcher.fetch_odds_for_games(target_date)
                if odds_result['success']:
                    save_result = fetcher.save_odds_to_database(odds_result)
                else:
                    save_result = {'success': False, 'error': '所有數據源都失敗'}
        else:
            # 只使用API
            odds_result = fetcher.fetch_odds_for_games(target_date)
            if odds_result['success']:
                save_result = fetcher.save_odds_to_database(odds_result)
            else:
                save_result = {'success': False, 'error': 'API下載失敗'}

        if save_result['success']:
            db.session.commit()
            return {
                'success': True,
                'records': save_result['total_processed']
            }
        else:
            db.session.rollback()
            return {
                'success': False,
                'error': odds_result.get('error', '重新下載失敗')
            }

    except Exception as e:
        db.session.rollback()
        return {
            'success': False,
            'error': str(e)
        }


def _fetch_odds_with_scraper(target_date, scraper, games):
    """使用網頁抓取器獲取盤口數據"""
    try:
        from models.covers_scraper import CoversMLBScraper

        # 檢查抓取器類型並調用相應方法
        if isinstance(scraper, CoversMLBScraper):
            # 使用covers.com抓取器
            scraper_result = scraper.fetch_mlb_games_for_date(target_date)
        else:
            # 使用sportsbookreview抓取器（備用）
            scraper_result = scraper.fetch_historical_odds(target_date, bookmaker='bet365')

        if not scraper_result['success']:
            return {
                'success': False,
                'error': scraper_result.get('error', '網頁抓取失敗'),
                'matched_games': 0
            }

        # 匹配遊戲數據
        matched_count = 0
        scraped_games = scraper_result.get('games', [])

        for scraped_game in scraped_games:
            # 查找對應的遊戲記錄
            away_team = scraped_game.get('away_team', '').upper()
            home_team = scraped_game.get('home_team', '').upper()

            # 在數據庫遊戲中查找匹配
            for game in games:
                if (game.away_team.upper() == away_team and
                    game.home_team.upper() == home_team):
                    matched_count += 1
                    break

        return {
            'success': True,
            'games': scraped_games,
            'matched_games': matched_count,
            'total_games': len(scraped_games),
            'source': 'sportsbookreview'
        }

    except Exception as e:
        logger.error(f"網頁抓取器獲取數據失敗: {e}")
        return {
            'success': False,
            'error': str(e),
            'matched_games': 0
        }


def _save_scraper_odds_to_database(odds_result, target_date, games):
    """保存網頁抓取的盤口數據到數據庫"""
    try:
        from models.database import db, BettingOdds, Game

        saved_count = 0
        scraped_games = odds_result.get('games', [])

        for scraped_game in scraped_games:
            try:
                # 查找對應的遊戲記錄
                away_team = scraped_game.get('away_team', '').upper()
                home_team = scraped_game.get('home_team', '').upper()

                game = None
                for g in games:
                    if (g.away_team.upper() == away_team and
                        g.home_team.upper() == home_team):
                        game = g
                        break

                if not game:
                    logger.warning(f"找不到對應遊戲: {away_team} @ {home_team}")
                    continue

                # 檢查是否已存在盤口數據
                existing_odds = BettingOdds.query.filter_by(
                    game_id=game.game_id,  # 修復：使用game.game_id而不是game.id
                    bookmaker=scraped_game.get('bookmaker', 'bet365')
                ).first()

                if existing_odds:
                    logger.info(f"遊戲 {game.game_id} 的盤口數據已存在，跳過")
                    continue

                # 創建新的盤口記錄
                odds_data = {}

                # 處理讓分盤數據 - 支持多種數據格式
                # 1. 現代化SBR格式：spreads字段
                if 'spreads' in scraped_game:
                    spreads = scraped_game['spreads']
                    odds_data.update({
                        'home_spread_point': spreads.get('home_spread_point'),
                        'home_spread_price': spreads.get('home_spread_price', -110),
                        'away_spread_point': spreads.get('away_spread_point'),
                        'away_spread_price': spreads.get('away_spread_price', -110)
                    })

                # 2. covers.com格式：odds.spread_line
                odds_info = scraped_game.get('odds', {})
                if odds_info.get('spread_line'):
                    try:
                        spread_line = float(odds_info['spread_line'])
                        odds_data.update({
                            'home_spread_point': spread_line,
                            'home_spread_price': -110,
                            'away_spread_point': -spread_line,
                            'away_spread_price': -110
                        })
                    except (ValueError, TypeError):
                        logger.warning(f"無效的讓分線數據: {odds_info['spread_line']}")

                # 處理總分盤數據 - 支持多種數據格式
                # 1. 現代化SBR格式：totals字段
                if 'totals' in scraped_game:
                    totals = scraped_game['totals']
                    odds_data.update({
                        'total_point': totals.get('total_point'),
                        'over_price': totals.get('over_price', -110),
                        'under_price': totals.get('under_price', -110)
                    })

                # 2. covers.com格式：odds.total_line
                if odds_info.get('total_line'):
                    try:
                        total_line = float(odds_info['total_line'])
                        if 5.0 <= total_line <= 15.0:  # 合理範圍檢查
                            odds_data.update({
                                'total_point': total_line,
                                'over_price': -110,
                                'under_price': -110
                            })
                    except (ValueError, TypeError):
                        logger.warning(f"無效的總分線數據: {odds_info['total_line']}")

                # 創建BettingOdds記錄
                betting_odds = BettingOdds(
                    game_id=game.game_id,  # 修復：使用game.game_id而不是game.id
                    bookmaker=scraped_game.get('bookmaker', 'bet365'),
                    market_type='both',  # 包含讓分盤和總分盤
                    **odds_data
                )

                db.session.add(betting_odds)
                saved_count += 1

            except Exception as e:
                logger.error(f"保存單場遊戲盤口數據失敗: {e}")
                continue

        if saved_count > 0:
            db.session.commit()
            logger.info(f"成功保存 {saved_count} 場遊戲的盤口數據")

        return {
            'success': True,
            'total_processed': saved_count,
            'source': 'sportsbookreview'
        }

    except Exception as e:
        db.session.rollback()
        logger.error(f"保存網頁抓取盤口數據失敗: {e}")
        return {
            'success': False,
            'error': str(e)
        }


