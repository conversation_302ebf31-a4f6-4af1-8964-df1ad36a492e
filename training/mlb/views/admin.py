@admin_bp.route('/advanced-backtest')
def advanced_backtest():
    """進階回測介面"""
    today = date.today().isoformat()
    return render_template('admin/advanced_backtest.html', today=today)

@admin_bp.route('/api/games-for-date')
def api_games_for_date():
    """API: 根據日期獲取比賽列表"""
    target_date_str = request.args.get('date')
    if not target_date_str:
        return jsonify({'error': '請提供日期'}), 400

    try:
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        games = Game.query.filter_by(date=target_date).order_by(Game.game_id).all()
        
        games_data = [
            {
                'game_id': game.game_id,
                'home_team': game.home_team,
                'away_team': game.away_team,
                'status': game.game_status,
                'score': f'{game.away_score} - {game.home_score}' if game.game_status == 'completed' else 'N/A'
            } for game in games
        ]
        return jsonify(games_data)

    except Exception as e:
        logger.error(f"API獲取比賽列表失敗: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/run-backtest', methods=['POST'])
def api_run_backtest():
    """API: 執行單場比賽的回測"""
    game_id = request.json.get('game_id')
    if not game_id:
        return jsonify({'error': '請提供 game_id'}), 400

    try:
        # 獲取比賽基本資訊
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({'error': '找不到比賽'}), 404

        # 1. 執行智能對戰分析
        analyzer = MatchupAnalyzer(game.game_id, session=db.session)
        matchup_analysis = analyzer.analyze()

        # 2. 執行預測
        from models.prediction_service import PredictionService
        prediction_service = PredictionService()
        # 我們需要一個方法來預測單場比賽並傳入 matchup_analysis
        # 暫時我們手動組合這個流程
        features = prediction_service.feature_engineer.extract_comprehensive_features(
            game.home_team, game.away_team, game.date, matchup_analysis=matchup_analysis
        )
        prediction_data = prediction_service.predictor.predict_game(
            game.home_team, game.away_team, game.date, matchup_analysis=matchup_analysis
        )

        # 3. 組合回測報告
        report = {
            'game_info': {
                'home_team': game.home_team,
                'away_team': game.away_team,
                'date': game.date.isoformat(),
                'actual_score': f'{game.away_score} - {game.home_score}' if game.game_status == 'completed' else 'N/A'
            },
            'matchup_analysis': matchup_analysis,
            'prediction_result': prediction_data
        }

        return jsonify(report)

    except Exception as e:
        logger.error(f"API執行回測失敗: {e}")
        return jsonify({'error': str(e)}), 500