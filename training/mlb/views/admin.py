"""
管理員功能模組
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from datetime import date, datetime, timedelta
import logging
import json

from models.database import db, Game, Team, Prediction, Player, PlayerStats
from models.prediction_service import PredictionService
from models.automated_predictor import AutomatedPredictor
from models.data_fetcher import MLBDataFetcher

logger = logging.getLogger(__name__)

# 創建 Blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
def admin_dashboard():
    """管理面板主頁"""
    # 獲取系統狀態
    system_status = {
        'scheduler_running': True,  # 假設調度器正在運行
        'database_status': 'healthy',
        'api_status': 'online',
        'last_update': '2025-07-18 16:55'
    }

    # 獲取統計數據
    stats = {
        'total_games': Game.query.count(),
        'total_teams': Team.query.count(),
        'total_predictions': Prediction.query.count(),
        'total_players': Player.query.count()
    }

    return render_template('admin/dashboard.html',
                         system_status=system_status,
                         stats=stats)

@admin_bp.route('/advanced-backtest')
def advanced_backtest():
    """進階回測介面"""
    today = date.today().isoformat()
    return render_template('admin/advanced_backtest.html', today=today)

@admin_bp.route('/prediction-methodology')
def prediction_methodology():
    """預測方法論說明頁面"""
    return render_template('admin/prediction_methodology.html')

@admin_bp.route('/daily-operations')
def daily_operations():
    """日常操作頁面"""
    return render_template('admin/daily_operations.html')

@admin_bp.route('/data-management')
def data_management():
    """數據管理頁面"""
    return render_template('admin/data_management.html')

@admin_bp.route('/model-predictions')
def model_predictions():
    """模型與預測頁面"""
    return render_template('admin/model_predictions.html')

@admin_bp.route('/system-management')
def system_management():
    """系統設定頁面"""
    return render_template('admin/system_management.html')

@admin_bp.route('/api/games-for-date')
def api_games_for_date():
    """API: 根據日期獲取比賽列表"""
    target_date_str = request.args.get('date')
    if not target_date_str:
        return jsonify({'error': '請提供日期'}), 400

    try:
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        games = Game.query.filter_by(date=target_date).order_by(Game.game_id).all()
        
        games_data = [
            {
                'game_id': game.game_id,
                'home_team': game.home_team,
                'away_team': game.away_team,
                'status': game.game_status,
                'score': f'{game.away_score} - {game.home_score}' if game.game_status == 'completed' else 'N/A'
            } for game in games
        ]
        return jsonify(games_data)

    except Exception as e:
        logger.error(f"API獲取比賽列表失敗: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/api/run-backtest', methods=['POST'])
def api_run_backtest():
    """API: 執行單場比賽的回測"""
    game_id = request.json.get('game_id')
    if not game_id:
        return jsonify({'error': '請提供 game_id'}), 400

    try:
        # 獲取比賽基本資訊
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({'error': '找不到比賽'}), 404

        # 1. 執行智能對戰分析
        analyzer = MatchupAnalyzer(game.game_id, session=db.session)
        matchup_analysis = analyzer.analyze()

        # 2. 執行預測
        from models.prediction_service import PredictionService
        prediction_service = PredictionService()
        # 我們需要一個方法來預測單場比賽並傳入 matchup_analysis
        # 暫時我們手動組合這個流程
        features = prediction_service.feature_engineer.extract_comprehensive_features(
            game.home_team, game.away_team, game.date, matchup_analysis=matchup_analysis
        )
        prediction_data = prediction_service.predictor.predict_game(
            game.home_team, game.away_team, game.date, matchup_analysis=matchup_analysis
        )

        # 3. 組合回測報告
        report = {
            'game_info': {
                'home_team': game.home_team,
                'away_team': game.away_team,
                'date': game.date.isoformat(),
                'actual_score': f'{game.away_score} - {game.home_score}' if game.game_status == 'completed' else 'N/A'
            },
            'matchup_analysis': matchup_analysis,
            'prediction_result': prediction_data
        }

        return jsonify(report)

    except Exception as e:
        logger.error(f"API執行回測失敗: {e}")
        return jsonify({'error': str(e)}), 500