from flask import Blueprint, render_template, request, jsonify, abort
from models.database import Team, TeamStats, Game, PlayerStats, db
from datetime import datetime, date
from sqlalchemy import func

teams_bp = Blueprint('teams', __name__)

@teams_bp.route('/')
def teams_list():
    """球隊列表頁面"""
    teams = Team.query.order_by(Team.league, Team.division, Team.team_name).all()
    return render_template('teams.html', teams=teams)

@teams_bp.route('/<int:team_id>')
def team_detail(team_id):
    """球隊詳情頁面"""
    team = Team.query.filter_by(team_id=team_id).first_or_404()
    
    # 獲取球隊統計
    current_season = datetime.now().year
    team_stats = TeamStats.query.filter_by(
        team_id=team_id, 
        season=current_season
    ).first()
    
    # 獲取最近已完成的比賽
    recent_games = Game.query.filter(
        ((Game.home_team == team.team_code) | (Game.away_team == team.team_code)) &
        (Game.game_status == 'completed')
    ).order_by(Game.date.desc()).limit(10).all()
    
    # 獲取主客場記錄
    home_games = Game.query.filter_by(home_team=team.team_code, game_status='completed').all()
    away_games = Game.query.filter_by(away_team=team.team_code, game_status='completed').all()

    home_wins = sum(1 for game in home_games if game.home_score is not None and game.away_score is not None and game.home_score > game.away_score)
    away_wins = sum(1 for game in away_games if game.away_score is not None and game.home_score is not None and game.away_score > game.home_score)
    
    record = {
        'home_wins': home_wins,
        'home_losses': len(home_games) - home_wins,
        'away_wins': away_wins,
        'away_losses': len(away_games) - away_wins,
        'total_wins': home_wins + away_wins,
        'total_losses': (len(home_games) - home_wins) + (len(away_games) - away_wins)
    }
    
    return render_template('team_detail.html', 
                         team=team, 
                         team_stats=team_stats,
                         recent_games=recent_games,
                         record=record)

@teams_bp.route('/<int:team_id>/stats')
def team_stats(team_id):
    """球隊統計頁面"""
    team = Team.query.filter_by(team_id=team_id).first_or_404()
    
    # 獲取多個賽季的統計
    stats_by_season = TeamStats.query.filter_by(team_id=team_id).order_by(
        TeamStats.season.desc()
    ).all()
    
    return render_template('team_stats.html', 
                         team=team, 
                         stats_by_season=stats_by_season)

@teams_bp.route('/<int:team_id>/players')
def team_players(team_id):
    """球隊球員頁面"""
    team = Team.query.filter_by(team_id=team_id).first_or_404()
    
    # 獲取球員統計
    current_season = datetime.now().year
    players = PlayerStats.query.filter_by(
        team_id=team_id, 
        season=current_season
    ).order_by(PlayerStats.batting_avg.desc()).all()
    
    # 分離打者和投手
    batters = [p for p in players if p.at_bats > 0]
    pitchers = [p for p in players if p.innings_pitched > 0]
    
    return render_template('team_players.html', 
                         team=team, 
                         batters=batters,
                         pitchers=pitchers)

@teams_bp.route('/<int:team_id>/games')
def team_games(team_id):
    """球隊比賽記錄"""
    team = Team.query.filter_by(team_id=team_id).first_or_404()
    
    page = request.args.get('page', 1, type=int)
    season = request.args.get('season', datetime.now().year, type=int)
    
    # 獲取球隊的比賽
    games_query = Game.query.filter(
        ((Game.home_team == team.team_code) | (Game.away_team == team.team_code)) &
        (func.extract('year', Game.date) == season)
    ).order_by(Game.date.desc())
    
    games = games_query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('team_games.html', 
                         team=team, 
                         games=games,
                         season=season)

@teams_bp.route('/api/<int:team_id>/summary')
def api_team_summary(team_id):
    """API端點：球隊摘要"""
    team = Team.query.filter_by(team_id=team_id).first_or_404()
    
    # 獲取基本統計
    current_season = datetime.now().year
    team_stats = TeamStats.query.filter_by(
        team_id=team_id, 
        season=current_season
    ).first()
    
    # 計算最近表現
    recent_games = Game.query.filter(
        (Game.home_team == team.team_code) | (Game.away_team == team.team_code),
        Game.game_status == 'completed'
    ).order_by(Game.date.desc()).limit(10).all()
    
    recent_wins = 0
    for game in recent_games:
        if game.home_team == team.team_code:
            if game.home_score > game.away_score:
                recent_wins += 1
        else:
            if game.away_score > game.home_score:
                recent_wins += 1
    
    summary = {
        'team_id': team.team_id,
        'team_code': team.team_code,
        'team_name': team.team_name,
        'league': team.league,
        'division': team.division,
        'venue': team.venue_name,
        'stats': team_stats.to_dict() if team_stats else None,
        'recent_form': {
            'wins': recent_wins,
            'games': len(recent_games),
            'win_rate': recent_wins / len(recent_games) if recent_games else 0
        }
    }
    
    return jsonify(summary)

@teams_bp.route('/api/standings')
def api_standings():
    """API端點：聯盟排名"""
    current_season = datetime.now().year
    
    # 獲取所有球隊統計
    stats = db.session.query(Team, TeamStats).join(
        TeamStats, Team.team_id == TeamStats.team_id
    ).filter(TeamStats.season == current_season).all()
    
    # 按聯盟和分區分組
    standings = {}
    for team, stat in stats:
        league = team.league
        division = team.division
        
        if league not in standings:
            standings[league] = {}
        if division not in standings[league]:
            standings[league][division] = []
        
        standings[league][division].append({
            'team_code': team.team_code,
            'team_name': team.team_name,
            'wins': stat.wins,
            'losses': stat.losses,
            'win_percentage': stat.win_percentage,
            'games_behind': 0  # 需要計算
        })
    
    # 排序每個分區
    for league in standings:
        for division in standings[league]:
            standings[league][division].sort(
                key=lambda x: x['win_percentage'], 
                reverse=True
            )
            
            # 計算落後場次
            if standings[league][division]:
                leader_pct = standings[league][division][0]['win_percentage']
                for team in standings[league][division]:
                    games_behind = (leader_pct - team['win_percentage']) * 162 / 2
                    team['games_behind'] = round(games_behind, 1)
    
    return jsonify(standings)
