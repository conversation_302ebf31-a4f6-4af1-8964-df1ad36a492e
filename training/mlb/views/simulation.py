"""
模擬功能視圖
用於模擬特定日期的預測情況，測試模型表現
"""

from flask import Blueprint, render_template, request, jsonify, current_app
from datetime import datetime, date, timedelta
import logging

from models.database import db, Game, Prediction, Team
from models.optimized_predictor import OptimizedMLBPredictor

logger = logging.getLogger(__name__)

simulation_bp = Blueprint('simulation', __name__)

@simulation_bp.route('/')
def simulation_index():
    """模擬功能主頁"""
    return render_template('simulation/index.html')

@simulation_bp.route('/date_simulation')
def date_simulation():
    """日期模擬頁面"""
    selected_date = request.args.get('date', '2025-06-30')
    
    try:
        # 解析日期
        target_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
        
        # 獲取該日期的比賽
        games = Game.query.filter(Game.date == target_date).all()
        
        # 獲取該日期的預測
        predictions = []
        for game in games:
            pred = Prediction.query.filter_by(game_id=game.game_id).first()
            if pred:
                predictions.append({
                    'game': game,
                    'prediction': pred,
                    'home_team_name': get_team_name(game.home_team),
                    'away_team_name': get_team_name(game.away_team)
                })
        
        # 計算統計信息
        stats = calculate_simulation_stats(games, predictions)
        
        return render_template('simulation/date_simulation.html',
                             selected_date=selected_date,
                             target_date=target_date,
                             games=games,
                             predictions=predictions,
                             stats=stats)
        
    except ValueError:
        return render_template('simulation/date_simulation.html',
                             error="日期格式錯誤，請使用 YYYY-MM-DD 格式")
    except Exception as e:
        logger.error(f"日期模擬失敗: {e}")
        return render_template('simulation/date_simulation.html',
                             error=f"模擬失敗: {str(e)}")

@simulation_bp.route('/api/simulate_date', methods=['POST'])
def api_simulate_date():
    """API: 模擬特定日期的預測"""
    try:
        data = request.get_json()
        date_str = data.get('date')
        
        if not date_str:
            return jsonify({'success': False, 'error': '請提供日期'}), 400
        
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # 初始化預測器 (延遲導入避免循環導入)
        try:
            from models.unified_betting_predictor import UnifiedBettingPredictor
            predictor = UnifiedBettingPredictor()
        except ImportError as e:
            return jsonify({
                'success': False,
                'error': f'預測器導入失敗: {str(e)}'
            }), 500

        # 模擬該日期的預測生成 (使用快速版本)
        result = simulate_prediction_generation_fast(target_date)
        
        return jsonify({
            'success': True,
            'date': date_str,
            'simulation_result': result
        })
        
    except Exception as e:
        logger.error(f"模擬預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@simulation_bp.route('/api/compare_models', methods=['POST'])
def api_compare_models():
    """API: 比較不同模型在特定日期的表現"""
    try:
        data = request.get_json()
        date_str = data.get('date')
        
        if not date_str:
            return jsonify({'success': False, 'error': '請提供日期'}), 400
        
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # 比較不同模型版本的表現
        comparison_result = compare_model_performance(target_date)
        
        return jsonify({
            'success': True,
            'date': date_str,
            'comparison': comparison_result
        })
        
    except Exception as e:
        logger.error(f"模型比較失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@simulation_bp.route('/model_testing')
def model_testing():
    """模型測試頁面"""
    return render_template('simulation/model_testing.html')

@simulation_bp.route('/historical_analysis')
def historical_analysis():
    """歷史分析頁面"""
    days_back = request.args.get('days', 30, type=int)
    
    try:
        # 獲取歷史數據
        end_date = date.today()
        start_date = end_date - timedelta(days=days_back)
        
        historical_data = get_historical_performance(start_date, end_date)
        
        return render_template('simulation/historical_analysis.html',
                             historical_data=historical_data,
                             days_back=days_back,
                             start_date=start_date,
                             end_date=end_date)
        
    except Exception as e:
        logger.error(f"歷史分析失敗: {e}")
        return render_template('simulation/historical_analysis.html',
                             error=f"分析失敗: {str(e)}")

def get_team_name(team_code):
    """獲取球隊名稱"""
    team = Team.query.filter_by(team_code=team_code).first()
    return team.team_name if team else team_code

def calculate_simulation_stats(games, predictions):
    """計算模擬統計信息"""
    stats = {
        'total_games': len(games),
        'games_with_predictions': len(predictions),
        'prediction_coverage': len(predictions) / len(games) * 100 if games else 0,
        'completed_games': len([g for g in games if g.game_status == 'completed']),
        'scheduled_games': len([g for g in games if g.game_status == 'scheduled']),
        'postponed_games': len([g for g in games if g.game_status == 'postponed'])
    }
    
    # 如果有已完成的比賽，計算準確率
    if stats['completed_games'] > 0:
        correct_predictions = 0
        total_evaluated = 0
        
        for pred_data in predictions:
            game = pred_data['game']
            pred = pred_data['prediction']
            
            if game.game_status == 'completed' and game.home_score is not None and game.away_score is not None:
                total_evaluated += 1
                
                # 檢查勝負預測是否正確
                actual_home_win = game.home_score > game.away_score
                predicted_home_win = pred.predicted_home_score > pred.predicted_away_score
                
                if actual_home_win == predicted_home_win:
                    correct_predictions += 1
        
        stats['accuracy'] = correct_predictions / total_evaluated * 100 if total_evaluated > 0 else 0
        stats['evaluated_games'] = total_evaluated
    else:
        stats['accuracy'] = None
        stats['evaluated_games'] = 0
    
    return stats

def simulate_prediction_generation_fast(target_date):
    """快速模擬預測生成過程 - 僅統計不詳細分析"""
    try:
        # 使用單一查詢獲取統計數據
        from sqlalchemy import func

        # 獲取該日期的比賽統計 - 直接使用 Game.query
        total_games = Game.query.filter(Game.date == target_date).count()

        # 獲取預測統計 - 使用子查詢
        game_ids = db.session.query(Game.game_id).filter(Game.date == target_date).subquery()
        total_predictions = Prediction.query.filter(Prediction.game_id.in_(
            db.session.query(game_ids.c.game_id)
        )).count()

        return {
            'status': 'success',
            'games_count': total_games,
            'summary': {
                'total_games': total_games,
                'games_with_predictions': total_predictions,
                'successful_simulations': total_predictions,
                'prediction_coverage': (total_predictions / total_games * 100) if total_games > 0 else 0
            },
            'message': f'快速模擬完成: {total_games}場比賽, {total_predictions}個預測'
        }

    except Exception as e:
        logger.error(f"快速模擬失敗: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'games_count': 0
        }

def simulate_prediction_generation(target_date):
    """模擬預測生成過程 - 優化版本"""
    try:
        # 獲取該日期的比賽
        games = Game.query.filter(Game.date == target_date).all()

        if not games:
            return {
                'status': 'no_games',
                'message': f'{target_date} 沒有找到比賽',
                'games_count': 0
            }

        # 批量獲取所有預測記錄 (性能優化)
        game_ids = [game.game_id for game in games]
        predictions = Prediction.query.filter(Prediction.game_id.in_(game_ids)).all()

        # 建立預測字典以快速查找
        prediction_dict = {pred.game_id: pred for pred in predictions}

        # 快速生成模擬結果
        simulation_results = []

        for game in games:
            existing_pred = prediction_dict.get(game.game_id)

            game_result = {
                'game_id': game.game_id,
                'home_team': game.home_team,
                'away_team': game.away_team,
                'game_status': game.game_status,
                'has_existing_prediction': existing_pred is not None,
                'simulation_status': 'success'
            }

            if existing_pred:
                game_result['prediction_details'] = {
                    'home_score': existing_pred.predicted_home_score,
                    'away_score': existing_pred.predicted_away_score,
                    'win_probability': existing_pred.home_win_probability,
                    'model_version': existing_pred.model_version
                }
            else:
                game_result['simulation_status'] = 'no_prediction'
                game_result['message'] = '沒有找到預測記錄'

            simulation_results.append(game_result)

        return {
            'status': 'completed',
            'games_count': len(games),
            'results': simulation_results,
            'summary': {
                'total_games': len(games),
                'successful_simulations': len([r for r in simulation_results if r['simulation_status'] == 'success']),
                'games_with_predictions': len([r for r in simulation_results if r.get('has_existing_prediction', False)])
            }
        }
        
    except Exception as e:
        logger.error(f"模擬預測生成失敗: {e}")
        return {
            'status': 'error',
            'error': str(e)
        }

def compare_model_performance(target_date):
    """比較不同模型版本的表現 - 優化版本"""
    try:
        # 使用單一查詢獲取所有相關數據 (性能優化)
        from sqlalchemy.orm import joinedload

        # 批量獲取該日期的所有遊戲和預測
        games_with_predictions = db.session.query(Game)\
            .options(joinedload(Game.predictions))\
            .filter(Game.date == target_date)\
            .all()

        # 快速統計各版本表現
        version_stats = {}

        for game in games_with_predictions:
            for pred in game.predictions:
                version = pred.model_version or 'unknown'

                if version not in version_stats:
                    version_stats[version] = {
                        'total': 0,
                        'evaluated': 0,
                        'correct': 0
                    }

                version_stats[version]['total'] += 1

                # 只對已完成的比賽計算準確率
                if (game.game_status == 'completed' and
                    game.home_score is not None and
                    game.away_score is not None and
                    pred.predicted_home_score is not None and
                    pred.predicted_away_score is not None):

                    version_stats[version]['evaluated'] += 1

                    actual_home_win = game.home_score > game.away_score
                    predicted_home_win = pred.predicted_home_score > pred.predicted_away_score

                    if actual_home_win == predicted_home_win:
                        version_stats[version]['correct'] += 1

        # 轉換為最終格式
        version_performance = {}
        for version, stats in version_stats.items():
            version_performance[version] = {
                'total_predictions': stats['total'],
                'evaluated_games': stats['evaluated'],
                'correct_predictions': stats['correct'],
                'accuracy': (stats['correct'] / stats['evaluated'] * 100) if stats['evaluated'] > 0 else 0
            }
        
        return {
            'date': target_date.isoformat(),
            'model_versions': list(version_performance.keys()),
            'performance': version_performance,
            'total_games': len(games_with_predictions)
        }
        
    except Exception as e:
        logger.error(f"模型比較失敗: {e}")
        return {
            'error': str(e)
        }

def get_historical_performance(start_date, end_date):
    """獲取歷史表現數據"""
    try:
        # 獲取日期範圍內的所有預測
        games = Game.query.filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.game_status == 'completed'
        ).all()
        
        daily_performance = {}
        
        for game in games:
            game_date = game.date.isoformat()
            
            if game_date not in daily_performance:
                daily_performance[game_date] = {
                    'total_games': 0,
                    'games_with_predictions': 0,
                    'correct_predictions': 0,
                    'accuracy': 0
                }
            
            daily_performance[game_date]['total_games'] += 1
            
            # 檢查預測
            pred = Prediction.query.filter_by(game_id=game.game_id).first()
            if pred and game.home_score is not None and game.away_score is not None:
                daily_performance[game_date]['games_with_predictions'] += 1
                
                actual_home_win = game.home_score > game.away_score
                predicted_home_win = pred.predicted_home_score > pred.predicted_away_score
                
                if actual_home_win == predicted_home_win:
                    daily_performance[game_date]['correct_predictions'] += 1
        
        # 計算每日準確率
        for date_str, data in daily_performance.items():
            if data['games_with_predictions'] > 0:
                data['accuracy'] = data['correct_predictions'] / data['games_with_predictions'] * 100
        
        return {
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
            'daily_performance': daily_performance,
            'summary': calculate_overall_summary(daily_performance)
        }
        
    except Exception as e:
        logger.error(f"獲取歷史表現失敗: {e}")
        return {
            'error': str(e)
        }

def calculate_overall_summary(daily_performance):
    """計算整體摘要統計"""
    total_games = sum(data['total_games'] for data in daily_performance.values())
    total_predictions = sum(data['games_with_predictions'] for data in daily_performance.values())
    total_correct = sum(data['correct_predictions'] for data in daily_performance.values())
    
    return {
        'total_games': total_games,
        'total_predictions': total_predictions,
        'total_correct': total_correct,
        'overall_accuracy': total_correct / total_predictions * 100 if total_predictions > 0 else 0,
        'prediction_coverage': total_predictions / total_games * 100 if total_games > 0 else 0,
        'active_days': len([d for d in daily_performance.values() if d['total_games'] > 0])
    }

@simulation_bp.route('/api/optimized_simulate', methods=['POST'])
def api_optimized_simulate():
    """API: 使用優化預測器進行模擬"""
    try:
        data = request.get_json()
        date_str = data.get('date')
        
        if not date_str:
            return jsonify({'success': False, 'error': '請提供日期'}), 400
        
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # 使用優化預測器
        from flask import current_app
        predictor = OptimizedMLBPredictor(current_app)
        
        # 訓練模型
        training_result = predictor.train_models(target_date)
        if not training_result['success']:
            return jsonify({
                'success': False,
                'error': f'模型訓練失敗: {training_result.get("error")}'
            }), 500
        
        # 獲取該日期的比賽
        games = Game.query.filter(Game.date == target_date).all()
        
        results = []
        for game in games:
            prediction = predictor.predict_game(
                game.home_team, game.away_team, game.date
            )
            
            if prediction['success']:
                results.append({
                    'game_id': game.game_id,
                    'teams': f"{game.away_team} @ {game.home_team}",
                    'prediction': prediction['predictions'],
                    'model_info': prediction['model_info']
                })
        
        return jsonify({
            'success': True,
            'date': date_str,
            'training_stats': training_result['training_stats'],
            'predictions': results,
            'total_games': len(results)
        })
        
    except Exception as e:
        logger.error(f"優化模擬失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

