from flask import Blueprint, render_template, request, jsonify
from models.database import Game, Team, Prediction, db
from models.prediction_service import PredictionService
from models.enhanced_feature_engineer import EnhancedFeatureEngineer
# OverUnderPredictor 將在需要時延遲導入
from datetime import datetime, date, timedelta
from sqlalchemy import or_, and_, func
import json
import logging

predictions_bp = Blueprint('predictions', __name__)

# 初始化預測服務
prediction_service = PredictionService()
prediction_service.initialize_service()

@predictions_bp.route('/')
def predictions_list():
    """預測列表頁面"""
    page = request.args.get('page', 1, type=int)
    selected_date = request.args.get('date', '')
    
    # 建立查詢 - 獲取有預測的比賽
    query = db.session.query(Game, Prediction).join(
        Prediction, Game.game_id == Prediction.game_id
    )
    
    # 日期篩選
    if selected_date:
        try:
            filter_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            query = query.filter(Game.date == filter_date)
        except ValueError:
            pass
    else:
        # 默認顯示今天和未來的預測
        query = query.filter(Game.date >= date.today())
    
    # 分頁
    predictions = query.order_by(Game.date.asc(), Game.game_id.asc()).paginate(
        page=page, per_page=12, error_out=False
    )
    
    return render_template('predictions.html',
                         predictions=predictions.items,
                         pagination=predictions,
                         selected_date=selected_date)

@predictions_bp.route('/<game_id>')
def prediction_detail(game_id):
    """預測詳情頁面"""
    game = Game.query.filter_by(game_id=game_id).first_or_404()
    prediction = Prediction.query.filter_by(game_id=game_id).first()

    if not prediction:
        # 如果沒有預測，創建一個基本預測
        prediction = create_basic_prediction(game)

    # 獲取球隊信息
    home_team = Team.query.filter_by(team_code=game.home_team).first()
    away_team = Team.query.filter_by(team_code=game.away_team).first()

    # 獲取預測分析
    analysis = analyze_prediction(game, prediction, home_team, away_team)

    # 🆕 獲取投手狀態信息
    features = get_pitcher_status_features(game, game_id)

    return render_template('prediction_detail_enhanced.html',
                         game=game,
                         prediction=prediction,
                         home_team=home_team,
                         away_team=away_team,
                         analysis=analysis,
                         features=features)

@predictions_bp.route('/api/<game_id>')
def api_prediction(game_id):
    """API端點：獲取預測"""
    prediction = Prediction.query.filter_by(game_id=game_id).first()
    
    if not prediction:
        return jsonify({'error': '找不到預測'}), 404
    
    return jsonify(prediction.to_dict())

@predictions_bp.route('/api/today')
def api_todays_predictions():
    """API端點：今日預測"""
    today = date.today()
    
    predictions = db.session.query(Game, Prediction).join(
        Prediction, Game.game_id == Prediction.game_id
    ).filter(Game.date == today).all()
    
    result = []
    for game, prediction in predictions:
        game_data = game.to_dict()
        game_data['prediction'] = prediction.to_dict()
        result.append(game_data)
    
    return jsonify(result)

@predictions_bp.route('/custom')
def custom_prediction():
    """自定義預測頁面"""
    return render_template('custom_prediction.html')

@predictions_bp.route('/accuracy')
def prediction_accuracy():
    """預測準確率統計"""
    # 使用新的預測服務獲取準確率統計
    accuracy_stats = prediction_service.get_prediction_accuracy_stats(days=30)

    return render_template('prediction_accuracy.html',
                         stats=accuracy_stats)

@predictions_bp.route('/api/generate_daily')
def api_generate_daily_predictions():
    """API端點：生成每日預測"""
    target_date_str = request.args.get('date')

    if target_date_str:
        try:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式錯誤'}), 400
    else:
        target_date = date.today()

    result = prediction_service.generate_daily_predictions(target_date)
    return jsonify(result)

@predictions_bp.route('/api/custom_predict')
def api_custom_predict():
    """API端點：自定義日期預測"""
    try:
        home_team = request.args.get('home_team', '').upper()
        away_team = request.args.get('away_team', '').upper()
        game_date = request.args.get('date', '')

        if not all([home_team, away_team, game_date]):
            return jsonify({
                'error': '缺少必要參數',
                'required': ['home_team', 'away_team', 'date']
            }), 400

        # 解析日期
        try:
            prediction_date = datetime.strptime(game_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式錯誤，請使用 YYYY-MM-DD'}), 400

        # 驗證球隊代碼
        valid_teams = ['LAA', 'OAK', 'NYY', 'BOS', 'LAD', 'SF', 'HOU', 'TEX', 'SEA', 'MIN',
                      'CLE', 'DET', 'KC', 'CWS', 'TB', 'TOR', 'BAL', 'ATL', 'NYM', 'PHI',
                      'WSH', 'MIA', 'MIL', 'CHC', 'STL', 'CIN', 'PIT', 'COL', 'AZ', 'SD']

        if home_team not in valid_teams or away_team not in valid_teams:
            return jsonify({
                'error': '無效的球隊代碼',
                'valid_teams': valid_teams
            }), 400

        if home_team == away_team:
            return jsonify({'error': '主隊和客隊不能相同'}), 400

        # 使用改進的預測器進行預測
        from models.improved_predictor import ImprovedMLBPredictor
        import numpy as np

        predictor = ImprovedMLBPredictor()
        result = predictor.predict_game_advanced(home_team, away_team, prediction_date)

        # 轉換numpy類型為Python原生類型以便JSON序列化
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            return obj

        # 清理結果數據
        cleaned_result = convert_numpy_types(result)

        return jsonify({
            'success': True,
            'prediction': cleaned_result,
            'request': {
                'home_team': home_team,
                'away_team': away_team,
                'date': game_date
            }
        })

    except Exception as e:
        return jsonify({
            'error': f'預測失敗: {str(e)}',
            'success': False
        }), 500

@predictions_bp.route('/api/ml_prediction/<game_id>')
def api_ml_prediction(game_id):
    """API端點：獲取ML預測"""
    prediction_data = prediction_service.get_prediction_for_game(game_id)

    if not prediction_data:
        return jsonify({'error': '找不到預測或無法生成預測'}), 404

    return jsonify(prediction_data)

@predictions_bp.route('/api/accuracy_stats')
def api_accuracy_stats():
    """API端點：獲取準確率統計"""
    days = request.args.get('days', 30, type=int)
    stats = prediction_service.get_prediction_accuracy_stats(days)
    return jsonify(stats)

@predictions_bp.route('/over_under')
def over_under_predictions():
    """大小分預測頁面"""
    target_date_str = request.args.get('date')

    if target_date_str:
        try:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        except ValueError:
            target_date = date.today()
    else:
        target_date = date.today()

    # 獲取當日比賽的預測
    predictions = Prediction.query.join(Game).filter(
        Game.date == target_date,
        Prediction.predicted_total_runs.isnot(None)
    ).all()

    # 計算統計數據
    over_recommendations = sum(1 for p in predictions if p.over_probability and p.over_probability > 0.6)
    under_recommendations = sum(1 for p in predictions if p.under_probability and p.under_probability > 0.6)
    average_confidence = sum(p.over_under_confidence or 0.5 for p in predictions) / len(predictions) * 100 if predictions else 0

    return render_template('over_under_predictions.html',
                         predictions=predictions,
                         target_date=target_date,
                         over_recommendations=over_recommendations,
                         under_recommendations=under_recommendations,
                         average_confidence=average_confidence)

@predictions_bp.route('/api/over_under_analysis/<game_id>')
def api_over_under_analysis(game_id):
    """API端點：獲取大小分分析"""
    try:
        # 獲取預測記錄
        prediction = Prediction.query.filter_by(game_id=game_id).first()
        if not prediction:
            return jsonify({'error': '找不到預測數據'}), 404

        # 解析投手對戰分析
        analysis_data = {}
        if prediction.pitcher_vs_batter_analysis:
            try:
                analysis_data = json.loads(prediction.pitcher_vs_batter_analysis)
            except:
                pass

        # 提取關鍵因素
        key_factors = []
        if analysis_data:
            matchup_summary = analysis_data.get('matchup_summary', {})
            key_matchups = matchup_summary.get('key_matchups', [])

            for matchup in key_matchups[:5]:  # 只顯示前5個
                if matchup.get('advantage') == 'batter':
                    key_factors.append(
                        f"{matchup['batter']} 對 {matchup['pitcher']} 有優勢 "
                        f"(打擊率 {matchup['batting_avg']:.3f})"
                    )
                elif matchup.get('advantage') == 'pitcher':
                    key_factors.append(
                        f"{matchup['pitcher']} 對 {matchup['batter']} 有優勢 "
                        f"(被打擊率 {matchup['batting_avg']:.3f})"
                    )

        if not key_factors:
            key_factors = ["基於聯盟平均數據進行預測", "雙方實力相當"]

        return jsonify({
            'key_factors': key_factors,
            'pitcher_info': {
                'home_pitcher': prediction.starting_pitcher_home,
                'away_pitcher': prediction.starting_pitcher_away
            },
            'matchup_advantage': prediction.pitcher_matchup_advantage,
            'confidence': prediction.over_under_confidence
        })

    except Exception as e:
        return jsonify({'error': f'分析數據載入失敗: {str(e)}'}), 500

@predictions_bp.route('/api/generate_over_under')
def api_generate_over_under():
    """API端點：生成大小分預測"""
    target_date_str = request.args.get('date')

    if target_date_str:
        try:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式錯誤'}), 400
    else:
        target_date = date.today()

    try:
        from models.over_under_predictor import OverUnderPredictor
        over_under_predictor = OverUnderPredictor()
        result = over_under_predictor.batch_predict_over_under(target_date)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'生成大小分預測失敗: {str(e)}'}), 500

def create_basic_prediction(game):
    """創建基本預測"""
    # 這裡可以實現簡單的預測邏輯
    # 目前返回一個基本的預測
    prediction = Prediction(
        game_id=game.game_id,
        predicted_home_score=5.0,
        predicted_away_score=4.0,
        home_win_probability=0.55,
        away_win_probability=0.45,
        confidence=0.6
    )
    
    return prediction

def analyze_prediction(game, prediction, home_team, away_team):
    """分析預測"""
    analysis = {
        'confidence': prediction.confidence_level if hasattr(prediction, 'confidence_level') else 0.5,
        'model_used': getattr(prediction, 'prediction_model', 'ML模型'),
        'key_factors': [],
        'risk_factors': [],
        'recommendation': ''
    }
    
    # 分析置信度
    confidence_level = getattr(prediction, 'confidence_level', getattr(prediction, 'confidence', 0.5))
    if confidence_level > 0.7:
        analysis['recommendation'] = '高信心預測'
    elif confidence_level > 0.5:
        analysis['recommendation'] = '中等信心預測'
    else:
        analysis['recommendation'] = '低信心預測，建議謹慎'
    
    # 關鍵因素
    if prediction.home_win_probability > 0.6:
        analysis['key_factors'].append(f'{home_team.team_name if home_team else game.home_team} 主場優勢明顯')
    
    if abs(prediction.predicted_home_score - prediction.predicted_away_score) > 2:
        analysis['key_factors'].append('預期比分差距較大')
    
    # 風險因素
    if confidence_level < 0.5:
        analysis['risk_factors'].append('預測信心度較低')
    
    if game.date == date.today():
        analysis['risk_factors'].append('當日比賽，變數較多')
    
    return analysis

def is_prediction_correct(game, prediction):
    """檢查預測是否正確"""
    if game.home_score is None or game.away_score is None:
        return False
    
    # 檢查勝負預測是否正確
    predicted_home_wins = prediction.home_win_probability > prediction.away_win_probability
    actual_home_wins = game.home_score > game.away_score
    
    return predicted_home_wins == actual_home_wins

def get_monthly_accuracy_stats():
    """獲取月度準確率統計"""
    # 簡化版本，返回空列表
    # 實際實現可以按月統計準確率
    return []

# ==================== 目標性博彩預測路由 ====================

@predictions_bp.route('/targeted_betting')
def targeted_betting_predictions():
    """目標性博彩預測頁面"""
    try:
        from models.targeted_betting_predictor import TargetedBettingPredictor

        predictor = TargetedBettingPredictor()

        # 獲取今日預測
        prediction_data = predictor.generate_targeted_predictions()

        if 'error' in prediction_data:
            return render_template('targeted_betting_predictions.html',
                                 error_message=prediction_data['error'])

        return render_template('targeted_betting_predictions.html',
                             prediction_data=prediction_data)

    except Exception as e:
        return render_template('targeted_betting_predictions.html',
                             error_message=f'載入預測失敗: {str(e)}')

@predictions_bp.route('/targeted_betting/weekly_targets')
def weekly_targets():
    """週預測目標頁面"""
    try:
        from models.targeted_betting_predictor import TargetedBettingPredictor

        predictor = TargetedBettingPredictor()
        week_data = predictor.get_prediction_targets_for_week()

        if 'error' in week_data:
            return jsonify(week_data), 500

        return jsonify(week_data)

    except Exception as e:
        return jsonify({'error': f'獲取週目標失敗: {str(e)}'}), 500

@predictions_bp.route('/api/targeted_betting/generate')
def generate_targeted_predictions():
    """API: 生成目標性預測"""
    try:
        from models.targeted_betting_predictor import TargetedBettingPredictor

        target_date_str = request.args.get('date')
        target_date = None

        if target_date_str:
            try:
                target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': '日期格式錯誤，請使用 YYYY-MM-DD'}), 400

        predictor = TargetedBettingPredictor()
        result = predictor.generate_targeted_predictions(target_date)

        return jsonify(result)

    except Exception as e:
        return jsonify({'error': f'生成目標性預測失敗: {str(e)}'}), 500

@predictions_bp.route('/api/targeted_betting/upcoming_games')
def get_upcoming_games_with_odds():
    """API: 獲取未來有盤口的比賽"""
    try:
        from models.targeted_betting_predictor import TargetedBettingPredictor

        days_ahead = request.args.get('days', 7, type=int)

        if days_ahead < 1 or days_ahead > 14:
            return jsonify({'error': '天數範圍必須在1-14之間'}), 400

        predictor = TargetedBettingPredictor()
        result = predictor.get_upcoming_games_with_odds(days_ahead)

        return jsonify(result)

    except Exception as e:
        return jsonify({'error': f'獲取未來比賽失敗: {str(e)}'}), 500

def get_pitcher_status_features(game, game_id):
    """獲取投手狀態特徵信息"""
    try:
        logger = logging.getLogger(__name__)
        feature_engineer = EnhancedFeatureEngineer()

        # 提取投手相關特徵
        features = feature_engineer.extract_comprehensive_features(
            home_team=game.home_team,
            away_team=game.away_team,
            game_date=game.date,
            game_id=game_id
        )

        # 只返回投手相關的特徵
        pitcher_features = {}

        # 投手姓名
        pitcher_features['home_probable_starter'] = features.get('home_probable_starter', '未知')
        pitcher_features['away_probable_starter'] = features.get('away_probable_starter', '未知')

        # 投手狀態
        pitcher_features['home_pitcher_status'] = features.get('home_pitcher_status', 'unknown')
        pitcher_features['away_pitcher_status'] = features.get('away_pitcher_status', 'unknown')

        # 投手數據狀態
        pitcher_features['home_pitcher_data_status'] = features.get('home_pitcher_data_status', 'unknown')
        pitcher_features['away_pitcher_data_status'] = features.get('away_pitcher_data_status', 'unknown')

        # 數據來源
        pitcher_features['home_pitcher_source'] = features.get('home_pitcher_source', 'unknown')
        pitcher_features['away_pitcher_source'] = features.get('away_pitcher_source', 'unknown')
        pitcher_features['home_pitcher_data_source'] = features.get('home_pitcher_data_source', 'unknown')
        pitcher_features['away_pitcher_data_source'] = features.get('away_pitcher_data_source', 'unknown')

        # 投手統計數據
        pitcher_features['home_pitcher_era'] = features.get('home_pitcher_era')
        pitcher_features['away_pitcher_era'] = features.get('away_pitcher_era')
        pitcher_features['home_pitcher_whip'] = features.get('home_pitcher_whip')
        pitcher_features['away_pitcher_whip'] = features.get('away_pitcher_whip')

        logger.info(f"獲取投手狀態特徵成功 - Game ID: {game_id}")
        return pitcher_features

    except Exception as e:
        logger.error(f"獲取投手狀態特徵失敗: {e}")
        return {
            'home_probable_starter': '獲取失敗',
            'away_probable_starter': '獲取失敗',
            'home_pitcher_status': 'error',
            'away_pitcher_status': 'error',
            'error_message': str(e)
        }
