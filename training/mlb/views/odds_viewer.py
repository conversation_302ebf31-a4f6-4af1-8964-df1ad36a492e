#!/usr/bin/env python3
"""
盤口查看模組 - 專門用於查看和分析賠率數據
"""

from flask import Blueprint, render_template, request, jsonify
from datetime import date, datetime, timedelta
from sqlalchemy import and_, or_, desc
from models.database import db, BettingOdds, Game
import logging

logger = logging.getLogger(__name__)

# 創建 Blueprint
odds_viewer_bp = Blueprint('odds_viewer', __name__, url_prefix='/odds')

@odds_viewer_bp.route('/')
@odds_viewer_bp.route('/dashboard')
def odds_dashboard():
    """盤口儀表板 - 主要查看頁面"""
    try:
        # 獲取最新的賠率數據，並加入比賽信息
        latest_odds = db.session.query(BettingOdds, Game).outerjoin(
            Game, BettingOdds.game_id == Game.game_id
        ).order_by(BettingOdds.created_at.desc()).limit(20).all()
        
        # 獲取可用的日期範圍
        date_range = db.session.query(
            db.func.min(db.func.date(BettingOdds.odds_time)).label('min_date'),
            db.func.max(db.func.date(BettingOdds.odds_time)).label('max_date')
        ).first()
        
        # 獲取可用的博彩商
        bookmakers = db.session.query(BettingOdds.bookmaker).distinct().all()
        bookmaker_list = [b[0] for b in bookmakers if b[0]]
        
        # 整理數據以包含比賽資訊
        odds_with_games = []
        for odd, game in latest_odds:
            odd_data = odd
            odd_data.game_info = game
            odds_with_games.append(odd_data)
        
        return render_template('odds/dashboard.html',
                             latest_odds=odds_with_games,
                             min_date=date_range.min_date,
                             max_date=date_range.max_date,
                             bookmakers=bookmaker_list,
                             today=date.today().isoformat())
    
    except Exception as e:
        logger.error(f"盤口儀表板錯誤: {e}")
        return render_template('odds/dashboard.html',
                             latest_odds=[],
                             min_date=None,
                             max_date=None,
                             bookmakers=[],
                             today=date.today().isoformat(),
                             error=str(e))

@odds_viewer_bp.route('/search')
def odds_search():
    """搜索和篩選盤口數據"""
    try:
        # 獲取查詢參數
        selected_date = request.args.get('date', date.today().isoformat())
        market_type = request.args.get('market_type', 'all')
        bookmaker = request.args.get('bookmaker', 'all')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        # 構建查詢
        query = BettingOdds.query
        
        # 按日期篩選
        if selected_date:
            target_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            query = query.filter(db.func.date(BettingOdds.odds_time) == target_date)
        
        # 按市場類型篩選
        if market_type != 'all':
            query = query.filter(BettingOdds.market_type == market_type)
        
        # 按博彩商篩選
        if bookmaker != 'all':
            query = query.filter(BettingOdds.bookmaker == bookmaker)
        
        # 加入比賽信息並排序分頁
        query = db.session.query(BettingOdds, Game).select_from(BettingOdds).outerjoin(
            Game, BettingOdds.game_id == Game.game_id
        )
        
        # 重新應用篩選條件
        if selected_date:
            target_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            query = query.filter(db.func.date(BettingOdds.odds_time) == target_date)
        
        if market_type != 'all':
            query = query.filter(BettingOdds.market_type == market_type)
        
        if bookmaker != 'all':
            query = query.filter(BettingOdds.bookmaker == bookmaker)
        
        query = query.order_by(BettingOdds.created_at.desc())
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        # 整理數據以包含比賽資訊
        odds_data = []
        for odd, game in pagination.items:
            odd.game_info = game
            odds_data.append(odd)
        
        # 獲取統計信息（基於原始BettingOdds查詢）
        base_query = BettingOdds.query
        if selected_date:
            base_query = base_query.filter(db.func.date(BettingOdds.odds_time) == target_date)
        if market_type != 'all':
            base_query = base_query.filter(BettingOdds.market_type == market_type)
        if bookmaker != 'all':
            base_query = base_query.filter(BettingOdds.bookmaker == bookmaker)
        
        total_count = base_query.count()
        spreads_count = base_query.filter(
            or_(BettingOdds.market_type == 'spreads',
                and_(BettingOdds.market_type == 'both', 
                     BettingOdds.home_spread_point.isnot(None)))
        ).count()
        totals_count = base_query.filter(
            or_(BettingOdds.market_type == 'totals',
                and_(BettingOdds.market_type == 'both', 
                     BettingOdds.total_point.isnot(None)))
        ).count()
        
        return render_template('odds/search_results.html',
                             odds_data=odds_data,
                             pagination=pagination,
                             selected_date=selected_date,
                             market_type=market_type,
                             bookmaker=bookmaker,
                             total_count=total_count,
                             spreads_count=spreads_count,
                             totals_count=totals_count)
    
    except Exception as e:
        logger.error(f"盤口搜索錯誤: {e}")
        return render_template('odds/search_results.html',
                             odds_data=[],
                             pagination=None,
                             error=str(e))

@odds_viewer_bp.route('/api/odds/<date_str>')
def api_odds_by_date(date_str):
    """API: 獲取指定日期的盤口數據"""
    try:
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # 獲取該日期的所有盤口數據
        odds = BettingOdds.query.filter(
            db.func.date(BettingOdds.odds_time) == target_date
        ).order_by(BettingOdds.created_at.desc()).all()
        
        # 按比賽分組
        games_odds = {}
        for odd in odds:
            game_id = odd.game_id
            if game_id not in games_odds:
                games_odds[game_id] = {
                    'game_id': game_id,
                    'spreads': [],
                    'totals': [],
                    'bookmakers': set()
                }
            
            games_odds[game_id]['bookmakers'].add(odd.bookmaker)
            
            if odd.market_type in ['spreads', 'both'] and odd.home_spread_point is not None:
                games_odds[game_id]['spreads'].append({
                    'bookmaker': odd.bookmaker,
                    'home_spread': odd.home_spread_point,
                    'home_spread_price': odd.home_spread_price,
                    'away_spread': odd.away_spread_point,
                    'away_spread_price': odd.away_spread_price,
                    'created_at': odd.created_at.isoformat()
                })
            
            if odd.market_type in ['totals', 'both'] and odd.total_point is not None:
                games_odds[game_id]['totals'].append({
                    'bookmaker': odd.bookmaker,
                    'total_line': odd.total_point,
                    'over_price': odd.over_price,
                    'under_price': odd.under_price,
                    'created_at': odd.created_at.isoformat()
                })
        
        # 轉換為列表
        for game_id in games_odds:
            games_odds[game_id]['bookmakers'] = list(games_odds[game_id]['bookmakers'])
        
        return jsonify({
            'success': True,
            'date': date_str,
            'games_count': len(games_odds),
            'total_odds': len(odds),
            'games': games_odds
        })
    
    except Exception as e:
        logger.error(f"API獲取盤口數據失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@odds_viewer_bp.route('/comparison')
def odds_comparison():
    """盤口比較頁面"""
    try:
        # 獲取最新的不同博彩商數據進行比較
        selected_date = request.args.get('date', date.today().isoformat())
        target_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
        
        # 獲取該日期的所有盤口數據，並加入比賽信息
        odds_data = db.session.query(BettingOdds, Game).outerjoin(
            Game, BettingOdds.game_id == Game.game_id
        ).filter(
            db.func.date(BettingOdds.odds_time) == target_date
        ).all()
        
        # 按比賽和博彩商組織數據
        comparison_data = {}
        
        for odd, game in odds_data:
            game_id = odd.game_id
            if game_id not in comparison_data:
                comparison_data[game_id] = {
                    'game_id': game_id,
                    'game_info': game,
                    'bookmakers': {}
                }
            
            bookmaker = odd.bookmaker
            if bookmaker not in comparison_data[game_id]['bookmakers']:
                comparison_data[game_id]['bookmakers'][bookmaker] = {
                    'spreads': None,
                    'totals': None
                }
            
            # 讓分盤數據
            if odd.market_type in ['spreads', 'both'] and odd.home_spread_point is not None:
                comparison_data[game_id]['bookmakers'][bookmaker]['spreads'] = {
                    'home_spread': odd.home_spread_point,
                    'home_price': odd.home_spread_price,
                    'away_spread': odd.away_spread_point,
                    'away_price': odd.away_spread_price
                }
            
            # 大小分數據
            if odd.market_type in ['totals', 'both'] and odd.total_point is not None:
                comparison_data[game_id]['bookmakers'][bookmaker]['totals'] = {
                    'total_line': odd.total_point,
                    'over_price': odd.over_price,
                    'under_price': odd.under_price
                }
        
        return render_template('odds/comparison.html',
                             comparison_data=comparison_data,
                             selected_date=selected_date)
    
    except Exception as e:
        logger.error(f"盤口比較錯誤: {e}")
        return render_template('odds/comparison.html',
                             comparison_data={},
                             selected_date=selected_date,
                             error=str(e))

@odds_viewer_bp.route('/history/<game_id>')
def odds_history(game_id):
    """查看特定比賽的盤口歷史"""
    try:
        # 獲取該比賽的所有盤口歷史
        odds_history = BettingOdds.query.filter_by(game_id=game_id).order_by(
            BettingOdds.created_at.asc()
        ).all()
        
        # 嘗試獲取比賽基本信息
        game_info = Game.query.filter_by(game_id=game_id).first()
        
        return render_template('odds/history.html',
                             odds_history=odds_history,
                             game_info=game_info,
                             game_id=game_id)
    
    except Exception as e:
        logger.error(f"盤口歷史查看錯誤: {e}")
        return render_template('odds/history.html',
                             odds_history=[],
                             game_info=None,
                             game_id=game_id,
                             error=str(e))