"""
比賽結果更新Web界面
提供Web端點用於更新特定日期的比賽結果
"""

from flask import Blueprint, render_template, request, jsonify
from datetime import datetime, timedelta
import logging
import threading

logger = logging.getLogger(__name__)

# 創建 Blueprint
game_updater_bp = Blueprint('game_updater', __name__, url_prefix='/admin/game-updater')

@game_updater_bp.route('/')
def index():
    """比賽結果更新主頁"""
    today = datetime.now().strftime('%Y-%m-%d')
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    
    return render_template('admin/game_updater.html', 
                         today=today, 
                         yesterday=yesterday)

@game_updater_bp.route('/api/check-games', methods=['POST'])
def api_check_games():
    """API: 檢查特定日期的比賽狀態"""
    try:
        data = request.get_json()
        target_date = data.get('date')
        
        if not target_date:
            return jsonify({'success': False, 'message': '請提供日期'}), 400
            
        from flask import current_app
        app = current_app._get_current_object()
        
        with app.app_context():
            from models.database import db, Game
            
            # 查詢該日期的比賽
            games = db.session.query(Game).filter(
                Game.date == target_date
            ).all()
            
            if not games:
                return jsonify({
                    'success': False,
                    'message': f'未找到 {target_date} 的比賽記錄'
                }), 404
            
            # 分析比賽狀態
            total_games = len(games)
            completed_games = 0
            scheduled_games = 0
            games_with_scores = 0
            games_without_scores = 0
            game_details = []
            
            for game in games:
                status = game.game_status or 'unknown'
                has_score = game.away_score is not None and game.home_score is not None
                
                if status == 'completed':
                    completed_games += 1
                elif status in ['scheduled', 'postponed']:
                    scheduled_games += 1
                    
                if has_score:
                    games_with_scores += 1
                else:
                    games_without_scores += 1
                
                game_details.append({
                    'id': game.game_id,
                    'teams': f"{game.away_team} @ {game.home_team}",
                    'status': status,
                    'score': f"{game.away_score or 'N/A'} - {game.home_score or 'N/A'}",
                    'needs_update': not has_score or status != 'completed',
                    'updated_at': game.updated_at.strftime('%Y-%m-%d %H:%M:%S') if game.updated_at else 'Never'
                })
            
            # 判斷是否需要更新
            check_date = datetime.strptime(target_date, '%Y-%m-%d')
            today = datetime.now()
            days_passed = (today - check_date).days
            needs_update = days_passed >= 1 and games_without_scores > 0
            
            return jsonify({
                'success': True,
                'date': target_date,
                'summary': {
                    'total': total_games,
                    'completed': completed_games,
                    'scheduled': scheduled_games,
                    'with_scores': games_with_scores,
                    'without_scores': games_without_scores,
                    'days_passed': days_passed,
                    'needs_update': needs_update
                },
                'games': game_details
            })
            
    except Exception as e:
        logger.error(f"檢查比賽狀態失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@game_updater_bp.route('/api/update-games', methods=['POST'])
def api_update_games():
    """API: 更新特定日期的比賽結果"""
    try:
        data = request.get_json()
        target_date = data.get('date')
        
        if not target_date:
            return jsonify({'success': False, 'message': '請提供日期'}), 400
        
        # 在後台線程執行更新
        from flask import current_app
        app = current_app._get_current_object()
        
        def run_update(app_instance, date_str):
            with app_instance.app_context():
                try:
                    # 使用我們創建的更新工具
                    import sys
                    import os
                    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    
                    from update_specific_date_games import update_games_for_date, verify_update_results
                    
                    logger.info(f"🔄 開始更新 {date_str} 的比賽結果...")
                    
                    # 執行更新
                    success = update_games_for_date(date_str)
                    
                    if success:
                        # 驗證結果
                        verify_update_results(date_str)
                        logger.info(f"✅ {date_str} 比賽結果更新成功")
                    else:
                        logger.error(f"❌ {date_str} 比賽結果更新失敗")
                        
                except Exception as e:
                    logger.error(f"更新過程失敗: {e}")
        
        # 啟動後台更新線程
        thread = threading.Thread(
            target=run_update,
            args=(app, target_date)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': f'已開始更新 {target_date} 的比賽結果',
            'note': '更新正在後台執行，請稍後刷新頁面查看結果'
        })
        
    except Exception as e:
        logger.error(f"啟動更新失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@game_updater_bp.route('/api/download-boxscores', methods=['POST'])
def api_download_boxscores():
    """API: 下載Box Score詳細數據"""
    try:
        data = request.get_json()
        target_date = data.get('date')
        
        if not target_date:
            return jsonify({'success': False, 'message': '請提供日期'}), 400
        
        # 在後台執行Box Score下載
        from flask import current_app
        app = current_app._get_current_object()
        
        def run_boxscore_download(app_instance, date_str):
            with app_instance.app_context():
                try:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    
                    from simple_boxscore_downloader import download_boxscores_for_date
                    
                    logger.info(f"📊 開始下載 {date_str} 的Box Score數據...")
                    
                    # 執行下載
                    success = download_boxscores_for_date(date_str)
                    
                    if success:
                        logger.info(f"✅ {date_str} Box Score數據下載成功")
                    else:
                        logger.error(f"❌ {date_str} Box Score數據下載失敗")
                        
                except Exception as e:
                    logger.error(f"Box Score下載過程失敗: {e}")
        
        # 啟動後台下載線程
        thread = threading.Thread(
            target=run_boxscore_download,
            args=(app, target_date)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': f'已開始下載 {target_date} 的Box Score數據',
            'note': '下載正在後台執行，包含詳細的球員統計數據'
        })
        
    except Exception as e:
        logger.error(f"啟動Box Score下載失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@game_updater_bp.route('/api/batch-update', methods=['POST'])
def api_batch_update():
    """API: 批量更新多天的比賽結果"""
    try:
        data = request.get_json()
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if not start_date or not end_date:
            return jsonify({'success': False, 'message': '請提供開始和結束日期'}), 400
        
        # 驗證日期範圍
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        
        if start > end:
            return jsonify({'success': False, 'message': '開始日期不能晚於結束日期'}), 400
        
        days_diff = (end - start).days
        if days_diff > 7:
            return jsonify({'success': False, 'message': '批量更新最多支持7天'}), 400
        
        # 生成日期列表
        dates_to_update = []
        current_date = start
        while current_date <= end:
            dates_to_update.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
        
        # 在後台執行批量更新
        from flask import current_app
        app = current_app._get_current_object()
        
        def run_batch_update(app_instance, dates):
            with app_instance.app_context():
                try:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    
                    from update_specific_date_games import update_games_for_date
                    
                    success_count = 0
                    for date_str in dates:
                        logger.info(f"🔄 批量更新: 處理 {date_str}...")
                        if update_games_for_date(date_str):
                            success_count += 1
                    
                    logger.info(f"✅ 批量更新完成: {success_count}/{len(dates)} 天成功")
                    
                except Exception as e:
                    logger.error(f"批量更新失敗: {e}")
        
        # 啟動批量更新線程
        thread = threading.Thread(
            target=run_batch_update,
            args=(app, dates_to_update)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': f'已開始批量更新 {len(dates_to_update)} 天的比賽結果',
            'dates': dates_to_update,
            'note': '批量更新正在後台執行，可能需要幾分鐘時間'
        })
        
    except Exception as e:
        logger.error(f"批量更新失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500