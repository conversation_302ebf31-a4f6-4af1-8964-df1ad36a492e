#!/usr/bin/env python3
"""
增強預測系統視圖
整合統一預測引擎，解決投手資料缺失和日期邏輯問題
"""

from flask import Blueprint, render_template, request, jsonify, current_app
from datetime import datetime, date, timedelta
import json
import logging
import asyncio

from models.database import Game, Team, Prediction, db
from models.unified_prediction_engine import unified_engine, predict_game
from models.prediction_context import create_prediction_context
from models.starting_pitcher_tracker import StartingPitcherTracker

logger = logging.getLogger(__name__)

enhanced_predictions_bp = Blueprint('enhanced_predictions', __name__, url_prefix='/enhanced')

@enhanced_predictions_bp.route('/')
def enhanced_predictions_list():
    """增強預測列表頁面"""
    page = request.args.get('page', 1, type=int)
    selected_date = request.args.get('date', '')
    prediction_mode = request.args.get('mode', 'all')  # all, historical, live, future
    
    # 建立查詢
    query = Game.query
    
    # 日期篩選
    if selected_date:
        try:
            filter_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            query = query.filter(Game.date == filter_date)
        except ValueError:
            pass
    else:
        # 默認顯示最近一週的比賽
        start_date = date.today() - timedelta(days=3)
        end_date = date.today() + timedelta(days=4)
        query = query.filter(Game.date.between(start_date, end_date))
    
    # 預測模式篩選
    today = date.today()
    if prediction_mode == 'historical':
        query = query.filter(Game.date < today)
    elif prediction_mode == 'live':
        query = query.filter(Game.date == today)
    elif prediction_mode == 'future':
        query = query.filter(Game.date > today)
    
    # 分頁
    games_pagination = query.order_by(Game.date.desc(), Game.game_id.asc()).paginate(
        page=page, per_page=15, error_out=False
    )
    
    # 為每場比賽添加預測狀態和投手信息
    enhanced_games = []
    pitcher_tracker = StartingPitcherTracker()
    
    for game in games_pagination.items:
        # 獲取現有預測
        existing_prediction = Prediction.query.filter_by(game_id=game.game_id).first()
        
        # 獲取投手信息
        pitcher_info = pitcher_tracker.get_starting_pitchers(game.game_id)
        
        # 判斷預測模式
        if game.date < today:
            game_mode = 'historical'
        elif game.date == today:
            game_mode = 'live'
        else:
            game_mode = 'future'
        
        enhanced_game = {
            'game': game,
            'game_mode': game_mode,
            'has_prediction': existing_prediction is not None,
            'prediction': existing_prediction,
            'pitcher_info': pitcher_info,
            'home_pitcher': pitcher_info.get('home_starting_pitcher') if pitcher_info else '未確認',
            'away_pitcher': pitcher_info.get('away_starting_pitcher') if pitcher_info else '未確認',
            'pitcher_data_source': pitcher_info.get('data_source') if pitcher_info else None,
            'can_predict': True  # 統一引擎可以處理所有情況
        }
        
        enhanced_games.append(enhanced_game)
    
    return render_template('enhanced_predictions_list.html',
                         enhanced_games=enhanced_games,
                         pagination=games_pagination,
                         selected_date=selected_date,
                         prediction_mode=prediction_mode)

@enhanced_predictions_bp.route('/<game_id>')
def enhanced_prediction_detail(game_id):
    """增強預測詳情頁面"""
    game = Game.query.filter_by(game_id=game_id).first_or_404()
    
    # 獲取現有預測
    existing_prediction = Prediction.query.filter_by(game_id=game_id).first()
    
    # 獲取球隊信息
    home_team = Team.query.filter_by(team_code=game.home_team).first()
    away_team = Team.query.filter_by(team_code=game.away_team).first()
    
    # 獲取投手信息
    pitcher_tracker = StartingPitcherTracker()
    pitcher_info = pitcher_tracker.get_starting_pitchers(game_id)
    
    # 判斷預測模式
    today = date.today()
    if game.date < today:
        game_mode = 'historical'
    elif game.date == today:
        game_mode = 'live'
    else:
        game_mode = 'future'
    
    # 準備上下文信息
    context_info = {
        'game_id': game_id,
        'target_date': game.date,
        'game_mode': game_mode,
        'home_team': game.home_team,
        'away_team': game.away_team,
        'venue': game.venue,
        'pitcher_info': pitcher_info
    }
    
    return render_template('enhanced_prediction_detail.html',
                         game=game,
                         existing_prediction=existing_prediction,
                         home_team=home_team,
                         away_team=away_team,
                         pitcher_info=pitcher_info,
                         context_info=context_info,
                         game_mode=game_mode)

@enhanced_predictions_bp.route('/api/predict/<game_id>', methods=['POST'])
def api_generate_prediction(game_id):
    """API端點：生成增強預測"""
    try:
        # 獲取請求參數
        data = request.get_json() or {}
        target_date_str = data.get('target_date')
        prediction_type = data.get('prediction_type', 'comprehensive')
        force_refresh = data.get('force_refresh', False)
        
        # 解析目標日期
        target_date = None
        if target_date_str:
            try:
                target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': f'無效的日期格式: {target_date_str}'
                }), 400
        
        logger.info(f"🎯 API預測請求: {game_id} | 日期: {target_date} | 類型: {prediction_type}")
        
        # 檢查是否已有預測 (除非強制刷新)
        if not force_refresh:
            existing = Prediction.query.filter_by(game_id=game_id).first()
            if existing:
                logger.info(f"返回現有預測: {game_id}")
                return jsonify({
                    'success': True,
                    'source': 'existing',
                    'prediction': existing.to_dict(),
                    'generated_at': existing.created_at.isoformat()
                })
        
        # 使用統一預測引擎生成新預測
        async def run_prediction():
            return await predict_game(
                game_id=game_id,
                target_date=target_date,
                prediction_type=prediction_type
            )
        
        # 在Flask中運行異步函數
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            prediction_result = loop.run_until_complete(run_prediction())
        finally:
            loop.close()
        
        if prediction_result.get('success'):
            logger.info(f"✅ 預測生成成功: {game_id}")
            
            # 格式化響應
            response = {
                'success': True,
                'source': 'generated',
                'prediction': prediction_result.get('prediction', {}),
                'confidence': prediction_result.get('confidence', {}),
                'pitcher_analysis': prediction_result.get('pitcher_analysis', {}),
                'formatted_display': prediction_result.get('formatted_display', {}),
                'recommendation': prediction_result.get('recommendation', {}),
                'context': prediction_result.get('context_info', {}),
                'strategy': prediction_result.get('prediction_strategy', 'unknown'),
                'generated_at': datetime.now().isoformat()
            }
            
            return jsonify(response)
        else:
            logger.error(f"❌ 預測生成失敗: {game_id}")
            return jsonify({
                'success': False,
                'error': prediction_result.get('error', '預測生成失敗'),
                'context': prediction_result.get('context_info', {})
            }), 500
            
    except Exception as e:
        logger.error(f"API預測請求失敗: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'內部錯誤: {str(e)}'
        }), 500

@enhanced_predictions_bp.route('/api/batch_predict', methods=['POST'])
def api_batch_predict():
    """API端點：批量預測"""
    try:
        data = request.get_json() or {}
        game_ids = data.get('game_ids', [])
        target_date_str = data.get('target_date')
        prediction_type = data.get('prediction_type', 'comprehensive')
        
        if not game_ids:
            return jsonify({
                'success': False,
                'error': '未提供比賽ID列表'
            }), 400
        
        # 解析目標日期
        target_date = None
        if target_date_str:
            try:
                target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': f'無效的日期格式: {target_date_str}'
                }), 400
        
        logger.info(f"🎯 批量預測請求: {len(game_ids)} 場比賽 | 日期: {target_date}")
        
        # 批量生成預測
        async def run_batch_prediction():
            results = []
            for game_id in game_ids:
                try:
                    prediction_result = await predict_game(
                        game_id=game_id,
                        target_date=target_date,
                        prediction_type=prediction_type
                    )
                    results.append({
                        'game_id': game_id,
                        'success': prediction_result.get('success', False),
                        'prediction': prediction_result.get('prediction', {}),
                        'confidence': prediction_result.get('confidence', {}),
                        'error': prediction_result.get('error')
                    })
                except Exception as e:
                    results.append({
                        'game_id': game_id,
                        'success': False,
                        'error': str(e)
                    })
            return results
        
        # 執行批量預測
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            batch_results = loop.run_until_complete(run_batch_prediction())
        finally:
            loop.close()
        
        # 統計結果
        success_count = sum(1 for r in batch_results if r['success'])
        
        return jsonify({
            'success': True,
            'total_games': len(game_ids),
            'successful_predictions': success_count,
            'failed_predictions': len(game_ids) - success_count,
            'results': batch_results,
            'generated_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"批量預測失敗: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'內部錯誤: {str(e)}'
        }), 500

@enhanced_predictions_bp.route('/api/pitcher_info/<game_id>')
def api_pitcher_info(game_id):
    """API端點：獲取投手信息"""
    try:
        pitcher_tracker = StartingPitcherTracker()
        pitcher_info = pitcher_tracker.get_starting_pitchers(game_id)
        
        if pitcher_info:
            return jsonify({
                'success': True,
                'pitcher_info': pitcher_info
            })
        else:
            return jsonify({
                'success': False,
                'error': '未找到投手信息'
            })
            
    except Exception as e:
        logger.error(f"獲取投手信息失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'獲取投手信息失敗: {str(e)}'
        }), 500

@enhanced_predictions_bp.route('/api/context/<game_id>')
def api_prediction_context(game_id):
    """API端點：獲取預測上下文"""
    try:
        # 獲取比賽信息
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({
                'success': False,
                'error': '比賽不存在'
            }), 404
        
        # 創建預測上下文
        context = create_prediction_context(
            game_id=game_id,
            target_date=game.date
        )
        
        return jsonify({
            'success': True,
            'context': context.to_dict()
        })
        
    except Exception as e:
        logger.error(f"獲取預測上下文失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'獲取上下文失敗: {str(e)}'
        }), 500

# 錯誤處理
@enhanced_predictions_bp.errorhandler(404)
def not_found_error(error):
    return jsonify({'success': False, 'error': '資源未找到'}), 404

@enhanced_predictions_bp.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return jsonify({'success': False, 'error': '內部服務器錯誤'}), 500