#!/usr/bin/env python3
"""
預測分析視圖 - 提供預測準確性分析和效果評估
"""

from flask import Blueprint, render_template, request, jsonify
from datetime import datetime, date, timedelta
from models.database import db, Game, Prediction
from sqlalchemy import func, and_
import statistics
import logging

logger = logging.getLogger(__name__)

prediction_analysis_bp = Blueprint('prediction_analysis', __name__)

@prediction_analysis_bp.route('/analysis')
def prediction_analysis_page():
    """預測分析主頁面"""
    try:
        # 計算默認日期範圍 (過去7天)
        end_date = date.today() - timedelta(days=1)
        start_date = end_date - timedelta(days=6)
        
        return render_template('prediction_analysis/analysis.html',
                             default_start_date=start_date.isoformat(),
                             default_end_date=end_date.isoformat())
        
    except Exception as e:
        logger.error(f"載入預測分析頁面失敗: {e}")
        return render_template('prediction_analysis/analysis.html',
                             error_message=f'載入失敗: {str(e)}')

@prediction_analysis_bp.route('/api/analyze', methods=['POST'])
def api_analyze_predictions():
    """API: 分析預測準確性"""
    try:
        data = request.get_json()
        start_date_str = data.get('start_date')
        end_date_str = data.get('end_date')
        
        if not start_date_str or not end_date_str:
            return jsonify({
                'success': False,
                'error': '請提供開始和結束日期'
            })
        
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        
        # 執行分析
        analysis_result = analyze_predictions(start_date, end_date)
        
        return jsonify({
            'success': True,
            'data': analysis_result
        })
        
    except Exception as e:
        logger.error(f"預測分析API失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@prediction_analysis_bp.route('/api/summary')
def api_prediction_summary():
    """API: 預測摘要統計"""
    try:
        # 計算不同時間段的統計
        today = date.today()
        
        # 過去7天
        week_ago = today - timedelta(days=7)
        week_stats = get_prediction_stats(week_ago, today - timedelta(days=1))
        
        # 過去30天
        month_ago = today - timedelta(days=30)
        month_stats = get_prediction_stats(month_ago, today - timedelta(days=1))
        
        return jsonify({
            'success': True,
            'data': {
                'week': week_stats,
                'month': month_stats
            }
        })
        
    except Exception as e:
        logger.error(f"預測摘要API失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

def analyze_predictions(start_date, end_date):
    """分析預測準確性的核心函數"""
    
    # 查找有實際結果的預測
    predictions_with_results = db.session.query(Prediction, Game).join(
        Game, Prediction.game_id == Game.game_id
    ).filter(
        Game.date >= start_date,
        Game.date <= end_date,
        Game.home_score.isnot(None),
        Game.away_score.isnot(None)
    ).all()
    
    if not predictions_with_results:
        return {
            'message': '該日期範圍內沒有找到有實際結果的預測',
            'games': []
        }
    
    # 分析每場比賽
    game_analyses = []
    correct_winners = 0
    over_under_correct = 0
    score_differences = []
    total_score_differences = []
    confidence_scores = []
    
    for prediction, game in predictions_with_results:
        # 預測結果
        pred_home = prediction.predicted_home_score or 0
        pred_away = prediction.predicted_away_score or 0
        pred_total = pred_home + pred_away
        
        # 實際結果
        actual_home = game.home_score
        actual_away = game.away_score
        actual_total = actual_home + actual_away
        
        # 勝負預測正確性
        pred_winner = "home" if pred_home > pred_away else "away" if pred_away > pred_home else "tie"
        actual_winner = "home" if actual_home > actual_away else "away" if actual_away > actual_home else "tie"
        winner_correct = pred_winner == actual_winner
        
        if winner_correct:
            correct_winners += 1
        
        # 分數差異
        home_diff = abs(pred_home - actual_home)
        away_diff = abs(pred_away - actual_away)
        avg_score_diff = (home_diff + away_diff) / 2
        score_differences.append(avg_score_diff)
        
        # 總分差異
        total_diff = abs(pred_total - actual_total)
        total_score_differences.append(total_diff)
        
        # 大小分預測 (假設線是8.5)
        over_under_line = 8.5
        pred_over = pred_total > over_under_line
        actual_over = actual_total > over_under_line
        over_under_correct_this_game = pred_over == actual_over
        if over_under_correct_this_game:
            over_under_correct += 1
        
        # 信心度
        confidence = prediction.confidence or 0.65
        confidence_scores.append(confidence)
        
        # 計算盤口信息（預測分差 vs 實際分差）
        pred_spread = pred_home - pred_away  # 主隊預測分差
        actual_spread = actual_home - actual_away  # 主隊實際分差
        spread_diff = abs(pred_spread - actual_spread)
        
        # 生成盤口顯示信息
        if pred_spread > 0:
            spread_info = f"主讓 {pred_spread:.1f}"
        elif pred_spread < 0:
            spread_info = f"客讓 {abs(pred_spread):.1f}"
        else:
            spread_info = "平手"
        
        # 記錄這場比賽的分析
        game_analysis = {
            'date': game.date.isoformat(),
            'matchup': f"{game.away_team} @ {game.home_team}",
            'predicted_score': f"{pred_away:.1f} - {pred_home:.1f}",
            'actual_score': f"{actual_away} - {actual_home}",
            'winner_correct': winner_correct,
            'score_diff': avg_score_diff,
            'total_diff': total_diff,
            'spread_info': spread_info,
            'spread_diff': spread_diff,
            'over_under_correct': over_under_correct_this_game,
            'confidence': confidence * 100
        }
        game_analyses.append(game_analysis)
    
    # 計算整體統計
    total_games = len(predictions_with_results)
    winner_accuracy = (correct_winners / total_games) * 100
    over_under_accuracy = (over_under_correct / total_games) * 100
    avg_score_diff = statistics.mean(score_differences) if score_differences else 0
    avg_total_diff = statistics.mean(total_score_differences) if total_score_differences else 0
    avg_confidence = statistics.mean(confidence_scores) * 100 if confidence_scores else 0
    
    # 生成改進建議
    suggestions = generate_improvement_suggestions(winner_accuracy, avg_score_diff, over_under_accuracy)
    
    return {
        'date_range': {
            'start': start_date.isoformat(),
            'end': end_date.isoformat()
        },
        'overall_stats': {
            'total_games': total_games,
            'winner_accuracy': round(winner_accuracy, 1),
            'over_under_accuracy': round(over_under_accuracy, 1),
            'avg_score_diff': round(avg_score_diff, 2),
            'avg_total_diff': round(avg_total_diff, 2),
            'avg_confidence': round(avg_confidence, 1)
        },
        'quality_assessment': assess_prediction_quality(winner_accuracy, avg_score_diff, over_under_accuracy),
        'games': game_analyses,
        'suggestions': suggestions
    }

def get_prediction_stats(start_date, end_date):
    """獲取預測統計數據"""
    try:
        predictions_count = db.session.query(Prediction, Game).join(
            Game, Prediction.game_id == Game.game_id
        ).filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).count()
        
        if predictions_count == 0:
            return {
                'total_games': 0,
                'winner_accuracy': 0,
                'avg_confidence': 0
            }
        
        # 簡化的統計計算
        analysis = analyze_predictions(start_date, end_date)
        
        return {
            'total_games': analysis['overall_stats']['total_games'],
            'winner_accuracy': analysis['overall_stats']['winner_accuracy'],
            'avg_confidence': analysis['overall_stats']['avg_confidence']
        }
        
    except Exception as e:
        logger.error(f"獲取預測統計失敗: {e}")
        return {
            'total_games': 0,
            'winner_accuracy': 0,
            'avg_confidence': 0
        }

def assess_prediction_quality(winner_accuracy, avg_score_diff, over_under_accuracy):
    """評估預測品質"""
    assessments = []
    
    # 勝負預測評估
    if winner_accuracy >= 60:
        assessments.append({'category': '勝負預測', 'status': 'excellent', 'message': '優秀 (≥60%)'})
    elif winner_accuracy >= 55:
        assessments.append({'category': '勝負預測', 'status': 'good', 'message': '良好 (55-60%)'})
    elif winner_accuracy >= 50:
        assessments.append({'category': '勝負預測', 'status': 'fair', 'message': '及格 (50-55%)'})
    else:
        assessments.append({'category': '勝負預測', 'status': 'poor', 'message': '需要改善 (<50%)'})
    
    # 分數預測評估
    if avg_score_diff <= 1.5:
        assessments.append({'category': '分數預測', 'status': 'excellent', 'message': '優秀 (差異≤1.5分)'})
    elif avg_score_diff <= 2.5:
        assessments.append({'category': '分數預測', 'status': 'good', 'message': '良好 (差異1.5-2.5分)'})
    else:
        assessments.append({'category': '分數預測', 'status': 'poor', 'message': '需要改善 (差異>2.5分)'})
    
    # 大小分預測評估
    if over_under_accuracy >= 55:
        assessments.append({'category': '大小分預測', 'status': 'excellent', 'message': '優秀 (≥55%)'})
    elif over_under_accuracy >= 50:
        assessments.append({'category': '大小分預測', 'status': 'fair', 'message': '及格 (50-55%)'})
    else:
        assessments.append({'category': '大小分預測', 'status': 'poor', 'message': '需要改善 (<50%)'})
    
    return assessments

def generate_improvement_suggestions(winner_accuracy, avg_score_diff, over_under_accuracy):
    """生成改進建議"""
    suggestions = []
    
    if winner_accuracy < 55:
        suggestions.extend([
            "考慮調整投手分析權重",
            "增加主場優勢係數分析",
            "檢查球隊近期狀態評估",
            "優化球員傷病影響評估"
        ])
    
    if avg_score_diff > 2.0:
        suggestions.extend([
            "優化得分預測算法",
            "考慮天氣因素對得分的影響",
            "調整攻擊力評估模型",
            "加強球場因子分析"
        ])
    
    if over_under_accuracy < 52:
        suggestions.extend([
            "改善總分預測模型",
            "分析投手壓制力評估準確性",
            "考慮牛棚實力對總分的影響",
            "優化比賽節奏預測"
        ])
    
    return suggestions