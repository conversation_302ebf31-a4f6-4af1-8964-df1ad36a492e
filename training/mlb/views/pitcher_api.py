#!/usr/bin/env python3
"""
投手對戰 API 端點
提供投手數據刷新和查詢功能
"""

from flask import Blueprint, jsonify, request, current_app
import logging

logger = logging.getLogger(__name__)

# 創建 Blueprint
pitcher_api_bp = Blueprint('pitcher_api', __name__, url_prefix='/api/pitcher')

@pitcher_api_bp.route('/matchup/<game_id>')
def api_get_pitcher_matchup(game_id):
    """API: 獲取投手對戰數據"""
    try:
        from models.database import Game
        from enhanced_pitcher_matchup import EnhancedPitcherMatchup
        
        # 查找比賽
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({
                'success': False,
                'error': f'找不到比賽 ID: {game_id}'
            }), 404
        
        # 使用增強投手分析器
        analyzer = EnhancedPitcherMatchup()
        matchup_data = analyzer.get_pitcher_matchup_data(game)
        
        # 檢查是否有錯誤
        if 'error' in matchup_data:
            return jsonify({
                'success': False,
                'error': matchup_data['error'],
                'data': matchup_data
            }), 500
        
        return jsonify({
            'success': True,
            'data': matchup_data,
            'message': '投手對戰數據載入成功'
        })
        
    except Exception as e:
        logger.error(f"投手對戰 API 失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@pitcher_api_bp.route('/refresh/<game_id>')
def api_refresh_pitcher_data(game_id):
    """API: 刷新投手數據"""
    try:
        from models.database import Game
        from enhanced_pitcher_matchup import EnhancedPitcherMatchup
        
        # 查找比賽
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({
                'success': False,
                'error': f'找不到比賽 ID: {game_id}'
            }), 404
        
        # 強制刷新數據
        analyzer = EnhancedPitcherMatchup()
        
        # 先檢查是否有 Box Score 數據
        has_boxscore = analyzer.get_starting_pitcher_from_boxscore(game_id, True) is not None
        
        if not has_boxscore:
            return jsonify({
                'success': False,
                'error': '沒有找到 Box Score 數據',
                'suggestion': '請先下載該比賽的 Box Score 數據',
                'action_needed': 'download_boxscore'
            }), 404
        
        # 獲取最新數據
        matchup_data = analyzer.get_pitcher_matchup_data(game)
        
        return jsonify({
            'success': True,
            'data': matchup_data,
            'message': '投手數據刷新成功',
            'data_source': 'enhanced_boxscore'
        })
        
    except Exception as e:
        logger.error(f"刷新投手數據失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@pitcher_api_bp.route('/boxscore-status/<game_id>')
def api_check_boxscore_status(game_id):
    """API: 檢查 Box Score 數據狀態"""
    try:
        from models.database import db
        from sqlalchemy import text
        
        # 查詢 Box Score 數據
        result = db.session.execute(text("""
            SELECT 
                game_id,
                player_count,
                pitcher_count,
                created_at,
                CASE 
                    WHEN boxscore_json IS NOT NULL THEN 1 
                    ELSE 0 
                END as has_data
            FROM boxscore_data 
            WHERE game_id = :game_id
        """), {"game_id": game_id}).fetchone()
        
        if result:
            return jsonify({
                'success': True,
                'has_boxscore': True,
                'data': {
                    'game_id': result[0],
                    'player_count': result[1],
                    'pitcher_count': result[2],
                    'created_at': result[3],
                    'has_json_data': bool(result[4])
                },
                'message': 'Box Score 數據存在'
            })
        else:
            return jsonify({
                'success': True,
                'has_boxscore': False,
                'message': '沒有 Box Score 數據',
                'suggestion': '請先下載該比賽的 Box Score 數據'
            })
        
    except Exception as e:
        logger.error(f"檢查 Box Score 狀態失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@pitcher_api_bp.route('/debug/<game_id>')
def api_debug_pitcher_data(game_id):
    """API: 調試投手數據（僅開發用）"""
    try:
        from models.database import Game, db
        from enhanced_pitcher_matchup import EnhancedPitcherMatchup
        from sqlalchemy import text
        import json
        
        # 查找比賽
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({'error': f'找不到比賽 ID: {game_id}'}), 404
        
        debug_info = {
            'game_info': {
                'game_id': game_id,
                'date': game.date.strftime('%Y-%m-%d'),
                'teams': f"{game.away_team} @ {game.home_team}",
                'status': game.game_status
            }
        }
        
        # 檢查 Box Score 數據
        boxscore_result = db.session.execute(text("""
            SELECT boxscore_json, player_count, pitcher_count 
            FROM boxscore_data 
            WHERE game_id = :game_id
        """), {"game_id": game_id}).fetchone()
        
        if boxscore_result:
            debug_info['boxscore_status'] = {
                'exists': True,
                'player_count': boxscore_result[1],
                'pitcher_count': boxscore_result[2]
            }
            
            # 解析投手數據預覽
            try:
                boxscore_data = json.loads(boxscore_result[0])
                teams_data = boxscore_data.get('teams', {})
                
                debug_info['pitcher_preview'] = {}
                for team_side in ['home', 'away']:
                    team_data = teams_data.get(team_side, {})
                    players = team_data.get('players', {})
                    
                    pitchers = []
                    for player_id, player_data in players.items():
                        pitching_stats = player_data.get('stats', {}).get('pitching')
                        if pitching_stats:
                            pitchers.append({
                                'name': player_data.get('person', {}).get('fullName', ''),
                                'innings': pitching_stats.get('inningsPitched', '0')
                            })
                    
                    debug_info['pitcher_preview'][team_side] = pitchers
                    
            except Exception as e:
                debug_info['boxscore_parse_error'] = str(e)
        else:
            debug_info['boxscore_status'] = {'exists': False}
        
        # 測試增強分析器
        try:
            analyzer = EnhancedPitcherMatchup()
            matchup_data = analyzer.get_pitcher_matchup_data(game)
            debug_info['enhanced_analyzer'] = {
                'success': True,
                'home_pitcher': matchup_data['pitchers']['home']['name'] if matchup_data['pitchers']['home'] else None,
                'away_pitcher': matchup_data['pitchers']['away']['name'] if matchup_data['pitchers']['away'] else None,
                'data_sources': {
                    'home': matchup_data['pitchers']['home']['data_source'] if matchup_data['pitchers']['home'] else None,
                    'away': matchup_data['pitchers']['away']['data_source'] if matchup_data['pitchers']['away'] else None
                }
            }
        except Exception as e:
            debug_info['enhanced_analyzer'] = {
                'success': False,
                'error': str(e)
            }
        
        return jsonify(debug_info)
        
    except Exception as e:
        logger.error(f"調試投手數據失敗: {e}")
        return jsonify({'error': str(e)}), 500