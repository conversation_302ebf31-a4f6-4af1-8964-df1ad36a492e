from flask import Blueprint, render_template, request, jsonify, abort, flash, redirect, url_for
from models.database import Game, Team, Prediction, BoxScore, PlayerGameStats, Player, db
from datetime import datetime, date, timedelta
from sqlalchemy import or_, and_, func
import logging

logger = logging.getLogger(__name__)
import json

games_bp = Blueprint('games', __name__)

@games_bp.route('/')
def games_list():
    """比賽列表頁面"""
    page = request.args.get('page', 1, type=int)
    selected_date = request.args.get('date', '')
    selected_status = request.args.get('status', '')
    selected_team = request.args.get('team', '')
    
    # 建立查詢
    query = Game.query
    
    # 日期篩選
    if selected_date:
        try:
            filter_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            query = query.filter(Game.date == filter_date)
        except ValueError:
            pass
    
    # 狀態篩選
    if selected_status:
        query = query.filter(Game.game_status == selected_status)
    
    # 球隊篩選
    if selected_team:
        query = query.filter(
            or_(Game.home_team == selected_team, Game.away_team == selected_team)
        )
    
    # 分頁
    games = query.order_by(Game.date.desc(), Game.game_id.desc()).paginate(
        page=page, per_page=12, error_out=False
    )
    
    # 獲取所有球隊用於篩選
    teams = Team.query.order_by(Team.team_code).all()
    
    return render_template('games.html',
                         games=games.items,
                         pagination=games,
                         teams=teams,
                         selected_date=selected_date,
                         selected_status=selected_status,
                         selected_team=selected_team)

@games_bp.route('/<game_id>')
def game_detail(game_id):
    """比賽詳情頁面"""
    game = Game.query.filter_by(game_id=game_id).first_or_404()

    # 獲取球隊信息
    home_team = Team.query.filter_by(team_code=game.home_team).first()
    away_team = Team.query.filter_by(team_code=game.away_team).first()

    # 獲取預測信息
    prediction = Prediction.query.filter_by(game_id=game_id).first()

    # 生成進階預測（如果比賽尚未開始）
    advanced_prediction = None
    if game.game_status in ['scheduled', 'postponed'] and home_team and away_team:
        try:
            from models.ml_predictor import MLBPredictor
            predictor = MLBPredictor()
            advanced_prediction = predictor.predict_game_advanced(
                game.home_team, game.away_team, game.date
            )
        except Exception as e:
            logger.error(f"生成進階預測失敗 {game_id}: {e}")
            advanced_prediction = None

    # 獲取Box Score數據
    box_scores = {}
    if home_team and away_team:
        home_box_score = BoxScore.query.filter_by(
            game_id=game_id,
            team_id=home_team.team_id
        ).first()
        away_box_score = BoxScore.query.filter_by(
            game_id=game_id,
            team_id=away_team.team_id
        ).first()

        box_scores = {
            'home': home_box_score,
            'away': away_box_score
        }

    # 獲取球員比賽統計
    player_stats = {}
    if game.game_status == 'completed':
        # 獲取主隊球員統計 (使用 LEFT JOIN 確保統計數據不會丟失)
        home_player_stats = db.session.query(PlayerGameStats, Player).outerjoin(
            Player, PlayerGameStats.player_id == Player.player_id
        ).filter(
            PlayerGameStats.game_id == game_id,
            PlayerGameStats.team_id == home_team.team_id if home_team else None
        ).order_by(
            PlayerGameStats.batting_order.asc().nullslast(),
            PlayerGameStats.innings_pitched.desc()
        ).all()

        # 獲取客隊球員統計 (使用 LEFT JOIN 確保統計數據不會丟失)
        away_player_stats = db.session.query(PlayerGameStats, Player).outerjoin(
            Player, PlayerGameStats.player_id == Player.player_id
        ).filter(
            PlayerGameStats.game_id == game_id,
            PlayerGameStats.team_id == away_team.team_id if away_team else None
        ).order_by(
            PlayerGameStats.batting_order.asc().nullslast(),
            PlayerGameStats.innings_pitched.desc()
        ).all()

        player_stats = {
            'home': home_player_stats,
            'away': away_player_stats
        }

    # 獲取對戰記錄
    h2h_games = Game.query.filter(
        or_(
            and_(Game.home_team == game.home_team, Game.away_team == game.away_team),
            and_(Game.home_team == game.away_team, Game.away_team == game.home_team)
        ),
        Game.game_status == 'completed',
        Game.game_id != game_id
    ).order_by(Game.date.desc()).limit(10).all()

    return render_template('game_detail.html',
                         game=game,
                         home_team=home_team,
                         away_team=away_team,
                         prediction=prediction,
                         advanced_prediction=advanced_prediction,
                         box_scores=box_scores,
                         player_stats=player_stats,
                         h2h_games=h2h_games)

@games_bp.route('/<game_id>/preview')
def game_preview(game_id):
    """比賽預覽頁面 - 類似MLB官網的比賽預覽"""
    try:
        from models.game_preview import GamePreview
        preview_service = GamePreview()
        preview_data = preview_service.get_game_preview(game_id)

        return render_template('game_preview.html', preview=preview_data)
    except Exception as e:
        logger.error(f"比賽預覽頁面錯誤: {e}")
        flash(f'載入比賽預覽失敗: {e}', 'error')
        return redirect(url_for('games.game_detail', game_id=game_id))

@games_bp.route('/<game_id>/analysis')
def game_analysis(game_id):
    """比賽分析頁面"""
    game = Game.query.filter_by(game_id=game_id).first_or_404()

    # 獲取球隊信息
    home_team = Team.query.filter_by(team_code=game.home_team).first()
    away_team = Team.query.filter_by(team_code=game.away_team).first()

    # 分析數據
    analysis_data = analyze_game_matchup(game, home_team, away_team)

    return render_template('game_analysis.html',
                         game=game,
                         home_team=home_team,
                         away_team=away_team,
                         analysis=analysis_data)

@games_bp.route('/today')
def todays_games():
    """今日比賽"""
    today = date.today()
    games = Game.query.filter(Game.date == today).order_by(Game.game_id).all()
    
    return render_template('todays_games.html', games=games, date=today, timedelta=timedelta)

@games_bp.route('/api/<game_id>/advanced_prediction')
def api_generate_advanced_prediction(game_id):
    """API端點：生成進階預測"""
    try:
        game = Game.query.filter_by(game_id=game_id).first_or_404()

        # 檢查比賽狀態
        if game.game_status not in ['scheduled', 'postponed']:
            return jsonify({
                'success': False,
                'error': '只能為尚未開始的比賽生成預測'
            }), 400

        # 獲取球隊信息
        home_team = Team.query.filter_by(team_code=game.home_team).first()
        away_team = Team.query.filter_by(team_code=game.away_team).first()

        if not home_team or not away_team:
            return jsonify({
                'success': False,
                'error': '找不到球隊信息'
            }), 404

        # 生成進階預測
        from models.ml_predictor import MLBPredictor
        predictor = MLBPredictor()
        advanced_prediction = predictor.predict_game_advanced(
            game.home_team, game.away_team, game.date
        )

        return jsonify({
            'success': True,
            'prediction': advanced_prediction
        })

    except Exception as e:
        logger.error(f"生成進階預測失敗 {game_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@games_bp.route('/api/<game_id>')
def api_game_detail(game_id):
    """API端點：比賽詳情"""
    game = Game.query.filter_by(game_id=game_id).first_or_404()

    # 獲取預測
    prediction = Prediction.query.filter_by(game_id=game_id).first()
    
    game_data = game.to_dict()
    if prediction:
        game_data['prediction'] = prediction.to_dict()
    
    return jsonify(game_data)

@games_bp.route('/api/schedule')
def api_schedule():
    """API端點：比賽日程"""
    start_date = request.args.get('start_date', date.today().isoformat())
    end_date = request.args.get('end_date', (date.today() + timedelta(days=7)).isoformat())
    
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d').date()
        end = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'error': '日期格式錯誤'}), 400
    
    games = Game.query.filter(
        Game.date >= start,
        Game.date <= end
    ).order_by(Game.date, Game.game_id).all()
    
    schedule = {}
    for game in games:
        date_str = game.date.isoformat()
        if date_str not in schedule:
            schedule[date_str] = []
        schedule[date_str].append(game.to_dict())
    
    return jsonify(schedule)

def analyze_game_matchup(game, home_team, away_team):
    """分析比賽對戰"""
    analysis = {
        'home_advantages': [],
        'away_advantages': [],
        'key_factors': {},
        'prediction_factors': {}
    }

    # 主場優勢
    if home_team:
        analysis['home_advantages'].append(f"{home_team.team_name} 在 {home_team.venue_name} 主場作戰")

    # 獲取最近表現
    home_recent = get_team_recent_form(game.home_team)
    away_recent = get_team_recent_form(game.away_team)

    if home_recent['win_rate'] > away_recent['win_rate']:
        analysis['home_advantages'].append(f"最近10場勝率較高 ({home_recent['win_rate']:.1%} vs {away_recent['win_rate']:.1%})")
    else:
        analysis['away_advantages'].append(f"最近10場勝率較高 ({away_recent['win_rate']:.1%} vs {home_recent['win_rate']:.1%})")

    # 對戰記錄
    h2h_record = get_head_to_head_record(game.home_team, game.away_team)
    if h2h_record['total_games'] > 0:
        home_wins = h2h_record['home_wins']
        away_wins = h2h_record['total_games'] - home_wins

        if home_wins > away_wins:
            analysis['home_advantages'].append(f"對戰記錄佔優 ({home_wins}勝{away_wins}敗)")
        elif away_wins > home_wins:
            analysis['away_advantages'].append(f"對戰記錄佔優 ({away_wins}勝{home_wins}敗)")

    # 詳細關鍵因素分析
    analysis['key_factors'] = get_detailed_key_factors(game, home_team, away_team, home_recent, away_recent)

    return analysis

def get_team_recent_form(team_code, games_count=10):
    """獲取球隊最近表現"""
    recent_games = Game.query.filter(
        or_(Game.home_team == team_code, Game.away_team == team_code),
        Game.game_status == 'completed'
    ).order_by(Game.date.desc()).limit(games_count).all()
    
    wins = 0
    for game in recent_games:
        # 確保分數不是 None
        if game.home_score is None or game.away_score is None:
            continue

        if game.home_team == team_code:
            if game.home_score > game.away_score:
                wins += 1
        else:
            if game.away_score > game.home_score:
                wins += 1
    
    return {
        'wins': wins,
        'losses': len(recent_games) - wins,
        'total_games': len(recent_games),
        'win_rate': wins / len(recent_games) if recent_games else 0
    }

def get_head_to_head_record(team1, team2):
    """獲取對戰記錄"""
    h2h_games = Game.query.filter(
        or_(
            and_(Game.home_team == team1, Game.away_team == team2),
            and_(Game.home_team == team2, Game.away_team == team1)
        ),
        Game.game_status == 'completed'
    ).all()
    
    team1_wins = 0
    for game in h2h_games:
        # 確保分數不是 None
        if game.home_score is None or game.away_score is None:
            continue

        if game.home_team == team1:
            if game.home_score > game.away_score:
                team1_wins += 1
        else:
            if game.away_score > game.home_score:
                team1_wins += 1
    
    return {
        'total_games': len(h2h_games),
        'home_wins': team1_wins,
        'away_wins': len(h2h_games) - team1_wins
    }

def get_detailed_key_factors(game, home_team, away_team, home_recent, away_recent):
    """獲取詳細的關鍵因素分析"""
    factors = {}

    # 1. 投手對戰
    factors['投手對戰'] = {
        'title': '投手對戰',
        'icon': 'fas fa-baseball-ball',
        'details': [
            '先發投手ERA對比',
            '投手最近5場表現',
            '對戰球隊打擊率',
            '投手球種分析',
            '投球局數統計'
        ],
        'analysis': get_pitcher_analysis(game, home_team, away_team)
    }

    # 2. 打線深度
    factors['打線深度'] = {
        'title': '打線深度',
        'icon': 'fas fa-users',
        'details': [
            '主力打者打擊率',
            '長打能力分析',
            '上壘率統計',
            '關鍵時刻打擊',
            '替補打者實力'
        ],
        'analysis': get_batting_analysis(game, home_team, away_team, home_recent, away_recent)
    }

    # 3. 牛棚實力
    factors['牛棚實力'] = {
        'title': '牛棚實力',
        'icon': 'fas fa-shield-alt',
        'details': [
            '後援投手ERA',
            '救援成功率',
            '最近使用頻率',
            '關鍵局面表現',
            '牛棚深度分析'
        ],
        'analysis': get_bullpen_analysis(game, home_team, away_team)
    }

    # 4. 傷兵情況
    factors['傷兵情況'] = {
        'title': '傷兵情況',
        'icon': 'fas fa-user-injured',
        'details': [
            '主力球員傷勢',
            '替補球員能力',
            '陣容完整度',
            '近期復出球員',
            '傷兵對戰力影響'
        ],
        'analysis': get_injury_analysis(game, home_team, away_team)
    }

    # 5. 天氣條件
    factors['天氣條件'] = {
        'title': '天氣條件',
        'icon': 'fas fa-cloud-sun',
        'details': [
            '比賽日天氣預報',
            '風向風速影響',
            '溫度濕度條件',
            '球場特性分析',
            '天氣對比賽影響'
        ],
        'analysis': get_weather_analysis(game, home_team)
    }

    return factors

def get_pitcher_analysis(game, home_team, away_team):
    """投手對戰分析"""
    analysis = {
        'home_pitcher': {
            'name': '待確認',
            'era': 'N/A',
            'recent_form': '近期表現良好',
            'vs_opponent': '對戰記錄平均'
        },
        'away_pitcher': {
            'name': '待確認',
            'era': 'N/A',
            'recent_form': '近期表現良好',
            'vs_opponent': '對戰記錄平均'
        },
        'matchup_advantage': '勢均力敵',
        'key_points': [
            '兩隊先發投手實力相當',
            '需關注投手近期狀態',
            '對戰歷史記錄參考價值有限'
        ]
    }
    return analysis

def get_batting_analysis(game, home_team, away_team, home_recent, away_recent):
    """打線深度分析"""
    analysis = {
        'home_batting': {
            'avg': home_recent.get('avg_runs', 4.5),
            'power': '中等',
            'depth': '良好',
            'recent_form': f"最近10場平均得分 {home_recent.get('avg_runs', 4.5):.1f}"
        },
        'away_batting': {
            'avg': away_recent.get('avg_runs', 4.2),
            'power': '中等',
            'depth': '良好',
            'recent_form': f"最近10場平均得分 {away_recent.get('avg_runs', 4.2):.1f}"
        },
        'advantage': '主隊略優' if home_recent.get('avg_runs', 4.5) > away_recent.get('avg_runs', 4.2) else '客隊略優',
        'key_points': [
            '打線整體實力均衡',
            '關鍵在於臨場發揮',
            '替補打者深度影響後段比賽'
        ]
    }
    return analysis

def get_bullpen_analysis(game, home_team, away_team):
    """牛棚實力分析"""
    analysis = {
        'home_bullpen': {
            'era': 'N/A',
            'save_rate': 'N/A',
            'recent_usage': '使用頻率正常',
            'depth': '深度良好'
        },
        'away_bullpen': {
            'era': 'N/A',
            'save_rate': 'N/A',
            'recent_usage': '使用頻率正常',
            'depth': '深度良好'
        },
        'advantage': '勢均力敵',
        'key_points': [
            '兩隊牛棚實力相當',
            '關鍵在於使用時機',
            '後段比賽將是決勝關鍵'
        ]
    }
    return analysis

def get_injury_analysis(game, home_team, away_team):
    """傷兵情況分析"""
    analysis = {
        'home_injuries': {
            'major_injuries': 0,
            'minor_injuries': 0,
            'impact': '輕微影響',
            'key_players': []
        },
        'away_injuries': {
            'major_injuries': 0,
            'minor_injuries': 0,
            'impact': '輕微影響',
            'key_players': []
        },
        'advantage': '無明顯影響',
        'key_points': [
            '兩隊主力陣容相對完整',
            '替補球員能力足夠',
            '傷兵情況對比賽影響有限'
        ]
    }
    return analysis

def get_weather_analysis(game, home_team):
    """天氣條件分析"""
    analysis = {
        'conditions': {
            'temperature': '適中',
            'humidity': '正常',
            'wind': '微風',
            'precipitation': '無降雨'
        },
        'venue_factors': {
            'field_type': '天然草皮' if home_team else '人工草皮',
            'dimensions': '標準尺寸',
            'altitude': '海平面',
            'dome': '露天球場' if home_team else '室內球場'
        },
        'impact': '天氣條件良好',
        'key_points': [
            '天氣條件適合比賽進行',
            '風向對長打影響輕微',
            '球場條件有利於正常發揮'
        ]
    }
    return analysis
