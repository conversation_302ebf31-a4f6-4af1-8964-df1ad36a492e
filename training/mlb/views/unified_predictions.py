"""
統一目標性博彩預測系統路由
"""

from flask import Blueprint, render_template, request, jsonify, current_app
from datetime import date, datetime, timedelta
import logging
from models.database import db

# 延遲導入以避免循環導入
def get_unified_predictor():
    """獲取統一預測器實例"""
    from flask import current_app
    from models.unified_betting_predictor import UnifiedBettingPredictor
    return UnifiedBettingPredictor(current_app)

logger = logging.getLogger(__name__)

def _get_over_under_recommendation(pred):
    """獲取大小分推薦"""
    if not pred.over_probability or not pred.under_probability:
        return 'N/A'

    if pred.over_probability > pred.under_probability:
        return f'大分 ({pred.over_probability * 100:.1f}%)'
    else:
        return f'小分 ({pred.under_probability * 100:.1f}%)'

def _get_detailed_over_under_analysis(pred, betting_odds=None):
    """獲取詳細的大小分分析（支持盤口資料）"""
    try:
        # 計算預測總分
        predicted_total = None
        if pred.predicted_total_runs:
            predicted_total = pred.predicted_total_runs
        elif pred.predicted_home_score and pred.predicted_away_score:
            predicted_total = pred.predicted_home_score + pred.predicted_away_score

        if not predicted_total:
            return None

        # 獲取大小分線（優先使用真實盤口資料，否則生成模擬線）
        over_under_line = None
        is_real_line = False
        data_source = "unknown"

        # 1. 優先使用數據庫中的真實博彩盤口
        if betting_odds and betting_odds.get('totals') and betting_odds['totals'].get('total_point'):
            over_under_line = betting_odds['totals']['total_point']
            is_real_line = True
            data_source = "database_real"
        # 2. 使用預測器計算的盤口（可能是真實或模擬）
        elif pred.over_under_line and pred.over_under_line > 0:
            over_under_line = pred.over_under_line
            # 檢查是否來自真實API
            if hasattr(pred, 'model_version') and 'real_odds' in str(pred.model_version):
                is_real_line = True
                data_source = "api_real"
            else:
                is_real_line = False
                data_source = "algorithm_simulated"
        else:
            # 3. 最後生成模擬大小分線（基於預測總分）
            over_under_line = round(predicted_total * 0.95, 1)  # 稍微低於預測總分
            is_real_line = False
            data_source = "generated_simulated"

        analysis = {
            'line': over_under_line,
            'predicted_total': predicted_total,
            'difference': predicted_total - over_under_line,
            'over_probability': pred.over_probability or 0,
            'under_probability': pred.under_probability or 0,
            'confidence': pred.over_under_confidence or 0,
            'is_real_line': is_real_line,
            'data_source': data_source,
            'data_source_text': _get_data_source_text(data_source, is_real_line)
        }

        # 判断推荐（基於預測總分與盤口線的比較）
        if predicted_total > over_under_line:
            analysis['recommendation'] = 'over'
            analysis['recommendation_text'] = '大分'
        else:
            analysis['recommendation'] = 'under'
            analysis['recommendation_text'] = '小分'

        # 如果有概率數據，使用概率推薦
        if pred.over_probability and pred.under_probability:
            if pred.over_probability > pred.under_probability:
                analysis['recommendation'] = 'over'
                analysis['recommendation_text'] = f'大分 ({pred.over_probability * 100:.1f}%)'
            else:
                analysis['recommendation'] = 'under'
                analysis['recommendation_text'] = f'小分 ({pred.under_probability * 100:.1f}%)'

        # 分析强度
        diff_abs = abs(analysis['difference'])
        if diff_abs > 2:
            analysis['strength'] = 'strong'
        elif diff_abs > 1:
            analysis['strength'] = 'moderate'
        else:
            analysis['strength'] = 'weak'

        return analysis

    except Exception as e:
        logger.error(f"獲取大小分分析失敗: {e}")
        return None

def _get_data_source_text(data_source: str, is_real: bool) -> str:
    """獲取數據源說明文本"""
    source_texts = {
        'database_real': '真實盤口 (數據庫)',
        'api_real': '真實盤口 (API)',
        'algorithm_simulated': '算法計算',
        'generated_simulated': '模擬生成',
        'unknown': '未知來源'
    }

    text = source_texts.get(data_source, '未知來源')

    if not is_real and data_source in ['algorithm_simulated', 'generated_simulated']:
        text += ' (非真實博彩盤口)'

    return text

def _get_betting_analysis(prediction, game, totals_bet, spreads_bet):
    """分析預測結果與盤口資料"""
    try:
        analysis = {
            'over_under_analysis': None,
            'spread_analysis': None,
            'recommendations': []
        }

        if not prediction.predicted_home_score or not prediction.predicted_away_score:
            return analysis

        predicted_total = prediction.predicted_home_score + prediction.predicted_away_score
        predicted_home_wins = prediction.predicted_home_score > prediction.predicted_away_score
        predicted_margin = abs(prediction.predicted_home_score - prediction.predicted_away_score)

        # 大小分分析
        if totals_bet and totals_bet.total_point:
            total_line = totals_bet.total_point
            over_under_recommendation = "大分" if predicted_total > total_line else "小分"
            confidence_level = "高" if abs(predicted_total - total_line) > 1.5 else "中" if abs(predicted_total - total_line) > 0.5 else "低"

            analysis['over_under_analysis'] = {
                'line': total_line,
                'predicted_total': predicted_total,
                'recommendation': over_under_recommendation,
                'difference': round(predicted_total - total_line, 1),
                'confidence': confidence_level
            }

            analysis['recommendations'].append(f"{over_under_recommendation} ({confidence_level}信心)")

        # 讓分分析
        if spreads_bet and spreads_bet.home_spread_point:
            home_spread = spreads_bet.home_spread_point
            # 正數表示主隊讓分，負數表示主隊受讓
            if home_spread > 0:  # 主隊讓分
                spread_recommendation = "客隊受讓" if predicted_margin < home_spread else "主隊讓分"
            else:  # 主隊受讓
                spread_recommendation = "主隊受讓" if predicted_margin < abs(home_spread) else "客隊讓分"

            spread_confidence = "高" if abs(predicted_margin - abs(home_spread)) > 1.5 else "中" if abs(predicted_margin - abs(home_spread)) > 0.5 else "低"

            analysis['spread_analysis'] = {
                'line': home_spread,
                'predicted_margin': predicted_margin,
                'predicted_winner': game.home_team if predicted_home_wins else game.away_team,
                'recommendation': spread_recommendation,
                'confidence': spread_confidence
            }

            analysis['recommendations'].append(f"{spread_recommendation} ({spread_confidence}信心)")

        return analysis

    except Exception as e:
        logger.error(f"盤口分析失敗: {e}")
        return {
            'over_under_analysis': None,
            'spread_analysis': None,
            'recommendations': []
        }

def _get_run_line_recommendation(pred, betting_odds=None):
    """獲取讓分盤推薦（支持盤口資料）"""
    if not pred.predicted_home_score or not pred.predicted_away_score:
        return 'N/A'

    score_diff = pred.predicted_home_score - pred.predicted_away_score

    # 檢查是否有真實盤口資料
    if betting_odds and betting_odds.get('spreads') and betting_odds['spreads'].get('home_spread_point'):
        spread_line = betting_odds['spreads']['home_spread_point']

        # 基於預測分差與盤口線比較
        if score_diff > spread_line:
            return f'主隊讓分 ({spread_line})'
        else:
            return f'客隊受讓 ({spread_line})'
    else:
        # 沒有盤口資料時的傳統推薦
        if score_diff > 1.5:
            return '主隊 -1.5'
        elif score_diff < -1.5:
            return '客隊 -1.5'
        else:
            return '接近盤口'

# 創建Blueprint
unified_predictions_bp = Blueprint('unified_predictions', __name__, url_prefix='/unified')

@unified_predictions_bp.route('/')
def index():
    """統一預測系統主頁 - 按需加載數據"""
    try:
        # 只獲取基本信息，不自動執行預測
        today = date.today()

        # 檢查是否有今日預測數據
        predictor = get_unified_predictor()

        # 獲取現有的預測歷史（不生成新的）
        history = None
        analytics = None
        daily_summary = None

        # 檢查是否請求加載數據
        load_data = request.args.get('load_data', 'false').lower() == 'true'

        if load_data:
            # 只有在明確請求時才加載數據
            try:
                history = predictor.get_prediction_history(days_back=7, include_results=True)
                analytics = predictor.get_performance_analytics(days_back=30)

                # 檢查今日是否已有預測
                from models.database import Prediction, Game
                from datetime import timedelta

                existing_predictions = Prediction.query.join(Game).filter(
                    Game.date >= today,
                    Game.date < today + timedelta(days=1)
                ).count()

                # 嘗試獲取預測數據（今日或最近的預測）
                try:
                    # 獲取今日預測數據
                    from models.database import Prediction, Game
                    from datetime import timedelta

                    predictions = Prediction.query.join(Game).filter(
                        Game.date >= today,
                        Game.date < today + timedelta(days=1)
                    ).all()

                    # 如果今日沒有預測，嘗試獲取昨日的預測作為示例
                    if not predictions:
                        yesterday = today - timedelta(days=1)
                        predictions = Prediction.query.join(Game).filter(
                            Game.date >= yesterday,
                            Game.date < yesterday + timedelta(days=1)
                        ).all()
                        display_date = yesterday.isoformat()
                        date_label = "昨日"
                    else:
                        display_date = today.isoformat()
                        date_label = "今日"

                    if predictions:
                        # 格式化預測數據
                        prediction_data = []

                        for pred in predictions:
                            # 確保 pred 是 Prediction 對象
                            if not hasattr(pred, 'game_id'):
                                continue

                            game = Game.query.filter_by(game_id=pred.game_id).first()
                            if game:
                                # 創建質量評估對象 - Prediction模型沒有quality_assessment字段，需要創建默認值
                                class QualityAssessment:
                                    def __init__(self, confidence=0.0):
                                        # 基於信心度創建簡單的質量評估
                                        if confidence > 0.8:
                                            self.grade = 'A'
                                            self.description = '優秀'
                                            self.score = 85
                                        elif confidence > 0.6:
                                            self.grade = 'B'
                                            self.description = '良好'
                                            self.score = 70
                                        elif confidence > 0.4:
                                            self.grade = 'C'
                                            self.description = '一般'
                                            self.score = 55
                                        else:
                                            self.grade = 'D'
                                            self.description = '較差'
                                            self.score = 40

                                confidence_value = pred.confidence or 0.0
                                quality_obj = QualityAssessment(confidence_value)

                                pred_dict = {
                                    'success': True,
                                    'game_info': {
                                        'home_team': game.home_team,
                                        'away_team': game.away_team
                                    },
                                    'predictions': {
                                        'score': {
                                            'home_score': pred.predicted_home_score,
                                            'away_score': pred.predicted_away_score,
                                            'confidence': confidence_value  # 添加 confidence 字段
                                        },
                                        'win_loss': getattr(pred, 'win_loss_prediction', None),
                                        'over_under': getattr(pred, 'over_under_prediction', None),
                                        'run_line': getattr(pred, 'run_line_prediction', None)
                                    },
                                    'over_under_analysis': _get_detailed_over_under_analysis(pred),
                                    'quality_assessment': quality_obj
                                }
                                prediction_data.append(pred_dict)

                        daily_summary = {
                            'success': True,
                            'date': display_date,
                            'message': f'{date_label}已有 {len(predictions)} 個預測',
                            'predictions_count': len(predictions),
                            'auto_generated': False,
                            'predictions': prediction_data
                        }
                    else:
                        daily_summary = {
                            'success': True,
                            'date': today.isoformat(),
                            'message': f'今日已有 {existing_predictions} 個預測',
                            'predictions_count': existing_predictions,
                            'auto_generated': False
                        }
                except Exception as e:
                    logger.error(f"獲取預測數據失敗: {e}")
                    daily_summary = {
                        'success': True,
                        'date': today.isoformat(),
                        'message': f'今日已有 {existing_predictions} 個預測',
                        'predictions_count': existing_predictions,
                        'auto_generated': False
                    }

            except Exception as e:
                logger.error(f"加載數據失敗: {e}")
                analytics = {'success': False, 'error': str(e)}

        return render_template('unified_predictions/index.html',
                             daily_summary=daily_summary,
                             history=history,
                             analytics=analytics,
                             current_date=today.isoformat(),
                             data_loaded=load_data)

    except Exception as e:
        logger.error(f"載入統一預測主頁失敗: {e}")
        return render_template('unified_predictions/index.html',
                             error_message=f'載入失敗: {str(e)}',
                             current_date=date.today().isoformat())

@unified_predictions_bp.route('/predict')
def predict_page():
    """預測頁面"""
    try:
        target_date_str = request.args.get('date')
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()
        
        return render_template('unified_predictions/predict.html',
                             target_date=target_date.isoformat())
        
    except Exception as e:
        logger.error(f"載入預測頁面失敗: {e}")
        return render_template('unified_predictions/predict.html',
                             error_message=f'載入失敗: {str(e)}')

@unified_predictions_bp.route('/history')
def history_page():
    """預測歷史頁面"""
    try:
        days_back = request.args.get('days', 30, type=int)
        
        predictor = get_unified_predictor()
        history = predictor.get_prediction_history(days_back=days_back, include_results=True)
        
        return render_template('unified_predictions/history.html',
                             history=history,
                             days_back=days_back)
        
    except Exception as e:
        logger.error(f"載入歷史頁面失敗: {e}")
        return render_template('unified_predictions/history.html',
                             error_message=f'載入失敗: {str(e)}')

@unified_predictions_bp.route('/analytics')
def analytics_page():
    """性能分析頁面"""
    try:
        days_back = request.args.get('days', 30, type=int)

        predictor = get_unified_predictor()
        analytics = predictor.get_performance_analytics(days_back=days_back)

        return render_template('unified_predictions/analytics.html',
                             analytics=analytics,
                             days_back=days_back)

    except Exception as e:
        logger.error(f"載入分析頁面失敗: {e}")
        return render_template('unified_predictions/analytics.html',
                             error_message=f'載入失敗: {str(e)}')

@unified_predictions_bp.route('/custom_predict')
def custom_predict_page():
    """自定義日期預測頁面"""
    try:
        from datetime import datetime, timedelta
        return render_template('unified_predictions/custom_predict.html',
                             datetime=datetime,
                             timedelta=timedelta)
    except Exception as e:
        logger.error(f"載入自定義預測頁面失敗: {e}")
        return render_template('unified_predictions/custom_predict.html',
                             error_message=f'載入失敗: {str(e)}',
                             datetime=datetime,
                             timedelta=timedelta)

@unified_predictions_bp.route('/custom_game')
def custom_game_predict_page():
    """自定義單場比賽預測頁面"""
    try:
        from datetime import datetime, timedelta
        return render_template('unified_predictions/custom_game.html',
                             datetime=datetime,
                             timedelta=timedelta)
    except Exception as e:
        logger.error(f"載入自定義單場預測頁面失敗: {e}")
        return render_template('unified_predictions/custom_game.html',
                             error_message=f'載入失敗: {str(e)}',
                             datetime=datetime,
                             timedelta=timedelta)

@unified_predictions_bp.route('/query')
def query_page():
    """查詢預測結果頁面"""
    try:
        date_str = request.args.get('date')
        predictions_data = None

        if date_str:
            from datetime import datetime, timedelta
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            predictor = get_unified_predictor()

            # 獲取該日期的預測
            with current_app.app_context():
                from models.database import Prediction, Game
                predictions = Prediction.query.join(Game).filter(
                    Game.date >= target_date,
                    Game.date < target_date + timedelta(days=1),
                    Prediction.model_version == predictor.model_version
                ).all()

                if predictions:
                    # 格式化預測數據
                    prediction_list = []
                    for pred in predictions:
                        game = Game.query.filter_by(game_id=pred.game_id).first()

                        pred_dict = {
                            'game_id': pred.game_id,
                            'home_team': game.home_team if game else None,
                            'away_team': game.away_team if game else None,
                            'predicted_home_score': pred.predicted_home_score,
                            'predicted_away_score': pred.predicted_away_score,
                            'predicted_total_runs': pred.predicted_total_runs,
                            'confidence': pred.confidence,
                            'over_under_line': pred.over_under_line,
                            'over_under_prediction': _get_over_under_recommendation(pred),
                            'over_under_analysis': _get_detailed_over_under_analysis(pred),
                            'run_line': getattr(pred, 'run_line', None),
                            'run_line_prediction': _get_run_line_recommendation(pred),
                            'prediction_time': pred.created_at if pred.created_at else None,
                            # 實際結果（如果有）
                            'actual_home_score': pred.actual_home_score,
                            'actual_away_score': pred.actual_away_score,
                            'actual_total_runs': pred.actual_total_runs,
                            'is_correct': pred.is_correct,
                            'over_under_correct': pred.over_under_correct,
                            'score_difference': pred.score_difference,
                            'total_runs_difference': pred.total_runs_difference
                        }
                        prediction_list.append(pred_dict)

                    # 計算統計
                    total_predictions = len(predictions)
                    correct_predictions = sum(1 for p in predictions if p.is_correct is True)
                    accuracy_rate = (correct_predictions / total_predictions * 100) if total_predictions > 0 else 0

                    predictions_data = {
                        'date': date_str,
                        'total_predictions': total_predictions,
                        'correct_predictions': correct_predictions,
                        'accuracy_rate': round(accuracy_rate, 2),
                        'predictions': prediction_list
                    }

        from datetime import datetime, timedelta
        return render_template('unified_predictions/query.html',
                             predictions_data=predictions_data,
                             query_date=date_str,
                             datetime=datetime,
                             timedelta=timedelta)

    except Exception as e:
        logger.error(f"載入查詢頁面失敗: {e}")
        from datetime import datetime, timedelta
        return render_template('unified_predictions/query.html',
                             error_message=f'載入失敗: {str(e)}',
                             query_date=date_str,
                             datetime=datetime,
                             timedelta=timedelta)

# ==================== API 路由 ====================

@unified_predictions_bp.route('/api/predict/daily')
def api_predict_daily():
    """API: 生成日預測"""
    try:
        target_date_str = request.args.get('date')
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()
        
        predictor = get_unified_predictor()
        result = predictor.generate_daily_predictions(target_date)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API生成日預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@unified_predictions_bp.route('/api/predict/game')
def api_predict_game():
    """API: 生成單場比賽預測"""
    try:
        game_id = request.args.get('game_id')
        if not game_id:
            return jsonify({
                'success': False,
                'error': '缺少game_id參數'
            }), 400
        
        target_date_str = request.args.get('date')
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()
        
        predictor = get_unified_predictor()
        result = predictor.generate_unified_prediction(game_id, target_date)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API生成單場預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@unified_predictions_bp.route('/api/update_data')
def api_update_data():
    """API: 更新數據"""
    try:
        target_date_str = request.args.get('date')
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()
        
        predictor = get_unified_predictor()
        result = predictor.auto_update_data(target_date)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API更新數據失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@unified_predictions_bp.route('/api/update_results')
def api_update_results():
    """API: 更新預測結果"""
    try:
        target_date_str = request.args.get('date')
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today() - timedelta(days=1)  # 默認更新昨天
        
        predictor = get_unified_predictor()
        result = predictor.update_prediction_results(target_date)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API更新結果失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@unified_predictions_bp.route('/api/train_models')
def api_train_models():
    """API: 訓練模型"""
    try:
        predictor = get_unified_predictor()
        
        # 初始化預測模型
        predictor.initialize_prediction_models()

        # 檢查是否有可用的預測模型
        if predictor.basic_predictor or (predictor.improved_predictor and predictor.improved_predictor.is_trained):
            model_info = []
            if predictor.basic_predictor:
                model_info.append('基礎模型')
            if predictor.improved_predictor and predictor.improved_predictor.is_trained:
                model_info.append('高級模型')

            return jsonify({
                'success': True,
                'message': f'模型初始化完成: {", ".join(model_info)}',
                'model_version': predictor.model_version,
                'available_models': model_info
            })
        else:
            return jsonify({
                'success': False,
                'error': '沒有可用的預測模型'
            }), 500
        
    except Exception as e:
        logger.error(f"API訓練模型失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@unified_predictions_bp.route('/api/train_advanced_models', methods=['POST'])
def api_train_advanced_models():
    """API: 訓練高級模型"""
    try:
        from datetime import date, timedelta

        predictor = get_unified_predictor()

        # 確保有高級模型實例
        if not predictor.improved_predictor:
            from models.improved_predictor import ImprovedMLBPredictor
            predictor.improved_predictor = ImprovedMLBPredictor()

        # 設定訓練日期範圍（最近90天）
        end_date = date.today()
        start_date = end_date - timedelta(days=90)

        # 訓練高級模型
        training_result = predictor.improved_predictor.train_advanced_ensemble_models(start_date, end_date)

        return jsonify({
            'success': True,
            'message': '高級模型訓練完成',
            'training_summary': training_result,
            'is_trained': predictor.improved_predictor.is_trained
        })

    except Exception as e:
        logger.error(f"API訓練高級模型失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@unified_predictions_bp.route('/api/predict/custom_date_enhanced', methods=['POST'])
def api_predict_custom_date_enhanced():
    """API: 增強的自定義日期預測（支持多種處理選項）"""
    try:
        data = request.get_json()
        target_date_str = data.get('date')
        prediction_options = data.get('prediction_options', {})

        if not target_date_str:
            return jsonify({
                'success': False,
                'error': '請提供目標日期'
            }), 400

        from datetime import datetime, timedelta
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()

        # 導入增強預測器
        from enhanced_custom_predict import EnhancedCustomPredictor
        enhanced_predictor = EnhancedCustomPredictor()

        # 執行增強預測
        results = enhanced_predictor.predict_with_options(target_date, prediction_options)

        if results['success']:
            # 轉換為前端期望的格式
            formatted_results = {
                'success': True,
                'target_date': target_date_str,
                'total_games': results['total_games'],
                'successful_predictions': results['processed_games'],
                'skipped_games': results['skipped_games'],
                'updated_games': results['updated_games'],
                'new_predictions': results['new_predictions'],
                'backed_up_predictions': results['backed_up_predictions'],
                'postponed_games_excluded': 0,  # 會在後面計算
                'predictions': []
            }

            # 轉換預測結果
            for pred in results['predictions']:
                if pred['action'] not in ['error', 'skipped'] and pred['prediction']:
                    # 獲取博彩數據
                    betting_data = pred['prediction'].get('betting_data', {})
                    over_under_data = betting_data.get('over_under', {})
                    run_line_data = betting_data.get('run_line', {})

                    formatted_pred = {
                        'game_id': pred['game_id'],
                        'matchup': pred['matchup'],
                        'away_team': pred.get('away_team', 'undefined'),
                        'home_team': pred.get('home_team', 'undefined'),
                        'predicted_away_score': pred['prediction']['away_score'],
                        'predicted_home_score': pred['prediction']['home_score'],
                        'predicted_score': f"{pred['prediction']['away_score']:.1f} - {pred['prediction']['home_score']:.1f}",
                        'predicted_total_runs': pred['prediction']['total_runs'],
                        'confidence': pred['prediction']['confidence'],
                        'model_used': prediction_options.get('model_version_suffix', 'enhanced'),
                        'model_version': prediction_options.get('model_version_suffix', 'enhanced'),
                        'training_end_date': pred['prediction'].get('training_end_date', ''),
                        'action_taken': pred['action'],
                        'backed_up': pred.get('backed_up', False),
                        'actual_away_score': pred.get('actual_away_score'),
                        'actual_home_score': pred.get('actual_home_score'),
                        # 博彩數據
                        'over_under_line': over_under_data.get('line', 'N/A'),
                        'over_under_recommendation': over_under_data.get('recommendation', 'N/A'),
                        'run_line': run_line_data.get('line', 'N/A'),
                        'run_line_recommendation': run_line_data.get('recommendation', 'N/A')
                    }
                    formatted_results['predictions'].append(formatted_pred)

            return jsonify(formatted_results)
        else:
            return jsonify({
                'success': False,
                'error': results.get('error', '預測失敗'),
                'details': results
            }), 500

    except Exception as e:
        logger.error(f"增強自定義日期預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@unified_predictions_bp.route('/api/predict/custom_date', methods=['POST'])
def api_predict_custom_date():
    """API: 為指定日期生成預測（使用該日期-1的數據訓練）"""
    try:
        data = request.get_json()
        target_date_str = data.get('date')

        if not target_date_str:
            return jsonify({
                'success': False,
                'error': '請提供目標日期'
            }), 400

        from datetime import datetime, timedelta
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        training_end_date = target_date - timedelta(days=1)  # 使用目標日期-1的數據

        predictor = get_unified_predictor()

        # 獲取該日期的比賽，並檢查是否排除延期比賽
        exclude_postponed = data.get('exclude_postponed', True)  # 默認排除延期比賽

        with current_app.app_context():
            from models.database import Game

            # 基本查詢
            query = Game.query.filter(
                Game.date >= target_date,
                Game.date < target_date + timedelta(days=1)
            )

            # 如果選擇排除延期比賽，添加過濾條件
            if exclude_postponed:
                # 使用正確的延期檢測邏輯：檢查game_status
                all_games = query.all()
                games = []
                postponed_games = []

                for game in all_games:
                    # 檢查比賽狀態是否為延期相關
                    if game.game_status in ['postponed', 'rescheduled', 'cancelled']:
                        postponed_games.append(game)
                    else:
                        games.append(game)

                logger.info(f"排除了 {len(postponed_games)} 場延期/取消比賽")
                if postponed_games:
                    logger.info("延期/取消比賽列表:")
                    for pg in postponed_games:
                        logger.info(f"  - {pg.game_id}: {pg.away_team} @ {pg.home_team} (狀態: {pg.game_status})")
            else:
                games = query.all()

            if not games:
                return jsonify({
                    'success': False,
                    'error': f'沒有找到 {target_date_str} 的比賽數據'
                }), 404

            # 為每場比賽生成預測
            predictions_made = []
            for game in games:
                try:
                    # 使用統一預測器生成預測
                    prediction_result = predictor.predict_game_comprehensive(
                        game.game_id,
                        target_date=target_date,
                        training_end_date=training_end_date
                    )

                    if prediction_result and not prediction_result.get('error'):
                        # 保存預測結果
                        saved_prediction = predictor.save_prediction_result(
                            game.game_id,
                            prediction_result,
                            target_date
                        )

                        # 檢測比賽狀態
                        is_postponed = game.game_status in ['postponed', 'rescheduled', 'cancelled']

                        # 獲取真實博彩盤口數據
                        betting_odds = None
                        try:
                            from models.database import BettingOdds

                            # 查詢bet365的真實盤口數據
                            # 先查詢 'both' 類型的記錄（包含總分線和讓分線）
                            both_odds = BettingOdds.query.filter_by(
                                game_id=game.game_id,
                                market_type='both',
                                bookmaker='bet365'
                            ).first()

                            # 如果沒有 'both' 類型，再查詢分別的記錄
                            totals_odds = both_odds if both_odds and both_odds.total_point else BettingOdds.query.filter_by(
                                game_id=game.game_id,
                                market_type='totals',
                                bookmaker='bet365'
                            ).first()

                            spreads_odds = both_odds if both_odds and both_odds.home_spread_point else BettingOdds.query.filter_by(
                                game_id=game.game_id,
                                market_type='spreads',
                                bookmaker='bet365'
                            ).first()

                            if totals_odds or spreads_odds:
                                betting_odds = {
                                    'totals': {
                                        'total_point': totals_odds.total_point if totals_odds else None,
                                        'over_price': totals_odds.over_price if totals_odds else None,
                                        'under_price': totals_odds.under_price if totals_odds else None,
                                        'bookmaker': 'bet365',
                                        'is_real': True
                                    } if totals_odds else None,
                                    'spreads': {
                                        'home_spread_point': spreads_odds.home_spread_point if spreads_odds else None,
                                        'home_spread_price': spreads_odds.home_spread_price if spreads_odds else None,
                                        'away_spread_price': spreads_odds.away_spread_price if spreads_odds else None,
                                        'bookmaker': 'bet365',
                                        'is_real': True
                                    } if spreads_odds else None
                                }
                        except Exception as odds_error:
                            logger.warning(f"獲取比賽 {game.game_id} 博彩盤口失敗: {odds_error}")

                        # 生成博彩推薦
                        betting_recommendations = _generate_betting_recommendations(prediction_result, betting_odds)

                        predictions_made.append({
                            'game_id': game.game_id,
                            'home_team': game.home_team,
                            'away_team': game.away_team,
                            'predicted_score': f"{prediction_result.get('predicted_away_score', 0)} - {prediction_result.get('predicted_home_score', 0)}",
                            'confidence': prediction_result.get('confidence', 0),
                            'model_used': prediction_result.get('model_used', 'Unknown'),
                            'game_status': game.game_status,
                            'is_postponed': is_postponed,
                            'betting_odds': betting_odds,
                            'betting_recommendations': betting_recommendations
                        })

                except Exception as game_error:
                    logger.warning(f"預測比賽 {game.game_id} 失敗: {game_error}")
                    continue

            # 計算延期比賽統計
            postponed_count = 0
            if exclude_postponed:
                with current_app.app_context():
                    all_games_count = Game.query.filter(
                        Game.date >= target_date,
                        Game.date < target_date + timedelta(days=1)
                    ).count()
                    postponed_count = all_games_count - len(games)

            return jsonify({
                'success': True,
                'message': f'成功為 {target_date_str} 生成 {len(predictions_made)} 個預測' +
                          (f'（排除 {postponed_count} 場延期比賽）' if postponed_count > 0 else ''),
                'target_date': target_date_str,
                'training_end_date': training_end_date.strftime('%Y-%m-%d'),
                'predictions': predictions_made,
                'total_games': len(games),
                'postponed_games_excluded': postponed_count,
                'exclude_postponed': exclude_postponed,
                'successful_predictions': len(predictions_made)
            })

    except Exception as e:
        logger.error(f"自定義日期預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@unified_predictions_bp.route('/api/predict/custom_game')
def api_predict_custom_game():
    """API: 自定義單場比賽預測"""
    try:
        home_team = request.args.get('home_team', '').upper()
        away_team = request.args.get('away_team', '').upper()
        game_date = request.args.get('date', '')

        if not all([home_team, away_team, game_date]):
            return jsonify({
                'success': False,
                'error': '缺少必要參數',
                'required': ['home_team', 'away_team', 'date'],
                'success': False
            }), 400

        # 解析日期
        try:
            prediction_date = datetime.strptime(game_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({
                'error': '日期格式錯誤，請使用 YYYY-MM-DD',
                'success': False
            }), 400

        # 驗證球隊代碼
        valid_teams = ['LAA', 'OAK', 'NYY', 'BOS', 'LAD', 'SF', 'HOU', 'TEX', 'SEA', 'MIN',
                      'CLE', 'DET', 'KC', 'CWS', 'TB', 'TOR', 'BAL', 'ATL', 'NYM', 'PHI',
                      'WSH', 'MIA', 'MIL', 'CHC', 'STL', 'CIN', 'PIT', 'COL', 'AZ', 'SD']

        if home_team not in valid_teams or away_team not in valid_teams:
            return jsonify({
                'error': '無效的球隊代碼',
                'valid_teams': valid_teams,
                'success': False
            }), 400

        if home_team == away_team:
            return jsonify({
                'error': '主隊和客隊不能相同',
                'success': False
            }), 400

        # 使用統一預測器進行預測
        predictor = get_unified_predictor()

        # 計算訓練截止日期（預測日期-1）
        from datetime import timedelta
        training_end_date = prediction_date - timedelta(days=1)

        logger.info(f"自定義單場預測: {away_team} @ {home_team} ({prediction_date}), 訓練數據截止: {training_end_date}")

        # 創建虛擬比賽ID用於預測
        virtual_game_id = f"custom_{away_team}_{home_team}_{prediction_date.strftime('%Y%m%d')}"

        # 使用綜合預測方法
        result = predictor.predict_game_comprehensive(
            virtual_game_id,
            target_date=prediction_date,
            training_end_date=training_end_date
        )

        # 如果綜合預測失敗，回退到直接使用改進預測器
        if result.get('error') or not result.get('success'):
            logger.warning("綜合預測失敗，使用改進預測器...")

            # 確保預測器已初始化
            if not predictor.improved_predictor:
                from models.improved_predictor import ImprovedMLBPredictor
                predictor.improved_predictor = ImprovedMLBPredictor()

            # 進行預測
            result = predictor.improved_predictor.predict_game_advanced(home_team, away_team, training_end_date)

        # 轉換numpy類型為Python原生類型以便JSON序列化
        def convert_numpy_types(obj):
            import numpy as np
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            return obj

        # 清理結果數據
        cleaned_result = convert_numpy_types(result)

        # 添加統一預測系統的博彩推薦
        betting_recommendations = _generate_betting_recommendations(cleaned_result)
        cleaned_result['betting_recommendations'] = betting_recommendations

        return jsonify({
            'success': True,
            'prediction': cleaned_result,
            'request': {
                'home_team': home_team,
                'away_team': away_team,
                'date': game_date
            },
            'model_version': predictor.model_version
        })

    except Exception as e:
        logger.error(f"自定義單場預測失敗: {e}")
        return jsonify({
            'error': f'預測失敗: {str(e)}',
            'success': False
        }), 500

def _generate_betting_recommendations(prediction, betting_odds=None):
    """生成博彩推薦（支持真實盤口數據）"""
    try:
        recommendations = {}

        # 大小分分析
        predicted_total = prediction.get('predicted_total_runs',
                                       prediction.get('predicted_home_score', 0) + prediction.get('predicted_away_score', 0))

        over_under_line = None
        is_real_line = False

        # 優先使用真實盤口數據
        if betting_odds and betting_odds.get('totals') and betting_odds['totals'].get('total_point'):
            over_under_line = betting_odds['totals']['total_point']
            is_real_line = True
        else:
            # 生成模擬大小分線
            over_under_line = round(predicted_total * 0.95, 1) if predicted_total > 0 else 8.5
            is_real_line = False

        # 大小分推薦
        if predicted_total > over_under_line + 0.5:
            ou_recommendation = f'大分 (預測: {predicted_total:.1f})'
        elif predicted_total < over_under_line - 0.5:
            ou_recommendation = f'小分 (預測: {predicted_total:.1f})'
        else:
            ou_recommendation = f'接近盤口 (預測: {predicted_total:.1f})'

        recommendations['over_under_analysis'] = {
            'line': over_under_line,
            'predicted_total': predicted_total,
            'recommendation': ou_recommendation,
            'is_real_line': is_real_line
        }

        # 讓分盤分析
        home_score = prediction.get('predicted_home_score', 0)
        away_score = prediction.get('predicted_away_score', 0)
        predicted_spread = home_score - away_score

        spread_line = None
        is_real_spread = False

        # 優先使用真實盤口數據
        if betting_odds and betting_odds.get('spreads') and betting_odds['spreads'].get('home_spread_point') is not None:
            spread_line = betting_odds['spreads']['home_spread_point']
            is_real_spread = True
        else:
            # 生成模擬讓分線
            spread_line = -1.5 if predicted_spread > 0 else 1.5
            is_real_spread = False

        # 讓分盤推薦
        if predicted_spread > spread_line + 0.5:
            spread_recommendation = f'主隊讓分 ({spread_line})'
        elif predicted_spread < spread_line - 0.5:
            spread_recommendation = f'客隊受讓 ({spread_line})'
        else:
            spread_recommendation = f'接近盤口 ({spread_line})'

        recommendations['spread_analysis'] = {
            'line': spread_line,
            'predicted_spread': predicted_spread,
            'recommendation': spread_recommendation,
            'is_real_line': is_real_spread
        }

        # 勝負推薦
        home_win_prob = prediction.get('home_win_probability', 0.5)
        if home_win_prob > 0.6:
            recommendations['moneyline'] = '主隊勝'
        elif home_win_prob < 0.4:
            recommendations['moneyline'] = '客隊勝'
        else:
            recommendations['moneyline'] = '勢均力敵'

        # 信心等級
        confidence = prediction.get('confidence', 0)
        if confidence > 0.8:
            recommendations['confidence_level'] = '高信心'
        elif confidence > 0.6:
            recommendations['confidence_level'] = '中等信心'
        else:
            recommendations['confidence_level'] = '低信心'

        return recommendations

    except Exception as e:
        logger.error(f"生成博彩推薦失敗: {e}")
        return {
            'over_under_analysis': {
                'line': None,
                'predicted_total': 0,
                'recommendation': 'N/A',
                'is_real_line': False
            },
            'spread_analysis': {
                'line': None,
                'predicted_spread': 0,
                'recommendation': 'N/A',
                'is_real_line': False
            },
            'moneyline': 'N/A',
            'confidence_level': 'N/A'
        }

def _get_betting_analysis(prediction, game, totals_bet, spreads_bet):
    """分析預測結果與盤口資料"""
    try:
        analysis = {
            'over_under_analysis': None,
            'spread_analysis': None,
            'recommendations': []
        }

        if not prediction.predicted_home_score or not prediction.predicted_away_score:
            return analysis

        predicted_total = prediction.predicted_home_score + prediction.predicted_away_score
        predicted_home_wins = prediction.predicted_home_score > prediction.predicted_away_score
        predicted_margin = abs(prediction.predicted_home_score - prediction.predicted_away_score)

        # 大小分分析
        if totals_bet and totals_bet.total_point:
            total_line = totals_bet.total_point
            over_under_recommendation = "大分" if predicted_total > total_line else "小分"
            confidence_level = "高" if abs(predicted_total - total_line) > 1.5 else "中" if abs(predicted_total - total_line) > 0.5 else "低"

            analysis['over_under_analysis'] = {
                'line': total_line,
                'predicted_total': predicted_total,
                'recommendation': over_under_recommendation,
                'difference': round(predicted_total - total_line, 1),
                'confidence': confidence_level
            }

            analysis['recommendations'].append(f"{over_under_recommendation} ({confidence_level}信心)")

        # 讓分分析
        if spreads_bet and spreads_bet.home_spread_point:
            home_spread = spreads_bet.home_spread_point
            # 正數表示主隊讓分，負數表示主隊受讓
            if home_spread > 0:  # 主隊讓分
                spread_recommendation = "客隊受讓" if predicted_margin < home_spread else "主隊讓分"
            else:  # 主隊受讓
                spread_recommendation = "主隊受讓" if predicted_margin < abs(home_spread) else "客隊讓分"

            spread_confidence = "高" if abs(predicted_margin - abs(home_spread)) > 1.5 else "中" if abs(predicted_margin - abs(home_spread)) > 0.5 else "低"

            analysis['spread_analysis'] = {
                'line': home_spread,
                'predicted_margin': predicted_margin,
                'predicted_winner': game.home_team if predicted_home_wins else game.away_team,
                'recommendation': spread_recommendation,
                'confidence': spread_confidence
            }

            analysis['recommendations'].append(f"{spread_recommendation} ({spread_confidence}信心)")

        return analysis

    except Exception as e:
        logger.error(f"盤口分析失敗: {e}")
        return {
            'over_under_analysis': None,
            'spread_analysis': None,
            'recommendations': []
        }

@unified_predictions_bp.route('/api/predictions/date/<date_str>')
def api_get_predictions_by_date(date_str):
    """API: 查詢特定日期的預測結果（包含盤口資料）"""
    try:
        from datetime import datetime, timedelta
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        predictor = get_unified_predictor()

        # 獲取該日期的預測，並LEFT JOIN盤口資料
        with current_app.app_context():
            from models.database import Prediction, Game, BettingOdds
            from sqlalchemy.orm import aliased

            # 創建別名用於不同市場類型的盤口
            totals_odds = aliased(BettingOdds)
            spreads_odds = aliased(BettingOdds)

            # 查詢預測結果並LEFT JOIN盤口資料 (使用bet365真實數據)
            query = db.session.query(
                Prediction, Game, totals_odds, spreads_odds
            ).join(
                Game, Game.game_id == Prediction.game_id
            ).outerjoin(
                totals_odds,
                (totals_odds.game_id == Game.game_id) &
                ((totals_odds.market_type == 'totals') | (totals_odds.market_type == 'both')) &
                (totals_odds.bookmaker == 'bet365') &
                (totals_odds.total_point.isnot(None))
            ).outerjoin(
                spreads_odds,
                (spreads_odds.game_id == Game.game_id) &
                ((spreads_odds.market_type == 'spreads') | (spreads_odds.market_type == 'both')) &
                (spreads_odds.bookmaker == 'bet365') &
                (spreads_odds.home_spread_point.isnot(None))
            ).filter(
                Game.date >= target_date,
                Game.date < target_date + timedelta(days=1)
                # 移除model_version過濾，以顯示所有預測記錄
            )

            results = query.all()

            if not results:
                return jsonify({
                    'success': True,
                    'date': date_str,
                    'message': f'{date_str} 沒有找到預測記錄',
                    'predictions': []
                })

            # 格式化預測數據
            prediction_data = []
            for pred, game, totals_bet, spreads_bet in results:

                # 檢查是否需要更新實際結果
                if game and game.home_score is not None and game.away_score is not None:
                    if pred.actual_home_score is None or pred.actual_away_score is None:
                        # 更新實際結果
                        pred.actual_home_score = game.home_score
                        pred.actual_away_score = game.away_score
                        pred.actual_total_runs = game.home_score + game.away_score

                        # 計算預測準確性
                        if pred.predicted_home_score is not None and pred.predicted_away_score is not None:
                            predicted_home_wins = pred.predicted_home_score > pred.predicted_away_score
                            actual_home_wins = game.home_score > game.away_score
                            pred.is_correct = predicted_home_wins == actual_home_wins

                            # 計算得分差異
                            home_diff = abs(pred.predicted_home_score - game.home_score)
                            away_diff = abs(pred.predicted_away_score - game.away_score)
                            pred.score_difference = (home_diff + away_diff) / 2

                            # 計算總分差異
                            predicted_total = pred.predicted_home_score + pred.predicted_away_score
                            actual_total = game.home_score + game.away_score
                            pred.total_runs_difference = abs(predicted_total - actual_total)

                            # 大小分準確性（使用盤口線或預測線）
                            over_under_line = totals_bet.total_point if totals_bet else pred.over_under_line
                            if over_under_line:
                                predicted_over = predicted_total > over_under_line
                                actual_over = actual_total > over_under_line
                                pred.over_under_correct = predicted_over == actual_over

                        pred.updated_at = datetime.now()
                        db.session.commit()

                # 準備盤口分析
                betting_analysis = _get_betting_analysis(pred, game, totals_bet, spreads_bet)

                pred_dict = {
                    'game_id': pred.game_id,
                    'home_team': game.home_team if game else None,
                    'away_team': game.away_team if game else None,
                    'predicted_home_score': pred.predicted_home_score,
                    'predicted_away_score': pred.predicted_away_score,
                    'predicted_total_runs': pred.predicted_total_runs,
                    'confidence': pred.confidence,
                    'over_under_line': totals_bet.total_point if totals_bet else pred.over_under_line,
                    'over_under_prediction': _get_over_under_recommendation(pred),
                    'over_under_analysis': _get_detailed_over_under_analysis(pred, {
                        'totals': {
                            'total_point': totals_bet.total_point if totals_bet else None,
                            'over_price': totals_bet.over_price if totals_bet else None,
                            'under_price': totals_bet.under_price if totals_bet else None,
                        } if totals_bet else None,
                        'spreads': {
                            'home_spread_point': spreads_bet.home_spread_point if spreads_bet else None,
                            'home_spread_price': spreads_bet.home_spread_price if spreads_bet else None,
                            'away_spread_point': spreads_bet.away_spread_point if spreads_bet else None,
                            'away_spread_price': spreads_bet.away_spread_price if spreads_bet else None,
                        } if spreads_bet else None
                    }),
                    'run_line': getattr(pred, 'run_line', None),
                    'run_line_prediction': _get_run_line_recommendation(pred, {
                        'totals': {
                            'total_point': totals_bet.total_point if totals_bet else None,
                            'over_price': totals_bet.over_price if totals_bet else None,
                            'under_price': totals_bet.under_price if totals_bet else None,
                        } if totals_bet else None,
                        'spreads': {
                            'home_spread_point': spreads_bet.home_spread_point if spreads_bet else None,
                            'home_spread_price': spreads_bet.home_spread_price if spreads_bet else None,
                            'away_spread_point': spreads_bet.away_spread_point if spreads_bet else None,
                            'away_spread_price': spreads_bet.away_spread_price if spreads_bet else None,
                        } if spreads_bet else None
                    }),
                    'prediction_time': pred.prediction_date if pred.prediction_date else None,
                    # 實際結果（如果有）
                    'actual_home_score': pred.actual_home_score,
                    'actual_away_score': pred.actual_away_score,
                    'actual_total_runs': pred.actual_total_runs,
                    'is_correct': pred.is_correct,
                    # 盤口資料和分析 (優先使用真實bet365數據)
                    'betting_odds': {
                        'totals': {
                            'total_point': totals_bet.total_point if totals_bet else None,
                            'over_price': totals_bet.over_price if totals_bet else None,
                            'under_price': totals_bet.under_price if totals_bet else None,
                            'bookmaker': totals_bet.bookmaker if totals_bet else None,
                            'is_real': totals_bet is not None and totals_bet.bookmaker == 'bet365'
                        } if totals_bet else None,
                        'spreads': {
                            'home_spread_point': spreads_bet.home_spread_point if spreads_bet else None,
                            'home_spread_price': spreads_bet.home_spread_price if spreads_bet else None,
                            'away_spread_point': spreads_bet.away_spread_point if spreads_bet else None,
                            'away_spread_price': spreads_bet.away_spread_price if spreads_bet else None,
                            'bookmaker': spreads_bet.bookmaker if spreads_bet else None,
                            'is_real': spreads_bet is not None and spreads_bet.bookmaker == 'bet365'
                        } if spreads_bet else None
                    },
                    'betting_analysis': betting_analysis,
                    'over_under_correct': pred.over_under_correct,
                    'score_difference': pred.score_difference,
                    'total_runs_difference': pred.total_runs_difference,
                    # 比賽狀態信息
                    'game_status': game.game_status if game else None,
                    'game_completed': game.home_score is not None and game.away_score is not None if game else False
                }
                prediction_data.append(pred_dict)

            # 計算統計
            total_predictions = len(results)
            correct_predictions = sum(1 for pred, _, _, _ in results if pred.is_correct is True)
            accuracy_rate = (correct_predictions / total_predictions * 100) if total_predictions > 0 else 0

            return jsonify({
                'success': True,
                'date': date_str,
                'total_predictions': total_predictions,
                'correct_predictions': correct_predictions,
                'accuracy_rate': round(accuracy_rate, 2),
                'predictions': prediction_data
            })

    except ValueError:
        return jsonify({
            'success': False,
            'error': '日期格式錯誤，請使用 YYYY-MM-DD 格式'
        }), 400

    except Exception as e:
        logger.error(f"API查詢預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
