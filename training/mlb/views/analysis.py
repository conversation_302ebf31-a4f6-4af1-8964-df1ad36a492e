"""
分析頁面視圖
"""

from flask import Blueprint, render_template, request, jsonify
from models.database import db, Game, Team, Player, PlayerStats, BoxScore, Prediction
from models.prediction_service import PredictionService
from datetime import date, timedelta
import logging

logger = logging.getLogger(__name__)

analysis_bp = Blueprint('analysis', __name__)

@analysis_bp.route('/')
def analysis_dashboard():
    """分析儀表板"""
    try:
        # 獲取基本統計
        total_games = Game.query.count()
        total_teams = Team.query.count()
        total_players = Player.query.count()
        total_boxscores = BoxScore.query.count()
        total_predictions = Prediction.query.count()
        
        # 獲取最近的比賽（昨天的比賽）
        yesterday = date.today() - timedelta(days=1)
        recent_games = Game.query.filter(
            Game.date == yesterday
        ).order_by(Game.date.desc()).limit(10).all()

        # 如果昨天沒有比賽，則顯示最近7天的比賽
        if not recent_games:
            recent_games = Game.query.filter(
                Game.date >= date.today() - timedelta(days=7)
            ).order_by(Game.date.desc()).limit(10).all()
        
        # 獲取最近的預測
        recent_predictions = Prediction.query.order_by(
            Prediction.prediction_date.desc()
        ).limit(10).all()
        
        # 計算預測準確率
        prediction_service = PredictionService()
        accuracy_stats = prediction_service.get_prediction_accuracy_stats(days=30)
        
        return render_template('analysis/dashboard.html',
                             total_games=total_games,
                             total_teams=total_teams,
                             total_players=total_players,
                             total_boxscores=total_boxscores,
                             total_predictions=total_predictions,
                             recent_games=recent_games,
                             recent_predictions=recent_predictions,
                             accuracy_stats=accuracy_stats)
                             
    except Exception as e:
        logger.error(f"分析儀表板錯誤: {e}")
        return render_template('analysis/dashboard.html',
                             total_games=0,
                             total_teams=0,
                             total_players=0,
                             total_boxscores=0,
                             total_predictions=0,
                             recent_games=[],
                             recent_predictions=[],
                             accuracy_stats={})

@analysis_bp.route('/team-performance')
def team_performance():
    """球隊表現分析"""
    try:
        # 獲取所有球隊
        teams = Team.query.all()
        
        # 計算每個球隊的統計
        team_stats = []
        for team in teams:
            # 主場比賽
            home_games = Game.query.filter(
                Game.home_team == team.team_code,
                Game.game_status == 'completed'
            ).all()
            
            # 客場比賽
            away_games = Game.query.filter(
                Game.away_team == team.team_code,
                Game.game_status == 'completed'
            ).all()
            
            # 計算勝率
            home_wins = sum(1 for g in home_games if g.home_score is not None and g.away_score is not None and g.home_score > g.away_score)
            away_wins = sum(1 for g in away_games if g.away_score is not None and g.home_score is not None and g.away_score > g.home_score)
            total_games = len(home_games) + len(away_games)
            total_wins = home_wins + away_wins

            # 計算平均得失分
            home_runs_scored = sum(g.home_score for g in home_games if g.home_score is not None)
            home_runs_allowed = sum(g.away_score for g in home_games if g.away_score is not None)
            away_runs_scored = sum(g.away_score for g in away_games if g.away_score is not None)
            away_runs_allowed = sum(g.home_score for g in away_games if g.home_score is not None)

            total_runs_scored = home_runs_scored + away_runs_scored
            total_runs_allowed = home_runs_allowed + away_runs_allowed

            avg_runs_scored = total_runs_scored / total_games if total_games > 0 else 0
            avg_runs_allowed = total_runs_allowed / total_games if total_games > 0 else 0
            run_differential = avg_runs_scored - avg_runs_allowed

            # 創建統計對象
            class TeamStat:
                def __init__(self, team, total_games, home_wins, away_wins, home_games, away_games,
                           avg_runs_scored, avg_runs_allowed, run_differential):
                    self.team_code = team.team_code
                    self.team_name = team.team_name
                    self.league = team.league
                    self.total_games = total_games
                    self.home_wins = home_wins
                    self.home_losses = home_games - home_wins
                    self.away_wins = away_wins
                    self.away_losses = away_games - away_wins
                    self.overall_win_rate = (home_wins + away_wins) / total_games if total_games > 0 else 0
                    self.home_win_rate = home_wins / home_games if home_games > 0 else 0
                    self.away_win_rate = away_wins / away_games if away_games > 0 else 0
                    self.avg_runs_scored = avg_runs_scored
                    self.avg_runs_allowed = avg_runs_allowed
                    self.run_differential = run_differential

            team_stats.append(TeamStat(
                team, total_games, home_wins, away_wins, len(home_games), len(away_games),
                avg_runs_scored, avg_runs_allowed, run_differential
            ))
        
        # 按勝率排序
        team_stats.sort(key=lambda x: x.overall_win_rate, reverse=True)
        
        return render_template('analysis/team_performance.html', team_stats=team_stats)
        
    except Exception as e:
        logger.error(f"球隊表現分析錯誤: {e}")
        return render_template('analysis/team_performance.html', team_stats=[])

@analysis_bp.route('/prediction-trends')
def prediction_trends():
    """預測趨勢分析"""
    try:
        # 獲取最近30天的預測
        start_date = date.today() - timedelta(days=30)
        predictions = Prediction.query.filter(
            Prediction.prediction_date >= start_date
        ).order_by(Prediction.prediction_date.desc()).all()
        
        # 按日期分組統計
        daily_stats = {}
        for prediction in predictions:
            pred_date = prediction.prediction_date
            if pred_date not in daily_stats:
                daily_stats[pred_date] = {
                    'total': 0,
                    'correct': 0,
                    'avg_confidence': 0,
                    'confidences': []
                }
            
            daily_stats[pred_date]['total'] += 1
            daily_stats[pred_date]['confidences'].append(prediction.confidence)
            
            # 檢查預測是否正確（如果比賽已完成）
            if prediction.game.game_status == 'completed':
                predicted_home_win = prediction.home_win_probability > 0.5
                actual_home_win = prediction.game.home_score > prediction.game.away_score
                if predicted_home_win == actual_home_win:
                    daily_stats[pred_date]['correct'] += 1
        
        # 計算平均信心度
        for date_key in daily_stats:
            confidences = daily_stats[date_key]['confidences']
            daily_stats[date_key]['avg_confidence'] = sum(confidences) / len(confidences) if confidences else 0
            daily_stats[date_key]['accuracy'] = daily_stats[date_key]['correct'] / daily_stats[date_key]['total'] if daily_stats[date_key]['total'] > 0 else 0
        
        return render_template('analysis/prediction_trends.html', 
                             daily_stats=daily_stats,
                             predictions=predictions[:20])  # 顯示最近20個預測
        
    except Exception as e:
        logger.error(f"預測趨勢分析錯誤: {e}")
        return render_template('analysis/prediction_trends.html', 
                             daily_stats={},
                             predictions=[])

@analysis_bp.route('/data-quality')
def data_quality():
    """數據質量分析"""
    try:
        # 檢查數據完整性
        total_games = Game.query.count()
        completed_games = Game.query.filter(Game.game_status == 'completed').count()
        games_with_boxscore = db.session.query(Game).filter(
            Game.game_id.in_(db.session.query(BoxScore.game_id).distinct())
        ).count()
        
        # 按年份統計
        years_stats = []
        for year in [2023, 2024, 2025]:
            year_games = Game.query.filter(
                db.func.strftime('%Y', Game.date) == str(year)
            ).count()
            
            year_completed = Game.query.filter(
                db.func.strftime('%Y', Game.date) == str(year),
                Game.game_status == 'completed'
            ).count()
            
            year_boxscores = db.session.query(Game).filter(
                db.func.strftime('%Y', Game.date) == str(year),
                Game.game_id.in_(db.session.query(BoxScore.game_id).distinct())
            ).count()
            
            years_stats.append({
                'year': year,
                'total_games': year_games,
                'completed_games': year_completed,
                'games_with_boxscore': year_boxscores,
                'boxscore_coverage': year_boxscores / year_completed if year_completed > 0 else 0
            })
        
        # 檢查數據質量問題
        quality_issues = []
        
        # 檢查缺少分數的已完成比賽
        games_missing_scores = Game.query.filter(
            Game.game_status == 'completed',
            db.or_(Game.home_score.is_(None), Game.away_score.is_(None))
        ).count()
        
        if games_missing_scores > 0:
            quality_issues.append(f"有 {games_missing_scores} 場已完成比賽缺少分數")
        
        # 檢查重複的比賽記錄
        duplicate_games = db.session.query(
            Game.game_id,
            db.func.count(Game.id).label('count')
        ).group_by(Game.game_id).having(db.func.count(Game.id) > 1).count()
        
        if duplicate_games > 0:
            quality_issues.append(f"發現 {duplicate_games} 個重複的比賽記錄")
        
        # 創建數據質量對象
        class DataQuality:
            def __init__(self, total_games, completed_games, total_predictions, total_boxscores):
                self.total_games = total_games
                self.completed_games = completed_games
                self.total_predictions = total_predictions
                self.total_boxscores = total_boxscores
                self.boxscore_coverage = (total_boxscores / completed_games * 100) if completed_games > 0 else 0
                self.prediction_coverage = (total_predictions / total_games * 100) if total_games > 0 else 0
                self.player_stats_coverage = 85.0  # 假設值，可以根據實際情況計算
                self.issues = []
                self.recent_updates = []

        total_predictions = Prediction.query.count()
        total_boxscores = BoxScore.query.count()

        data_quality = DataQuality(total_games, completed_games, total_predictions, total_boxscores)

        return render_template('analysis/data_quality.html',
                             data_quality=data_quality,
                             years_stats=years_stats,
                             quality_issues=quality_issues)
        
    except Exception as e:
        logger.error(f"數據質量分析錯誤: {e}")
        # 創建空的數據質量對象
        class DataQuality:
            def __init__(self):
                self.total_games = 0
                self.completed_games = 0
                self.total_predictions = 0
                self.total_boxscores = 0
                self.boxscore_coverage = 0
                self.prediction_coverage = 0
                self.player_stats_coverage = 0
                self.issues = []
                self.recent_updates = []

        return render_template('analysis/data_quality.html',
                             data_quality=DataQuality(),
                             years_stats=[],
                             quality_issues=[])

@analysis_bp.route('/api/team-stats/<team_code>')
def api_team_stats(team_code):
    """API: 獲取球隊統計數據"""
    try:
        team = Team.query.filter_by(team_code=team_code).first()
        if not team:
            return jsonify({'error': '球隊不存在'}), 404
        
        # 獲取球隊最近的比賽
        recent_games = Game.query.filter(
            db.or_(Game.home_team == team_code, Game.away_team == team_code),
            Game.game_status == 'completed'
        ).order_by(Game.date.desc()).limit(10).all()
        
        games_data = []
        for game in recent_games:
            is_home = game.home_team == team_code
            opponent = game.away_team if is_home else game.home_team
            team_score = game.home_score if is_home else game.away_score
            opp_score = game.away_score if is_home else game.home_score
            won = team_score > opp_score
            
            games_data.append({
                'date': game.date.isoformat(),
                'opponent': opponent,
                'is_home': is_home,
                'team_score': team_score,
                'opponent_score': opp_score,
                'won': won
            })
        
        return jsonify({
            'team': {
                'code': team.team_code,
                'name': team.team_name,
                'city': team.city
            },
            'recent_games': games_data
        })
        
    except Exception as e:
        logger.error(f"API球隊統計錯誤: {e}")
        return jsonify({'error': '獲取數據失敗'}), 500
