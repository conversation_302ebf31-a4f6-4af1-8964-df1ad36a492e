#!/usr/bin/env python3
"""
智能預測系統Web界面
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from datetime import datetime, date, timedelta
import logging

logger = logging.getLogger(__name__)

intelligent_bp = Blueprint('intelligent', __name__)

@intelligent_bp.route('/')
def intelligent_dashboard():
    """智能預測系統儀表板"""
    try:
        from models.database import db, Game
        from models.simple_intelligent_predictor import SimpleIntelligentPredictor
        
        # 獲取今天和明天的比賽
        today = date.today()
        tomorrow = today + timedelta(days=1)
        
        today_games = Game.query.filter_by(date=today).all()
        tomorrow_games = Game.query.filter_by(date=tomorrow).all()
        
        return render_template('intelligent/dashboard.html',
                             today=today,
                             tomorrow=tomorrow,
                             today_games=today_games,
                             tomorrow_games=tomorrow_games)
        
    except Exception as e:
        logger.error(f"智能預測儀表板失敗: {e}")
        flash(f'載入智能預測儀表板失敗: {e}', 'error')
        return redirect(url_for('main.index'))

@intelligent_bp.route('/predict', methods=['POST'])
def predict_game():
    """執行智能預測"""
    try:
        from models.simple_intelligent_predictor import SimpleIntelligentPredictor
        
        # 獲取參數
        home_team = request.form.get('home_team')
        away_team = request.form.get('away_team')
        game_date_str = request.form.get('game_date')
        game_id = request.form.get('game_id')
        
        if not all([home_team, away_team, game_date_str]):
            return jsonify({
                'success': False,
                'error': '缺少必要參數'
            })
        
        game_date = datetime.strptime(game_date_str, '%Y-%m-%d').date()
        
        # 執行智能預測
        predictor = SimpleIntelligentPredictor()
        result = predictor.predict_game_intelligent(
            home_team=home_team,
            away_team=away_team,
            game_date=game_date,
            game_id=game_id
        )
        
        return jsonify({
            'success': True,
            'prediction': result
        })
        
    except Exception as e:
        logger.error(f"智能預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@intelligent_bp.route('/api/predict/<game_id>')
def api_predict_by_game_id(game_id):
    """API端點：根據game_id預測"""
    try:
        from models.database import Game
        from models.simple_intelligent_predictor import SimpleIntelligentPredictor
        
        # 查找比賽
        game = Game.query.filter_by(game_id=game_id).first()
        if not game:
            return jsonify({
                'success': False,
                'error': f'找不到比賽 ID: {game_id}'
            })
        
        # 執行智能預測
        predictor = SimpleIntelligentPredictor()
        result = predictor.predict_game_intelligent(
            home_team=game.home_team,
            away_team=game.away_team,
            game_date=game.date,
            game_id=game_id
        )
        
        # 添加比賽信息
        result['game_info'] = {
            'game_id': game.game_id,
            'home_team': game.home_team,
            'away_team': game.away_team,
            'date': game.date.isoformat(),
            'status': game.game_status,
            'actual_home_score': game.home_score,
            'actual_away_score': game.away_score
        }
        
        return jsonify({
            'success': True,
            'prediction': result
        })
        
    except Exception as e:
        logger.error(f"API智能預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@intelligent_bp.route('/batch_predict', methods=['POST'])
def batch_predict():
    """批量預測"""
    try:
        from models.database import Game
        from models.simple_intelligent_predictor import SimpleIntelligentPredictor
        
        # 獲取日期
        target_date_str = request.form.get('date')
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()
        
        # 獲取該日期的所有比賽
        games = Game.query.filter_by(date=target_date).all()
        
        if not games:
            return jsonify({
                'success': False,
                'error': f'{target_date} 沒有比賽記錄'
            })
        
        # 批量預測
        predictor = SimpleIntelligentPredictor()
        predictions = []
        
        for game in games:
            try:
                result = predictor.predict_game_intelligent(
                    home_team=game.home_team,
                    away_team=game.away_team,
                    game_date=game.date,
                    game_id=game.game_id
                )
                
                # 添加比賽信息
                result['game_info'] = {
                    'game_id': game.game_id,
                    'home_team': game.home_team,
                    'away_team': game.away_team,
                    'status': game.game_status,
                    'actual_home_score': game.home_score,
                    'actual_away_score': game.away_score
                }
                
                predictions.append(result)
                
            except Exception as e:
                logger.error(f"預測比賽 {game.game_id} 失敗: {e}")
                predictions.append({
                    'game_info': {
                        'game_id': game.game_id,
                        'home_team': game.home_team,
                        'away_team': game.away_team,
                        'error': str(e)
                    }
                })
        
        return jsonify({
            'success': True,
            'date': target_date.isoformat(),
            'total_games': len(games),
            'successful_predictions': len([p for p in predictions if 'error' not in p.get('game_info', {})]),
            'predictions': predictions
        })
        
    except Exception as e:
        logger.error(f"批量預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@intelligent_bp.route('/demo')
def demo_page():
    """智能預測演示頁面"""
    try:
        return render_template('intelligent/demo.html')
    except Exception as e:
        logger.error(f"載入演示頁面失敗: {e}")
        flash(f'載入演示頁面失敗: {e}', 'error')
        return redirect(url_for('intelligent.intelligent_dashboard'))

@intelligent_bp.route('/api/demo_scenarios')
def api_demo_scenarios():
    """API端點：獲取演示場景"""
    try:
        from models.simple_intelligent_predictor import SimpleIntelligentPredictor
        
        predictor = SimpleIntelligentPredictor()
        
        # 演示場景
        scenarios = [
            {
                'title': '🔥 王牌對決場景',
                'description': '兩位王牌投手對決 → 應該預測低分比賽',
                'pitcher_analysis': {
                    'home_pitcher': {'name': 'Gerrit Cole', 'era': 2.20, 'quality': 85, 'strength': '王牌'},
                    'away_pitcher': {'name': 'Shane Bieber', 'era': 2.50, 'quality': 82, 'strength': '王牌'},
                    'matchup_type': '王牌對決',
                    'average_era': 2.35,
                    'average_quality': 83.5
                }
            },
            {
                'title': '💥 打擊戰場景',
                'description': '兩位弱勢投手 → 應該預測高分比賽',
                'pitcher_analysis': {
                    'home_pitcher': {'name': 'Weak Pitcher A', 'era': 5.50, 'quality': 30, 'strength': '弱勢'},
                    'away_pitcher': {'name': 'Weak Pitcher B', 'era': 6.00, 'quality': 25, 'strength': '弱勢'},
                    'matchup_type': '打擊戰',
                    'average_era': 5.75,
                    'average_quality': 27.5
                }
            },
            {
                'title': '⚖️ 強弱對戰場景',
                'description': '一強一弱投手 → 應該預測不平衡比分',
                'pitcher_analysis': {
                    'home_pitcher': {'name': 'Good Pitcher', 'era': 2.80, 'quality': 78, 'strength': '優秀'},
                    'away_pitcher': {'name': 'Poor Pitcher', 'era': 5.20, 'quality': 35, 'strength': '弱勢'},
                    'matchup_type': '強弱對戰',
                    'average_era': 4.00,
                    'average_quality': 56.5
                }
            }
        ]
        
        # 為每個場景生成策略和預測
        for scenario in scenarios:
            strategy = predictor._determine_prediction_strategy(scenario['pitcher_analysis'])
            
            # 模擬基礎預測
            base_prediction = {
                'predicted_home_score': 5.0,
                'predicted_away_score': 4.5,
                'total_runs': 9.5,
                'confidence': 0.6,
                'home_win_probability': 0.52
            }
            
            # 應用策略調整
            adjusted_prediction = predictor._adjust_prediction_by_strategy(base_prediction, strategy)
            
            scenario['strategy'] = strategy
            scenario['base_prediction'] = base_prediction
            scenario['adjusted_prediction'] = adjusted_prediction
        
        return jsonify({
            'success': True,
            'scenarios': scenarios
        })
        
    except Exception as e:
        logger.error(f"獲取演示場景失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@intelligent_bp.route('/analysis')
def analysis_page():
    """智能預測分析頁面"""
    try:
        from models.database import db, Game
        
        # 獲取最近的預測統計
        recent_games = Game.query.filter(
            Game.date >= date.today() - timedelta(days=7),
            Game.game_status == 'completed'
        ).all()
        
        return render_template('intelligent/analysis.html',
                             recent_games=recent_games)
        
    except Exception as e:
        logger.error(f"載入分析頁面失敗: {e}")
        flash(f'載入分析頁面失敗: {e}', 'error')
        return redirect(url_for('intelligent.intelligent_dashboard'))

@intelligent_bp.route('/api/accuracy_stats')
def api_accuracy_stats():
    """API端點：獲取預測準確率統計"""
    try:
        from models.database import db, Game
        from models.simple_intelligent_predictor import SimpleIntelligentPredictor
        
        # 獲取最近完成的比賽
        recent_games = Game.query.filter(
            Game.date >= date.today() - timedelta(days=30),
            Game.game_status == 'completed',
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).limit(50).all()
        
        if not recent_games:
            return jsonify({
                'success': False,
                'error': '沒有足夠的已完成比賽數據'
            })
        
        predictor = SimpleIntelligentPredictor()
        stats = {
            'total_games': 0,
            'accurate_predictions': 0,  # 差異 ≤ 2分
            'acceptable_predictions': 0,  # 差異 ≤ 4分
            'strategy_effectiveness': {
                '王牌對決': {'count': 0, 'accurate': 0},
                '打擊戰': {'count': 0, 'accurate': 0},
                '強弱對戰': {'count': 0, 'accurate': 0},
                '普通對戰': {'count': 0, 'accurate': 0}
            }
        }
        
        for game in recent_games:
            try:
                # 執行預測
                result = predictor.predict_game_intelligent(
                    home_team=game.home_team,
                    away_team=game.away_team,
                    game_date=game.date,
                    game_id=game.game_id
                )
                
                actual_total = game.home_score + game.away_score
                predicted_total = result['total_runs']
                diff = abs(predicted_total - actual_total)
                
                stats['total_games'] += 1
                
                if diff <= 2.0:
                    stats['accurate_predictions'] += 1
                if diff <= 4.0:
                    stats['acceptable_predictions'] += 1
                
                # 策略統計
                strategy_type = result['pitcher_analysis']['matchup_type']
                if strategy_type in stats['strategy_effectiveness']:
                    stats['strategy_effectiveness'][strategy_type]['count'] += 1
                    if diff <= 2.0:
                        stats['strategy_effectiveness'][strategy_type]['accurate'] += 1
                
            except Exception as e:
                logger.warning(f"統計比賽 {game.game_id} 失敗: {e}")
        
        # 計算準確率
        if stats['total_games'] > 0:
            stats['accuracy_rate'] = stats['accurate_predictions'] / stats['total_games']
            stats['acceptable_rate'] = stats['acceptable_predictions'] / stats['total_games']
        else:
            stats['accuracy_rate'] = 0
            stats['acceptable_rate'] = 0
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"獲取準確率統計失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })
