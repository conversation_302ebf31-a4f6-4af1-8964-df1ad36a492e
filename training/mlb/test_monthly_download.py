#!/usr/bin/env python3
"""
測試月度數據下載功能
"""

import sys
sys.path.append('.')

import threading
import time
from flask import Flask
from datetime import datetime

def test_app_context_in_thread():
    """測試線程中的應用上下文"""
    
    # 創建一個簡單的Flask應用
    app = Flask(__name__)
    app.config['TESTING'] = True
    
    def thread_function():
        """在線程中測試應用上下文"""
        # 模擬我們修復後的代碼
        with app.app_context():
            print("✅ 線程中的應用上下文工作正常")
            return True
    
    with app.app_context():
        print("🔧 創建測試線程...")
        
        # 獲取應用實例 (模擬修復後的邏輯)
        # 在Flask應用上下文中，app就是實際應用
        current_app_obj = app
        
        def safe_thread_function():
            with current_app_obj.app_context():
                print("✅ 使用 _get_current_object() 的線程上下文工作正常")
                time.sleep(1)
                print("✅ 線程執行完成")
        
        # 啟動線程
        thread = threading.Thread(target=safe_thread_function)
        thread.start()
        thread.join()
    
    print("🎉 應用上下文測試通過！")

def test_progress_monitor_integration():
    """測試進度監控器集成"""
    try:
        from progress_monitor import progress_monitor
        from datetime import datetime
        
        # 測試創建任務
        task_id = f"test_monthly_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print("🚀 測試進度監控器集成...")
        
        # 啟動任務
        progress_monitor.start_task(
            task_id=task_id,
            task_name="測試：月度下載功能",
            total_steps=10
        )
        
        # 模擬進度更新
        for step in range(1, 11):
            progress_monitor.update_progress(task_id, step, f"步驟 {step}/10 完成")
            time.sleep(0.2)
        
        # 完成任務
        progress_monitor.complete_task(task_id, success=True, final_message="測試完成")
        
        print("✅ 進度監控器集成測試通過！")
        
        # 顯示任務狀態
        task_status = progress_monitor.get_task_status(task_id)
        if task_status:
            print(f"📊 任務狀態: {task_status['status']}")
            print(f"📊 最終進度: {task_status['progress']:.1f}%")
        
    except Exception as e:
        print(f"❌ 進度監控器測試失敗: {e}")
        return False
    
    return True

def main():
    """主測試函數"""
    print("🧪 月度數據下載功能測試")
    print("=" * 50)
    
    try:
        # 測試應用上下文
        test_app_context_in_thread()
        
        print()
        
        # 測試進度監控器
        test_progress_monitor_integration()
        
        print()
        print("🎉 所有測試通過！月度數據下載功能應該可以正常工作了。")
        print()
        print("💡 建議測試步驟:")
        print("1. 啟動系統: ./start.sh")
        print("2. 打開瀏覽器: http://localhost:5500/admin/data-management")
        print("3. 點擊'下載整月數據'按鈕")
        print("4. 打開進度監控: http://localhost:5500/admin/progress-monitor")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)