#!/usr/bin/env python3
"""
測試預測修復效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game, Prediction
from models.enhanced_feature_engineer import EnhancedFeatureEngineer

def test_pitcher_stats_fix():
    """測試投手統計數據修復"""
    app = create_app()
    
    with app.app_context():
        print("🔧 測試投手統計數據修復")
        print("=" * 60)
        
        feature_engineer = EnhancedFeatureEngineer()
        
        # 測試已知投手
        test_pitchers = [
            "<PERSON>",
            "<PERSON>", 
            "<PERSON><PERSON>",
            "<PERSON>"
        ]
        
        for pitcher in test_pitchers:
            print(f"\n🎯 測試投手: {pitcher}")
            stats = feature_engineer._get_pitcher_stats_by_name(pitcher)
            
            if stats:
                print(f"  ✅ 找到統計數據:")
                print(f"    ERA: {stats['era']}")
                print(f"    WHIP: {stats['whip']}")
                print(f"    勝場: {stats['wins']}")
                print(f"    敗場: {stats['losses']}")
            else:
                print(f"  ❌ 未找到統計數據")

def test_feature_differences():
    """測試特徵差異"""
    app = create_app()
    
    with app.app_context():
        print("\n🔍 測試特徵差異")
        print("=" * 60)
        
        feature_engineer = EnhancedFeatureEngineer()
        
        # 測試NYM @ BAL兩場比賽的特徵
        game_date = date(2025, 7, 10)
        
        print(f"\n🎲 比賽1: NYM @ BAL (Game ID: 777165)")
        features1 = feature_engineer.extract_comprehensive_features(
            home_team='BAL',
            away_team='NYM', 
            game_date=game_date,
            game_id='777165'
        )
        
        print(f"\n🎲 比賽2: NYM @ BAL (Game ID: 777181)")
        features2 = feature_engineer.extract_comprehensive_features(
            home_team='BAL',
            away_team='NYM',
            game_date=game_date, 
            game_id='777181'
        )
        
        # 比較關鍵特徵
        key_features = [
            'home_probable_starter',
            'away_probable_starter', 
            'home_pitcher_quality',
            'away_pitcher_quality',
            'pitcher_matchup_advantage'
        ]
        
        print(f"\n📊 特徵比較:")
        for feature in key_features:
            val1 = features1.get(feature, 'N/A')
            val2 = features2.get(feature, 'N/A')
            
            if val1 == val2:
                status = "❌ 相同"
            else:
                status = "✅ 不同"
                
            print(f"  {feature}:")
            print(f"    比賽1: {val1}")
            print(f"    比賽2: {val2}")
            print(f"    狀態: {status}")

def test_extreme_value_detection():
    """測試極端值檢測"""
    app = create_app()
    
    with app.app_context():
        print("\n⚠️  測試極端值檢測")
        print("=" * 60)
        
        feature_engineer = EnhancedFeatureEngineer()
        
        # 測試TB @ BOS比賽
        print(f"\n🎯 測試 TB @ BOS (Game ID: 777170)")
        features = feature_engineer.extract_comprehensive_features(
            home_team='BOS',
            away_team='TB',
            game_date=date(2025, 7, 10),
            game_id='777170'
        )
        
        # 檢查極端值
        extreme_values = {}
        for key, value in features.items():
            if isinstance(value, (int, float)):
                if abs(value) > 100:  # 檢查絕對值大於100的特徵
                    extreme_values[key] = value
        
        if extreme_values:
            print(f"  ⚠️  發現 {len(extreme_values)} 個極端值:")
            for key, value in extreme_values.items():
                print(f"    {key}: {value}")
        else:
            print(f"  ✅ 沒有發現極端值")

def test_prediction_validation():
    """測試預測驗證功能"""
    print("\n🛡️  測試預測驗證功能")
    print("=" * 60)
    
    # 模擬極端預測
    test_cases = [
        (4.0, 12.0, "極端高分"),
        (0.5, 1.0, "極端低分"), 
        (18.0, 5.0, "單隊極端高分"),
        (5.5, 4.5, "正常預測")
    ]
    
    # 這裡我們需要導入預測器，但由於模型可能未訓練，我們先模擬驗證邏輯
    for away_score, home_score, description in test_cases:
        total_score = away_score + home_score
        
        print(f"\n🎯 測試案例: {description}")
        print(f"  原始預測: {away_score:.1f} - {home_score:.1f} (總分: {total_score:.1f})")
        
        # 模擬驗證邏輯
        if total_score > 20:
            scale_factor = 10.0 / total_score
            corrected_away = round(away_score * scale_factor, 1)
            corrected_home = round(home_score * scale_factor, 1)
            print(f"  修正預測: {corrected_away:.1f} - {corrected_home:.1f} (總分: {corrected_away + corrected_home:.1f})")
            print(f"  狀態: ✅ 已修正極端高分")
        elif total_score < 3:
            corrected_away = max(away_score, 1.5)
            corrected_home = max(home_score, 1.5)
            print(f"  修正預測: {corrected_away:.1f} - {corrected_home:.1f} (總分: {corrected_away + corrected_home:.1f})")
            print(f"  狀態: ✅ 已修正極端低分")
        else:
            print(f"  狀態: ✅ 預測合理，無需修正")

if __name__ == "__main__":
    test_pitcher_stats_fix()
    test_feature_differences()
    test_extreme_value_detection()
    test_prediction_validation()
