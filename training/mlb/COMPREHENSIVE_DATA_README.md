# MLB 全面數據系統

## 概述

這個全面數據系統擴展了原有的MLB預測系統，增加了所有可能影響賽果的球隊和球員資料收集功能。系統設計用於提供更準確的比賽預測，通過分析更多影響因素來提高預測準確性。

## 新增功能

### 1. 增強數據模型

#### TeamAdvancedStats (球隊進階統計)
- **打線深度和品質**: 核心打線表現、板凳深度、關鍵時刻表現
- **投手輪值和牛棚**: 先發投手統計、牛棚強度、終結者表現
- **守備能力**: 守備率、守備效率、失誤統計
- **特殊情況表現**: 主客場差異、對戰記錄、近期狀態
- **傷兵情況**: 傷兵人數、關鍵球員受傷狀況
- **其他影響因素**: 團隊化學反應、總教練經驗、薪資排名

#### PlayerInjuryReport (球員傷病報告)
- 傷病類型和描述
- 受傷部位和嚴重程度
- 預計復出時間
- 對球隊影響評估

#### PlayerPerformanceTrends (球員表現趨勢)
- **打者趨勢**: 最近表現、熱手/冷手狀態、特殊情況表現
- **投手趨勢**: 最近表現、疲勞指標、對戰優勢
- **一致性評分**: 穩定性指標

#### WeatherConditions (天氣條件)
- 溫度、濕度、風速、風向
- 天氣狀況和降雨量
- 對打擊/投球的影響評估
- 巨蛋球場標記

#### TeamChemistry (球隊化學反應)
- 休息室和諧度
- 領導力品質
- 老將影響力
- 壓力下表現

### 2. 全面數據抓取器 (ComprehensiveDataFetcher)

#### 主要功能
- `fetch_all_team_data()`: 獲取單個球隊的所有相關數據
- `fetch_all_teams_comprehensive_data()`: 獲取所有球隊的全面數據
- `fetch_team_advanced_stats()`: 獲取球隊進階統計
- `fetch_team_injury_report()`: 獲取傷兵報告
- `fetch_team_player_trends()`: 獲取球員表現趨勢
- `fetch_team_chemistry()`: 獲取球隊化學反應數據
- `fetch_game_weather_conditions()`: 獲取比賽天氣條件

#### 數據來源
- MLB Stats API (主要數據源)
- 天氣API (需要配置API密鑰)
- 傷兵報告 (需要外部數據源)

### 3. 數據管理器 (ComprehensiveDataManager)

#### 命令行工具
```bash
# 初始化數據庫
python comprehensive_data_manager.py init

# 獲取所有球隊數據
python comprehensive_data_manager.py fetch-all --season 2024

# 獲取單個球隊數據
python comprehensive_data_manager.py fetch-team --team LAA --season 2024

# 更新球員趨勢
python comprehensive_data_manager.py update-trends --season 2024

# 檢查數據狀態
python comprehensive_data_manager.py status --season 2024

# 清理舊數據
python comprehensive_data_manager.py cleanup --days 30
```

### 4. Web管理界面

#### 新增管理頁面
- **全面數據管理**: `/admin/comprehensive-data`
- **數據狀態概覽**: 顯示各類數據的數量和完整度
- **批量數據抓取**: 一鍵獲取所有球隊數據
- **單個球隊管理**: 針對特定球隊的數據更新
- **球員趨勢更新**: 批量更新球員表現趨勢
- **數據維護**: 清理舊數據、刷新狀態

## 安裝和設置

### 1. 數據庫初始化
```bash
# 運行測試腳本
python run_comprehensive_data_test.py

# 或手動初始化
python comprehensive_data_manager.py init
```

### 2. 配置外部API (可選)
```python
# 在 comprehensive_data_fetcher.py 中設置
self.weather_api_key = "your_weather_api_key"
```

### 3. 啟動Web應用
```bash
python app.py
```

### 4. 訪問管理界面
- 主管理面板: `http://localhost:5500/admin`
- 全面數據管理: `http://localhost:5500/admin/comprehensive-data`

## 使用流程

### 1. 初始設置
1. 運行 `python run_comprehensive_data_test.py` 測試系統
2. 訪問Web管理界面
3. 點擊 "全面數據管理"

### 2. 數據收集
1. **首次使用**: 點擊 "獲取所有球隊數據" 進行完整數據收集
2. **日常更新**: 選擇特定球隊進行增量更新
3. **球員趨勢**: 定期更新球員表現趨勢

### 3. 數據維護
1. **狀態監控**: 定期檢查數據完整度
2. **清理舊數據**: 清理過期的趨勢和傷兵數據
3. **錯誤處理**: 查看操作日誌處理失敗的數據抓取

## 數據影響因素

### 球隊層面
1. **打線深度**: 核心打者vs替補打者表現差異
2. **牛棚強度**: 救援投手的可靠性和深度
3. **傷兵情況**: 關鍵球員的缺席對整體實力的影響
4. **主場優勢**: 不同球場的特殊條件
5. **近期狀態**: 最近10場比賽的表現趨勢
6. **團隊化學**: 球員間的配合和士氣

### 球員層面
1. **表現趨勢**: 最近7/15/30天的表現變化
2. **對戰優勢**: 對特定投手/打者的歷史表現
3. **疲勞狀況**: 連續出賽和工作量
4. **傷病狀況**: 當前的健康狀態
5. **情境表現**: 主客場、日夜場差異

### 環境因素
1. **天氣條件**: 溫度、風速對比賽的影響
2. **球場特性**: 巨蛋vs露天球場
3. **比賽時間**: 日場vs夜場的表現差異

## 技術架構

### 數據流程
```
MLB Stats API → ComprehensiveDataFetcher → Enhanced Data Models → Database
                                                ↓
Web Interface ← ComprehensiveDataManager ← Feature Engineering ← ML Predictor
```

### 數據庫結構
- **原有表**: games, teams, players, box_scores, predictions
- **新增表**: team_advanced_stats, player_injury_reports, player_performance_trends, weather_conditions, team_chemistry

### API集成
- **MLB Stats API**: 主要統計數據
- **天氣API**: 比賽天氣條件 (需要配置)
- **傷兵報告**: 外部數據源 (需要實現)

## 故障排除

### 常見問題
1. **導入錯誤**: 確保所有新模組都已正確創建
2. **數據庫錯誤**: 運行 `init` 命令創建新表
3. **API限制**: 注意MLB API的請求頻率限制
4. **權限問題**: 確保數據庫寫入權限

### 日誌查看
- 應用日誌: `comprehensive_data.log`
- Web界面: 操作日誌面板
- 命令行: 直接輸出到控制台

## 未來擴展

### 計劃功能
1. **實時傷兵報告**: 集成ESPN或其他傷兵數據源
2. **社交媒體情緒**: 分析球隊和球員的媒體情緒
3. **交易影響**: 分析球員交易對球隊化學的影響
4. **裁判因素**: 不同裁判的判決傾向
5. **旅行疲勞**: 球隊旅行距離和時區變化的影響

### 機器學習增強
1. **特徵權重**: 動態調整不同因素的重要性
2. **深度學習**: 使用神經網絡處理複雜的交互關係
3. **時間序列**: 更好地建模時間相關的趨勢

## 貢獻指南

### 添加新數據源
1. 在 `enhanced_data_models.py` 中定義新模型
2. 在 `comprehensive_data_fetcher.py` 中實現抓取邏輯
3. 更新 `comprehensive_data_manager.py` 中的管理功能
4. 在Web界面中添加相應的管理選項

### 測試
- 運行 `python run_comprehensive_data_test.py` 進行完整測試
- 使用 `python test_comprehensive_data.py` 進行單元測試

## 授權

本項目遵循原有的授權條款。
