#!/usr/bin/env python3
"""
演示多 API 系統與 MLB 預測系統的整合
展示如何在實際應用中使用可靠的數據獲取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from models.reliable_odds_fetcher import ReliableOddsFetcher
from models.free_api_fetcher import FreeAPIFetcher

def print_separator(title: str):
    """打印分隔線"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def demonstrate_api_fallback():
    """演示 API 自動切換功能"""
    print_separator("API 自動切換演示")
    
    print("🎯 演示場景:")
    print("1. 付費 API 密鑰已用完額度")
    print("2. 系統自動切換到免費 API")
    print("3. 確保數據獲取不中斷")
    
    fetcher = ReliableOddsFetcher()
    
    print("\n🔍 嘗試獲取今天的 MLB 數據...")
    
    # 獲取數據
    result = fetcher.fetch_mlb_odds()
    
    if result.get('success'):
        print("✅ 數據獲取成功！")
        
        if result.get('is_free_api'):
            print("🔄 自動切換到免費 API")
            print(f"📊 數據源: {', '.join(result.get('sources', []))}")
            print("📝 注意: 免費 API 不包含博彩賠率，但提供完整的比賽數據")
        else:
            print(f"💰 使用付費 API: {result.get('source')}")
        
        games = result.get('games', [])
        print(f"🎮 找到 {len(games)} 場比賽")
        
        # 顯示前3場比賽的詳細信息
        print("\n📋 比賽詳情 (前3場):")
        for i, game in enumerate(games[:3]):
            home_team = game.get('home_team', {})
            away_team = game.get('away_team', {})
            
            print(f"\n{i+1}. {away_team.get('abbreviation', 'N/A')} @ {home_team.get('abbreviation', 'N/A')}")
            print(f"   狀態: {game.get('status', 'N/A')}")
            
            # 顯示比分
            if home_team.get('score') and away_team.get('score'):
                print(f"   比分: {away_team.get('score')} - {home_team.get('score')}")
            
            # 顯示戰績
            if home_team.get('record') and away_team.get('record'):
                print(f"   戰績: {away_team.get('record')} vs {home_team.get('record')}")
            
            # 顯示投手信息
            pitchers = game.get('probable_pitchers', {})
            if pitchers:
                home_pitcher = pitchers.get('home', {}).get('name', '')
                away_pitcher = pitchers.get('away', {}).get('name', '')
                if home_pitcher or away_pitcher:
                    print(f"   投手: {away_pitcher} vs {home_pitcher}")
            
            # 顯示賠率信息
            odds = game.get('odds')
            if odds:
                print(f"   賠率提供商: {odds.get('provider', 'N/A')}")
                if odds.get('home_odds') and odds.get('away_odds'):
                    print(f"   賠率: {odds.get('away_odds')} / {odds.get('home_odds')}")
    
    else:
        print(f"❌ 數據獲取失敗: {result.get('error')}")

def demonstrate_historical_data():
    """演示歷史數據獲取"""
    print_separator("歷史數據獲取演示")
    
    print("🎯 演示場景:")
    print("1. 獲取過去幾天的比賽數據")
    print("2. 用於模型訓練和回測")
    print("3. 展示數據一致性")
    
    fetcher = ReliableOddsFetcher()
    
    # 測試過去3天的數據
    test_dates = [
        date.today() - timedelta(days=1),
        date.today() - timedelta(days=2),
        date.today() - timedelta(days=3)
    ]
    
    historical_data = []
    
    for test_date in test_dates:
        print(f"\n📅 獲取 {test_date} 的數據...")
        
        result = fetcher.fetch_mlb_odds(test_date)
        
        if result.get('success'):
            games_count = len(result.get('games', []))
            source_info = ""
            
            if result.get('is_free_api'):
                source_info = f"免費 API ({', '.join(result.get('sources', []))})"
            else:
                source_info = f"付費 API ({result.get('source', 'N/A')})"
            
            print(f"   ✅ 找到 {games_count} 場比賽 - {source_info}")
            
            # 統計已完成的比賽
            completed_games = [g for g in result.get('games', []) if 'Final' in g.get('status', '')]
            print(f"   📊 已完成比賽: {len(completed_games)} 場")
            
            historical_data.append({
                'date': test_date,
                'total_games': games_count,
                'completed_games': len(completed_games),
                'source': source_info
            })
        
        else:
            print(f"   ❌ 失敗: {result.get('error', '未知錯誤')}")
    
    # 顯示歷史數據摘要
    if historical_data:
        print("\n📈 歷史數據摘要:")
        print("-" * 40)
        total_games = sum(d['total_games'] for d in historical_data)
        total_completed = sum(d['completed_games'] for d in historical_data)
        
        print(f"總比賽數: {total_games}")
        print(f"已完成比賽: {total_completed}")
        print(f"完成率: {total_completed/total_games*100:.1f}%" if total_games > 0 else "N/A")

def demonstrate_prediction_integration():
    """演示與預測系統的整合"""
    print_separator("預測系統整合演示")
    
    print("🎯 演示場景:")
    print("1. 模擬預測系統如何使用多 API 數據")
    print("2. 展示數據預處理流程")
    print("3. 顯示預測輸入格式")
    
    fetcher = ReliableOddsFetcher()
    
    print("\n🔍 獲取今天的比賽數據用於預測...")
    
    result = fetcher.fetch_mlb_odds()
    
    if not result.get('success'):
        print(f"❌ 無法獲取數據: {result.get('error')}")
        return
    
    games = result.get('games', [])
    scheduled_games = [g for g in games if 'Scheduled' in g.get('status', '')]
    
    print(f"✅ 找到 {len(scheduled_games)} 場待預測比賽")
    
    if not scheduled_games:
        print("ℹ️  今天沒有待預測的比賽")
        return
    
    print("\n🤖 模擬預測流程:")
    
    for i, game in enumerate(scheduled_games[:3]):  # 只處理前3場
        home_team = game.get('home_team', {})
        away_team = game.get('away_team', {})
        
        print(f"\n{i+1}. 預測比賽: {away_team.get('abbreviation')} @ {home_team.get('abbreviation')}")
        
        # 模擬特徵提取
        features = {
            'home_team': home_team.get('abbreviation', ''),
            'away_team': away_team.get('abbreviation', ''),
            'home_record': home_team.get('record', ''),
            'away_record': away_team.get('record', ''),
            'venue': game.get('venue', ''),
            'has_weather': game.get('weather') is not None,
            'has_odds': game.get('odds') is not None,
            'has_pitchers': bool(game.get('probable_pitchers'))
        }
        
        print("   📊 提取的特徵:")
        for key, value in features.items():
            print(f"      {key}: {value}")
        
        # 模擬預測結果
        print("   🎯 模擬預測結果:")
        print("      勝負預測: 主隊勝 (65% 信心度)")
        print("      總分預測: 8.5 分")
        print("      讓分預測: 主隊 -1.5")
        
        if game.get('odds'):
            print("      📈 有博彩賠率可用於比較")
        else:
            print("      📝 無博彩賠率 (使用免費 API)")

def demonstrate_system_monitoring():
    """演示系統監控功能"""
    print_separator("系統監控演示")
    
    print("🎯 演示場景:")
    print("1. 監控各 API 的健康狀況")
    print("2. 追蹤使用統計")
    print("3. 提供運維建議")
    
    fetcher = ReliableOddsFetcher()
    
    print("\n📊 獲取系統狀態...")
    status = fetcher.get_status_report()
    
    print(f"⏰ 報告時間: {status['timestamp']}")
    print(f"🔗 付費端點: {status['active_endpoints']}/{status['total_endpoints']} 活躍")
    print(f"✅ 可用端點: {status['available_endpoints']}")
    
    print("\n💰 付費 API 詳情:")
    for endpoint in status['endpoints']:
        status_icon = "✅" if endpoint['is_active'] else "❌"
        rate_icon = "✅" if endpoint['rate_limit_ok'] else "⏰"
        
        print(f"   {status_icon} {endpoint['name']} (優先級 {endpoint['priority']})")
        print(f"      使用量: {endpoint['request_count']}/{endpoint['rate_limit']} 請求/小時")
        print(f"      速率限制: {rate_icon}")
        
        if endpoint['last_error']:
            print(f"      最後錯誤: {endpoint['last_error'][:60]}...")
    
    print("\n🆓 免費 API 詳情:")
    for api in status.get('free_apis', []):
        status_icon = "✅" if api['status'] == "可用" else "❌"
        print(f"   {status_icon} {api['name']}: {api['status']}")
        
        if 'games_count' in api:
            print(f"      當前比賽數: {api['games_count']}")
    
    # 提供運維建議
    print("\n💡 運維建議:")
    
    active_paid_apis = [e for e in status['endpoints'] if e['is_active']]
    if not active_paid_apis:
        print("   ⚠️  所有付費 API 都不可用，建議:")
        print("      - 檢查 API 密鑰是否有效")
        print("      - 確認是否達到使用限制")
        print("      - 考慮升級 API 方案")
    
    available_free_apis = [a for a in status.get('free_apis', []) if a['status'] == "可用"]
    if available_free_apis:
        print(f"   ✅ {len(available_free_apis)} 個免費 API 可用，系統運行正常")
    
    high_usage_apis = [e for e in status['endpoints'] if e['request_count'] > e['rate_limit'] * 0.8]
    if high_usage_apis:
        print("   ⚠️  以下 API 使用量較高:")
        for api in high_usage_apis:
            usage_percent = api['request_count'] / api['rate_limit'] * 100
            print(f"      - {api['name']}: {usage_percent:.1f}% 使用率")

def main():
    """主演示函數"""
    print_separator("多 API 系統整合演示")
    
    print("🎉 歡迎使用 MLB 多 API 數據獲取系統！")
    print("\n📋 演示內容:")
    print("1. API 自動切換功能")
    print("2. 歷史數據獲取")
    print("3. 預測系統整合")
    print("4. 系統監控功能")
    
    # 執行各項演示
    demonstrate_api_fallback()
    demonstrate_historical_data()
    demonstrate_prediction_integration()
    demonstrate_system_monitoring()
    
    print_separator("演示完成")
    print("🎉 多 API 系統整合演示完成！")
    
    print("\n🚀 系統特色:")
    print("✅ 智能 API 切換 - 付費失敗時自動使用免費 API")
    print("✅ 多數據源整合 - ESPN API + MLB Stats API")
    print("✅ 歷史數據支持 - 支持任意日期查詢")
    print("✅ 統一數據格式 - 無論數據源如何，格式一致")
    print("✅ 實時監控 - 詳細的狀態報告和使用統計")
    print("✅ 錯誤恢復 - 自動重試和端點重置")
    
    print("\n📈 實際應用價值:")
    print("- 確保 MLB 預測系統的數據可用性")
    print("- 降低對單一 API 的依賴風險")
    print("- 提供成本效益的數據獲取方案")
    print("- 支持大規模歷史數據分析")
    print("- 為生產環境提供可靠的數據基礎")
    
    print("\n🔧 下一步建議:")
    print("1. 將此系統整合到現有的 MLB 預測流程中")
    print("2. 設置定時任務自動獲取每日數據")
    print("3. 建立數據質量監控和告警機制")
    print("4. 考慮添加更多免費數據源作為備用")

if __name__ == "__main__":
    main()
