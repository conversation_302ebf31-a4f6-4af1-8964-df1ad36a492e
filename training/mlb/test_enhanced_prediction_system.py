#!/usr/bin/env python3
"""
測試增強的預測系統
確認投手信息現在正確地從博彩盤口數據中獲取
"""

import os
import sys
from datetime import date, datetime, timedelta
import logging

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_enhanced_betting_odds_fetcher():
    """測試增強的博彩盤口獲取器"""
    logger.info("=== 測試增強的博彩盤口獲取器 ===")
    
    try:
        from flask import Flask
        from models.real_betting_odds_fetcher import RealBettingOddsFetcher
        
        # 創建測試應用
        app = Flask(__name__)
        app.config['TESTING'] = True
        
        with app.app_context():
            # 初始化獲取器
            fetcher = RealBettingOddsFetcher(app)
            
            # 測試今天的數據
            target_date = date.today()
            logger.info(f"測試獲取 {target_date} 的博彩盤口數據...")
            
            odds_data = fetcher.get_mlb_odds_today(target_date)
            
            if odds_data and odds_data.get('success'):
                games = odds_data.get('games', [])
                logger.info(f"✅ 成功獲取 {len(games)} 場比賽的盤口數據")
                
                # 檢查投手信息
                pitcher_games = [g for g in games if g.get('pitcher_info', {}).get('home_pitcher') != 'N/A']
                logger.info(f"✅ 其中 {len(pitcher_games)} 場比賽包含投手信息")
                
                # 顯示前幾場的投手信息
                for i, game in enumerate(pitcher_games[:3]):
                    pitcher_info = game.get('pitcher_info', {})
                    logger.info(f"   比賽 {i+1}: {game.get('away_team')} @ {game.get('home_team')}")
                    logger.info(f"     客隊投手: {pitcher_info.get('away_pitcher', 'N/A')}")
                    logger.info(f"     主隊投手: {pitcher_info.get('home_pitcher', 'N/A')}")
                    logger.info(f"     數據源: {pitcher_info.get('source', 'unknown')}")
                    logger.info(f"     信心度: {pitcher_info.get('confidence', 'unknown')}")
                
                return True
            else:
                logger.warning(f"未能獲取盤口數據: {odds_data.get('message', '未知錯誤')}")
                return False
                
    except Exception as e:
        logger.error(f"測試博彩盤口獲取器失敗: {e}")
        return False

def test_unified_predictor():
    """測試統一預測器"""
    logger.info("=== 測試統一預測器 ===")
    
    try:
        from flask import Flask
        from models.unified_betting_predictor import UnifiedBettingPredictor
        from models.database import db, Game
        
        # 創建測試應用
        app = Flask(__name__)
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/mlb_data.db'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        db.init_app(app)
        
        with app.app_context():
            # 初始化統一預測器
            predictor = UnifiedBettingPredictor(app)
            
            # 獲取今天的比賽
            target_date = date.today()
            games = Game.query.filter(
                Game.date >= target_date,
                Game.date < target_date + timedelta(days=1)
            ).limit(3).all()
            
            if not games:
                logger.warning("今天沒有找到比賽")
                return False
            
            logger.info(f"找到 {len(games)} 場比賽，測試預測功能...")
            
            # 測試第一場比賽的預測
            game = games[0]
            logger.info(f"測試比賽: {game.away_team} @ {game.home_team}")
            
            prediction = predictor.generate_unified_prediction(game.game_id, target_date)
            
            if prediction.get('success'):
                logger.info("✅ 預測生成成功")
                
                # 檢查投手信息
                pitcher_info = prediction.get('pitcher_info')
                if pitcher_info:
                    logger.info("✅ 包含投手信息:")
                    logger.info(f"   客隊投手: {pitcher_info.get('away_pitcher', 'N/A')}")
                    logger.info(f"   主隊投手: {pitcher_info.get('home_pitcher', 'N/A')}")
                    logger.info(f"   數據源: {pitcher_info.get('source', 'unknown')}")
                    logger.info(f"   信心度: {pitcher_info.get('confidence', 'unknown')}")
                else:
                    logger.warning("❌ 未包含投手信息")
                
                # 檢查預測數據
                predictions = prediction.get('predictions', {})
                score_pred = predictions.get('score', {})
                if score_pred and 'error' not in score_pred:
                    home_score = score_pred.get('home_score', 0)
                    away_score = score_pred.get('away_score', 0)
                    logger.info(f"✅ 比分預測: {game.away_team} {away_score} - {home_score} {game.home_team}")
                else:
                    logger.warning("❌ 比分預測失敗")
                
                return True
            else:
                logger.error(f"預測生成失敗: {prediction.get('error', '未知錯誤')}")
                return False
                
    except Exception as e:
        logger.error(f"測試統一預測器失敗: {e}")
        return False

def test_api_response():
    """測試API響應格式"""
    logger.info("=== 測試API響應格式 ===")
    
    try:
        from flask import Flask
        from views.unified_predictions import get_unified_predictor
        
        # 創建測試應用
        app = Flask(__name__)
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/mlb_data.db'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        with app.app_context():
            # 獲取統一預測器
            predictor = get_unified_predictor()
            
            # 生成今天的預測
            target_date = date.today()
            result = predictor.generate_daily_predictions(target_date)
            
            if result.get('success'):
                predictions = result.get('predictions', [])
                logger.info(f"✅ API生成 {len(predictions)} 場比賽預測")
                
                # 檢查第一場比賽的API格式
                if predictions:
                    pred = predictions[0]
                    logger.info(f"測試比賽: {pred.get('game_info', {}).get('away_team')} @ {pred.get('game_info', {}).get('home_team')}")
                    
                    # 檢查投手信息是否在頂層
                    if 'pitcher_info' in pred:
                        pitcher_info = pred['pitcher_info']
                        logger.info("✅ API級別的投手信息:")
                        logger.info(f"   客隊投手: {pitcher_info.get('away_pitcher', 'N/A')}")
                        logger.info(f"   主隊投手: {pitcher_info.get('home_pitcher', 'N/A')}")
                    else:
                        logger.warning("❌ API響應中缺少投手信息")
                    
                    # 檢查預測分數
                    predictions_data = pred.get('predictions', {})
                    score_data = predictions_data.get('score', {})
                    if score_data and 'error' not in score_data:
                        logger.info(f"✅ API比分預測: {score_data.get('away_score', 0)} - {score_data.get('home_score', 0)}")
                    else:
                        logger.warning("❌ API比分預測失敗")
                
                return True
            else:
                logger.error(f"API預測失敗: {result.get('error', '未知錯誤')}")
                return False
                
    except Exception as e:
        logger.error(f"測試API響應失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試增強的預測系統...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 測試1: 博彩盤口獲取器
    if test_enhanced_betting_odds_fetcher():
        success_count += 1
    
    print("-" * 60)
    
    # 測試2: 統一預測器
    if test_unified_predictor():
        success_count += 1
    
    print("-" * 60)
    
    # 測試3: API響應
    if test_api_response():
        success_count += 1
    
    print("=" * 60)
    print(f"📊 測試結果: {success_count}/{total_tests} 通過")
    
    if success_count == total_tests:
        print("✅ 所有測試通過！增強的預測系統工作正常。")
        print("🎯 投手信息現在應該在預測界面中正確顯示。")
    else:
        print("❌ 部分測試失敗，需要進一步調試。")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()