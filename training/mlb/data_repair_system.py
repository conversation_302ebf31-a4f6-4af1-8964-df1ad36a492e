#!/usr/bin/env python3
"""
MLB數據批量修復系統
解決數據完整性問題，包括：
1. 批量修復423場缺失比賽
2. 改善5.7%的boxscore覆蓋率 
3. 修復"更新不了最新的狀況"問題
4. 智能重試和錯誤恢復
"""

import sys
sys.path.append('.')

import requests
import json
import time
import threading
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Set
import logging
from collections import defaultdict
import calendar
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import sqlite3

from models.database import db, Game, Prediction, BettingOdds, PredictionHistory
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from boxscore_data_collector import BoxscoreDataCollector
from team_pitcher_detailed_analyzer import TeamPitcherDetailedAnalyzer
from monthly_data_manager import MonthlyDataManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataRepairSystem:
    """MLB數據批量修復系統"""
    
    def __init__(self):
        self.mlb_api_base = "https://statsapi.mlb.com/api/v1"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MLB-Data-Repair-System/2.0',
            'Accept': 'application/json'
        })
        
        # 初始化依賴系統
        self.monthly_manager = MonthlyDataManager()
        self.odds_fetcher = RealBettingOddsFetcher()
        self.boxscore_collector = BoxscoreDataCollector()
        self.pitcher_analyzer = TeamPitcherDetailedAnalyzer()
        
        # 重試配置
        self.max_retries = 5
        self.base_delay = 2.0  # 基礎延遲秒數
        self.max_delay = 60.0  # 最大延遲秒數
        
        # API速率限制配置
        self.api_requests_per_minute = 100
        self.request_timestamps = []
        
    def create_repair_report(self, year: int, month: int) -> Dict:
        """創建詳細的修復需求報告"""
        logger.info(f"📊 創建 {year}年{month}月 詳細修復報告...")
        
        from flask import current_app
        with current_app.app_context():
            # 獲取完整性分析
            completeness = self.monthly_manager.check_data_completeness(year, month)
            
            # 詳細分析每個缺失項目
            repair_plan = {
                'month': f"{year}-{month:02d}",
                'created_at': datetime.now().isoformat(),
                'total_issues': 0,
                'repair_tasks': [],
                'estimated_time': 0,
                'priority_levels': {
                    'critical': [],  # 已完成但缺數據的比賽
                    'high': [],      # 進行中但缺數據的比賽  
                    'medium': [],    # 預定但缺預測的比賽
                    'low': []        # 其他數據缺失
                }
            }
            
            # 分析缺失比賽
            for missing_game in completeness['missing_games']:
                task = {
                    'type': 'missing_game',
                    'game_id': missing_game['game_id'],
                    'date': missing_game['date'],
                    'matchup': missing_game['matchup'],
                    'status': missing_game['status'],
                    'venue': missing_game['venue'],
                    'repair_actions': ['create_game_record', 'fetch_game_details']
                }
                
                # 根據比賽狀態確定優先級
                if missing_game['status'] == 'Final':
                    task['priority'] = 'critical'
                    task['repair_actions'].extend(['fetch_boxscore', 'update_scores', 'create_prediction'])
                    repair_plan['priority_levels']['critical'].append(task)
                elif 'In Progress' in missing_game['status']:
                    task['priority'] = 'high'  
                    task['repair_actions'].extend(['monitor_game', 'create_prediction'])
                    repair_plan['priority_levels']['high'].append(task)
                else:
                    task['priority'] = 'medium'
                    task['repair_actions'].append('create_prediction')
                    repair_plan['priority_levels']['medium'].append(task)
                
                repair_plan['repair_tasks'].append(task)
            
            # 分析缺失boxscore
            for missing_boxscore in completeness['games_without_boxscore']:
                task = {
                    'type': 'missing_boxscore',
                    'game_id': missing_boxscore['game_id'],
                    'date': missing_boxscore['date'],
                    'matchup': missing_boxscore['matchup'],
                    'priority': 'critical',
                    'repair_actions': ['fetch_boxscore', 'update_game_scores', 'verify_data']
                }
                repair_plan['repair_tasks'].append(task)
                repair_plan['priority_levels']['critical'].append(task)
            
            # 分析缺失預測
            for missing_prediction in completeness['missing_predictions']:
                task = {
                    'type': 'missing_prediction',
                    'game_id': missing_prediction['game_id'],
                    'date': missing_prediction['date'],
                    'matchup': missing_prediction['matchup'],
                    'priority': 'medium',
                    'repair_actions': ['generate_prediction', 'analyze_teams', 'fetch_pitcher_data']
                }
                repair_plan['repair_tasks'].append(task)
                repair_plan['priority_levels']['medium'].append(task)
            
            # 分析缺失賠率數據
            for missing_odds in completeness['missing_odds']:
                task = {
                    'type': 'missing_odds',
                    'game_id': missing_odds['game_id'],
                    'date': missing_odds['date'],
                    'matchup': missing_odds['matchup'],
                    'priority': 'low',
                    'repair_actions': ['fetch_historical_odds', 'estimate_odds']
                }
                repair_plan['repair_tasks'].append(task)
                repair_plan['priority_levels']['low'].append(task)
            
            # 計算修復統計
            repair_plan['total_issues'] = len(repair_plan['repair_tasks'])
            repair_plan['priority_summary'] = {
                'critical': len(repair_plan['priority_levels']['critical']),
                'high': len(repair_plan['priority_levels']['high']),
                'medium': len(repair_plan['priority_levels']['medium']),
                'low': len(repair_plan['priority_levels']['low'])
            }
            
            # 估算修復時間 (秒)
            repair_plan['estimated_time'] = (
                repair_plan['priority_summary']['critical'] * 30 +  # 每個關鍵問題30秒
                repair_plan['priority_summary']['high'] * 20 +      # 每個高優先級20秒
                repair_plan['priority_summary']['medium'] * 15 +    # 每個中優先級15秒
                repair_plan['priority_summary']['low'] * 10         # 每個低優先級10秒
            )
            
            logger.info(f"📋 修復報告完成: {repair_plan['total_issues']} 個問題需要修復")
            logger.info(f"   關鍵: {repair_plan['priority_summary']['critical']} | " +
                       f"高: {repair_plan['priority_summary']['high']} | " +
                       f"中: {repair_plan['priority_summary']['medium']} | " +
                       f"低: {repair_plan['priority_summary']['low']}")
            logger.info(f"   預估修復時間: {repair_plan['estimated_time']/60:.1f} 分鐘")
            
            return repair_plan
    
    def execute_batch_repair(self, repair_plan: Dict, max_concurrent: int = 5) -> Dict:
        """執行批量修復"""
        logger.info(f"🔧 開始執行批量修復 ({repair_plan['total_issues']} 個問題)...")
        
        start_time = datetime.now()
        repair_results = {
            'repair_plan_id': repair_plan['month'],
            'start_time': start_time.isoformat(),
            'total_tasks': repair_plan['total_issues'],
            'completed_tasks': 0,
            'failed_tasks': 0,
            'success_rate': 0.0,
            'results_by_priority': {
                'critical': {'completed': 0, 'failed': 0, 'details': []},
                'high': {'completed': 0, 'failed': 0, 'details': []},
                'medium': {'completed': 0, 'failed': 0, 'details': []},
                'low': {'completed': 0, 'failed': 0, 'details': []}
            },
            'errors': [],
            'performance_metrics': {}
        }
        
        # 按優先級順序處理
        priority_order = ['critical', 'high', 'medium', 'low']
        
        for priority in priority_order:
            tasks = repair_plan['priority_levels'][priority]
            if not tasks:
                continue
                
            logger.info(f"🎯 處理 {priority} 優先級任務 ({len(tasks)} 個)...")
            
            # 使用線程池進行並發處理
            with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
                future_to_task = {
                    executor.submit(self._repair_single_task, task): task 
                    for task in tasks
                }
                
                for future in as_completed(future_to_task):
                    task = future_to_task[future]
                    try:
                        result = future.result()
                        if result['success']:
                            repair_results['completed_tasks'] += 1
                            repair_results['results_by_priority'][priority]['completed'] += 1
                        else:
                            repair_results['failed_tasks'] += 1  
                            repair_results['results_by_priority'][priority]['failed'] += 1
                            repair_results['errors'].append({
                                'task_id': task['game_id'],
                                'priority': priority,
                                'error': result.get('error', 'Unknown error')
                            })
                        
                        repair_results['results_by_priority'][priority]['details'].append(result)
                        
                    except Exception as e:
                        repair_results['failed_tasks'] += 1
                        repair_results['results_by_priority'][priority]['failed'] += 1
                        error_msg = f"Task {task['game_id']} failed: {e}"
                        logger.error(error_msg)
                        repair_results['errors'].append({
                            'task_id': task['game_id'],
                            'priority': priority,
                            'error': error_msg
                        })
            
            # 優先級間增加延遲，避免API過載
            if priority != 'low':
                time.sleep(5)
        
        # 計算最終統計
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        repair_results['end_time'] = end_time.isoformat()
        repair_results['duration_seconds'] = duration
        repair_results['success_rate'] = (repair_results['completed_tasks'] / repair_results['total_tasks']) * 100 if repair_results['total_tasks'] > 0 else 0
        
        repair_results['performance_metrics'] = {
            'tasks_per_second': repair_results['completed_tasks'] / duration if duration > 0 else 0,
            'average_task_time': duration / repair_results['total_tasks'] if repair_results['total_tasks'] > 0 else 0,
            'error_rate': (repair_results['failed_tasks'] / repair_results['total_tasks']) * 100 if repair_results['total_tasks'] > 0 else 0
        }
        
        logger.info(f"✅ 批量修復完成!")
        logger.info(f"   總任務: {repair_results['total_tasks']}")
        logger.info(f"   成功: {repair_results['completed_tasks']} ({repair_results['success_rate']:.1f}%)")
        logger.info(f"   失敗: {repair_results['failed_tasks']}")
        logger.info(f"   耗時: {duration:.1f}秒")
        logger.info(f"   平均每任務: {repair_results['performance_metrics']['average_task_time']:.2f}秒")
        
        return repair_results
    
    def _repair_single_task(self, task: Dict) -> Dict:
        """修復單個任務"""
        task_result = {
            'task_id': task['game_id'],
            'task_type': task['type'],
            'priority': task['priority'],
            'success': False,
            'actions_completed': [],
            'error': None,
            'start_time': datetime.now().isoformat()
        }
        
        try:
            # 根據任務類型執行相應的修復動作
            if task['type'] == 'missing_game':
                success = self._repair_missing_game(task, task_result)
            elif task['type'] == 'missing_boxscore':
                success = self._repair_missing_boxscore(task, task_result)
            elif task['type'] == 'missing_prediction':
                success = self._repair_missing_prediction(task, task_result)
            elif task['type'] == 'missing_odds':
                success = self._repair_missing_odds(task, task_result)
            else:
                raise ValueError(f"Unknown task type: {task['type']}")
            
            task_result['success'] = success
            
        except Exception as e:
            task_result['error'] = str(e)
            logger.error(f"修復任務 {task['game_id']} 失敗: {e}")
        
        task_result['end_time'] = datetime.now().isoformat()
        return task_result
    
    def _repair_missing_game(self, task: Dict, result: Dict) -> bool:
        """修復缺失的比賽記錄"""
        from flask import current_app
        with current_app.app_context():
            try:
                # 1. 從MLB API獲取比賽詳細信息
                game_data = self._fetch_game_details_with_retry(task['game_id'])
                if not game_data:
                    result['error'] = 'Unable to fetch game data from MLB API'
                    return False
                
                result['actions_completed'].append('fetch_game_details')
                
                # 2. 創建或更新Game記錄
                success = self._create_or_update_game_record(game_data, result)
                if success:
                    result['actions_completed'].append('create_game_record')
                
                # 3. 如果比賽已完成，獲取boxscore
                if game_data.get('status') == 'Final':
                    boxscore_success = self._fetch_and_save_boxscore(game_data, result)
                    if boxscore_success:
                        result['actions_completed'].append('fetch_boxscore')
                
                # 4. 生成預測（如果需要）
                prediction_success = self._generate_missing_prediction(game_data, result)
                if prediction_success:
                    result['actions_completed'].append('create_prediction')
                
                return success
                
            except Exception as e:
                result['error'] = f'Failed to repair missing game: {e}'
                return False
    
    def _repair_missing_boxscore(self, task: Dict, result: Dict) -> bool:
        """修復缺失的boxscore數據"""
        from flask import current_app
        with current_app.app_context():
            try:
                # 找到現有的Game記錄
                game = db.session.query(Game).filter_by(game_id=task['game_id']).first()
                if not game:
                    result['error'] = 'Game record not found in database'
                    return False
                
                # 獲取game_pk
                game_pk = self._extract_game_pk_from_id(task['game_id'])
                if not game_pk:
                    result['error'] = 'Unable to extract game_pk from game_id'
                    return False
                
                # 獲取boxscore數據
                boxscore_data = self._fetch_boxscore_with_retry(game_pk)
                if not boxscore_data:
                    result['error'] = 'Unable to fetch boxscore data'
                    return False
                
                result['actions_completed'].append('fetch_boxscore')
                
                # 更新Game記錄的得分
                if self._update_game_scores_from_boxscore(game, boxscore_data):
                    result['actions_completed'].append('update_game_scores')
                    db.session.commit()
                    return True
                else:
                    result['error'] = 'Failed to update game scores'
                    return False
                
            except Exception as e:
                result['error'] = f'Failed to repair missing boxscore: {e}'
                db.session.rollback()
                return False
    
    def _repair_missing_prediction(self, task: Dict, result: Dict) -> bool:
        """修復缺失的預測"""
        from flask import current_app
        with current_app.app_context():
            try:
                # 檢查是否已存在預測
                existing_prediction = db.session.query(Prediction).filter_by(
                    game_id=task['game_id']
                ).first()
                
                if existing_prediction:
                    result['actions_completed'].append('prediction_exists')
                    return True
                
                # 找到對應的Game記錄
                game = db.session.query(Game).filter_by(game_id=task['game_id']).first()
                if not game:
                    result['error'] = 'Game record not found'
                    return False
                
                # 創建基本預測記錄
                prediction = Prediction(
                    game_id=task['game_id'],
                    home_team=game.home_team,
                    away_team=game.away_team,
                    prediction_date=datetime.now(),
                    confidence_score=0.5,  # 默認置信度
                    predicted_winner='TBD',  # 待定
                    prediction_source='data_repair_system'
                )
                
                db.session.add(prediction)
                db.session.commit()
                
                result['actions_completed'].append('create_basic_prediction')
                return True
                
            except Exception as e:
                result['error'] = f'Failed to repair missing prediction: {e}'
                db.session.rollback()
                return False
    
    def _repair_missing_odds(self, task: Dict, result: Dict) -> bool:
        """修復缺失的賠率數據"""
        from flask import current_app
        with current_app.app_context():
            try:
                # 嘗試從歷史數據或外部源獲取賠率
                game_date = datetime.strptime(task['date'], '%Y-%m-%d').date()
                
                # 如果比賽日期是過去的，嘗試獲取歷史賠率
                if game_date < date.today():
                    odds_data = self._fetch_historical_odds(task['game_id'], game_date)
                    if odds_data:
                        result['actions_completed'].append('fetch_historical_odds')
                        return True
                
                # 創建估算的賠率記錄（基於團隊實力）
                estimated_odds = self._estimate_odds_from_team_stats(task)
                if estimated_odds:
                    result['actions_completed'].append('estimate_odds')
                    return True
                
                result['error'] = 'Unable to fetch or estimate odds data'
                return False
                
            except Exception as e:
                result['error'] = f'Failed to repair missing odds: {e}'
                return False
    
    def _fetch_game_details_with_retry(self, game_id: str) -> Optional[Dict]:
        """使用重試機制獲取比賽詳情"""
        # 從game_id提取信息來構造API請求
        # game_id格式通常是: TEAM1_TEAM2_YYYYMMDD
        parts = game_id.split('_')
        if len(parts) >= 3:
            date_str = parts[-1]
            if len(date_str) == 8:
                game_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                return self._fetch_schedule_for_date_with_retry(game_date, game_id)
        
        return None
    
    def _fetch_schedule_for_date_with_retry(self, date_str: str, target_game_id: str) -> Optional[Dict]:
        """獲取特定日期的賽程並找到目標比賽"""
        for attempt in range(self.max_retries):
            try:
                self._wait_for_rate_limit()
                
                url = f"{self.mlb_api_base}/schedule"
                params = {
                    'date': date_str,
                    'sportId': 1,
                    'hydrate': 'team,linescore,flags,liveLookin,review,decisions,person,probablePitcher,stats'
                }
                
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                
                schedule_data = response.json()
                
                # 查找目標比賽
                if 'dates' in schedule_data:
                    for date_info in schedule_data['dates']:
                        for game in date_info.get('games', []):
                            # 構造game_id進行匹配
                            away_team = game.get('teams', {}).get('away', {}).get('team', {}).get('abbreviation', '')
                            home_team = game.get('teams', {}).get('home', {}).get('team', {}).get('abbreviation', '')
                            constructed_id = f"{away_team}_{home_team}_{date_str.replace('-', '')}"
                            
                            if constructed_id == target_game_id:
                                return {
                                    'game_pk': str(game.get('gamePk', '')),
                                    'game_id': target_game_id,
                                    'date': date_str,
                                    'home_team': home_team,
                                    'away_team': away_team,
                                    'status': game.get('status', {}).get('detailedState', ''),
                                    'venue': game.get('venue', {}).get('name', ''),
                                    'scheduled_time': game.get('gameDate', ''),
                                    'linescore': game.get('linescore', {})
                                }
                
                return None
                
            except Exception as e:
                wait_time = min(self.base_delay * (2 ** attempt) + random.uniform(0, 1), self.max_delay)
                logger.warning(f"獲取 {date_str} 賽程失敗 (嘗試 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(wait_time)
                else:
                    logger.error(f"獲取 {date_str} 賽程最終失敗")
                    return None
    
    def _wait_for_rate_limit(self):
        """API速率限制控制"""
        now = time.time()
        # 清理超過1分鐘的舊時間戳
        self.request_timestamps = [ts for ts in self.request_timestamps if now - ts < 60]
        
        # 如果達到限制，等待
        if len(self.request_timestamps) >= self.api_requests_per_minute:
            wait_time = 60 - (now - self.request_timestamps[0])
            if wait_time > 0:
                logger.info(f"API速率限制: 等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
        
        # 記錄當前請求時間
        self.request_timestamps.append(now)
    
    def _create_or_update_game_record(self, game_data: Dict, result: Dict) -> bool:
        """創建或更新Game記錄"""
        try:
            from flask import current_app
            with current_app.app_context():
                # 檢查是否已存在
                existing_game = db.session.query(Game).filter_by(
                    game_id=game_data['game_id']
                ).first()
                
                if existing_game:
                    # 更新現有記錄
                    existing_game.venue = game_data.get('venue', existing_game.venue)
                    existing_game.scheduled_time = game_data.get('scheduled_time', existing_game.scheduled_time)
                    
                    # 如果有得分數據，更新得分
                    linescore = game_data.get('linescore', {})
                    if linescore and 'teams' in linescore:
                        home_runs = linescore['teams'].get('home', {}).get('runs')
                        away_runs = linescore['teams'].get('away', {}).get('runs')
                        if home_runs is not None:
                            existing_game.home_score = home_runs
                        if away_runs is not None:
                            existing_game.away_score = away_runs
                    
                    result['actions_completed'].append('update_existing_game')
                else:
                    # 創建新記錄
                    game = Game(
                        game_id=game_data['game_id'],
                        date=datetime.strptime(game_data['date'], '%Y-%m-%d').date(),
                        home_team=game_data['home_team'],
                        away_team=game_data['away_team'],
                        venue=game_data.get('venue', ''),
                        scheduled_time=game_data.get('scheduled_time', ''),
                        created_at=datetime.now()
                    )
                    
                    # 如果有得分數據，添加得分
                    linescore = game_data.get('linescore', {})
                    if linescore and 'teams' in linescore:
                        home_runs = linescore['teams'].get('home', {}).get('runs')
                        away_runs = linescore['teams'].get('away', {}).get('runs')
                        if home_runs is not None:
                            game.home_score = home_runs
                        if away_runs is not None:
                            game.away_score = away_runs
                    
                    db.session.add(game)
                    result['actions_completed'].append('create_new_game')
                
                db.session.commit()
                return True
                
        except Exception as e:
            logger.error(f"創建/更新Game記錄失敗: {e}")
            db.session.rollback()
            return False
    
    def _fetch_boxscore_with_retry(self, game_pk: str) -> Optional[Dict]:
        """使用重試機制獲取boxscore"""
        for attempt in range(self.max_retries):
            try:
                self._wait_for_rate_limit()
                boxscore = self.boxscore_collector.get_game_boxscore(game_pk)
                if boxscore:
                    return boxscore
                    
            except Exception as e:
                wait_time = min(self.base_delay * (2 ** attempt) + random.uniform(0, 1), self.max_delay)
                logger.warning(f"獲取 {game_pk} boxscore失敗 (嘗試 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(wait_time)
        
        return None
    
    def _extract_game_pk_from_id(self, game_id: str) -> Optional[str]:
        """從game_id提取game_pk（需要通過API查詢）"""
        # 這需要通過日期和球隊查詢來找到game_pk
        parts = game_id.split('_')
        if len(parts) >= 3:
            date_str = parts[-1]
            if len(date_str) == 8:
                formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                game_data = self._fetch_schedule_for_date_with_retry(formatted_date, game_id)
                if game_data and game_data.get('game_pk'):
                    return game_data['game_pk']
        
        return None
    
    def _update_game_scores_from_boxscore(self, game: Game, boxscore_data: Dict) -> bool:
        """從boxscore數據更新比賽得分"""
        try:
            # 從boxscore提取得分信息
            if 'teams' in boxscore_data:
                home_score = boxscore_data['teams'].get('home', {}).get('teamStats', {}).get('batting', {}).get('runs')
                away_score = boxscore_data['teams'].get('away', {}).get('teamStats', {}).get('batting', {}).get('runs')
                
                if home_score is not None:
                    game.home_score = home_score
                if away_score is not None:
                    game.away_score = away_score
                
                return True
            
        except Exception as e:
            logger.error(f"更新比賽得分失敗: {e}")
        
        return False
    
    def _fetch_and_save_boxscore(self, game_data: Dict, result: Dict) -> bool:
        """獲取並保存boxscore數據"""
        try:
            game_pk = game_data.get('game_pk')
            if not game_pk:
                return False
            
            boxscore = self._fetch_boxscore_with_retry(game_pk)
            if boxscore:
                # 這裡可以保存boxscore到數據庫或者更新Game記錄
                result['actions_completed'].append('boxscore_fetched')
                return True
            
        except Exception as e:
            logger.error(f"獲取保存boxscore失敗: {e}")
        
        return False
    
    def _generate_missing_prediction(self, game_data: Dict, result: Dict) -> bool:
        """為缺失比賽生成預測"""
        try:
            from flask import current_app
            with current_app.app_context():
                # 檢查是否已有預測
                existing = db.session.query(Prediction).filter_by(
                    game_id=game_data['game_id']
                ).first()
                
                if not existing:
                    prediction = Prediction(
                        game_id=game_data['game_id'],
                        home_team=game_data['home_team'],
                        away_team=game_data['away_team'],
                        prediction_date=datetime.now(),
                        confidence_score=0.5,
                        predicted_winner='TBD',
                        prediction_source='data_repair_system'
                    )
                    
                    db.session.add(prediction)
                    db.session.commit()
                    result['actions_completed'].append('prediction_generated')
                
                return True
                
        except Exception as e:
            logger.error(f"生成預測失敗: {e}")
            db.session.rollback()
            return False
    
    def _fetch_historical_odds(self, game_id: str, game_date: date) -> Optional[Dict]:
        """獲取歷史賠率數據（模擬實現）"""
        # 這裡應該實現從歷史賠率源獲取數據的邏輯
        # 目前返回None表示無法獲取歷史賠率
        return None
    
    def _estimate_odds_from_team_stats(self, task: Dict) -> Optional[Dict]:
        """基於球隊統計估算賠率（模擬實現）"""
        # 這裡應該實現基於球隊實力的賠率估算邏輯
        # 目前返回None表示無法估算
        return None
    
    def generate_repair_summary(self, repair_results: Dict) -> str:
        """生成修復摘要報告"""
        summary = []
        summary.append(f"📊 MLB數據修復摘要報告")
        summary.append(f"═" * 50)
        summary.append(f"修復期間: {repair_results['repair_plan_id']}")
        summary.append(f"執行時間: {repair_results['duration_seconds']:.1f} 秒")
        summary.append(f"")
        summary.append(f"📈 整體統計:")
        summary.append(f"   總任務數: {repair_results['total_tasks']}")
        summary.append(f"   成功完成: {repair_results['completed_tasks']} ({repair_results['success_rate']:.1f}%)")
        summary.append(f"   失敗任務: {repair_results['failed_tasks']}")
        summary.append(f"   平均耗時: {repair_results['performance_metrics']['average_task_time']:.2f} 秒/任務")
        summary.append(f"")
        summary.append(f"🎯 按優先級統計:")
        
        for priority in ['critical', 'high', 'medium', 'low']:
            stats = repair_results['results_by_priority'][priority]
            total = stats['completed'] + stats['failed']
            if total > 0:
                success_rate = (stats['completed'] / total) * 100
                summary.append(f"   {priority.upper()}: {stats['completed']}/{total} ({success_rate:.1f}%)")
        
        if repair_results['errors']:
            summary.append(f"")
            summary.append(f"❌ 錯誤詳情 (前5個):")
            for i, error in enumerate(repair_results['errors'][:5]):
                summary.append(f"   {i+1}. [{error['priority']}] {error['task_id']}: {error['error']}")
        
        return '\n'.join(summary)

def main():
    """主函數 - 演示數據修復系統"""
    repair_system = DataRepairSystem()
    
    # 獲取當前月份
    current_date = datetime.now()
    year = current_date.year
    month = current_date.month
    
    print(f"🔧 MLB數據修復系統")
    print(f"目標月份: {year}年{month}月")
    print("=" * 50)
    
    # 1. 創建修復報告
    print("📊 正在分析數據完整性問題...")
    repair_plan = repair_system.create_repair_report(year, month)
    
    if repair_plan['total_issues'] == 0:
        print("✅ 沒有發現需要修復的數據問題！")
        return
    
    print(f"📋 發現 {repair_plan['total_issues']} 個問題需要修復")
    print(f"   關鍵問題: {repair_plan['priority_summary']['critical']} 個")
    print(f"   高優先級: {repair_plan['priority_summary']['high']} 個") 
    print(f"   中優先級: {repair_plan['priority_summary']['medium']} 個")
    print(f"   低優先級: {repair_plan['priority_summary']['low']} 個")
    print(f"   預估修復時間: {repair_plan['estimated_time']/60:.1f} 分鐘")
    
    # 2. 執行修復（演示模式，不實際執行）
    print("\n🎯 若要執行修復，請運行:")
    print("   repair_results = repair_system.execute_batch_repair(repair_plan)")
    print("   summary = repair_system.generate_repair_summary(repair_results)")
    print("   print(summary)")

if __name__ == "__main__":
    main()