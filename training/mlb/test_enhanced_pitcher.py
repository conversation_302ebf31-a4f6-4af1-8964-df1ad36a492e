#!/usr/bin/env python3
"""
測試增強投手管理器
驗證 game_id 和 player_id 的正確顯示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.enhanced_pitcher_manager import enhanced_pitcher_manager

def test_game_and_player_ids():
    """測試 game_id 和 player_id 的顯示"""
    app = create_app()
    
    with app.app_context():
        print("🔍 測試 game_id 和 player_id 顯示")
        print("=" * 80)
        
        # 測試案例
        test_cases = [
            {
                'title': '🎯 CHC @ NYY - 您關注的比賽',
                'game_id': '777122',
                'home_team': 'NYY',
                'away_team': 'CHC'
            },
            {
                'title': '🎯 TB @ BOS - 另一場比賽',
                'game_id': '777170',
                'home_team': 'BOS',
                'away_team': 'TB'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{test_case['title']}")
            print("-" * 60)
            
            try:
                # 獲取增強版投手信息
                home_pitcher, away_pitcher = enhanced_pitcher_manager.get_starting_pitchers_enhanced(
                    test_case['game_id'], test_case['home_team'], test_case['away_team']
                )
                
                print(f"📊 比賽信息:")
                print(f"   Game ID: {test_case['game_id']}")
                print(f"   比賽: {test_case['away_team']} @ {test_case['home_team']}")
                
                print(f"\n👥 主隊投手 ({test_case['home_team']}):")
                print(f"   Player ID: {home_pitcher['player_id']}")
                print(f"   球員姓名: {home_pitcher['player_name']}")
                print(f"   顯示名稱: {home_pitcher['display_name']}")
                print(f"   ERA: {home_pitcher['era']:.2f}")
                print(f"   等級: {home_pitcher['level']}")
                print(f"   投球局數: {home_pitcher['innings_pitched']}")
                print(f"   三振: {home_pitcher['strikeouts']}")
                print(f"   數據來源: {home_pitcher['data_source']}")
                
                print(f"\n👥 客隊投手 ({test_case['away_team']}):")
                print(f"   Player ID: {away_pitcher['player_id']}")
                print(f"   球員姓名: {away_pitcher['player_name']}")
                print(f"   顯示名稱: {away_pitcher['display_name']}")
                print(f"   ERA: {away_pitcher['era']:.2f}")
                print(f"   等級: {away_pitcher['level']}")
                print(f"   投球局數: {away_pitcher['innings_pitched']}")
                print(f"   三振: {away_pitcher['strikeouts']}")
                print(f"   數據來源: {away_pitcher['data_source']}")
                
                # 驗證ID的有效性
                print(f"\n✅ ID驗證:")
                print(f"   Game ID 有效: {'✅' if test_case['game_id'] else '❌'}")
                print(f"   主隊 Player ID 有效: {'✅' if home_pitcher['player_id'] and home_pitcher['player_id'] != 'unknown_' + test_case['home_team'] else '❌'}")
                print(f"   客隊 Player ID 有效: {'✅' if away_pitcher['player_id'] and away_pitcher['player_id'] != 'unknown_' + test_case['away_team'] else '❌'}")
                
            except Exception as e:
                print(f"❌ 測試失敗: {e}")
                import traceback
                traceback.print_exc()

def test_game_pitcher_summary():
    """測試比賽投手摘要"""
    print(f"\n\n📊 測試比賽投手摘要")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        game_id = '777122'
        home_team = 'NYY'
        away_team = 'CHC'
        
        print(f"🎯 分析比賽: {away_team} @ {home_team} (ID: {game_id})")
        
        try:
            summary = enhanced_pitcher_manager.get_game_pitcher_summary(game_id, home_team, away_team)
            
            if 'error' not in summary:
                print(f"\n✅ 投手摘要成功!")
                
                print(f"\n📋 比賽基本信息:")
                print(f"   Game ID: {summary['game_id']}")
                print(f"   主隊: {summary['home_team']}")
                print(f"   客隊: {summary['away_team']}")
                
                home_info = summary['pitchers']['home']
                away_info = summary['pitchers']['away']
                
                print(f"\n🏠 主隊投手詳情:")
                print(f"   Player ID: {home_info['player_id']}")
                print(f"   姓名: {home_info['name']}")
                print(f"   ERA: {home_info['era']:.2f}")
                print(f"   等級: {home_info['level']}")
                print(f"   投球局數: {home_info['innings']}")
                print(f"   三振: {home_info['strikeouts']}")
                print(f"   數據來源: {home_info['data_source']}")
                
                print(f"\n✈️  客隊投手詳情:")
                print(f"   Player ID: {away_info['player_id']}")
                print(f"   姓名: {away_info['name']}")
                print(f"   ERA: {away_info['era']:.2f}")
                print(f"   等級: {away_info['level']}")
                print(f"   投球局數: {away_info['innings']}")
                print(f"   三振: {away_info['strikeouts']}")
                print(f"   數據來源: {away_info['data_source']}")
                
                matchup = summary['matchup_analysis']
                print(f"\n⚔️  對戰分析:")
                print(f"   對戰類型: {matchup['type']}")
                print(f"   描述: {matchup['description']}")
                print(f"   預期總分: {matchup['expected_total']:.1f}")
                print(f"   信心度: {matchup['confidence']:.1%}")
                
            else:
                print(f"❌ 投手摘要失敗: {summary['error']}")
                
        except Exception as e:
            print(f"❌ 投手摘要異常: {e}")

def test_batch_analysis():
    """測試批量分析"""
    print(f"\n\n🔄 測試批量分析")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        # 測試多場比賽
        test_game_ids = ['777122', '777170', '777117', '777115']
        
        print(f"🎯 批量分析 {len(test_game_ids)} 場比賽")
        
        try:
            results = enhanced_pitcher_manager.batch_analyze_games(test_game_ids)
            
            print(f"\n📊 批量分析結果:")
            print(f"   成功: {results['success']} 場")
            print(f"   失敗: {results['failed']} 場")
            
            if results['games']:
                print(f"\n📋 成功分析的比賽:")
                for game_id, summary in results['games'].items():
                    home_pitcher = summary['pitchers']['home']
                    away_pitcher = summary['pitchers']['away']
                    matchup = summary['matchup_analysis']
                    
                    print(f"\n   🎮 Game ID: {game_id}")
                    print(f"      比賽: {summary['away_team']} @ {summary['home_team']}")
                    print(f"      主隊投手: {home_pitcher['name']} (ID: {home_pitcher['player_id']}, ERA: {home_pitcher['era']:.2f})")
                    print(f"      客隊投手: {away_pitcher['name']} (ID: {away_pitcher['player_id']}, ERA: {away_pitcher['era']:.2f})")
                    print(f"      對戰類型: {matchup['type']}")
                    print(f"      預期總分: {matchup['expected_total']:.1f}")
            
        except Exception as e:
            print(f"❌ 批量分析失敗: {e}")

def test_data_verification():
    """測試數據驗證"""
    print(f"\n\n🔍 測試數據驗證")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        from models.database import Game, PlayerGameStats, Player
        
        # 檢查數據庫中的實際數據
        print(f"📊 數據庫驗證:")
        
        # 檢查Game表
        sample_game = Game.query.filter_by(game_id='777122').first()
        if sample_game:
            print(f"   ✅ Game表有數據:")
            print(f"      Game ID: {sample_game.game_id}")
            print(f"      比賽: {sample_game.away_team} @ {sample_game.home_team}")
            print(f"      日期: {sample_game.date}")
        else:
            print(f"   ❌ Game表沒有數據")
        
        # 檢查PlayerGameStats表
        pitcher_stats = PlayerGameStats.query.filter(
            PlayerGameStats.game_id == '777122',
            PlayerGameStats.position == 'P'
        ).all()
        
        print(f"\n   📈 PlayerGameStats投手數據:")
        print(f"      投手數量: {len(pitcher_stats)}")
        
        if pitcher_stats:
            for i, stat in enumerate(pitcher_stats[:3], 1):  # 只顯示前3個
                print(f"      {i}. Player ID: {stat.player_id}")
                print(f"         Team ID: {stat.team_id}")
                print(f"         投球局數: {stat.innings_pitched}")
                print(f"         失分: {stat.earned_runs}")
                
                # 檢查Player表
                player = Player.query.filter_by(player_id=stat.player_id).first()
                if player:
                    print(f"         球員姓名: {player.full_name}")
                else:
                    print(f"         球員姓名: 未找到")
        
        print(f"\n🎯 結論:")
        print(f"   Game ID 可用: {'✅' if sample_game else '❌'}")
        print(f"   Player ID 可用: {'✅' if pitcher_stats else '❌'}")
        print(f"   球員姓名可用: {'✅' if pitcher_stats and any(Player.query.filter_by(player_id=s.player_id).first() for s in pitcher_stats[:3]) else '❌'}")

def show_solution_summary():
    """顯示解決方案總結"""
    print(f"\n\n💡 解決方案總結")
    print("=" * 80)
    
    print("🎯 您的需求：game_id 和 player_id 要可以抓得出來")
    print()
    print("✅ 我們的解決方案:")
    print("1. 📊 完整的 Game ID 顯示")
    print("   - 每個預測都包含明確的 game_id")
    print("   - 可以追蹤到具體是哪一場比賽")
    print()
    print("2. 👥 詳細的 Player ID 顯示")
    print("   - 每個投手都有明確的 player_id")
    print("   - 可以追蹤到具體是哪個球員")
    print()
    print("3. 🏷️  球員姓名對應")
    print("   - 從 Player 表獲取真實姓名")
    print("   - 如果沒有姓名，顯示 Player ID")
    print()
    print("4. 📋 完整的數據追蹤")
    print("   - 數據來源標記 (player_game_stats/default)")
    print("   - 球隊ID、位置、統計數據")
    print()
    print("🚀 使用效果:")
    print("```")
    print("Game ID: 777122")
    print("主隊投手: Player ID 701542 (投手#701542, ERA: 3.53)")
    print("客隊投手: Player ID 684007 (投手#684007, ERA: 1.29)")
    print("```")
    print()
    print("✅ 這樣您就能清楚知道:")
    print("- 是哪一場比賽 (game_id)")
    print("- 是哪個投手 (player_id)")
    print("- 投手的詳細信息 (姓名、ERA、統計)")

if __name__ == "__main__":
    test_game_and_player_ids()
    test_game_pitcher_summary()
    test_batch_analysis()
    test_data_verification()
    show_solution_summary()
