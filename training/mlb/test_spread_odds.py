#!/usr/bin/env python3
"""
測試讓分盤口數據獲取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.real_betting_odds_fetcher import RealBettingOddsFetcher

def test_spread_odds_retrieval():
    """測試讓分盤口數據獲取"""
    
    print("🎯 測試讓分盤口數據獲取功能")
    print("=" * 60)
    
    # 創建應用
    app = create_app()
    
    # 創建博彩盤口獲取器
    odds_fetcher = RealBettingOddsFetcher(app)
    
    # 測試2025-07-07的比賽（注意：需要修改獲取邏輯以支持歷史日期）
    test_games = [
        ("SD", "AZ"),      # 聖地牙哥 vs 響尾蛇
        ("HOU", "CLE"),    # 休士頓 vs 守護者
        ("BOS", "COL"),    # 波士頓 vs 洛磯
        ("MIL", "LAD"),    # 釀酒人 vs 道奇
        ("CIN", "MIA"),    # 辛辛那提 vs 馬林魚
    ]

    target_date = date(2025, 7, 7)  # 指定測試日期
    
    print("測試比賽讓分盤口數據獲取：")
    print("-" * 60)
    
    for home_team, away_team in test_games:
        print(f"\n🏟️  {away_team} @ {home_team}")
        
        try:
            # 獲取博彩盤口數據
            odds_data = odds_fetcher.get_game_odds_by_teams(home_team, away_team, target_date)
            
            if odds_data:
                print(f"   ✅ 成功獲取博彩盤口數據")
                
                # 檢查大小分數據
                if 'totals' in odds_data:
                    totals = odds_data['totals']
                    print(f"   📊 大小分: {totals.get('total_point', 'N/A')}")
                    print(f"       Over: {totals.get('over_odds', 'N/A')}")
                    print(f"       Under: {totals.get('under_odds', 'N/A')}")
                else:
                    print(f"   ⚠️  缺少大小分數據")
                
                # 檢查讓分盤數據
                if 'run_line' in odds_data:
                    run_line = odds_data['run_line']
                    print(f"   🎯 讓分盤:")
                    print(f"       主隊: {run_line.get('home_line', 'N/A'):+.1f} ({run_line.get('home_odds', 'N/A')})")
                    print(f"       客隊: {run_line.get('away_line', 'N/A'):+.1f} ({run_line.get('away_odds', 'N/A')})")
                    print(f"       數據源: {run_line.get('bookmaker', 'Unknown')}")
                else:
                    print(f"   ❌ 缺少讓分盤數據")
                    
            else:
                print(f"   ❌ 無法獲取博彩盤口數據")
                
        except Exception as e:
            print(f"   ❌ 獲取失敗: {e}")
    
    print(f"\n" + "=" * 60)
    print("🎉 讓分盤口數據測試完成！")

if __name__ == "__main__":
    test_spread_odds_retrieval()
