#!/usr/bin/env python3
"""
分析預測準確率問題
找出影響預測質量的關鍵因素
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from datetime import date, timedelta
import pandas as pd
import numpy as np

def analyze_prediction_accuracy():
    """分析預測準確率問題"""
    print("🔍 MLB預測準確率深度分析")
    print("=" * 60)
    
    conn = sqlite3.connect('instance/mlb_data.db')
    
    # 1. 檢查最近預測的準確率
    print("\n📊 最近預測準確率分析:")
    print("-" * 40)
    
    query = """
    SELECT 
        p.game_id,
        g.date,
        g.home_team,
        g.away_team,
        g.home_score,
        g.away_score,
        p.predicted_home_score,
        p.predicted_away_score,
        p.home_win_probability,
        p.away_win_probability,
        p.confidence,
        p.is_correct,
        p.score_difference
    FROM predictions p
    JOIN games g ON p.game_id = g.game_id
    WHERE g.game_status = 'completed' 
    AND g.home_score IS NOT NULL 
    AND g.away_score IS NOT NULL
    AND p.is_correct IS NOT NULL
    ORDER BY g.date DESC
    LIMIT 50
    """
    
    df = pd.read_sql_query(query, conn)
    
    if len(df) > 0:
        accuracy = (df['is_correct'].sum() / len(df)) * 100
        avg_confidence = df['confidence'].mean()
        avg_score_error = df['score_difference'].mean()
        
        print(f"最近50場預測:")
        print(f"  準確率: {accuracy:.1f}%")
        print(f"  平均信心度: {avg_confidence:.3f}")
        print(f"  平均得分誤差: {avg_score_error:.2f}")
        
        # 分析錯誤預測
        wrong_predictions = df[df['is_correct'] == 0]
        if len(wrong_predictions) > 0:
            print(f"\n❌ 錯誤預測分析 ({len(wrong_predictions)}場):")
            
            # 檢查是否有明顯的預測偏差
            home_score_bias = (wrong_predictions['predicted_home_score'] - wrong_predictions['home_score']).mean()
            away_score_bias = (wrong_predictions['predicted_away_score'] - wrong_predictions['away_score']).mean()
            
            print(f"  主隊得分偏差: {home_score_bias:+.2f}")
            print(f"  客隊得分偏差: {away_score_bias:+.2f}")
            
            # 顯示幾個典型錯誤案例
            print(f"\n典型錯誤案例:")
            for _, row in wrong_predictions.head(5).iterrows():
                actual_winner = "主隊" if row['home_score'] > row['away_score'] else "客隊"
                predicted_winner = "主隊" if row['home_win_probability'] > row['away_win_probability'] else "客隊"
                print(f"  {row['date']} {row['away_team']}@{row['home_team']}")
                print(f"    實際: {row['away_score']}-{row['home_score']} ({actual_winner}勝)")
                print(f"    預測: {row['predicted_away_score']:.1f}-{row['predicted_home_score']:.1f} ({predicted_winner}勝)")
                print(f"    信心度: {row['confidence']:.3f}")
    else:
        print("❌ 沒有找到已驗證的預測數據")
    
    # 2. 檢查數據質量問題
    print(f"\n🔧 數據質量問題分析:")
    print("-" * 40)
    
    # 檢查缺少分數的比賽
    cursor = conn.cursor()
    cursor.execute("""
        SELECT COUNT(*) FROM games 
        WHERE game_status = 'completed' AND (home_score IS NULL OR away_score IS NULL)
    """)
    missing_scores = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM games WHERE game_status = 'completed'")
    total_completed = cursor.fetchone()[0]
    
    missing_percentage = (missing_scores / total_completed) * 100 if total_completed > 0 else 0
    
    print(f"缺少分數的已完成比賽: {missing_scores}/{total_completed} ({missing_percentage:.1f}%)")
    
    if missing_percentage > 10:
        print("⚠️  警告: 大量已完成比賽缺少分數，這會嚴重影響模型訓練")
    
    # 3. 檢查特徵數據質量
    print(f"\n📈 特徵數據質量分析:")
    print("-" * 40)
    
    # 檢查球隊統計數據
    cursor.execute("SELECT COUNT(*) FROM team_stats")
    team_stats_count = cursor.fetchone()[0]
    print(f"球隊統計記錄: {team_stats_count}")
    
    # 檢查球員統計數據
    cursor.execute("SELECT COUNT(*) FROM player_stats")
    player_stats_count = cursor.fetchone()[0]
    print(f"球員統計記錄: {player_stats_count}")
    
    # 檢查Box Score數據
    cursor.execute("SELECT COUNT(*) FROM box_scores")
    boxscore_count = cursor.fetchone()[0]
    print(f"Box Score記錄: {boxscore_count}")
    
    # 4. 檢查模型特徵的有效性
    print(f"\n🤖 模型特徵有效性分析:")
    print("-" * 40)
    
    # 檢查預測分數的變異性
    cursor.execute("""
        SELECT 
            MIN(predicted_home_score) as min_home,
            MAX(predicted_home_score) as max_home,
            AVG(predicted_home_score) as avg_home,
            MIN(predicted_away_score) as min_away,
            MAX(predicted_away_score) as max_away,
            AVG(predicted_away_score) as avg_away,
            COUNT(DISTINCT ROUND(predicted_home_score, 1)) as unique_home_scores,
            COUNT(DISTINCT ROUND(predicted_away_score, 1)) as unique_away_scores
        FROM predictions
        WHERE predicted_home_score IS NOT NULL
    """)
    
    result = cursor.fetchone()
    if result:
        min_home, max_home, avg_home, min_away, max_away, avg_away, unique_home, unique_away = result
        
        print(f"主隊預測得分範圍: {min_home:.1f} - {max_home:.1f} (平均: {avg_home:.1f})")
        print(f"客隊預測得分範圍: {min_away:.1f} - {max_away:.1f} (平均: {avg_away:.1f})")
        print(f"主隊得分種類: {unique_home}")
        print(f"客隊得分種類: {unique_away}")
        
        if unique_home <= 5 or unique_away <= 5:
            print("⚠️  警告: 預測分數變異性太低，模型可能過於簡化")
    
    # 5. 建議改進措施
    print(f"\n💡 改進建議:")
    print("-" * 40)
    
    suggestions = []
    
    if missing_percentage > 10:
        suggestions.append("1. 修復缺少分數的已完成比賽數據")
    
    if len(df) > 0 and accuracy < 60:
        suggestions.append("2. 重新訓練預測模型，加入更多特徵")
    
    if result and (unique_home <= 5 or unique_away <= 5):
        suggestions.append("3. 增加模型複雜度，提高預測分數的變異性")
    
    if team_stats_count < 30:
        suggestions.append("4. 更新球隊統計數據")
    
    if player_stats_count < 500:
        suggestions.append("5. 補充球員統計數據")
    
    suggestions.append("6. 加入更多實時數據源（傷病、天氣、投手對戰等）")
    suggestions.append("7. 實施集成學習方法，結合多個模型")
    suggestions.append("8. 加入博彩市場數據作為特徵")
    
    for suggestion in suggestions:
        print(f"  {suggestion}")
    
    conn.close()
    
    return {
        'accuracy': accuracy if len(df) > 0 else 0,
        'missing_scores_percentage': missing_percentage,
        'suggestions': suggestions
    }

if __name__ == "__main__":
    analyze_prediction_accuracy()
