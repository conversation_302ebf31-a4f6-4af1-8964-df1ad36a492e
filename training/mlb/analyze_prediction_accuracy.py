#!/usr/bin/env python3
"""
分析預測準確性
找出預測偏差大和接近的模式，為算法改良提供依據
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
from app import create_app
from models.database import db, PredictionHistory, Game
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from typing import Dict, List, Tuple, Any
import json

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PredictionAccuracyAnalyzer:
    """預測準確性分析器"""
    
    def __init__(self):
        self.app = create_app()
        self.analysis_results = {}
    
    def analyze_prediction_accuracy(self, days_back: int = 30) -> Dict:
        """分析預測準確性"""
        with self.app.app_context():
            print(f"🔍 分析過去 {days_back} 天的預測準確性...")
            
            # 獲取有實際結果的預測記錄
            end_date = date.today()
            start_date = end_date - timedelta(days=days_back)
            
            # 查詢預測記錄和對應的實際比賽結果
            query = db.session.query(PredictionHistory, Game).join(
                Game, PredictionHistory.game_id == Game.game_id
            ).filter(
                PredictionHistory.prediction_date >= start_date,
                PredictionHistory.prediction_date <= end_date,
                PredictionHistory.actual_away_score.isnot(None),
                PredictionHistory.actual_home_score.isnot(None),
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            print(f"   找到 {len(query)} 條有完整數據的預測記錄")
            
            if not query:
                return {"error": "沒有找到有完整數據的預測記錄"}
            
            # 準備分析數據
            analysis_data = []
            
            for pred, game in query:
                # 預測比分
                pred_home = float(pred.predicted_home_score)
                pred_away = float(pred.predicted_away_score) 
                pred_total = pred_home + pred_away
                
                # 實際比分
                actual_home = int(game.home_score)
                actual_away = int(game.away_score)
                actual_total = actual_home + actual_away
                
                # 計算偏差
                home_error = abs(pred_home - actual_home)
                away_error = abs(pred_away - actual_away)
                total_error = abs(pred_total - actual_total)
                
                # 預測勝負正確性
                pred_winner = 'home' if pred_home > pred_away else 'away'
                actual_winner = 'home' if actual_home > actual_away else 'away'
                winner_correct = pred_winner == actual_winner
                
                data_point = {
                    'game_id': pred.game_id,
                    'date': pred.prediction_date,
                    'pred_home': pred_home,
                    'pred_away': pred_away,
                    'pred_total': pred_total,
                    'actual_home': actual_home,
                    'actual_away': actual_away,
                    'actual_total': actual_total,
                    'home_error': home_error,
                    'away_error': away_error,
                    'total_error': total_error,
                    'avg_score_error': (home_error + away_error) / 2,
                    'winner_correct': winner_correct,
                    'confidence': float(pred.overall_confidence) if pred.overall_confidence else 0.0,
                    'home_team': game.home_team,
                    'away_team': game.away_team
                }
                analysis_data.append(data_point)
            
            df = pd.DataFrame(analysis_data)
            
            # 進行詳細分析
            accuracy_analysis = self._analyze_accuracy_patterns(df)
            error_analysis = self._analyze_error_patterns(df)
            team_analysis = self._analyze_team_patterns(df)
            confidence_analysis = self._analyze_confidence_patterns(df)
            
            # 綜合分析結果
            results = {
                'summary': {
                    'total_predictions': len(df),
                    'date_range': f"{start_date} to {end_date}",
                    'avg_home_error': df['home_error'].mean(),
                    'avg_away_error': df['away_error'].mean(),
                    'avg_total_error': df['total_error'].mean(),
                    'winner_accuracy': df['winner_correct'].mean() * 100,
                    'median_total_error': df['total_error'].median(),
                    'std_total_error': df['total_error'].std()
                },
                'accuracy_patterns': accuracy_analysis,
                'error_patterns': error_analysis,
                'team_patterns': team_analysis,
                'confidence_patterns': confidence_analysis,
                'recommendations': self._generate_recommendations(df)
            }
            
            self.analysis_results = results
            return results
    
    def _analyze_accuracy_patterns(self, df: pd.DataFrame) -> Dict:
        """分析準確性模式"""
        
        # 按錯誤範圍分類
        df['error_category'] = pd.cut(df['total_error'], 
                                    bins=[0, 2, 4, 6, 10, float('inf')],
                                    labels=['很準確(<2分)', '準確(2-4分)', '一般(4-6分)', '偏差大(6-10分)', '很偏差(>10分)'])
        
        error_distribution = df['error_category'].value_counts()
        
        # 找出最準確和最偏差的預測
        most_accurate = df.nsmallest(10, 'total_error')[['game_id', 'date', 'home_team', 'away_team', 
                                                        'pred_total', 'actual_total', 'total_error']].to_dict('records')
        
        most_inaccurate = df.nlargest(10, 'total_error')[['game_id', 'date', 'home_team', 'away_team', 
                                                         'pred_total', 'actual_total', 'total_error']].to_dict('records')
        
        return {
            'error_distribution': error_distribution.to_dict(),
            'most_accurate_predictions': most_accurate,
            'most_inaccurate_predictions': most_inaccurate,
            'accuracy_by_total_score': self._analyze_by_total_score(df)
        }
    
    def _analyze_by_total_score(self, df: pd.DataFrame) -> Dict:
        """按總分範圍分析準確性"""
        df['total_score_range'] = pd.cut(df['actual_total'], 
                                       bins=[0, 6, 8, 10, 12, float('inf')],
                                       labels=['低分(≤6)', '中低分(7-8)', '中分(9-10)', '中高分(11-12)', '高分(>12)'])
        
        by_total = df.groupby('total_score_range').agg({
            'total_error': ['mean', 'std', 'count'],
            'winner_correct': 'mean'
        }).round(2)
        
        return by_total.to_dict()
    
    def _analyze_error_patterns(self, df: pd.DataFrame) -> Dict:
        """分析錯誤模式"""
        
        # 預測偏向分析
        df['home_bias'] = df['pred_home'] - df['actual_home']
        df['away_bias'] = df['pred_away'] - df['actual_away'] 
        df['total_bias'] = df['pred_total'] - df['actual_total']
        
        return {
            'bias_analysis': {
                'avg_home_bias': df['home_bias'].mean(),
                'avg_away_bias': df['away_bias'].mean(),
                'avg_total_bias': df['total_bias'].mean(),
                'home_overpredict_pct': (df['home_bias'] > 0).mean() * 100,
                'away_overpredict_pct': (df['away_bias'] > 0).mean() * 100,
                'total_overpredict_pct': (df['total_bias'] > 0).mean() * 100
            },
            'error_correlations': {
                'home_away_error_corr': df['home_error'].corr(df['away_error']),
                'confidence_error_corr': df['confidence'].corr(df['total_error']),
            }
        }
    
    def _analyze_team_patterns(self, df: pd.DataFrame) -> Dict:
        """分析球隊相關模式"""
        
        # 按主隊統計
        home_team_stats = df.groupby('home_team').agg({
            'total_error': 'mean',
            'winner_correct': 'mean',
            'game_id': 'count'
        }).rename(columns={'game_id': 'count'}).round(3)
        
        # 只顯示有足夠樣本的球隊
        home_team_stats = home_team_stats[home_team_stats['count'] >= 3]
        
        # 按客隊統計  
        away_team_stats = df.groupby('away_team').agg({
            'total_error': 'mean',
            'winner_correct': 'mean',
            'game_id': 'count'
        }).rename(columns={'game_id': 'count'}).round(3)
        
        away_team_stats = away_team_stats[away_team_stats['count'] >= 3]
        
        return {
            'best_predicted_home_teams': home_team_stats.nsmallest(5, 'total_error').to_dict(),
            'worst_predicted_home_teams': home_team_stats.nlargest(5, 'total_error').to_dict(),
            'best_predicted_away_teams': away_team_stats.nsmallest(5, 'total_error').to_dict(),
            'worst_predicted_away_teams': away_team_stats.nlargest(5, 'total_error').to_dict()
        }
    
    def _analyze_confidence_patterns(self, df: pd.DataFrame) -> Dict:
        """分析信心度模式"""
        
        # 按信心度分組
        df['confidence_range'] = pd.cut(df['confidence'], 
                                      bins=[0, 0.6, 0.7, 0.8, 0.9, 1.0],
                                      labels=['低信心(≤0.6)', '中低信心(0.6-0.7)', '中信心(0.7-0.8)', '中高信心(0.8-0.9)', '高信心(0.9-1.0)'])
        
        confidence_stats = df.groupby('confidence_range').agg({
            'total_error': ['mean', 'std'],
            'winner_correct': 'mean',
            'game_id': 'count'
        }).round(3)
        
        return {
            'confidence_vs_accuracy': confidence_stats.to_dict(),
            'confidence_distribution': df['confidence_range'].value_counts().to_dict()
        }
    
    def _generate_recommendations(self, df: pd.DataFrame) -> List[str]:
        """生成改良建議"""
        recommendations = []
        
        # 基於分析結果生成建議
        if df['total_error'].mean() > 3:
            recommendations.append("整體預測誤差較大，建議加強特徵工程和模型調優")
        
        if (df['total_bias'] > 0).mean() > 0.6:
            recommendations.append("預測總分普遍偏高，建議調整模型偏向")
        elif (df['total_bias'] < 0).mean() > 0.6:
            recommendations.append("預測總分普遍偏低，建議調整模型偏向")
        
        if df['winner_correct'].mean() < 0.55:
            recommendations.append("勝負預測準確率偏低，建議改進勝負判斷邏輯")
        
        # 信心度相關建議
        high_conf_low_acc = df[(df['confidence'] > 0.8) & (df['total_error'] > 4)]
        if len(high_conf_low_acc) > len(df) * 0.2:
            recommendations.append("高信心度預測的準確性不佳，需要重新校準信心度計算")
        
        return recommendations
    
    def save_analysis_report(self, filepath: str = None):
        """保存分析報告"""
        if not filepath:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filepath = f"prediction_accuracy_analysis_{timestamp}.json"
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 分析報告已保存至: {filepath}")
    
    def print_summary(self):
        """打印分析摘要"""
        if not self.analysis_results:
            print("❌ 請先執行 analyze_prediction_accuracy() 方法")
            return
            
        print("\n" + "="*80)
        print("🎯 MLB預測準確性分析報告")
        print("="*80)
        
        summary = self.analysis_results['summary']
        print(f"\n📊 基本統計:")
        print(f"   總預測數量: {summary['total_predictions']}")
        print(f"   分析期間: {summary['date_range']}")
        print(f"   平均主隊誤差: {summary['avg_home_error']:.2f} 分")
        print(f"   平均客隊誤差: {summary['avg_away_error']:.2f} 分") 
        print(f"   平均總分誤差: {summary['avg_total_error']:.2f} 分")
        print(f"   勝負預測準確率: {summary['winner_accuracy']:.1f}%")
        print(f"   總分誤差中位數: {summary['median_total_error']:.2f} 分")
        
        print(f"\n🎯 準確性分布:")
        error_dist = self.analysis_results['accuracy_patterns']['error_distribution']
        for category, count in error_dist.items():
            percentage = count / summary['total_predictions'] * 100
            print(f"   {category}: {count} 場 ({percentage:.1f}%)")
        
        print(f"\n💡 改良建議:")
        for i, rec in enumerate(self.analysis_results['recommendations'], 1):
            print(f"   {i}. {rec}")
        
        print("\n" + "="*80)

def main():
    """主函數"""
    analyzer = PredictionAccuracyAnalyzer()
    
    # 分析過去30天的預測準確性
    results = analyzer.analyze_prediction_accuracy(days_back=30)
    
    if 'error' in results:
        print(f"❌ 分析失敗: {results['error']}")
        return
    
    # 打印摘要
    analyzer.print_summary()
    
    # 保存詳細報告
    analyzer.save_analysis_report()
    
    print("\n🔍 詳細分析完成！接下來將基於這些發現設計更好的預測算法...")

if __name__ == "__main__":
    main()