#!/usr/bin/env python3
"""
測試球隊名稱映射修復
"""

import sys
import os
from datetime import date

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game
from models.odds_data_fetcher import OddsDataFetcher

def test_team_mapping():
    """測試球隊名稱映射"""
    print("🔧 測試球隊名稱映射修復")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        fetcher = OddsDataFetcher(app)
        
        # 1. 檢查映射表
        team_mapping = fetcher._create_team_mapping()
        print(f"📋 球隊映射表 ({len(team_mapping)} 個映射):")
        
        # 顯示部分映射
        sample_mappings = list(team_mapping.items())[:10]
        for api_name, db_code in sample_mappings:
            print(f"   '{api_name}' -> '{db_code}'")
        print("   ...")
        
        # 2. 檢查數據庫中的球隊縮寫
        print(f"\n📊 數據庫中的球隊縮寫:")
        db_teams = set()
        recent_games = Game.query.filter(
            Game.date >= date(2025, 6, 20)
        ).limit(50).all()
        
        for game in recent_games:
            db_teams.add(game.home_team)
            db_teams.add(game.away_team)
        
        db_teams_sorted = sorted(list(db_teams))
        print(f"   發現 {len(db_teams_sorted)} 個唯一球隊縮寫:")
        for team in db_teams_sorted:
            print(f"     - {team}")
        
        # 3. 檢查映射覆蓋率
        print(f"\n🎯 映射覆蓋率分析:")
        mapped_values = set(team_mapping.values())
        
        covered_teams = db_teams & mapped_values
        missing_teams = db_teams - mapped_values
        
        print(f"   數據庫球隊: {len(db_teams)} 個")
        print(f"   映射覆蓋: {len(covered_teams)} 個")
        print(f"   缺少映射: {len(missing_teams)} 個")
        print(f"   覆蓋率: {len(covered_teams)/len(db_teams)*100:.1f}%")
        
        if missing_teams:
            print(f"\n❌ 缺少映射的球隊:")
            for team in sorted(missing_teams):
                print(f"     - {team}")
        
        # 4. 模擬API數據測試匹配
        print(f"\n🧪 模擬匹配測試:")
        
        # 創建模擬的API賠率數據
        mock_api_data = [
            {
                'id': 'test1',
                'home_team': 'Cleveland Guardians',
                'away_team': 'St. Louis Cardinals',
                'commence_time': '2025-06-29T19:00:00Z',
                'bookmakers': []
            },
            {
                'id': 'test2', 
                'home_team': 'Pittsburgh Pirates',
                'away_team': 'New York Mets',
                'commence_time': '2025-06-29T19:00:00Z',
                'bookmakers': []
            }
        ]
        
        # 測試匹配
        test_games = Game.query.filter_by(date=date(2025, 6, 29)).limit(5).all()
        
        print(f"   測試 {len(test_games)} 場真實比賽 vs {len(mock_api_data)} 場模擬API數據:")
        
        for game in test_games:
            print(f"\n   🏟️  數據庫比賽: {game.away_team} @ {game.home_team}")
            
            # 嘗試匹配
            matched_odds = fetcher._find_odds_for_game(game, mock_api_data, team_mapping)
            
            if matched_odds:
                print(f"     ✅ 找到匹配的賠率數據")
            else:
                print(f"     ❌ 沒有找到匹配的賠率數據")
                
                # 檢查為什麼沒匹配
                print(f"     🔍 查找 '{game.home_team}' 和 '{game.away_team}' 的API名稱:")
                
                # 反向查找
                api_home_names = [api_name for api_name, db_code in team_mapping.items() if db_code == game.home_team]
                api_away_names = [api_name for api_name, db_code in team_mapping.items() if db_code == game.away_team]
                
                print(f"       主隊 {game.home_team} 對應API名稱: {api_home_names}")
                print(f"       客隊 {game.away_team} 對應API名稱: {api_away_names}")

def add_missing_mappings():
    """添加缺少的球隊映射"""
    print(f"\n🔧 需要添加的映射建議:")
    
    # 常見的缺少映射
    missing_mappings = {
        'ATH': 'Oakland Athletics',  # 特殊情況
        # 可能需要添加更多
    }
    
    for db_code, suggested_api_name in missing_mappings.items():
        print(f"   '{suggested_api_name}': '{db_code}',")

if __name__ == "__main__":
    test_team_mapping()
    add_missing_mappings()
