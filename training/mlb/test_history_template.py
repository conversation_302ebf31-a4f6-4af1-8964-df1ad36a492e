#!/usr/bin/env python3
"""
測試歷史模板是否可以正常工作
"""

import requests
import sys

def test_history_page():
    """測試歷史頁面是否可以正常訪問"""
    try:
        # 測試歷史頁面
        response = requests.get('http://127.0.0.1:5500/unified/history', timeout=10)
        
        print(f"歷史頁面狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 歷史頁面成功載入！")
            print(f"頁面內容長度: {len(response.text)} 字符")
            
            # 檢查關鍵內容
            if "預測歷史記錄" in response.text:
                print("✅ 頁面標題正確")
            else:
                print("❌ 頁面標題缺失")
                
            if "時間範圍選擇" in response.text:
                print("✅ 時間範圍選擇功能存在")
            else:
                print("❌ 時間範圍選擇功能缺失")
                
            if "統計摘要" in response.text or "總預測數" in response.text:
                print("✅ 統計摘要部分存在")
            else:
                print("❌ 統計摘要部分缺失")
                
        elif response.status_code == 500:
            print("❌ 服務器內部錯誤 (500)")
            print("錯誤內容:")
            print(response.text[:500])
        else:
            print(f"❌ 意外的狀態碼: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到 Flask 應用程序")
        print("請確保 Flask 應用程序正在運行在 http://127.0.0.1:5500")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
        
    return response.status_code == 200

def test_other_unified_pages():
    """測試其他統一預測頁面"""
    pages = [
        ('/unified/', '統一預測主頁'),
        ('/unified/query', '查詢頁面'),
    ]
    
    for url, name in pages:
        try:
            response = requests.get(f'http://127.0.0.1:5500{url}', timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} 正常工作")
            else:
                print(f"❌ {name} 返回狀態碼: {response.status_code}")
        except Exception as e:
            print(f"❌ {name} 測試失敗: {e}")

if __name__ == "__main__":
    print("🧪 開始測試歷史模板...")
    print("=" * 50)
    
    # 測試歷史頁面
    success = test_history_page()
    
    print("\n" + "=" * 50)
    print("🧪 測試其他相關頁面...")
    
    # 測試其他頁面
    test_other_unified_pages()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 歷史模板測試成功！")
        sys.exit(0)
    else:
        print("💥 歷史模板測試失敗！")
        sys.exit(1)
