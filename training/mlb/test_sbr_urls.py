#!/usr/bin/env python3
"""
測試sportsbookreview的URL是否正確
"""

import requests
from datetime import date

def test_sbr_urls():
    """測試sportsbookreview的URL"""
    base_url = "https://www.sportsbookreview.com"
    test_date = "2025-07-07"
    
    # 測試URL
    urls = {
        "總分盤": f"{base_url}/betting-odds/mlb-baseball/totals/full-game/?date={test_date}",
        "讓分盤": f"{base_url}/betting-odds/mlb-baseball/pointspread/full-game/?date={test_date}",
        "錯誤讓分盤": f"{base_url}/betting-odds/mlb-baseball/spreads/full-game/?date={test_date}"
    }
    
    print(f"🧪 測試sportsbookreview URL - {test_date}")
    
    for name, url in urls.items():
        try:
            print(f"\n📡 測試 {name}:")
            print(f"  URL: {url}")
            
            response = requests.get(url, timeout=10)
            print(f"  狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                content_length = len(response.content)
                print(f"  內容長度: {content_length:,} bytes")
                
                # 檢查是否包含MLB相關內容
                text = response.text.lower()
                has_mlb = 'mlb' in text or 'baseball' in text
                has_games = 'game' in text or 'match' in text
                has_odds = 'odds' in text or 'line' in text
                
                print(f"  包含MLB: {'✅' if has_mlb else '❌'}")
                print(f"  包含比賽: {'✅' if has_games else '❌'}")
                print(f"  包含賠率: {'✅' if has_odds else '❌'}")
                
                if content_length > 50000:  # 正常頁面應該有足夠內容
                    print(f"  評估: ✅ 可能是有效頁面")
                else:
                    print(f"  評估: ⚠️  內容較少，可能是錯誤頁面")
            else:
                print(f"  評估: ❌ HTTP錯誤")
                
        except Exception as e:
            print(f"  錯誤: {e}")

if __name__ == "__main__":
    test_sbr_urls()
