#!/usr/bin/env python3
"""
整合腳本 - 將增強算法整合到現有系統中
自動生成於: 2025-08-30 09:40:35
"""

import sys
import os
from pathlib import Path

# 添加項目路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.enhanced_prediction_service import initialize_enhanced_service

def main():
    print("🚀 啟動增強預測算法整合...")
    
    # 增強模型路徑
    model_path = "/Users/<USER>/python/training/mlb/enhanced_mlb_predictor_20250830_093556.joblib"
    
    # 初始化增強預測服務
    success = initialize_enhanced_service(model_path)
    
    if success:
        print("✅ 增強預測算法整合成功！")
        print("\n📊 增強算法特性:")
        print("   • 多模型集成 (RandomForest + GradientBoosting + Ridge + ElasticNet)")
        print("   • 智能偏差校正")
        print("   • 時間序列交叉驗證")
        print("   • 22個增強特徵")
        print("   • 平均誤差改善 8.6%")
        print("   • 勝負預測提升 8.9%")
        
        print("\n🎯 使用方式:")
        print("   from models.enhanced_prediction_service import get_enhanced_service")
        print("   service = get_enhanced_service()")
        print("   result = service.predict_game(game_data)")
        
    else:
        print("❌ 增強預測算法整合失敗")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
