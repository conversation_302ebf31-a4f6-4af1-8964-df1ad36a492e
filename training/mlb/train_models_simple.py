#!/usr/bin/env python3
"""
簡單的模型訓練腳本
"""

from app import create_app
from models.model_trainer import ModelTrainer
from datetime import date, timedelta
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函數"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("🤖 開始訓練MLB預測模型...")
            
            trainer = ModelTrainer()
            
            # 使用最近3個月的數據進行訓練
            end_date = date.today() - timedelta(days=1)
            start_date = end_date - timedelta(days=90)
            
            logger.info(f"訓練數據範圍: {start_date} 到 {end_date}")
            
            result = trainer.train_full_pipeline(
                start_date=start_date,
                end_date=end_date,
                save_models=True
            )

            if result and result.get('training_samples', 0) > 0:
                logger.info("✅ 模型訓練成功完成！")
                logger.info(f"訓練數據量: {result.get('training_samples', 0)} 個樣本")
                logger.info(f"特徵數量: {result.get('features_count', 0)} 個")

                # 檢查評估結果
                eval_results = result.get('evaluation_results', {})
                if eval_results.get('win_prob_cv_accuracy'):
                    logger.info(f"勝負預測準確率: {eval_results['win_prob_cv_accuracy']:.3f}")

                # 測試模型載入
                from models.prediction_service import PredictionService
                service = PredictionService()
                if service.models_loaded:
                    logger.info("✅ 模型載入測試成功！")
                else:
                    logger.warning("⚠️ 模型載入測試失敗")

            else:
                logger.error(f"❌ 模型訓練失敗: 沒有有效的訓練結果")
                
        except Exception as e:
            logger.error(f"❌ 訓練過程發生錯誤: {e}")
            raise

if __name__ == '__main__':
    main()
