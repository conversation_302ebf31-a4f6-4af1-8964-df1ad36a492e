#!/usr/bin/env python3
"""
只測試sportsbookreview讓分盤抓取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from models.modern_sbr_scraper import ModernSB<PERSON><PERSON>raper

def test_sbr_spreads_only():
    """只測試讓分盤抓取"""
    try:
        # 測試日期
        test_date = date(2025, 7, 7)
        
        print(f"🧪 測試sportsbookreview讓分盤抓取 - {test_date}")
        
        # 創建抓取器
        scraper = ModernSBRScraper()

        # 設置WebDriver
        if not scraper._setup_driver():
            print("❌ WebDriver 設置失敗")
            return

        # 只測試讓分盤數據抓取
        date_str = test_date.strftime('%Y-%m-%d')
        spreads_data = scraper._fetch_spreads_data(date_str, 'bet365')
        
        print(f"🌐 讓分盤抓取結果:")
        print(f"  抓取到的比賽數: {len(spreads_data)}")
        
        if spreads_data:
            print(f"\n📋 讓分盤詳情:")
            
            for i, game in enumerate(spreads_data[:3]):  # 只顯示前3場
                print(f"\n  比賽 {i+1}:")
                print(f"    客隊: {game.get('away_team')}")
                print(f"    主隊: {game.get('home_team')}")
                print(f"    博彩商: {game.get('bookmaker')}")
                print(f"    市場類型: {game.get('market_type')}")
                
                spreads_data_detail = game.get('spreads_data', {})
                if spreads_data_detail:
                    print(f"    讓分盤詳情:")
                    print(f"      主隊讓分: {spreads_data_detail.get('home_spread_point')}")
                    print(f"      主隊賠率: {spreads_data_detail.get('home_spread_price')}")
                    print(f"      客隊讓分: {spreads_data_detail.get('away_spread_point')}")
                    print(f"      客隊賠率: {spreads_data_detail.get('away_spread_price')}")
                else:
                    print(f"    讓分盤詳情: ❌ 無數據")
        else:
            print("❌ 沒有抓取到讓分盤數據")
            
        # 關閉WebDriver
        if hasattr(scraper, 'driver') and scraper.driver:
            scraper.driver.quit()
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sbr_spreads_only()
