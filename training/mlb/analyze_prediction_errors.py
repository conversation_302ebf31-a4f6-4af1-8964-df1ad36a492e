#!/usr/bin/env python3
"""
深入分析預測誤差的原因
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction, BettingOdds
import numpy as np
import pandas as pd
from collections import defaultdict

def analyze_prediction_errors():
    """分析預測誤差的具體原因"""
    app = create_app()
    
    with app.app_context():
        print("🔍 深入分析預測誤差原因...")
        
        # 獲取詳細的預測數據
        query = """
        SELECT 
            g.game_id,
            g.date,
            g.home_team,
            g.away_team,
            g.home_score,
            g.away_score,
            (g.home_score + g.away_score) as actual_total,
            p.predicted_home_score,
            p.predicted_away_score,
            (p.predicted_home_score + p.predicted_away_score) as predicted_total,
            p.confidence,
            p.model_version,
            bo.total_point as betting_line,
            bo.bookmaker,
            ABS((p.predicted_home_score + p.predicted_away_score) - (g.home_score + g.away_score)) as total_error,
            ABS(p.predicted_home_score - g.home_score) as home_error,
            ABS(p.predicted_away_score - g.away_score) as away_error
        FROM games g
        JOIN predictions p ON g.game_id = p.game_id
        LEFT JOIN betting_odds bo ON g.game_id = bo.game_id
        WHERE g.home_score IS NOT NULL 
        AND g.away_score IS NOT NULL
        AND p.predicted_home_score IS NOT NULL
        AND p.predicted_away_score IS NOT NULL
        AND g.date >= DATE('now', '-30 days')
        ORDER BY total_error DESC
        """
        
        results = db.session.execute(db.text(query)).fetchall()
        
        if not results:
            print("❌ 沒有找到足夠的數據進行分析")
            return
        
        columns = ['game_id', 'date', 'home_team', 'away_team', 'home_score', 'away_score', 
                  'actual_total', 'predicted_home_score', 'predicted_away_score', 'predicted_total', 
                  'confidence', 'model_version', 'betting_line', 'bookmaker', 'total_error', 
                  'home_error', 'away_error']
        
        df = pd.DataFrame(results, columns=columns)
        
        print(f"📊 分析 {len(df)} 場比賽的預測誤差")
        
        # 1. 誤差分布分析
        analyze_error_distribution(df)
        
        # 2. 高誤差案例分析
        analyze_high_error_cases(df)
        
        # 3. 按隊伍分析誤差
        analyze_team_specific_errors(df)
        
        # 4. 按模型版本分析
        analyze_model_version_errors(df)
        
        # 5. 預測vs博彩盤口對比
        analyze_vs_betting_lines(df)
        
        # 6. 系統性偏差分析
        analyze_systematic_bias(df)

def analyze_error_distribution(df):
    """分析誤差分布"""
    print(f"\n📈 誤差分布分析:")
    
    print(f"  總分預測誤差:")
    print(f"    平均誤差: {df['total_error'].mean():.1f} 分")
    print(f"    中位數誤差: {df['total_error'].median():.1f} 分")
    print(f"    標準差: {df['total_error'].std():.1f}")
    print(f"    最大誤差: {df['total_error'].max():.1f} 分")
    
    print(f"\n  主隊得分預測誤差:")
    print(f"    平均誤差: {df['home_error'].mean():.1f} 分")
    print(f"    中位數誤差: {df['home_error'].median():.1f} 分")
    
    print(f"\n  客隊得分預測誤差:")
    print(f"    平均誤差: {df['away_error'].mean():.1f} 分")
    print(f"    中位數誤差: {df['away_error'].median():.1f} 分")
    
    # 誤差範圍分布
    error_ranges = [
        (0, 2, "很準確"),
        (2, 4, "準確"), 
        (4, 6, "一般"),
        (6, 8, "較差"),
        (8, float('inf'), "很差")
    ]
    
    print(f"\n  誤差範圍分布:")
    for min_err, max_err, label in error_ranges:
        if max_err == float('inf'):
            count = (df['total_error'] >= min_err).sum()
        else:
            count = ((df['total_error'] >= min_err) & (df['total_error'] < max_err)).sum()
        
        percentage = count / len(df) * 100
        print(f"    {label} ({min_err}-{max_err if max_err != float('inf') else '∞'}分): {percentage:.1f}% ({count}/{len(df)})")

def analyze_high_error_cases(df):
    """分析高誤差案例"""
    print(f"\n🚨 高誤差案例分析 (誤差 > 8分):")
    
    high_error_cases = df[df['total_error'] > 8].copy()
    
    if len(high_error_cases) == 0:
        print("  ✅ 沒有發現誤差超過8分的案例")
        return
    
    print(f"  發現 {len(high_error_cases)} 個高誤差案例:")
    
    for _, case in high_error_cases.head(10).iterrows():
        print(f"\n    📅 {case['date']} - {case['away_team']} @ {case['home_team']}")
        print(f"      實際: {case['away_score']}-{case['home_score']} (總分 {case['actual_total']})")
        print(f"      預測: {case['predicted_away_score']:.1f}-{case['predicted_home_score']:.1f} (總分 {case['predicted_total']:.1f})")
        print(f"      誤差: {case['total_error']:.1f} 分")
        print(f"      信心度: {case['confidence']:.1f}")
        if case['betting_line']:
            print(f"      博彩盤口: {case['betting_line']}")
    
    # 分析高誤差的共同特徵
    print(f"\n  高誤差案例特徵:")
    print(f"    平均信心度: {high_error_cases['confidence'].mean():.2f}")
    print(f"    平均實際總分: {high_error_cases['actual_total'].mean():.1f}")
    print(f"    平均預測總分: {high_error_cases['predicted_total'].mean():.1f}")
    
    # 檢查是否有特定隊伍
    team_errors = defaultdict(int)
    for _, case in high_error_cases.iterrows():
        team_errors[case['home_team']] += 1
        team_errors[case['away_team']] += 1
    
    if team_errors:
        print(f"    最常出現的隊伍:")
        for team, count in sorted(team_errors.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"      {team}: {count} 次")

def analyze_team_specific_errors(df):
    """按隊伍分析誤差"""
    print(f"\n🏟️ 按隊伍分析預測誤差:")
    
    # 主隊誤差
    home_team_errors = df.groupby('home_team')['total_error'].agg(['mean', 'count']).reset_index()
    home_team_errors = home_team_errors[home_team_errors['count'] >= 3]  # 至少3場比賽
    home_team_errors = home_team_errors.sort_values('mean', ascending=False)
    
    print(f"  主隊預測誤差最大的隊伍:")
    for _, row in home_team_errors.head(5).iterrows():
        print(f"    {row['home_team']}: 平均誤差 {row['mean']:.1f} 分 ({row['count']} 場)")
    
    # 客隊誤差
    away_team_errors = df.groupby('away_team')['total_error'].agg(['mean', 'count']).reset_index()
    away_team_errors = away_team_errors[away_team_errors['count'] >= 3]
    away_team_errors = away_team_errors.sort_values('mean', ascending=False)
    
    print(f"\n  客隊預測誤差最大的隊伍:")
    for _, row in away_team_errors.head(5).iterrows():
        print(f"    {row['away_team']}: 平均誤差 {row['mean']:.1f} 分 ({row['count']} 場)")

def analyze_model_version_errors(df):
    """按模型版本分析誤差"""
    print(f"\n🤖 按模型版本分析誤差:")
    
    model_errors = df.groupby('model_version')['total_error'].agg(['mean', 'count', 'std']).reset_index()
    model_errors = model_errors.sort_values('mean', ascending=False)
    
    for _, row in model_errors.iterrows():
        print(f"  {row['model_version']}: 平均誤差 {row['mean']:.1f}±{row['std']:.1f} 分 ({row['count']} 場)")

def analyze_vs_betting_lines(df):
    """預測vs博彩盤口對比"""
    print(f"\n💰 預測 vs 博彩盤口對比:")
    
    df_with_lines = df[df['betting_line'].notna()].copy()
    
    if len(df_with_lines) == 0:
        print("  ❌ 沒有博彩盤口數據")
        return
    
    # 計算預測與盤口的差異
    df_with_lines['pred_vs_line'] = df_with_lines['predicted_total'] - df_with_lines['betting_line']
    df_with_lines['actual_vs_line'] = df_with_lines['actual_total'] - df_with_lines['betting_line']
    
    print(f"  有盤口數據的比賽: {len(df_with_lines)} 場")
    print(f"  預測總分 vs 盤口:")
    print(f"    平均差異: {df_with_lines['pred_vs_line'].mean():.1f} 分")
    print(f"    預測高於盤口: {(df_with_lines['pred_vs_line'] > 0).sum()} 場 ({(df_with_lines['pred_vs_line'] > 0).mean()*100:.1f}%)")
    
    print(f"  實際總分 vs 盤口:")
    print(f"    平均差異: {df_with_lines['actual_vs_line'].mean():.1f} 分")
    print(f"    實際高於盤口: {(df_with_lines['actual_vs_line'] > 0).sum()} 場 ({(df_with_lines['actual_vs_line'] > 0).mean()*100:.1f}%)")
    
    # 分析預測準確性與盤口的關係
    df_with_lines['line_category'] = pd.cut(df_with_lines['betting_line'], 
                                           bins=[0, 7.5, 8.5, 9.5, float('inf')], 
                                           labels=['低分 (<7.5)', '中低分 (7.5-8.5)', '中高分 (8.5-9.5)', '高分 (>9.5)'])
    
    print(f"\n  按盤口範圍分析預測誤差:")
    for category in df_with_lines['line_category'].cat.categories:
        subset = df_with_lines[df_with_lines['line_category'] == category]
        if len(subset) > 0:
            print(f"    {category}: 平均誤差 {subset['total_error'].mean():.1f} 分 ({len(subset)} 場)")

def analyze_systematic_bias(df):
    """分析系統性偏差"""
    print(f"\n⚖️ 系統性偏差分析:")
    
    # 總分偏差
    total_bias = (df['predicted_total'] - df['actual_total']).mean()
    print(f"  總分預測偏差: {total_bias:+.1f} 分 ({'高估' if total_bias > 0 else '低估' if total_bias < 0 else '無偏差'})")
    
    # 主客隊偏差
    home_bias = (df['predicted_home_score'] - df['home_score']).mean()
    away_bias = (df['predicted_away_score'] - df['away_score']).mean()
    
    print(f"  主隊得分偏差: {home_bias:+.1f} 分")
    print(f"  客隊得分偏差: {away_bias:+.1f} 分")
    
    # 按信心度分析偏差
    df['confidence_category'] = pd.cut(df['confidence'], 
                                     bins=[0, 0.4, 0.6, 0.8, 1.0], 
                                     labels=['低信心', '中低信心', '中高信心', '高信心'])
    
    print(f"\n  按信心度分析偏差:")
    for category in df['confidence_category'].cat.categories:
        subset = df[df['confidence_category'] == category]
        if len(subset) > 0:
            bias = (subset['predicted_total'] - subset['actual_total']).mean()
            print(f"    {category}: {bias:+.1f} 分偏差 ({len(subset)} 場)")
    
    # 時間趨勢分析
    df['date'] = pd.to_datetime(df['date'])
    df_recent = df[df['date'] >= df['date'].max() - pd.Timedelta(days=7)]
    df_older = df[df['date'] < df['date'].max() - pd.Timedelta(days=7)]
    
    if len(df_recent) > 0 and len(df_older) > 0:
        recent_bias = (df_recent['predicted_total'] - df_recent['actual_total']).mean()
        older_bias = (df_older['predicted_total'] - df_older['actual_total']).mean()
        
        print(f"\n  時間趨勢分析:")
        print(f"    最近7天偏差: {recent_bias:+.1f} 分 ({len(df_recent)} 場)")
        print(f"    7天前偏差: {older_bias:+.1f} 分 ({len(df_older)} 場)")
        print(f"    趨勢變化: {recent_bias - older_bias:+.1f} 分")

if __name__ == "__main__":
    print("🚀 開始深入分析預測誤差...")
    
    analyze_prediction_errors()
    
    print("\n✅ 誤差分析完成！")
