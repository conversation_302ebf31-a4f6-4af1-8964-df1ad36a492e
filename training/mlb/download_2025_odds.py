#!/usr/bin/env python3
"""
下載2025賽季所有已完成比賽的賠率數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from models.database import db, Game, BettingOdds
from models.covers_scraper import CoversMLBScraper
from app import create_app
import time
import logging

# 設置日誌級別
logging.basicConfig(level=logging.INFO)

def download_2025_odds():
    """下載2025賽季所有已完成比賽的賠率數據"""
    print("=== 下載2025賽季賠率數據 ===")
    
    app = create_app()
    
    with app.app_context():
        # 獲取所有需要下載賠率的日期
        completed_games_2025 = Game.query.filter(
            Game.date >= date(2025, 1, 1),
            Game.date < date(2026, 1, 1),
            Game.away_score.isnot(None),
            Game.home_score.isnot(None),
            Game.away_score > 0,
            Game.home_score >= 0,
            Game.date < date.today()  # 只處理今天之前的比賽
        ).order_by(Game.date.desc()).all()  # 從最新日期開始
        
        # 按日期分組
        dates_to_process = {}
        for game in completed_games_2025:
            if game.date not in dates_to_process:
                dates_to_process[game.date] = []
            dates_to_process[game.date].append(game)
        
        # 檢查已有賠率數據的比賽
        existing_odds_games = set()
        existing_odds = BettingOdds.query.filter_by(bookmaker='covers.com').all()
        for odds in existing_odds:
            existing_odds_games.add(odds.game_id)
        
        print(f"總共需要處理 {len(dates_to_process)} 個日期")
        print(f"已有賠率數據的比賽: {len(existing_odds_games)}")
        
        scraper = CoversMLBScraper()
        
        total_processed = 0
        total_new_records = 0
        total_consistent = 0
        failed_dates = []
        
        # 按日期處理（從最新到最舊）
        sorted_dates = sorted(dates_to_process.keys(), reverse=True)
        
        for i, target_date in enumerate(sorted_dates):
            games_on_date = dates_to_process[target_date]
            
            print(f"\n--- 處理日期 {i+1}/{len(sorted_dates)}: {target_date} ({len(games_on_date)} 場比賽) ---")
            
            try:
                # 抓取該日期的賠率數據
                result = scraper.fetch_mlb_games_for_date(target_date)
                
                if result['success']:
                    scraped_games = result['games']
                    print(f"📥 抓取到 {len(scraped_games)} 場比賽")
                    
                    date_new_records = 0
                    date_consistent = 0
                    
                    for scraped_game in scraped_games:
                        # 查找對應的數據庫比賽記錄
                        db_game = None
                        for game in games_on_date:
                            if (game.away_team == scraped_game['away_team'] and 
                                game.home_team == scraped_game['home_team']):
                                db_game = game
                                break
                        
                        if db_game:
                            total_processed += 1
                            
                            # 檢查是否已有賠率數據
                            if db_game.game_id in existing_odds_games:
                                print(f"  ✅ 大小分數據一致: {scraped_game['away_team']} @ {scraped_game['home_team']}")
                                date_consistent += 1
                                continue
                            
                            # 處理總分線數據
                            if scraped_game['odds'].get('total_line'):
                                try:
                                    total_line = float(scraped_game['odds']['total_line'])
                                    
                                    if 5.0 <= total_line <= 15.0:  # 合理範圍
                                        # 創建賠率記錄
                                        betting_odds = BettingOdds(
                                            game_id=db_game.game_id,
                                            bookmaker='covers.com',
                                            market_type='totals',
                                            total_point=total_line,
                                            last_update=db.func.now()
                                        )
                                        
                                        db.session.add(betting_odds)
                                        existing_odds_games.add(db_game.game_id)
                                        
                                        print(f"  ➕ 新增大小分記錄: {scraped_game['away_team']} @ {scraped_game['home_team']} (total: {total_line})")
                                        date_new_records += 1
                                    else:
                                        print(f"  ⚠️  異常總分線: {scraped_game['away_team']} @ {scraped_game['home_team']} (total: {total_line})")
                                
                                except ValueError:
                                    print(f"  ❌ 總分線格式錯誤: {scraped_game['away_team']} @ {scraped_game['home_team']}")
                            else:
                                print(f"  ⚪ 無總分線數據: {scraped_game['away_team']} @ {scraped_game['home_team']}")
                    
                    total_new_records += date_new_records
                    total_consistent += date_consistent
                    
                    # 每處理一個日期就保存一次
                    db.session.commit()
                    print(f"  💾 已保存 {date_new_records} 條新記錄")
                    
                else:
                    print(f"  ❌ 抓取失敗: {result.get('error')}")
                    failed_dates.append(target_date)
                
                # 添加延遲避免被封鎖
                if i < len(sorted_dates) - 1:  # 不是最後一個
                    print(f"  ⏳ 等待 2 秒...")
                    time.sleep(2)
                
            except Exception as e:
                print(f"  ❌ 處理日期 {target_date} 時發生錯誤: {e}")
                failed_dates.append(target_date)
                continue
        
        print(f"\n=== 下載統計 ===")
        print(f"總處理比賽數: {total_processed}")
        print(f"新增記錄數: {total_new_records}")
        print(f"一致記錄數: {total_consistent}")
        print(f"失敗日期數: {len(failed_dates)}")
        
        if failed_dates:
            print(f"\n=== 失敗日期 ===")
            for failed_date in failed_dates[:10]:  # 只顯示前10個
                print(f"  {failed_date}")
            if len(failed_dates) > 10:
                print(f"  ... 還有 {len(failed_dates) - 10} 個失敗日期")
        
        print(f"✅ 所有更改已保存到數據庫")

if __name__ == "__main__":
    download_2025_odds()
