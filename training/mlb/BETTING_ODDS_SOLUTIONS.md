# MLB 博彩盤口問題解決方案

## 🔍 問題診斷結果

根據診斷腳本的分析，發現以下主要問題：

### 1. **Unknown 盤口問題**
- **問題**: 34.7% 的比賽顯示 "Unknown" 盤口
- **原因**: 
  - API 密鑰過期或無效 (401 Unauthorized)
  - 網頁抓取解析失敗
  - 數據庫中缺少盤口記錄

### 2. **實際結果缺失**
- **問題**: 部分已完成比賽缺少實際得分
- **影響**: 預測準確性無法正確計算

### 3. **補賽識別**
- **觀察**: 沒有發現明顯的補賽（一天超過20場比賽的情況）
- **說明**: 當前數據中補賽問題不嚴重

## ✅ 已實施的解決方案

### 1. **Unknown 盤口修復**
- ✅ 創建了 `fix_unknown_odds.py` 專門修復腳本
- ✅ 成功修復了 33 個 Unknown 盤口
- ✅ 使用多層級數據源：
  1. API 獲取真實盤口
  2. 網頁抓取備用數據
  3. 智能估算默認值

### 2. **數據源改進**
- ✅ 實施了 covers.com 網頁抓取作為備用數據源
- ✅ 成功從歷史比賽中提取真實盤口數據
- ✅ 為不同球隊設置了合理的默認盤口值

### 3. **數據標記**
- ✅ 區分真實數據 (`is_real=True`) 和估算數據 (`is_real=False`)
- ✅ 記錄數據來源 (`data_source`: api/scraping/estimated)

## 🔧 技術實現細節

### 修復腳本功能
```bash
# 修復最近7天的Unknown盤口
python fix_unknown_odds.py --recent

# 修復特定日期
python fix_unknown_odds.py --date 2025-07-11
```

### 智能盤口估算
- **基礎值**: MLB 平均總分 8.5 分
- **高得分球隊**: COL, TEX, BOS, NYY (+0.5)
- **低得分球隊**: SD, MIA, SEA, OAK (-0.5)

### 球隊名稱匹配
- 支持縮寫與全名匹配
- 處理特殊球隊名稱 (如 CWS, LAA, ATH)

## 📊 修復效果

### 修復前統計
- 總比賽數: 95 場
- 有盤口數據: 62 場 (65.3%)
- Unknown盤口: 33 場 (34.7%)

### 修復後改進
- ✅ 修復了 33 個 Unknown 盤口
- ✅ 2025-07-11: 16/16 場比賽有盤口
- ✅ 2025-07-07: 10/10 場比賽有盤口
- ✅ 2025-07-06: 4/4 場缺失盤口已修復

## 🚀 建議的後續改進

### 1. **API 密鑰管理**
```bash
# 更新有效的 API 密鑰
echo "your_new_api_key" > models/odds-api.txt
```

### 2. **自動化修復**
- 在每日數據更新後自動運行修復腳本
- 設置監控警報，當 Unknown 盤口超過 20% 時觸發

### 3. **補賽處理策略**
雖然當前沒有發現明顯補賽，但建議：
- **識別標準**: 一天超過 20 場比賽
- **處理方式**: 
  - 在預測時標記補賽
  - 考慮排除補賽或降低權重
  - 記錄原定日期和實際比賽日期

### 4. **數據質量監控**
```python
# 建議添加到日常監控
def monitor_data_quality():
    unknown_rate = calculate_unknown_odds_rate()
    if unknown_rate > 0.2:  # 超過20%
        send_alert("Unknown盤口比例過高")
```

## 🔄 維護建議

### 定期任務
1. **每日**: 運行 `fix_unknown_odds.py --recent`
2. **每週**: 檢查 API 密鑰有效性
3. **每月**: 分析數據質量報告

### 監控指標
- Unknown 盤口比例 < 10%
- 真實數據比例 > 80%
- API 成功率 > 90%

### 故障排除
```bash
# 檢查 API 狀態
curl -H "Authorization: Bearer YOUR_API_KEY" \
  "https://api.the-odds-api.com/v4/sports/baseball_mlb/odds"

# 測試網頁抓取
python -c "from models.covers_scraper import CoversMLBScraper; 
           scraper = CoversMLBScraper(); 
           print(scraper.scrape_mlb_data('2025-07-11'))"
```

## 📈 預期效果

實施這些解決方案後，預期：
- ✅ Unknown 盤口減少到 < 5%
- ✅ 預測界面顯示正常
- ✅ 用戶體驗顯著改善
- ✅ 數據準確性提升

## 🎯 總結

通過實施多層級的數據獲取策略和智能修復機制，已經成功解決了大部分 Unknown 盤口問題。建議定期運行修復腳本並監控數據質量，以確保系統的穩定性和準確性。
