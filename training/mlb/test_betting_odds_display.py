#!/usr/bin/env python3
"""
測試博彩盤口顯示修復效果
"""

import requests
import json
from datetime import date

def test_api_endpoints():
    """測試API端點的博彩盤口數據"""
    base_url = "http://localhost:5500"
    test_date = "2025-07-09"
    
    print("🧪 測試博彩盤口數據顯示")
    print("=" * 60)
    
    # 測試unified/query API
    print(f"1️⃣  測試 unified/query API ({test_date}):")
    try:
        response = requests.get(f"{base_url}/unified/api/predictions/date/{test_date}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('predictions'):
                for pred in data['predictions'][:3]:  # 只顯示前3個
                    game = f"{pred.get('away_team', 'N/A')}@{pred.get('home_team', 'N/A')}"
                    betting_odds = pred.get('betting_odds', {})
                    totals = betting_odds.get('totals', {})
                    
                    if totals:
                        line = totals.get('total_point', 'N/A')
                        bookmaker = totals.get('bookmaker', 'N/A')
                        is_real = totals.get('is_real', False)
                        source = "真實" if is_real else "模擬"
                        
                        print(f"   {game}: 盤口 {line} ({bookmaker}) [{source}]")
                    else:
                        print(f"   {game}: 無盤口數據")
            else:
                print("   ❌ 無預測數據")
        else:
            print(f"   ❌ API錯誤: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 請求失敗: {e}")
    
    print()
    
    # 測試custom_predict頁面生成預測
    print("2️⃣  測試 custom_predict 預測生成:")
    try:
        # 生成2025-07-10的預測
        response = requests.post(f"{base_url}/unified/api/predict/custom_date", 
                               json={"date": "2025-07-10"})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                predictions = data.get('predictions', [])
                print(f"   ✅ 成功生成 {len(predictions)} 個預測")
                
                # 檢查前3個預測的盤口數據
                for pred in predictions[:3]:
                    game_id = pred.get('game_id', 'N/A')
                    matchup = pred.get('matchup', 'N/A')
                    over_under_line = pred.get('over_under_line', 'N/A')
                    is_real_odds = pred.get('is_real_odds', False)
                    source = "真實" if is_real_odds else "模擬"
                    
                    print(f"   {matchup}: 盤口 {over_under_line} [{source}]")
            else:
                print(f"   ❌ 預測失敗: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ API錯誤: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 請求失敗: {e}")
    
    print()
    
    # 測試獲取生成的預測
    print("3️⃣  測試獲取生成的預測數據:")
    try:
        response = requests.get(f"{base_url}/unified/api/predictions/date/2025-07-10")
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('predictions'):
                for pred in data['predictions'][:3]:  # 只顯示前3個
                    game = f"{pred.get('away_team', 'N/A')}@{pred.get('home_team', 'N/A')}"
                    betting_odds = pred.get('betting_odds', {})
                    totals = betting_odds.get('totals', {})
                    
                    if totals:
                        line = totals.get('total_point', 'N/A')
                        bookmaker = totals.get('bookmaker', 'N/A')
                        is_real = totals.get('is_real', False)
                        source = "真實" if is_real else "模擬"
                        
                        print(f"   {game}: 盤口 {line} ({bookmaker}) [{source}]")
                    else:
                        print(f"   {game}: 無盤口數據")
            else:
                print("   ❌ 無預測數據")
        else:
            print(f"   ❌ API錯誤: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 請求失敗: {e}")

def test_database_consistency():
    """測試數據庫一致性"""
    print("\n🔍 測試數據庫一致性")
    print("=" * 60)
    
    import sqlite3
    import os
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查2025-07-09的數據一致性
        cursor.execute("""
            SELECT 
                g.away_team || '@' || g.home_team as matchup,
                p.over_under_line as pred_line,
                bo.total_point as bet365_line,
                bo.bookmaker,
                CASE WHEN p.over_under_line = bo.total_point THEN '一致' ELSE '不一致' END as consistency
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            LEFT JOIN betting_odds bo ON p.game_id = bo.game_id 
                AND bo.market_type = 'totals' AND bo.bookmaker = 'bet365'
            WHERE g.date = '2025-07-09'
            AND p.model_version = 'unified_v1.0'
            ORDER BY g.game_id
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        
        print("比賽對戰        | 預測盤口 | bet365盤口 | 博彩商 | 一致性")
        print("-" * 60)
        
        for matchup, pred_line, bet365_line, bookmaker, consistency in results:
            bookmaker_str = bookmaker or "N/A"
            bet365_str = f"{bet365_line:.1f}" if bet365_line else "N/A"
            pred_str = f"{pred_line:.1f}" if pred_line else "N/A"
            print(f"{matchup:15s} | {pred_str:8s} | {bet365_str:10s} | {bookmaker_str:6s} | {consistency}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 數據庫檢查失敗: {e}")

if __name__ == '__main__':
    test_api_endpoints()
    test_database_consistency()
    
    print("\n" + "=" * 60)
    print("🎯 修復驗證完成")
    print("請檢查 unified/custom_predict 頁面是否正確顯示:")
    print("1. 真實博彩盤口應標記為 '真實'")
    print("2. 模擬數據應標記為 '模擬'")
    print("3. 盤口數值應與bet365一致")
    print("=" * 60)
