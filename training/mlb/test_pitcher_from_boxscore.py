#!/usr/bin/env python3
"""
測試從BoxScore獲取投手信息的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game, PlayerGameStats, Player, Team
from models.simple_intelligent_predictor import SimpleIntelligentPredictor

def test_pitcher_from_boxscore():
    """測試從BoxScore獲取投手信息"""
    app = create_app()
    
    with app.app_context():
        print("🔍 測試從BoxScore獲取投手信息")
        print("=" * 80)
        
        predictor = SimpleIntelligentPredictor()
        
        # 測試案例：查找有PlayerGameStats數據的比賽
        test_games = [
            {
                'game_id': '777122',
                'home_team': 'NYY',
                'away_team': 'CHC',
                'description': 'CHC @ NYY - 測試從比賽統計獲取投手'
            },
            {
                'game_id': '777170',
                'home_team': 'BOS', 
                'away_team': 'TB',
                'description': 'TB @ BOS - 測試另一場比賽的投手'
            }
        ]
        
        for i, test_case in enumerate(test_games, 1):
            print(f"\n🎯 測試案例 {i}: {test_case['description']}")
            print("-" * 60)
            
            game_id = test_case['game_id']
            home_team = test_case['home_team']
            away_team = test_case['away_team']
            
            # 檢查比賽是否存在
            game = Game.query.filter_by(game_id=game_id).first()
            if not game:
                print(f"❌ 找不到比賽 ID: {game_id}")
                continue
                
            print(f"📅 比賽: {away_team} @ {home_team} ({game.date})")
            
            # 檢查是否有PlayerGameStats數據
            pitcher_stats = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.position == 'P',
                PlayerGameStats.innings_pitched > 0
            ).all()
            
            print(f"📊 該比賽投手統計數量: {len(pitcher_stats)}")
            
            if pitcher_stats:
                print(f"📋 投手列表:")
                for stat in pitcher_stats:
                    player = Player.query.get(stat.player_id)
                    team = Team.query.get(stat.team_id)
                    print(f"   - {player.full_name if player and hasattr(player, 'full_name') else 'Unknown'} "
                          f"({team.team_code if team and hasattr(team, 'team_code') else 'Unknown'}) "
                          f"投球 {stat.innings_pitched} 局, "
                          f"失分 {stat.earned_runs}")
            
            # 測試獲取主隊投手
            print(f"\n🏠 測試獲取主隊投手 ({home_team}):")
            home_pitcher = predictor._get_starting_pitcher_from_game_stats(
                game_id, home_team, is_home=True
            )
            if home_pitcher:
                print(f"   ✅ 找到主隊投手: {home_pitcher['name']} "
                      f"(ERA: {home_pitcher['era']:.2f}, 質量: {home_pitcher['quality']:.1f})")
            else:
                print(f"   ❌ 找不到主隊投手")
            
            # 測試獲取客隊投手
            print(f"\n✈️  測試獲取客隊投手 ({away_team}):")
            away_pitcher = predictor._get_starting_pitcher_from_game_stats(
                game_id, away_team, is_home=False
            )
            if away_pitcher:
                print(f"   ✅ 找到客隊投手: {away_pitcher['name']} "
                      f"(ERA: {away_pitcher['era']:.2f}, 質量: {away_pitcher['quality']:.1f})")
            else:
                print(f"   ❌ 找不到客隊投手")
            
            # 測試完整的投手信息獲取流程
            print(f"\n🧠 測試完整投手信息獲取:")
            home_pitcher_full = predictor._get_pitcher_info_simple(
                home_team, game.date, game_id, is_home=True
            )
            away_pitcher_full = predictor._get_pitcher_info_simple(
                away_team, game.date, game_id, is_home=False
            )
            
            print(f"   主隊: {home_pitcher_full['name']} "
                  f"(ERA: {home_pitcher_full['era']:.2f}, 等級: {predictor._era_to_quality(home_pitcher_full['era']):.0f})")
            print(f"   客隊: {away_pitcher_full['name']} "
                  f"(ERA: {away_pitcher_full['era']:.2f}, 等級: {predictor._era_to_quality(away_pitcher_full['era']):.0f})")

def test_pitcher_quality_analysis():
    """測試投手質量分析"""
    print(f"\n\n🔬 測試投手質量分析")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        predictor = SimpleIntelligentPredictor()
        
        # 測試不同ERA的投手質量計算
        test_eras = [2.20, 2.80, 3.50, 4.50, 5.50, 6.00]
        
        print("ERA → 質量分數 → 投手等級:")
        for era in test_eras:
            quality = predictor._era_to_quality(era)
            
            # 確定投手等級
            if era <= 2.50 and quality >= 80:
                strength = "王牌"
            elif era <= 3.50 and quality >= 70:
                strength = "優秀"
            elif era <= 4.50 and quality >= 50:
                strength = "普通"
            else:
                strength = "弱勢"
            
            print(f"   ERA {era:.2f} → 質量 {quality:.0f} → {strength}")

def test_recent_pitcher_lookup():
    """測試最近投手查找功能"""
    print(f"\n\n🕒 測試最近投手查找功能")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        predictor = SimpleIntelligentPredictor()
        
        # 測試幾個球隊的最近投手
        test_teams = ['NYY', 'CHC', 'BOS', 'TB']
        
        for team in test_teams:
            print(f"\n🏟️  測試球隊: {team}")
            
            # 測試主場投手
            home_pitcher = predictor._get_recent_team_pitcher(team, is_home=True)
            if home_pitcher:
                print(f"   主場投手: {home_pitcher['name']} (ERA: {home_pitcher['era']:.2f})")
            else:
                print(f"   主場投手: 找不到")
            
            # 測試客場投手
            away_pitcher = predictor._get_recent_team_pitcher(team, is_home=False)
            if away_pitcher:
                print(f"   客場投手: {away_pitcher['name']} (ERA: {away_pitcher['era']:.2f})")
            else:
                print(f"   客場投手: 找不到")

def test_intelligent_prediction_with_boxscore():
    """測試使用BoxScore數據的智能預測"""
    print(f"\n\n🧠 測試使用BoxScore數據的智能預測")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        predictor = SimpleIntelligentPredictor()
        
        # 測試CHC @ NYY比賽
        try:
            result = predictor.predict_game_intelligent(
                home_team='NYY',
                away_team='CHC',
                game_date=date(2025, 7, 13),
                game_id='777122'
            )
            
            print("✅ 智能預測成功!")
            print(f"📊 投手分析:")
            print(f"   主隊投手: {result['pitcher_analysis']['home_pitcher']['name']} "
                  f"(ERA: {result['pitcher_analysis']['home_pitcher']['era']:.2f}, "
                  f"等級: {result['pitcher_analysis']['home_pitcher']['strength']})")
            print(f"   客隊投手: {result['pitcher_analysis']['away_pitcher']['name']} "
                  f"(ERA: {result['pitcher_analysis']['away_pitcher']['era']:.2f}, "
                  f"等級: {result['pitcher_analysis']['away_pitcher']['strength']})")
            print(f"   對戰類型: {result['pitcher_analysis']['matchup_type']}")
            
            print(f"\n🧠 智能策略: {result['strategy']['name']}")
            print(f"   策略說明: {result['strategy']['description']}")
            
            print(f"\n📈 預測結果:")
            print(f"   預測比分: {result['predicted_away_score']:.1f} - {result['predicted_home_score']:.1f}")
            print(f"   預測總分: {result['total_runs']:.1f}分")
            print(f"   信心度: {result['confidence']:.1%}")
            
            # 檢查實際結果
            game = Game.query.filter_by(game_id='777122').first()
            if game and game.away_score is not None and game.home_score is not None:
                actual_total = game.away_score + game.home_score
                predicted_total = result['total_runs']
                diff = abs(predicted_total - actual_total)
                
                print(f"\n🎯 與實際結果比較:")
                print(f"   實際比分: {game.away_score} - {game.home_score}")
                print(f"   實際總分: {actual_total}分")
                print(f"   預測差異: {diff:.1f}分")
                
                if diff <= 2.0:
                    print(f"   ✅ 預測非常準確!")
                elif diff <= 4.0:
                    print(f"   ⚠️  預測尚可")
                else:
                    print(f"   ❌ 預測偏差較大")
            
        except Exception as e:
            print(f"❌ 智能預測失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_pitcher_from_boxscore()
    test_pitcher_quality_analysis()
    test_recent_pitcher_lookup()
    test_intelligent_prediction_with_boxscore()
