#!/usr/bin/env python3
"""
調試covers.com數據抓取和保存問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime
import json

def debug_covers_data():
    """調試covers.com數據抓取"""
    try:
        from models.covers_scraper import CoversMLBScraper
        from models.database import db, Game, BettingOdds
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            # 測試日期
            test_date = date(2025, 7, 11)
            
            print(f"🔍 調試 {test_date} 的covers.com數據抓取...")
            
            # 1. 檢查數據庫中的比賽
            games = Game.query.filter_by(date=test_date).all()
            print(f"📊 數據庫中有 {len(games)} 場比賽:")
            for game in games[:5]:  # 只顯示前5場
                print(f"  - {game.away_team} @ {game.home_team}")
            
            # 2. 使用covers抓取器獲取數據
            scraper = CoversMLBScraper()
            result = scraper.fetch_mlb_games_for_date(test_date)
            
            print(f"\n🌐 covers.com抓取結果:")
            print(f"  成功: {result.get('success')}")
            print(f"  總比賽數: {result.get('total_games', 0)}")
            
            if result.get('success'):
                scraped_games = result.get('games', [])
                print(f"\n📋 抓取到的比賽詳情:")
                
                for i, game in enumerate(scraped_games[:3]):  # 只顯示前3場
                    print(f"\n  比賽 {i+1}:")
                    print(f"    客隊: {game.get('away_team')}")
                    print(f"    主隊: {game.get('home_team')}")
                    print(f"    狀態: {game.get('status')}")
                    print(f"    客隊分數: {game.get('away_score')}")
                    print(f"    主隊分數: {game.get('home_score')}")
                    
                    odds = game.get('odds', {})
                    print(f"    盤口數據:")
                    print(f"      總分線: {odds.get('total_line')}")
                    print(f"      讓分線: {odds.get('spread_line')}")
                    print(f"      客隊勝負盤: {odds.get('moneyline_away')}")
                    print(f"      主隊勝負盤: {odds.get('moneyline_home')}")
                
                # 3. 測試數據匹配
                print(f"\n🔗 測試數據匹配:")
                matched_count = 0
                
                for scraped_game in scraped_games:
                    away_team = scraped_game.get('away_team', '').upper()
                    home_team = scraped_game.get('home_team', '').upper()
                    
                    # 在數據庫遊戲中查找匹配
                    for game in games:
                        if (game.away_team.upper() == away_team and
                            game.home_team.upper() == home_team):
                            matched_count += 1
                            print(f"  ✅ 匹配: {away_team} @ {home_team}")
                            
                            # 檢查是否有盤口數據
                            odds = scraped_game.get('odds', {})
                            has_total = odds.get('total_line') is not None
                            has_spread = odds.get('spread_line') is not None
                            print(f"    總分線: {'✅' if has_total else '❌'} {odds.get('total_line')}")
                            print(f"    讓分線: {'✅' if has_spread else '❌'} {odds.get('spread_line')}")
                            break
                
                print(f"\n📈 匹配統計:")
                print(f"  數據庫比賽: {len(games)}")
                print(f"  抓取比賽: {len(scraped_games)}")
                print(f"  成功匹配: {matched_count}")
                
                # 4. 檢查現有盤口數據
                existing_odds = BettingOdds.query.join(Game).filter(Game.date == test_date).all()
                print(f"  現有盤口記錄: {len(existing_odds)}")
                
                if existing_odds:
                    print(f"  現有盤口詳情:")
                    for odds in existing_odds[:3]:
                        print(f"    - 遊戲ID: {odds.game_id}, 博彩商: {odds.bookmaker}")
                        print(f"      總分線: {odds.total_point}, 讓分線: {odds.home_spread_point}")
                
                # 5. 測試保存邏輯
                print(f"\n💾 測試保存邏輯:")
                test_save_logic(scraped_games, games)
            
            else:
                print(f"❌ 抓取失敗: {result.get('error')}")
                
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_save_logic(scraped_games, db_games):
    """測試保存邏輯"""
    try:
        from models.database import db, BettingOdds
        
        print("  測試保存邏輯...")
        
        for scraped_game in scraped_games[:2]:  # 只測試前2場
            away_team = scraped_game.get('away_team', '').upper()
            home_team = scraped_game.get('home_team', '').upper()
            
            # 查找對應的遊戲記錄
            game = None
            for g in db_games:
                if (g.away_team.upper() == away_team and
                    g.home_team.upper() == home_team):
                    game = g
                    break
            
            if not game:
                print(f"    ❌ 找不到對應遊戲: {away_team} @ {home_team}")
                continue
            
            print(f"    🎯 測試遊戲: {away_team} @ {home_team} (ID: {game.id})")
            
            # 檢查是否已存在盤口數據
            existing_odds = BettingOdds.query.filter_by(
                game_id=game.id,
                bookmaker=scraped_game.get('bookmaker', 'bet365')
            ).first()
            
            if existing_odds:
                print(f"      ⚠️  盤口數據已存在，跳過")
                continue
            
            # 創建新的盤口記錄
            odds_data = {}
            odds_info = scraped_game.get('odds', {})
            
            print(f"      📊 原始盤口數據: {odds_info}")
            
            # 處理總分盤數據
            if odds_info.get('total_line'):
                try:
                    total_line = float(odds_info['total_line'])
                    if 5.0 <= total_line <= 15.0:  # 合理範圍檢查
                        odds_data.update({
                            'total_point': total_line,
                            'over_price': -110,
                            'under_price': -110
                        })
                        print(f"      ✅ 總分線數據: {total_line}")
                    else:
                        print(f"      ❌ 總分線超出範圍: {total_line}")
                except (ValueError, TypeError) as e:
                    print(f"      ❌ 無效的總分線數據: {odds_info['total_line']} - {e}")
            
            # 處理讓分盤數據
            if odds_info.get('spread_line'):
                try:
                    spread_line = float(odds_info['spread_line'])
                    odds_data.update({
                        'home_spread_point': spread_line,
                        'home_spread_price': -110,
                        'away_spread_point': -spread_line,
                        'away_spread_price': -110
                    })
                    print(f"      ✅ 讓分線數據: {spread_line}")
                except (ValueError, TypeError) as e:
                    print(f"      ❌ 無效的讓分線數據: {odds_info['spread_line']} - {e}")
            
            print(f"      📝 最終盤口數據: {odds_data}")
            
            if odds_data:
                print(f"      ✅ 可以保存盤口數據")
            else:
                print(f"      ❌ 沒有有效的盤口數據可保存")
                
    except Exception as e:
        print(f"    ❌ 測試保存邏輯失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_covers_data()
