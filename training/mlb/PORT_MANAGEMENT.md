# 🔌 MLB 系統端口管理指南

## 問題背景
當您看到 "Port 5502 is in use by another program" 錯誤時，說明端口被其他進程佔用了。

## 🚀 快速解決方案

### 方法 1: 使用清理腳本 (推薦)
```bash
./cleanup_ports.sh
```
這會自動檢查端口狀態並詢問是否清理。

### 方法 2: 手動指定端口
```bash
export FLASK_PORT=5500
./start.sh
```

### 方法 3: 使用端口管理工具
```bash
# 檢查端口狀態
python port_manager.py --check

# 自動清理被佔用端口
python port_manager.py --cleanup

# 尋找可用端口
python port_manager.py --find
```

## 🔧 端口管理工具詳細說明

### 檢查端口狀態
```bash
python port_manager.py --check
```
顯示端口 5500-5505 的使用狀況和佔用進程信息。

### 清理被佔用端口
```bash
python port_manager.py --cleanup
```
自動終止佔用端口的 Flask/Python 進程。

### 清理特定端口
```bash
python port_manager.py --port 5502 --cleanup
```

### 尋找可用端口
```bash
python port_manager.py --find --start-port 5500
```

## 📊 端口分配說明
- **5500**: 主要 Web 服務端口
- **5501-5505**: 備用端口
- 系統會自動選擇第一個可用端口

## ⚠️ 常見問題

### Q: 清理後端口仍被佔用？
**A**: 某些進程可能需要管理員權限才能終止：
```bash
sudo python port_manager.py --cleanup
```

### Q: 如何手動查找佔用端口的進程？
**A**: 使用系統命令：
```bash
lsof -i :5502  # 查看端口5502的使用情況
```

### Q: 如何手動終止進程？
**A**: 
```bash
kill -9 <PID>  # 替換 <PID> 為實際進程ID
```

### Q: 啟動腳本會自動處理端口衝突嗎？
**A**: 是的！更新後的 `start.sh` 會：
1. 自動檢查端口狀態
2. 嘗試清理被佔用端口
3. 尋找可用端口並啟動服務

## 🎯 最佳實踐

1. **每次啟動前檢查**: 習慣性運行 `python port_manager.py --check`
2. **使用清理腳本**: `./cleanup_ports.sh` 提供交互式清理體驗
3. **保留備用端口**: 不要同時運行多個 MLB 系統實例
4. **定期清理**: 開發過程中定期清理殭屍進程

## 🔍 故障排除

### 啟動失敗排查步驟：
1. 檢查端口狀態: `python port_manager.py --check`
2. 清理端口: `python port_manager.py --cleanup`
3. 確認虛擬環境: `source .venv/bin/activate`
4. 重新啟動: `./start.sh`

### 日誌分析：
如果看到以下錯誤：
```
Address already in use
Port 5502 is in use by another program
```

立即執行：
```bash
./cleanup_ports.sh
```

## ✅ 驗證系統運行

成功啟動後應該看到：
```
✅ 端口 5500 可用
🌐 啟動Web服務器...
📱 請在瀏覽器中打開: http://localhost:5500
* Serving Flask app 'app'
* Debug mode: on
```

瀏覽器打開 `http://localhost:5500` 應該能看到 MLB 預測系統界面。