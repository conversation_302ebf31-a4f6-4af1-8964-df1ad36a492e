#!/usr/bin/env python3
"""
緊急修復 4.0-5.0 預測問題
"""

import sys
import os
from datetime import date, timedelta
from collections import defaultdict

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, TeamStats, Team, Prediction

def emergency_fix():
    """緊急修復預測系統"""
    print("🚨 緊急修復 4.0-5.0 預測問題")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        
        # 1. 刪除所有有問題的預測
        print("🗑️ 刪除有問題的預測...")
        problem_predictions = Prediction.query.filter(
            Prediction.predicted_home_score.between(4.0, 5.0),
            Prediction.predicted_away_score.between(4.0, 5.0)
        ).all()
        
        print(f"找到 {len(problem_predictions)} 個有問題的預測")
        
        for pred in problem_predictions:
            db.session.delete(pred)
        
        db.session.commit()
        print("✅ 已刪除有問題的預測")
        
        # 2. 重新生成TeamStats
        print("\n📊 重新生成TeamStats...")
        regenerate_team_stats()
        
        # 3. 驗證TeamStats差異
        print("\n🔍 驗證TeamStats差異...")
        verify_team_differences()
        
        print("\n✅ 緊急修復完成!")
        print("請通過web界面重新生成預測")

def regenerate_team_stats():
    """重新生成球隊統計"""
    # 清除現有統計
    TeamStats.query.delete()
    
    # 獲取比賽數據
    end_date = date.today()
    start_date = end_date - timedelta(days=90)
    
    games = Game.query.filter(
        Game.date >= start_date,
        Game.date < end_date,
        Game.home_score.isnot(None),
        Game.away_score.isnot(None),
        Game.game_status == 'completed'
    ).all()
    
    print(f"分析 {len(games)} 場比賽")
    
    # 計算統計
    team_stats = defaultdict(lambda: {
        'games': 0, 'wins': 0, 'losses': 0,
        'home_wins': 0, 'home_losses': 0,
        'away_wins': 0, 'away_losses': 0,
        'runs_scored': 0, 'runs_allowed': 0,
    })
    
    for game in games:
        home_team = game.home_team
        away_team = game.away_team
        home_score = game.home_score
        away_score = game.away_score
        
        # 主隊統計
        team_stats[home_team]['games'] += 1
        team_stats[home_team]['runs_scored'] += home_score
        team_stats[home_team]['runs_allowed'] += away_score
        team_stats[home_team]['home_wins' if home_score > away_score else 'home_losses'] += 1
        team_stats[home_team]['wins' if home_score > away_score else 'losses'] += 1
        
        # 客隊統計
        team_stats[away_team]['games'] += 1
        team_stats[away_team]['runs_scored'] += away_score
        team_stats[away_team]['runs_allowed'] += home_score
        team_stats[away_team]['away_wins' if away_score > home_score else 'away_losses'] += 1
        team_stats[away_team]['wins' if away_score > home_score else 'losses'] += 1
    
    # 保存統計
    saved_count = 0
    for team_code, stats in team_stats.items():
        team = Team.query.filter_by(team_code=team_code).first()
        if not team or stats['games'] == 0:
            continue
        
        games = stats['games']
        runs_per_game = stats['runs_scored'] / games
        runs_allowed_per_game = stats['runs_allowed'] / games
        win_pct = stats['wins'] / games
        
        # 創建差異化的統計數據
        team_stat = TeamStats(
            team_id=team.team_id,
            season=2025,
            games_played=games,
            wins=stats['wins'],
            losses=stats['losses'],
            win_percentage=win_pct,
            runs_scored=runs_per_game,  # 這個很重要！
            runs_allowed=runs_allowed_per_game,  # 這個也很重要！
            batting_avg=0.240 + (runs_per_game - 4.5) * 0.03,  # 基於得分差異化
            era=runs_allowed_per_game,  # 基於失分差異化
            home_wins=stats['home_wins'],
            home_losses=stats['home_losses'],
            away_wins=stats['away_wins'],
            away_losses=stats['away_losses']
        )
        
        db.session.add(team_stat)
        saved_count += 1
    
    db.session.commit()
    print(f"✅ 生成 {saved_count} 個球隊統計")

def verify_team_differences():
    """驗證球隊差異"""
    test_teams = ['LAD', 'NYY', 'HOU', 'OAK', 'MIA', 'COL']
    
    print("球隊統計差異驗證:")
    for team_code in test_teams:
        team = Team.query.filter_by(team_code=team_code).first()
        if team:
            stats = TeamStats.query.filter_by(team_id=team.team_id).first()
            if stats:
                print(f"  {team_code}: 得分={stats.runs_scored:.2f}, 失分={stats.era:.2f}, 勝率={stats.win_percentage:.3f}")
            else:
                print(f"  {team_code}: ❌ 無統計")
        else:
            print(f"  {team_code}: ❌ 找不到")
    
    # 檢查差異範圍
    all_stats = TeamStats.query.all()
    if all_stats:
        runs_scored = [s.runs_scored for s in all_stats]
        runs_allowed = [s.runs_allowed for s in all_stats]
        
        runs_diff = max(runs_scored) - min(runs_scored)
        era_diff = max(runs_allowed) - min(runs_allowed)
        
        print(f"\n📊 統計差異範圍:")
        print(f"得分差異: {runs_diff:.2f} (最高: {max(runs_scored):.2f}, 最低: {min(runs_scored):.2f})")
        print(f"失分差異: {era_diff:.2f} (最高: {max(runs_allowed):.2f}, 最低: {min(runs_allowed):.2f})")
        
        if runs_diff > 1.0 and era_diff > 1.0:
            print("✅ 球隊統計有合理差異!")
        else:
            print("❌ 球隊統計差異不足!")

if __name__ == "__main__":
    emergency_fix()
