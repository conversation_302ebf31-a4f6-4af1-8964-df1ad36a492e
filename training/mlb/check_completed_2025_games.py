#!/usr/bin/env python3
"""
檢查2025賽季已完成的比賽
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime
from models.database import db, Game, BettingOdds
from app import create_app
from sqlalchemy import func, and_

def check_completed_2025_games():
    """檢查2025賽季已完成的比賽"""
    print("=== 檢查2025賽季已完成的比賽 ===")
    
    app = create_app()
    
    with app.app_context():
        # 查詢2025年已完成的比賽（有真實比分的）
        completed_games_2025 = Game.query.filter(
            Game.date >= date(2025, 1, 1),
            Game.date < date(2026, 1, 1),
            Game.away_score.isnot(None),
            Game.home_score.isnot(None),
            Game.away_score > 0,  # 排除0-0的比賽
            Game.home_score >= 0
        ).order_by(Game.date).all()
        
        print(f"2025賽季已完成比賽數: {len(completed_games_2025)}")
        
        if completed_games_2025:
            # 檢查日期範圍
            earliest_game = min(completed_games_2025, key=lambda x: x.date)
            latest_game = max(completed_games_2025, key=lambda x: x.date)
            
            print(f"\n=== 已完成比賽日期範圍 ===")
            print(f"最早完成比賽: {earliest_game.date} ({earliest_game.away_team} {earliest_game.away_score}-{earliest_game.home_score} {earliest_game.home_team})")
            print(f"最晚完成比賽: {latest_game.date} ({latest_game.away_team} {latest_game.away_score}-{latest_game.home_score} {latest_game.home_team})")
            
            # 按月份統計已完成比賽
            monthly_completed = {}
            for game in completed_games_2025:
                month_key = f"{game.date.year}-{game.date.month:02d}"
                if month_key not in monthly_completed:
                    monthly_completed[month_key] = 0
                monthly_completed[month_key] += 1
            
            print(f"\n=== 按月份統計已完成比賽 ===")
            for month, count in sorted(monthly_completed.items()):
                print(f"  {month}: {count} 場比賽")
            
            # 檢查已有賠率數據
            completed_games_with_odds = db.session.query(Game).join(BettingOdds).filter(
                Game.date >= date(2025, 1, 1),
                Game.date < date(2026, 1, 1),
                Game.away_score.isnot(None),
                Game.home_score.isnot(None),
                Game.away_score > 0,
                Game.home_score >= 0,
                BettingOdds.bookmaker == 'covers.com'
            ).distinct().all()
            
            print(f"\n=== 賠率數據統計 ===")
            print(f"已有賠率數據的已完成比賽: {len(completed_games_with_odds)}")
            print(f"缺少賠率數據的已完成比賽: {len(completed_games_2025) - len(completed_games_with_odds)}")
            
            # 顯示最近10場已完成比賽
            print(f"\n=== 最近10場已完成比賽 ===")
            recent_completed = sorted(completed_games_2025, key=lambda x: x.date, reverse=True)[:10]
            for i, game in enumerate(recent_completed):
                has_odds = any(g.game_id == game.game_id for g in completed_games_with_odds)
                odds_status = "✅" if has_odds else "❌"
                print(f"  {i+1}. {game.date}: {game.away_team} {game.away_score}-{game.home_score} {game.home_team} {odds_status}")
            
            # 檢查今天之前的比賽
            today = date.today()
            past_games = [g for g in completed_games_2025 if g.date < today]
            print(f"\n=== 今天之前的已完成比賽 ===")
            print(f"今天之前已完成比賽: {len(past_games)}")
            
            if past_games:
                # 按日期分組統計需要下載賠率的比賽
                dates_need_odds = {}
                for game in past_games:
                    has_odds = any(g.game_id == game.game_id for g in completed_games_with_odds)
                    if not has_odds:
                        if game.date not in dates_need_odds:
                            dates_need_odds[game.date] = 0
                        dates_need_odds[game.date] += 1
                
                print(f"\n=== 需要下載賠率的日期 ===")
                sorted_dates = sorted(dates_need_odds.keys(), reverse=True)
                for i, game_date in enumerate(sorted_dates[:20]):  # 顯示最近20個日期
                    count = dates_need_odds[game_date]
                    print(f"  {i+1}. {game_date}: {count} 場比賽")
                
                if len(sorted_dates) > 20:
                    print(f"  ... 還有 {len(sorted_dates) - 20} 個日期")
                
                print(f"\n總共需要處理 {len(sorted_dates)} 個日期")
        
        else:
            print("未找到2025賽季已完成的比賽")

if __name__ == "__main__":
    check_completed_2025_games()
