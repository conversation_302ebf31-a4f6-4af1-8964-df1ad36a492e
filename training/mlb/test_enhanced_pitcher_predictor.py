#!/usr/bin/env python3
"""
測試增強投手預測器
驗證先發投手個人統計、牛棚強度、投手疲勞度等特徵提取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime
from models.database import db, Game, GameDetail, PlayerStats, Team
from models.enhanced_pitcher_predictor import EnhancedPitcherPredictor
from app import create_app

def test_enhanced_pitcher_predictor():
    """測試增強投手預測器功能"""
    
    app = create_app()
    
    with app.app_context():
        print("🧪 測試增強投手預測器")
        print("=" * 60)
        
        # 初始化預測器
        predictor = EnhancedPitcherPredictor()
        
        # 1. 測試數據庫連接和基本數據
        print("\n📊 檢查數據庫狀態:")
        
        total_games = Game.query.count()
        games_with_details = Game.query.join(GameDetail).count()
        total_players = PlayerStats.query.count()
        pitchers_with_era = PlayerStats.query.filter(PlayerStats.era.isnot(None)).count()
        
        print(f"總比賽數: {total_games}")
        print(f"有詳細信息的比賽: {games_with_details}")
        print(f"總球員數: {total_players}")
        print(f"有ERA數據的投手: {pitchers_with_era}")
        
        # 2. 選擇一場有完整數據的比賽進行測試
        print("\n🔍 查找測試比賽:")
        
        test_game = Game.query.join(GameDetail).filter(
            GameDetail.home_starting_pitcher.isnot(None),
            GameDetail.away_starting_pitcher.isnot(None),
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).first()
        
        if not test_game:
            print("❌ 未找到合適的測試比賽")
            return
        
        game_detail = GameDetail.query.filter_by(game_id=test_game.game_id).first()
        
        print(f"測試比賽: {test_game.away_team} @ {test_game.home_team}")
        print(f"比賽日期: {test_game.date}")
        print(f"比分: {test_game.away_score} - {test_game.home_score}")
        print(f"主隊先發: {game_detail.home_starting_pitcher}")
        print(f"客隊先發: {game_detail.away_starting_pitcher}")
        
        # 3. 測試特徵提取
        print("\n⚾ 測試投手特徵提取:")
        
        game_data = {
            'home_team': test_game.home_team,
            'away_team': test_game.away_team,
            'date': test_game.date,
            'game_id': test_game.game_id
        }
        
        try:
            features = predictor.get_pitcher_enhanced_features(game_data)
            
            print(f"✅ 成功提取 {len(features)} 個投手特徵")
            
            # 4. 分析先發投手特徵
            print("\n🎯 先發投手特徵分析:")
            
            starter_features = {k: v for k, v in features.items() if 'starter' in k}
            print(f"先發投手特徵數量: {len(starter_features)}")
            
            # 主隊先發投手
            home_starter_features = {k: v for k, v in starter_features.items() if k.startswith('home_')}
            print(f"\n主隊先發投手 ({game_detail.home_starting_pitcher}):")
            for key, value in home_starter_features.items():
                print(f"  {key}: {value}")
            
            # 客隊先發投手
            away_starter_features = {k: v for k, v in starter_features.items() if k.startswith('away_')}
            print(f"\n客隊先發投手 ({game_detail.away_starting_pitcher}):")
            for key, value in away_starter_features.items():
                print(f"  {key}: {value}")
            
            # 5. 分析牛棚特徵
            print("\n🔥 牛棚強度特徵分析:")
            
            bullpen_features = {k: v for k, v in features.items() if 'bullpen' in k}
            print(f"牛棚特徵數量: {len(bullpen_features)}")
            
            for key, value in bullpen_features.items():
                print(f"  {key}: {value}")
            
            # 6. 分析投手對戰特徵
            print("\n⚔️ 投手對戰特徵分析:")
            
            matchup_features = {k: v for k, v in features.items() if 'pitcher_' in k and 'starter' not in k and 'bullpen' not in k}
            
            for key, value in matchup_features.items():
                print(f"  {key}: {value}")
            
            # 7. 分析疲勞度特徵
            print("\n😴 投手疲勞度特徵分析:")
            
            fatigue_features = {k: v for k, v in features.items() if 'fatigue' in k or 'recent_performance' in k}
            
            for key, value in fatigue_features.items():
                print(f"  {key}: {value}")
            
            # 8. 特徵質量評估
            print("\n📈 特徵質量評估:")
            
            # 檢查是否有王牌投手
            home_is_ace = features.get('home_starter_is_ace', 0)
            away_is_ace = features.get('away_starter_is_ace', 0)
            ace_duel = features.get('ace_pitcher_duel', 0)
            
            print(f"主隊有王牌投手: {'是' if home_is_ace else '否'}")
            print(f"客隊有王牌投手: {'是' if away_is_ace else '否'}")
            print(f"王牌投手對決: {'是' if ace_duel else '否'}")
            
            # 投手優勢分析
            matchup_advantage = features.get('pitcher_matchup_advantage', 0)
            if matchup_advantage > 0.5:
                print(f"投手對戰優勢: 主隊投手優勢 ({matchup_advantage:.2f})")
            elif matchup_advantage < -0.5:
                print(f"投手對戰優勢: 客隊投手優勢 ({matchup_advantage:.2f})")
            else:
                print(f"投手對戰優勢: 勢均力敵 ({matchup_advantage:.2f})")
            
            # 預測影響分析
            dominance_factor = features.get('pitcher_dominance_factor', 0)
            if dominance_factor > 0.5:
                print("預測影響: 投手主導，可能低分比賽")
            elif dominance_factor < -0.5:
                print("預測影響: 投手較弱，可能高分比賽")
            else:
                print("預測影響: 投手表現正常")
            
            print("\n✅ 增強投手預測器測試完成!")
            print(f"總特徵數: {len(features)}")
            print("所有投手相關特徵已成功提取並分析")
            
        except Exception as e:
            print(f"❌ 特徵提取失敗: {e}")
            import traceback
            traceback.print_exc()

def test_multiple_games():
    """測試多場比賽的投手特徵提取"""
    
    app = create_app()
    
    with app.app_context():
        print("\n🔄 測試多場比賽投手特徵提取")
        print("=" * 60)
        
        predictor = EnhancedPitcherPredictor()
        
        # 獲取最近5場有完整數據的比賽
        test_games = Game.query.join(GameDetail).filter(
            GameDetail.home_starting_pitcher.isnot(None),
            GameDetail.away_starting_pitcher.isnot(None),
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).order_by(Game.date.desc()).limit(5).all()
        
        print(f"找到 {len(test_games)} 場測試比賽")
        
        success_count = 0
        total_features = 0
        
        for i, game in enumerate(test_games, 1):
            try:
                game_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
                
                game_data = {
                    'home_team': game.home_team,
                    'away_team': game.away_team,
                    'date': game.date,
                    'game_id': game.game_id
                }
                
                features = predictor.get_pitcher_enhanced_features(game_data)
                
                print(f"比賽 {i}: {game.away_team} @ {game.home_team} - ✅ {len(features)} 特徵")
                print(f"  先發: {game_detail.away_starting_pitcher} vs {game_detail.home_starting_pitcher}")
                
                success_count += 1
                total_features += len(features)
                
            except Exception as e:
                print(f"比賽 {i}: {game.away_team} @ {game.home_team} - ❌ 失敗: {e}")
        
        print(f"\n📊 測試結果:")
        print(f"成功率: {success_count}/{len(test_games)} ({success_count/len(test_games)*100:.1f}%)")
        print(f"平均特徵數: {total_features/success_count:.1f}" if success_count > 0 else "平均特徵數: 0")

if __name__ == "__main__":
    print("🚀 開始測試增強投手預測器")
    
    # 測試單場比賽
    test_enhanced_pitcher_predictor()
    
    # 測試多場比賽
    test_multiple_games()
    
    print("\n🎉 所有測試完成!")
