#!/usr/bin/env python3
"""
整月數據管理器
下載整個月的MLB數據，包括比賽、boxscore、投手數據
檢查數據完整性，確保沒有遺漏的場次
"""

import sys
sys.path.append('.')

import requests
import json
import time
import threading
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from collections import defaultdict
import calendar

from models.database import db, Game, Prediction, BettingOdds, PredictionHistory
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from boxscore_data_collector import BoxscoreDataCollector
from team_pitcher_detailed_analyzer import TeamPitcherDetailedAnalyzer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MonthlyDataManager:
    """整月數據管理和完整性檢查器"""
    
    def __init__(self):
        self.mlb_api_base = "https://statsapi.mlb.com/api/v1"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MLB-Prediction-System/1.0'
        })
        
        # 初始化各種數據收集器
        self.odds_fetcher = RealBettingOddsFetcher()
        self.boxscore_collector = BoxscoreDataCollector()
        self.pitcher_analyzer = TeamPitcherDetailedAnalyzer()
        
        # MLB 球隊代碼映射
        self.mlb_teams = {
            'ARI', 'ATL', 'BAL', 'BOS', 'CHC', 'CWS', 'CIN', 'CLE', 'COL', 'DET',
            'HOU', 'KC', 'LAA', 'LAD', 'MIA', 'MIL', 'MIN', 'NYM', 'NYY', 'OAK',
            'PHI', 'PIT', 'SD', 'SF', 'SEA', 'STL', 'TB', 'TEX', 'TOR', 'WSH'
        }
    
    def get_mlb_schedule_for_month(self, year: int, month: int) -> List[Dict]:
        """獲取指定月份的MLB完整賽程"""
        start_date = date(year, month, 1)
        last_day = calendar.monthrange(year, month)[1]
        end_date = date(year, month, last_day)
        
        logger.info(f"獲取 {year}年{month}月 ({start_date} 到 {end_date}) 的MLB賽程...")
        
        try:
            url = f"{self.mlb_api_base}/schedule"
            params = {
                'startDate': start_date.strftime('%Y-%m-%d'),
                'endDate': end_date.strftime('%Y-%m-%d'),
                'sportId': 1,  # MLB
                'gameType': 'R',  # 常規賽
                'hydrate': 'team,linescore,flags,liveLookin,review,broadcasts,decisions,person,probablePitcher,stats,homeRuns,previousPlay,game(content(media(epg),summary),tickets),seriesStatus(useOverride=true)'
            }
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            schedule_data = response.json()
            all_games = []
            
            if 'dates' in schedule_data:
                for date_info in schedule_data['dates']:
                    game_date = date_info.get('date')
                    games = date_info.get('games', [])
                    
                    for game in games:
                        game_info = {
                            'game_pk': str(game.get('gamePk', '')),
                            'game_id': f"{game.get('teams', {}).get('away', {}).get('team', {}).get('abbreviation', '')}_{game.get('teams', {}).get('home', {}).get('team', {}).get('abbreviation', '')}_{game_date.replace('-', '')}",
                            'date': game_date,
                            'home_team': game.get('teams', {}).get('home', {}).get('team', {}).get('abbreviation', ''),
                            'away_team': game.get('teams', {}).get('away', {}).get('team', {}).get('abbreviation', ''),
                            'venue': game.get('venue', {}).get('name', ''),
                            'game_status': game.get('status', {}).get('detailedState', ''),
                            'scheduled_time': game.get('gameDate', ''),
                            'probable_pitchers': {
                                'home': game.get('teams', {}).get('home', {}).get('probablePitcher', {}).get('fullName', ''),
                                'away': game.get('teams', {}).get('away', {}).get('probablePitcher', {}).get('fullName', '')
                            }
                        }
                        
                        # 如果比賽已完成，獲取最終比分
                        if game_info['game_status'] == 'Final':
                            linescore = game.get('linescore', {})
                            if linescore:
                                game_info['final_score'] = {
                                    'home': linescore.get('teams', {}).get('home', {}).get('runs', 0),
                                    'away': linescore.get('teams', {}).get('away', {}).get('runs', 0)
                                }
                        
                        all_games.append(game_info)
            
            logger.info(f"✅ 成功獲取 {len(all_games)} 場比賽的賽程數據")
            return all_games
            
        except Exception as e:
            logger.error(f"獲取MLB賽程失敗: {e}")
            return []
    
    def check_data_completeness(self, year: int, month: int) -> Dict:
        """檢查指定月份的數據完整性"""
        from flask import current_app
        with current_app.app_context():
            logger.info(f"檢查 {year}年{month}月 的數據完整性...")
            
            # 1. 獲取官方賽程
            official_schedule = self.get_mlb_schedule_for_month(year, month)
            
            # 2. 獲取數據庫中的比賽記錄
            start_date = date(year, month, 1)
            last_day = calendar.monthrange(year, month)[1]
            end_date = date(year, month, last_day)
            
            db_games = db.session.query(Game).filter(
                Game.date >= start_date,
                Game.date <= end_date
            ).all()
            
            # 3. 獲取預測記錄
            db_predictions = db.session.query(Prediction).join(
                Game, Prediction.game_id == Game.game_id
            ).filter(
                Game.date >= start_date,
                Game.date <= end_date
            ).all()
            
            # 4. 獲取博彩盤口記錄
            db_odds = db.session.query(BettingOdds).filter(
                BettingOdds.odds_time >= datetime.combine(start_date, datetime.min.time()),
                BettingOdds.odds_time <= datetime.combine(end_date, datetime.max.time())
            ).all()
            
            # 5. 分析完整性
            analysis = {
                'month': f"{year}-{month:02d}",
                'official_games_count': len(official_schedule),
                'db_games_count': len(db_games),
                'predictions_count': len(db_predictions),
                'odds_count': len(db_odds),
                'missing_games': [],
                'missing_predictions': [],
                'missing_odds': [],
                'games_without_boxscore': [],
                'completed_games_analysis': {},
                'data_quality_score': 0.0
            }
            
            # 創建數據庫遊戲映射
            db_games_map = {game.game_id: game for game in db_games}
            db_predictions_map = {pred.game_id: pred for pred in db_predictions}
            odds_by_game = defaultdict(list)
            for odds in db_odds:
                odds_by_game[odds.game_id].append(odds)
            
            # 檢查遺漏的比賽
            for official_game in official_schedule:
                game_id = official_game['game_id']
                
                if game_id not in db_games_map:
                    analysis['missing_games'].append({
                        'game_id': game_id,
                        'date': official_game['date'],
                        'matchup': f"{official_game['away_team']} @ {official_game['home_team']}",
                        'status': official_game['game_status'],
                        'venue': official_game['venue']
                    })
                
                if game_id not in db_predictions_map:
                    analysis['missing_predictions'].append({
                        'game_id': game_id,
                        'date': official_game['date'],
                        'matchup': f"{official_game['away_team']} @ {official_game['home_team']}"
                    })
                
                if game_id not in odds_by_game:
                    analysis['missing_odds'].append({
                        'game_id': game_id,
                        'date': official_game['date'],
                        'matchup': f"{official_game['away_team']} @ {official_game['home_team']}"
                    })
                
                # 檢查已完成比賽是否有boxscore數據
                if official_game['game_status'] == 'Final' and 'final_score' in official_game:
                    db_game = db_games_map.get(game_id)
                    if db_game and (db_game.home_score is None or db_game.away_score is None):
                        analysis['games_without_boxscore'].append({
                            'game_id': game_id,
                            'date': official_game['date'],
                            'matchup': f"{official_game['away_team']} @ {official_game['home_team']}",
                            'official_score': f"{official_game['final_score']['away']} - {official_game['final_score']['home']}"
                        })
            
            # 計算數據質量分數
            total_expected = len(official_schedule)
            if total_expected > 0:
                games_coverage = len(db_games) / total_expected
                predictions_coverage = len(db_predictions) / total_expected
                odds_coverage = len([g for g in official_schedule if g['game_id'] in odds_by_game]) / total_expected
                
                # 已完成比賽的boxscore覆蓋率
                completed_games = [g for g in official_schedule if g['game_status'] == 'Final']
                if completed_games:
                    boxscore_coverage = 1 - (len(analysis['games_without_boxscore']) / len(completed_games))
                else:
                    boxscore_coverage = 1.0
                
                analysis['data_quality_score'] = (
                    games_coverage * 0.3 +
                    predictions_coverage * 0.2 +
                    odds_coverage * 0.2 +
                    boxscore_coverage * 0.3
                ) * 100
                
                analysis['coverage_breakdown'] = {
                    'games_coverage': games_coverage * 100,
                    'predictions_coverage': predictions_coverage * 100,
                    'odds_coverage': odds_coverage * 100,
                    'boxscore_coverage': boxscore_coverage * 100
                }
            
            logger.info(f"✅ 數據完整性檢查完成，質量分數: {analysis['data_quality_score']:.1f}%")
            return analysis
    
    def download_monthly_data(self, year: int, month: int, include_boxscore: bool = True, 
                             max_concurrent: int = 3) -> Dict:
        """下載整月的完整數據"""
        logger.info(f"🚀 開始下載 {year}年{month}月 的完整MLB數據...")
        
        start_time = datetime.now()
        results = {
            'month': f"{year}-{month:02d}",
            'start_time': start_time.isoformat(),
            'games_processed': 0,
            'odds_downloaded': 0,
            'boxscores_downloaded': 0,
            'pitcher_analyses': 0,
            'errors': [],
            'missing_data': [],
            'success': False
        }
        
        try:
            # 1. 獲取官方賽程
            official_schedule = self.get_mlb_schedule_for_month(year, month)
            if not official_schedule:
                results['errors'].append('無法獲取官方賽程')
                return results
            
            results['total_games'] = len(official_schedule)
            
            # 2. 按日期分組下載
            games_by_date = defaultdict(list)
            for game in official_schedule:
                games_by_date[game['date']].append(game)
            
            # 3. 逐日下載數據
            for game_date, daily_games in games_by_date.items():
                try:
                    logger.info(f"📅 處理 {game_date} ({len(daily_games)} 場比賽)...")
                    
                    # 下載基本比賽數據和賠率
                    self._download_daily_basic_data(game_date, daily_games, results)
                    
                    # 如果包含boxscore，下載詳細數據
                    if include_boxscore:
                        self._download_daily_boxscore_data(daily_games, results)
                    
                    # 添加延遲避免API限制
                    time.sleep(1)
                    
                except Exception as e:
                    error_msg = f"處理 {game_date} 失敗: {e}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            # 4. 檢查數據完整性
            completeness = self.check_data_completeness(year, month)
            results['completeness_analysis'] = completeness
            
            # 5. 生成投手分析報告
            if include_boxscore:
                pitcher_analysis = self._generate_pitcher_analysis_for_month(year, month)
                results['pitcher_analysis'] = pitcher_analysis
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            results['end_time'] = end_time.isoformat()
            results['duration_seconds'] = duration
            results['success'] = True
            
            logger.info(f"🎉 {year}年{month}月 數據下載完成！")
            logger.info(f"   處理時間: {duration:.1f}秒")
            logger.info(f"   比賽數據: {results['games_processed']} / {results['total_games']}")
            logger.info(f"   賠率數據: {results['odds_downloaded']} 條")
            logger.info(f"   Boxscore: {results['boxscores_downloaded']} 場")
            logger.info(f"   數據質量: {completeness.get('data_quality_score', 0):.1f}%")
            
        except Exception as e:
            error_msg = f"月度數據下載失敗: {e}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
            results['success'] = False
        
        return results
    
    def _download_daily_basic_data(self, game_date: str, daily_games: List[Dict], results: Dict):
        """下載單日基本數據"""
        try:
            # 使用現有的賠率抓取器
            date_obj = datetime.strptime(game_date, '%Y-%m-%d').date()
            odds_result = self.odds_fetcher.get_mlb_odds_today(date_obj)
            
            if odds_result and odds_result.get('success'):
                # 計算獲取的賠率數據量（基於games數量）
                games_count = len(odds_result.get('games', []))
                odds_count = games_count * 2  # 假設每場比賽平均有2種盤口（讓分+大小分）
                results['odds_downloaded'] += odds_count
                logger.info(f"   ✅ {game_date}: 下載 {games_count} 場比賽的賠率數據")
            else:
                logger.warning(f"   ⚠️ {game_date}: 賠率數據下載失敗")
            
            results['games_processed'] += len(daily_games)
            
        except Exception as e:
            logger.error(f"下載 {game_date} 基本數據失敗: {e}")
    
    def _download_daily_boxscore_data(self, daily_games: List[Dict], results: Dict):
        """下載單日boxscore數據"""
        for game in daily_games:
            try:
                if game['game_status'] == 'Final' and game.get('game_pk'):
                    # 獲取詳細boxscore數據
                    boxscore = self.boxscore_collector.get_game_boxscore(game['game_pk'])
                    
                    if boxscore:
                        # 保存boxscore數據到數據庫
                        self._save_boxscore_to_database(game, boxscore)
                        results['boxscores_downloaded'] += 1
                        logger.debug(f"     ✅ Boxscore: {game['away_team']}@{game['home_team']}")
                    else:
                        results['missing_data'].append({
                            'type': 'boxscore',
                            'game_id': game['game_id'],
                            'reason': 'API無回應或數據不完整'
                        })
                
                # 小延遲避免API過載
                time.sleep(0.2)
                
            except Exception as e:
                logger.error(f"下載 {game['game_id']} boxscore失敗: {e}")
    
    def _save_boxscore_to_database(self, game: Dict, boxscore: Dict):
        """保存boxscore數據到數據庫"""
        try:
            from flask import current_app
            
            # 提取比分數據
            linescore = boxscore.get('linescore', {})
            teams = linescore.get('teams', {})
            
            home_score = teams.get('home', {}).get('runs')
            away_score = teams.get('away', {}).get('runs')
            
            if home_score is not None and away_score is not None:
                # 更新games表的比分
                with current_app.app_context():
                    game_record = db.session.query(Game).filter_by(game_id=game['game_id']).first()
                    
                    if game_record:
                        game_record.home_score = home_score
                        game_record.away_score = away_score
                        game_record.updated_at = datetime.now()
                        
                        # 更新比賽狀態為completed
                        if game_record.game_status != 'completed':
                            game_record.game_status = 'completed'
                        
                        db.session.commit()
                        logger.info(f"     💾 保存比分: {game['away_team']} {away_score}-{home_score} {game['home_team']}")
                    else:
                        logger.warning(f"     ⚠️ 找不到比賽記錄: {game['game_id']}")
            
        except Exception as e:
            logger.error(f"保存boxscore數據失敗 {game['game_id']}: {e}")
    
    def _generate_pitcher_analysis_for_month(self, year: int, month: int) -> Dict:
        """生成月度投手分析報告"""
        from flask import current_app
        with current_app.app_context():
            start_date = date(year, month, 1)
            last_day = calendar.monthrange(year, month)[1]
            end_date = date(year, month, last_day)
            
            # 獲取該月所有已完成的比賽
            completed_games = db.session.query(Game).filter(
                Game.date >= start_date,
                Game.date <= end_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            pitcher_performance = {}
            
            # 分析每場比賽的投手表現
            for game in completed_games:
                try:
                    # 從PredictionHistory獲取投手信息
                    history = db.session.query(PredictionHistory).filter_by(
                        game_id=game.game_id
                    ).first()
                    
                    if history:
                        home_pitcher = history.starting_pitcher_home
                        away_pitcher = history.starting_pitcher_away
                        
                        # 分析主場投手
                        if home_pitcher and home_pitcher not in pitcher_performance:
                            analysis = self.pitcher_analyzer.analyze_pitcher_home_away_performance(
                                home_pitcher, days_back=365
                            )
                            if 'error' not in analysis:
                                pitcher_performance[home_pitcher] = analysis
                        
                        # 分析客場投手
                        if away_pitcher and away_pitcher not in pitcher_performance:
                            analysis = self.pitcher_analyzer.analyze_pitcher_home_away_performance(
                                away_pitcher, days_back=365
                            )
                            if 'error' not in analysis:
                                pitcher_performance[away_pitcher] = analysis
                
                except Exception as e:
                    logger.warning(f"分析比賽 {game.game_id} 投手表現失敗: {e}")
            
            return {
                'month': f"{year}-{month:02d}",
                'completed_games': len(completed_games),
                'analyzed_pitchers': len(pitcher_performance),
                'pitcher_stats': pitcher_performance
            }
    
    def download_and_verify_month(self, year: int, month: int, auto_fix_missing: bool = True) -> Dict:
        """下載並驗證整月數據的完整版本"""
        logger.info(f"🎯 執行 {year}年{month}月 完整數據下載與驗證...")
        
        # 1. 先檢查當前數據狀態
        initial_check = self.check_data_completeness(year, month)
        
        # 2. 下載數據
        download_result = self.download_monthly_data(year, month, include_boxscore=True)
        
        # 3. 再次檢查完整性
        final_check = self.check_data_completeness(year, month)
        
        # 4. 如果啟用自動修復，嘗試補充缺失數據
        if auto_fix_missing and final_check['missing_games']:
            logger.info(f"🔧 嘗試修復 {len(final_check['missing_games'])} 場缺失比賽...")
            self._fix_missing_games(final_check['missing_games'])
            
            # 再次檢查
            final_check = self.check_data_completeness(year, month)
        
        # 5. 生成完整報告
        report = {
            'month': f"{year}-{month:02d}",
            'download_result': download_result,
            'initial_completeness': initial_check,
            'final_completeness': final_check,
            'improvement': {
                'quality_score_improvement': final_check.get('data_quality_score', 0) - initial_check.get('data_quality_score', 0),
                'games_added': final_check['db_games_count'] - initial_check['db_games_count'],
                'predictions_added': final_check['predictions_count'] - initial_check['predictions_count'],
                'odds_added': final_check['odds_count'] - initial_check['odds_count']
            }
        }
        
        return report
    
    def _fix_missing_games(self, missing_games: List[Dict]):
        """嘗試修復缺失的比賽數據"""
        from flask import current_app
        with current_app.app_context():
            for missing in missing_games:
                try:
                    game_date = missing['date']
                    
                    # 重新嘗試獲取該日期的數據
                    logger.info(f"🔧 修復 {game_date} 的 {missing['matchup']} 比賽數據...")
                    
                    # 這裡可以調用修復邏輯
                    # 比如重新調用API、手動創建記錄等
                    
                except Exception as e:
                    logger.error(f"修復 {missing['game_id']} 失敗: {e}")

def main():
    """主函數 - 演示月度數據管理"""
    manager = MonthlyDataManager()
    
    # 檢查當前月份的數據完整性
    current_date = datetime.now()
    year = current_date.year
    month = current_date.month
    
    print(f"🔍 檢查 {year}年{month}月 數據完整性...")
    completeness = manager.check_data_completeness(year, month)
    
    print(f"\n📊 數據完整性報告:")
    print(f"   官方賽程: {completeness['official_games_count']} 場比賽")
    print(f"   數據庫記錄: {completeness['db_games_count']} 場")
    print(f"   預測記錄: {completeness['predictions_count']} 條")
    print(f"   賠率記錄: {completeness['odds_count']} 條")
    print(f"   數據質量分數: {completeness['data_quality_score']:.1f}%")
    
    if completeness['missing_games']:
        print(f"\n❌ 缺失比賽 ({len(completeness['missing_games'])} 場):")
        for missing in completeness['missing_games'][:5]:  # 只顯示前5場
            print(f"   - {missing['date']}: {missing['matchup']} ({missing['status']})")
    
    if completeness['missing_predictions']:
        print(f"\n🎯 缺失預測 ({len(completeness['missing_predictions'])} 場):")
        for missing in completeness['missing_predictions'][:5]:
            print(f"   - {missing['date']}: {missing['matchup']}")
    
    if completeness['games_without_boxscore']:
        print(f"\n📊 缺失Boxscore ({len(completeness['games_without_boxscore'])} 場):")
        for missing in completeness['games_without_boxscore'][:5]:
            print(f"   - {missing['date']}: {missing['matchup']} (實際: {missing['official_score']})")
    
    # 詢問是否要下載完整數據
    print(f"\n🎯 若要下載完整月度數據，請運行:")
    print(f"   manager.download_and_verify_month({year}, {month})")

if __name__ == "__main__":
    main()