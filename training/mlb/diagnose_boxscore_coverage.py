#!/usr/bin/env python3
"""
MLB Boxscore數據覆蓋率診斷工具
檢查比賽結果更新問題和boxscore數據下載狀況
"""

import sys
sys.path.append('.')

import os
from datetime import datetime, timedelta
import sqlite3

def diagnose_boxscore_coverage():
    """診斷boxscore數據覆蓋率和比賽結果更新問題"""
    
    print("🔍 MLB Boxscore 數據覆蓋率診斷")
    print("=" * 60)
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    try:
        # 連接數據庫 - 使用正確的數據庫位置
        db_path = 'instance/mlb_data.db'
        if not os.path.exists(db_path):
            print(f"❌ 數據庫不存在: {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"✅ 已連接數據庫: {db_path}")
        
        # 1. 檢查總體數據統計
        print(f"\n📊 總體數據統計:")
        
        # 檢查games表
        cursor.execute("SELECT COUNT(*) FROM games")
        total_games = cursor.fetchone()[0]
        print(f"   📋 總比賽數: {total_games}")
        
        # 檢查有boxscore的比賽
        cursor.execute("""
            SELECT COUNT(*) FROM games 
            WHERE home_score IS NOT NULL 
            AND away_score IS NOT NULL
        """)
        games_with_scores = cursor.fetchone()[0]
        print(f"   🎯 有比分的比賽: {games_with_scores}")
        
        if total_games > 0:
            coverage_rate = (games_with_scores / total_games) * 100
            print(f"   📈 比分覆蓋率: {coverage_rate:.1f}%")
        else:
            print(f"   ⚠️ 沒有比賽數據")
            return False
        
        # 2. 檢查最近7天的數據
        print(f"\n📅 最近7天數據分析:")
        
        seven_days_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        today = datetime.now().strftime('%Y-%m-%d')
        
        cursor.execute("""
            SELECT 
                date,
                COUNT(*) as total_games,
                SUM(CASE WHEN home_score IS NOT NULL AND away_score IS NOT NULL THEN 1 ELSE 0 END) as games_with_scores,
                SUM(CASE WHEN game_status = 'Final' THEN 1 ELSE 0 END) as final_games
            FROM games 
            WHERE date BETWEEN ? AND ?
            GROUP BY date
            ORDER BY date DESC
        """, (seven_days_ago, today))
        
        recent_data = cursor.fetchall()
        
        if recent_data:
            print(f"   日期       | 總比賽 | 有比分 | 已結束 | 覆蓋率")
            print(f"   " + "-" * 50)
            for date, total, with_scores, final_games in recent_data:
                rate = (with_scores / final_games * 100) if final_games > 0 else 0
                print(f"   {date} | {total:6d} | {with_scores:6d} | {final_games:6d} | {rate:5.1f}%")
        else:
            print(f"   ⚠️ 最近7天沒有比賽數據")
        
        # 3. 檢查缺失boxscore的具體比賽
        print(f"\n🎯 缺失boxscore的比賽 (最近30天已結束比賽):")
        
        thirty_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        cursor.execute("""
            SELECT game_id, date, home_team, away_team, game_status
            FROM games 
            WHERE date BETWEEN ? AND ?
            AND (home_score IS NULL OR away_score IS NULL)
            AND game_status = 'Final'
            ORDER BY date DESC, game_id
        """, (thirty_days_ago, today))
        
        missing_boxscores = cursor.fetchall()
        
        if missing_boxscores:
            print(f"   發現 {len(missing_boxscores)} 場已結束比賽缺失boxscore:")
            for game_id, date, home, away, status in missing_boxscores[:10]:  # 顯示前10場
                print(f"   🏟️ {date} | {away} vs {home} | 狀態: {status} | ID: {game_id}")
            
            if len(missing_boxscores) > 10:
                print(f"   ... 還有 {len(missing_boxscores) - 10} 場比賽缺失boxscore")
        else:
            print(f"   ✅ 最近30天所有已結束比賽都有boxscore數據")
        
        # 4. 檢查betting_odds數據
        print(f"\n💰 賠率數據檢查:")
        
        cursor.execute("SELECT COUNT(*) FROM betting_odds")
        total_odds = cursor.fetchone()[0]
        print(f"   📊 總賠率記錄: {total_odds}")
        
        # 檢查最近的賠率數據
        cursor.execute("""
            SELECT COUNT(DISTINCT game_id) FROM betting_odds 
            WHERE DATE(created_at) >= ?
        """, (thirty_days_ago,))
        recent_odds_games = cursor.fetchone()[0]
        print(f"   🎯 最近30天有賠率的比賽: {recent_odds_games}")
        
        # 5. 檢查任務進度記錄
        print(f"\n📋 檢查進度監控記錄:")
        
        progress_file = 'data/task_progress.json'
        if os.path.exists(progress_file):
            file_size = os.path.getsize(progress_file)
            print(f"   ✅ 進度文件存在: {progress_file} ({file_size} bytes)")
            
            # 讀取進度文件
            import json
            try:
                with open(progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                
                print(f"   📊 進度記錄數: {len(progress_data)}")
                
                # 查找boxscore相關任務
                boxscore_tasks = [
                    task_id for task_id, task in progress_data.items()
                    if 'boxscore' in task.get('task_name', '').lower() or 
                       'boxscore' in str(task.get('messages', [])).lower()
                ]
                
                if boxscore_tasks:
                    print(f"   🎯 發現 {len(boxscore_tasks)} 個boxscore相關任務:")
                    for task_id in boxscore_tasks[:3]:  # 顯示前3個
                        task = progress_data[task_id]
                        print(f"      📝 {task.get('task_name', 'Unknown')} - {task.get('status', 'unknown')}")
                else:
                    print(f"   ⚠️ 沒有發現boxscore相關的任務記錄")
                    
            except Exception as e:
                print(f"   ❌ 讀取進度文件失敗: {e}")
        else:
            print(f"   ❌ 進度文件不存在: {progress_file}")
        
        # 6. 檢查數據獲取配置
        print(f"\n⚙️ 檢查數據獲取配置:")
        
        try:
            from app import app
            with app.app_context():
                from monthly_data_manager import MonthlyDataManager
                
                manager = MonthlyDataManager()
                print(f"   ✅ MonthlyDataManager 初始化成功")
                
                # 檢查是否有boxscore獲取功能
                if hasattr(manager, 'download_missing_boxscores'):
                    print(f"   ✅ 包含boxscore下載功能")
                else:
                    print(f"   ❌ 缺少boxscore下載功能")
                
                # 檢查MLB API配置
                if hasattr(manager, 'mlb_api'):
                    print(f"   ✅ MLB API已配置")
                else:
                    print(f"   ⚠️ MLB API未配置")
                    
        except Exception as e:
            print(f"   ❌ 檢查配置失敗: {e}")
        
        # 7. 生成診斷摘要
        print(f"\n🎯 診斷摘要:")
        print(f"   📊 總比賽數: {total_games}")
        print(f"   ✅ 有比分比賽: {games_with_scores}")
        print(f"   📈 整體覆蓋率: {coverage_rate:.1f}%")
        print(f"   ⚠️ 缺失boxscore: {len(missing_boxscores)} 場")
        
        # 8. 生成建議
        print(f"\n💡 建議修復方案:")
        
        if coverage_rate < 95:
            print(f"   🔧 覆蓋率偏低，建議:")
            print(f"      1. 檢查MLB API連接狀態")
            print(f"      2. 運行缺失boxscore修復程序")
            print(f"      3. 增強錯誤重試機制")
        
        if len(missing_boxscores) > 0:
            print(f"   🎯 針對 {len(missing_boxscores)} 場缺失比賽:")
            print(f"      1. 運行批量boxscore下載")
            print(f"      2. 檢查比賽狀態是否為已結束")
            print(f"      3. 驗證API訪問權限")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 診斷失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 MLB Boxscore 覆蓋率診斷工具")
    print("檢查比賽結果更新問題和數據完整性")
    print("")
    
    success = diagnose_boxscore_coverage()
    
    if success:
        print(f"\n✅ 診斷完成")
        print(f"💡 根據診斷結果執行相應的修復操作")
    else:
        print(f"\n❌ 診斷失敗，請檢查系統配置")