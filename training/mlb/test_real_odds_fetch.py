#!/usr/bin/env python3
"""
測試真實博彩盤口數據獲取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from datetime import datetime, date
import logging
import sqlite3
import os

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_real_odds_fetch():
    """測試真實博彩數據獲取"""
    print("🎯 測試真實博彩盤口數據獲取")
    print("=" * 50)
    
    try:
        # 初始化獲取器
        fetcher = RealBettingOddsFetcher()
        
        # 測試獲取今天的數據
        today = date.today()
        print(f"📅 獲取日期: {today}")
        
        # 獲取MLB賽事
        odds_data = fetcher.get_mlb_odds_today(target_date=today)
        
        if odds_data and odds_data.get('games'):
            games = odds_data.get('games', [])
            print(f"✅ 成功獲取 {len(games)} 場比賽的博彩數據")

            # 顯示前3場比賽的數據
            for i, game_odds in enumerate(games[:3]):
                print(f"\n🏟️  比賽 {i+1}:")
                print(f"   主隊: {game_odds.get('home_team', 'N/A')}")
                print(f"   客隊: {game_odds.get('away_team', 'N/A')}")
                print(f"   開始時間: {game_odds.get('commence_time', 'N/A')}")

                # 顯示博彩盤口
                odds = game_odds.get('odds', {})
                total_odds = odds.get('total', {})
                if total_odds:
                    line = total_odds.get('line', 'N/A')
                    over_odds = total_odds.get('over_odds', 'N/A')
                    bookmaker = total_odds.get('bookmaker', 'N/A')
                    print(f"   📊 {bookmaker}: 大小分 {line} (Over賠率: {over_odds})")
        else:
            print("❌ 沒有獲取到博彩數據")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_save_real_odds():
    """測試保存真實博彩數據到數據庫"""
    print("\n💾 測試保存真實博彩數據")
    print("=" * 50)

    try:
        # 初始化
        fetcher = RealBettingOddsFetcher()

        # 連接數據庫
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.join(basedir, "instance", "mlb_data.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 獲取今天的數據
        today = date.today()
        odds_data = fetcher.get_mlb_odds_today(target_date=today)

        if not odds_data:
            print("❌ 沒有數據可保存")
            conn.close()
            return

        saved_count = 0
        games = odds_data.get('games', [])

        for game_odds in games:
            home_team = game_odds.get('home_team', '')
            away_team = game_odds.get('away_team', '')

            # 查找對應的game_id
            cursor.execute("""
                SELECT game_id FROM games
                WHERE away_team = ? AND home_team = ? AND date = ?
            """, (away_team, home_team, str(today)))

            result = cursor.fetchone()
            if not result:
                print(f"⚠️  找不到比賽: {away_team} @ {home_team}")
                continue

            game_id = result[0]

            # 保存博彩數據
            odds = game_odds.get('odds', {})
            total_odds = odds.get('total', {})

            if total_odds and total_odds.get('line'):
                total_point = total_odds.get('line')
                bookmaker_name = total_odds.get('bookmaker', 'Unknown')

                # 保存到數據庫
                cursor.execute("""
                    INSERT OR REPLACE INTO betting_odds
                    (game_id, bookmaker, market_type, total_point, odds_time, created_at, updated_at)
                    VALUES (?, ?, 'totals', ?, ?, ?, ?)
                """, (
                    game_id,
                    bookmaker_name,
                    float(total_point),
                    datetime.now().isoformat(),
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
                saved_count += 1
                print(f"✅ 保存: {away_team}@{home_team} - {bookmaker_name}: {total_point}")

        conn.commit()
        conn.close()
        print(f"\n💾 總共保存了 {saved_count} 個博彩盤口數據")

    except Exception as e:
        print(f"❌ 保存失敗: {e}")
        import traceback
        traceback.print_exc()

def check_saved_odds():
    """檢查已保存的博彩數據"""
    print("\n🔍 檢查已保存的博彩數據")
    print("=" * 50)

    try:
        # 連接數據庫
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.join(basedir, "instance", "mlb_data.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 查詢今天的博彩數據
        today = date.today()

        query = """
        SELECT
            g.away_team || '@' || g.home_team as matchup,
            bo.bookmaker,
            bo.total_point,
            bo.odds_time
        FROM betting_odds bo
        JOIN games g ON bo.game_id = g.game_id
        WHERE g.date = ? AND bo.bookmaker != 'estimated'
        ORDER BY g.game_id, bo.bookmaker
        """

        cursor.execute(query, (str(today),))
        results = cursor.fetchall()

        if results:
            print(f"找到 {len(results)} 個真實博彩數據:")
            for matchup, bookmaker, total_point, odds_time in results:
                print(f"  {matchup} - {bookmaker}: {total_point}")
        else:
            print("❌ 沒有找到真實博彩數據")

        conn.close()

    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

if __name__ == '__main__':
    test_real_odds_fetch()
    test_save_real_odds()
    check_saved_odds()
