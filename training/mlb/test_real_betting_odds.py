#!/usr/bin/env python3
"""
測試真實博彩盤口獲取系統
驗證大小分和讓分盤的真實數據來源
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from flask import Flask
from models.database import db, Game
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from models.over_under_predictor import OverUnderPredictor
from models.run_line_predictor import RunLinePredictor

def create_test_app():
    """創建測試Flask應用"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///mlb_data.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    return app

def test_betting_odds_fetcher():
    """測試博彩盤口獲取器"""
    print("=" * 80)
    print("🎰 測試真實博彩盤口獲取器")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        # 1. 檢查API狀態
        print("\n📊 檢查API狀態...")
        status = fetcher.check_api_status()
        print(f"API狀態: {status['message']}")
        print(f"API密鑰配置: {'是' if status['api_key_configured'] else '否'}")
        print(f"API可訪問: {'是' if status['api_accessible'] else '否'}")
        
        # 2. 獲取今日盤口
        print(f"\n📈 獲取今日MLB博彩盤口...")
        odds_data = fetcher.get_mlb_odds_today()
        
        print(f"比賽數量: {odds_data['summary']['total_games']}")
        print(f"可用市場: {', '.join(odds_data['summary']['markets_available'])}")
        print(f"數據來源: {'真實博彩商' if not odds_data['summary'].get('note') else '模擬數據'}")
        
        # 3. 顯示前3場比賽的詳細盤口
        print(f"\n🏟️  前3場比賽的博彩盤口:")
        for i, game in enumerate(odds_data['games'][:3]):
            print(f"\n比賽 {i+1}: {game['away_team']} @ {game['home_team']}")
            
            # 勝負盤
            ml = game['odds']['moneyline']
            if ml['home'] and ml['away']:
                print(f"  勝負盤: 主隊 {ml['home']} | 客隊 {ml['away']} ({ml['bookmaker']})")
            else:
                print(f"  勝負盤: 無數據")
            
            # 讓分盤
            rl = game['odds']['run_line']
            if rl['home_line'] and rl['away_line']:
                print(f"  讓分盤: 主隊 {rl['home_line']} ({rl['home_odds']}) | 客隊 {rl['away_line']} ({rl['away_odds']}) ({rl['bookmaker']})")
            else:
                print(f"  讓分盤: 無數據")
            
            # 大小分
            total = game['odds']['total']
            if total['line']:
                print(f"  大小分: {total['line']} (大分 {total['over_odds']} | 小分 {total['under_odds']}) ({total['bookmaker']})")
            else:
                print(f"  大小分: 無數據")
        
        return odds_data

def test_over_under_predictor():
    """測試大小分預測器"""
    print("\n" + "=" * 80)
    print("🎯 測試大小分預測器 (使用真實盤口)")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        predictor = OverUnderPredictor(app)
        
        # 獲取今日比賽
        today_games = Game.query.filter(
            Game.date == date.today(),
            Game.game_status.in_(['scheduled', 'in_progress'])
        ).limit(2).all()
        
        if not today_games:
            print("❌ 今日沒有可預測的比賽")
            return
        
        for i, game in enumerate(today_games):
            print(f"\n🏟️  測試比賽 {i+1}: {game.away_team} @ {game.home_team}")
            
            result = predictor.predict_over_under(game.game_id)
            
            if 'error' in result:
                print(f"❌ 預測失敗: {result['error']}")
            else:
                print(f"✅ 預測成功")
                print(f"大小分盤口: {result['total_line']}")
                print(f"盤口來源: {'真實博彩商' if result.get('real_odds_used') else '算法計算'}")
                print(f"預期總得分: {result['expected_runs']['total']:.1f}")
                print(f"大分概率: {result['over_probability']:.1%}")
                print(f"小分概率: {result['under_probability']:.1%}")
                print(f"推薦: {result['recommendation']}")

def test_run_line_predictor():
    """測試讓分盤預測器"""
    print("\n" + "=" * 80)
    print("🎯 測試讓分盤預測器 (使用真實盤口)")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        predictor = RunLinePredictor(app)
        
        # 獲取今日比賽
        today_games = Game.query.filter(
            Game.date == date.today(),
            Game.game_status.in_(['scheduled', 'in_progress'])
        ).limit(2).all()
        
        if not today_games:
            print("❌ 今日沒有可預測的比賽")
            return
        
        for i, game in enumerate(today_games):
            print(f"\n🏟️  測試比賽 {i+1}: {game.away_team} @ {game.home_team}")
            
            result = predictor.predict_run_line(game.game_id)
            
            if 'error' in result:
                print(f"❌ 預測失敗: {result['error']}")
            else:
                print(f"✅ 預測成功")
                run_line_data = result['run_line_data']
                prediction = result['prediction']
                
                print(f"讓分盤: 主隊 {run_line_data['home_line']} | 客隊 {run_line_data['away_line']}")
                print(f"盤口來源: {run_line_data['source']} ({run_line_data['bookmaker']})")
                print(f"主隊讓分勝率: {prediction['home_cover_probability']:.1%}")
                print(f"客隊受讓勝率: {prediction['away_cover_probability']:.1%}")
                print(f"推薦: {prediction['recommendation']}")
                print(f"信心度: {prediction['confidence']:.1%}")

def analyze_odds_source():
    """分析盤口數據來源"""
    print("\n" + "=" * 80)
    print("📊 分析盤口數據來源")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        # 檢查是否有API密鑰
        status = fetcher.check_api_status()
        
        print(f"API密鑰狀態: {'已配置' if status['api_key_configured'] else '未配置'}")
        
        if not status['api_key_configured']:
            print("\n⚠️  重要說明:")
            print("1. 當前使用模擬博彩盤口數據")
            print("2. 大小分盤口 9.0 是算法計算的結果，不是真實博彩商盤口")
            print("3. 讓分盤 ±1.5 是MLB標準設定，不是真實博彩商盤口")
            print("\n🔧 如需真實盤口數據，請:")
            print("1. 註冊 The Odds API (https://the-odds-api.com/)")
            print("2. 獲取API密鑰")
            print("3. 在系統中配置API密鑰")
            print("4. 真實盤口包括: FanDuel, DraftKings, BetMGM 等博彩商")
        else:
            print("✅ 已配置真實博彩API，可獲取實際盤口數據")
        
        # 獲取今日數據並分析
        odds_data = fetcher.get_mlb_odds_today()
        
        print(f"\n📈 今日數據分析:")
        print(f"比賽數量: {odds_data['summary']['total_games']}")
        
        if odds_data['summary'].get('note'):
            print(f"數據類型: 模擬數據")
            print(f"說明: {odds_data['summary']['note']}")
        else:
            print(f"數據類型: 真實博彩商數據")
            print(f"博彩商數量: {odds_data['summary']['bookmakers_count']}")

def main():
    """主測試函數"""
    print("🎰 MLB真實博彩盤口系統測試")
    print("測試時間:", date.today().strftime('%Y-%m-%d'))
    
    try:
        # 1. 測試博彩盤口獲取器
        odds_data = test_betting_odds_fetcher()
        
        # 2. 測試大小分預測器
        test_over_under_predictor()
        
        # 3. 測試讓分盤預測器
        test_run_line_predictor()
        
        # 4. 分析數據來源
        analyze_odds_source()
        
        print("\n" + "=" * 80)
        print("✅ 所有測試完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
