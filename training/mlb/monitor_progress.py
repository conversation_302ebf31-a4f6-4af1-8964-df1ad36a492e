#!/usr/bin/env python3
"""
MLB 進度監控命令行工具
使用方法:
  python monitor_progress.py --watch    # 持續監控模式
  python monitor_progress.py            # 一次性查看當前狀態
  python monitor_progress.py --clean    # 清理舊任務
"""

import sys
sys.path.append('.')

import argparse
from progress_monitor import progress_monitor

def main():
    """命令行進度監控入口"""
    parser = argparse.ArgumentParser(
        description="MLB 系統進度監控",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用範例:
  python monitor_progress.py              # 查看當前所有任務狀態
  python monitor_progress.py --watch      # 持續監控模式 (每3秒刷新)
  python monitor_progress.py --clean      # 清理24小時前完成的任務
  python monitor_progress.py -w -i 5      # 持續監控，每5秒刷新
        """
    )
    
    parser.add_argument(
        '--watch', '-w', 
        action='store_true', 
        help='持續監控模式，自動刷新顯示'
    )
    
    parser.add_argument(
        '--clean', '-c', 
        action='store_true', 
        help='清理已完成的舊任務 (默認24小時前)'
    )
    
    parser.add_argument(
        '--interval', '-i', 
        type=int, 
        default=3, 
        help='刷新間隔秒數 (默認: 3秒)'
    )
    
    args = parser.parse_args()
    
    # 加載現有進度數據
    progress_monitor.load_progress()
    
    # 執行對應操作
    if args.clean:
        print("🧹 清理舊任務...")
        progress_monitor.clean_completed_tasks()
        print("✅ 清理完成")
    
    if args.watch:
        print("🔍 開始持續監控模式...")
        print("   按 Ctrl+C 可退出監控")
        progress_monitor.watch_tasks(args.interval)
    else:
        # 一次性顯示狀態
        progress_monitor.display_status()
        
        # 如果有正在運行的任務，提示用戶可以使用監控模式
        running_tasks = progress_monitor.get_running_tasks()
        if running_tasks:
            print(f"\n💡 發現 {len(running_tasks)} 個正在運行的任務")
            print("   使用 'python monitor_progress.py --watch' 可持續監控進度")

if __name__ == "__main__":
    main()