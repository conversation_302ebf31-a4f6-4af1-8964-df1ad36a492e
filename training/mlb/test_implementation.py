#!/usr/bin/env python3
"""
MLB 預測系統實施測試
測試所有增強模組的基本功能

目標：
- 驗證所有模組能正常運作
- 測試基本預測流程
- 檢查結果合理性
"""

import os
import sys
import json
import numpy as np
from datetime import datetime, date
from typing import Dict, List
import logging

# 設置路徑
project_root = os.path.dirname(__file__)
sys.path.append(os.path.join(project_root, 'models', 'enhanced_prediction'))

# 導入模組
from score_calibration import ScoreCalibrationModule
from confidence_calculator import ConfidenceCalculator
from win_prediction import EnhancedWinPrediction
from pitcher_analyzer import PitcherImpactAnalyzer
from feature_weights import DynamicFeatureWeights
from ensemble_model import EnsemblePredictionModel

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_score_calibration():
    """測試分數校正模組"""
    print("\n1. 測試分數校正模組...")
    
    calibrator = ScoreCalibrationModule()
    
    # 測試案例
    test_cases = [
        (5.2, 4.8, 'LAD', 'SF'),
        (3.5, 6.1, 'NYY', 'BOS'),
        (4.0, 4.0, 'HOU', 'SEA')
    ]
    
    results = []
    for home_score, away_score, home_team, away_team in test_cases:
        try:
            result = calibrator.calibrate_scores(home_score, away_score, home_team, away_team)
            success = (result is not None and 
                      'calibrated_home_score' in result and 
                      'calibrated_away_score' in result)
            results.append(success)
            
            if success:
                print(f"  ✅ {home_team} {home_score} → {result['calibrated_home_score']:.2f}, " +
                      f"{away_team} {away_score} → {result['calibrated_away_score']:.2f}")
            else:
                print(f"  ❌ 校正失敗: {home_team} vs {away_team}")
                
        except Exception as e:
            print(f"  ❌ 錯誤: {e}")
            results.append(False)
    
    success_rate = np.mean(results)
    print(f"  成功率: {success_rate:.2%}")
    return success_rate

def test_confidence_calculator():
    """測試信心度計算器"""
    print("\n2. 測試信心度計算器...")
    
    calculator = ConfidenceCalculator()
    
    # 測試案例
    test_cases = [
        {'home_score': 5.2, 'away_score': 4.8, 'win_probability': 0.65},
        {'home_score': 3.5, 'away_score': 6.1, 'win_probability': 0.35},
        {'home_score': 4.0, 'away_score': 4.0, 'win_probability': 0.54}
    ]
    
    results = []
    for i, prediction_data in enumerate(test_cases):
        try:
            confidence = calculator.calculate_confidence(
                prediction_data, 
                {'accuracy': 0.55, 'recent_accuracy': 0.60}
            )
            
            success = 0 <= confidence <= 1
            results.append(success)
            
            if success:
                print(f"  ✅ 案例 {i+1}: 信心度 = {confidence:.3f}")
            else:
                print(f"  ❌ 案例 {i+1}: 無效信心度 = {confidence}")
                
        except Exception as e:
            print(f"  ❌ 錯誤: {e}")
            results.append(False)
    
    success_rate = np.mean(results)
    print(f"  成功率: {success_rate:.2%}")
    return success_rate

def test_win_prediction():
    """測試勝負預測器"""
    print("\n3. 測試勝負預測器...")
    
    predictor = EnhancedWinPrediction()
    
    # 測試案例
    test_cases = [
        {
            'home_score': 5.2, 'away_score': 4.8,
            'features': {
                'home_team': 'LAD', 'away_team': 'SF',
                'game_date': date(2024, 6, 15),
                'game_importance': 'regular'
            }
        },
        {
            'home_score': 3.5, 'away_score': 6.1,
            'features': {
                'home_team': 'NYY', 'away_team': 'BOS',
                'game_date': date(2024, 8, 20),
                'game_importance': 'division_critical'
            }
        }
    ]
    
    results = []
    for i, case in enumerate(test_cases):
        try:
            prediction = predictor.predict_winner(
                case['home_score'], case['away_score'], 
                case['features']
            )
            
            required_fields = ['predicted_winner', 'home_win_probability', 'confidence_level']
            success = all(field in prediction for field in required_fields)
            results.append(success)
            
            if success:
                winner = prediction['predicted_winner']
                prob = prediction['home_win_probability']
                conf = prediction['confidence_level']
                print(f"  ✅ 案例 {i+1}: {winner} 獲勝 (主隊勝率: {prob:.3f}, 信心: {conf})")
            else:
                print(f"  ❌ 案例 {i+1}: 預測結果不完整")
                
        except Exception as e:
            print(f"  ❌ 錯誤: {e}")
            results.append(False)
    
    success_rate = np.mean(results)
    print(f"  成功率: {success_rate:.2%}")
    return success_rate

def test_dynamic_weights():
    """測試動態特徵權重"""
    print("\n4. 測試動態特徵權重...")
    
    weight_system = DynamicFeatureWeights()
    
    # 測試不同情境
    test_contexts = [
        {
            'home_team': 'LAD', 'away_team': 'SF',
            'game_date': date(2024, 6, 15),
            'pitcher_data': {
                'home_pitcher': {'era': 2.85, 'whip': 1.10, 'k9': 10.5},
                'away_pitcher': {'era': 4.20, 'whip': 1.35, 'k9': 8.2}
            }
        },
        {
            'home_team': 'COL', 'away_team': 'LAD',
            'game_date': date(2024, 9, 5),
            'weather': {'temperature': 45, 'wind_speed': 18, 'precipitation_chance': 60}
        }
    ]
    
    results = []
    for i, context in enumerate(test_contexts):
        try:
            weights = weight_system.adjust_weights(context)
            
            # 檢查權重總和是否接近1.0
            weight_sum = sum(weights.values())
            success = abs(weight_sum - 1.0) < 0.01
            results.append(success)
            
            if success:
                print(f"  ✅ 情境 {i+1}: 權重總和 = {weight_sum:.3f}")
                for feature, weight in weights.items():
                    print(f"    {feature}: {weight:.3f}")
            else:
                print(f"  ❌ 情境 {i+1}: 權重總和異常 = {weight_sum:.3f}")
                
        except Exception as e:
            print(f"  ❌ 錯誤: {e}")
            results.append(False)
    
    success_rate = np.mean(results)
    print(f"  成功率: {success_rate:.2%}")
    return success_rate

def test_pitcher_analyzer():
    """測試投手分析器"""
    print("\n5. 測試投手分析器...")
    
    analyzer = PitcherImpactAnalyzer()
    
    # 創建模擬投手數據
    test_cases = [
        {
            'home_pitcher': {
                'era': 3.20, 'whip': 1.15, 'k9': 9.5, 'wins': 12, 'losses': 6,
                'recent_games': [{'era': 2.80, 'innings': 7}, {'era': 4.20, 'innings': 6}]
            },
            'away_pitcher': {
                'era': 3.80, 'whip': 1.25, 'k9': 8.8, 'wins': 10, 'losses': 8,
                'recent_games': [{'era': 3.60, 'innings': 6.5}, {'era': 4.00, 'innings': 5}]
            }
        },
        {
            'home_pitcher': {
                'era': 2.85, 'whip': 1.10, 'k9': 10.5, 'wins': 15, 'losses': 4,
                'recent_games': [{'era': 1.80, 'innings': 8}, {'era': 3.20, 'innings': 7}]
            },
            'away_pitcher': {
                'era': 4.20, 'whip': 1.35, 'k9': 8.2, 'wins': 8, 'losses': 10,
                'recent_games': [{'era': 5.40, 'innings': 5}, {'era': 3.80, 'innings': 6}]
            }
        }
    ]
    
    results = []
    for i, case in enumerate(test_cases):
        try:
            impact = analyzer.analyze_pitcher_impact(
                case['home_pitcher'], 
                case['away_pitcher']
            )
            
            required_fields = ['home_pitcher_analysis', 'away_pitcher_analysis', 'matchup_summary']
            success = all(field in impact for field in required_fields)
            results.append(success)
            
            if success:
                print(f"  ✅ 案例 {i+1}: 分析完成")
                summary = impact['matchup_summary']
                advantage = summary.get('advantage', '未知')
                print(f"    投手優勢: {advantage}")
            else:
                print(f"  ❌ 案例 {i+1}: 分析結果不完整")
                
        except Exception as e:
            print(f"  ❌ 錯誤: {e}")
            results.append(False)
    
    success_rate = np.mean(results)
    print(f"  成功率: {success_rate:.2%}")
    return success_rate

def test_ensemble_model():
    """測試集成模型"""
    print("\n6. 測試集成學習模型...")
    
    ensemble = EnsemblePredictionModel()
    
    try:
        # 生成模擬訓練數據
        n_samples = 100
        n_features = 10
        
        X = np.random.randn(n_samples, n_features)
        y_home = np.random.randn(n_samples) + 4.5  # 主隊得分
        y_away = np.random.randn(n_samples) + 4.2  # 客隊得分
        
        training_data = {
            'features': X,
            'target_home_score': y_home,
            'target_away_score': y_away
        }
        
        # 測試訓練過程
        result = ensemble.train_ensemble(training_data, ['home_score', 'away_score'])
        success = result.get('success', False)
        
        if success:
            print(f"  ✅ 模型訓練成功")
            print(f"    訓練樣本數: {n_samples}")
            print(f"    特徵維度: {n_features}")
        else:
            print(f"  ❌ 模型訓練失敗")
            
        return 1.0 if success else 0.0
        
    except Exception as e:
        print(f"  ❌ 錯誤: {e}")
        return 0.0

def run_integration_test():
    """執行整合測試"""
    print("\n7. 整合測試 - 完整預測流程...")
    
    try:
        # 初始化所有模組
        calibrator = ScoreCalibrationModule()
        confidence_calc = ConfidenceCalculator()
        win_predictor = EnhancedWinPrediction()
        weight_system = DynamicFeatureWeights()
        
        # 測試案例
        original_home_score = 5.2
        original_away_score = 4.8
        home_team = 'LAD'
        away_team = 'SF'
        
        features = {
            'home_team': home_team,
            'away_team': away_team,
            'game_date': date(2024, 6, 15),
            'game_importance': 'regular'
        }
        
        game_context = {
            'home_team': home_team,
            'away_team': away_team,
            'game_date': date(2024, 6, 15),
            'pitcher_data': {
                'home_pitcher': {'era': 3.20, 'whip': 1.15, 'k9': 9.5},
                'away_pitcher': {'era': 3.80, 'whip': 1.25, 'k9': 8.8}
            }
        }
        
        print(f"  原始預測: {home_team} {original_home_score} - {away_team} {original_away_score}")
        
        # 步驟1: 分數校正
        calibrated = calibrator.calibrate_scores(
            original_home_score, original_away_score, home_team, away_team
        )
        print(f"  校正後分數: {calibrated['calibrated_home_score']:.2f} - {calibrated['calibrated_away_score']:.2f}")
        
        # 步驟2: 動態權重調整
        weights = weight_system.adjust_weights(game_context)
        print(f"  權重調整完成 (總和: {sum(weights.values()):.3f})")
        
        # 步驟3: 勝負預測
        prediction = win_predictor.predict_winner(
            calibrated['calibrated_home_score'],
            calibrated['calibrated_away_score'],
            features
        )
        print(f"  預測獲勝: {prediction['predicted_winner']}")
        print(f"  主隊勝率: {prediction['home_win_probability']:.3f}")
        print(f"  信心等級: {prediction['confidence_level']}")
        
        # 步驟4: 信心度計算
        confidence = confidence_calc.calculate_confidence(
            {
                'home_score': calibrated['calibrated_home_score'],
                'away_score': calibrated['calibrated_away_score'],
                'win_probability': prediction['home_win_probability']
            },
            {'accuracy': 0.55}
        )
        print(f"  最終信心度: {confidence:.3f}")
        
        print(f"  ✅ 整合測試成功!")
        return 1.0
        
    except Exception as e:
        print(f"  ❌ 整合測試失敗: {e}")
        return 0.0

def main():
    """主程式"""
    print("=" * 60)
    print("MLB 預測系統增強模組測試")
    print("=" * 60)
    
    # 執行所有測試
    test_results = []
    
    test_results.append(test_score_calibration())
    test_results.append(test_confidence_calculator())
    test_results.append(test_win_prediction())
    test_results.append(test_dynamic_weights())
    test_results.append(test_pitcher_analyzer())
    test_results.append(test_ensemble_model())
    test_results.append(run_integration_test())
    
    # 計算總體結果
    overall_success = np.mean(test_results)
    
    print("\n" + "=" * 60)
    print("測試結果摘要")
    print("=" * 60)
    print(f"總體成功率: {overall_success:.2%}")
    
    if overall_success >= 0.8:
        print("🎉 測試通過! 系統準備就緒")
        status = "通過"
    elif overall_success >= 0.6:
        print("⚠️  部分測試通過，需要改善")
        status = "部分通過"
    else:
        print("❌ 測試失敗，需要修復問題")
        status = "失敗"
    
    # 儲存測試結果
    result_summary = {
        'timestamp': datetime.now().isoformat(),
        'overall_success_rate': overall_success,
        'status': status,
        'individual_results': {
            'score_calibration': test_results[0],
            'confidence_calculator': test_results[1],
            'win_prediction': test_results[2],
            'dynamic_weights': test_results[3],
            'pitcher_analyzer': test_results[4],
            'ensemble_model': test_results[5],
            'integration_test': test_results[6]
        }
    }
    
    with open('implementation_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(result_summary, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"測試結果已儲存至: implementation_test_results.json")

if __name__ == "__main__":
    main()