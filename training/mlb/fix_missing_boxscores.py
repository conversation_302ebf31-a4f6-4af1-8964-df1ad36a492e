#!/usr/bin/env python3
"""
修復缺失的boxscore數據
針對839場已完成但完全缺失比分的比賽，從MLB API獲取並保存boxscore數據
"""

import sys
sys.path.append('.')

import os
import sqlite3
import requests
import json
import time
from datetime import datetime, timed<PERSON><PERSON>

def fix_missing_boxscores():
    """修復缺失的boxscore數據"""
    
    print("🔧 修復缺失的boxscore數據")
    print("=" * 60)
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    try:
        # 連接數據庫
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        print("✅ 已連接數據庫: instance/mlb_data.db")
        
        # 1. 查找完全缺失比分的已完成比賽
        print("\n🔍 查找完全缺失比分的已完成比賽...")
        
        cursor.execute("""
            SELECT game_id, date, home_team, away_team, game_status
            FROM games 
            WHERE game_status = 'completed'
            AND home_score IS NULL 
            AND away_score IS NULL
            ORDER BY date DESC
            LIMIT 50
        """)
        
        missing_games = cursor.fetchall()
        
        if not missing_games:
            print("   ✅ 沒有發現完全缺失比分的已完成比賽")
            conn.close()
            return True
        
        print(f"   🎯 發現 {len(missing_games)} 場比賽需要從API獲取boxscore")
        
        # 2. 設置MLB API
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'MLB-Prediction-System/1.0'
        })
        
        mlb_api_base = "https://statsapi.mlb.com/api/v1"
        
        fixed_count = 0
        api_error_count = 0
        
        print(f"\n🔧 開始從MLB API獲取boxscore數據...")
        
        for i, (game_id, game_date, home_team, away_team, status) in enumerate(missing_games[:20]):  # 限制處理20場
            
            print(f"   🔄 [{i+1:2d}/20] {game_date} | {away_team} vs {home_team}")
            
            try:
                # 嘗試從MLB API獲取比分數據
                # 首先獲取當日賽程來找到game_pk
                schedule_url = f"{mlb_api_base}/schedule"
                schedule_params = {
                    'startDate': game_date,
                    'endDate': game_date,
                    'sportId': 1,
                    'hydrate': 'linescore'
                }
                
                schedule_response = session.get(schedule_url, params=schedule_params, timeout=10)
                
                if schedule_response.status_code == 200:
                    schedule_data = schedule_response.json()
                    
                    # 找到對應的比賽
                    target_game = None
                    if 'dates' in schedule_data:
                        for date_info in schedule_data['dates']:
                            for game in date_info.get('games', []):
                                home_abbr = game.get('teams', {}).get('home', {}).get('team', {}).get('abbreviation')
                                away_abbr = game.get('teams', {}).get('away', {}).get('team', {}).get('abbreviation')
                                
                                if home_abbr == home_team and away_abbr == away_team:
                                    target_game = game
                                    break
                            if target_game:
                                break
                    
                    if target_game:
                        # 獲取比分數據
                        linescore = target_game.get('linescore', {})
                        if linescore:
                            home_score = linescore.get('teams', {}).get('home', {}).get('runs')
                            away_score = linescore.get('teams', {}).get('away', {}).get('runs')
                            
                            if home_score is not None and away_score is not None:
                                # 更新數據庫
                                cursor.execute("""
                                    UPDATE games 
                                    SET home_score = ?, away_score = ?, updated_at = ?
                                    WHERE game_id = ?
                                """, (home_score, away_score, datetime.now(), game_id))
                                
                                fixed_count += 1
                                print(f"      ✅ 修復: {away_team} {away_score}-{home_score} {home_team}")
                            else:
                                print(f"      ⚠️ API返回數據不完整")
                        else:
                            print(f"      ⚠️ 比賽未完成或無linescore數據")
                    else:
                        print(f"      ❌ 在API中找不到對應比賽")
                else:
                    api_error_count += 1
                    print(f"      ❌ API請求失敗: {schedule_response.status_code}")
                
                # API限速
                time.sleep(0.5)
                
            except Exception as e:
                api_error_count += 1
                print(f"      ❌ 處理失敗: {str(e)[:50]}...")
                time.sleep(1)  # 錯誤後稍長延遲
        
        # 提交更改
        if fixed_count > 0:
            conn.commit()
            print(f"\n🎉 修復結果:")
            print(f"   ✅ 成功修復: {fixed_count} 場比賽")
            print(f"   ❌ API錯誤: {api_error_count} 場")
            
            # 檢查修復後的覆蓋率
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_completed,
                    SUM(CASE WHEN home_score IS NOT NULL AND away_score IS NOT NULL THEN 1 ELSE 0 END) as with_scores
                FROM games 
                WHERE game_status = 'completed'
            """)
            
            post_fix_data = cursor.fetchone()
            if post_fix_data:
                total_completed, with_scores = post_fix_data
                new_rate = (with_scores / total_completed * 100) if total_completed > 0 else 0
                print(f"   📈 新覆蓋率: {new_rate:.1f}% ({with_scores}/{total_completed})")
        else:
            print(f"\n⚠️ 沒有成功修復任何比賽")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_game_pk_availability():
    """檢查games表中是否有game_pk數據"""
    
    print("\n🔍 檢查game_pk可用性...")
    
    try:
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        # 檢查games表結構中是否有game_pk欄位
        cursor.execute("PRAGMA table_info(games)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'game_pk' in column_names:
            print("   ✅ games表包含game_pk欄位")
            
            # 檢查有多少比賽有game_pk
            cursor.execute("SELECT COUNT(*) FROM games WHERE game_pk IS NOT NULL")
            games_with_pk = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM games")
            total_games = cursor.fetchone()[0]
            
            pk_rate = (games_with_pk / total_games * 100) if total_games > 0 else 0
            print(f"   📊 有game_pk的比賽: {games_with_pk}/{total_games} ({pk_rate:.1f}%)")
            
        else:
            print("   ❌ games表缺少game_pk欄位")
            print("   💡 需要添加game_pk欄位或使用其他方法識別比賽")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 檢查失敗: {e}")

if __name__ == "__main__":
    print("🔧 MLB Boxscore 缺失數據修復工具")
    print("從MLB API獲取並修復缺失的boxscore數據")
    print("")
    
    # 先檢查game_pk可用性
    check_game_pk_availability()
    
    print("\n" + "="*60)
    
    success = fix_missing_boxscores()
    
    if success:
        print(f"\n✅ 修復程序執行完成")
        print(f"💡 如需處理更多比賽，可重新運行此腳本")
    else:
        print(f"\n❌ 修復失敗，請檢查錯誤信息")