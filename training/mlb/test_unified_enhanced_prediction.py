#!/usr/bin/env python3
"""
測試統一界面的增強預測功能
驗證 /unified/custom_predict 的增強預測 API
"""

import json
from datetime import date
from app import create_app

def test_unified_enhanced_prediction():
    """測試統一界面的增強預測 API"""
    
    app = create_app('development')
    
    with app.app_context():
        print("🎯 測試統一界面增強預測功能")
        print("="*60)
        
        # 使用Flask測試客戶端測試API
        with app.test_client() as client:
            
            # 測試增強日預測 API
            test_data = {
                'target_date': '2025-08-23',
                'date': '2025-08-23'
            }
            
            print(f"📡 測試 POST /unified/api/predict/enhanced_daily")
            print(f"   請求數據: {test_data}")
            
            response = client.post('/unified/api/predict/enhanced_daily',
                                 json=test_data,
                                 content_type='application/json')
            
            print(f"   HTTP狀態: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                
                print(f"✅ API響應成功!")
                print(f"   總預測場次: {data.get('total_games', 0)}")
                print(f"   成功預測: {data.get('successful_predictions', 0)}")
                print(f"   算法版本: {data.get('algorithm_version', 'N/A')}")
                
                # 檢查預測結果質量
                predictions = data.get('predictions', [])
                if predictions:
                    sample_pred = predictions[0]
                    print(f"\n📊 樣本預測檢查:")
                    print(f"   比賽: {sample_pred.get('matchup', 'N/A')}")
                    print(f"   預測比分: {sample_pred.get('predicted_away_score', 0):.1f} - {sample_pred.get('predicted_home_score', 0):.1f}")
                    
                    pitcher_info = sample_pred.get('pitcher_info', {})
                    home_pitcher = pitcher_info.get('home_pitcher', '未確認')
                    away_pitcher = pitcher_info.get('away_pitcher', '未確認')
                    print(f"   投手對戰: {away_pitcher} vs {home_pitcher}")
                    print(f"   信心度: {sample_pred.get('confidence', 0):.2f}")
                    
                    # 關鍵檢查：是否還是通用4.0-5.0分數
                    is_generic = (
                        abs(sample_pred.get('predicted_home_score', 0) - 4.0) < 0.1 and 
                        abs(sample_pred.get('predicted_away_score', 0) - 5.0) < 0.1
                    )
                    
                    has_real_pitchers = (
                        home_pitcher != '未確認' and 
                        away_pitcher != '未確認' and
                        home_pitcher != 'N/A' and
                        away_pitcher != 'N/A'
                    )
                    
                    print(f"\n🔍 質量檢查:")
                    print(f"   {'❌' if is_generic else '✅'} 是否為通用4.0-5.0分數: {'是' if is_generic else '否'}")
                    print(f"   {'✅' if has_real_pitchers else '❌'} 有真實投手信息: {'是' if has_real_pitchers else '否'}")
                    
                    if not is_generic and has_real_pitchers:
                        print(f"\n🎉 增強預測功能正常工作!")
                        print(f"   ✅ 使用統一預測引擎")
                        print(f"   ✅ 顯示真實投手姓名")
                        print(f"   ✅ 生成具體預測分數")
                        print(f"   ✅ 不再是通用4:5分數")
                        return True
                    else:
                        print(f"\n⚠️  預測功能需要進一步優化")
                        return False
                else:
                    print(f"❌ 沒有預測結果")
                    return False
                    
            else:
                print(f"❌ API調用失敗")
                error_data = response.get_data(as_text=True)
                print(f"   錯誤信息: {error_data}")
                return False

if __name__ == "__main__":
    print("🚀 開始測試統一界面增強預測功能")
    success = test_unified_enhanced_prediction()
    
    print(f"\n{'='*60}")
    if success:
        print("✅ 統一界面增強預測功能測試通過!")
        print("   用戶現在可以在 /unified/custom_predict 看到:")
        print("   📊 真實的預測比分 (不是4.0-5.0)")
        print("   ⚾ 完整的投手信息 (不是'未確認')")
        print("   🎯 可靠的信心度評估")
        print("   🚀 統一預測引擎的增強功能")
    else:
        print("⚠️  統一界面增強預測功能需要進一步調試")