#!/usr/bin/env python3
"""
測試比賽級別的投手載入功能
驗證系統是否能為不同比賽載入不同的先發投手
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game
from models.enhanced_feature_engineer import EnhancedFeatureEngineer
from datetime import date

def test_game_specific_pitcher_loading():
    """測試比賽級別的投手載入"""
    app = create_app()
    
    with app.app_context():
        print("🎯 測試比賽級別的投手載入功能")
        print("=" * 60)
        
        # 獲取2025-07-12的比賽
        test_date = date(2025, 7, 12)
        games = Game.query.filter(Game.date == test_date).limit(4).all()
        
        if len(games) < 2:
            print("❌ 找不到足夠的比賽進行測試")
            return
        
        print(f"📅 測試日期: {test_date}")
        print(f"🎲 測試 {len(games)} 場比賽")
        
        # 初始化增強特徵工程器
        feature_engineer = EnhancedFeatureEngineer()
        
        # 清空投手緩存確保重新載入
        feature_engineer.pitcher_cache = {}
        
        game_results = []
        
        for i, game in enumerate(games, 1):
            print(f"\n🏟️ 比賽 {i}: {game.away_team} @ {game.home_team}")
            print(f"   Game ID: {game.game_id}")
            
            try:
                # 🎯 關鍵測試：傳遞game_id進行特徵提取
                features = feature_engineer.extract_comprehensive_features(
                    game.home_team, game.away_team, test_date, game.game_id
                )
                
                if features:
                    # 提取投手相關信息
                    pitcher_info = {
                        'game_id': game.game_id,
                        'matchup': f"{game.away_team} @ {game.home_team}",
                        'home_pitcher_name': features.get('home_pitcher_name', 'Unknown'),
                        'away_pitcher_name': features.get('away_pitcher_name', 'Unknown'),
                        'home_pitcher_quality': features.get('home_pitcher_quality', 50),
                        'away_pitcher_quality': features.get('away_pitcher_quality', 50),
                        'pitcher_matchup_advantage': features.get('pitcher_matchup_advantage', 0),
                        'ace_pitcher_duel': features.get('ace_pitcher_duel', 0)
                    }
                    
                    game_results.append(pitcher_info)
                    
                    print(f"   ✅ 特徵提取成功")
                    print(f"   🥎 主隊投手: {pitcher_info['home_pitcher_name']}")
                    print(f"   ⚾ 客隊投手: {pitcher_info['away_pitcher_name']}")
                    print(f"   📊 投手質量: 主隊 {pitcher_info['home_pitcher_quality']:.1f}, 客隊 {pitcher_info['away_pitcher_quality']:.1f}")
                    
                    if pitcher_info['ace_pitcher_duel']:
                        print(f"   🌟 王牌投手對決")
                    
                    advantage = pitcher_info['pitcher_matchup_advantage']
                    if abs(advantage) > 0.1:
                        team = "主隊" if advantage > 0 else "客隊"
                        print(f"   ⚖️ {team}投手優勢 ({advantage:.2f})")
                
                else:
                    print(f"   ❌ 特徵提取失敗")
                    
            except Exception as e:
                print(f"   ❌ 錯誤: {e}")
        
        # 分析結果
        print(f"\n📊 投手載入結果分析:")
        print("=" * 60)
        
        if len(game_results) >= 2:
            # 檢查投手名稱的唯一性
            unique_home_pitchers = set(result['home_pitcher_name'] for result in game_results if result['home_pitcher_name'] != 'Unknown')
            unique_away_pitchers = set(result['away_pitcher_name'] for result in game_results if result['away_pitcher_name'] != 'Unknown')
            
            print(f"🏠 主隊投手數量: {len(unique_home_pitchers)}")
            print(f"✈️ 客隊投手數量: {len(unique_away_pitchers)}")
            
            # 檢查投手質量分數的差異
            quality_scores = [(result['home_pitcher_quality'], result['away_pitcher_quality']) for result in game_results]
            unique_quality_combinations = set(quality_scores)
            
            print(f"📈 唯一投手質量組合: {len(unique_quality_combinations)}")
            
            # 詳細比較
            print(f"\n📋 詳細比較:")
            for i, result in enumerate(game_results, 1):
                print(f"  比賽{i} ({result['matchup']}):")
                print(f"    投手: {result['away_pitcher_name']} vs {result['home_pitcher_name']}")
                print(f"    質量: {result['away_pitcher_quality']:.1f} vs {result['home_pitcher_quality']:.1f}")
                print(f"    優勢: {result['pitcher_matchup_advantage']:.2f}")
            
            # 成功判定
            if len(unique_home_pitchers) > 1 or len(unique_away_pitchers) > 1 or len(unique_quality_combinations) > 1:
                print(f"\n✅ 成功！系統能為不同比賽載入不同的投手信息")
                print(f"   - 找到 {len(unique_home_pitchers)} 個不同的主隊投手")
                print(f"   - 找到 {len(unique_away_pitchers)} 個不同的客隊投手")
                print(f"   - 生成 {len(unique_quality_combinations)} 種不同的投手質量組合")
            else:
                print(f"\n⚠️ 警告：所有比賽使用相同的投手信息")
                print(f"   這可能表示投手載入功能仍有問題")
        
        else:
            print(f"❌ 測試數據不足，無法進行比較")
        
        # 測試緩存功能
        print(f"\n🗄️ 投手緩存測試:")
        cache_keys = list(feature_engineer.pitcher_cache.keys())
        print(f"   緩存條目數: {len(cache_keys)}")
        for key in cache_keys[:5]:  # 只顯示前5個
            print(f"   - {key}: {feature_engineer.pitcher_cache[key]}")

def test_same_teams_different_games():
    """測試相同球隊在不同比賽中的投手差異"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔄 測試相同球隊在不同比賽中的投手差異")
        print("=" * 60)
        
        # 查找有重賽的球隊
        test_date = date(2025, 7, 12)
        
        # 查找同一天有多場比賽的球隊
        from sqlalchemy import func
        team_game_counts = db.session.query(
            Game.home_team.label('team'),
            func.count(Game.game_id).label('game_count')
        ).filter(Game.date == test_date).group_by(Game.home_team).all()
        
        team_game_counts += db.session.query(
            Game.away_team.label('team'),
            func.count(Game.game_id).label('game_count')
        ).filter(Game.date == test_date).group_by(Game.away_team).all()
        
        # 找到有多場比賽的球隊
        teams_with_multiple_games = [team for team, count in team_game_counts if count > 1]
        
        if teams_with_multiple_games:
            print(f"📊 找到有多場比賽的球隊: {teams_with_multiple_games}")
            
            for team in teams_with_multiple_games[:2]:  # 測試前兩個球隊
                games = Game.query.filter(
                    Game.date == test_date,
                    (Game.home_team == team) | (Game.away_team == team)
                ).all()
                
                print(f"\n🏟️ 球隊 {team} 的比賽:")
                
                feature_engineer = EnhancedFeatureEngineer()
                feature_engineer.pitcher_cache = {}  # 清空緩存
                
                for i, game in enumerate(games, 1):
                    print(f"  比賽{i}: {game.away_team} @ {game.home_team} (ID: {game.game_id})")
                    
                    try:
                        features = feature_engineer.extract_comprehensive_features(
                            game.home_team, game.away_team, test_date, game.game_id
                        )
                        
                        if features:
                            if team == game.home_team:
                                pitcher = features.get('home_pitcher_name', 'Unknown')
                                quality = features.get('home_pitcher_quality', 50)
                                print(f"    主隊投手: {pitcher} (質量: {quality:.1f})")
                            else:
                                pitcher = features.get('away_pitcher_name', 'Unknown')
                                quality = features.get('away_pitcher_quality', 50)
                                print(f"    客隊投手: {pitcher} (質量: {quality:.1f})")
                        
                    except Exception as e:
                        print(f"    ❌ 錯誤: {e}")
        
        else:
            print(f"📊 該日期沒有球隊有多場比賽")

if __name__ == "__main__":
    test_game_specific_pitcher_loading()
    test_same_teams_different_games()
