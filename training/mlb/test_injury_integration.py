#!/usr/bin/env python3
"""
測試傷兵數據與預測系統整合
"""

import sys
import os

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.injury_prediction_integration import InjuryPredictionIntegration

def test_injury_integration():
    """測試傷兵整合系統"""
    print("=" * 80)
    print("🏥 測試傷兵數據與預測系統整合")
    print("=" * 80)
    
    integration = InjuryPredictionIntegration()
    
    # 測試1: 獲取特定比賽的傷兵特徵
    print("\n📊 測試1: 獲取比賽傷兵特徵")
    print("-" * 50)
    
    # 測試幾場比賽
    test_matchups = [
        ("BOS", "NYY", "波士頓紅襪 vs 紐約洋基"),
        ("LAD", "HOU", "洛杉磯道奇 vs 休士頓太空人"),
        ("CHC", "STL", "芝加哥小熊 vs 聖路易紅雀")
    ]
    
    for home_team, away_team, description in test_matchups:
        print(f"\n🏟️  {description} ({home_team} vs {away_team})")
        
        injury_features = integration.get_injury_features_for_game(home_team, away_team)
        
        print(f"主隊傷兵數: {injury_features['home_total_injured']}")
        print(f"客隊傷兵數: {injury_features['away_total_injured']}")
        print(f"主隊影響分數: {injury_features['home_injury_impact_score']:.1f}")
        print(f"客隊影響分數: {injury_features['away_injury_impact_score']:.1f}")
        print(f"傷兵優勢: {injury_features['injury_advantage']:.1f} (正值=主隊優勢)")
        
        # 顯示位置分布
        print(f"主隊投手傷兵: {injury_features['home_injured_pitchers']}")
        print(f"客隊投手傷兵: {injury_features['away_injured_pitchers']}")
        
        # 顯示嚴重程度分布
        print(f"主隊重大傷兵: {injury_features['home_major_injuries']}")
        print(f"客隊重大傷兵: {injury_features['away_major_injuries']}")
        
        # 關鍵指標
        if injury_features['home_key_player_injured']:
            print("⚠️  主隊有關鍵球員受傷")
        if injury_features['away_key_player_injured']:
            print("⚠️  客隊有關鍵球員受傷")
    
    # 測試2: 獲取顯示用的傷兵摘要
    print(f"\n📊 測試2: 獲取顯示用傷兵摘要")
    print("-" * 50)
    
    for home_team, away_team, description in test_matchups[:2]:  # 只測試前2場
        print(f"\n🏟️  {description}")
        
        summary = integration.get_injury_summary_for_display(home_team, away_team)
        
        if 'error' in summary:
            print(f"❌ 錯誤: {summary['error']}")
            continue
        
        print(f"\n主隊 ({summary['home_team']['name']}):")
        print(f"  總傷兵: {summary['home_team']['total_injured']}")
        print(f"  影響分數: {summary['home_team']['impact_score']:.1f}")
        print(f"  投手傷兵: {summary['home_team']['pitcher_injuries']}")
        
        if summary['home_team']['key_injuries']:
            print("  關鍵傷兵:")
            for injury in summary['home_team']['key_injuries']:
                print(f"    {injury['name']} ({injury['position']}) - {injury['severity']}")
        
        print(f"\n客隊 ({summary['away_team']['name']}):")
        print(f"  總傷兵: {summary['away_team']['total_injured']}")
        print(f"  影響分數: {summary['away_team']['impact_score']:.1f}")
        print(f"  投手傷兵: {summary['away_team']['pitcher_injuries']}")
        
        if summary['away_team']['key_injuries']:
            print("  關鍵傷兵:")
            for injury in summary['away_team']['key_injuries']:
                print(f"    {injury['name']} ({injury['position']}) - {injury['severity']}")
        
        print(f"\n📈 比賽分析:")
        print(f"  優勢情況: {summary['advantage']}")
        print(f"  影響等級: {summary['impact_level']}")
    
    # 測試3: 傷兵特徵的完整性檢查
    print(f"\n📊 測試3: 傷兵特徵完整性檢查")
    print("-" * 50)
    
    # 使用第一場比賽測試
    home_team, away_team = "BOS", "NYY"
    injury_features = integration.get_injury_features_for_game(home_team, away_team)
    
    print(f"傷兵特徵總數: {len(injury_features)}")
    print("\n所有傷兵特徵:")
    
    feature_categories = {
        '基本數量': ['home_total_injured', 'away_total_injured'],
        '嚴重程度': [k for k in injury_features.keys() if 'minor' in k or 'moderate' in k or 'major' in k or 'season_ending' in k],
        '位置分布': [k for k in injury_features.keys() if 'pitcher' in k or 'catcher' in k or 'infielder' in k or 'outfielder' in k],
        '影響分析': [k for k in injury_features.keys() if 'impact' in k or 'advantage' in k or 'risk' in k],
        '關鍵指標': [k for k in injury_features.keys() if 'key_player' in k]
    }
    
    for category, features in feature_categories.items():
        print(f"\n{category}:")
        for feature in features:
            if feature in injury_features:
                value = injury_features[feature]
                print(f"  {feature}: {value}")
    
    # 測試4: 預測系統整合準備
    print(f"\n📊 測試4: 預測系統整合準備")
    print("-" * 50)
    
    print("傷兵特徵可以直接用於機器學習模型:")
    
    # 檢查所有特徵都是數值型
    all_numeric = True
    for key, value in injury_features.items():
        if not isinstance(value, (int, float)):
            print(f"❌ 非數值特徵: {key} = {value} ({type(value)})")
            all_numeric = False
    
    if all_numeric:
        print("✅ 所有特徵都是數值型，可直接用於ML模型")
    
    # 檢查特徵範圍
    print(f"\n特徵值範圍檢查:")
    for key, value in injury_features.items():
        if isinstance(value, (int, float)):
            if value < 0:
                print(f"⚠️  負值特徵: {key} = {value}")
            elif value > 100:
                print(f"⚠️  大值特徵: {key} = {value}")
    
    print(f"\n✅ 傷兵整合系統測試完成")
    print(f"📈 系統可以為每場比賽提供 {len(injury_features)} 個傷兵相關特徵")
    print(f"🎯 這些特徵將顯著提升預測準確性")

def main():
    """主函數"""
    test_injury_integration()

if __name__ == "__main__":
    main()
