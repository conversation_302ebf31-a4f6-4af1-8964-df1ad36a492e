#!/usr/bin/env python3
"""
簡單檢查數據庫
"""

import sqlite3
import os

def check_database(db_path):
    """檢查數據庫"""
    if not os.path.exists(db_path):
        print(f"❌ 數據庫不存在: {db_path}")
        return
    
    size = os.path.getsize(db_path)
    print(f"📁 數據庫: {db_path} ({size/1024/1024:.1f} MB)")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📊 表數量: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  {table_name}: {count} 記錄")
        
        # 檢查預測
        if any('predictions' in t[0] for t in tables):
            print(f"\n🎯 最近預測:")
            cursor.execute("""
                SELECT p.predicted_away_score, p.predicted_home_score, 
                       g.away_team, g.home_team
                FROM predictions p
                JOIN games g ON p.game_id = g.game_id
                ORDER BY g.date DESC
                LIMIT 5
            """)
            
            predictions = cursor.fetchall()
            for pred in predictions:
                away_score, home_score, away_team, home_team = pred
                print(f"  {away_team} @ {home_team}: {away_score:.1f} - {home_score:.1f}")
        
        # 檢查球隊統計
        if any('team_stats' in t[0] for t in tables):
            print(f"\n🏟️ 球隊統計:")
            cursor.execute("""
                SELECT team_id, runs_per_game, era
                FROM team_stats
                ORDER BY runs_per_game DESC
                LIMIT 5
            """)
            
            stats = cursor.fetchall()
            for stat in stats:
                team, rpg, era = stat
                print(f"  {team}: {rpg:.2f}分/場, ERA={era:.2f}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 數據庫錯誤: {e}")

def main():
    """主函數"""
    print("🔍 檢查數據庫狀態")
    print("=" * 50)
    
    # 檢查可能的數據庫位置
    db_paths = [
        "mlb_data.db",
        "instance/mlb_data.db", 
        "instance/mlb_predictions.db"
    ]
    
    for db_path in db_paths:
        print()
        check_database(db_path)

if __name__ == "__main__":
    main()
