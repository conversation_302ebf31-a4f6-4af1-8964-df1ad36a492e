#!/usr/bin/env python3
"""
測試整合投手因素的統一預測系統
"""

import sys
import os
from datetime import date, timedelta

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.enhanced_feature_engineer import EnhancedFeatureEngineer
from models.unified_betting_predictor import UnifiedBettingPredictor
from models.database import Game

def test_enhanced_feature_engineer():
    """測試增強特徵工程器的王牌投手功能"""
    app = create_app()
    
    with app.app_context():
        print("🔍 測試增強特徵工程器 - 王牌投手因素")
        print("=" * 60)
        
        # 初始化特徵工程器
        feature_engineer = EnhancedFeatureEngineer()
        
        # 獲取一場測試比賽
        test_game = Game.query.filter(
            Game.date == date(2025, 6, 28)
        ).first()
        
        if not test_game:
            print("❌ 找不到測試比賽")
            return
        
        print(f"📊 測試比賽: {test_game.away_team} @ {test_game.home_team}")
        print(f"📅 比賽日期: {test_game.date}")
        
        # 提取全面特徵
        print("\n🎯 提取全面特徵...")
        features = feature_engineer.extract_comprehensive_features(
            test_game.home_team, 
            test_game.away_team, 
            test_game.date
        )
        
        if not features:
            print("❌ 特徵提取失敗")
            return
        
        # 顯示王牌投手相關特徵
        print("\n⚾ 王牌投手因素:")
        pitcher_features = [
            'home_pitcher_quality', 'away_pitcher_quality',
            'home_has_ace', 'away_has_ace',
            'pitcher_matchup_advantage', 'pitcher_era_differential',
            'ace_pitcher_duel', 'home_pitcher_dominance', 'away_pitcher_dominance'
        ]
        
        for feature in pitcher_features:
            if feature in features:
                value = features[feature]
                if isinstance(value, float):
                    print(f"  {feature}: {value:.2f}")
                else:
                    print(f"  {feature}: {value}")
        
        # 分析王牌投手效應
        print(f"\n🥊 王牌投手效應分析:")
        if features.get('ace_pitcher_duel', 0) == 1:
            print("  🌟 王牌投手對決！雙方都有ERA < 3.00的投手")
            print("  📉 預期得分會被壓制")
        elif features.get('home_has_ace', 0) == 1:
            print("  🏠 主隊有王牌投手，客隊得分可能被壓制")
        elif features.get('away_has_ace', 0) == 1:
            print("  ✈️ 客隊有王牌投手，主隊得分可能被壓制")
        else:
            print("  📊 雙方都是普通投手，正常得分預期")
        
        advantage = features.get('pitcher_matchup_advantage', 0)
        if advantage > 0.2:
            print(f"  ✅ 主隊投手優勢明顯 (+{advantage:.2f})")
        elif advantage < -0.2:
            print(f"  ✅ 客隊投手優勢明顯 ({advantage:.2f})")
        else:
            print(f"  ⚖️ 投手實力相當 ({advantage:.2f})")
        
        print(f"\n📈 特徵統計:")
        print(f"  總特徵數: {len(features)}")
        print(f"  投手相關特徵: {len([f for f in features.keys() if 'pitcher' in f])}")

def test_unified_prediction_with_pitcher_factors():
    """測試整合投手因素的統一預測"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("🚀 測試統一預測系統 - 投手因素整合")
        print("=" * 60)
        
        # 初始化統一預測器
        unified_predictor = UnifiedBettingPredictor(app)
        
        # 獲取明天的比賽
        tomorrow = date.today() + timedelta(days=1)
        games = Game.query.filter(Game.date == tomorrow).limit(3).all()
        
        if not games:
            print("❌ 找不到明天的比賽")
            return
        
        print(f"📅 預測日期: {tomorrow}")
        print(f"📊 找到 {len(games)} 場比賽")
        
        for i, game in enumerate(games, 1):
            print(f"\n🎯 比賽 {i}: {game.away_team} @ {game.home_team}")
            
            try:
                # 測試特徵提取
                feature_engineer = EnhancedFeatureEngineer()
                features = feature_engineer.extract_comprehensive_features(
                    game.home_team, game.away_team, game.date
                )
                
                if features:
                    # 顯示關鍵投手指標
                    print(f"  投手質量: 主隊 {features.get('home_pitcher_quality', 50):.1f}, "
                          f"客隊 {features.get('away_pitcher_quality', 50):.1f}")
                    
                    if features.get('ace_pitcher_duel', 0):
                        print("  🌟 王牌投手對決")
                    elif features.get('home_has_ace', 0):
                        print("  🏠 主隊有王牌")
                    elif features.get('away_has_ace', 0):
                        print("  ✈️ 客隊有王牌")
                    
                    advantage = features.get('pitcher_matchup_advantage', 0)
                    if abs(advantage) > 0.2:
                        team = "主隊" if advantage > 0 else "客隊"
                        print(f"  ⚖️ {team}投手優勢 ({advantage:.2f})")
                else:
                    print("  ❌ 特徵提取失敗")
                
            except Exception as e:
                print(f"  ❌ 分析失敗: {e}")

def analyze_pitcher_impact_on_scoring():
    """分析投手因素對得分的影響"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("📊 投手因素對得分影響分析")
        print("=" * 60)
        
        feature_engineer = EnhancedFeatureEngineer()
        
        # 分析最近幾場比賽的投手因素
        recent_games = Game.query.filter(
            Game.date >= date(2025, 6, 25),
            Game.date <= date(2025, 6, 28),
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).limit(10).all()
        
        ace_duel_games = []
        normal_games = []
        
        for game in recent_games:
            try:
                features = feature_engineer.extract_comprehensive_features(
                    game.home_team, game.away_team, game.date
                )
                
                if features:
                    total_runs = (game.home_score or 0) + (game.away_score or 0)
                    
                    if features.get('ace_pitcher_duel', 0) == 1:
                        ace_duel_games.append({
                            'game': f"{game.away_team}@{game.home_team}",
                            'total_runs': total_runs,
                            'home_quality': features.get('home_pitcher_quality', 50),
                            'away_quality': features.get('away_pitcher_quality', 50)
                        })
                    else:
                        normal_games.append({
                            'game': f"{game.away_team}@{game.home_team}",
                            'total_runs': total_runs
                        })
            except Exception as e:
                print(f"分析比賽失敗 {game.game_id}: {e}")
        
        print(f"🌟 王牌投手對決比賽 ({len(ace_duel_games)}):")
        if ace_duel_games:
            avg_runs_ace = sum(g['total_runs'] for g in ace_duel_games) / len(ace_duel_games)
            for game_info in ace_duel_games:
                print(f"  {game_info['game']}: {game_info['total_runs']} 分 "
                      f"(投手質量: {game_info['home_quality']:.1f}/{game_info['away_quality']:.1f})")
            print(f"  平均得分: {avg_runs_ace:.1f}")
        
        print(f"\n📊 普通比賽 ({len(normal_games)}):")
        if normal_games:
            avg_runs_normal = sum(g['total_runs'] for g in normal_games) / len(normal_games)
            for game_info in normal_games[:5]:  # 只顯示前5場
                print(f"  {game_info['game']}: {game_info['total_runs']} 分")
            print(f"  平均得分: {avg_runs_normal:.1f}")
        
        if ace_duel_games and normal_games:
            avg_runs_ace = sum(g['total_runs'] for g in ace_duel_games) / len(ace_duel_games)
            avg_runs_normal = sum(g['total_runs'] for g in normal_games) / len(normal_games)
            difference = avg_runs_normal - avg_runs_ace
            print(f"\n🎯 王牌投手效應:")
            print(f"  王牌對決平均得分: {avg_runs_ace:.1f}")
            print(f"  普通比賽平均得分: {avg_runs_normal:.1f}")
            print(f"  得分差距: {difference:.1f} 分")
            if difference > 1:
                print(f"  ✅ 王牌投手確實能顯著壓制得分！")
            else:
                print(f"  📊 王牌投手效應不明顯")

if __name__ == "__main__":
    print("⚾ 整合投手因素的統一預測系統測試")
    print("=" * 60)
    
    # 測試增強特徵工程器
    test_enhanced_feature_engineer()
    
    # 測試統一預測系統
    test_unified_prediction_with_pitcher_factors()
    
    # 分析投手影響
    analyze_pitcher_impact_on_scoring()
    
    print("\n✅ 整合測試完成")
