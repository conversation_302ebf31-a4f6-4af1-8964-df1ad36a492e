#!/usr/bin/env python3
"""
測試整合後的預測API
驗證用戶在 /predictions/ 路徑可以看到增強功能
"""

import asyncio
import json
from datetime import date, datetime
from app import create_app
from models.database import Game, Prediction, db

def test_integrated_prediction_api():
    """測試整合後的預測API"""
    
    app = create_app('development')
    
    with app.app_context():
        print("🔍 測試整合後的預測API")
        print("="*50)
        
        # 1. 查找8/23的比賽
        games_823 = Game.query.filter_by(date=date(2025, 8, 23)).limit(3).all()
        
        if not games_823:
            print("❌ 沒有找到8/23的比賽")
            return
        
        # 2. 測試每場比賽的API
        for i, game in enumerate(games_823, 1):
            print(f"\n📊 測試比賽 {i}: {game.game_id}")
            print(f"   日期: {game.date}")
            print(f"   對陣: {game.away_team} @ {game.home_team}")
            
            # 檢查現有預測狀態
            existing_prediction = Prediction.query.filter_by(game_id=game.game_id).first()
            
            if existing_prediction:
                print(f"   現有預測: {existing_prediction.starting_pitcher_home} vs {existing_prediction.starting_pitcher_away}")
                print(f"   比分預測: {existing_prediction.predicted_away_score} - {existing_prediction.predicted_home_score}")
                
                # 檢查是否需要增強
                needs_enhancement = (
                    not existing_prediction.starting_pitcher_home or 
                    not existing_prediction.starting_pitcher_away or
                    existing_prediction.starting_pitcher_home == '未確認' or
                    existing_prediction.starting_pitcher_away == '未確認'
                )
                
                if needs_enhancement:
                    print("   ⚠️  需要增強 - 投手信息缺失")
                else:
                    print("   ✅ 預測完整")
                    
            else:
                print("   ⚠️  無現有預測")
        
        print(f"\n🚀 模擬API調用測試")
        print("="*50)
        
        # 3. 模擬API調用 (不直接調用Flask路由，而是使用核心邏輯)
        test_game = games_823[0]
        
        try:
            from models.unified_prediction_engine import predict_game
            
            async def run_test():
                print(f"🎯 測試遊戲: {test_game.game_id}")
                print(f"   目標日期: {test_game.date}")
                
                # 調用統一預測引擎
                result = await predict_game(
                    game_id=test_game.game_id,
                    target_date=test_game.date
                )
                
                if result.get('success'):
                    print("✅ 預測成功!")
                    
                    prediction = result.get('prediction', {})
                    pitcher_analysis = result.get('pitcher_analysis', {})
                    confidence = result.get('confidence', {})
                    
                    print(f"   預測比分: {prediction.get('away_predicted_runs', 0):.1f} - {prediction.get('home_predicted_runs', 0):.1f}")
                    print(f"   總分預測: {prediction.get('predicted_total_runs', 0):.1f}")
                    
                    # 投手信息
                    home_pitcher = pitcher_analysis.get('home_pitcher', {}).get('name', '未確認')
                    away_pitcher = pitcher_analysis.get('away_pitcher', {}).get('name', '未確認')
                    print(f"   投手對戰: {away_pitcher} vs {home_pitcher}")
                    
                    print(f"   信心度: {confidence.get('confidence_level', 'medium')} ({confidence.get('overall_confidence', 0.5):.2f})")
                    print(f"   策略: {result.get('prediction_strategy', 'unknown')}")
                    
                else:
                    print(f"❌ 預測失敗: {result.get('error', 'Unknown error')}")
                
                return result
            
            # 運行異步測試
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                api_result = loop.run_until_complete(run_test())
            finally:
                loop.close()
            
            print(f"\n📋 API響應格式測試")
            print("="*30)
            
            # 4. 驗證API響應格式符合前端期望
            if api_result.get('success'):
                prediction_data = api_result.get('prediction', {})
                pitcher_analysis = api_result.get('pitcher_analysis', {})
                confidence_info = api_result.get('confidence', {})
                
                # 構建API響應格式
                api_response = {
                    'game_id': test_game.game_id,
                    'success': True,
                    'enhanced': True,
                    'source': 'enhanced_prediction_engine',
                    
                    # 核心預測數據
                    'predicted_home_score': prediction_data.get('home_predicted_runs', 0),
                    'predicted_away_score': prediction_data.get('away_predicted_runs', 0),
                    'predicted_total_runs': prediction_data.get('predicted_total_runs', 0),
                    
                    # 投手信息
                    'starting_pitcher_home': pitcher_analysis.get('home_pitcher', {}).get('name', '未確認'),
                    'starting_pitcher_away': pitcher_analysis.get('away_pitcher', {}).get('name', '未確認'),
                    
                    # 信心度
                    'confidence': confidence_info.get('overall_confidence', 0.5),
                    'confidence_level': confidence_info.get('confidence_level', 'medium'),
                    
                    'generated_at': datetime.now().isoformat()
                }
                
                print("✅ API響應格式:")
                for key, value in api_response.items():
                    if key not in ['pitcher_analysis', 'confidence_details']:
                        print(f"   {key}: {value}")
                
                # 檢查關鍵字段
                critical_fields = ['predicted_home_score', 'predicted_away_score', 'starting_pitcher_home', 'starting_pitcher_away']
                missing_fields = [field for field in critical_fields if not api_response.get(field) or api_response.get(field) == '未確認']
                
                if missing_fields:
                    print(f"⚠️  仍需修復的字段: {missing_fields}")
                else:
                    print("✅ 所有關鍵字段都有值!")
                    
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_integrated_prediction_api()