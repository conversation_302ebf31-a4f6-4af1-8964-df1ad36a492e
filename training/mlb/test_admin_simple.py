#!/usr/bin/env python3
"""
簡單測試管理後台API問題
快速診斷Load failed錯誤
"""

from app import create_app
import requests
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_admin_api_direct():
    """直接測試管理後台API，無需等待長時間抓取"""
    print("🔍 直接測試管理後台API...")
    print("=" * 80)
    
    try:
        # 測試服務器是否運行
        response = requests.get("http://localhost:5000/admin/dashboard", timeout=5)
        print(f"管理後台頁面狀態: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ 管理後台無法訪問，請確認服務器運行")
            return
            
        # 測試API端點
        test_data = {
            "start_date": "2025-08-29",
            "end_date": "2025-08-29",  # 單日測試
            "market_type": "both"
        }
        
        print("📊 測試自定義範圍抓取API...")
        print(f"   測試數據: {test_data}")
        
        response = requests.post(
            "http://localhost:5000/admin/api/fetch-custom-range",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30  # 30秒超時
        )
        
        print(f"   狀態碼: {response.status_code}")
        print(f"   響應頭: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   ✅ API響應: {data}")
            except Exception as e:
                print(f"   ❌ JSON解析失敗: {e}")
                print(f"   原始響應: {response.text[:500]}")
        else:
            print(f"   ❌ API錯誤: {response.status_code}")
            print(f"   錯誤內容: {response.text[:500]}")
            
    except requests.exceptions.ConnectRefusedError:
        print("❌ 無法連接到服務器，請確認Flask應用正在運行")
    except requests.exceptions.Timeout:
        print("❌ 請求超時 - API可能卡在長時間操作中")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def test_with_flask_app():
    """使用Flask app測試客戶端測試（不實際執行耗時操作）"""
    print("\n🧪 使用Flask測試客戶端...")
    
    try:
        app = create_app()
        with app.test_client() as client:
            # 測試空的請求（驗證路由和基本處理）
            print("   測試路由可訪問性...")
            
            # 先測試OPTIONS請求
            response = client.options('/admin/api/fetch-custom-range')
            print(f"   OPTIONS響應: {response.status_code}")
            
            # 測試無效數據
            response = client.post('/admin/api/fetch-custom-range',
                                 json={},
                                 content_type='application/json')
            print(f"   空數據測試: {response.status_code}")
            
            if response.status_code == 400:
                data = response.get_json()
                print(f"   預期錯誤: {data}")
                print("   ✅ 路由和基本驗證正常工作")
            else:
                print(f"   ⚠️ 未預期的響應: {response.status_code}")
                
    except Exception as e:
        print(f"   ❌ Flask測試失敗: {e}")

def check_import_issues():
    """檢查導入問題"""
    print("\n📦 檢查導入問題...")
    
    try:
        from models.real_betting_odds_fetcher import RealBettingOddsFetcher
        print("   ✅ RealBettingOddsFetcher導入成功")
        
        # 不實際實例化，只檢查類存在
        print(f"   類型: {type(RealBettingOddsFetcher)}")
        
    except ImportError as e:
        print(f"   ❌ 導入失敗: {e}")
    except Exception as e:
        print(f"   ❌ 其他錯誤: {e}")

if __name__ == "__main__":
    check_import_issues()
    test_with_flask_app()
    test_admin_api_direct()