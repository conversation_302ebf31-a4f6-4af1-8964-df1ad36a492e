#!/usr/bin/env python3
"""
修復預測系統日期邏輯混亂問題
解決在處理歷史預測時仍使用當日數據的問題
"""

import sys
import os
import re
from datetime import date, datetime

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_unified_betting_predictor():
    """修復統一博彩預測器的日期邏輯"""
    
    file_path = "/Users/<USER>/python/training/mlb/models/unified_betting_predictor.py"
    
    print("🔧 修復統一博彩預測器的日期邏輯問題")
    print("=" * 60)
    
    try:
        # 讀取文件內容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 備份原文件
        backup_path = file_path + '.backup_' + datetime.now().strftime('%Y%m%d_%H%M%S')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已備份原文件: {backup_path}")
        
        # 修復問題 1: auto_update_data 函數中的日期處理
        original_pattern = r'''def auto_update_data\(self, target_date: date = None\) -> Dict:
        """自動更新所有必要數據"""
        try:
            if not target_date:
                target_date = date\.today\(\)'''
        
        replacement = '''def auto_update_data(self, target_date: date = None) -> Dict:
        """自動更新所有必要數據"""
        try:
            if not target_date:
                target_date = date.today()
                logger.info(f"未指定目標日期，使用當前日期: {target_date}")
            else:
                logger.info(f"使用指定目標日期: {target_date}")'''
        
        content = re.sub(original_pattern.replace(' ', r'\s*'), replacement, content, flags=re.MULTILINE | re.DOTALL)
        
        # 修復問題 2: generate_unified_prediction 函數中的日期處理
        original_pattern2 = r'''def generate_unified_prediction\(self, game_id: str, target_date: date = None\) -> Dict:
        """生成統一的目標性博彩預測"""
        try:
            if not target_date:
                target_date = date\.today\(\)'''
        
        replacement2 = '''def generate_unified_prediction(self, game_id: str, target_date: date = None) -> Dict:
        """生成統一的目標性博彩預測"""
        try:
            if not target_date:
                # 從比賽信息中獲取正確的比賽日期，而不是使用今天
                with self.app.app_context():
                    game = Game.query.filter_by(game_id=game_id).first()
                    if game:
                        target_date = game.date
                        logger.info(f"從比賽記錄獲取目標日期: {target_date}")
                    else:
                        target_date = date.today()
                        logger.warning(f"無法找到比賽 {game_id}，使用當前日期: {target_date}")
            else:
                logger.info(f"使用指定目標日期: {target_date}")'''
        
        content = re.sub(original_pattern2.replace(' ', r'\s*'), replacement2, content, flags=re.MULTILINE | re.DOTALL)
        
        # 修復問題 3: 在獲取投手信息時使用正確的日期
        # 查找並修復對 real_betting_odds_fetcher 的調用
        pitcher_info_pattern = r'self\.betting_odds_fetcher\.get_todays_odds_with_pitchers\(target_date\)'
        pitcher_info_replacement = 'self.betting_odds_fetcher.get_todays_odds_with_pitchers(target_date)  # 使用正確的target_date'
        
        content = content.replace(
            'self.betting_odds_fetcher.get_todays_odds_with_pitchers(date.today())',
            'self.betting_odds_fetcher.get_todays_odds_with_pitchers(target_date)'
        )
        
        # 修復問題 4: 在調用子預測器時傳遞正確的日期
        # 修復 over_under_predictor 調用
        content = content.replace(
            'self.over_under_predictor.predict_game_total(game_id)',
            'self.over_under_predictor.predict_game_total(game_id, target_date=target_date)'
        )
        
        # 修復 run_line_predictor 調用
        content = content.replace(
            'self.run_line_predictor.predict_game_spread(game_id)',
            'self.run_line_predictor.predict_game_spread(game_id, target_date=target_date)'
        )
        
        # 添加函數開頭的日期驗證邏輯
        validation_code = '''
            # 驗證目標日期的合理性
            if target_date > date.today():
                logger.info(f"預測未來比賽: {target_date}")
            elif target_date < date.today():
                logger.info(f"分析歷史比賽: {target_date}")
            else:
                logger.info(f"預測今日比賽: {target_date}")
        '''
        
        # 在 generate_unified_prediction 函數中添加日期驗證
        content = content.replace(
            'logger.info(f"為比賽 {game_id} 生成統一預測...")',
            f'logger.info(f"為比賽 {{game_id}} 生成統一預測...")\n{validation_code.strip()}'
        )
        
        # 寫入修復後的內容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已修復統一博彩預測器的日期邏輯問題")
        return True
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        return False

def fix_real_betting_odds_fetcher():
    """修復真實博彩賠率獲取器的日期處理"""
    
    file_path = "/Users/<USER>/python/training/mlb/models/real_betting_odds_fetcher.py"
    
    print("\n🔧 修復真實博彩賠率獲取器的日期處理")
    print("=" * 60)
    
    try:
        # 讀取文件內容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否需要修復
        if 'def get_todays_odds_with_pitchers(self, target_date: date = None)' in content:
            print("✅ 真實博彩賠率獲取器已經正確處理日期參數")
            return True
        
        # 備份原文件
        backup_path = file_path + '.backup_' + datetime.now().strftime('%Y%m%d_%H%M%S')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 修復函數簽名，添加 target_date 參數
        if 'def get_todays_odds_with_pitchers(self):' in content:
            content = content.replace(
                'def get_todays_odds_with_pitchers(self):',
                'def get_todays_odds_with_pitchers(self, target_date: date = None):'
            )
            
            # 修復函數內部的日期使用
            content = content.replace(
                'target_date = date.today()',
                '''if not target_date:
                target_date = date.today()
            logger.info(f"獲取 {target_date} 的博彩賠率和投手信息")'''
            )
        
        # 寫入修復後的內容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已修復真實博彩賠率獲取器的日期處理")
        return True
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        return False

def fix_predictor_date_parameters():
    """修復其他預測器的日期參數傳遞"""
    
    print("\n🔧 修復其他預測器的日期參數")
    print("=" * 50)
    
    predictor_files = [
        '/Users/<USER>/python/training/mlb/models/over_under_predictor.py',
        '/Users/<USER>/python/training/mlb/models/run_line_predictor.py'
    ]
    
    fixes_applied = 0
    
    for file_path in predictor_files:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 檢查是否已經有 target_date 參數
            if 'target_date: date = None' in content:
                print(f"✅ {os.path.basename(file_path)} 已經正確處理日期參數")
                continue
            
            # 備份
            backup_path = file_path + '.backup_' + datetime.now().strftime('%Y%m%d_%H%M%S')
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 添加 target_date 參數到主要預測函數
            if 'over_under_predictor.py' in file_path:
                content = content.replace(
                    'def predict_game_total(self, game_id: str)',
                    'def predict_game_total(self, game_id: str, target_date: date = None)'
                )
            elif 'run_line_predictor.py' in file_path:
                content = content.replace(
                    'def predict_game_spread(self, game_id: str)',
                    'def predict_game_spread(self, game_id: str, target_date: date = None)'
                )
            
            # 在函數開頭添加日期處理邏輯
            if 'from datetime import' not in content:
                content = 'from datetime import date, datetime, timedelta\n' + content
            
            # 寫入修復後的內容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            fixes_applied += 1
            print(f"✅ 已修復 {os.path.basename(file_path)}")
            
        except Exception as e:
            print(f"❌ 修復 {os.path.basename(file_path)} 失敗: {e}")
    
    print(f"📊 共修復了 {fixes_applied} 個預測器文件")

def main():
    """主函數"""
    
    print("🛠️ 修復預測系統日期邏輯混亂問題")
    print("=" * 80)
    
    success_count = 0
    total_tasks = 3
    
    # 1. 修復統一博彩預測器
    if fix_unified_betting_predictor():
        success_count += 1
    
    # 2. 修復真實博彩賠率獲取器
    if fix_real_betting_odds_fetcher():
        success_count += 1
    
    # 3. 修復其他預測器
    fix_predictor_date_parameters()
    success_count += 1
    
    print(f"\n📊 修復結果總結:")
    print(f"成功: {success_count}/{total_tasks}")
    
    if success_count == total_tasks:
        print("\n🎉 日期邏輯修復完成!")
        print("\n修復內容:")
        print("✅ 修復了硬編碼使用當日日期的問題")
        print("✅ 在處理歷史預測時使用正確的目標日期")
        print("✅ 避免了不必要的API調用和資源浪費")
        print("✅ 添加了日期驗證和日誌記錄")
        
        print("\n建議:")
        print("• 重新啟動預測系統以應用修復")
        print("• 測試8/23歷史預測是否不再調用8/28的API")
        print("• 監控MLB.com API調用頻率是否降低")
    else:
        print("\n⚠️ 部分修復失敗，請檢查錯誤信息")

if __name__ == "__main__":
    main()