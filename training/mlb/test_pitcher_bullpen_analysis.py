#!/usr/bin/env python3
"""
投手+牛棚綜合分析系統測試
測試三層牛棚架構：先發 + 中繼 + Setup + 終結者
"""

import sys
import os
import asyncio
import json
from datetime import date, datetime

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.pitcher_bullpen_analyzer import PitcherBullpenAnalyzer
from models.starting_pitcher_tracker import StartingPitcherTracker

def demonstrate_mlb_bullpen_strategy():
    """展示MLB牛棚策略的重要性"""
    
    print("⚾ MLB三層牛棚策略的重要性")
    print("=" * 60)
    print("在MLB中，投手使用策略決定比賽勝負:")
    print()
    print("🔥 傳統先發投手時代 (已過時):")
    print("• 先發投手投滿9局")
    print("• 每場比賽依賴1名投手")
    print("• 高風險，投手疲勞影響大")
    print()
    print("🎯 現代三層牛棚策略:")
    print("• 先發投手: 1-6局 (ERA 3.80)")
    print("• 中繼投手: 第7局 (ERA 4.00, 橋接角色)")  
    print("• Setup Man: 第8局 (ERA 3.50, 專業建立優勢)")
    print("• 終結者: 第9局 (ERA 3.20, 救援專家)")
    print()
    print("💡 為什麼需要分層分析?")
    print("• 不同角色的投手有不同的心理壓力和技能")
    print("• 終結者專門訓練對付壓力局面")
    print("• Setup man負責關鍵的第8局建立優勢")
    print("• 中繼投手質量決定比賽走向")
    print()
    print("🚨 簡化預測的問題:")
    print("• '牛棚ERA 4.20' 無法反映專業分工")
    print("• 忽略了投手角色的專業性差異") 
    print("• 無法預測先發提前退場的連鎖效應")
    print()

async def test_pitcher_bullpen_analyzer():
    """測試完整的投手+牛棚分析系統"""
    
    print("🔬 測試投手+牛棚分析系統")
    print("=" * 50)
    
    app = create_app('development')
    
    with app.app_context():
        # 創建分析器
        analyzer = PitcherBullpenAnalyzer()
        
        # 添加測試數據 - 紐約洋基 vs 波士頓紅襪
        pitcher_tracker = StartingPitcherTracker()
        test_game_id = "20250823_BOS_NYY_bullpen_test"
        
        pitcher_tracker.record_starting_pitchers(
            game_id=test_game_id,
            game_date=date(2025, 8, 23),
            home_team="NYY",  # 洋基主場
            away_team="BOS",  # 紅襪客場 
            home_pitcher="Gerrit Cole",    # 洋基王牌
            away_pitcher="Brayan Bello",   # 紅襪先發
            data_source="test_data",
            confidence_level="high"
        )
        
        print("✅ 已設置測試比賽數據")
        print(f"   🏠 主隊: NYY (Gerrit Cole 先發)")
        print(f"   ✈️  客隊: BOS (Brayan Bello 先發)")
        print()
        
        print("🎯 執行完整投手陣容分析...")
        
        # 測試1: 分析洋基投手陣容 (主隊)
        print("\n【測試1: 洋基投手陣容分析】")
        yankees_analysis = await analyzer.analyze_complete_pitching_staff(
            starting_pitcher="Gerrit Cole",
            team="NYY",
            opposing_team="BOS",
            target_date=date(2025, 8, 23),
            is_home=True
        )
        
        if 'error' not in yankees_analysis:
            print("✅ 洋基投手分析成功!")
            
            # 顯示先發投手評估
            starter_profile = yankees_analysis.get('starter_profile', {})
            print(f"   🔥 先發投手: {starter_profile.get('pitcher_name', 'Unknown')}")
            print(f"      投手類型: {starter_profile.get('pitcher_tier', 'Unknown')}")
            print(f"      預期局數: {starter_profile.get('expected_innings', 0):.1f}")
            print(f"      優質先發機率: {starter_profile.get('quality_start_probability', 0):.1%}")
            print(f"      早退風險: {starter_profile.get('early_exit_risk', 0):.1%}")
            
            # 顯示牛棚分析
            bullpen_analysis = yankees_analysis.get('bullpen_analysis', {})
            print(f"   🎯 牛棚整體等級: {bullpen_analysis.get('bullpen_tier', 'Unknown').upper()}")
            
            middle_relief = bullpen_analysis.get('middle_relievers', {})
            setup_man = bullpen_analysis.get('setup_man', {}) 
            closer = bullpen_analysis.get('closer', {})
            
            print(f"      中繼投手 (7局): ERA {middle_relief.get('era', 0):.2f} | 可靠性 {middle_relief.get('reliability', 0):.1%}")
            print(f"      Setup Man (8局): ERA {setup_man.get('era', 0):.2f} | 可靠性 {setup_man.get('reliability', 0):.1%}")
            print(f"      終結者 (9局): ERA {closer.get('era', 0):.2f} | 救援率 {closer.get('save_rate', 0):.1%}")
            
            # 顯示局數分配
            innings_breakdown = yankees_analysis.get('innings_breakdown', {})
            print(f"   ⚾ 局數分配:")
            print(f"      先發投手: {innings_breakdown.get('starter_innings', 0):.1f} 局")
            print(f"      牛棚接力: {innings_breakdown.get('bullpen_innings', 0):.1f} 局") 
            
            # 顯示綜合預測
            combined_prediction = yankees_analysis.get('combined_prediction', {})
            print(f"   📊 失分預測:")
            print(f"      先發貢獻: {combined_prediction.get('starter_contribution', 0):.1f} 分")
            print(f"      牛棚貢獻: {combined_prediction.get('bullpen_contribution', 0):.1f} 分")
            print(f"      總預期失分: {combined_prediction.get('total_expected_runs_allowed', 0):.1f} 分")
            
            # 顯示牛棚明細分解
            bullpen_breakdown = combined_prediction.get('bullpen_breakdown', {})
            if bullpen_breakdown:
                print(f"   🔍 牛棚明細:")
                middle = bullpen_breakdown.get('middle_relief', {})
                setup = bullpen_breakdown.get('setup_man', {})
                closer_detail = bullpen_breakdown.get('closer', {})
                
                print(f"      中繼 ({middle.get('innings', 0):.1f}局): ERA {middle.get('era', 0):.2f} → {middle.get('expected_runs', 0):.2f}分")
                print(f"      Setup ({setup.get('innings', 0):.1f}局): ERA {setup.get('era', 0):.2f} → {setup.get('expected_runs', 0):.2f}分") 
                print(f"      終結 ({closer_detail.get('innings', 0):.1f}局): ERA {closer_detail.get('era', 0):.2f} → {closer_detail.get('expected_runs', 0):.2f}分")
            
            # 信心度評估
            confidence = yankees_analysis.get('confidence_assessment', {})
            print(f"   🎯 分析信心度: {confidence.get('confidence_level', 'Unknown').upper()} ({confidence.get('overall_confidence', 0):.1%})")
            
        else:
            print(f"❌ 洋基分析失敗: {yankees_analysis.get('error', 'Unknown error')}")
        
        print("\n" + "="*50)
        
        # 測試2: 分析紅襪投手陣容 (客隊)
        print("\n【測試2: 紅襪投手陣容分析】") 
        red_sox_analysis = await analyzer.analyze_complete_pitching_staff(
            starting_pitcher="Brayan Bello",
            team="BOS", 
            opposing_team="NYY",
            target_date=date(2025, 8, 23),
            is_home=False
        )
        
        if 'error' not in red_sox_analysis:
            print("✅ 紅襪投手分析成功!")
            
            # 簡化顯示 (主要數據)
            starter_profile = red_sox_analysis.get('starter_profile', {})
            combined_prediction = red_sox_analysis.get('combined_prediction', {})
            
            print(f"   🔥 先發: {starter_profile.get('pitcher_name')} ({starter_profile.get('pitcher_tier')})")
            print(f"      預期局數: {starter_profile.get('expected_innings', 0):.1f} | 早退風險: {starter_profile.get('early_exit_risk', 0):.1%}")
            print(f"   📊 總預期失分: {combined_prediction.get('total_expected_runs_allowed', 0):.1f} 分")
            
            bullpen_analysis = red_sox_analysis.get('bullpen_analysis', {})
            print(f"   🎯 牛棚等級: {bullpen_analysis.get('bullpen_tier', 'Unknown').upper()}")
            
        else:
            print(f"❌ 紅襪分析失敗: {red_sox_analysis.get('error', 'Unknown error')}")
        
        return yankees_analysis, red_sox_analysis

def compare_old_vs_new_system(yankees_analysis, red_sox_analysis):
    """比較新舊預測系統的差異"""
    
    print("\n📈 新舊系統比較")
    print("=" * 50)
    
    # 提取新系統預測
    yankees_prediction = yankees_analysis.get('combined_prediction', {})
    red_sox_prediction = red_sox_analysis.get('combined_prediction', {})
    
    yankees_runs = yankees_prediction.get('total_expected_runs_allowed', 0)
    red_sox_runs = red_sox_prediction.get('total_expected_runs_allowed', 0)
    
    print("🔴 舊系統 (簡化算法):")
    print(f"   BOS @ NYY: 4.0 - 5.0")
    print(f"   投手: 未確認 vs 未確認")
    print(f"   方法: 基於球隊平均，無投手細節")
    print(f"   牛棚: 統一ERA 4.20，無角色區分")
    print()
    
    print("🟢 新系統 (三層牛棚分析):")
    print(f"   BOS @ NYY: {red_sox_runs:.1f} - {yankees_runs:.1f}")
    print(f"   投手: Brayan Bello vs Gerrit Cole")
    print(f"   方法: 精細投手對戰 + 三層牛棚架構")
    
    # 顯示牛棚分層細節
    yankees_breakdown = yankees_prediction.get('bullpen_breakdown', {})
    if yankees_breakdown:
        print(f"   洋基牛棚架構:")
        middle = yankees_breakdown.get('middle_relief', {})
        setup = yankees_breakdown.get('setup_man', {})  
        closer = yankees_breakdown.get('closer', {})
        
        print(f"     • 中繼投手: {middle.get('innings', 0):.1f}局, ERA {middle.get('era', 0):.2f}")
        print(f"     • Setup Man: {setup.get('innings', 0):.1f}局, ERA {setup.get('era', 0):.2f}")
        print(f"     • 終結者: {closer.get('innings', 0):.1f}局, ERA {closer.get('era', 0):.2f}")
    
    print()
    print("💡 關鍵改進:")
    print("✅ 先發投手身份確認和歷史對戰分析")
    print("✅ 三層牛棚專業分工模擬 (中繼→Setup→終結)")
    print("✅ 投手局數限制的現實建模") 
    print("✅ 早退風險的連鎖效應計算")
    print("✅ 基於投手角色的信心度評估")
    print("✅ 主場優勢和對戰歷史整合")

def demonstrate_bullpen_impact_scenarios():
    """展示牛棚影響的不同情境"""
    
    print("\n🎭 牛棚策略情境分析")
    print("=" * 50)
    
    print("📊 情境1: 王牌先發 + 優秀牛棚")
    print("   先發: Gerrit Cole, 6.5局, ERA 2.80 → 2.0分")
    print("   中繼: 0.5局, ERA 3.00 → 0.17分") 
    print("   Setup: 1.0局, ERA 2.50 → 0.28分")
    print("   終結: 1.0局, ERA 2.00 → 0.22分")
    print("   總失分: 2.67分 🔥")
    print()
    
    print("📊 情境2: 掙扎先發 + 平均牛棚")
    print("   先發: 5.0局, ERA 6.00 → 3.33分")
    print("   中繼: 1.5局, ERA 4.50 → 0.75分")
    print("   Setup: 1.0局, ERA 3.80 → 0.42分") 
    print("   終結: 1.5局, ERA 3.50 → 0.58分")
    print("   總失分: 5.08分 💥")
    print()
    
    print("📊 情境3: 早退風險觸發")
    print("   先發: 提前退場 (4.5局)")
    print("   中繼: 需額外投1.0局 (疲勞影響)")
    print("   Setup: 壓力增加，表現下降")
    print("   風險調整: +0.5分")
    print("   展示了連鎖效應的重要性 ⚠️")

async def main():
    """主函數"""
    
    print("⚾ MLB投手+牛棚綜合分析系統測試")
    print("=" * 80)
    
    # 1. 展示MLB牛棚策略重要性
    demonstrate_mlb_bullpen_strategy()
    
    # 2. 測試完整分析系統
    print("\n")
    yankees_analysis, red_sox_analysis = await test_pitcher_bullpen_analyzer()
    
    # 3. 比較新舊系統
    if yankees_analysis and red_sox_analysis:
        compare_old_vs_new_system(yankees_analysis, red_sox_analysis)
    
    # 4. 展示不同情境的影響
    demonstrate_bullpen_impact_scenarios()
    
    print("\n" + "=" * 80)
    print("🎉 三層牛棚分析系統測試完成!")
    print()
    print("📋 系統特色:")
    print("✅ 現實的三層投手架構建模 (先發→中繼→Setup→終結)")
    print("✅ 投手角色專業化分析")
    print("✅ 早退風險的連鎖效應計算")
    print("✅ 基於歷史對戰的牛棚表現預測")
    print("✅ 詳細的局數和失分分解")
    print("✅ 多層次信心度評估")
    print()
    print("🔗 實戰應用:")
    print("• 更準確的得分預測 (考慮投手使用策略)")
    print("• 大小分投注的精細化分析")
    print("• 即時比賽中的投手替換影響評估")
    print("• 球隊牛棚深度的量化比較")

if __name__ == "__main__":
    asyncio.run(main())