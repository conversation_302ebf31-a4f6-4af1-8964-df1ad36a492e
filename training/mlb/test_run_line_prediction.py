#!/usr/bin/env python3
"""
測試讓分盤預測功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.run_line_predictor import RunLinePredictor

def test_run_line_prediction():
    """測試讓分盤預測功能"""
    
    print("🎯 測試讓分盤預測功能")
    print("=" * 60)
    
    # 創建應用
    app = create_app()
    
    # 創建讓分盤預測器
    run_line_predictor = RunLinePredictor(app)
    
    # 測試2025-07-07的比賽
    target_date = date(2025, 7, 7)
    
    # 獲取測試比賽的game_id
    with app.app_context():
        from models.database import Game
        
        test_games = Game.query.filter_by(date=target_date).limit(3).all()
        
        if not test_games:
            print("❌ 找不到測試比賽數據")
            return
        
        print(f"📊 找到 {len(test_games)} 場測試比賽")
        print("-" * 60)
        
        for i, game in enumerate(test_games, 1):
            print(f"\n🏟️  測試 {i}: {game.away_team} @ {game.home_team}")
            print(f"   Game ID: {game.game_id}")
            
            try:
                # 進行讓分盤預測
                prediction_result = run_line_predictor.predict_run_line(
                    game.game_id, 
                    target_date
                )
                
                if 'error' in prediction_result:
                    print(f"   ❌ 預測失敗: {prediction_result['error']}")
                    continue
                
                print(f"   ✅ 預測成功")
                
                # 顯示讓分盤數據
                run_line_data = prediction_result.get('run_line_data', {})
                if run_line_data:
                    print(f"   📊 讓分盤數據:")
                    print(f"       主隊讓分: {run_line_data.get('home_line', 'N/A')}")
                    print(f"       客隊讓分: {run_line_data.get('away_line', 'N/A')}")
                    print(f"       數據來源: {run_line_data.get('source', 'Unknown')}")
                    print(f"       是否真實數據: {run_line_data.get('is_real_data', False)}")
                
                # 顯示預測結果
                prediction = prediction_result.get('prediction', {})
                if prediction:
                    print(f"   🎯 預測結果:")
                    print(f"       推薦: {prediction.get('recommendation', 'N/A')}")
                    print(f"       信心度: {prediction.get('confidence', 'N/A')}")
                    print(f"       預測讓分: {prediction.get('predicted_spread', 'N/A')}")
                
                # 顯示球隊分析
                team_analysis = prediction_result.get('team_analysis', {})
                if team_analysis:
                    print(f"   📈 球隊分析:")
                    home_strength = team_analysis.get('home_team_strength', {})
                    away_strength = team_analysis.get('away_team_strength', {})
                    
                    if home_strength:
                        print(f"       主隊實力: {home_strength.get('overall_rating', 'N/A')}")
                    if away_strength:
                        print(f"       客隊實力: {away_strength.get('overall_rating', 'N/A')}")
                
            except Exception as e:
                print(f"   ❌ 預測異常: {e}")
    
    print(f"\n" + "=" * 60)
    print("🎉 讓分盤預測測試完成！")

if __name__ == "__main__":
    test_run_line_prediction()
