#!/bin/bash

echo "🔧 快速修復並測試增強預測引擎"
echo "=" * 50

# 激活虛擬環境
if [ -d ".venv" ]; then
    echo "🔧 激活虛擬環境..."
    source .venv/bin/activate
else
    echo "❌ 未找到虛擬環境，使用系統 Python"
fi

# 安裝缺少的依賴
echo "📦 安裝 nest_asyncio..."
pip install nest_asyncio

echo "✅ 依賴安裝完成"
echo ""
echo "🌐 請在另一個終端運行: ./start.sh"
echo "然後運行: python test_enhanced_api.py"
echo ""
echo "或者直接運行增強預測測試："
python test_enhanced_prediction_engine.py