#!/usr/bin/env python3
"""
测试大小分显示功能 - 验证盘口信息是否正确显示
"""

import requests
import json
from datetime import date, timedelta

def test_over_under_display():
    """测试大小分显示功能"""
    base_url = 'http://127.0.0.1:5500'
    
    print('🔍 测试大小分盘口信息显示')
    print('=' * 60)
    
    # 1. 测试API响应
    print('1️⃣ 测试API响应中的大小分数据...')
    try:
        # 检查最近几天的预测
        for days_back in range(3):
            check_date = date.today() - timedelta(days=days_back)
            
            response = requests.get(f'{base_url}/unified/api/predictions/date/{check_date}', timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    predictions = data.get('predictions', [])
                    if predictions:
                        print(f'📅 {check_date}: 找到 {len(predictions)} 条预测')
                        
                        # 分析第一条预测的大小分数据
                        pred = predictions[0]
                        print(f'   🏟️ 比赛: {pred.get("away_team", "N/A")} @ {pred.get("home_team", "N/A")}')
                        
                        # 检查大小分分析数据
                        ou_analysis = pred.get('over_under_analysis')
                        if ou_analysis:
                            print(f'   ✅ 包含详细大小分分析:')
                            print(f'      📊 盘口: {ou_analysis.get("line", "N/A")}')
                            print(f'      🎯 预测总分: {ou_analysis.get("predicted_total", "N/A")}')
                            print(f'      📈 差距: {ou_analysis.get("difference", "N/A")}')
                            print(f'      🔥 推荐: {ou_analysis.get("recommendation_text", "N/A")}')
                            print(f'      💪 强度: {ou_analysis.get("strength", "N/A")}')
                        else:
                            print(f'   ⚠️ 缺少详细大小分分析')
                        
                        # 检查原始大小分数据
                        over_under = pred.get('predictions', {}).get('over_under', {})
                        if over_under and 'error' not in over_under:
                            print(f'   📋 原始大小分数据:')
                            print(f'      盘口: {over_under.get("total_line", "N/A")}')
                            print(f'      预测总分: {over_under.get("expected_runs", {}).get("total", "N/A")}')
                            print(f'      大分概率: {over_under.get("over_probability", 0)*100:.1f}%')
                            print(f'      小分概率: {over_under.get("under_probability", 0)*100:.1f}%')
                            print(f'      使用真实盘口: {over_under.get("使用真實盤口", False)}')
                        
                        print()
                        break
                else:
                    print(f'📅 {check_date}: API错误')
            else:
                print(f'📅 {check_date}: HTTP错误 {response.status_code}')
    
    except Exception as e:
        print(f'❌ API测试失败: {e}')
    
    print()
    
    # 2. 测试网页显示
    print('2️⃣ 测试网页显示...')
    try:
        response = requests.get(f'{base_url}/unified', timeout=15)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查是否包含新的显示元素
            checks = [
                ('盤口:', '盘口标签'),
                ('預測總分:', '预测总分标签'),
                ('真實', '真实盘口标记'),
                ('模擬', '模拟盘口标记'),
                ('信心度:', '信心度标签'),
                ('over_under_analysis', '大小分分析数据')
            ]
            
            print('   🔍 检查页面元素:')
            for check_text, description in checks:
                if check_text in content:
                    print(f'   ✅ 找到 {description}')
                else:
                    print(f'   ⚠️ 未找到 {description}')
                    
        else:
            print(f'   ❌ 页面访问失败: {response.status_code}')
            
    except Exception as e:
        print(f'❌ 网页测试失败: {e}')
    
    print()
    
    # 3. 测试查询页面
    print('3️⃣ 测试查询页面显示...')
    try:
        yesterday = date.today() - timedelta(days=1)
        response = requests.get(f'{base_url}/unified/query?date={yesterday}', timeout=15)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查查询页面的新元素
            query_checks = [
                ('實際總分:', '实际总分显示'),
                ('預測>盤口', '预测vs盘口分析'),
                ('預測<盤口', '预测vs盘口分析'),
                ('預測正確', '准确性标记'),
                ('預測錯誤', '准确性标记')
            ]
            
            print('   🔍 检查查询页面元素:')
            for check_text, description in query_checks:
                if check_text in content:
                    print(f'   ✅ 找到 {description}')
                else:
                    print(f'   ⚠️ 未找到 {description}')
                    
        else:
            print(f'   ❌ 查询页面访问失败: {response.status_code}')
            
    except Exception as e:
        print(f'❌ 查询页面测试失败: {e}')
    
    print()
    print('🎉 大小分显示功能测试完成!')
    print()
    print('💡 新增功能说明:')
    print('   📊 显示详细盘口信息 (真实/模拟)')
    print('   🎯 显示预测总分 vs 盘口对比')
    print('   📈 显示大分/小分概率')
    print('   💪 显示推荐强度分析')
    print('   ✅ 显示预测准确性标记')
    print('   📋 便于分析和改进预测模型')

if __name__ == "__main__":
    test_over_under_display()
