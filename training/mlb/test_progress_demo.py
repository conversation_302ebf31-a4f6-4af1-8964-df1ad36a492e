#!/usr/bin/env python3
"""
進度監控系統演示腳本
模擬一個月度數據下載任務
"""

import sys
sys.path.append('.')

import time
import random
from progress_monitor import progress_monitor
from datetime import datetime

def demo_monthly_download():
    """演示月度下載進度監控"""
    
    # 生成任務ID
    task_id = f"demo_monthly_download_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"🚀 開始演示任務: {task_id}")
    
    try:
        # 啟動任務監控
        progress_monitor.start_task(
            task_id=task_id,
            task_name="演示：2025年8月整月數據下載",
            total_steps=100
        )
        
        # 模擬各個步驟
        steps = [
            (5, "🔧 初始化數據管理器..."),
            (15, "📅 獲取官方賽程數據..."),
            (25, "🏟️ 下載比賽基本數據..."),
            (45, "📊 處理boxscore詳細數據..."),
            (65, "⚾ 收集投手統計數據..."),
            (80, "🎯 生成缺失場次的預測..."),
            (90, "🔍 執行數據完整性驗證..."),
            (95, "📋 生成質量改善報告..."),
        ]
        
        for step, message in steps:
            progress_monitor.update_progress(task_id, step, message)
            print(f"   進度 {step}%: {message}")
            
            # 模擬處理時間
            time.sleep(2)
            
            # 模擬偶爾的警告
            if step == 45 and random.choice([True, False]):
                progress_monitor.add_error(task_id, "⚠️ 某個比賽的boxscore數據不完整，跳過處理")
        
        # 完成任務
        final_message = "✅ 演示完成！質量分數改善: +12.5%, 新增: 28場比賽, 84條預測, 156條賠率"
        progress_monitor.complete_task(task_id, success=True, final_message=final_message)
        
        print("✅ 演示任務完成！")
        print(f"   任務ID: {task_id}")
        print("   使用 'python monitor_progress.py' 查看任務狀態")
        print("   使用 'python monitor_progress.py --watch' 開始持續監控")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷演示")
        progress_monitor.complete_task(task_id, success=False, final_message="用戶中斷演示")
    except Exception as e:
        print(f"❌ 演示失敗: {e}")
        progress_monitor.add_error(task_id, f"演示失敗: {e}")
        progress_monitor.complete_task(task_id, success=False, final_message=f"演示失敗: {e}")

if __name__ == "__main__":
    demo_monthly_download()