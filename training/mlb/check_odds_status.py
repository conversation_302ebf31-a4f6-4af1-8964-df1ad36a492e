#!/usr/bin/env python3
"""
檢查盤口數據狀況
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, BettingOdds, Game

def check_odds_status():
    """檢查盤口數據狀況"""
    app = create_app()
    
    with app.app_context():
        test_date = date(2025, 7, 7)
        games = Game.query.filter_by(date=test_date).all()
        game_ids = [g.game_id for g in games]
        
        odds = BettingOdds.query.filter(BettingOdds.game_id.in_(game_ids)).all()
        
        print(f'2025-07-07 比賽數: {len(games)}')
        print(f'現有盤口記錄: {len(odds)}')
        
        if odds:
            print('現有盤口詳情:')
            for o in odds[:5]:
                game = Game.query.filter_by(game_id=o.game_id).first()
                if game:
                    print(f'  - {game.away_team} @ {game.home_team}')
                    print(f'    博彩商: {o.bookmaker}')
                    print(f'    總分線: {o.total_point}')
                    print(f'    讓分線: {o.home_spread_point}')
        else:
            print('沒有盤口數據')

if __name__ == "__main__":
    check_odds_status()
