#!/usr/bin/env python3
"""
測試 MLBPredictor 與 EnhancedPitcherPredictor 的整合
驗證投手特徵是否正確整合到主預測系統中
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from datetime import date, datetime
from models.database import db, Game, GameDetail
from models.ml_predictor import MLBPredictor
from app import create_app

def test_ml_predictor_integration():
    """測試 MLBPredictor 與增強投手預測器的整合"""
    
    app = create_app()
    
    with app.app_context():
        print("🎯 測試 MLBPredictor 投手特徵整合")
        print("=" * 60)
        
        # 1. 初始化預測器
        print("\n🔧 初始化 MLBPredictor...")
        try:
            predictor = MLBPredictor()
            print("✅ MLBPredictor 初始化成功")
            print(f"✅ EnhancedPitcherPredictor 已整合")
        except Exception as e:
            print(f"❌ 預測器初始化失敗: {e}")
            return
        
        # 2. 獲取測試數據
        print("\n📊 獲取測試數據...")
        
        # 查找有完整投手信息的比賽
        test_games = Game.query.join(GameDetail).filter(
            GameDetail.home_starting_pitcher.isnot(None),
            GameDetail.away_starting_pitcher.isnot(None),
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).limit(2).all()
        
        if not test_games:
            print("❌ 未找到合適的測試比賽")
            return
        
        print(f"✅ 找到 {len(test_games)} 場測試比賽")
        
        # 3. 準備測試數據框
        games_data = []
        for game in test_games:
            games_data.append({
                'game_id': game.game_id,
                'home_team': game.home_team,
                'away_team': game.away_team,
                'date': game.date,
                'home_score': game.home_score,
                'away_score': game.away_score
            })
        
        games_df = pd.DataFrame(games_data)
        
        # 4. 測試特徵提取
        print("\n⚾ 測試增強特徵提取...")
        
        try:
            features_df = predictor.extract_features(games_df)
            
            print(f"✅ 成功提取特徵")
            print(f"   比賽數量: {len(features_df)}")
            print(f"   特徵數量: {len(features_df.columns)}")
            
            # 5. 分析投手特徵
            print("\n🎯 分析投手特徵...")
            
            pitcher_features = [col for col in features_df.columns if any(keyword in col for keyword in [
                'starter', 'pitcher', 'bullpen', 'ace', 'fatigue', 'matchup'
            ])]
            
            print(f"投手相關特徵數量: {len(pitcher_features)}")
            
            if len(pitcher_features) > 20:  # 期望有 43 個投手特徵
                print("✅ 投手特徵整合成功")
                
                # 顯示部分投手特徵
                print("\n📋 投手特徵樣本:")
                for feature in pitcher_features[:10]:
                    value = features_df[feature].iloc[0]
                    print(f"   {feature}: {value}")
                
                if len(pitcher_features) > 10:
                    print(f"   ... 還有 {len(pitcher_features) - 10} 個投手特徵")
                
            else:
                print("⚠️ 投手特徵數量不足，可能整合失敗")
            
            # 6. 分析具體比賽案例
            print("\n🏟️ 分析具體比賽案例:")
            
            for i, (_, game_features) in enumerate(features_df.iterrows()):
                if i >= 1:  # 只分析第1場比賽
                    break
                    
                game = test_games[i]
                game_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
                
                print(f"\n比賽: {game.away_team} @ {game.home_team}")
                print(f"   日期: {game.date}")
                print(f"   比分: {game.away_score} - {game.home_score}")
                print(f"   先發: {game_detail.away_starting_pitcher} vs {game_detail.home_starting_pitcher}")
                
                # 檢查關鍵投手特徵
                key_features = [
                    'home_starter_quality_score',
                    'away_starter_quality_score', 
                    'home_starter_is_ace',
                    'away_starter_is_ace',
                    'home_bullpen_quality_score',
                    'away_bullpen_quality_score',
                    'pitcher_matchup_advantage'
                ]
                
                print("   關鍵投手特徵:")
                for feature in key_features:
                    if feature in game_features:
                        value = game_features[feature]
                        print(f"     {feature}: {value}")
                    else:
                        print(f"     {feature}: 缺失")
            
            print("\n✅ MLBPredictor 投手特徵整合測試完成!")
            
        except Exception as e:
            print(f"❌ 特徵提取失敗: {e}")
            import traceback
            traceback.print_exc()

def test_feature_comparison():
    """比較整合前後的特徵數量"""
    
    app = create_app()
    
    with app.app_context():
        print("\n📊 比較整合前後的特徵數量")
        print("=" * 60)
        
        try:
            predictor = MLBPredictor()
            
            # 獲取一場測試比賽
            test_game = Game.query.join(GameDetail).filter(
                GameDetail.home_starting_pitcher.isnot(None),
                GameDetail.away_starting_pitcher.isnot(None)
            ).first()
            
            if not test_game:
                print("❌ 未找到測試比賽")
                return
            
            # 準備測試數據
            games_df = pd.DataFrame([{
                'game_id': test_game.game_id,
                'home_team': test_game.home_team,
                'away_team': test_game.away_team,
                'date': test_game.date,
                'home_score': test_game.home_score,
                'away_score': test_game.away_score
            }])
            
            # 提取特徵
            features_df = predictor.extract_features(games_df)
            
            # 分類特徵
            all_features = list(features_df.columns)
            
            basic_features = [col for col in all_features if not any(keyword in col for keyword in [
                'starter', 'pitcher', 'bullpen', 'ace', 'fatigue', 'matchup'
            ])]
            
            pitcher_features = [col for col in all_features if any(keyword in col for keyword in [
                'starter', 'pitcher', 'bullpen', 'ace', 'fatigue', 'matchup'
            ])]
            
            print(f"總特徵數: {len(all_features)}")
            print(f"基本特徵數: {len(basic_features)}")
            print(f"投手特徵數: {len(pitcher_features)}")
            print(f"投手特徵佔比: {len(pitcher_features)/len(all_features)*100:.1f}%")
            
            if len(pitcher_features) >= 40:
                print("✅ 投手特徵整合成功 - 達到預期數量")
            else:
                print("⚠️ 投手特徵數量低於預期")
            
        except Exception as e:
            print(f"❌ 特徵比較失敗: {e}")

if __name__ == "__main__":
    print("🚀 開始測試 MLBPredictor 投手特徵整合")
    
    # 測試整合效果
    test_ml_predictor_integration()
    
    # 比較特徵數量
    test_feature_comparison()
    
    print("\n🎉 測試完成!")
