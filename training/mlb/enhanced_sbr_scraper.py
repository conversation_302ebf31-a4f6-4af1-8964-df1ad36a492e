#!/usr/bin/env python3
"""
增強型SportsbookReview賠率抓取器
使用多種策略解析網頁數據，包括DOM解析和模式匹配
"""

import requests
import re
import json
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
import time
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class EnhancedSBRScraper:
    """增強型SportsbookReview MLB賠率抓取器"""
    
    def __init__(self):
        self.base_url = "https://www.sportsbookreview.com"
        self.session = requests.Session()
        
        # 設置現代瀏覽器標識
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
        })
        
        self.bookmaker_mapping = {
            'BetMGM': 'betmgm',
            'FanDuel': 'fanduel', 
            'Caesars': 'caesars',
            'DraftKings': 'draftkings',
            'BetRivers': 'betrivers',
            'bet365': 'bet365'
        }

    def fetch_current_odds(self, target_date: date = None) -> Dict:
        """抓取當前或指定日期的賠率數據"""
        if target_date is None:
            target_date = date.today()
        
        date_str = target_date.strftime("%Y-%m-%d")
        
        logger.info(f"📊 開始抓取 {date_str} 的MLB賠率數據")
        
        # 嘗試多種抓取策略
        strategies = [
            self._fetch_with_dom_parsing,
            self._fetch_with_regex_patterns, 
            self._fetch_with_json_extraction
        ]
        
        for i, strategy in enumerate(strategies, 1):
            try:
                logger.info(f"🔄 嘗試策略 {i}: {strategy.__name__}")
                result = strategy(date_str)
                if result and result.get('games'):
                    logger.info(f"✅ 策略 {i} 成功！獲取到 {len(result['games'])} 場比賽數據")
                    return result
            except Exception as e:
                logger.warning(f"❌ 策略 {i} 失敗: {e}")
                continue
        
        logger.error("🚨 所有抓取策略都失敗了")
        return {'games': [], 'source': 'enhanced_sbr_scraper', 'error': 'All strategies failed'}

    def _fetch_with_dom_parsing(self, date_str: str) -> Dict:
        """策略1: 使用DOM解析抓取賠率數據"""
        url = f"{self.base_url}/betting-odds/mlb-baseball/pointspread/full-game/?date={date_str}"
        
        response = self.session.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        games_data = []
        
        # 查找比賽行（根據截圖中的HTML結構）
        game_rows = soup.find_all('tr', class_='GameRows_row')
        if not game_rows:
            # 嘗試其他可能的選擇器
            game_rows = soup.find_all('tr', {'data-testid': lambda x: x and 'game-row' in x})
        
        if not game_rows:
            # 最後嘗試：尋找包含隊伍名稱的行
            game_rows = soup.find_all('tr')
            game_rows = [row for row in game_rows if self._contains_team_names(row)]
        
        logger.info(f"🎯 找到 {len(game_rows)} 行比賽數據")
        
        for row in game_rows:
            try:
                game_info = self._parse_game_row_dom(row)
                if game_info:
                    games_data.append(game_info)
            except Exception as e:
                logger.debug(f"解析比賽行失敗: {e}")
                continue
        
        return {
            'games': games_data,
            'source': 'dom_parsing',
            'timestamp': datetime.now().isoformat(),
            'total_games': len(games_data)
        }

    def _fetch_with_regex_patterns(self, date_str: str) -> Dict:
        """策略2: 使用正則表達式模式匹配"""
        url = f"{self.base_url}/betting-odds/mlb-baseball/pointspread/full-game/?date={date_str}"
        
        response = self.session.get(url, timeout=30)
        response.raise_for_status()
        
        html_content = response.text
        games_data = []
        
        # 模式1: 匹配時間和隊伍信息
        time_team_pattern = r'(\d{1,2}:\d{2}\s*(?:AM|PM)\s*EDT?)\s*([A-Z]{3})\s*-\s*([A-Za-z\.\s]+)\s*([A-Z]{3})\s*-\s*([A-Za-z\.\s]+)'
        
        matches = re.findall(time_team_pattern, html_content, re.IGNORECASE)
        
        for match in matches:
            game_time, away_code, away_pitcher, home_code, home_pitcher = match
            
            # 查找對應的賠率數據
            odds_info = self._extract_odds_for_teams(html_content, away_code, home_code)
            
            game_data = {
                'game_time': game_time.strip(),
                'away_team': away_code.strip(),
                'home_team': home_code.strip(), 
                'away_pitcher': away_pitcher.strip(),
                'home_pitcher': home_pitcher.strip(),
                'odds': odds_info
            }
            
            games_data.append(game_data)
        
        return {
            'games': games_data,
            'source': 'regex_patterns',
            'timestamp': datetime.now().isoformat(),
            'total_games': len(games_data)
        }

    def _fetch_with_json_extraction(self, date_str: str) -> Dict:
        """策略3: JSON數據提取（改進版本）"""
        url = f"{self.base_url}/betting-odds/mlb-baseball/pointspread/full-game/?date={date_str}"
        
        response = self.session.get(url, timeout=30)
        response.raise_for_status()
        
        html_content = response.text
        games_data = []
        
        # 改進的JSON提取模式
        json_patterns = [
            # Next.js數據
            r'<script id="__NEXT_DATA__" type="application/json">(.*?)</script>',
            # 內聯JSON數據
            r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
            r'window\.__PRELOADED_STATE__\s*=\s*({.*?});',
            # 賠率表數據
            r'"oddsTables":\s*(\[.*?\])',
            r'"gameRows":\s*(\[.*?\])',
            # React props
            r'"props":\s*({.*?"oddsTables":.*?})',
        ]
        
        for pattern in json_patterns:
            matches = re.finditer(pattern, html_content, re.DOTALL)
            for match in matches:
                try:
                    json_str = match.group(1)
                    data = json.loads(json_str)
                    
                    # 嘗試解析數據
                    extracted_games = self._extract_games_from_json(data)
                    if extracted_games:
                        games_data.extend(extracted_games)
                        logger.info(f"✅ JSON提取成功，獲得 {len(extracted_games)} 場比賽")
                        break
                        
                except (json.JSONDecodeError, KeyError) as e:
                    logger.debug(f"JSON解析失敗: {e}")
                    continue
        
        return {
            'games': games_data,
            'source': 'json_extraction',
            'timestamp': datetime.now().isoformat(), 
            'total_games': len(games_data)
        }

    def _parse_game_row_dom(self, row) -> Optional[Dict]:
        """解析比賽行的DOM元素"""
        try:
            # 提取時間
            time_cell = row.find('td', class_='time') or row.find(attrs={'data-testid': 'game-time'})
            game_time = time_cell.get_text(strip=True) if time_cell else ""
            
            # 提取隊伍信息
            teams_cell = row.find('td', class_='teams') or row.find(attrs={'data-testid': 'teams'})
            if not teams_cell:
                return None
                
            team_elements = teams_cell.find_all(['span', 'div'], class_=re.compile(r'team|code'))
            
            if len(team_elements) >= 2:
                away_team = team_elements[0].get_text(strip=True)
                home_team = team_elements[1].get_text(strip=True)
            else:
                # 後備方案：從文本中提取
                teams_text = teams_cell.get_text()
                teams = re.findall(r'([A-Z]{3})', teams_text)
                if len(teams) >= 2:
                    away_team, home_team = teams[0], teams[1]
                else:
                    return None
            
            # 提取賠率信息
            odds_cells = row.find_all('td', class_=re.compile(r'odds|sportsbook'))
            odds_data = {}
            
            for cell in odds_cells:
                bookmaker = self._identify_bookmaker(cell)
                if bookmaker:
                    spread_odds = self._extract_spread_from_cell(cell)
                    if spread_odds:
                        odds_data[bookmaker] = spread_odds
            
            return {
                'game_time': game_time,
                'away_team': away_team,
                'home_team': home_team,
                'odds': odds_data
            }
            
        except Exception as e:
            logger.debug(f"解析比賽行失敗: {e}")
            return None

    def _contains_team_names(self, row) -> bool:
        """檢查行是否包含隊伍名稱"""
        text = row.get_text()
        # 尋找MLB隊伍代碼模式
        mlb_teams = ['NYY', 'BOS', 'LAD', 'SF', 'CHC', 'ATL', 'HOU', 'TB', 'NYM', 'WSN', 
                    'PHI', 'MIA', 'CIN', 'MIL', 'STL', 'COL', 'ARI', 'SD', 'LAA', 'OAK',
                    'TEX', 'SEA', 'MIN', 'CWS', 'DET', 'KC', 'CLE', 'BAL', 'TOR', 'PIT']
        
        return any(team in text for team in mlb_teams)

    def _identify_bookmaker(self, cell) -> Optional[str]:
        """識別博彩商"""
        cell_text = cell.get_text().lower()
        class_attr = ' '.join(cell.get('class', []))
        
        for bookmaker, code in self.bookmaker_mapping.items():
            if code.lower() in cell_text or code.lower() in class_attr.lower():
                return bookmaker
        return None

    def _extract_spread_from_cell(self, cell) -> Optional[Dict]:
        """從單元格提取讓分賠率"""
        try:
            text = cell.get_text(strip=True)
            
            # 匹配讓分格式: -1.5 +115
            spread_pattern = r'([+-]\d+\.?\d*)\s*([+-]\d+)'
            match = re.search(spread_pattern, text)
            
            if match:
                point, price = match.groups()
                return {
                    'point': float(point),
                    'price': int(price)
                }
        except:
            pass
        return None

    def _extract_odds_for_teams(self, html_content: str, away_team: str, home_team: str) -> Dict:
        """為特定隊伍提取賠率"""
        # 在HTML中搜索包含這些隊伍的賠率信息
        team_section_pattern = f'({away_team}.*?{home_team}.*?)(?:{away_team}|{home_team}|\n|\r)'
        
        match = re.search(team_section_pattern, html_content, re.IGNORECASE | re.DOTALL)
        if match:
            section = match.group(1)
            
            # 提取賠率數字
            odds_pattern = r'([+-]\d+\.?\d*)\s*([+-]\d+)'
            odds_matches = re.findall(odds_pattern, section)
            
            odds_data = {}
            for point, price in odds_matches[:3]:  # 最多取前3個賠率
                odds_data[f'option_{len(odds_data)+1}'] = {
                    'point': float(point),
                    'price': int(price)
                }
            
            return odds_data
        
        return {}

    def _extract_games_from_json(self, data: Dict) -> List[Dict]:
        """從JSON數據中提取比賽信息"""
        games = []
        
        try:
            # 嘗試不同的JSON結構路徑
            paths = [
                ['props', 'pageProps', 'oddsTables'],
                ['pageProps', 'oddsTables'],
                ['oddsTables'],
                ['data', 'games'],
                ['games']
            ]
            
            tables_data = None
            for path in paths:
                current = data
                try:
                    for key in path:
                        current = current[key]
                    if isinstance(current, list) and current:
                        tables_data = current
                        break
                except (KeyError, TypeError):
                    continue
            
            if not tables_data:
                return []
            
            # 解析表格數據
            for table in tables_data:
                if isinstance(table, dict):
                    game_rows = table.get('oddsTableModel', {}).get('gameRows', [])
                    for row in game_rows:
                        game = self._parse_json_game_row(row)
                        if game:
                            games.append(game)
            
        except Exception as e:
            logger.debug(f"JSON遊戲提取失敗: {e}")
        
        return games

    def _parse_json_game_row(self, row: Dict) -> Optional[Dict]:
        """解析JSON格式的比賽行（基於實際數據結構）"""
        try:
            # 基於調試結果的真實數據結構
            game_view = row.get('gameView', {})
            odds_views = row.get('oddsViews', [])
            
            if not game_view:
                return None
                
            # 提取隊伍信息（實際結構）
            away_team_data = game_view.get('awayTeam', {})
            home_team_data = game_view.get('homeTeam', {})
            
            if not away_team_data or not home_team_data:
                return None
            
            # 提取隊伍縮寫和名稱
            away_team = away_team_data.get('shortName', away_team_data.get('nickname', 'N/A'))
            home_team = home_team_data.get('shortName', home_team_data.get('nickname', 'N/A'))
            
            # 提取投手信息
            away_starter = game_view.get('awayStarter', {})
            home_starter = game_view.get('homeStarter', {})
            
            away_pitcher = f"{away_starter.get('firstInital', '')}. {away_starter.get('lastName', '')}" if away_starter else 'N/A'
            home_pitcher = f"{home_starter.get('firstInital', '')}. {home_starter.get('lastName', '')}" if home_starter else 'N/A'
            
            # 提取開始時間
            start_date = game_view.get('startDate', '')
            
            # 解析賠率數據
            odds_data = {}
            
            for odds_view in odds_views:
                # 博彩商信息
                sportsbook = odds_view.get('sportsbook', {})
                book_name = sportsbook.get('name', '') if isinstance(sportsbook, dict) else str(sportsbook)
                
                if not book_name:
                    continue
                
                # 從當前盤口提取實際賠率數據
                current_line = odds_view.get('currentLine', {})
                
                if current_line and isinstance(current_line, dict):
                    # 提取讓分和賠率
                    home_spread = current_line.get('homeSpread')
                    away_spread = current_line.get('awaySpread')
                    home_odds = current_line.get('homeOdds')
                    away_odds = current_line.get('awayOdds')
                    total = current_line.get('total')
                    over_odds = current_line.get('overOdds')
                    under_odds = current_line.get('underOdds')
                    
                    odds_entry = {}
                    
                    # 讓分盤數據
                    if home_spread is not None or away_spread is not None:
                        odds_entry['spread'] = {
                            'home_spread': home_spread,
                            'home_odds': home_odds,
                            'away_spread': away_spread, 
                            'away_odds': away_odds
                        }
                    
                    # 大小分數據
                    if total is not None:
                        odds_entry['totals'] = {
                            'total_line': total,
                            'over_odds': over_odds,
                            'under_odds': under_odds
                        }
                    
                    # 勝負盤（Money Line）
                    if home_odds is not None or away_odds is not None:
                        odds_entry['moneyline'] = {
                            'home_odds': home_odds,
                            'away_odds': away_odds
                        }
                    
                    if odds_entry:  # 只有在有數據時才添加
                        odds_data[book_name] = odds_entry
            
            # 構造返回數據
            game_data = {
                'away_team': away_team,
                'home_team': home_team,
                'away_pitcher': away_pitcher.strip(),
                'home_pitcher': home_pitcher.strip(),
                'game_time': start_date,
                'venue': game_view.get('venueName', ''),
                'game_id': game_view.get('gameId', ''),
                'odds': odds_data
            }
            
            return game_data
            
        except Exception as e:
            logger.debug(f"解析JSON比賽行失敗: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return None

# 測試函數
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    scraper = EnhancedSBRScraper()
    result = scraper.fetch_current_odds()
    
    print(f"獲取到 {len(result.get('games', []))} 場比賽數據")
    for game in result.get('games', [])[:3]:  # 顯示前3場
        print(f"⚾ {game.get('away_team')} @ {game.get('home_team')}")
        print(f"   時間: {game.get('game_time')}")
        print(f"   賠率: {game.get('odds', {})}")
        print()