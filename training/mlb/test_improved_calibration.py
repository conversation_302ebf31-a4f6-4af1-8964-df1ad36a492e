#!/usr/bin/env python3
"""
測試改進的校正算法效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.ml_predictor import MLBPredictor
from models.over_under_predictor import OverUnderPredictor

def test_improved_calibration():
    """測試改進的校正效果"""
    print("🧪 測試改進的校正算法效果")
    print("=" * 60)
    
    app = create_app()
    with app.app_context():
        ml_predictor = MLBPredictor()
        ou_predictor = OverUnderPredictor(app)
        
        # 基於實際問題案例測試
        test_cases = [
            # (原始主隊, 原始客隊, 實際總分, 描述)
            (7.0, 6.0, 4, "NYM @ KC - 原預測13分，實際4分"),
            (4.5, 4.2, 3, "LAD @ SF - 原預測8.7分，實際3分"),
            (6.0, 5.1, 8, "CLE @ CWS - 原預測11.1分，實際8分"),
            (7.5, 4.3, 7, "TOR @ ATH - 原預測11.8分，實際7分"),
            (8.5, 8.8, 9, "TEX @ HOU - 原預測17.3分，實際9分"),
            (5.0, 5.0, 11, "WSH @ MIL - 原預測10.1分，實際11分"),
        ]
        
        print("測試案例 (原始預測 -> 改進校正 vs 實際結果):")
        print("-" * 80)
        print("描述                           原始    校正    實際    誤差改善")
        print("-" * 80)
        
        total_original_error = 0
        total_calibrated_error = 0
        
        for home, away, actual, desc in test_cases:
            # 原始預測
            original_total = home + away
            
            # 應用改進的校正
            calibrated_home, calibrated_away = ml_predictor._apply_score_calibration(home, away)
            calibrated_total = calibrated_home + calibrated_away
            
            # 計算誤差
            original_error = abs(original_total - actual)
            calibrated_error = abs(calibrated_total - actual)
            error_improvement = original_error - calibrated_error
            
            total_original_error += original_error
            total_calibrated_error += calibrated_error
            
            status = "✅" if calibrated_error < original_error else "❌"
            
            print(f"{desc[:30]:30} {original_total:5.1f}   {calibrated_total:5.1f}   {actual:5.0f}   {error_improvement:+5.1f} {status}")
        
        print("-" * 80)
        avg_original_error = total_original_error / len(test_cases)
        avg_calibrated_error = total_calibrated_error / len(test_cases)
        overall_improvement = avg_original_error - avg_calibrated_error
        
        print(f"平均誤差改善: {avg_original_error:.2f} -> {avg_calibrated_error:.2f} (改善 {overall_improvement:+.2f})")
        
        # 測試校正參數
        print(f"\n📊 新校正參數:")
        print(f"  主隊得分降低: 35% (0.65倍)")
        print(f"  客隊得分降低: 25% (0.75倍)")
        print(f"  總分上限: 9.5分")
        print(f"  高分懲罰閾值: 8分")
        print(f"  懲罰因子: 0.7倍")
        
        return overall_improvement > 0

def test_extreme_cases():
    """測試極端案例"""
    print(f"\n🔥 測試極端高分預測案例:")
    print("-" * 50)
    
    app = create_app()
    with app.app_context():
        ml_predictor = MLBPredictor()
        
        extreme_cases = [
            (12.0, 10.0, "極高分預測 (22分)"),
            (15.0, 8.0, "超高分預測 (23分)"),
            (9.0, 9.0, "高分預測 (18分)"),
            (6.0, 6.0, "中等預測 (12分)"),
            (3.0, 3.0, "低分預測 (6分)")
        ]
        
        for home, away, desc in extreme_cases:
            original_total = home + away
            calibrated_home, calibrated_away = ml_predictor._apply_score_calibration(home, away)
            calibrated_total = calibrated_home + calibrated_away
            reduction = original_total - calibrated_total
            
            print(f"{desc:20} {original_total:5.1f} -> {calibrated_total:5.1f} (降低 {reduction:.1f}分)")

def main():
    """主要測試流程"""
    print("🎯 改進校正算法測試")
    print("目標: 修復7.9%準確率和+2.12分系統性偏差")
    print("=" * 60)
    
    # 測試改進效果
    improvement = test_improved_calibration()
    
    # 測試極端案例
    test_extreme_cases()
    
    print(f"\n📋 測試結果:")
    if improvement:
        print("✅ 校正算法改進有效，誤差明顯減少")
        print("✅ 建議立即部署到生產環境")
    else:
        print("❌ 校正算法需要進一步調整")
        print("❌ 建議重新分析數據並調整參數")
    
    print(f"\n🚀 下一步行動:")
    print("1. 重新生成所有預測以應用新校正")
    print("2. 監控未來幾天的預測準確率")
    print("3. 如果準確率仍低於50%，考慮重新訓練模型")
    print("4. 整合投手ERA和天氣因素進一步優化")

if __name__ == "__main__":
    main()
