#!/usr/bin/env python3
"""
測試單個日期的賠率抓取 - 使用最後一次API請求
"""

import sys
import os
from datetime import date

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, BettingOdds
from models.odds_data_fetcher import OddsDataFetcher

def test_single_date():
    """測試單個日期的賠率抓取"""
    print("🧪 測試單個日期的賠率抓取")
    print("=" * 60)
    
    # 選擇一個有較多已完成比賽的日期
    target_date = date(2025, 6, 29)  # 15場已完成比賽
    
    app = create_app()
    
    with app.app_context():
        fetcher = OddsDataFetcher(app)
        
        # 1. 檢查API狀態
        print("🔑 檢查API狀態...")
        status = fetcher.check_api_status()
        
        if not status['api_accessible']:
            print(f"❌ API不可用: {status['message']}")
            return
        
        remaining = status.get('remaining_requests', 'N/A')
        print(f"✅ API可用，剩餘請求: {remaining}")
        
        if remaining and int(remaining) < 1:
            print(f"❌ API配額不足，無法進行測試")
            return
        
        # 2. 檢查目標日期的比賽
        games = Game.query.filter(
            Game.date == target_date,
            Game.game_status == 'completed'
        ).all()
        
        print(f"\n📅 目標日期: {target_date}")
        print(f"   已完成比賽: {len(games)} 場")
        
        if not games:
            print(f"❌ 該日期沒有已完成的比賽")
            return
        
        # 顯示前5場比賽
        print(f"   比賽列表 (前5場):")
        for i, game in enumerate(games[:5]):
            print(f"     [{i+1}] {game.away_team} @ {game.home_team} ({game.game_status})")
        
        # 3. 檢查是否已有賠率數據
        existing_odds = db.session.query(BettingOdds)\
            .join(Game, BettingOdds.game_id == Game.game_id)\
            .filter(Game.date == target_date).count()
        
        print(f"\n💾 現有賠率記錄: {existing_odds} 筆")
        
        if existing_odds > 0:
            print(f"⚠️  該日期已有賠率數據，是否繼續？")
            print(f"   (這將使用寶貴的API配額)")
            
            # 為了測試，我們繼續
            print(f"   繼續測試以驗證球隊映射修復...")
        
        # 4. 抓取賠率數據 (使用最後一次API請求)
        print(f"\n🔄 開始抓取賠率數據...")
        print(f"   ⚠️  這將使用最後一次API請求")
        
        try:
            odds_result = fetcher.fetch_odds_for_games(target_date)
            
            if odds_result['success']:
                print(f"✅ 成功抓取賠率數據")
                print(f"   總比賽數: {odds_result['total_games']}")
                print(f"   匹配比賽: {odds_result['matched_games']}")
                print(f"   匹配率: {odds_result['summary']['match_rate']:.1f}%")
                
                # 顯示匹配詳情
                if odds_result['matched_games'] > 0:
                    print(f"\n🎯 匹配成功的比賽:")
                    for game_data in odds_result.get('games', [])[:3]:  # 只顯示前3場
                        print(f"     ✅ {game_data['away_team']} @ {game_data['home_team']}")
                        
                        odds_info = game_data['odds']
                        spreads_count = len(odds_info.get('spreads', {}))
                        totals_count = len(odds_info.get('totals', {}))
                        print(f"        讓分盤: {spreads_count} 個博彩商")
                        print(f"        大小分: {totals_count} 個博彩商")
                
                # 顯示未匹配的比賽
                unmatched = odds_result.get('unmatched_games', [])
                if unmatched:
                    print(f"\n❌ 未匹配的比賽 ({len(unmatched)} 場):")
                    for game_data in unmatched[:3]:  # 只顯示前3場
                        print(f"     ❌ {game_data['away_team']} @ {game_data['home_team']}")
                
                # 5. 保存到數據庫
                if odds_result['matched_games'] > 0:
                    print(f"\n💾 保存賠率數據到數據庫...")
                    save_result = fetcher.save_odds_to_database(odds_result)
                    
                    if save_result['success']:
                        print(f"✅ 數據保存成功")
                        print(f"   新增記錄: {save_result['saved_count']}")
                        print(f"   更新記錄: {save_result['updated_count']}")
                        print(f"   總處理記錄: {save_result['total_processed']}")
                    else:
                        print(f"❌ 數據保存失敗: {save_result['error']}")
                else:
                    print(f"⚠️  沒有匹配的比賽，跳過保存")
                
            else:
                print(f"❌ 抓取賠率失敗: {odds_result['error']}")
                
        except Exception as e:
            print(f"❌ 測試過程中發生錯誤: {e}")
        
        # 6. 最終統計
        print(f"\n📊 最終統計:")
        total_odds_after = BettingOdds.query.count()
        spreads_count = BettingOdds.query.filter_by(market_type='spreads').count()
        totals_count = BettingOdds.query.filter_by(market_type='totals').count()
        
        print(f"   數據庫總賠率記錄: {total_odds_after:,}")
        print(f"   讓分盤記錄: {spreads_count:,}")
        print(f"   大小分記錄: {totals_count:,}")
        
        # 檢查API配額
        final_status = fetcher.check_api_status()
        final_remaining = final_status.get('remaining_requests', 'N/A')
        print(f"   剩餘API請求: {final_remaining}")

if __name__ == "__main__":
    test_single_date()
