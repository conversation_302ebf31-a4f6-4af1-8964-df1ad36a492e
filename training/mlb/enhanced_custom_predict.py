#!/usr/bin/env python3
"""
增強的自定義預測系統
提供多種處理已存在預測的選項，方便開發和驗證改進的預測邏輯
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction
from models.unified_betting_predictor import UnifiedBettingPredictor
import json

class EnhancedCustomPredictor:
    """增強的自定義預測器"""
    
    def __init__(self):
        self.app = create_app()
        self.predictor = UnifiedBettingPredictor()
    
    def check_existing_predictions(self, target_date: date):
        """檢查指定日期是否已有預測"""
        with self.app.app_context():
            games = Game.query.filter(
                Game.date == target_date,
                Game.game_status.in_(['completed', 'final'])
            ).all()
            
            existing_predictions = []
            missing_predictions = []
            
            for game in games:
                prediction = Prediction.query.filter_by(
                    game_id=game.game_id
                ).first()
                
                if prediction:
                    existing_predictions.append({
                        'game_id': game.game_id,
                        'matchup': f"{game.away_team} @ {game.home_team}",
                        'prediction': f"{prediction.predicted_away_score:.1f} - {prediction.predicted_home_score:.1f}",
                        'actual': f"{game.away_score} - {game.home_score}",
                        'model_version': prediction.model_version,
                        'created_at': prediction.created_at,
                        'confidence': prediction.confidence
                    })
                else:
                    missing_predictions.append({
                        'game_id': game.game_id,
                        'matchup': f"{game.away_team} @ {game.home_team}",
                        'actual': f"{game.away_score} - {game.home_score}"
                    })
            
            return {
                'target_date': target_date,
                'total_games': len(games),
                'existing_predictions': existing_predictions,
                'missing_predictions': missing_predictions,
                'has_existing': len(existing_predictions) > 0,
                'has_missing': len(missing_predictions) > 0
            }
    
    def predict_with_options(self, target_date: date, options: dict):
        """根據選項進行預測"""
        
        # 預設選項
        default_options = {
            'force_regenerate': False,      # 強制重新生成所有預測
            'update_existing': True,        # 更新現有預測
            'skip_existing': False,         # 跳過已有預測的比賽
            'backup_existing': True,        # 備份現有預測
            'model_version_suffix': '',     # 模型版本後綴
            'exclude_postponed': True,      # 排除延期比賽
            'confidence_threshold': 0.0     # 信心度閾值（只更新低於此值的預測）
        }
        
        # 合併選項
        options = {**default_options, **options}
        
        with self.app.app_context():
            print(f"🎯 開始為 {target_date} 生成預測...")
            print(f"📋 選項: {json.dumps(options, indent=2, ensure_ascii=False)}")
            
            # 獲取比賽
            games_query = Game.query.filter(Game.date == target_date)
            
            if options['exclude_postponed']:
                games_query = games_query.filter(
                    ~Game.game_status.in_(['postponed', 'rescheduled', 'cancelled'])
                )
            
            games = games_query.all()
            
            if not games:
                return {
                    'success': False,
                    'error': f'沒有找到 {target_date} 的比賽數據',
                    'games_processed': 0
                }
            
            print(f"📊 找到 {len(games)} 場比賽")
            
            results = {
                'success': True,
                'target_date': target_date,
                'total_games': len(games),
                'processed_games': 0,
                'skipped_games': 0,
                'updated_games': 0,
                'new_predictions': 0,
                'backed_up_predictions': 0,
                'errors': [],
                'predictions': []
            }
            
            for game in games:
                try:
                    game_result = self._process_single_game(game, options)
                    
                    # 更新統計
                    if game_result['action'] == 'skipped':
                        results['skipped_games'] += 1
                    elif game_result['action'] == 'updated':
                        results['updated_games'] += 1
                        results['processed_games'] += 1
                    elif game_result['action'] == 'created':
                        results['new_predictions'] += 1
                        results['processed_games'] += 1
                    
                    if game_result.get('backed_up'):
                        results['backed_up_predictions'] += 1
                    
                    results['predictions'].append(game_result)
                    
                except Exception as e:
                    error_msg = f"處理比賽 {game.game_id} 失敗: {str(e)}"
                    print(f"❌ {error_msg}")
                    results['errors'].append(error_msg)
            
            # 提交數據庫更改
            try:
                db.session.commit()
                print(f"✅ 數據庫更改已提交")
            except Exception as e:
                db.session.rollback()
                print(f"❌ 數據庫提交失敗: {e}")
                results['success'] = False
                results['error'] = f"數據庫提交失敗: {e}"
            
            return results
    
    def _process_single_game(self, game, options):
        """處理單場比賽的預測"""
        
        # 檢查現有預測
        existing_prediction = Prediction.query.filter_by(
            game_id=game.game_id
        ).first()
        
        game_info = {
            'game_id': game.game_id,
            'matchup': f"{game.away_team} @ {game.home_team}",
            'away_team': game.away_team,
            'home_team': game.home_team,
            'actual_score': f"{game.away_score} - {game.home_score}",
            'actual_away_score': game.away_score,
            'actual_home_score': game.home_score,
            'action': 'none',
            'backed_up': False,
            'prediction': None,
            'error': None
        }
        
        # 決定處理策略
        if existing_prediction:
            # 已有預測的處理邏輯
            if options['skip_existing']:
                game_info['action'] = 'skipped'
                game_info['reason'] = '跳過已有預測'
                return game_info
            
            # 檢查信心度閾值
            if (options['confidence_threshold'] > 0 and 
                existing_prediction.confidence >= options['confidence_threshold']):
                game_info['action'] = 'skipped'
                game_info['reason'] = f'信心度 {existing_prediction.confidence:.2f} 高於閾值 {options["confidence_threshold"]}'
                return game_info
            
            # 備份現有預測
            if options['backup_existing']:
                self._backup_prediction(existing_prediction)
                game_info['backed_up'] = True
        
        # 生成新預測
        try:
            training_end_date = game.date - timedelta(days=1)

            # 傳遞模型版本後綴和其他選項
            prediction_options = {
                'model_version_suffix': options.get('model_version_suffix', ''),
                'training_end_date': training_end_date,
                'target_date': game.date,
                'include_betting_data': True,
                'use_improved_logic': True
            }

            print(f"🔬 使用模型版本: {prediction_options['model_version_suffix'] or '預設'}")
            print(f"📅 訓練數據截止: {training_end_date}")

            prediction_result = self.predictor.predict_game_comprehensive(
                game.game_id,
                target_date=game.date,
                training_end_date=training_end_date,
                options=prediction_options
            )
            
            if prediction_result and not prediction_result.get('error'):
                # 保存預測
                if existing_prediction and options['update_existing']:
                    # 更新現有預測
                    self._update_existing_prediction(existing_prediction, prediction_result, options)
                    game_info['action'] = 'updated'
                else:
                    # 創建新預測
                    self._create_new_prediction(game.game_id, prediction_result, options)
                    game_info['action'] = 'created'
                
                # 提取博彩建議數據
                betting_recommendations = prediction_result.get('betting_recommendations', {})
                betting_lines = prediction_result.get('betting_lines', {})

                # 生成大小分數據
                over_under_data = {}
                if betting_recommendations.get('over_under'):
                    ou = betting_recommendations['over_under']
                    over_under_data = {
                        'line': ou.get('line', betting_lines.get('over_under', 8.5)),
                        'recommendation': ou.get('recommendation', 'N/A'),
                        'predicted_total': ou.get('predicted_total', prediction_result.get('predicted_total_runs', 0)),
                        'edge': ou.get('edge', 0)
                    }
                elif betting_lines.get('over_under'):
                    predicted_total = prediction_result.get('predicted_total_runs', 0)
                    line = betting_lines['over_under']
                    over_under_data = {
                        'line': line,
                        'recommendation': 'Over' if predicted_total > line else 'Under',
                        'predicted_total': predicted_total,
                        'edge': abs(predicted_total - line)
                    }

                # 生成讓分盤數據
                run_line_data = {}
                if betting_recommendations.get('run_line'):
                    rl = betting_recommendations['run_line']
                    run_line_data = {
                        'line': rl.get('spread', betting_lines.get('run_line_spread', 1.5)),
                        'recommendation': rl.get('recommendation', 'N/A'),
                        'predicted_margin': rl.get('predicted_margin', 0),
                        'covers': rl.get('covers', False)
                    }
                elif betting_lines.get('run_line_spread'):
                    predicted_home = prediction_result.get('predicted_home_score', 0)
                    predicted_away = prediction_result.get('predicted_away_score', 0)
                    predicted_margin = predicted_home - predicted_away
                    spread = betting_lines['run_line_spread']
                    covers = predicted_margin > spread
                    run_line_data = {
                        'line': spread,
                        'recommendation': f"{game.home_team} -{spread}" if covers else f"{game.away_team} +{spread}",
                        'predicted_margin': predicted_margin,
                        'covers': covers
                    }

                # 記錄預測結果
                game_info['prediction'] = {
                    'away_score': prediction_result.get('predicted_away_score', 0),
                    'home_score': prediction_result.get('predicted_home_score', 0),
                    'confidence': prediction_result.get('confidence', 0),
                    'total_runs': prediction_result.get('predicted_total_runs', 0),
                    'training_end_date': training_end_date.isoformat(),
                    'model_version': options.get('model_version_suffix', '預設'),
                    'betting_data': {
                        'over_under': over_under_data,
                        'run_line': run_line_data,
                        'betting_lines': betting_lines,
                        'betting_recommendations': betting_recommendations
                    }
                }
                
                print(f"✅ {game.away_team} @ {game.home_team}: "
                      f"預測 {game_info['prediction']['away_score']:.1f}-{game_info['prediction']['home_score']:.1f}, "
                      f"實際 {game.away_score}-{game.home_score}")
            
            else:
                game_info['error'] = prediction_result.get('error', '預測失敗')
                game_info['action'] = 'error'
        
        except Exception as e:
            game_info['error'] = str(e)
            game_info['action'] = 'error'
        
        return game_info
    
    def _backup_prediction(self, prediction):
        """備份現有預測"""
        backup_data = {
            'game_id': prediction.game_id,
            'predicted_home_score': prediction.predicted_home_score,
            'predicted_away_score': prediction.predicted_away_score,
            'confidence': prediction.confidence,
            'model_version': prediction.model_version,
            'created_at': prediction.created_at.isoformat() if prediction.created_at else None,
            'backup_time': datetime.now().isoformat()
        }
        
        # 可以保存到文件或專門的備份表
        backup_file = f"prediction_backups/{prediction.game_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(backup_file), exist_ok=True)
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)
    
    def _update_existing_prediction(self, prediction, prediction_result, options):
        """更新現有預測"""
        prediction.predicted_home_score = prediction_result.get('predicted_home_score', 0)
        prediction.predicted_away_score = prediction_result.get('predicted_away_score', 0)
        prediction.predicted_total_runs = prediction_result.get('predicted_total_runs', 0)
        prediction.confidence = prediction_result.get('confidence', 0)
        
        # 更新模型版本
        base_version = self.predictor.model_version
        if options['model_version_suffix']:
            prediction.model_version = f"{base_version}_{options['model_version_suffix']}"
        else:
            prediction.model_version = base_version
        
        prediction.updated_at = datetime.now()
    
    def _create_new_prediction(self, game_id, prediction_result, options):
        """創建新預測"""
        base_version = self.predictor.model_version
        model_version = f"{base_version}_{options['model_version_suffix']}" if options['model_version_suffix'] else base_version
        
        new_prediction = Prediction(
            game_id=game_id,
            predicted_home_score=prediction_result.get('predicted_home_score', 0),
            predicted_away_score=prediction_result.get('predicted_away_score', 0),
            predicted_total_runs=prediction_result.get('predicted_total_runs', 0),
            confidence=prediction_result.get('confidence', 0),
            model_version=model_version,
            prediction_date=datetime.now(),
            created_at=datetime.now()
        )
        
        db.session.add(new_prediction)

def main():
    """主函數 - 示範用法"""
    predictor = EnhancedCustomPredictor()
    
    # 示例：檢查 2025-07-12 的預測狀況
    target_date = date(2025, 7, 12)
    
    print("🔍 檢查現有預測...")
    status = predictor.check_existing_predictions(target_date)
    
    print(f"\n📊 {target_date} 預測狀況:")
    print(f"  總比賽數: {status['total_games']}")
    print(f"  已有預測: {len(status['existing_predictions'])}")
    print(f"  缺少預測: {len(status['missing_predictions'])}")
    
    if status['existing_predictions']:
        print(f"\n📋 現有預測:")
        for pred in status['existing_predictions']:
            print(f"  {pred['matchup']}: 預測 {pred['prediction']}, 實際 {pred['actual']}")
    
    # 示例選項
    options = {
        'force_regenerate': False,
        'update_existing': True,
        'skip_existing': False,
        'backup_existing': True,
        'model_version_suffix': 'improved_v1',
        'exclude_postponed': True,
        'confidence_threshold': 0.5  # 只更新信心度低於 50% 的預測
    }
    
    print(f"\n🚀 開始預測...")
    results = predictor.predict_with_options(target_date, options)
    
    print(f"\n✅ 預測完成:")
    print(f"  處理比賽: {results['processed_games']}/{results['total_games']}")
    print(f"  跳過比賽: {results['skipped_games']}")
    print(f"  更新預測: {results['updated_games']}")
    print(f"  新建預測: {results['new_predictions']}")
    print(f"  備份預測: {results['backed_up_predictions']}")

if __name__ == "__main__":
    main()
