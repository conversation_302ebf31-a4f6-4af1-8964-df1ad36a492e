#!/usr/bin/env python3
"""
快速批量抓取器 - 針對較小日期範圍進行高效抓取
"""

import logging
import time
import random
from datetime import date, datetime, timedelta
from typing import List, Dict, Optional
from integrated_odds_system import IntegratedOddsSystem
import json

logger = logging.getLogger(__name__)

class QuickBatchCrawler:
    """快速批量抓取器"""
    
    def __init__(self):
        self.integrated_system = IntegratedOddsSystem()
        
        # 更快的配置
        self.min_delay = 1.0  # 最小延遲秒數
        self.max_delay = 3.0  # 最大延遲秒數
        self.max_retries = 2   # 最大重試次數
        self.batch_size = 7    # 批次大小
        
    def crawl_date_range(self, start_date: date, end_date: date, 
                        market_type: str = 'both') -> Dict:
        """抓取日期範圍內的賠率數據"""
        
        logger.info(f"🚀 開始快速批量抓取: {start_date} 到 {end_date}")
        
        # 生成日期列表
        date_list = self._generate_date_list(start_date, end_date)
        logger.info(f"📅 總計 {len(date_list)} 個日期需要抓取")
        
        # 結果統計
        results = {
            'total_dates': len(date_list),
            'successful_dates': 0,
            'failed_dates': 0,
            'total_records_saved': 0,
            'date_results': [],
            'success_rate': 0.0
        }
        
        # 處理每個日期
        for i, target_date in enumerate(date_list, 1):
            logger.info(f"📅 處理日期 {i}/{len(date_list)}: {target_date}")
            
            try:
                # 嘗試抓取
                result = self.integrated_system.fetch_and_save_odds(target_date, market_type)
                
                if result.get('success') and result.get('total_saved', 0) > 0:
                    results['successful_dates'] += 1
                    results['total_records_saved'] += result.get('total_saved', 0)
                    
                    results['date_results'].append({
                        'date': target_date.isoformat(),
                        'success': True,
                        'records_saved': result.get('total_saved', 0),
                        'games_processed': result.get('games_processed', 0),
                        'message': result.get('message', '')
                    })
                    
                    logger.info(f"✅ {target_date}: 保存了 {result.get('total_saved')} 條記錄")
                else:
                    results['failed_dates'] += 1
                    results['date_results'].append({
                        'date': target_date.isoformat(),
                        'success': False,
                        'records_saved': 0,
                        'games_processed': 0,
                        'message': result.get('message', '無數據')
                    })
                    logger.warning(f"⚠️ {target_date}: {result.get('message', '無數據')}")
                
            except Exception as e:
                results['failed_dates'] += 1
                results['date_results'].append({
                    'date': target_date.isoformat(),
                    'success': False,
                    'records_saved': 0,
                    'games_processed': 0,
                    'message': f'抓取失敗: {e}'
                })
                logger.error(f"❌ {target_date} 抓取失敗: {e}")
            
            # 延遲控制
            if i < len(date_list):
                delay = random.uniform(self.min_delay, self.max_delay)
                logger.info(f"⏳ 延遲 {delay:.1f} 秒")
                time.sleep(delay)
        
        # 計算成功率
        results['success_rate'] = (results['successful_dates'] / results['total_dates']) * 100
        
        logger.info(f"✅ 快速批量抓取完成!")
        logger.info(f"📊 成功: {results['successful_dates']}/{results['total_dates']} ({results['success_rate']:.1f}%)")
        logger.info(f"💾 總記錄: {results['total_records_saved']}")
        
        return results
    
    def _generate_date_list(self, start_date: date, end_date: date) -> List[date]:
        """生成日期列表"""
        date_list = []
        current_date = start_date
        
        while current_date <= end_date:
            date_list.append(current_date)
            current_date += timedelta(days=1)
            
        return date_list

def crawl_june_2025():
    """抓取2025年6月的數據"""
    logging.basicConfig(level=logging.INFO)
    
    from app import create_app
    app = create_app()
    
    with app.app_context():
        crawler = QuickBatchCrawler()
        
        results = crawler.crawl_date_range(
            date(2025, 6, 1),
            date(2025, 6, 30),
            'both'
        )
        
        # 保存結果
        with open('june_2025_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📋 6月結果: {results['success_rate']:.1f}% 成功率")
        print(f"💾 總記錄: {results['total_records_saved']}")
        return results

def crawl_august_2025():
    """抓取2025年8月的數據"""
    logging.basicConfig(level=logging.INFO)
    
    from app import create_app
    app = create_app()
    
    with app.app_context():
        crawler = QuickBatchCrawler()
        
        results = crawler.crawl_date_range(
            date(2025, 8, 1),
            date(2025, 8, 25),
            'both'
        )
        
        # 保存結果
        with open('august_2025_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📋 8月結果: {results['success_rate']:.1f}% 成功率")
        print(f"💾 總記錄: {results['total_records_saved']}")
        return results

if __name__ == "__main__":
    print("🔧 快速批量抓取器")
    print("1. crawl_june_2025() - 抓取6月數據")
    print("2. crawl_august_2025() - 抓取8月數據")
    
    # 默認運行6月數據抓取
    crawl_june_2025()