#!/usr/bin/env python3
"""
测试全面数据更新功能
"""

import requests
import time
from datetime import date, timedelta

def test_comprehensive_update():
    """测试全面数据更新功能"""
    base_url = 'http://127.0.0.1:5500'
    
    print('🔍 测试全面数据更新功能')
    print('=' * 60)
    
    # 1. 测试刷新行程API
    print('1️⃣ 测试刷新未来七天行程...')
    try:
        response = requests.post(f'{base_url}/admin/api/refresh-schedule', timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f'✅ 行程刷新成功: {data.get("message")}')
                print(f'📊 更新天数: {data.get("updated_days")}/7')
            else:
                print(f'❌ 行程刷新失败: {data.get("error")}')
        else:
            print(f'❌ HTTP错误: {response.status_code}')
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')
    
    print()
    
    # 2. 检查数据库中的比赛数据
    print('2️⃣ 检查数据库中的比赛数据...')
    try:
        import sqlite3
        
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        # 检查未来7天的比赛数据
        today = date.today()
        future_dates = [today + timedelta(days=i) for i in range(7)]
        
        total_games = 0
        for future_date in future_dates:
            cursor.execute('''
                SELECT COUNT(*) FROM games WHERE date = ?
            ''', (future_date.isoformat(),))
            
            count = cursor.fetchone()[0]
            total_games += count
            
            status = '✅' if count > 0 else '⚠️'
            print(f'   {status} {future_date}: {count} 场比赛')
        
        print(f'📊 未来7天总计: {total_games} 场比赛')
        conn.close()
        
    except Exception as e:
        print(f'❌ 数据库检查失败: {e}')
    
    print()
    
    # 3. 测试预测结果查询
    print('3️⃣ 测试预测结果查询...')
    try:
        # 查询最近的预测记录
        test_date = date.today() - timedelta(days=1)  # 昨天
        response = requests.get(f'{base_url}/unified/api/predictions/date/{test_date}', timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                predictions = data.get('predictions', [])
                print(f'✅ 查询到 {len(predictions)} 条预测记录')
                
                # 统计准确性
                completed = sum(1 for p in predictions if p.get('game_completed', False))
                correct = sum(1 for p in predictions if p.get('is_correct') is True)
                
                if completed > 0:
                    accuracy = (correct / completed) * 100
                    print(f'📊 预测准确率: {accuracy:.1f}% ({correct}/{completed})')
                else:
                    print('📊 暂无已完成的比赛可验证')
            else:
                print(f'❌ 查询失败: {data.get("error")}')
        else:
            print(f'❌ HTTP错误: {response.status_code}')
            
    except Exception as e:
        print(f'❌ 预测查询失败: {e}')
    
    print()
    print('🎉 全面数据更新功能测试完成!')
    print()
    print('💡 使用说明:')
    print('   1. 访问管理员仪表板: http://127.0.0.1:5500/admin')
    print('   2. 点击"全面更新"按钮更新所有数据')
    print('   3. 点击"刷新行程"按钮更新未来行程')
    print('   4. 使用"每日更新"按钮更新特定日期')

if __name__ == "__main__":
    test_comprehensive_update()
