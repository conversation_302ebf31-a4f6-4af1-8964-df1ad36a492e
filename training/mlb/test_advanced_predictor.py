#!/usr/bin/env python3
"""
測試進階預測系統
"""

import sys
import os
from datetime import date

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.advanced_predictor import AdvancedMLBPredictor

def test_advanced_predictor():
    """測試進階預測系統"""
    print("🎯 測試進階MLB預測系統")
    print("=" * 60)
    
    app = create_app()
    with app.app_context():
        predictor = AdvancedMLBPredictor()
        
        # 測試不同類型的比賽
        test_scenarios = [
            {
                'name': '強隊 vs 弱隊',
                'games': [('LAD', 'OAK'), ('NYY', 'MIA'), ('HOU', 'COL')]
            },
            {
                'name': '勢均力敵',
                'games': [('ATL', 'PHI'), ('SD', 'SF'), ('TEX', 'SEA')]
            },
            {
                'name': '投手對決',
                'games': [('LAD', 'ATL'), ('NYY', 'HOU')]  # 假設都有好投手
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n📊 {scenario['name']}:")
            print("-" * 40)
            
            for home, away in scenario['games']:
                try:
                    prediction = predictor.predict_game_advanced(home, away)
                    
                    print(f"\n🏟️ {away} @ {home}")
                    print(f"   預測比分: {prediction['predicted_away_score']} - {prediction['predicted_home_score']}")
                    print(f"   總分: {prediction['total_score']}")
                    print(f"   主隊勝率: {prediction['home_win_probability']:.1%}")
                    
                    factors = prediction['prediction_factors']
                    print(f"   進攻評級: {away}({factors['away_offense_rating']:.0f}) vs {home}({factors['home_offense_rating']:.0f})")
                    print(f"   投手評級: {away}({factors['away_pitching_rating']:.0f}) vs {home}({factors['home_pitching_rating']:.0f})")
                    print(f"   主場優勢: +{factors['home_field_advantage']:.1f}")
                    
                    # 分析預測合理性
                    analyze_prediction_quality(prediction, away, home)
                    
                except Exception as e:
                    print(f"   ❌ 預測失敗: {e}")
        
        # 總結分析
        print(f"\n📈 系統特色分析:")
        print("=" * 40)
        print("✅ 考慮因素:")
        print("   • 球隊平均得分能力")
        print("   • 殘壘效率 (LOB%)")
        print("   • 得點圈打擊能力 (RISP)")
        print("   • 投手防禦率和WHIP")
        print("   • 主客場差異")
        print("   • 最近10場表現")
        print("   • 王牌投手對決效應")
        print("   • 打擊大戰效應")

def analyze_prediction_quality(prediction: dict, away_team: str, home_team: str):
    """分析預測質量"""
    home_score = prediction['predicted_home_score']
    away_score = prediction['predicted_away_score']
    total_score = prediction['total_score']
    
    # 檢查預測合理性
    issues = []
    
    if total_score < 6:
        issues.append("🥶 低分比賽 (投手對決)")
    elif total_score > 12:
        issues.append("🔥 高分比賽 (打擊大戰)")
    
    if abs(home_score - away_score) > 3:
        issues.append("⚡ 實力懸殊")
    elif abs(home_score - away_score) < 0.5:
        issues.append("⚖️ 勢均力敵")
    
    if home_score > 7 or away_score > 7:
        issues.append("💥 高得分預期")
    
    if issues:
        print(f"   特徵: {' | '.join(issues)}")

def compare_with_historical_data():
    """與歷史數據比較"""
    print(f"\n📚 歷史數據參考:")
    print("-" * 30)
    print("MLB 2024賽季平均:")
    print("   • 每場總分: 8.8分")
    print("   • 主隊勝率: 54%")
    print("   • 得分範圍: 2-15分")
    print("   • 王牌投手ERA: < 3.00")
    print("   • 強隊平均得分: > 5.2分/場")
    print("   • 弱隊平均得分: < 4.2分/場")
    
    print(f"\n🎯 預測目標:")
    print("   • 強隊 vs 弱隊: 6.5 - 3.8 (差距2.7分)")
    print("   • 王牌對決: 3.2 - 2.9 (總分6.1分)")
    print("   • 打擊大戰: 8.1 - 7.4 (總分15.5分)")
    print("   • 主場優勢: +0.3 ~ +0.8分")

if __name__ == "__main__":
    test_advanced_predictor()
    compare_with_historical_data()
