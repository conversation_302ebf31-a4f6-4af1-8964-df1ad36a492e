#!/usr/bin/env python3
"""
測試修復後的盤口數據保存邏輯
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, BettingOdds, Game
from models.covers_scraper import CoversMLBScraper
from views.admin import _save_scraper_odds_to_database

def test_fixed_odds_save():
    """測試修復後的盤口數據保存"""
    app = create_app()
    
    with app.app_context():
        test_date = date(2025, 7, 11)
        
        print(f"🧪 測試修復後的盤口數據保存 - {test_date}")
        
        # 1. 獲取數據庫中的比賽
        games = Game.query.filter_by(date=test_date).all()
        print(f"📊 數據庫中有 {len(games)} 場比賽")
        
        # 2. 使用covers抓取器獲取數據
        scraper = CoversMLBScraper()
        result = scraper.fetch_mlb_games_for_date(test_date)
        
        if not result.get('success'):
            print(f"❌ 抓取失敗: {result.get('error')}")
            return
        
        print(f"🌐 成功抓取到 {result.get('total_games', 0)} 場比賽")
        
        # 3. 檢查現有盤口數據
        existing_count = BettingOdds.query.join(Game).filter(Game.date == test_date).count()
        print(f"📈 保存前現有盤口記錄: {existing_count}")
        
        # 4. 保存盤口數據
        save_result = _save_scraper_odds_to_database(result, test_date, games)
        
        print(f"💾 保存結果:")
        print(f"  成功: {save_result.get('success')}")
        print(f"  處理數量: {save_result.get('total_processed', 0)}")
        if not save_result.get('success'):
            print(f"  錯誤: {save_result.get('error')}")
        
        # 5. 檢查保存後的盤口數據
        final_count = BettingOdds.query.join(Game).filter(Game.date == test_date).count()
        print(f"📈 保存後盤口記錄: {final_count}")
        print(f"📈 新增記錄: {final_count - existing_count}")
        
        # 6. 顯示保存的盤口詳情
        if final_count > 0:
            print(f"\n📋 保存的盤口詳情:")
            saved_odds = BettingOdds.query.join(Game).filter(Game.date == test_date).limit(5).all()
            for odds in saved_odds:
                game = Game.query.filter_by(game_id=odds.game_id).first()
                if game:
                    print(f"  - {game.away_team} @ {game.home_team}")
                    print(f"    博彩商: {odds.bookmaker}")
                    print(f"    總分線: {odds.total_point}")
                    print(f"    讓分線: {odds.home_spread_point}")

if __name__ == "__main__":
    test_fixed_odds_save()
