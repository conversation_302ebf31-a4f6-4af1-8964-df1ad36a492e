#!/usr/bin/env python3
"""
創建詳細數據表的腳本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db

def create_detailed_tables():
    """創建詳細數據表"""
    app = create_app()
    
    with app.app_context():
        try:
            print("創建詳細數據表...")
            
            # 創建所有表
            db.create_all()
            
            print("✓ 詳細數據表創建完成")
            
            # 檢查表是否創建成功
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            expected_tables = ['box_scores', 'player_game_stats']
            
            for table in expected_tables:
                if table in tables:
                    print(f"✓ 表 {table} 創建成功")
                else:
                    print(f"✗ 表 {table} 創建失敗")
            
            return True
            
        except Exception as e:
            print(f"✗ 創建詳細數據表失敗: {e}")
            return False

if __name__ == "__main__":
    success = create_detailed_tables()
    if success:
        print("\n🎉 詳細數據表創建完成！")
    else:
        print("\n❌ 詳細數據表創建失敗！")
        sys.exit(1)
