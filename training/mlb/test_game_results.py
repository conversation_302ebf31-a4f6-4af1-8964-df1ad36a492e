#!/usr/bin/env python3
"""
測試比賽結果獲取功能
"""

from app import create_app
from models.database import db, Game, BoxScore
from models.data_fetcher import MLBDataFetcher
from models.detailed_data_fetcher import DetailedDataFetcher
from datetime import date, timedelta
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_game_results():
    """測試比賽結果獲取功能"""
    app = create_app()
    
    with app.app_context():
        print("🔍 檢查比賽資料狀況...")
        print("=" * 60)
        
        # 檢查最近5天的比賽資料
        recent_dates = [date.today() - timedelta(days=x) for x in range(5)]
        
        for check_date in recent_dates:
            games = Game.query.filter_by(date=check_date).all()
            total_games = len(games)
            
            completed_games = [g for g in games if g.home_score is not None and g.away_score is not None]
            pending_games = [g for g in games if g.home_score is None or g.away_score is None]
            
            print(f"📅 {check_date}: 總共 {total_games} 場比賽")
            print(f"   ✅ 已完成: {len(completed_games)} 場 (有比分)")
            print(f"   ⏳ 待結果: {len(pending_games)} 場 (無比分)")
            
            if pending_games:
                print(f"   📋 待結果比賽:")
                for game in pending_games[:3]:  # 顯示前3場
                    print(f"      - {game.away_team} @ {game.home_team} ({game.game_status}) [{game.game_id}]")
            
            if completed_games:
                print(f"   🏆 已完成比賽範例:")
                for game in completed_games[:2]:  # 顯示前2場
                    print(f"      - {game.away_team} {game.away_score} @ {game.home_team} {game.home_score} [{game.game_status}]")
            
            print()
        
        # 測試資料擷取功能
        print("🛠️ 測試資料擷取功能...")
        print("=" * 60)
        
        # 找一個待結果的比賽來測試
        pending_game = Game.query.filter(
            Game.home_score.is_(None),
            Game.away_score.is_(None),
            Game.date >= date.today() - timedelta(days=3)
        ).first()
        
        if pending_game:
            print(f"📺 測試比賽: {pending_game.away_team} @ {pending_game.home_team}")
            print(f"   Game ID: {pending_game.game_id}")
            print(f"   狀態: {pending_game.game_status}")
            print(f"   日期: {pending_game.date}")
            
            # 使用 MLB API 檢查比賽狀態
            try:
                import statsapi as mlb
                
                game_data = mlb.get('game', {'gamePk': pending_game.game_id.split('_')[-1]})
                if game_data and 'gameData' in game_data:
                    status = game_data.get('gameData', {}).get('status', {})
                    print(f"   📡 API狀態: {status.get('detailedState', 'Unknown')}")
                    
                    # 檢查比分
                    line_score = game_data.get('liveData', {}).get('linescore', {})
                    if line_score:
                        teams = line_score.get('teams', {})
                        away_runs = teams.get('away', {}).get('runs')
                        home_runs = teams.get('home', {}).get('runs')
                        
                        if away_runs is not None and home_runs is not None:
                            print(f"   🎯 API比分: {pending_game.away_team} {away_runs} @ {pending_game.home_team} {home_runs}")
                            print(f"   ❗ 比分已可用但未更新到資料庫！")
                        else:
                            print(f"   ⏸️ API暫無比分資料")
                            
            except Exception as e:
                print(f"   ❌ API測試失敗: {e}")
        else:
            print("📝 沒有找到待結果的比賽進行測試")
        
        # 檢查 BoxScore 資料
        print("\n📊 BoxScore 資料狀況...")
        print("=" * 60)
        
        boxscore_count = BoxScore.query.count()
        recent_boxscores = BoxScore.query.join(Game).filter(
            Game.date >= date.today() - timedelta(days=7)
        ).count()
        
        print(f"📈 總 BoxScore 記錄: {boxscore_count}")
        print(f"📈 最近7天 BoxScore: {recent_boxscores}")

if __name__ == "__main__":
    test_game_results()