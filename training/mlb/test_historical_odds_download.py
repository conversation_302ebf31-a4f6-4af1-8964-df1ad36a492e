#!/usr/bin/env python3
"""
測試歷史賠率下載功能
按照用戶要求：從今天的比賽場次往回下載，如果已經有數據的比對一下是不是一致
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from models.covers_scraper import CoversMLBScraper
from models.database import db, BettingOdds, Game
from app import create_app

def test_historical_odds_download():
    """測試歷史賠率下載"""
    print("=== 測試歷史賠率下載功能 ===")

    # 創建Flask應用上下文
    app = create_app()

    with app.app_context():
        # 初始化scraper
        scraper = CoversMLBScraper()

        # 從今天開始往回下載
        start_date = date.today()
        days_to_check = 5  # 檢查5天
        
        total_downloaded = 0
        total_inconsistent = 0
        total_new_records = 0
        
        for i in range(days_to_check):
            current_date = start_date - timedelta(days=i)
            print(f"\n--- 處理日期: {current_date} ---")
            
            # 抓取該日期的比賽數據
            result = scraper.fetch_mlb_games_for_date(current_date)
            
            if not result['success']:
                print(f"❌ 抓取失敗: {result.get('error', '未知錯誤')}")
                continue
                
            games = result['games']
            print(f"📥 抓取到 {len(games)} 場比賽")
            
            # 檢查每場比賽的賠率數據
            for game_data in games:
                away_team = game_data['away_team']
                home_team = game_data['home_team']
                
                # 查找對應的Game記錄
                game = Game.query.filter(
                    Game.date == current_date,
                    Game.away_team.like(f'%{away_team}%'),
                    Game.home_team.like(f'%{home_team}%')
                ).first()
                
                if not game:
                    print(f"  ⚠️  未找到對應的Game記錄: {away_team} @ {home_team}")
                    continue
                
                # 檢查是否已有BettingOdds記錄
                game_odds = game_data['odds']

                # 檢查大小分記錄
                if game_odds.get('total_line'):
                    existing_totals = BettingOdds.query.filter_by(
                        game_id=game.game_id,
                        market_type='totals',
                        bookmaker='covers.com'
                    ).first()

                    if existing_totals:
                        is_consistent = check_odds_consistency(existing_totals, game_odds)
                        if not is_consistent:
                            total_inconsistent += 1
                            print(f"  ⚠️  大小分數據不一致: {away_team} @ {home_team}")
                            print(f"      現有: {format_odds_for_display(existing_totals)}")
                            print(f"      新抓: total_line={game_odds['total_line']}")
                            update_betting_odds(existing_totals, game_odds)
                            print(f"      ✅ 已更新")
                        else:
                            print(f"  ✅ 大小分數據一致: {away_team} @ {home_team}")
                    else:
                        # 創建新的大小分記錄
                        create_betting_odds(game.game_id, game_odds)
                        total_new_records += 1
                        print(f"  ➕ 新增大小分記錄: {away_team} @ {home_team} (total: {game_odds['total_line']})")

                # 檢查讓分記錄
                if game_odds.get('spread_line'):
                    existing_spreads = BettingOdds.query.filter_by(
                        game_id=game.game_id,
                        market_type='spreads',
                        bookmaker='covers.com'
                    ).first()

                    if existing_spreads:
                        is_consistent = check_odds_consistency(existing_spreads, game_odds)
                        if not is_consistent:
                            total_inconsistent += 1
                            print(f"  ⚠️  讓分數據不一致: {away_team} @ {home_team}")
                            print(f"      現有: {format_odds_for_display(existing_spreads)}")
                            print(f"      新抓: spread_line={game_odds['spread_line']}")
                            update_betting_odds(existing_spreads, game_odds)
                            print(f"      ✅ 已更新")
                        else:
                            print(f"  ✅ 讓分數據一致: {away_team} @ {home_team}")
                    else:
                        # 創建新的讓分記錄
                        create_betting_odds(game.game_id, game_odds)
                        total_new_records += 1
                        print(f"  ➕ 新增讓分記錄: {away_team} @ {home_team} (spread: {game_odds['spread_line']})")

                # 如果沒有任何有效賠率數據
                if not has_meaningful_odds(game_odds):
                    print(f"  ⚪ 無有效賠率數據: {away_team} @ {home_team}")
                
                total_downloaded += 1
        
        # 提交所有更改
        try:
            db.session.commit()
            print(f"\n=== 下載統計 ===")
            print(f"總處理比賽數: {total_downloaded}")
            print(f"新增記錄數: {total_new_records}")
            print(f"不一致記錄數: {total_inconsistent}")
            print(f"✅ 所有更改已保存到數據庫")
        except Exception as e:
            db.session.rollback()
            print(f"❌ 保存失敗: {e}")

def check_odds_consistency(existing_odds, new_odds):
    """檢查賠率數據一致性"""
    # 根據市場類型比較相應字段
    if existing_odds.market_type == 'totals':
        existing_value = existing_odds.total_point
        new_value = new_odds.get('total_line')
    elif existing_odds.market_type == 'spreads':
        existing_value = existing_odds.home_spread_point
        new_value = new_odds.get('spread_line')
    else:
        return True  # 其他類型暫時認為一致

    # 如果都有值且不相等，則不一致
    if existing_value is not None and new_value is not None:
        if abs(float(existing_value) - float(new_value)) > 0.01:  # 允許小數點誤差
            return False

    return True

def has_meaningful_odds(odds):
    """檢查是否有有意義的賠率數據"""
    meaningful_fields = ['spread_line', 'total_line']

    for field in meaningful_fields:
        if odds.get(field) is not None:
            return True

    return False

def create_betting_odds(game_id, odds):
    """創建新的BettingOdds記錄"""
    # 根據實際的BettingOdds模型結構創建記錄

    # 如果有大小分數據，創建totals記錄
    if odds.get('total_line'):
        betting_odds = BettingOdds(
            game_id=game_id,
            bookmaker='covers.com',
            market_type='totals',
            total_point=float(odds['total_line']),
            over_price=-110,  # 默認賠率
            under_price=-110,  # 默認賠率
            odds_time=datetime.now(),
            last_update=datetime.now()
        )
        db.session.add(betting_odds)

    # 如果有讓分數據，創建spreads記錄
    if odds.get('spread_line'):
        betting_odds = BettingOdds(
            game_id=game_id,
            bookmaker='covers.com',
            market_type='spreads',
            home_spread_point=float(odds['spread_line']),
            home_spread_price=-110,  # 默認賠率
            away_spread_point=-float(odds['spread_line']),
            away_spread_price=-110,  # 默認賠率
            odds_time=datetime.now(),
            last_update=datetime.now()
        )
        db.session.add(betting_odds)

def update_betting_odds(existing_odds, new_odds):
    """更新現有的BettingOdds記錄"""
    if existing_odds.market_type == 'totals' and new_odds.get('total_line'):
        existing_odds.total_point = float(new_odds['total_line'])
        existing_odds.last_update = datetime.now()
    elif existing_odds.market_type == 'spreads' and new_odds.get('spread_line'):
        existing_odds.home_spread_point = float(new_odds['spread_line'])
        existing_odds.away_spread_point = -float(new_odds['spread_line'])
        existing_odds.last_update = datetime.now()

def format_odds_for_display(odds):
    """格式化賠率用於顯示"""
    if odds.market_type == 'totals':
        return {
            'market_type': 'totals',
            'total_point': odds.total_point,
            'over_price': odds.over_price,
            'under_price': odds.under_price
        }
    elif odds.market_type == 'spreads':
        return {
            'market_type': 'spreads',
            'home_spread_point': odds.home_spread_point,
            'away_spread_point': odds.away_spread_point,
            'home_spread_price': odds.home_spread_price,
            'away_spread_price': odds.away_spread_price
        }
    else:
        return {'market_type': odds.market_type}

if __name__ == "__main__":
    test_historical_odds_download()
