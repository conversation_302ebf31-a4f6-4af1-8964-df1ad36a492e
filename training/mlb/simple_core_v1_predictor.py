#!/usr/bin/env python3
"""
簡單的 Core_v1.0 預測器
直接使用 Core_v1.0 模型進行預測，避免複雜的依賴
"""

import sys
import os
from datetime import date, timedelta
import joblib
import json
import pandas as pd
import numpy as np

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction

def load_core_v1_model():
    """載入 Core_v1.0 模型"""
    model_path = "models/saved/Core_v1.0"
    
    try:
        # 載入模型文件
        home_score_model = joblib.load(f"{model_path}/home_score_model.joblib")
        away_score_model = joblib.load(f"{model_path}/away_score_model.joblib")
        win_probability_model = joblib.load(f"{model_path}/win_probability_model.joblib")
        feature_scaler = joblib.load(f"{model_path}/feature_scaler.joblib")
        
        # 載入特徵列
        with open(f"{model_path}/feature_columns.json", 'r') as f:
            feature_columns = json.load(f)
        
        return {
            'home_score_model': home_score_model,
            'away_score_model': away_score_model,
            'win_probability_model': win_probability_model,
            'feature_scaler': feature_scaler,
            'feature_columns': feature_columns
        }
    
    except Exception as e:
        print(f"❌ 載入 Core_v1.0 模型失敗: {e}")
        return None

def extract_simple_features(home_team, away_team, game_date):
    """提取簡單特徵（從數據庫獲取真實數據）"""
    try:
        from models.database import Game, TeamStats
        from datetime import timedelta

        # 基本時間特徵
        features = {
            'is_weekend': 1 if game_date.weekday() >= 5 else 0,
            'month': game_date.month,
            'day_of_week': game_date.weekday(),
        }

        # 獲取最近30天的球隊統計數據
        cutoff_date = game_date - timedelta(days=30)

        # 主隊統計
        home_games = Game.query.filter(
            ((Game.home_team == home_team) | (Game.away_team == home_team)),
            Game.date >= cutoff_date,
            Game.date < game_date,
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).all()

        # 客隊統計
        away_games = Game.query.filter(
            ((Game.home_team == away_team) | (Game.away_team == away_team)),
            Game.date >= cutoff_date,
            Game.date < game_date,
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).all()

        # 計算主隊統計
        home_wins = 0
        home_runs_scored = []
        home_runs_allowed = []

        for game in home_games:
            if game.home_team == home_team:
                # 主場比賽
                home_runs_scored.append(game.home_score)
                home_runs_allowed.append(game.away_score)
                if game.home_score > game.away_score:
                    home_wins += 1
            else:
                # 客場比賽
                home_runs_scored.append(game.away_score)
                home_runs_allowed.append(game.home_score)
                if game.away_score > game.home_score:
                    home_wins += 1

        # 計算客隊統計
        away_wins = 0
        away_runs_scored = []
        away_runs_allowed = []

        for game in away_games:
            if game.home_team == away_team:
                # 主場比賽
                away_runs_scored.append(game.home_score)
                away_runs_allowed.append(game.away_score)
                if game.home_score > game.away_score:
                    away_wins += 1
            else:
                # 客場比賽
                away_runs_scored.append(game.away_score)
                away_runs_allowed.append(game.home_score)
                if game.away_score > game.home_score:
                    away_wins += 1

        # 計算統計值
        home_win_pct = home_wins / len(home_games) if home_games else 0.5
        away_win_pct = away_wins / len(away_games) if away_games else 0.5

        home_runs_avg = sum(home_runs_scored) / len(home_runs_scored) if home_runs_scored else 4.5
        home_allowed_avg = sum(home_runs_allowed) / len(home_runs_allowed) if home_runs_allowed else 4.5

        away_runs_avg = sum(away_runs_scored) / len(away_runs_scored) if away_runs_scored else 4.5
        away_allowed_avg = sum(away_runs_allowed) / len(away_runs_allowed) if away_runs_allowed else 4.5

        # 更新特徵
        features.update({
            'home_recent_win_pct': home_win_pct,
            'away_recent_win_pct': away_win_pct,
            'h2h_home_win_pct': 0.5,  # 簡化版本暫時使用默認值
            'home_offense_rating': min(1.0, home_runs_avg / 5.0),
            'away_offense_rating': min(1.0, away_runs_avg / 5.0),
            'home_pitcher_strength': max(0.0, 1.0 - (home_allowed_avg / 6.0)),
            'away_pitcher_strength': max(0.0, 1.0 - (away_allowed_avg / 6.0)),
            'is_pitcher_duel': 1 if (home_allowed_avg < 3.5 and away_allowed_avg < 3.5) else 0,
            'is_slugfest': 1 if (home_runs_avg > 6.0 and away_runs_avg > 6.0) else 0,
            'recent_win_pct_diff': home_win_pct - away_win_pct,
            'offense_rating_diff': (home_runs_avg / 5.0) - (away_runs_avg / 5.0),
            'home_win_pct': home_win_pct,
            'home_runs_scored_avg': home_runs_avg,
            'home_runs_allowed_avg': home_allowed_avg,
            'home_batting_avg': 0.250,  # 簡化版本使用默認值
            'home_ops': 0.750,
            'home_era': home_allowed_avg * 1.1,  # 簡化的ERA估算
            'home_whip': 1.30,
            'away_win_pct': away_win_pct,
            'away_runs_scored_avg': away_runs_avg,
            'away_runs_allowed_avg': away_allowed_avg,
            'away_batting_avg': 0.250,
            'away_ops': 0.750,
            'away_era': away_allowed_avg * 1.1,
            'away_whip': 1.30,
            'win_pct_diff': home_win_pct - away_win_pct,
            'runs_scored_avg_diff': home_runs_avg - away_runs_avg,
            'runs_allowed_avg_diff': home_allowed_avg - away_allowed_avg
        })

        print(f"   📊 特徵提取完成: 主隊得分{home_runs_avg:.1f}, 客隊得分{away_runs_avg:.1f}")
        return features

    except Exception as e:
        print(f"❌ 特徵提取失敗: {e}")
        # 返回默認值作為備用
        return {
            'is_weekend': 1 if game_date.weekday() >= 5 else 0,
            'month': game_date.month,
            'day_of_week': game_date.weekday(),
            'home_recent_win_pct': 0.5,
            'away_recent_win_pct': 0.5,
            'h2h_home_win_pct': 0.5,
            'home_offense_rating': 0.5,
            'away_offense_rating': 0.5,
            'home_pitcher_strength': 0.5,
            'away_pitcher_strength': 0.5,
            'is_pitcher_duel': 0,
            'is_slugfest': 0,
            'recent_win_pct_diff': 0.0,
            'offense_rating_diff': 0.0,
            'home_win_pct': 0.5,
            'home_runs_scored_avg': 4.5,
            'home_runs_allowed_avg': 4.5,
            'home_batting_avg': 0.250,
            'home_ops': 0.750,
            'home_era': 4.00,
            'home_whip': 1.30,
            'away_win_pct': 0.5,
            'away_runs_scored_avg': 4.5,
            'away_runs_allowed_avg': 4.5,
            'away_batting_avg': 0.250,
            'away_ops': 0.750,
            'away_era': 4.00,
            'away_whip': 1.30,
            'win_pct_diff': 0.0,
            'runs_scored_avg_diff': 0.0,
            'runs_allowed_avg_diff': 0.0
        }

def get_pitcher_info_for_core_v1(home_team, away_team, game_date):
    """為 Core_v1.0 模型獲取投手信息"""
    try:
        from models.database import Game, GameDetail, PlayerStats
        from datetime import timedelta

        # 嘗試找到該日期的比賽
        game = Game.query.filter(
            Game.home_team == home_team,
            Game.away_team == away_team,
            Game.date == game_date
        ).first()

        pitcher_info = {
            'home_pitcher': None,
            'away_pitcher': None,
            'home_pitcher_era': None,
            'away_pitcher_era': None
        }

        if game:
            # 嘗試從 GameDetail 獲取先發投手
            game_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
            if game_detail:
                if game_detail.home_starting_pitcher:
                    pitcher_info['home_pitcher'] = game_detail.home_starting_pitcher
                    # 查找投手統計
                    pitcher_stats = PlayerStats.query.filter(
                        PlayerStats.player_name.like(f'%{game_detail.home_starting_pitcher}%'),
                        PlayerStats.innings_pitched > 0,
                        PlayerStats.season == game_date.year
                    ).first()
                    if pitcher_stats:
                        pitcher_info['home_pitcher_era'] = pitcher_stats.era

                if game_detail.away_starting_pitcher:
                    pitcher_info['away_pitcher'] = game_detail.away_starting_pitcher
                    # 查找投手統計
                    pitcher_stats = PlayerStats.query.filter(
                        PlayerStats.player_name.like(f'%{game_detail.away_starting_pitcher}%'),
                        PlayerStats.innings_pitched > 0,
                        PlayerStats.season == game_date.year
                    ).first()
                    if pitcher_stats:
                        pitcher_info['away_pitcher_era'] = pitcher_stats.era

        # 如果沒有找到具體投手，使用球隊平均ERA
        if not pitcher_info['home_pitcher']:
            pitcher_info['home_pitcher'] = f'{home_team} 投手'
            pitcher_info['home_pitcher_era'] = get_team_average_era(home_team, game_date)

        if not pitcher_info['away_pitcher']:
            pitcher_info['away_pitcher'] = f'{away_team} 投手'
            pitcher_info['away_pitcher_era'] = get_team_average_era(away_team, game_date)

        return pitcher_info

    except Exception as e:
        print(f"⚠️ 獲取投手信息失敗: {e}")
        return {
            'home_pitcher': f'{home_team} 投手',
            'away_pitcher': f'{away_team} 投手',
            'home_pitcher_era': 4.50,
            'away_pitcher_era': 4.50
        }

def get_team_average_era(team, game_date):
    """獲取球隊平均ERA"""
    try:
        from models.database import PlayerStats, Team

        # 首先找到球隊的team_id
        team_obj = Team.query.filter_by(team_code=team).first()
        if not team_obj:
            return 4.50

        # 查找該球隊該賽季的投手統計
        team_pitchers = PlayerStats.query.filter(
            PlayerStats.team_id == team_obj.team_id,
            PlayerStats.season == game_date.year,
            PlayerStats.innings_pitched > 10  # 至少投過10局
        ).all()

        if team_pitchers:
            total_era = sum(p.era for p in team_pitchers if p.era > 0)
            count = len([p for p in team_pitchers if p.era > 0])
            if count > 0:
                return total_era / count

        return 4.50  # 默認ERA

    except Exception as e:
        print(f"⚠️ 獲取球隊平均ERA失敗: {e}")
        return 4.50

def predict_with_core_v1(models, home_team, away_team, game_date):
    """使用 Core_v1.0 模型進行預測"""
    try:
        # 提取特徵
        features = extract_simple_features(home_team, away_team, game_date)
        if not features:
            return None

        # 轉換為 DataFrame
        features_df = pd.DataFrame([features])

        # 確保特徵順序與訓練時一致
        X = features_df[models['feature_columns']]

        # 標準化特徵
        X_scaled = models['feature_scaler'].transform(X)

        # 進行預測
        home_score_pred = models['home_score_model'].predict(X_scaled)[0]
        away_score_pred = models['away_score_model'].predict(X_scaled)[0]

        # 預測勝率
        if hasattr(models['win_probability_model'], 'predict_proba'):
            home_win_prob = models['win_probability_model'].predict_proba(X_scaled)[0][1]
        else:
            home_win_prob = models['win_probability_model'].predict(X_scaled)[0]

        # 確保預測值在合理範圍內
        home_score_pred = max(0, min(20, home_score_pred))
        away_score_pred = max(0, min(20, away_score_pred))
        home_win_prob = max(0, min(1, home_win_prob))

        # 獲取投手信息
        pitcher_info = get_pitcher_info_for_core_v1(home_team, away_team, game_date)

        return {
            'predicted_home_score': float(home_score_pred),
            'predicted_away_score': float(away_score_pred),
            'home_win_probability': float(home_win_prob),
            'away_win_probability': float(1 - home_win_prob),
            'confidence': 0.7,  # 固定信心度
            'total_runs': float(home_score_pred + away_score_pred),
            'pitcher_info': pitcher_info
        }

    except Exception as e:
        print(f"❌ 預測失敗: {e}")
        return None

def generate_core_v1_predictions():
    """生成 Core_v1.0 預測"""
    print("🎯 使用 Core_v1.0 模型生成預測 (簡化版)")
    print("=" * 80)
    
    # 載入模型
    print("🔧 載入 Core_v1.0 模型...")
    models = load_core_v1_model()
    if not models:
        return False
    
    print("✅ Core_v1.0 模型載入成功")
    print(f"📋 特徵數量: {len(models['feature_columns'])}")
    
    app = create_app()
    
    with app.app_context():
        # 選擇要預測的日期
        target_dates = [
            date.today() - timedelta(days=1),  # 昨天
            date.today(),                      # 今天
            date.today() + timedelta(days=1),  # 明天
        ]
        
        total_predictions = 0
        
        for target_date in target_dates:
            print(f"\n📅 處理日期: {target_date}")
            
            # 獲取該日期的比賽
            games = Game.query.filter_by(date=target_date).limit(5).all()  # 限制5場比賽進行測試
            
            if not games:
                print(f"   ⚠️  沒有找到 {target_date} 的比賽")
                continue
            
            print(f"   📊 找到 {len(games)} 場比賽")
            
            for game in games:
                try:
                    # 檢查是否已有 Core_v1.0 的預測
                    existing_prediction = Prediction.query.filter_by(
                        game_id=game.game_id,
                        model_version='Core_v1.0'
                    ).first()
                    
                    if existing_prediction:
                        print(f"   ⏭️  跳過 {game.away_team} @ {game.home_team} (已有預測)")
                        continue
                    
                    # 使用 Core_v1.0 模型進行預測
                    print(f"   🔮 預測 {game.away_team} @ {game.home_team}...")
                    
                    prediction_result = predict_with_core_v1(
                        models, game.home_team, game.away_team, game.date
                    )
                    
                    if not prediction_result:
                        print(f"   ❌ 預測失敗")
                        continue
                    
                    # 創建預測記錄
                    new_prediction = Prediction(
                        game_id=game.game_id,
                        predicted_home_score=prediction_result['predicted_home_score'],
                        predicted_away_score=prediction_result['predicted_away_score'],
                        home_win_probability=prediction_result['home_win_probability'],
                        away_win_probability=prediction_result['away_win_probability'],
                        confidence=prediction_result['confidence'],
                        model_version='Core_v1.0',  # 明確設置為 Core_v1.0
                        prediction_date=target_date
                    )
                    
                    db.session.add(new_prediction)
                    db.session.commit()
                    
                    total_predictions += 1
                    
                    print(f"   ✅ 預測完成: {prediction_result['predicted_away_score']:.1f} - {prediction_result['predicted_home_score']:.1f}")
                    print(f"      主隊勝率: {prediction_result['home_win_probability']:.1%}, 總分: {prediction_result['total_runs']:.1f}")
                    
                except Exception as e:
                    print(f"   ❌ 預測 {game.away_team} @ {game.home_team} 失敗: {e}")
                    continue
        
        print(f"\n🎉 預測生成完成!")
        print(f"   總共生成: {total_predictions} 條 Core_v1.0 預測")
        
        # 驗證結果
        print(f"\n🔍 驗證結果...")
        core_predictions = Prediction.query.filter_by(model_version='Core_v1.0').count()
        print(f"   數據庫中 Core_v1.0 預測總數: {core_predictions}")
        
        if core_predictions > 0:
            # 顯示最新的 Core_v1.0 預測
            latest_predictions = Prediction.query.filter_by(
                model_version='Core_v1.0'
            ).order_by(Prediction.created_at.desc()).limit(5).all()
            
            print(f"\n📋 最新5條 Core_v1.0 預測:")
            for i, pred in enumerate(latest_predictions, 1):
                game = Game.query.filter_by(game_id=pred.game_id).first()
                if game:
                    print(f"   [{i}] {game.away_team} @ {game.home_team}: "
                          f"{pred.predicted_away_score:.1f}-{pred.predicted_home_score:.1f} "
                          f"(勝率: {pred.home_win_probability:.1%}, 信心: {pred.confidence:.1f})")
        
        return True

if __name__ == "__main__":
    success = generate_core_v1_predictions()
    if success:
        print("\n✅ Core_v1.0 預測生成成功!")
    else:
        print("\n❌ Core_v1.0 預測生成失敗!")
