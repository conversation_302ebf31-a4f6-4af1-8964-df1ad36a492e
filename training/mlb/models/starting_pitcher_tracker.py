#!/usr/bin/env python3
"""
先發投手追蹤系統
用於記錄和查詢歷史比賽的先發投手信息，特別是當boxscore中缺少投手資料時
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import date, datetime, timedelta
from sqlalchemy import and_, or_, func
import json

from models.database import db, Game, GameDetail, PlayerStats, BoxScore
from models.daily_lineup_fetcher import DailyLineupFetcher

logger = logging.getLogger(__name__)

class StartingPitcherRecord(db.Model):
    """先發投手記錄表"""
    __tablename__ = 'starting_pitcher_records'
    
    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.String(50), db.ForeignKey('games.game_id'), nullable=False, index=True)
    game_date = db.Column(db.Date, nullable=False, index=True)
    
    # 球隊和投手信息
    home_team = db.Column(db.String(10), nullable=False)
    away_team = db.Column(db.String(10), nullable=False)
    home_starting_pitcher = db.Column(db.String(100))
    away_starting_pitcher = db.Column(db.String(100))
    
    # 投手ID (如果可獲得)
    home_pitcher_id = db.Column(db.Integer)
    away_pitcher_id = db.Column(db.Integer)
    
    # 數據來源和可靠性
    data_source = db.Column(db.String(50), nullable=False)  # 'mlb_api', 'daily_lineup', 'boxscore', 'manual'
    confidence_level = db.Column(db.String(20), default='high')  # 'high', 'medium', 'low'
    
    # 驗證狀態
    is_verified = db.Column(db.Boolean, default=False)  # 是否已通過boxscore驗證
    verification_source = db.Column(db.String(50))  # 驗證來源
    
    # 時間戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 關聯
    game = db.relationship('Game', backref='starting_pitcher_record')
    
    def __repr__(self):
        return f'<StartingPitcherRecord {self.game_id}: {self.away_starting_pitcher} vs {self.home_starting_pitcher}>'

class StartingPitcherTracker:
    """先發投手追蹤器"""
    
    def __init__(self):
        self.logger = logger
        self.daily_fetcher = DailyLineupFetcher()
        
    def record_starting_pitchers(self, game_id: str, game_date: date, 
                               home_team: str, away_team: str,
                               home_pitcher: str = None, away_pitcher: str = None,
                               data_source: str = 'unknown',
                               confidence_level: str = 'medium') -> bool:
        """
        記錄先發投手信息
        
        Args:
            game_id: 比賽ID
            game_date: 比賽日期
            home_team: 主隊
            away_team: 客隊
            home_pitcher: 主隊先發投手
            away_pitcher: 客隊先發投手
            data_source: 數據來源
            confidence_level: 可靠性等級
            
        Returns:
            bool: 是否成功記錄
        """
        try:
            # 檢查是否已存在記錄
            existing = StartingPitcherRecord.query.filter_by(game_id=game_id).first()
            
            if existing:
                # 更新現有記錄 (如果新數據更可靠)
                if self._is_better_source(data_source, existing.data_source):
                    existing.home_starting_pitcher = home_pitcher or existing.home_starting_pitcher
                    existing.away_starting_pitcher = away_pitcher or existing.away_starting_pitcher
                    existing.data_source = data_source
                    existing.confidence_level = confidence_level
                    existing.updated_at = datetime.utcnow()
                    
                    self.logger.info(f"更新先發投手記錄: {game_id} - {away_pitcher} vs {home_pitcher}")
                else:
                    self.logger.debug(f"跳過更新 (數據源不夠可靠): {game_id}")
                    return True
            else:
                # 創建新記錄
                record = StartingPitcherRecord(
                    game_id=game_id,
                    game_date=game_date,
                    home_team=home_team,
                    away_team=away_team,
                    home_starting_pitcher=home_pitcher,
                    away_starting_pitcher=away_pitcher,
                    data_source=data_source,
                    confidence_level=confidence_level
                )
                db.session.add(record)
                
                self.logger.info(f"新增先發投手記錄: {game_id} - {away_pitcher} vs {home_pitcher}")
            
            db.session.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"記錄先發投手失敗: {e}")
            db.session.rollback()
            return False
    
    def get_starting_pitchers(self, game_id: str) -> Optional[Dict]:
        """
        獲取比賽的先發投手信息
        
        Args:
            game_id: 比賽ID
            
        Returns:
            Dict: 包含先發投手信息的字典，如果找不到則返回None
        """
        try:
            record = StartingPitcherRecord.query.filter_by(game_id=game_id).first()
            
            if record:
                return {
                    'home_starting_pitcher': record.home_starting_pitcher,
                    'away_starting_pitcher': record.away_starting_pitcher,
                    'home_pitcher_id': record.home_pitcher_id,
                    'away_pitcher_id': record.away_pitcher_id,
                    'data_source': record.data_source,
                    'confidence_level': record.confidence_level,
                    'is_verified': record.is_verified
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"獲取先發投手失敗: {e}")
            return None
    
    def batch_collect_historical_pitchers(self, start_date: date, end_date: date) -> Dict:
        """
        批量收集歷史先發投手信息
        
        Args:
            start_date: 開始日期
            end_date: 結束日期
            
        Returns:
            Dict: 收集結果統計
        """
        stats = {
            'total_games': 0,
            'found_in_gamedetail': 0,
            'found_in_boxscore': 0,
            'found_via_api': 0,
            'missing_pitchers': 0,
            'errors': 0
        }
        
        try:
            # 獲取指定日期範圍的比賽
            games = Game.query.filter(
                Game.date >= start_date,
                Game.date <= end_date,
                Game.game_status == 'completed'
            ).order_by(Game.date).all()
            
            stats['total_games'] = len(games)
            self.logger.info(f"開始收集 {start_date} 到 {end_date} 的先發投手信息，共 {len(games)} 場比賽")
            
            for game in games:
                try:
                    # 1. 首先檢查GameDetail表
                    game_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
                    if game_detail and game_detail.home_starting_pitcher and game_detail.away_starting_pitcher:
                        self.record_starting_pitchers(
                            game.game_id, game.date, game.home_team, game.away_team,
                            game_detail.home_starting_pitcher, game_detail.away_starting_pitcher,
                            'gamedetail', 'high'
                        )
                        stats['found_in_gamedetail'] += 1
                        continue
                    
                    # 2. 檢查BoxScore中的投手信息
                    pitcher_info = self._extract_pitchers_from_boxscore(game.game_id)
                    if pitcher_info['home_pitcher'] and pitcher_info['away_pitcher']:
                        self.record_starting_pitchers(
                            game.game_id, game.date, game.home_team, game.away_team,
                            pitcher_info['home_pitcher'], pitcher_info['away_pitcher'],
                            'boxscore', 'medium'
                        )
                        stats['found_in_boxscore'] += 1
                        continue
                    
                    # 3. 嘗試通過每日陣容API獲取 (僅限近期比賽)
                    if (date.today() - game.date).days <= 30:
                        api_info = self._get_pitchers_from_daily_api(game.date, game.home_team, game.away_team)
                        if api_info['home_pitcher'] and api_info['away_pitcher']:
                            self.record_starting_pitchers(
                                game.game_id, game.date, game.home_team, game.away_team,
                                api_info['home_pitcher'], api_info['away_pitcher'],
                                'daily_api', 'high'
                            )
                            stats['found_via_api'] += 1
                            continue
                    
                    # 4. 記錄缺失的投手信息
                    stats['missing_pitchers'] += 1
                    self.logger.warning(f"無法找到先發投手: {game.game_id} ({game.away_team} @ {game.home_team})")
                    
                except Exception as e:
                    stats['errors'] += 1
                    self.logger.error(f"處理比賽 {game.game_id} 時出錯: {e}")
            
            self.logger.info(f"先發投手收集完成: {stats}")
            return stats
            
        except Exception as e:
            self.logger.error(f"批量收集先發投手失敗: {e}")
            stats['errors'] += 1
            return stats
    
    def _extract_pitchers_from_boxscore(self, game_id: str) -> Dict:
        """從BoxScore中提取先發投手信息"""
        result = {'home_pitcher': None, 'away_pitcher': None}
        
        try:
            # 查詢該比賽的投手統計，按投球局數排序找到先發投手
            from models.database import PlayerGameStats
            
            # 主隊先發投手 (投球局數最多的投手)
            home_pitcher = db.session.query(PlayerGameStats)\
                .join(Game, PlayerGameStats.game_id == Game.game_id)\
                .filter(
                    PlayerGameStats.game_id == game_id,
                    PlayerGameStats.is_home == True,
                    PlayerGameStats.innings_pitched > 0
                )\
                .order_by(PlayerGameStats.innings_pitched.desc())\
                .first()
            
            if home_pitcher:
                result['home_pitcher'] = home_pitcher.player_name
            
            # 客隊先發投手
            away_pitcher = db.session.query(PlayerGameStats)\
                .join(Game, PlayerGameStats.game_id == Game.game_id)\
                .filter(
                    PlayerGameStats.game_id == game_id,
                    PlayerGameStats.is_home == False,
                    PlayerGameStats.innings_pitched > 0
                )\
                .order_by(PlayerGameStats.innings_pitched.desc())\
                .first()
            
            if away_pitcher:
                result['away_pitcher'] = away_pitcher.player_name
                
        except Exception as e:
            self.logger.error(f"從BoxScore提取投手失敗: {e}")
        
        return result
    
    def _get_pitchers_from_daily_api(self, game_date: date, home_team: str, away_team: str) -> Dict:
        """通過每日陣容API獲取先發投手"""
        result = {'home_pitcher': None, 'away_pitcher': None}
        
        try:
            daily_data = self.daily_fetcher.get_daily_lineups(game_date)
            
            for game_info in daily_data.get('games', []):
                if (home_team in game_info.get('home_team', '') and 
                    away_team in game_info.get('away_team', '')):
                    
                    home_pitcher_info = game_info.get('home_pitcher')
                    away_pitcher_info = game_info.get('away_pitcher')
                    
                    if home_pitcher_info:
                        result['home_pitcher'] = home_pitcher_info if isinstance(home_pitcher_info, str) else home_pitcher_info.get('name')
                    
                    if away_pitcher_info:
                        result['away_pitcher'] = away_pitcher_info if isinstance(away_pitcher_info, str) else away_pitcher_info.get('name')
                    
                    break
                    
        except Exception as e:
            self.logger.error(f"通過API獲取投手失敗: {e}")
        
        return result
    
    def _is_better_source(self, new_source: str, existing_source: str) -> bool:
        """判斷新數據源是否比現有數據源更可靠"""
        source_priority = {
            'gamedetail': 4,
            'daily_api': 3,
            'boxscore': 2,
            'manual': 1,
            'unknown': 0
        }
        
        return source_priority.get(new_source, 0) > source_priority.get(existing_source, 0)
    
    def verify_with_boxscore(self, game_id: str) -> bool:
        """使用BoxScore驗證先發投手記錄"""
        try:
            record = StartingPitcherRecord.query.filter_by(game_id=game_id).first()
            if not record:
                return False
            
            boxscore_pitchers = self._extract_pitchers_from_boxscore(game_id)
            
            # 檢查是否匹配
            home_match = (record.home_starting_pitcher and 
                         boxscore_pitchers['home_pitcher'] and
                         record.home_starting_pitcher in boxscore_pitchers['home_pitcher'])
            
            away_match = (record.away_starting_pitcher and 
                         boxscore_pitchers['away_pitcher'] and
                         record.away_starting_pitcher in boxscore_pitchers['away_pitcher'])
            
            if home_match and away_match:
                record.is_verified = True
                record.verification_source = 'boxscore'
                db.session.commit()
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"驗證先發投手失敗: {e}")
            return False
