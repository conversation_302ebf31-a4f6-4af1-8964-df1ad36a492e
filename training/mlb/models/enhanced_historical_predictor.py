#!/usr/bin/env python3
"""
增強歷史預測器
整合6年歷史數據(2019-2025)的智能預測系統
"""

import logging
from datetime import date, datetime
from typing import Dict, List, Optional, Tuple
import numpy as np
import asyncio

from models.historical_data_optimizer import HistoricalDataOptimizer, COVID19SeasonHandler
from models.database import db, Game, PlayerStats, TeamStats
from sqlalchemy import and_, or_

logger = logging.getLogger(__name__)

class EnhancedHistoricalPredictor:
    """增強歷史預測器 - 充分利用2019-2025年豐富數據"""
    
    def __init__(self):
        self.data_optimizer = HistoricalDataOptimizer()
        self.covid_handler = COVID19SeasonHandler()
        
        # 預測模型權重配置
        self.prediction_weights = {
            'recent_form': 0.4,        # 近期表現
            'historical_average': 0.3, # 歷史平均
            'matchup_history': 0.2,    # 對戰歷史
            'situational_factors': 0.1 # 情境因素
        }
        
        # 信心度評估閾值
        self.confidence_thresholds = {
            'high': 0.8,     # 高信心度
            'medium': 0.6,   # 中信心度
            'low': 0.4       # 低信心度
        }
    
    async def predict_game_with_historical_analysis(self, 
                                                   game_id: str, 
                                                   target_date: date) -> Dict:
        """
        使用歷史數據分析進行比賽預測
        
        Args:
            game_id: 比賽ID
            target_date: 預測目標日期
            
        Returns:
            包含詳細分析的預測結果
        """
        
        logger.info(f"開始歷史分析預測: {game_id} on {target_date}")
        
        try:
            # 1. 獲取比賽基本信息
            game_info = await self._get_game_info(game_id)
            if not game_info:
                raise ValueError(f"找不到比賽信息: {game_id}")
            
            # 2. 並行獲取球隊歷史數據
            team_tasks = [
                self._analyze_team_historical_performance(
                    game_info['home_team'], target_date, is_home=True
                ),
                self._analyze_team_historical_performance(
                    game_info['away_team'], target_date, is_home=False
                )
            ]
            
            home_analysis, away_analysis = await asyncio.gather(*team_tasks)
            
            # 3. 投手對戰分析 (如果有投手數據)
            pitching_analysis = await self._analyze_pitching_matchup(
                game_info, target_date
            )
            
            # 4. 歷史對戰分析
            head_to_head_analysis = await self._analyze_head_to_head_history(
                game_info['home_team'], game_info['away_team'], target_date
            )
            
            # 5. 綜合預測計算
            prediction_result = self._calculate_comprehensive_prediction(
                home_analysis, away_analysis, pitching_analysis, head_to_head_analysis
            )
            
            # 6. 信心度評估
            confidence_assessment = self._assess_prediction_confidence(
                prediction_result, home_analysis, away_analysis
            )
            
            # 7. 生成最終結果
            final_result = {
                'game_id': game_id,
                'game_info': game_info,
                'target_date': target_date.isoformat(),
                'prediction': prediction_result,
                'confidence': confidence_assessment,
                'analysis_details': {
                    'home_team_analysis': home_analysis,
                    'away_team_analysis': away_analysis,
                    'pitching_analysis': pitching_analysis,
                    'head_to_head_analysis': head_to_head_analysis
                },
                'data_sources': {
                    'seasons_used': list(range(2019, 2026)),
                    'covid_adjustments': True,
                    'total_historical_games': self._count_historical_games()
                },
                'generated_at': datetime.now().isoformat()
            }
            
            logger.info(f"歷史分析預測完成: {game_id}")
            return final_result
            
        except Exception as e:
            logger.error(f"歷史分析預測失敗 {game_id}: {e}", exc_info=True)
            return {
                'game_id': game_id,
                'error': str(e),
                'prediction': None
            }
    
    async def _get_game_info(self, game_id: str) -> Optional[Dict]:
        """獲取比賽基本信息"""
        
        try:
            game = Game.query.filter_by(game_id=game_id).first()
            if not game:
                return None
                
            return {
                'game_id': game_id,
                'home_team': game.home_team,
                'away_team': game.away_team,
                'date': game.date,
                'venue': game.venue
            }
            
        except Exception as e:
            logger.error(f"獲取比賽信息失敗 {game_id}: {e}")
            return None
    
    async def _analyze_team_historical_performance(self, 
                                                 team: str, 
                                                 target_date: date, 
                                                 is_home: bool = True) -> Dict:
        """分析球隊歷史表現"""
        
        logger.info(f"分析球隊 {team} 的歷史表現")
        
        try:
            # 1. 獲取球隊歷史統計
            team_stats = await self._get_team_historical_stats(team)
            
            # 2. 應用歷史權重
            weighted_stats = {}
            total_weight = 0
            
            for season, stats in team_stats.items():
                weight = self.data_optimizer.seasonal_weights.get(season, 0.1)
                quality_score = self.data_optimizer.season_quality_scores.get(season, 0.5)
                
                # COVID調整
                if season == 2020:
                    weight *= self.data_optimizer.covid_adjustment_factor
                
                final_weight = weight * quality_score
                weighted_stats[season] = {
                    'stats': stats,
                    'weight': final_weight
                }
                total_weight += final_weight
            
            # 3. 計算加權平均表現
            aggregated_performance = self._calculate_team_weighted_performance(
                weighted_stats, total_weight
            )
            
            # 4. 主客場調整
            if is_home:
                aggregated_performance = self._apply_home_field_advantage(
                    aggregated_performance, team
                )
            
            # 5. 近期狀態分析
            recent_form = self._analyze_recent_team_form(team, target_date)
            
            return {
                'team': team,
                'is_home': is_home,
                'aggregated_performance': aggregated_performance,
                'recent_form': recent_form,
                'historical_seasons': list(weighted_stats.keys()),
                'total_weight': total_weight,
                'analysis_timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"球隊歷史分析失敗 {team}: {e}")
            return {'team': team, 'error': str(e)}
    
    async def _get_team_historical_stats(self, team: str) -> Dict:
        """獲取球隊歷史統計"""
        
        try:
            team_stats = {}
            
            # 查詢2019-2025年的球隊統計
            for season in range(2019, 2026):
                stats = TeamStats.query.filter_by(
                    team_id=self._get_team_id(team),
                    season=season
                ).first()
                
                if stats:
                    # 計算每場平均得分 - 修復整季vs平均的問題
                    games_played = stats.wins + stats.losses
                    avg_runs_scored = stats.runs_scored / games_played if games_played > 0 else 4.5
                    avg_runs_allowed = stats.runs_allowed / games_played if games_played > 0 else 4.5
                    
                    team_stats[season] = {
                        'wins': stats.wins,
                        'losses': stats.losses,
                        'win_percentage': stats.win_percentage,
                        'runs_scored': avg_runs_scored,  # 每場平均得分
                        'runs_allowed': avg_runs_allowed,  # 每場平均失分
                        'era': stats.era,
                        'batting_avg': stats.batting_avg,
                        'home_record': f"{stats.home_wins}-{stats.home_losses}",
                        'away_record': f"{stats.away_wins}-{stats.away_losses}",
                        'games_played': games_played,
                        'total_runs_scored': stats.runs_scored,  # 保留原始總分數據
                        'total_runs_allowed': stats.runs_allowed
                    }
            
            return team_stats
            
        except Exception as e:
            logger.error(f"獲取球隊統計失敗 {team}: {e}")
            return {}
    
    def _calculate_team_weighted_performance(self, 
                                           weighted_stats: Dict, 
                                           total_weight: float) -> Dict:
        """計算球隊加權表現"""
        
        if total_weight == 0:
            return {}
        
        performance_metrics = [
            'win_percentage', 'runs_scored', 'runs_allowed', 
            'era', 'batting_avg'
        ]
        
        aggregated = {}
        
        for metric in performance_metrics:
            weighted_sum = 0
            valid_weight = 0
            
            for season_data in weighted_stats.values():
                stats = season_data['stats']
                weight = season_data['weight']
                
                if metric in stats and stats[metric] is not None:
                    weighted_sum += stats[metric] * weight
                    valid_weight += weight
            
            if valid_weight > 0:
                aggregated[metric] = weighted_sum / valid_weight
        
        # 計算衍生指標
        if 'runs_scored' in aggregated and 'runs_allowed' in aggregated:
            aggregated['run_differential'] = aggregated['runs_scored'] - aggregated['runs_allowed']
            aggregated['pythagorean_wins'] = (aggregated['runs_scored'] ** 2) / \
                                           (aggregated['runs_scored'] ** 2 + aggregated['runs_allowed'] ** 2)
        
        return aggregated
    
    def _apply_home_field_advantage(self, performance: Dict, team: str) -> Dict:
        """應用主場優勢調整"""
        
        # MLB平均主場優勢約為0.54勝率
        home_field_boost = 0.04  # 4%的勝率提升
        
        adjusted_performance = performance.copy()
        
        if 'win_percentage' in adjusted_performance:
            adjusted_performance['win_percentage'] += home_field_boost
            adjusted_performance['win_percentage'] = min(adjusted_performance['win_percentage'], 0.95)
        
        if 'runs_scored' in adjusted_performance:
            adjusted_performance['runs_scored'] *= 1.02  # 主場打擊稍微提升
        
        if 'era' in adjusted_performance:
            adjusted_performance['era'] *= 0.98  # 主場投手ERA稍微降低
        
        adjusted_performance['home_field_advantage_applied'] = True
        
        return adjusted_performance
    
    def _generate_pitching_insights(self, home_analysis: Dict, away_analysis: Dict) -> List[str]:
        """生成投手對戰洞察"""
        
        insights = []
        
        try:
            # 主隊投手洞察
            home_insights = home_analysis.get('key_matchup_insights', [])
            for insight in home_insights[:2]:  # 最多2個主要洞察
                insights.append(f"主隊投手: {insight}")
            
            # 客隊投手洞察
            away_insights = away_analysis.get('key_matchup_insights', [])
            for insight in away_insights[:2]:  # 最多2個主要洞察
                insights.append(f"客隊投手: {insight}")
            
            # 比較性洞察
            home_expected = home_analysis.get('predicted_impact', {}).get('expected_runs_allowed', 4.5)
            away_expected = away_analysis.get('predicted_impact', {}).get('expected_runs_allowed', 4.5)
            
            if abs(home_expected - away_expected) >= 1.0:
                better_pitcher = "主隊" if home_expected < away_expected else "客隊"
                insights.append(f"{better_pitcher}投手在對戰中具明顯優勢")
            
            # 信心度警告
            home_conf = home_analysis.get('confidence_level', 'low')
            away_conf = away_analysis.get('confidence_level', 'low')
            
            if home_conf == 'low' and away_conf == 'low':
                insights.append("兩投手歷史對戰數據有限，預測不確定性較高")
            
        except Exception as e:
            logger.error(f"生成投手洞察失敗: {e}")
            insights.append("投手對戰分析出現問題")
        
        return insights
    
    def _assess_overall_pitching_confidence(self, home_analysis: Dict, away_analysis: Dict) -> str:
        """評估整體投手分析信心度"""
        
        home_conf = home_analysis.get('confidence_level', 'low')
        away_conf = away_analysis.get('confidence_level', 'low')
        
        # 信心度映射
        conf_score = {'high': 3, 'medium': 2, 'low': 1}
        
        home_score = conf_score.get(home_conf, 1)
        away_score = conf_score.get(away_conf, 1)
        
        avg_score = (home_score + away_score) / 2
        
        if avg_score >= 2.5:
            return 'high'
        elif avg_score >= 1.5:
            return 'medium'
        else:
            return 'low'
    
    def _analyze_recent_team_form(self, team: str, target_date: date) -> Dict:
        """分析球隊近期狀態"""
        
        # 分析最近10場比賽的表現
        recent_games = Game.query.filter(
            or_(Game.home_team == team, Game.away_team == team),
            Game.date < target_date,
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).order_by(Game.date.desc()).limit(10).all()
        
        if not recent_games:
            return {'status': 'no_recent_data'}
        
        wins = 0
        total_runs = 0
        total_runs_allowed = 0
        
        for game in recent_games:
            if game.home_team == team:
                team_score = game.home_score
                opponent_score = game.away_score
            else:
                team_score = game.away_score
                opponent_score = game.home_score
            
            if team_score > opponent_score:
                wins += 1
            
            total_runs += team_score
            total_runs_allowed += opponent_score
        
        recent_record = wins / len(recent_games)
        avg_runs_scored = total_runs / len(recent_games)
        avg_runs_allowed = total_runs_allowed / len(recent_games)
        
        # 判斷狀態
        if recent_record >= 0.7:
            form_status = 'hot'
        elif recent_record >= 0.4:
            form_status = 'average'
        else:
            form_status = 'cold'
        
        return {
            'games_analyzed': len(recent_games),
            'recent_record': recent_record,
            'wins': wins,
            'losses': len(recent_games) - wins,
            'avg_runs_scored': avg_runs_scored,
            'avg_runs_allowed': avg_runs_allowed,
            'form_status': form_status,
            'run_differential': avg_runs_scored - avg_runs_allowed
        }
    
    async def _analyze_pitching_matchup(self, game_info: Dict, target_date: date) -> Dict:
        """分析投手對戰 - 現在包含精細化的投手對球隊/打者分析"""
        
        from models.pitcher_vs_team_analyzer import PitcherVsTeamAnalyzer
        from models.starting_pitcher_tracker import StartingPitcherTracker
        
        try:
            # 獲取投手信息
            pitcher_tracker = StartingPitcherTracker()
            pitcher_info = pitcher_tracker.get_starting_pitchers(game_info.get('game_id'))
            
            if not pitcher_info or not pitcher_info.get('home_starting_pitcher') or not pitcher_info.get('away_starting_pitcher'):
                logger.warning("投手信息不完整，使用基礎分析")
                return {
                    'analysis_type': 'basic_team_based',
                    'home_pitcher': '未確認',
                    'away_pitcher': '未確認',
                    'pitching_advantage': 'neutral',
                    'note': '投手信息不足，預測基於球隊平均表現'
                }
            
            home_pitcher = pitcher_info['home_starting_pitcher']
            away_pitcher = pitcher_info['away_starting_pitcher']
            home_team = game_info.get('home_team')
            away_team = game_info.get('away_team')
            
            logger.info(f"🔥 進行精細投手分析: {away_pitcher} (客) vs {home_pitcher} (主)")
            
            # 創建投手分析器
            pitcher_analyzer = PitcherVsTeamAnalyzer()
            
            # 並行分析兩名投手
            home_pitcher_analysis, away_pitcher_analysis = await asyncio.gather(
                pitcher_analyzer.analyze_pitcher_vs_team(
                    pitcher_name=home_pitcher,
                    opposing_team=away_team,
                    target_date=target_date,
                    is_home_pitcher=True
                ),
                pitcher_analyzer.analyze_pitcher_vs_team(
                    pitcher_name=away_pitcher,
                    opposing_team=home_team,
                    target_date=target_date,
                    is_home_pitcher=False
                )
            )
            
            # 比較兩投手預期表現
            home_expected_runs = home_pitcher_analysis.get('predicted_impact', {}).get('expected_runs_allowed', 4.5)
            away_expected_runs = away_pitcher_analysis.get('predicted_impact', {}).get('expected_runs_allowed', 4.5)
            
            # 判斷投手優勢
            pitching_advantage = 'neutral'
            if home_expected_runs < away_expected_runs - 0.5:
                pitching_advantage = 'home_pitcher'
            elif away_expected_runs < home_expected_runs - 0.5:
                pitching_advantage = 'away_pitcher'
            
            # 合併分析結果
            pitching_matchup_result = {
                'analysis_type': 'detailed_pitcher_vs_team',
                'home_pitcher': home_pitcher,
                'away_pitcher': away_pitcher,
                'home_pitcher_analysis': home_pitcher_analysis,
                'away_pitcher_analysis': away_pitcher_analysis,
                'pitching_advantage': pitching_advantage,
                'expected_runs': {
                    'home_pitcher_allows': home_expected_runs,
                    'away_pitcher_allows': away_expected_runs,
                    'total_expected': home_expected_runs + away_expected_runs
                },
                'key_insights': self._generate_pitching_insights(home_pitcher_analysis, away_pitcher_analysis),
                'confidence': {
                    'home_pitcher_confidence': home_pitcher_analysis.get('confidence_level', 'low'),
                    'away_pitcher_confidence': away_pitcher_analysis.get('confidence_level', 'low'),
                    'overall_confidence': self._assess_overall_pitching_confidence(home_pitcher_analysis, away_pitcher_analysis)
                }
            }
            
            logger.info(f"✅ 投手分析完成: 優勢 = {pitching_advantage}, 總預期得分 = {home_expected_runs + away_expected_runs:.1f}")
            
            return pitching_matchup_result
            
        except Exception as e:
            logger.error(f"精細投手分析失敗: {e}")
            # 回退到基礎分析
            return {
                'analysis_type': 'fallback',
                'error': str(e),
                'pitching_advantage': 'neutral',
                'note': '投手精細分析失敗，使用基礎預測'
            }
    
    async def _analyze_head_to_head_history(self, 
                                          home_team: str, 
                                          away_team: str, 
                                          target_date: date) -> Dict:
        """分析兩隊對戰歷史"""
        
        try:
            # 查詢歷史對戰記錄
            historical_games = Game.query.filter(
                and_(
                    Game.home_team == home_team,
                    Game.away_team == away_team,
                    Game.date < target_date,
                    Game.home_score.isnot(None),
                    Game.away_score.isnot(None)
                )
            ).order_by(Game.date.desc()).limit(20).all()  # 最近20場對戰
            
            if not historical_games:
                return {'status': 'no_historical_data'}
            
            home_wins = 0
            total_home_runs = 0
            total_away_runs = 0
            season_breakdown = {}
            
            for game in historical_games:
                season = self.data_optimizer._extract_season_from_date(game.date)
                
                if season not in season_breakdown:
                    season_breakdown[season] = {'games': 0, 'home_wins': 0}
                
                season_breakdown[season]['games'] += 1
                
                if game.home_score > game.away_score:
                    home_wins += 1
                    season_breakdown[season]['home_wins'] += 1
                
                total_home_runs += game.home_score
                total_away_runs += game.away_score
            
            # 應用歷史權重
            weighted_home_wins = 0
            total_weight = 0
            
            for season, data in season_breakdown.items():
                weight = self.data_optimizer.seasonal_weights.get(season, 0.1)
                if season == 2020:
                    weight *= self.data_optimizer.covid_adjustment_factor
                
                weighted_home_wins += data['home_wins'] * weight
                total_weight += data['games'] * weight
            
            home_win_rate = weighted_home_wins / total_weight if total_weight > 0 else 0.5
            avg_total_runs = (total_home_runs + total_away_runs) / len(historical_games)
            
            return {
                'total_games': len(historical_games),
                'home_wins': home_wins,
                'away_wins': len(historical_games) - home_wins,
                'weighted_home_win_rate': home_win_rate,
                'avg_total_runs': avg_total_runs,
                'season_breakdown': season_breakdown,
                'historical_advantage': 'home' if home_win_rate > 0.55 else 'away' if home_win_rate < 0.45 else 'neutral'
            }
            
        except Exception as e:
            logger.error(f"對戰歷史分析失敗 {home_team} vs {away_team}: {e}")
            return {'error': str(e)}
    
    def _calculate_comprehensive_prediction(self, 
                                          home_analysis: Dict, 
                                          away_analysis: Dict, 
                                          pitching_analysis: Dict,
                                          head_to_head_analysis: Dict) -> Dict:
        """計算綜合預測結果 - 現在整合精細投手對戰分析"""
        
        try:
            # 基礎預測計算
            home_performance = home_analysis.get('aggregated_performance', {})
            away_performance = away_analysis.get('aggregated_performance', {})
            
            # 基礎得分預測
            base_home_runs = self._predict_team_runs(home_performance, True)
            base_away_runs = self._predict_team_runs(away_performance, False)
            
            # 🔥 整合精細投手分析的核心邏輯
            if pitching_analysis.get('analysis_type') == 'detailed_pitcher_vs_team':
                logger.info("🎯 應用精細投手對戰分析結果")
                
                # 獲取投手預期失分
                pitcher_expected_runs = pitching_analysis.get('expected_runs', {})
                home_pitcher_allows = pitcher_expected_runs.get('home_pitcher_allows', base_away_runs)
                away_pitcher_allows = pitcher_expected_runs.get('away_pitcher_allows', base_home_runs)
                
                # 投手分析權重 (根據信心度調整)
                pitching_confidence = pitching_analysis.get('confidence', {}).get('overall_confidence', 'medium')
                
                if pitching_confidence == 'high':
                    pitcher_weight = 0.7  # 高信心度時投手分析佔70%
                elif pitching_confidence == 'medium':
                    pitcher_weight = 0.5  # 中信心度時投手分析佔50%
                else:
                    pitcher_weight = 0.3  # 低信心度時投手分析佔30%
                
                # 結合球隊打擊能力和投手防禦能力
                # 客隊得分 = 客隊打擊能力 × (1-pitcher_weight) + (9 - 主隊投手失分) × pitcher_weight
                away_predicted_runs = (base_away_runs * (1 - pitcher_weight)) + (home_pitcher_allows * pitcher_weight)
                home_predicted_runs = (base_home_runs * (1 - pitcher_weight)) + (away_pitcher_allows * pitcher_weight)
                
                logger.info(f"投手調整: 客隊 {base_away_runs:.1f}→{away_predicted_runs:.1f}, 主隊 {base_home_runs:.1f}→{home_predicted_runs:.1f}")
                
            else:
                # 沒有詳細投手分析時使用基礎預測
                home_predicted_runs = base_home_runs  
                away_predicted_runs = base_away_runs
                logger.warning("使用基礎預測 (無精細投手分析)")
            
            # 考慮近期狀態調整
            home_form = home_analysis.get('recent_form', {})
            away_form = away_analysis.get('recent_form', {})
            
            if home_form.get('form_status') == 'hot':
                home_predicted_runs *= 1.05
            elif home_form.get('form_status') == 'cold':
                home_predicted_runs *= 0.95
                
            if away_form.get('form_status') == 'hot':
                away_predicted_runs *= 1.05
            elif away_form.get('form_status') == 'cold':
                away_predicted_runs *= 0.95
            
            # 考慮對戰歷史
            h2h = head_to_head_analysis
            if h2h.get('historical_advantage') == 'home':
                home_predicted_runs *= 1.03
            elif h2h.get('historical_advantage') == 'away':
                away_predicted_runs *= 1.03
            
            # 計算勝率
            total_runs = home_predicted_runs + away_predicted_runs
            home_win_probability = home_predicted_runs / total_runs if total_runs > 0 else 0.5
            
            # 大小分預測
            predicted_total = home_predicted_runs + away_predicted_runs
            
            return {
                'home_predicted_runs': round(home_predicted_runs, 1),
                'away_predicted_runs': round(away_predicted_runs, 1),
                'predicted_total_runs': round(predicted_total, 1),
                'home_win_probability': round(home_win_probability, 3),
                'away_win_probability': round(1 - home_win_probability, 3),
                'spread_prediction': round(home_predicted_runs - away_predicted_runs, 1),
                'prediction_method': 'historical_weighted_analysis'
            }
            
        except Exception as e:
            logger.error(f"綜合預測計算失敗: {e}")
            return {'error': str(e)}
    
    def _predict_team_runs(self, performance: Dict, is_home: bool) -> float:
        """預測球隊得分"""
        
        # 基礎得分預測 (MLB平均約4.5分/場)
        base_runs = 4.5
        
        if not performance:
            return base_runs
        
        # 根據歷史平均調整
        runs_scored = performance.get('runs_scored', 4.5)
        runs_allowed = performance.get('runs_allowed', 4.5)
        
        # 綜合考慮攻擊和防守
        predicted_runs = (runs_scored * 0.7) + (base_runs - (runs_allowed - base_runs) * 0.3)
        
        # 主場優勢已在performance中考慮
        return max(2.0, min(predicted_runs, 12.0))  # 限制在合理範圍內
    
    def _assess_prediction_confidence(self, 
                                    prediction: Dict, 
                                    home_analysis: Dict, 
                                    away_analysis: Dict) -> Dict:
        """評估預測信心度"""
        
        confidence_factors = []
        
        # 1. 數據完整性
        home_seasons = len(home_analysis.get('historical_seasons', []))
        away_seasons = len(away_analysis.get('historical_seasons', []))
        data_completeness = (home_seasons + away_seasons) / 12  # 最大6+6=12個賽季
        confidence_factors.append(min(data_completeness, 1.0))
        
        # 2. 近期表現一致性
        home_form = home_analysis.get('recent_form', {})
        away_form = away_analysis.get('recent_form', {})
        
        if home_form.get('games_analyzed', 0) >= 5 and away_form.get('games_analyzed', 0) >= 5:
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.6)
        
        # 3. 預測合理性檢查
        predicted_total = prediction.get('predicted_total_runs', 9.0)
        if 6.0 <= predicted_total <= 12.0:  # MLB合理總分範圍
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.7)
        
        # 計算總體信心度
        overall_confidence = np.mean(confidence_factors)
        
        # 分類信心等級
        if overall_confidence >= self.confidence_thresholds['high']:
            confidence_level = 'high'
        elif overall_confidence >= self.confidence_thresholds['medium']:
            confidence_level = 'medium'
        else:
            confidence_level = 'low'
        
        return {
            'overall_confidence': round(overall_confidence, 3),
            'confidence_level': confidence_level,
            'confidence_factors': {
                'data_completeness': confidence_factors[0],
                'recent_form_reliability': confidence_factors[1],
                'prediction_reasonableness': confidence_factors[2]
            }
        }
    
    def _get_team_id(self, team_code: str) -> int:
        """獲取球隊ID (需要根據實際數據庫結構調整)"""
        
        # 這是一個簡化的映射，實際應該查詢teams表
        team_mapping = {
            'BOS': 111, 'NYY': 147, 'TB': 139, 'TOR': 141, 'BAL': 110,
            'CWS': 145, 'CLE': 114, 'DET': 116, 'KC': 118, 'MIN': 142,
            'HOU': 117, 'LAA': 108, 'OAK': 133, 'SEA': 136, 'TEX': 140,
            'ATL': 144, 'NYM': 121, 'PHI': 143, 'WSH': 120, 'MIA': 146,
            'CHC': 112, 'MIL': 158, 'STL': 138, 'PIT': 134, 'CIN': 113,
            'LAD': 119, 'SD': 135, 'SF': 137, 'COL': 115, 'AZ': 109
        }
        
        return team_mapping.get(team_code, 1)
    
    def _count_historical_games(self) -> int:
        """計算歷史比賽總數"""
        
        try:
            total = Game.query.filter(
                Game.date >= date(2019, 3, 1),
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).count()
            
            return total
            
        except Exception:
            return 15202  # 根據之前的查詢結果

# 使用示例
if __name__ == "__main__":
    async def test_prediction():
        predictor = EnhancedHistoricalPredictor()
        
        # 測試預測
        result = await predictor.predict_game_with_historical_analysis(
            game_id="test_game_123",
            target_date=date(2025, 8, 27)
        )
        
        print("Enhanced Historical Prediction Result:")
        print(json.dumps(result, indent=2, default=str))
    
    import json
    import asyncio
    asyncio.run(test_prediction())