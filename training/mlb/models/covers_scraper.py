#!/usr/bin/env python3
"""
Covers.com MLB歷史賠率數據抓取器
專門抓取covers.com的MLB歷史比賽和賠率數據
"""

import requests
import logging
import time
import re
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class CoversMLBScraper:
    """Covers.com MLB歷史賠率抓取器"""
    
    def __init__(self):
        self.base_url = "https://www.covers.com"
        self.session = requests.Session()
        
        # 設置請求頭，模擬瀏覽器，但不接受壓縮
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'identity',  # 不接受壓縮
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # MLB球隊名稱映射：從covers.com名稱到數據庫縮寫
        self.covers_to_db_mapping = {
            'Tampa Bay': 'TB', 'Detroit': 'DET', 'Colorado': 'COL', 'Boston': 'BOS',
            'Miami': 'MIA', 'Cincinnati': 'CIN', 'Toronto': 'TOR', 'Chi. White Sox': 'CWS',
            'LA Dodgers': 'LAD', 'Milwaukee': 'MIL', 'Pittsburgh': 'PIT', 'Kansas City': 'KC',
            'Cleveland': 'CLE', 'Houston': 'HOU', 'Texas': 'TEX', 'LA Angels': 'LAA',
            'Arizona': 'AZ', 'San Diego': 'SD', 'Philadelphia': 'PHI', 'San Francisco': 'SF',
            'St. Louis': 'STL', 'Atlanta': 'ATL', 'Baltimore': 'BAL', 'NY Yankees': 'NYY',
            'NY Mets': 'NYM', 'Chi. Cubs': 'CHC', 'Seattle': 'SEA', 'Washington': 'WSH',
            'Minnesota': 'MIN', 'Athletics': 'OAK'
        }

        # 舊的映射保持向後兼容
        self.team_mapping = {v: k for k, v in self.covers_to_db_mapping.items()}
        self.reverse_team_mapping = self.covers_to_db_mapping
        
    def fetch_mlb_games_for_date(self, target_date: date) -> Dict:
        """
        抓取指定日期的MLB比賽數據
        
        Args:
            target_date: 目標日期
            
        Returns:
            包含比賽數據的字典
        """
        try:
            # 構建URL
            date_str = target_date.strftime('%Y-%m-%d')
            url = f"{self.base_url}/sports/mlb/matchups?selectedDate={date_str}"
            
            logger.info(f"正在抓取 {date_str} 的MLB數據: {url}")
            
            # 發送請求
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # 解析HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找比賽數據
            games = self._parse_games_from_html(soup, target_date)
            
            logger.info(f"成功抓取到 {len(games)} 場比賽")
            
            return {
                'success': True,
                'date': date_str,
                'games': games,
                'total_games': len(games)
            }
            
        except requests.RequestException as e:
            logger.error(f"網絡請求失敗: {e}")
            return {'success': False, 'error': f'網絡請求失敗: {e}'}
        except Exception as e:
            logger.error(f"抓取數據失敗: {e}")
            return {'success': False, 'error': f'抓取數據失敗: {e}'}
    
    def _parse_games_from_html(self, soup: BeautifulSoup, target_date: date) -> List[Dict]:
        """從HTML中解析比賽數據"""
        games = []

        try:
            # 使用soup.stripped_strings來獲取清理過的文本
            all_texts = list(soup.stripped_strings)

            # 查找包含 "@" 的比賽對陣
            matchup_texts = []
            for text in all_texts:
                if '@' in text and len(text.split()) >= 3:
                    # 檢查是否是比賽對陣格式
                    parts = text.split('@')
                    if len(parts) == 2:
                        away_team = parts[0].strip()
                        home_team = parts[1].strip()
                        if away_team and home_team:
                            matchup_texts.append(text)

            logger.info(f"找到 {len(matchup_texts)} 個比賽對陣")

            # 為每個對陣查找相關的比賽結果信息
            for matchup in matchup_texts:
                game_info = self._parse_single_matchup(matchup, all_texts)
                if game_info:
                    games.append(game_info)
                    logger.debug(f"解析到比賽: {game_info['away_team']} @ {game_info['home_team']}")

            logger.info(f"成功解析到 {len(games)} 場比賽")

        except Exception as e:
            logger.error(f"解析HTML失敗: {e}")
            import traceback
            traceback.print_exc()

        return games

    def _parse_game_result_block(self, lines: List[str], start_index: int) -> Optional[Dict]:
        """解析單個比賽結果塊"""
        try:
            # 從當前行開始，向前後查找相關信息
            game_line = lines[start_index].strip()

            # 查找比賽隊伍和分數
            # 格式類似: "PHI 0 0 Final 6 ATL 6"
            parts = game_line.split()

            # 查找 "Final" 的位置
            final_index = -1
            for i, part in enumerate(parts):
                if 'Final' in part:
                    final_index = i
                    break

            if final_index == -1:
                return None

            # 提取隊伍和分數
            # Final前面應該是客隊信息，後面是主隊信息
            away_parts = parts[:final_index]
            home_parts = parts[final_index+1:]

            if len(away_parts) < 2 or len(home_parts) < 2:
                return None

            # 客隊：最後一個數字是分數，前面是隊名
            away_score = away_parts[-1]
            away_team = ' '.join(away_parts[:-1])

            # 主隊：第一個數字是分數，後面是隊名
            home_score = home_parts[0]
            home_team = ' '.join(home_parts[1:])

            # 清理隊名
            away_team = self._clean_team_name(away_team)
            home_team = self._clean_team_name(home_team)

            if not away_team or not home_team:
                return None

            # 查找賠率和over/under信息
            odds_info = self._extract_odds_from_context(lines[start_index-5:start_index+10])

            game_info = {
                'away_team': away_team,
                'home_team': home_team,
                'away_score': int(away_score) if away_score.isdigit() else None,
                'home_score': int(home_score) if home_score.isdigit() else None,
                'status': 'Final',
                'odds': odds_info
            }

            return game_info

        except Exception as e:
            logger.debug(f"解析比賽塊失敗: {e}")
            return None

    def _parse_single_matchup(self, matchup: str, all_texts: List[str]) -> Optional[Dict]:
        """解析單個比賽對陣"""
        try:
            # 解析球隊名稱
            parts = matchup.split('@')
            if len(parts) != 2:
                return None

            away_team = self._clean_team_name(parts[0].strip())
            home_team = self._clean_team_name(parts[1].strip())

            # 在所有文本中查找與這個對陣相關的信息
            matchup_index = -1
            for i, text in enumerate(all_texts):
                if text == matchup:
                    matchup_index = i
                    break

            if matchup_index == -1:
                return None

            # 查找附近的 "Final" 狀態
            status = None
            for i in range(max(0, matchup_index - 10), min(len(all_texts), matchup_index + 10)):
                if 'Final' in all_texts[i]:
                    status = 'Final'
                    break

            # 如果沒有找到Final狀態，可能是未開始的比賽
            if not status:
                status = 'Scheduled'

            # 查找分數信息（只有Final的比賽才有分數）
            away_score = None
            home_score = None

            if status == 'Final':
                # 在對陣附近查找分數
                for i in range(max(0, matchup_index - 5), min(len(all_texts), matchup_index + 15)):
                    text = all_texts[i]
                    # 查找包含兩個數字的文本，可能是分數
                    scores = re.findall(r'\b\d+\b', text)
                    if len(scores) >= 2:
                        # 嘗試確定這是分數而不是其他數字
                        if len(scores) == 2 and int(scores[0]) <= 20 and int(scores[1]) <= 20:
                            away_score = int(scores[0])
                            home_score = int(scores[1])
                            break

            # 查找賠率信息
            odds_info = self._extract_odds_from_matchup_context(all_texts, matchup_index)

            game_info = {
                'away_team': away_team,
                'home_team': home_team,
                'away_score': away_score,
                'home_score': home_score,
                'status': status,
                'odds': odds_info
            }

            return game_info

        except Exception as e:
            logger.debug(f"解析比賽對陣失敗: {e}")
            return None

    def _extract_games_from_text(self, text: str, target_date: date) -> List[Dict]:
        """從文本內容中提取比賽信息"""
        games = []
        
        try:
            # 查找比賽模式：Team1 @ Team2 或 Team1 vs Team2
            # 以及分數和賠率信息
            lines = text.split('\n')
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                # 查找包含 @ 的比賽行
                if '@' in line and any(team in line for team in self.team_mapping.values()):
                    game_info = self._parse_game_line(line, lines[i:i+10])  # 檢查後續10行
                    if game_info:
                        games.append(game_info)
                        
        except Exception as e:
            logger.error(f"從文本提取比賽信息失敗: {e}")
            
        return games
    
    def _parse_game_line(self, game_line: str, context_lines: List[str]) -> Optional[Dict]:
        """解析單個比賽行"""
        try:
            # 提取球隊名稱
            if '@' not in game_line:
                return None
                
            parts = game_line.split('@')
            if len(parts) != 2:
                return None
                
            away_team = parts[0].strip()
            home_team = parts[1].strip()
            
            # 清理球隊名稱
            away_team = self._clean_team_name(away_team)
            home_team = self._clean_team_name(home_team)
            
            if not away_team or not home_team:
                return None
            
            # 查找分數和賠率信息
            scores = self._extract_scores_from_context(context_lines)
            odds = self._extract_odds_from_context(context_lines)
            
            game_info = {
                'away_team': away_team,
                'home_team': home_team,
                'away_score': scores.get('away_score'),
                'home_score': scores.get('home_score'),
                'status': scores.get('status', 'Final'),
                'odds': odds
            }
            
            return game_info
            
        except Exception as e:
            logger.error(f"解析比賽行失敗: {e}")
            return None
    
    def _clean_team_name(self, team_name: str) -> Optional[str]:
        """清理和標準化球隊名稱"""
        if not team_name:
            return None

        # 移除多餘的字符和數字
        team_name = re.sub(r'\d+', '', team_name).strip()
        team_name = re.sub(r'[^\w\s\.]', '', team_name).strip()

        # 直接檢查是否是標準縮寫
        if team_name.upper() in self.team_mapping:
            return self.team_mapping[team_name.upper()]

        # 檢查covers.com到數據庫的映射
        if team_name in self.covers_to_db_mapping:
            return self.covers_to_db_mapping[team_name]

        # 模糊匹配
        for covers_name, db_abbr in self.covers_to_db_mapping.items():
            if (covers_name.lower() in team_name.lower() or
                team_name.lower() in covers_name.lower()):
                return db_abbr

        # 特殊處理一些常見變體
        team_name_lower = team_name.lower()
        if 'yankee' in team_name_lower or 'nyy' in team_name_lower:
            return 'NYY'
        elif 'dodger' in team_name_lower or 'lad' in team_name_lower:
            return 'LAD'
        elif 'angel' in team_name_lower or 'laa' in team_name_lower:
            return 'LAA'
        elif 'white sox' in team_name_lower or 'chw' in team_name_lower:
            return 'CWS'
        elif 'cubs' in team_name_lower or 'chc' in team_name_lower:
            return 'CHC'

        logger.debug(f"無法識別球隊名稱: {team_name}")
        return None
    
    def _extract_scores_from_context(self, context_lines: List[str]) -> Dict:
        """從上下文行中提取分數"""
        scores = {}
        
        for line in context_lines:
            line = line.strip()
            
            # 查找 "Final" 和分數
            if 'Final' in line:
                # 嘗試提取分數模式：數字 Final 數字
                score_match = re.search(r'(\d+)\s*Final\s*(\d+)', line)
                if score_match:
                    scores['away_score'] = int(score_match.group(1))
                    scores['home_score'] = int(score_match.group(2))
                    scores['status'] = 'Final'
                    break
                    
        return scores
    
    def _extract_odds_from_context(self, context_lines: List[str]) -> Dict:
        """從上下文行中提取賠率信息"""
        odds = {}

        for line in context_lines:
            line = line.strip()

            # 查找moneyline賠率：+數字 或 -數字
            odds_matches = re.findall(r'[+-]\d+', line)
            if odds_matches and 'moneyline' not in odds:
                odds['moneyline'] = odds_matches[0]

            # 查找over/under margin信息 (如 "o/u Margin u2")
            if 'o/u margin' in line.lower():
                # 提取over/under結果和margin
                if 'o' in line.lower() and 'margin' in line.lower():
                    margin_match = re.search(r'o(\d+\.?\d*)', line.lower())
                    if margin_match:
                        odds['total_result'] = 'over'
                        odds['total_margin'] = float(margin_match.group(1))
                elif 'u' in line.lower() and 'margin' in line.lower():
                    margin_match = re.search(r'u(\d+\.?\d*)', line.lower())
                    if margin_match:
                        odds['total_result'] = 'under'
                        odds['total_margin'] = float(margin_match.group(1))

            # 查找總分信息 (如 "The total score of 6 was under 8")
            total_match = re.search(r'total score of (\d+\.?\d*) was (over|under) (\d+\.?\d*)', line.lower())
            if total_match:
                actual_total = float(total_match.group(1))
                result = total_match.group(2)
                betting_total = float(total_match.group(3))

                odds['actual_total'] = actual_total
                odds['total_result'] = result
                odds['betting_total'] = betting_total

            # 查找獲勝賠率信息 (如 "won the game at odds of -160")
            win_odds_match = re.search(r'won the game at odds of ([+-]\d+)', line)
            if win_odds_match:
                odds['winning_odds'] = win_odds_match.group(1)

        return odds

    def _extract_odds_from_matchup_context(self, all_texts: List[str], matchup_index: int) -> Dict:
        """從比賽對陣上下文中提取賠率信息"""
        odds_info = {
            'moneyline_away': None,
            'moneyline_home': None,
            'spread_away': None,
            'spread_home': None,
            'spread_line': None,
            'total_over': None,
            'total_under': None,
            'total_line': None
        }

        try:
            # 大幅擴大搜索範圍以找到總分線信息
            start_idx = max(0, matchup_index - 50)
            end_idx = min(len(all_texts), matchup_index + 100)

            # 先搜索總分線模式
            found_total = False

            # 方法1：嘗試在單個文本元素中查找完整模式
            for i in range(start_idx, end_idx):
                text = all_texts[i]

                total_pattern = r'total score of \d+ was (?:under|over) (\d+(?:\.\d+)?)'
                total_match = re.search(total_pattern, text, re.IGNORECASE)

                if total_match:
                    odds_info['total_line'] = total_match.group(1)
                    print(f"✅ 找到總分線: {odds_info['total_line']} 從文本: {total_match.group(0)}")
                    found_total = True
                    break

            # 方法2：如果單個元素沒找到，嘗試組合相鄰元素
            if not found_total:
                for i in range(start_idx, end_idx - 5):
                    # 組合當前元素和後續5個元素
                    combined_text = ' '.join(all_texts[i:i+6])

                    total_pattern = r'total score of \d+ was (?:under|over) (\d+(?:\.\d+)?)'
                    total_match = re.search(total_pattern, combined_text, re.IGNORECASE)

                    if total_match:
                        odds_info['total_line'] = total_match.group(1)
                        print(f"✅ 找到總分線(組合): {odds_info['total_line']} 從文本: {total_match.group(0)}")
                        found_total = True
                        break

            if not found_total:
                print(f"⚠️  未找到總分線模式，搜索範圍: {start_idx}-{end_idx}")
                # 調試：顯示搜索範圍內包含 "total" 的文本
                total_texts = []
                for i in range(start_idx, end_idx):
                    if i < len(all_texts) and 'total' in all_texts[i].lower():
                        total_texts.append(f"  {i}: {all_texts[i]}")
                if total_texts:
                    print(f"  搜索範圍內包含'total'的文本:")
                    for text in total_texts[:3]:  # 只顯示前3個
                        print(text)

            # 然後搜索其他賠率信息
            for i in range(start_idx, end_idx):
                text = all_texts[i]

                # 查找moneyline賠率 (如 +150, -120)
                moneyline_matches = re.findall(r'[+-]\d{3,4}', text)
                if len(moneyline_matches) >= 2:
                    odds_info['moneyline_away'] = moneyline_matches[0]
                    odds_info['moneyline_home'] = moneyline_matches[1]

                # 查找spread賠率 (如 -1.5, +1.5)
                spread_matches = re.findall(r'[+-]\d+\.5', text)
                if spread_matches:
                    odds_info['spread_line'] = spread_matches[0]

                # 備用模式：查找傳統的 Over/Under 格式 (如 O 8.5, U 8.5, Over 8.5, Under 8.5)
                if not odds_info['total_line']:
                    total_matches = re.findall(r'(?:Over|Under|O|U)\s*(\d+\.5)', text, re.IGNORECASE)
                    if total_matches:
                        odds_info['total_line'] = total_matches[0]

        except Exception as e:
            logger.debug(f"提取賠率失敗: {e}")

        return odds_info

    def add_delay(self, seconds: float = 2.0):
        """添加延遲以避免被封鎖"""
        time.sleep(seconds)
        
    def get_team_abbreviation(self, team_name: str) -> Optional[str]:
        """獲取球隊縮寫"""
        return self.reverse_team_mapping.get(team_name)
