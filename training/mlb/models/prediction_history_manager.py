#!/usr/bin/env python3
"""
預測歷史管理系統
存儲和管理增強預測結果，包含精細投手分析和牛棚分析
"""

import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
from sqlalchemy import text, and_, or_, func
from sqlalchemy.exc import SQLAlchemyError

from models.database import db, Prediction, Game, PredictionHistory

logger = logging.getLogger(__name__)

class PredictionHistoryManager:
    """預測歷史管理器 - 存儲和查詢增強預測結果"""
    
    def __init__(self):
        self.storage_retention_days = 365  # 保留1年的歷史預測
        self.max_predictions_per_game = 5  # 每場比賽最多保留5次預測記錄
    
    def save_enhanced_prediction(self, 
                                game_id: str, 
                                prediction_result: Dict,
                                prediction_type: str = "enhanced_with_bullpen") -> bool:
        """
        保存增強預測結果到歷史記錄
        
        Args:
            game_id: 比賽ID
            prediction_result: 完整的預測結果
            prediction_type: 預測類型標識
            
        Returns:
            保存是否成功
        """
        
        logger.info(f"💾 保存增強預測到歷史記錄: {game_id}")
        
        try:
            # 1. 提取核心預測數據
            prediction_data = prediction_result.get('prediction', {})
            pitcher_analysis = prediction_result.get('pitcher_analysis', {})
            analysis_details = prediction_result.get('analysis_details', {})
            confidence = prediction_result.get('confidence', {})
            
            # 2. 構建歷史記錄條目
            history_entry = {
                'game_id': game_id,
                'prediction_date': datetime.now(),
                'prediction_type': prediction_type,
                
                # 核心預測結果
                'predicted_home_score': prediction_data.get('home_predicted_runs', 0),
                'predicted_away_score': prediction_data.get('away_predicted_runs', 0),
                'predicted_total_runs': prediction_data.get('predicted_total_runs', 0),
                'home_win_probability': prediction_data.get('home_win_probability', 0.5),
                'away_win_probability': prediction_data.get('away_win_probability', 0.5),
                
                # 投手信息
                'starting_pitcher_home': pitcher_analysis.get('home_pitcher', {}).get('name', '未確認'),
                'starting_pitcher_away': pitcher_analysis.get('away_pitcher', {}).get('name', '未確認'),
                'pitcher_matchup_advantage': pitcher_analysis.get('matchup_advantage', 'neutral'),
                
                # 信心度評估
                'confidence_level': confidence.get('confidence_level', 'medium'),
                'overall_confidence': confidence.get('overall_confidence', 0.5),
                
                # 完整分析數據 (JSON格式存儲)
                'detailed_analysis': json.dumps({
                    'pitcher_analysis': pitcher_analysis,
                    'analysis_details': analysis_details,
                    'confidence_breakdown': confidence.get('confidence_factors', {}),
                    'prediction_metadata': {
                        'strategy': prediction_result.get('strategy', 'unknown'),
                        'generated_at': prediction_result.get('generated_at', datetime.now().isoformat()),
                        'version': '3.0_enhanced_bullpen'
                    }
                }, ensure_ascii=False, default=str),
                
                'created_at': datetime.now()
            }
            
            # 3. 檢查是否已存在該比賽的預測記錄
            existing_prediction = Prediction.query.filter_by(game_id=game_id).first()
            
            if existing_prediction:
                # 更新現有預測記錄
                self._update_existing_prediction(existing_prediction, history_entry)
                action = "updated"
            else:
                # 創建新的預測記錄
                self._create_new_prediction(history_entry)
                action = "created"
            
            # 4. 保存到預測歷史表
            self._save_to_history_table(history_entry)
            
            # 5. 清理舊記錄
            self._cleanup_old_predictions(game_id)
            
            db.session.commit()
            logger.info(f"✅ 預測歷史保存成功: {action} prediction for {game_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存預測歷史失敗 {game_id}: {e}", exc_info=True)
            db.session.rollback()
            return False
    
    def _update_existing_prediction(self, prediction: Prediction, history_entry: Dict):
        """更新現有預測記錄"""
        
        # 保留舊的預測到歷史記錄
        old_prediction_data = {
            'old_predicted_home_score': prediction.predicted_home_score,
            'old_predicted_away_score': prediction.predicted_away_score,
            'old_confidence': prediction.confidence_level,
            'updated_reason': 'enhanced_analysis_upgrade'
        }
        
        # 更新主預測記錄
        prediction.predicted_home_score = history_entry['predicted_home_score']
        prediction.predicted_away_score = history_entry['predicted_away_score']
        prediction.predicted_total_runs = history_entry['predicted_total_runs']
        prediction.home_win_probability = history_entry['home_win_probability']
        prediction.away_win_probability = history_entry['away_win_probability']
        prediction.starting_pitcher_home = history_entry['starting_pitcher_home']
        prediction.starting_pitcher_away = history_entry['starting_pitcher_away']
        prediction.pitcher_matchup_advantage = history_entry['pitcher_matchup_advantage']
        prediction.confidence = history_entry['overall_confidence']  # 使用 confidence 而不是 confidence_level
        prediction.updated_at = datetime.now()
        
        # 更新詳細分析 (如果欄位存在)
        if hasattr(prediction, 'pitcher_vs_batter_analysis'):
            prediction.pitcher_vs_batter_analysis = history_entry['detailed_analysis']
        
        # 將更新信息添加到歷史條目
        history_entry.update(old_prediction_data)
    
    def _create_new_prediction(self, history_entry: Dict):
        """創建新的預測記錄"""
        
        new_prediction = Prediction(
            game_id=history_entry['game_id'],
            predicted_home_score=history_entry['predicted_home_score'],
            predicted_away_score=history_entry['predicted_away_score'],
            predicted_total_runs=history_entry['predicted_total_runs'],
            home_win_probability=history_entry['home_win_probability'],
            away_win_probability=history_entry['away_win_probability'],
            starting_pitcher_home=history_entry['starting_pitcher_home'],
            starting_pitcher_away=history_entry['starting_pitcher_away'],
            pitcher_matchup_advantage=history_entry['pitcher_matchup_advantage'],
            confidence=history_entry['overall_confidence'],  # 使用 confidence 而不是 confidence_level
            model_version='enhanced_bullpen_analysis',
            created_at=datetime.now()
        )
        
        # 如果存在投手vs打者分析欄位，添加詳細分析
        if hasattr(new_prediction, 'pitcher_vs_batter_analysis'):
            new_prediction.pitcher_vs_batter_analysis = history_entry['detailed_analysis']
        
        db.session.add(new_prediction)
    
    def _save_to_history_table(self, history_entry: Dict):
        """保存到預測歷史表"""
        
        # 檢查是否存在 PredictionHistory 表
        try:
            # 創建歷史記錄
            history_record = PredictionHistory(
                game_id=history_entry['game_id'],
                prediction_date=history_entry['prediction_date'],
                prediction_type=history_entry['prediction_type'],
                predicted_home_score=history_entry['predicted_home_score'],
                predicted_away_score=history_entry['predicted_away_score'],
                predicted_total_runs=history_entry['predicted_total_runs'],
                home_win_probability=history_entry['home_win_probability'],
                starting_pitcher_home=history_entry['starting_pitcher_home'],
                starting_pitcher_away=history_entry['starting_pitcher_away'],
                confidence_level=history_entry['confidence_level'],
                overall_confidence=history_entry['overall_confidence'],
                detailed_analysis=history_entry['detailed_analysis'],
                created_at=history_entry['created_at']
            )
            
            db.session.add(history_record)
            
        except Exception as e:
            logger.warning(f"⚠️ 預測歷史表不存在或無法訪問，跳過歷史記錄: {e}")
    
    def _cleanup_old_predictions(self, game_id: str):
        """清理該比賽的舊預測記錄"""
        
        try:
            # 保留最新的N條記錄，刪除更舊的
            cutoff_date = datetime.now() - timedelta(days=self.storage_retention_days)
            
            # 查詢該比賽的歷史預測，按日期排序
            old_predictions = db.session.execute(text("""
                SELECT id FROM prediction_history 
                WHERE game_id = :game_id 
                AND created_at < :cutoff_date
                ORDER BY created_at DESC
                OFFSET :keep_count
            """), {
                'game_id': game_id,
                'cutoff_date': cutoff_date,
                'keep_count': self.max_predictions_per_game
            }).fetchall()
            
            if old_predictions:
                old_ids = [str(row[0]) for row in old_predictions]
                db.session.execute(text(f"""
                    DELETE FROM prediction_history 
                    WHERE id IN ({','.join(old_ids)})
                """))
                
                logger.info(f"🧹 清理了 {len(old_ids)} 條舊預測記錄 for {game_id}")
                
        except Exception as e:
            logger.warning(f"⚠️ 清理舊預測記錄時出錯: {e}")
    
    def get_prediction_history(self, 
                              game_id: Optional[str] = None,
                              start_date: Optional[date] = None,
                              end_date: Optional[date] = None,
                              limit: int = 50) -> List[Dict]:
        """
        獲取預測歷史記錄
        
        Args:
            game_id: 特定比賽ID (可選)
            start_date: 開始日期 (可選)
            end_date: 結束日期 (可選) 
            limit: 返回記錄數限制
            
        Returns:
            預測歷史記錄列表
        """
        
        try:
            query = db.session.query(PredictionHistory)
            
            # 添加篩選條件
            if game_id:
                query = query.filter(PredictionHistory.game_id == game_id)
            
            if start_date:
                query = query.filter(PredictionHistory.prediction_date >= start_date)
                
            if end_date:
                query = query.filter(PredictionHistory.prediction_date <= end_date)
            
            # 按預測日期降序排列
            predictions = query.order_by(
                PredictionHistory.prediction_date.desc()
            ).limit(limit).all()
            
            # 轉換為字典格式
            history_list = []
            for pred in predictions:
                history_item = {
                    'id': pred.id,
                    'game_id': pred.game_id,
                    'prediction_date': pred.prediction_date.isoformat(),
                    'prediction_type': pred.prediction_type,
                    'predicted_home_score': pred.predicted_home_score,
                    'predicted_away_score': pred.predicted_away_score,
                    'predicted_total_runs': pred.predicted_total_runs,
                    'home_win_probability': pred.home_win_probability,
                    'starting_pitcher_home': pred.starting_pitcher_home,
                    'starting_pitcher_away': pred.starting_pitcher_away,
                    'confidence_level': pred.confidence_level,
                    'overall_confidence': pred.overall_confidence,
                    'created_at': pred.created_at.isoformat()
                }
                
                # 解析詳細分析數據
                if pred.detailed_analysis:
                    try:
                        history_item['detailed_analysis'] = json.loads(pred.detailed_analysis)
                    except:
                        history_item['detailed_analysis'] = {}
                
                history_list.append(history_item)
            
            logger.info(f"📚 查詢預測歷史: 找到 {len(history_list)} 條記錄")
            return history_list
            
        except Exception as e:
            logger.error(f"❌ 查詢預測歷史失敗: {e}")
            return []
    
    def get_game_prediction_evolution(self, game_id: str) -> Dict:
        """
        獲取特定比賽的預測演進歷史
        
        Args:
            game_id: 比賽ID
            
        Returns:
            該比賽的預測演進分析
        """
        
        try:
            # 獲取該比賽的所有預測記錄
            predictions = self.get_prediction_history(game_id=game_id)
            
            if not predictions:
                return {'game_id': game_id, 'evolution': [], 'summary': 'No predictions found'}
            
            # 分析預測演進
            evolution_analysis = {
                'game_id': game_id,
                'prediction_count': len(predictions),
                'first_prediction': predictions[-1]['prediction_date'],  # 最早的
                'latest_prediction': predictions[0]['prediction_date'],   # 最新的
                'evolution': predictions,
                'summary': self._analyze_prediction_changes(predictions)
            }
            
            logger.info(f"📈 分析預測演進: {game_id} - {len(predictions)} 次預測")
            return evolution_analysis
            
        except Exception as e:
            logger.error(f"❌ 分析預測演進失敗 {game_id}: {e}")
            return {'game_id': game_id, 'error': str(e)}
    
    def _analyze_prediction_changes(self, predictions: List[Dict]) -> Dict:
        """分析預測變化趨勢"""
        
        if len(predictions) < 2:
            return {'status': 'insufficient_data'}
        
        # 比較第一次和最新預測
        first = predictions[-1]  # 最早的預測
        latest = predictions[0]  # 最新的預測
        
        home_score_change = latest['predicted_home_score'] - first['predicted_home_score']
        away_score_change = latest['predicted_away_score'] - first['predicted_away_score']
        total_runs_change = latest['predicted_total_runs'] - first['predicted_total_runs']
        confidence_change = latest['overall_confidence'] - first['overall_confidence']
        
        return {
            'total_predictions': len(predictions),
            'prediction_span_hours': (
                datetime.fromisoformat(latest['prediction_date']) - 
                datetime.fromisoformat(first['prediction_date'])
            ).total_seconds() / 3600,
            'score_changes': {
                'home_score_change': round(home_score_change, 1),
                'away_score_change': round(away_score_change, 1),
                'total_runs_change': round(total_runs_change, 1)
            },
            'confidence_change': round(confidence_change, 3),
            'prediction_stability': 'stable' if abs(total_runs_change) < 1.0 else 'volatile',
            'improvement_trend': 'improving' if confidence_change > 0.05 else 'stable' if abs(confidence_change) < 0.05 else 'declining'
        }
    
    def get_daily_prediction_summary(self, target_date: date) -> Dict:
        """
        獲取指定日期的預測摘要
        
        Args:
            target_date: 目標日期
            
        Returns:
            當日預測摘要
        """
        
        try:
            # 查詢當日的所有預測
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = datetime.combine(target_date, datetime.max.time())
            
            daily_predictions = db.session.query(PredictionHistory).filter(
                and_(
                    PredictionHistory.prediction_date >= start_datetime,
                    PredictionHistory.prediction_date <= end_datetime
                )
            ).all()
            
            if not daily_predictions:
                return {
                    'date': target_date.isoformat(),
                    'total_predictions': 0,
                    'summary': 'No predictions found for this date'
                }
            
            # 統計分析
            prediction_types = {}
            confidence_levels = {'high': 0, 'medium': 0, 'low': 0}
            total_games = len(set(p.game_id for p in daily_predictions))
            
            for pred in daily_predictions:
                # 預測類型統計
                pred_type = pred.prediction_type
                prediction_types[pred_type] = prediction_types.get(pred_type, 0) + 1
                
                # 信心度統計
                conf_level = pred.confidence_level or 'medium'
                if conf_level in confidence_levels:
                    confidence_levels[conf_level] += 1
            
            return {
                'date': target_date.isoformat(),
                'total_predictions': len(daily_predictions),
                'unique_games': total_games,
                'prediction_types': prediction_types,
                'confidence_distribution': confidence_levels,
                'average_confidence': round(
                    sum(p.overall_confidence or 0.5 for p in daily_predictions) / len(daily_predictions), 3
                )
            }
            
        except Exception as e:
            logger.error(f"❌ 獲取日期預測摘要失敗 {target_date}: {e}")
            return {'date': target_date.isoformat(), 'error': str(e)}

# 使用示例
if __name__ == "__main__":
    import asyncio
    from models.unified_prediction_engine import predict_game
    from app import create_app
    
    async def test_prediction_history():
        app = create_app('development')
        
        with app.app_context():
            manager = PredictionHistoryManager()
            
            # 測試生成預測並保存歷史
            test_game_id = "20250823_BOS_NYY_history_test"
            prediction_result = await predict_game(
                game_id=test_game_id,
                target_date=date(2025, 8, 23)
            )
            
            if prediction_result.get('success'):
                # 保存到歷史
                success = manager.save_enhanced_prediction(
                    game_id=test_game_id,
                    prediction_result=prediction_result,
                    prediction_type="enhanced_with_bullpen"
                )
                
                print(f"保存歷史記錄: {'成功' if success else '失敗'}")
                
                # 查詢歷史記錄
                history = manager.get_prediction_history(game_id=test_game_id)
                print(f"歷史記錄數量: {len(history)}")
                
                # 分析預測演進
                evolution = manager.get_game_prediction_evolution(test_game_id)
                print(f"預測演進分析: {evolution.get('summary', {})}")
    
    asyncio.run(test_prediction_history())