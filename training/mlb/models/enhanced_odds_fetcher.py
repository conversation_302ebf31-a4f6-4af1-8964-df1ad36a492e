"""
增強版博彩賠率獲取器 - 整合多個 API 源
支援 oddsapi 包、直接 API 調用、以及自動切換機制
"""

import logging
import asyncio
from datetime import datetime, date
from typing import Dict, List, Optional, Any

# 設置 logger
logger = logging.getLogger(__name__)

# 嘗試導入 oddsapi 包
try:
    from oddsapi import OddsApiClient
    ODDSAPI_AVAILABLE = True
    logger.info("oddsapi 包導入成功")
except ImportError as e:
    ODDSAPI_AVAILABLE = False
    logger.warning(f"oddsapi 包未安裝或導入失敗: {e}，將使用直接 API 調用")
except Exception as e:
    ODDSAPI_AVAILABLE = False
    logger.warning(f"oddsapi 包導入錯誤: {e}，將使用直接 API 調用")

from .multi_api_manager import MultiAPIManager, APIStatus
from .real_betting_odds_fetcher import RealBettingOddsFetcher

class EnhancedOddsFetcher:
    """增強版博彩賠率獲取器"""
    
    def __init__(self):
        self.multi_api_manager = MultiAPIManager()
        self.fallback_fetcher = RealBettingOddsFetcher()
        self.oddsapi_client = None
        
        # 初始化 oddsapi 客戶端
        if ODDSAPI_AVAILABLE:
            self._init_oddsapi_client()
    
    def _init_oddsapi_client(self):
        """初始化 oddsapi 客戶端"""
        try:
            api_key = self._load_odds_api_key()
            if api_key:
                self.oddsapi_client = OddsApiClient(api_key=api_key)
                logger.info("oddsapi 客戶端初始化成功")
            else:
                logger.warning("未找到 odds API 密鑰，無法初始化 oddsapi 客戶端")
        except Exception as e:
            logger.error(f"初始化 oddsapi 客戶端失敗: {e}")
    
    def _load_odds_api_key(self) -> Optional[str]:
        """載入 The Odds API 密鑰"""
        try:
            with open('models/odds-api.txt', 'r') as f:
                return f.read().strip()
        except FileNotFoundError:
            logger.warning("未找到 odds-api.txt 文件")
            return None
        except Exception as e:
            logger.error(f"讀取 odds API 密鑰失敗: {e}")
            return None
    
    def get_mlb_odds_today(self, target_date: date = None) -> Dict[str, Any]:
        """獲取 MLB 今日賠率 - 多 API 自動切換"""
        if not target_date:
            target_date = date.today()
        
        logger.info(f"開始獲取 {target_date} 的 MLB 賠率數據...")
        
        # 方法 1: 嘗試使用 oddsapi 包
        if self.oddsapi_client:
            try:
                result = self._fetch_with_oddsapi(target_date)
                if result and result.get('success'):
                    logger.info("✅ 使用 oddsapi 包成功獲取數據")
                    return result
            except Exception as e:
                logger.warning(f"oddsapi 包獲取失敗: {e}")
        
        # 方法 2: 使用多 API 管理器
        try:
            result = self.multi_api_manager.fetch_mlb_odds(target_date)
            if result and result.get('success'):
                logger.info("✅ 使用多 API 管理器成功獲取數據")
                return result
        except Exception as e:
            logger.warning(f"多 API 管理器獲取失敗: {e}")
        
        # 方法 3: 使用原有的備用獲取器
        try:
            result = self.fallback_fetcher.get_mlb_odds_today(target_date)
            if result and result.get('success'):
                logger.info("✅ 使用備用獲取器成功獲取數據")
                return result
        except Exception as e:
            logger.warning(f"備用獲取器獲取失敗: {e}")
        
        # 所有方法都失敗
        logger.error("❌ 所有賠率獲取方法都失敗")
        return {
            "success": False,
            "error": "所有賠率獲取方法都失敗",
            "timestamp": datetime.now().isoformat()
        }
    
    def _fetch_with_oddsapi(self, target_date: date) -> Dict[str, Any]:
        """使用 oddsapi 包獲取數據"""
        try:
            logger.info("使用 oddsapi 包獲取 MLB 賠率...")
            
            # 獲取 MLB 賠率
            response = self.oddsapi_client.retrieve_odds(
                sport_key='baseball_mlb',
                region='us',
                mkt='h2h,spreads,totals',
                odds_format='decimal',
                date_format='iso'
            )
            
            if not response or not hasattr(response, 'data'):
                return {"success": False, "error": "oddsapi 返回空數據"}
            
            # 轉換數據格式
            games_data = []
            for game in response.data:
                game_info = {
                    "id": getattr(game, 'id', ''),
                    "sport_key": getattr(game, 'sport_key', ''),
                    "sport_title": getattr(game, 'sport_title', ''),
                    "commence_time": getattr(game, 'commence_time', ''),
                    "home_team": getattr(game, 'home_team', ''),
                    "away_team": getattr(game, 'away_team', ''),
                    "bookmakers": []
                }
                
                # 處理博彩公司數據
                if hasattr(game, 'bookmakers'):
                    for bookmaker in game.bookmakers:
                        bookmaker_info = {
                            "key": getattr(bookmaker, 'key', ''),
                            "title": getattr(bookmaker, 'title', ''),
                            "last_update": getattr(bookmaker, 'last_update', ''),
                            "markets": []
                        }
                        
                        # 處理市場數據
                        if hasattr(bookmaker, 'markets'):
                            for market in bookmaker.markets:
                                market_info = {
                                    "key": getattr(market, 'key', ''),
                                    "outcomes": []
                                }
                                
                                # 處理結果數據
                                if hasattr(market, 'outcomes'):
                                    for outcome in market.outcomes:
                                        outcome_info = {
                                            "name": getattr(outcome, 'name', ''),
                                            "price": getattr(outcome, 'price', 0),
                                            "point": getattr(outcome, 'point', None)
                                        }
                                        market_info["outcomes"].append(outcome_info)
                                
                                bookmaker_info["markets"].append(market_info)
                        
                        game_info["bookmakers"].append(bookmaker_info)
                
                games_data.append(game_info)
            
            # 獲取 API 使用統計
            rate_info = {}
            if hasattr(response, 'rate_info'):
                rate_info = {
                    "requests_remaining": getattr(response.rate_info, 'requests_remaining', 'N/A'),
                    "requests_used": getattr(response.rate_info, 'requests_used', 'N/A')
                }
            
            return {
                "success": True,
                "source": "oddsapi_package",
                "games": games_data,
                "rate_info": rate_info,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"oddsapi 包獲取數據失敗: {e}")
            return {"success": False, "error": str(e)}
    
    async def fetch_odds_async(self, target_date: date = None) -> Dict[str, Any]:
        """異步獲取賠率數據"""
        if not target_date:
            target_date = date.today()
        
        # 如果 oddsapi 支援異步，使用異步方式
        if self.oddsapi_client and hasattr(self.oddsapi_client, 'aio'):
            try:
                self.oddsapi_client.aio = True
                
                # 創建異步任務
                tasks = [
                    self._fetch_odds_async_task('h2h'),
                    self._fetch_odds_async_task('spreads'),
                    self._fetch_odds_async_task('totals')
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 合併結果
                combined_data = self._combine_async_results(results)
                return combined_data
                
            except Exception as e:
                logger.error(f"異步獲取失敗: {e}")
        
        # 回退到同步方式
        return self.get_mlb_odds_today(target_date)
    
    async def _fetch_odds_async_task(self, market: str):
        """異步獲取特定市場的賠率"""
        try:
            response = await self.oddsapi_client.retrieve_odds(
                sport_key='baseball_mlb',
                region='us',
                mkt=market,
                odds_format='decimal'
            )
            return {"market": market, "data": response, "success": True}
        except Exception as e:
            return {"market": market, "error": str(e), "success": False}
    
    def _combine_async_results(self, results: List[Any]) -> Dict[str, Any]:
        """合併異步結果"""
        combined = {
            "success": True,
            "source": "oddsapi_async",
            "markets": {},
            "timestamp": datetime.now().isoformat()
        }
        
        for result in results:
            if isinstance(result, dict) and result.get('success'):
                market = result.get('market')
                combined["markets"][market] = result.get('data')
            elif isinstance(result, Exception):
                logger.error(f"異步任務失敗: {result}")
        
        return combined
    
    def get_api_status_report(self) -> Dict[str, Any]:
        """獲取所有 API 的狀態報告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "oddsapi_package": {
                "available": ODDSAPI_AVAILABLE,
                "client_initialized": self.oddsapi_client is not None
            },
            "multi_api_manager": self.multi_api_manager.get_api_status(),
            "fallback_fetcher": {
                "available": True,
                "type": "direct_api_call"
            }
        }
        
        return report
    
    def install_oddsapi_package(self) -> Dict[str, Any]:
        """安裝 oddsapi 包的指導"""
        return {
            "message": "要安裝 oddsapi 包，請執行以下命令：",
            "commands": [
                "pip install oddsapi",
                "# 或者在虛擬環境中：",
                "source .venv/bin/activate && pip install oddsapi"
            ],
            "documentation": "https://pypi.org/project/oddsapi/",
            "api_key_setup": "請在 models/odds-api.txt 文件中添加您的 The Odds API 密鑰"
        }
