#!/usr/bin/env python3
"""
從BoxScore中提取投手信息的工具
解決Player表關聯問題，直接從PlayerGameStats獲取投手數據
"""

import logging
from datetime import date
from typing import Dict, Optional, List

logger = logging.getLogger(__name__)

class BoxScorePitcherExtractor:
    """從BoxScore中提取投手信息的工具類"""
    
    def __init__(self):
        pass
    
    def get_starting_pitcher_from_boxscore(self, game_id: str, team: str, is_home: bool = True) -> Optional[Dict]:
        """
        從BoxScore中獲取先發投手信息
        
        Args:
            game_id: 比賽ID
            team: 球隊代碼
            is_home: 是否為主隊
            
        Returns:
            投手信息字典或None
        """
        try:
            from models.database import db, PlayerGameStats, Team
            
            # 獲取球隊ID
            team_obj = Team.query.filter_by(team_code=team).first()
            if not team_obj:
                logger.warning(f"⚠️ 找不到球隊: {team}")
                return None
            
            # 查找該比賽中該球隊的投手，按投球局數排序
            pitchers = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.team_id == team_obj.team_id,
                PlayerGameStats.position == 'P',
                PlayerGameStats.innings_pitched > 0
            ).order_by(PlayerGameStats.innings_pitched.desc()).all()
            
            if not pitchers:
                logger.warning(f"⚠️ 在比賽 {game_id} 中找不到 {team} 的投手統計")
                return None
            
            # 取第一個投手（投球局數最多的，通常是先發投手）
            starting_pitcher = pitchers[0]
            
            # 計算該場比賽的ERA
            if starting_pitcher.innings_pitched > 0:
                game_era = (starting_pitcher.earned_runs * 9.0) / starting_pitcher.innings_pitched
            else:
                game_era = 0.0
            
            # 使用比賽ERA或默認值
            era = game_era if game_era > 0 else 4.50
            quality = self._calculate_pitcher_quality_from_era(era)
            
            # 生成投手名稱（使用player_id作為標識）
            pitcher_name = f"投手#{starting_pitcher.player_id}"
            
            logger.info(f"✅ 從BoxScore找到 {team} 先發投手: {pitcher_name} "
                       f"(ERA: {era:.2f}, 投球局數: {starting_pitcher.innings_pitched})")
            
            return {
                'name': pitcher_name,
                'era': era,
                'quality': quality,
                'innings_pitched': starting_pitcher.innings_pitched,
                'earned_runs': starting_pitcher.earned_runs,
                'player_id': starting_pitcher.player_id
            }
            
        except Exception as e:
            logger.warning(f"⚠️ 從BoxScore獲取先發投手失敗: {e}")
            return None
    
    def get_all_pitchers_from_boxscore(self, game_id: str, team: str) -> List[Dict]:
        """
        獲取該比賽中該球隊的所有投手信息
        
        Args:
            game_id: 比賽ID
            team: 球隊代碼
            
        Returns:
            投手信息列表
        """
        try:
            from models.database import db, PlayerGameStats, Team
            
            # 獲取球隊ID
            team_obj = Team.query.filter_by(team_code=team).first()
            if not team_obj:
                return []
            
            # 查找該比賽中該球隊的所有投手
            pitchers = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.team_id == team_obj.team_id,
                PlayerGameStats.position == 'P',
                PlayerGameStats.innings_pitched > 0
            ).order_by(PlayerGameStats.innings_pitched.desc()).all()
            
            pitcher_list = []
            for pitcher in pitchers:
                # 計算ERA
                if pitcher.innings_pitched > 0:
                    era = (pitcher.earned_runs * 9.0) / pitcher.innings_pitched
                else:
                    era = 0.0
                
                pitcher_info = {
                    'name': f"投手#{pitcher.player_id}",
                    'era': era,
                    'quality': self._calculate_pitcher_quality_from_era(era),
                    'innings_pitched': pitcher.innings_pitched,
                    'earned_runs': pitcher.earned_runs,
                    'hits_allowed': pitcher.hits_allowed,
                    'walks_allowed': pitcher.walks_allowed,
                    'strikeouts_pitched': pitcher.strikeouts_pitched,
                    'player_id': pitcher.player_id
                }
                pitcher_list.append(pitcher_info)
            
            return pitcher_list
            
        except Exception as e:
            logger.warning(f"⚠️ 獲取所有投手失敗: {e}")
            return []
    
    def analyze_pitcher_matchup(self, game_id: str, home_team: str, away_team: str) -> Dict:
        """
        分析投手對戰情況
        
        Args:
            game_id: 比賽ID
            home_team: 主隊代碼
            away_team: 客隊代碼
            
        Returns:
            投手對戰分析結果
        """
        try:
            # 獲取主隊先發投手
            home_pitcher = self.get_starting_pitcher_from_boxscore(game_id, home_team, is_home=True)
            
            # 獲取客隊先發投手
            away_pitcher = self.get_starting_pitcher_from_boxscore(game_id, away_team, is_home=False)
            
            # 如果找不到投手，使用默認值
            if not home_pitcher:
                home_pitcher = {
                    'name': f'{home_team} 投手',
                    'era': 4.50,
                    'quality': 50.0,
                    'strength': '普通'
                }
            else:
                home_pitcher['strength'] = self._classify_pitcher_strength(home_pitcher['era'], home_pitcher['quality'])
            
            if not away_pitcher:
                away_pitcher = {
                    'name': f'{away_team} 投手',
                    'era': 4.50,
                    'quality': 50.0,
                    'strength': '普通'
                }
            else:
                away_pitcher['strength'] = self._classify_pitcher_strength(away_pitcher['era'], away_pitcher['quality'])
            
            # 確定對戰類型
            matchup_type = self._determine_matchup_type(home_pitcher['strength'], away_pitcher['strength'])
            
            analysis = {
                'home_pitcher': home_pitcher,
                'away_pitcher': away_pitcher,
                'matchup_type': matchup_type,
                'average_era': (home_pitcher['era'] + away_pitcher['era']) / 2,
                'average_quality': (home_pitcher['quality'] + away_pitcher['quality']) / 2
            }
            
            logger.info(f"📊 BoxScore投手分析: {home_pitcher['name']}({home_pitcher['strength']}) vs "
                       f"{away_pitcher['name']}({away_pitcher['strength']}) - {matchup_type}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 投手對戰分析失敗: {e}")
            # 返回默認分析
            return {
                'home_pitcher': {'name': f'{home_team} 投手', 'era': 4.50, 'quality': 50.0, 'strength': '普通'},
                'away_pitcher': {'name': f'{away_team} 投手', 'era': 4.50, 'quality': 50.0, 'strength': '普通'},
                'matchup_type': '普通對戰',
                'average_era': 4.50,
                'average_quality': 50.0
            }
    
    def _calculate_pitcher_quality_from_era(self, era: float) -> float:
        """根據ERA計算投手質量分數"""
        return max(0, 100 - (era - 2.0) * 20)
    
    def _classify_pitcher_strength(self, era: float, quality: float) -> str:
        """分類投手強度"""
        if era <= 2.50 and quality >= 80:
            return "王牌"
        elif era <= 3.50 and quality >= 70:
            return "優秀"
        elif era <= 4.50 and quality >= 50:
            return "普通"
        else:
            return "弱勢"
    
    def _determine_matchup_type(self, home_strength: str, away_strength: str) -> str:
        """確定對戰類型"""
        strength_order = {'王牌': 4, '優秀': 3, '普通': 2, '弱勢': 1}
        
        home_level = strength_order.get(home_strength, 2)
        away_level = strength_order.get(away_strength, 2)
        
        if home_level >= 3 and away_level >= 3:
            return "王牌對決"
        elif home_level <= 1 and away_level <= 1:
            return "打擊戰"
        elif abs(home_level - away_level) >= 2:
            return "強弱對戰"
        else:
            return "普通對戰"
    
    def get_pitcher_stats_summary(self, game_id: str) -> Dict:
        """
        獲取比賽投手統計摘要
        
        Args:
            game_id: 比賽ID
            
        Returns:
            投手統計摘要
        """
        try:
            from models.database import db, PlayerGameStats, Game
            
            # 獲取比賽信息
            game = Game.query.filter_by(game_id=game_id).first()
            if not game:
                return {}
            
            # 獲取主隊投手
            home_pitchers = self.get_all_pitchers_from_boxscore(game_id, game.home_team)
            
            # 獲取客隊投手
            away_pitchers = self.get_all_pitchers_from_boxscore(game_id, game.away_team)
            
            summary = {
                'game_id': game_id,
                'home_team': game.home_team,
                'away_team': game.away_team,
                'date': game.date.isoformat() if game.date else None,
                'home_pitchers': home_pitchers,
                'away_pitchers': away_pitchers,
                'home_starting_pitcher': home_pitchers[0] if home_pitchers else None,
                'away_starting_pitcher': away_pitchers[0] if away_pitchers else None,
                'total_pitchers': len(home_pitchers) + len(away_pitchers)
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ 獲取投手統計摘要失敗: {e}")
            return {}

# 創建全局實例
boxscore_pitcher_extractor = BoxScorePitcherExtractor()
