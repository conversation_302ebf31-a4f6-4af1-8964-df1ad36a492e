"""
改進的MLB預測器 - 使用增強特徵工程和高級集成策略
目標：將準確率從50%提升至65%+
增強版本：集成高級特徵選擇和動態權重優化
"""

import logging
import numpy as np
import pandas as pd
from datetime import date, timed<PERSON>ta
from typing import Dict, List, Optional
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, accuracy_score, mean_squared_error, r2_score
import xgboost as xgb
import joblib
import os

from .enhanced_feature_engineer import EnhancedFeatureEngineer
from .advanced_feature_selector import AdvancedFeatureSelector
from .advanced_ensemble_strategy import AdvancedEnsembleStrategy
from .database import db, Game

logger = logging.getLogger(__name__)

class ImprovedMLBPredictor:
    """改進的MLB預測器"""
    
    def __init__(self):
        self.models = {
            'home_score': None,
            'away_score': None, 
            'win_probability': None
        }
        self.ensemble_models = {
            'xgboost': xgb.XGBRegressor(random_state=42),
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boost': GradientBoostingRegressor(random_state=42)
        }
        self.meta_model = LinearRegression()
        self.scalers = {}
        # 高級組件
        self.feature_engineer = EnhancedFeatureEngineer()
        self.feature_selector = AdvancedFeatureSelector()
        self.ensemble_strategy = AdvancedEnsembleStrategy()

        # 特徵和模型管理
        self.feature_columns = {}  # 每個目標變量的特徵列
        self.selected_features = {}  # 每個目標變量的選定特徵

        # 性能追蹤
        self.feature_importance_analysis = {}
        self.ensemble_performance = {}
        self.model_performance = {}

        # 訓練狀態
        self.is_trained = False
        self.training_summary = {}

        # 嘗試載入已保存的模型
        try:
            self.load_models()
            if any(model is not None for model in self.models.values()):
                self.is_trained = True
                logger.info("✅ 已載入保存的高級模型")
        except Exception as e:
            logger.info(f"未找到保存的高級模型: {e}")

    def train_advanced_ensemble_models(self, start_date: date = None, end_date: date = None) -> Dict:
        """訓練高級集成模型（包含特徵選擇和動態權重優化）"""
        logger.info("開始訓練高級集成模型...")

        try:
            # 1. 準備訓練數據
            training_data = self._prepare_advanced_training_data(start_date, end_date)
            if training_data.empty:
                raise ValueError("無可用的訓練數據")

            logger.info(f"準備了 {len(training_data)} 場比賽的訓練數據")

            # 2. 提取增強特徵
            features_df = self.extract_enhanced_features(training_data)
            if features_df.empty:
                raise ValueError("特徵提取失敗")

            logger.info(f"提取了 {len(features_df.columns)} 個特徵")

            # 3. 為每個目標變量進行特徵選擇和模型訓練
            targets = ['home_score', 'away_score', 'home_win']
            training_results = {}

            for target in targets:
                logger.info(f"\n=== 訓練 {target} 模型 ===")

                # 準備目標變量數據
                target_data = features_df.dropna(subset=[target])
                if len(target_data) < 100:
                    logger.warning(f"{target} 數據不足，跳過訓練")
                    continue

                # 分離特徵和目標
                feature_cols = [col for col in target_data.columns if col not in targets]
                X = target_data[feature_cols]
                y = target_data[target]

                # 特徵重要性分析
                logger.info(f"分析 {target} 的特徵重要性...")
                importance_analysis = self.feature_selector.analyze_feature_importance(X, y, target)

                # 選擇最優特徵
                selected_features = self.feature_selector.select_optimal_features(
                    target, top_k=30, min_confidence=0.3
                )

                if not selected_features:
                    logger.warning(f"{target} 沒有選擇到合適的特徵")
                    continue

                # 使用選定特徵訓練集成模型
                X_selected = X[selected_features]

                # 標準化特徵
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X_selected)

                # 訓練高級集成模型
                ensemble_results = self.ensemble_strategy.train_ensemble_with_optimization(
                    X_scaled, y.values, cv_splits=5
                )

                # 保存結果
                training_results[target] = {
                    'importance_analysis': importance_analysis,
                    'selected_features': selected_features,
                    'ensemble_results': ensemble_results,
                    'scaler': scaler,
                    'data_shape': X_selected.shape
                }

                # 保存到實例變量
                self.feature_importance_analysis[target] = importance_analysis
                self.selected_features[target] = selected_features
                self.scalers[target] = scaler
                self.models[target] = ensemble_results['final_models']
                self.ensemble_performance[target] = ensemble_results['ensemble_performance']

                logger.info(f"✅ {target} 模型訓練完成")
                logger.info(f"   選擇特徵數: {len(selected_features)}")
                logger.info(f"   集成性能: MAE={ensemble_results['ensemble_performance']['cv_mae']:.4f}")

            # 4. 設置特徵列（使用第一個目標的特徵列作為通用特徵列）
            first_target = list(training_results.keys())[0]
            self.feature_columns = training_results[first_target]['selected_features']

            # 5. 更新訓練狀態
            self.is_trained = True
            self.training_summary = {
                'training_date': date.today().isoformat(),
                'targets_trained': list(training_results.keys()),
                'total_games': len(training_data),
                'total_features_extracted': len(features_df.columns),
                'training_results': {
                    target: {
                        'selected_features_count': len(results['selected_features']),
                        'ensemble_mae': results['ensemble_results']['ensemble_performance']['cv_mae'],
                        'data_shape': results['data_shape']
                    }
                    for target, results in training_results.items()
                }
            }

            # 自動保存訓練好的模型
            try:
                self.save_models()
                logger.info("✅ 高級模型已自動保存")
            except Exception as save_error:
                logger.warning(f"模型保存失敗: {save_error}")

            # 更新訓練狀態
            self.is_trained = True

            logger.info("🎉 高級集成模型訓練完成！")
            return self.training_summary

        except Exception as e:
            logger.error(f"高級集成模型訓練失敗: {e}")
            raise

    def _prepare_advanced_training_data(self, start_date: date = None, end_date: date = None) -> pd.DataFrame:
        """準備高級訓練數據"""
        if start_date is None:
            start_date = date(2022, 1, 1)
        if end_date is None:
            end_date = date.today()

        # 獲取已完成的比賽
        games = Game.query.filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.game_status == 'completed',
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).order_by(Game.date).all()

        if not games:
            return pd.DataFrame()

        # 轉換為DataFrame
        games_data = []
        for game in games:
            games_data.append({
                'game_id': game.game_id,
                'date': game.date,
                'home_team': game.home_team,
                'away_team': game.away_team,
                'home_score': game.home_score,
                'away_score': game.away_score
            })

        return pd.DataFrame(games_data)

    def predict_game_advanced(self, home_team: str, away_team: str, game_date: date = None) -> Dict:
        """使用高級集成模型進行預測"""
        try:
            if game_date is None:
                game_date = date.today()

            # 檢查模型是否已訓練
            if not self.is_trained:
                raise ValueError("高級集成模型尚未訓練，請先調用 train_advanced_ensemble_models()")

            # 提取特徵
            features = self.feature_engineer.extract_comprehensive_features(
                home_team, away_team, game_date
            )

            if not features:
                raise ValueError("特徵提取失敗")

            # 為每個目標變量進行預測
            predictions = {}
            prediction_details = {}

            for target in ['home_score', 'away_score', 'home_win']:
                if target not in self.models or not self.models[target]:
                    logger.warning(f"{target} 模型未訓練，跳過預測")
                    continue

                # 獲取選定特徵 - 使用保存的特徵列表
                selected_features = self.selected_features.get(target, [])
                if not selected_features:
                    # 如果沒有特定目標的特徵，使用通用特徵列表
                    selected_features = self.feature_columns

                if not selected_features:
                    logger.warning(f"{target} 沒有選定特徵")
                    continue

                # 準備特徵向量 - 按照保存的特徵順序
                feature_vector = []
                for feature_name in selected_features:
                    feature_vector.append(features.get(feature_name, 0))

                if not feature_vector:
                    continue

                # 準備特徵數據
                X = np.array(feature_vector).reshape(1, -1)

                # 嘗試使用標準化器，如果失敗則使用原始特徵
                scaler = self.scalers.get(target) or self.scalers.get('features')
                if scaler is not None:
                    try:
                        X_scaled = scaler.transform(X)
                    except Exception as e:
                        logger.warning(f"{target} 標準化失敗，使用原始特徵: {e}")
                        X_scaled = X
                else:
                    logger.warning(f"{target} 沒有標準化器，使用原始特徵")
                    X_scaled = X

                # 使用集成策略進行預測
                # 檢查是否有權重，如果沒有則使用等權重
                if not self.ensemble_strategy.optimal_weights:
                    # 創建等權重
                    if isinstance(self.models[target], dict):
                        if 'base_models' in self.models[target]:
                            model_names = list(self.models[target]['base_models'].keys())
                        else:
                            model_names = list(self.models[target].keys())
                    else:
                        model_names = ['default']

                    equal_weight = 1.0 / len(model_names)
                    weights = {name: equal_weight for name in model_names}
                else:
                    weights = self.ensemble_strategy.optimal_weights

                # 嘗試集成預測，如果失敗則使用簡化預測
                try:
                    ensemble_result = self.ensemble_strategy.predict_with_ensemble(
                        X_scaled, self.models[target], weights
                    )

                    # 處理預測結果
                    if target in ['home_score', 'away_score']:
                        pred_value = max(0, round(ensemble_result['ensemble_prediction'][0], 1))
                    else:  # home_win
                        pred_value = max(0, min(1, ensemble_result['ensemble_prediction'][0]))

                    # 檢查是否為零值（表示集成預測失敗）
                    if pred_value == 0 and target in ['home_score', 'away_score']:
                        raise ValueError("集成預測返回零值")

                except Exception as e:
                    logger.warning(f"{target} 集成預測失敗，使用簡化預測: {e}")
                    # 使用簡化的預測邏輯
                    if target == 'home_score':
                        # 基於主隊平均得分和主場優勢
                        base_score = features.get('home_runs_avg_7d', 4.5)
                        home_advantage = features.get('home_field_advantage', 0.1)
                        pred_value = max(0, round(base_score * (1 + home_advantage), 1))
                    elif target == 'away_score':
                        # 基於客隊平均得分
                        pred_value = max(0, round(features.get('away_runs_avg_7d', 4.0), 1))
                    else:  # home_win
                        # 基於ELO評級
                        elo_prob = features.get('elo_win_probability', 0.5)
                        pred_value = max(0, min(1, elo_prob))

                predictions[target] = pred_value
                prediction_details[target] = {
                    'individual_predictions': ensemble_result['individual_predictions'],
                    'prediction_confidence': ensemble_result['prediction_confidence'][0],
                    'models_used': ensemble_result['models_used'],
                    'features_used': len(selected_features)
                }

            # 計算綜合信心度
            overall_confidence = self._calculate_advanced_confidence(
                predictions, prediction_details, features
            )

            # 構建最終預測結果
            result = {
                'home_team': home_team,
                'away_team': away_team,
                'game_date': game_date.isoformat(),
                'predicted_home_score': predictions.get('home_score', 0),
                'predicted_away_score': predictions.get('away_score', 0),
                'home_win_probability': predictions.get('home_win', 0.5),
                'away_win_probability': 1 - predictions.get('home_win', 0.5),
                'total_runs': predictions.get('home_score', 0) + predictions.get('away_score', 0),
                'confidence': overall_confidence,
                'model_type': 'advanced_ensemble',
                'prediction_details': prediction_details,
                'features_extracted': len(features),
                'targets_predicted': list(predictions.keys())
            }

            return result

        except Exception as e:
            logger.error(f"高級預測失敗 {away_team} @ {home_team}: {e}")
            raise

    def _calculate_advanced_confidence(self, predictions: Dict, prediction_details: Dict,
                                     features: Dict) -> float:
        """計算高級信心度"""
        try:
            confidence_factors = []

            # 1. 預測一致性（各模型預測的一致程度）
            if prediction_details:
                consistency_scores = []
                for target, details in prediction_details.items():
                    if 'prediction_confidence' in details:
                        consistency_scores.append(details['prediction_confidence'])

                if consistency_scores:
                    avg_consistency = np.mean(consistency_scores)
                    confidence_factors.append(avg_consistency)

            # 2. 特徵完整性
            feature_completeness = sum(1 for v in features.values() if v is not None and v != 0) / len(features)
            confidence_factors.append(feature_completeness)

            # 3. 模型覆蓋度（有多少個目標變量成功預測）
            model_coverage = len(predictions) / 3  # 總共3個目標變量
            confidence_factors.append(model_coverage)

            # 4. 特徵質量評估
            if 'elo_difference' in features:
                elo_diff = abs(features['elo_difference'])
                elo_confidence = min(elo_diff / 200, 1.0)
                confidence_factors.append(elo_confidence)

            if 'h2h_games_count' in features:
                h2h_confidence = min(features['h2h_games_count'] / 10, 1.0)
                confidence_factors.append(h2h_confidence)

            # 計算綜合信心度
            if confidence_factors:
                overall_confidence = np.mean(confidence_factors)
                # 應用非線性變換，提高區分度
                overall_confidence = 0.5 + 0.5 * np.tanh(2 * (overall_confidence - 0.5))
                return max(0.1, min(0.95, overall_confidence))
            else:
                return 0.5

        except Exception as e:
            logger.warning(f"信心度計算失敗: {e}")
            return 0.5

    def extract_enhanced_features(self, games_df: pd.DataFrame) -> pd.DataFrame:
        """使用增強特徵工程提取特徵"""
        features_list = []
        
        for _, game in games_df.iterrows():
            try:
                # 使用增強特徵工程器 - 傳遞game_id確保每場比賽獨立載入投手信息
                features = self.feature_engineer.extract_comprehensive_features(
                    game['home_team'], game['away_team'], game['date'], game.get('game_id')
                )
                
                if not features:
                    logger.warning(f"特徵提取失敗 - 比賽 {game.get('game_id', 'unknown')}")
                    continue
                
                # 添加目標變量（如果是訓練數據）
                if pd.notna(game.get('home_score')) and pd.notna(game.get('away_score')):
                    features.update({
                        'home_score': game['home_score'],
                        'away_score': game['away_score'],
                        'total_runs': game['home_score'] + game['away_score'],
                        'home_win': 1 if game['home_score'] > game['away_score'] else 0
                    })
                
                features_list.append(features)
                
            except Exception as e:
                logger.warning(f"提取增強特徵失敗 - 比賽 {game.get('game_id', 'unknown')}: {e}")
                continue
        
        return pd.DataFrame(features_list)
    
    def prepare_training_data(self, start_date: date = None, end_date: date = None) -> tuple:
        """準備訓練數據"""
        try:
            if start_date is None:
                start_date = date.today() - timedelta(days=365*2)  # 2年數據
            if end_date is None:
                end_date = date.today() - timedelta(days=1)
            
            # 獲取已完成的比賽
            games = Game.query.filter(
                Game.date >= start_date,
                Game.date <= end_date,
                Game.game_status == 'completed',
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).order_by(Game.date).all()
            
            if len(games) < 100:
                raise ValueError(f"訓練數據不足: 只有 {len(games)} 場比賽")
            
            logger.info(f"準備訓練數據: {len(games)} 場比賽 ({start_date} 到 {end_date})")
            
            # 轉換為DataFrame
            games_data = []
            for game in games:
                games_data.append({
                    'game_id': game.game_id,
                    'date': game.date,
                    'home_team': game.home_team,
                    'away_team': game.away_team,
                    'home_score': game.home_score,
                    'away_score': game.away_score
                })
            
            games_df = pd.DataFrame(games_data)
            
            # 使用增強特徵提取
            features_df = self.extract_enhanced_features(games_df)
            
            if features_df.empty:
                raise ValueError("增強特徵提取失敗")
            
            # 分離特徵和目標變量
            target_columns = ['home_score', 'away_score', 'total_runs', 'home_win']
            feature_columns = [col for col in features_df.columns if col not in target_columns]
            
            X = features_df[feature_columns]
            y = features_df[target_columns]
            
            self.feature_columns = feature_columns
            
            logger.info(f"增強訓練數據準備完成: {len(X)} 場比賽, {len(feature_columns)} 個特徵")
            return X, y
            
        except Exception as e:
            logger.error(f"準備增強訓練數據失敗: {e}")
            raise
    
    def train_ensemble_models(self, X: pd.DataFrame, y: pd.DataFrame) -> Dict:
        """訓練集成模型"""
        try:
            logger.info("開始訓練增強集成模型...")
            
            # 數據標準化 - 為每個目標創建獨立的scaler
            feature_scaler = StandardScaler()
            X_scaled = feature_scaler.fit_transform(X)

            # 保存特徵scaler
            self.scalers['features'] = feature_scaler
            
            results = {}
            
            # 為每個目標變量訓練集成模型
            for target in ['home_score', 'away_score', 'home_win']:
                logger.info(f"訓練 {target} 預測模型...")
                
                y_target = y[target]
                
                # 時間序列交叉驗證
                tscv = TimeSeriesSplit(n_splits=5)
                ensemble_predictions = []
                
                # 訓練基礎模型
                base_models = {}
                for model_name, model in self.ensemble_models.items():
                    model_copy = type(model)(**model.get_params())
                    model_copy.fit(X_scaled, y_target)
                    base_models[model_name] = model_copy
                
                # 生成元特徵用於集成
                meta_features = []
                meta_targets = []
                
                for train_idx, val_idx in tscv.split(X_scaled):
                    X_train, X_val = X_scaled[train_idx], X_scaled[val_idx]
                    y_train, y_val = y_target.iloc[train_idx], y_target.iloc[val_idx]
                    
                    val_predictions = []
                    for model_name, model in self.ensemble_models.items():
                        model_copy = type(model)(**model.get_params())
                        model_copy.fit(X_train, y_train)
                        pred = model_copy.predict(X_val)
                        val_predictions.append(pred)
                    
                    meta_features.extend(np.column_stack(val_predictions))
                    meta_targets.extend(y_val)
                
                # 訓練元模型
                meta_model = LinearRegression()
                meta_model.fit(meta_features, meta_targets)
                
                # 保存模型
                self.models[target] = {
                    'base_models': base_models,
                    'meta_model': meta_model
                }
                
                # 評估性能
                final_predictions = self._ensemble_predict(X_scaled, target)
                
                if target == 'home_win':
                    accuracy = accuracy_score(y_target, (final_predictions > 0.5).astype(int))
                    results[f'{target}_accuracy'] = accuracy
                    logger.info(f"{target} 準確率: {accuracy:.3f}")
                else:
                    mae = mean_absolute_error(y_target, final_predictions)
                    results[f'{target}_mae'] = mae
                    logger.info(f"{target} MAE: {mae:.3f}")
            
            self.model_performance = results
            logger.info("增強集成模型訓練完成")
            
            return results
            
        except Exception as e:
            logger.error(f"訓練增強集成模型失敗: {e}")
            raise
    
    def _ensemble_predict(self, X: np.ndarray, target: str) -> np.ndarray:
        """集成預測"""
        if target not in self.models or self.models[target] is None:
            raise ValueError(f"模型 {target} 尚未訓練")
        
        base_models = self.models[target]['base_models']
        meta_model = self.models[target]['meta_model']
        
        # 獲取基礎模型預測
        base_predictions = []
        for model_name, model in base_models.items():
            pred = model.predict(X)
            base_predictions.append(pred)
        
        # 元模型預測
        meta_features = np.column_stack(base_predictions)
        final_prediction = meta_model.predict(meta_features)
        
        return final_prediction
    
    def predict_game(self, home_team: str, away_team: str, game_date: date = None) -> Dict:
        """預測單場比賽（增強版）"""
        try:
            if game_date is None:
                game_date = date.today()
            
            # 檢查關鍵模型是否已訓練
            required_models = ['home_score', 'away_score', 'home_win']
            if not self.is_trained or not all(self.models.get(model) is not None for model in required_models):
                raise ValueError("增強模型尚未訓練，請先調用 train_ensemble_models()")
            
            # 使用增強特徵工程提取特徵 - 為單場預測生成虛擬game_id
            virtual_game_id = f"predict_{away_team}_{home_team}_{game_date.strftime('%Y%m%d')}"
            features = self.feature_engineer.extract_comprehensive_features(
                home_team, away_team, game_date, virtual_game_id
            )
            
            if not features:
                raise ValueError("增強特徵提取失敗")
            
            # 準備特徵向量
            feature_vector = []
            for col in self.feature_columns:
                feature_vector.append(features.get(col, 0))
            
            X = np.array(feature_vector).reshape(1, -1)

            # 檢查scaler是否存在
            if 'features' not in self.scalers or self.scalers['features'] is None:
                logger.warning("特徵標準化器未訓練，使用原始特徵")
                X_scaled = X
            else:
                X_scaled = self.scalers['features'].transform(X)
            
            # 集成預測
            home_score_pred = self._ensemble_predict(X_scaled, 'home_score')[0]
            away_score_pred = self._ensemble_predict(X_scaled, 'away_score')[0]
            home_win_prob = self._ensemble_predict(X_scaled, 'home_win')[0]
            
            # 確保預測值合理並驗證極端值
            home_score_pred = max(0, round(home_score_pred, 1))
            away_score_pred = max(0, round(away_score_pred, 1))
            home_win_prob = max(0, min(1, home_win_prob))

            # 驗證並修正極端預測
            home_score_pred, away_score_pred = self._validate_and_correct_predictions(
                home_score_pred, away_score_pred, home_team, away_team
            )

            # 計算信心度（基於特徵質量和模型一致性）
            confidence = self._calculate_enhanced_confidence(features, X_scaled)
            
            return {
                'home_team': home_team,
                'away_team': away_team,
                'predicted_home_score': home_score_pred,
                'predicted_away_score': away_score_pred,
                'home_win_probability': home_win_prob,
                'away_win_probability': 1 - home_win_prob,
                'confidence': confidence,
                'total_runs': home_score_pred + away_score_pred,
                'features_used': len(self.feature_columns),
                'model_type': 'enhanced_ensemble'
            }
            
        except Exception as e:
            logger.error(f"增強預測失敗 {away_team} @ {home_team}: {e}")
            raise

    def _validate_and_correct_predictions(self, home_score: float, away_score: float,
                                        home_team: str, away_team: str) -> tuple:
        """驗證並修正極端預測"""
        try:
            total_score = home_score + away_score

            # 檢查極端總分（超過20分或低於3分）
            if total_score > 20:
                logger.warning(f"檢測到極端高分預測 {away_team}@{home_team}: {away_score:.1f}-{home_score:.1f} (總分{total_score:.1f})")

                # 按比例縮放到合理範圍 (8-12分)
                scale_factor = 10.0 / total_score
                home_score = round(home_score * scale_factor, 1)
                away_score = round(away_score * scale_factor, 1)

                logger.info(f"修正後預測: {away_score:.1f}-{home_score:.1f} (總分{home_score + away_score:.1f})")

            elif total_score < 3:
                logger.warning(f"檢測到極端低分預測 {away_team}@{home_team}: {away_score:.1f}-{home_score:.1f} (總分{total_score:.1f})")

                # 調整到最低合理分數
                if home_score < 1.5:
                    home_score = 1.5
                if away_score < 1.5:
                    away_score = 1.5

                logger.info(f"修正後預測: {away_score:.1f}-{home_score:.1f} (總分{home_score + away_score:.1f})")

            # 檢查單隊極端分數（超過15分）
            if home_score > 15:
                logger.warning(f"檢測到主隊極端高分: {home_score:.1f}")
                home_score = min(home_score, 12.0)

            if away_score > 15:
                logger.warning(f"檢測到客隊極端高分: {away_score:.1f}")
                away_score = min(away_score, 12.0)

            return home_score, away_score

        except Exception as e:
            logger.error(f"預測驗證失敗: {e}")
            # 返回保守預測
            return 5.0, 4.5
    
    def _calculate_enhanced_confidence(self, features: Dict, X_scaled: np.ndarray) -> float:
        """計算增強信心度"""
        try:
            confidence_factors = []
            
            # 數據完整性
            feature_completeness = sum(1 for v in features.values() if v is not None and v != 0) / len(features)
            confidence_factors.append(feature_completeness)
            
            # ELO評分差異（如果有的話）
            if 'elo_difference' in features:
                elo_diff = abs(features['elo_difference'])
                elo_confidence = min(elo_diff / 200, 1.0)  # 200分差異為滿信心
                confidence_factors.append(elo_confidence)
            
            # 對戰歷史數據量
            if 'h2h_games_count' in features:
                h2h_confidence = min(features['h2h_games_count'] / 10, 1.0)
                confidence_factors.append(h2h_confidence)
            
            # 近期表現穩定性
            if 'home_recent_consistency' in features and 'away_recent_consistency' in features:
                consistency = (features['home_recent_consistency'] + features['away_recent_consistency']) / 2
                confidence_factors.append(consistency)
            
            # 基礎信心度
            base_confidence = 0.6
            
            # 加權平均
            if confidence_factors:
                enhanced_confidence = base_confidence + 0.3 * np.mean(confidence_factors)
            else:
                enhanced_confidence = base_confidence
            
            return min(enhanced_confidence, 0.95)  # 最高95%信心度
            
        except Exception as e:
            logger.warning(f"計算增強信心度失敗: {e}")
            return 0.6
    
    def save_models(self, model_dir: str = 'models/enhanced'):
        """保存增強模型"""
        try:
            os.makedirs(model_dir, exist_ok=True)
            
            # 保存集成模型
            for target, model_dict in self.models.items():
                if model_dict is not None:
                    joblib.dump(model_dict, f'{model_dir}/{target}_ensemble.pkl')
            
            # 保存標準化器和特徵列
            joblib.dump(self.scalers, f'{model_dir}/scalers.pkl')
            joblib.dump(self.feature_columns, f'{model_dir}/feature_columns.pkl')
            joblib.dump(self.model_performance, f'{model_dir}/performance.pkl')
            
            logger.info(f"增強模型保存完成: {model_dir}")
            
        except Exception as e:
            logger.error(f"保存增強模型失敗: {e}")
            raise
    
    def load_models(self, model_dir: str = 'models/enhanced'):
        """載入增強模型"""
        try:
            # 載入集成模型
            for target in ['home_score', 'away_score', 'home_win']:
                model_path = f'{model_dir}/{target}_ensemble.pkl'
                if os.path.exists(model_path):
                    self.models[target] = joblib.load(model_path)

            # 載入標準化器和特徵列
            self.scalers = joblib.load(f'{model_dir}/scalers.pkl')
            self.feature_columns = joblib.load(f'{model_dir}/feature_columns.pkl')
            self.model_performance = joblib.load(f'{model_dir}/performance.pkl')

            # 設置選定特徵（使用feature_columns作為所有目標的選定特徵）
            for target in ['home_score', 'away_score', 'home_win']:
                self.selected_features[target] = self.feature_columns

            # 設置訓練完成標誌
            self.is_trained = True

            logger.info(f"增強模型載入完成: {model_dir}")
            
        except Exception as e:
            logger.error(f"載入增強模型失敗: {e}")
            raise
