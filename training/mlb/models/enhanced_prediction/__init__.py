# Enhanced Prediction Module
# MLB 預測系統改善模組
# 目標：從 45.7% 提升至 80% 預測準確率

from .score_calibration import ScoreCalibrationModule
from .confidence_calculator import ConfidenceCalculator  
from .win_prediction import EnhancedWinPrediction
from .pitcher_analyzer import PitcherImpactAnalyzer
from .feature_weights import DynamicFeatureWeights
from .ensemble_model import EnsemblePredictionModel

__all__ = [
    'ScoreCalibrationModule',
    'ConfidenceCalculator', 
    'EnhancedWinPrediction',
    'PitcherImpactAnalyzer',
    'DynamicFeatureWeights',
    'EnsemblePredictionModel'
]

__version__ = "1.0.0"
__author__ = "Claude Code Enhancement Team"