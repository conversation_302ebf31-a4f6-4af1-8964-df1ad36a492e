"""
MLB 集成學習預測模型
機器學習模型升級 - 集成多個ML算法

目標：
- 結合 XGBoost、RandomForest、Neural Network 等算法
- 建立動態權重的集成系統
- 提高預測準確性和穩定性
- 實現時間序列特徵提取
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import joblib
from pathlib import Path

# Machine Learning imports
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier, VotingClassifier, VotingRegressor
from sklearn.linear_model import LinearRegression, LogisticRegression, Ridge, Lasso
from sklearn.neural_network import MLPRegressor, MLPClassifier
from sklearn.model_selection import train_test_split, cross_val_score, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, accuracy_score, precision_score, recall_score, f1_score
from sklearn.preprocessing import StandardScaler, RobustScaler
import xgboost as xgb
from collections import defaultdict

logger = logging.getLogger(__name__)

class EnsemblePredictionModel:
    """集成預測模型 - 多算法集成學習系統"""
    
    def __init__(self, model_dir: str = None):
        """
        初始化集成預測模型
        
        Args:
            model_dir: 模型存儲目錄
        """
        self.model_dir = Path(model_dir) if model_dir else Path('models/enhanced_ensemble')
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # 模型組件定義
        self.models = {
            # 回歸模型 (分數預測)
            'regression': {
                'xgboost': xgb.XGBRegressor(
                    n_estimators=200,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42
                ),
                'random_forest': RandomForestRegressor(
                    n_estimators=150,
                    max_depth=10,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42
                ),
                'neural_network': MLPRegressor(
                    hidden_layer_sizes=(100, 50, 25),
                    activation='relu',
                    solver='adam',
                    alpha=0.01,
                    learning_rate_init=0.001,
                    max_iter=500,
                    random_state=42
                ),
                'linear_ridge': Ridge(
                    alpha=1.0,
                    random_state=42
                ),
                'linear_lasso': Lasso(
                    alpha=0.1,
                    random_state=42
                )
            },
            
            # 分類模型 (勝負預測)
            'classification': {
                'xgboost': xgb.XGBClassifier(
                    n_estimators=200,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42
                ),
                'random_forest': RandomForestClassifier(
                    n_estimators=150,
                    max_depth=10,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42
                ),
                'neural_network': MLPClassifier(
                    hidden_layer_sizes=(100, 50),
                    activation='relu',
                    solver='adam',
                    alpha=0.01,
                    learning_rate_init=0.001,
                    max_iter=500,
                    random_state=42
                ),
                'logistic': LogisticRegression(
                    C=1.0,
                    random_state=42,
                    max_iter=1000
                )
            }
        }
        
        # 預處理器
        self.scalers = {
            'standard': StandardScaler(),
            'robust': RobustScaler()
        }
        
        # 模型權重 (基於驗證性能動態調整)
        self.model_weights = {
            'regression': {
                'xgboost': 0.3,
                'random_forest': 0.25,
                'neural_network': 0.2,
                'linear_ridge': 0.15,
                'linear_lasso': 0.1
            },
            'classification': {
                'xgboost': 0.35,
                'random_forest': 0.3,
                'neural_network': 0.2,
                'logistic': 0.15
            }
        }
        
        # 特徵列名
        self.feature_columns = []
        
        # 性能指標追蹤
        self.performance_metrics = {
            'regression': {
                'home_score': {'mae': 0, 'rmse': 0, 'r2': 0, 'samples': 0},
                'away_score': {'mae': 0, 'rmse': 0, 'r2': 0, 'samples': 0}
            },
            'classification': {
                'win_prediction': {'accuracy': 0, 'precision': 0, 'recall': 0, 'f1': 0, 'samples': 0}
            },
            'ensemble': {
                'overall_accuracy': 0,
                'confidence_calibration': 0,
                'prediction_consistency': 0
            }
        }
        
        # 模型狀態
        self.is_trained = False
        self.last_training_date = None
        self.training_data_size = 0
        
        # 時間序列特徵提取器
        self.time_series_extractor = TimeSeriesFeatureExtractor()
        
        logger.info("集成預測模型初始化完成")
    
    def train_ensemble(self, 
                      training_data: pd.DataFrame, 
                      target_columns: Dict[str, str],
                      validation_split: float = 0.2,
                      use_time_series: bool = True) -> Dict:
        """
        訓練集成模型
        
        Args:
            training_data: 訓練數據
            target_columns: 目標列映射 {'home_score': 'col1', 'away_score': 'col2', 'home_win': 'col3'}
            validation_split: 驗證集比例
            use_time_series: 是否使用時間序列特徵
            
        Returns:
            訓練結果報告
        """
        try:
            logger.info(f"開始訓練集成模型，數據量: {len(training_data)}")
            
            # 步驟1: 數據預處理
            X, y_dict = self._prepare_training_data(
                training_data, target_columns, use_time_series
            )
            
            if X is None or not y_dict:
                raise ValueError("數據預處理失敗")
            
            # 步驟2: 劃分訓練/驗證集
            split_result = self._split_training_data(X, y_dict, validation_split)
            X_train, X_val, y_train_dict, y_val_dict = split_result
            
            # 步驟3: 訓練回歸模型 (分數預測)
            regression_results = self._train_regression_models(
                X_train, X_val, y_train_dict, y_val_dict
            )
            
            # 步驟4: 訓練分類模型 (勝負預測)
            classification_results = self._train_classification_models(
                X_train, X_val, y_train_dict, y_val_dict
            )
            
            # 步驟5: 優化模型權重
            weight_optimization_results = self._optimize_model_weights(
                X_val, y_val_dict
            )
            
            # 步驟6: 評估集成效果
            ensemble_evaluation = self._evaluate_ensemble_performance(
                X_val, y_val_dict
            )
            
            # 步驟7: 保存模型
            save_results = self._save_ensemble_models()
            
            # 更新模型狀態
            self.is_trained = True
            self.last_training_date = datetime.now()
            self.training_data_size = len(training_data)
            
            # 生成訓練報告
            training_report = {
                'training_summary': {
                    'total_samples': len(training_data),
                    'training_samples': len(X_train),
                    'validation_samples': len(X_val),
                    'feature_count': len(self.feature_columns),
                    'training_time': datetime.now().isoformat()
                },
                'regression_results': regression_results,
                'classification_results': classification_results,
                'weight_optimization': weight_optimization_results,
                'ensemble_evaluation': ensemble_evaluation,
                'model_save_status': save_results,
                'performance_summary': self._generate_performance_summary()
            }
            
            logger.info("集成模型訓練完成")
            return training_report
            
        except Exception as e:
            logger.error(f"集成模型訓練失敗: {e}")
            return {'error': str(e), 'training_failed': True}
    
    def _prepare_training_data(self, 
                              data: pd.DataFrame, 
                              target_columns: Dict[str, str],
                              use_time_series: bool = True) -> Tuple[Optional[pd.DataFrame], Dict]:
        """準備訓練數據"""
        try:
            # 移除包含 NaN 的行
            clean_data = data.dropna()
            
            if len(clean_data) < 100:
                logger.error("清理後數據量不足")
                return None, {}
            
            logger.info(f"數據清理: {len(data)} -> {len(clean_data)} 樣本")
            
            # 分離特徵和目標
            target_vars = list(target_columns.values())
            feature_cols = [col for col in clean_data.columns if col not in target_vars]
            
            X = clean_data[feature_cols].copy()
            
            # 添加時間序列特徵
            if use_time_series and 'game_date' in clean_data.columns:
                ts_features = self.time_series_extractor.extract_features(clean_data)
                if ts_features is not None:
                    X = pd.concat([X, ts_features], axis=1)
                    logger.info(f"添加時間序列特徵: {ts_features.shape[1]} 個")
            
            # 準備目標變量
            y_dict = {}
            for target_name, col_name in target_columns.items():
                if col_name in clean_data.columns:
                    y_dict[target_name] = clean_data[col_name].copy()
            
            # 處理分類目標 (勝負預測)
            if 'home_win' in y_dict:
                # 基於分數創建勝負標籤
                if 'home_score' in y_dict and 'away_score' in y_dict:
                    y_dict['home_win'] = (y_dict['home_score'] > y_dict['away_score']).astype(int)
                else:
                    logger.warning("無法創建勝負標籤，缺少分數數據")
            
            # 特徵預處理
            X_processed = self._preprocess_features(X)
            
            # 記錄特徵列名
            self.feature_columns = list(X_processed.columns)
            
            logger.info(f"特徵準備完成: {X_processed.shape[1]} 個特徵")
            return X_processed, y_dict
            
        except Exception as e:
            logger.error(f"訓練數據準備失敗: {e}")
            return None, {}
    
    def _preprocess_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """預處理特徵"""
        try:
            X_processed = X.copy()
            
            # 處理無窮值
            X_processed = X_processed.replace([np.inf, -np.inf], np.nan)
            
            # 填充缺失值
            for col in X_processed.columns:
                if X_processed[col].dtype in ['int64', 'float64']:
                    # 數值特徵用中位數填充
                    X_processed[col] = X_processed[col].fillna(X_processed[col].median())
                else:
                    # 類別特徵用眾數填充
                    X_processed[col] = X_processed[col].fillna(X_processed[col].mode()[0] if len(X_processed[col].mode()) > 0 else 'unknown')
            
            # 編碼類別變量
            categorical_cols = X_processed.select_dtypes(include=['object']).columns
            for col in categorical_cols:
                X_processed[col] = pd.Categorical(X_processed[col]).codes
            
            # 標準化數值特徵
            numeric_cols = X_processed.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                X_processed[numeric_cols] = self.scalers['robust'].fit_transform(X_processed[numeric_cols])
            
            return X_processed
            
        except Exception as e:
            logger.error(f"特徵預處理失敗: {e}")
            return X
    
    def _split_training_data(self, X: pd.DataFrame, y_dict: Dict, validation_split: float) -> Tuple:
        """劃分訓練和驗證數據"""
        try:
            # 使用時間序列分割以避免數據洩漏
            split_index = int(len(X) * (1 - validation_split))
            
            X_train = X.iloc[:split_index].copy()
            X_val = X.iloc[split_index:].copy()
            
            y_train_dict = {}
            y_val_dict = {}
            
            for target_name, y_values in y_dict.items():
                y_train_dict[target_name] = y_values.iloc[:split_index].copy()
                y_val_dict[target_name] = y_values.iloc[split_index:].copy()
            
            logger.info(f"數據劃分完成 - 訓練: {len(X_train)}, 驗證: {len(X_val)}")
            return X_train, X_val, y_train_dict, y_val_dict
            
        except Exception as e:
            logger.error(f"數據劃分失敗: {e}")
            raise e
    
    def _train_regression_models(self, 
                                X_train: pd.DataFrame, 
                                X_val: pd.DataFrame,
                                y_train_dict: Dict, 
                                y_val_dict: Dict) -> Dict:
        """訓練回歸模型"""
        try:
            regression_results = {}
            
            for target in ['home_score', 'away_score']:
                if target not in y_train_dict:
                    continue
                
                target_results = {}
                y_train = y_train_dict[target]
                y_val = y_val_dict[target]
                
                logger.info(f"訓練回歸模型: {target}")
                
                for model_name, model in self.models['regression'].items():
                    try:
                        # 訓練模型
                        model.fit(X_train, y_train)
                        
                        # 驗證預測
                        y_pred = model.predict(X_val)
                        
                        # 計算指標
                        mae = mean_absolute_error(y_val, y_pred)
                        rmse = np.sqrt(mean_squared_error(y_val, y_pred))
                        r2 = r2_score(y_val, y_pred)
                        
                        target_results[model_name] = {
                            'mae': mae,
                            'rmse': rmse,
                            'r2': r2,
                            'validation_samples': len(y_val)
                        }
                        
                        logger.debug(f"{model_name} ({target}) - MAE: {mae:.3f}, RMSE: {rmse:.3f}, R²: {r2:.3f}")
                        
                    except Exception as e:
                        logger.error(f"{model_name} 回歸訓練失敗: {e}")
                        target_results[model_name] = {'error': str(e)}
                
                regression_results[target] = target_results
            
            return regression_results
            
        except Exception as e:
            logger.error(f"回歸模型訓練失敗: {e}")
            return {'error': str(e)}
    
    def _train_classification_models(self,
                                   X_train: pd.DataFrame,
                                   X_val: pd.DataFrame,
                                   y_train_dict: Dict,
                                   y_val_dict: Dict) -> Dict:
        """訓練分類模型"""
        try:
            classification_results = {}
            
            if 'home_win' not in y_train_dict:
                logger.warning("缺少勝負標籤，跳過分類模型訓練")
                return {'warning': '缺少勝負標籤'}
            
            y_train = y_train_dict['home_win']
            y_val = y_val_dict['home_win']
            
            logger.info("訓練分類模型: home_win")
            
            for model_name, model in self.models['classification'].items():
                try:
                    # 訓練模型
                    model.fit(X_train, y_train)
                    
                    # 預測
                    y_pred = model.predict(X_val)
                    y_pred_proba = None
                    
                    if hasattr(model, 'predict_proba'):
                        y_pred_proba = model.predict_proba(X_val)[:, 1]  # 獲取正類概率
                    
                    # 計算指標
                    accuracy = accuracy_score(y_val, y_pred)
                    precision = precision_score(y_val, y_pred, average='weighted', zero_division=0)
                    recall = recall_score(y_val, y_pred, average='weighted', zero_division=0)
                    f1 = f1_score(y_val, y_pred, average='weighted', zero_division=0)
                    
                    result = {
                        'accuracy': accuracy,
                        'precision': precision,
                        'recall': recall,
                        'f1': f1,
                        'validation_samples': len(y_val),
                        'has_probability': y_pred_proba is not None
                    }
                    
                    if y_pred_proba is not None:
                        # 計算概率校準指標
                        result['probability_stats'] = {
                            'mean_proba': np.mean(y_pred_proba),
                            'std_proba': np.std(y_pred_proba),
                            'min_proba': np.min(y_pred_proba),
                            'max_proba': np.max(y_pred_proba)
                        }
                    
                    classification_results[model_name] = result
                    
                    logger.debug(f"{model_name} - 準確率: {accuracy:.3f}, F1: {f1:.3f}")
                    
                except Exception as e:
                    logger.error(f"{model_name} 分類訓練失敗: {e}")
                    classification_results[model_name] = {'error': str(e)}
            
            return classification_results
            
        except Exception as e:
            logger.error(f"分類模型訓練失敗: {e}")
            return {'error': str(e)}
    
    def _optimize_model_weights(self, X_val: pd.DataFrame, y_val_dict: Dict) -> Dict:
        """優化模型權重"""
        try:
            logger.info("開始優化模型權重")
            
            optimization_results = {
                'regression': {},
                'classification': {}
            }
            
            # 優化回歸權重
            for target in ['home_score', 'away_score']:
                if target not in y_val_dict:
                    continue
                
                y_val = y_val_dict[target]
                target_weights = {}
                total_error = 0
                
                for model_name, model in self.models['regression'].items():
                    try:
                        if hasattr(model, 'predict'):
                            y_pred = model.predict(X_val)
                            mae = mean_absolute_error(y_val, y_pred)
                            
                            # 權重與誤差成反比
                            weight = 1.0 / (mae + 0.001)  # 避免除零
                            target_weights[model_name] = weight
                            total_error += mae
                            
                    except Exception as e:
                        logger.error(f"模型 {model_name} 權重計算失敗: {e}")
                        target_weights[model_name] = 0.1  # 最小權重
                
                # 正規化權重
                if target_weights and sum(target_weights.values()) > 0:
                    total_weight = sum(target_weights.values())
                    normalized_weights = {k: v/total_weight for k, v in target_weights.items()}
                    
                    # 更新模型權重
                    self.model_weights['regression'] = normalized_weights
                    optimization_results['regression'][target] = {
                        'optimized_weights': normalized_weights,
                        'average_error': total_error / len(target_weights) if target_weights else 0
                    }
            
            # 優化分類權重
            if 'home_win' in y_val_dict:
                y_val = y_val_dict['home_win']
                target_weights = {}
                
                for model_name, model in self.models['classification'].items():
                    try:
                        if hasattr(model, 'predict'):
                            y_pred = model.predict(X_val)
                            accuracy = accuracy_score(y_val, y_pred)
                            
                            # 權重與準確率成正比
                            weight = accuracy
                            target_weights[model_name] = weight
                            
                    except Exception as e:
                        logger.error(f"分類模型 {model_name} 權重計算失敗: {e}")
                        target_weights[model_name] = 0.25  # 均等權重
                
                # 正規化分類權重
                if target_weights and sum(target_weights.values()) > 0:
                    total_weight = sum(target_weights.values())
                    normalized_weights = {k: v/total_weight for k, v in target_weights.items()}
                    
                    # 更新分類權重
                    self.model_weights['classification'] = normalized_weights
                    optimization_results['classification']['home_win'] = {
                        'optimized_weights': normalized_weights,
                        'average_accuracy': sum(target_weights.values()) / len(target_weights)
                    }
            
            logger.info("模型權重優化完成")
            return optimization_results
            
        except Exception as e:
            logger.error(f"模型權重優化失敗: {e}")
            return {'error': str(e)}
    
    def _evaluate_ensemble_performance(self, X_val: pd.DataFrame, y_val_dict: Dict) -> Dict:
        """評估集成效果"""
        try:
            logger.info("評估集成模型性能")
            
            evaluation_results = {}
            
            # 評估回歸集成
            for target in ['home_score', 'away_score']:
                if target not in y_val_dict:
                    continue
                
                y_val = y_val_dict[target]
                
                # 獲取集成預測
                ensemble_pred = self._predict_regression_ensemble(X_val, target)
                
                if ensemble_pred is not None:
                    mae = mean_absolute_error(y_val, ensemble_pred)
                    rmse = np.sqrt(mean_squared_error(y_val, ensemble_pred))
                    r2 = r2_score(y_val, ensemble_pred)
                    
                    evaluation_results[f'{target}_ensemble'] = {
                        'mae': mae,
                        'rmse': rmse,
                        'r2': r2,
                        'samples': len(y_val)
                    }
                    
                    # 更新性能指標
                    self.performance_metrics['regression'][target] = {
                        'mae': mae,
                        'rmse': rmse,
                        'r2': r2,
                        'samples': len(y_val)
                    }
            
            # 評估分類集成
            if 'home_win' in y_val_dict:
                y_val = y_val_dict['home_win']
                
                # 獲取集成預測
                ensemble_pred, ensemble_proba = self._predict_classification_ensemble(X_val)
                
                if ensemble_pred is not None:
                    accuracy = accuracy_score(y_val, ensemble_pred)
                    precision = precision_score(y_val, ensemble_pred, average='weighted', zero_division=0)
                    recall = recall_score(y_val, ensemble_pred, average='weighted', zero_division=0)
                    f1 = f1_score(y_val, ensemble_pred, average='weighted', zero_division=0)
                    
                    evaluation_results['home_win_ensemble'] = {
                        'accuracy': accuracy,
                        'precision': precision,
                        'recall': recall,
                        'f1': f1,
                        'samples': len(y_val)
                    }
                    
                    # 更新性能指標
                    self.performance_metrics['classification']['win_prediction'] = {
                        'accuracy': accuracy,
                        'precision': precision,
                        'recall': recall,
                        'f1': f1,
                        'samples': len(y_val)
                    }
            
            # 計算整體性能指標
            if evaluation_results:
                # 綜合準確率 (結合回歸和分類)
                regression_r2 = np.mean([
                    evaluation_results.get('home_score_ensemble', {}).get('r2', 0),
                    evaluation_results.get('away_score_ensemble', {}).get('r2', 0)
                ])
                
                classification_acc = evaluation_results.get('home_win_ensemble', {}).get('accuracy', 0)
                
                overall_accuracy = (regression_r2 * 0.6 + classification_acc * 0.4)
                
                self.performance_metrics['ensemble']['overall_accuracy'] = overall_accuracy
                evaluation_results['overall_performance'] = {
                    'overall_accuracy': overall_accuracy,
                    'regression_r2': regression_r2,
                    'classification_accuracy': classification_acc
                }
            
            return evaluation_results
            
        except Exception as e:
            logger.error(f"集成性能評估失敗: {e}")
            return {'error': str(e)}
    
    def _predict_regression_ensemble(self, X: pd.DataFrame, target: str) -> Optional[np.ndarray]:
        """集成回歸預測"""
        try:
            predictions = []
            weights = []
            
            for model_name, model in self.models['regression'].items():
                if hasattr(model, 'predict'):
                    try:
                        pred = model.predict(X)
                        weight = self.model_weights['regression'].get(model_name, 0.2)
                        
                        predictions.append(pred)
                        weights.append(weight)
                        
                    except Exception as e:
                        logger.warning(f"模型 {model_name} 預測失敗: {e}")
            
            if not predictions:
                return None
            
            # 加權平均
            predictions = np.array(predictions)
            weights = np.array(weights)
            weights = weights / weights.sum()  # 正規化權重
            
            ensemble_pred = np.average(predictions, axis=0, weights=weights)
            
            return ensemble_pred
            
        except Exception as e:
            logger.error(f"回歸集成預測失敗: {e}")
            return None
    
    def _predict_classification_ensemble(self, X: pd.DataFrame) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """集成分類預測"""
        try:
            predictions = []
            probabilities = []
            weights = []
            
            for model_name, model in self.models['classification'].items():
                if hasattr(model, 'predict'):
                    try:
                        pred = model.predict(X)
                        weight = self.model_weights['classification'].get(model_name, 0.25)
                        
                        predictions.append(pred)
                        weights.append(weight)
                        
                        # 獲取概率
                        if hasattr(model, 'predict_proba'):
                            proba = model.predict_proba(X)[:, 1]  # 正類概率
                            probabilities.append(proba)
                        else:
                            probabilities.append(pred.astype(float))
                            
                    except Exception as e:
                        logger.warning(f"分類模型 {model_name} 預測失敗: {e}")
            
            if not predictions:
                return None, None
            
            # 加權投票
            predictions = np.array(predictions)
            probabilities = np.array(probabilities)
            weights = np.array(weights)
            weights = weights / weights.sum()  # 正規化權重
            
            # 集成概率
            ensemble_proba = np.average(probabilities, axis=0, weights=weights)
            
            # 集成預測 (基於概率閾值)
            ensemble_pred = (ensemble_proba > 0.5).astype(int)
            
            return ensemble_pred, ensemble_proba
            
        except Exception as e:
            logger.error(f"分類集成預測失敗: {e}")
            return None, None
    
    def predict(self, X: pd.DataFrame) -> Dict:
        """
        使用集成模型進行預測
        
        Args:
            X: 特徵數據
            
        Returns:
            預測結果字典
        """
        try:
            if not self.is_trained:
                raise ValueError("模型尚未訓練")
            
            # 預處理特徵 (使用已訓練的預處理器)
            X_processed = self._preprocess_features_predict(X)
            
            predictions = {}
            
            # 回歸預測 (分數)
            home_score_pred = self._predict_regression_ensemble(X_processed, 'home_score')
            away_score_pred = self._predict_regression_ensemble(X_processed, 'away_score')
            
            if home_score_pred is not None:
                predictions['home_score'] = home_score_pred
            if away_score_pred is not None:
                predictions['away_score'] = away_score_pred
            
            # 分類預測 (勝負)
            win_pred, win_proba = self._predict_classification_ensemble(X_processed)
            
            if win_pred is not None:
                predictions['home_win'] = win_pred
                predictions['home_win_probability'] = win_proba
            
            # 計算預測信心度
            confidence = self._calculate_prediction_confidence(predictions, X_processed)
            predictions['confidence'] = confidence
            
            return predictions
            
        except Exception as e:
            logger.error(f"集成預測失敗: {e}")
            return {'error': str(e)}
    
    def _preprocess_features_predict(self, X: pd.DataFrame) -> pd.DataFrame:
        """預測時的特徵預處理"""
        try:
            # 確保特徵列順序一致
            if self.feature_columns:
                # 只保留訓練時的特徵
                available_cols = [col for col in self.feature_columns if col in X.columns]
                missing_cols = [col for col in self.feature_columns if col not in X.columns]
                
                if missing_cols:
                    logger.warning(f"缺少特徵列: {missing_cols}")
                    # 用零填充缺少的特徵
                    for col in missing_cols:
                        X[col] = 0.0
                
                X_processed = X[self.feature_columns].copy()
            else:
                X_processed = X.copy()
            
            # 應用相同的預處理
            X_processed = X_processed.replace([np.inf, -np.inf], np.nan)
            X_processed = X_processed.fillna(0)  # 預測時用0填充
            
            # 編碼類別變量
            categorical_cols = X_processed.select_dtypes(include=['object']).columns
            for col in categorical_cols:
                X_processed[col] = pd.Categorical(X_processed[col]).codes
            
            # 應用已訓練的標準化器
            numeric_cols = X_processed.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0 and hasattr(self.scalers['robust'], 'scale_'):
                X_processed[numeric_cols] = self.scalers['robust'].transform(X_processed[numeric_cols])
            
            return X_processed
            
        except Exception as e:
            logger.error(f"預測特徵預處理失敗: {e}")
            return X
    
    def _calculate_prediction_confidence(self, predictions: Dict, X: pd.DataFrame) -> float:
        """計算預測信心度"""
        try:
            confidence_factors = []
            
            # 基於模型一致性
            if 'home_win_probability' in predictions:
                proba = predictions['home_win_probability']
                if isinstance(proba, np.ndarray):
                    # 概率分佈的集中度
                    proba_confidence = np.mean(np.abs(proba - 0.5)) * 2  # 轉換到0-1範圍
                    confidence_factors.append(proba_confidence)
            
            # 基於預測合理性
            if 'home_score' in predictions and 'away_score' in predictions:
                home_scores = predictions['home_score']
                away_scores = predictions['away_score']
                
                # 檢查分數合理性
                reasonable_scores = np.logical_and(
                    np.logical_and(home_scores >= 0, home_scores <= 20),
                    np.logical_and(away_scores >= 0, away_scores <= 20)
                )
                score_confidence = np.mean(reasonable_scores)
                confidence_factors.append(score_confidence)
            
            # 綜合信心度
            if confidence_factors:
                overall_confidence = np.mean(confidence_factors)
            else:
                overall_confidence = 0.5  # 預設中等信心度
            
            return np.clip(overall_confidence, 0.1, 0.9)
            
        except Exception as e:
            logger.error(f"信心度計算失敗: {e}")
            return 0.5
    
    def _save_ensemble_models(self) -> Dict:
        """保存集成模型"""
        try:
            save_results = {'saved_models': [], 'failed_models': []}
            
            # 保存回歸模型
            for model_name, model in self.models['regression'].items():
                try:
                    model_path = self.model_dir / f'regression_{model_name}.joblib'
                    joblib.dump(model, model_path)
                    save_results['saved_models'].append(f'regression_{model_name}')
                except Exception as e:
                    logger.error(f"保存回歸模型 {model_name} 失敗: {e}")
                    save_results['failed_models'].append(f'regression_{model_name}')
            
            # 保存分類模型
            for model_name, model in self.models['classification'].items():
                try:
                    model_path = self.model_dir / f'classification_{model_name}.joblib'
                    joblib.dump(model, model_path)
                    save_results['saved_models'].append(f'classification_{model_name}')
                except Exception as e:
                    logger.error(f"保存分類模型 {model_name} 失敗: {e}")
                    save_results['failed_models'].append(f'classification_{model_name}')
            
            # 保存元數據
            metadata = {
                'model_weights': self.model_weights,
                'feature_columns': self.feature_columns,
                'performance_metrics': self.performance_metrics,
                'last_training_date': self.last_training_date.isoformat() if self.last_training_date else None,
                'training_data_size': self.training_data_size,
                'is_trained': self.is_trained
            }
            
            metadata_path = self.model_dir / 'ensemble_metadata.json'
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            save_results['metadata_saved'] = True
            
            # 保存預處理器
            for scaler_name, scaler in self.scalers.items():
                try:
                    scaler_path = self.model_dir / f'{scaler_name}_scaler.joblib'
                    joblib.dump(scaler, scaler_path)
                    save_results['saved_models'].append(f'{scaler_name}_scaler')
                except Exception as e:
                    logger.error(f"保存預處理器 {scaler_name} 失敗: {e}")
                    save_results['failed_models'].append(f'{scaler_name}_scaler')
            
            logger.info(f"模型保存完成: {len(save_results['saved_models'])} 成功, {len(save_results['failed_models'])} 失敗")
            return save_results
            
        except Exception as e:
            logger.error(f"模型保存失敗: {e}")
            return {'error': str(e)}
    
    def load_ensemble_models(self) -> bool:
        """載入集成模型"""
        try:
            logger.info("載入集成模型")
            
            # 載入元數據
            metadata_path = self.model_dir / 'ensemble_metadata.json'
            if not metadata_path.exists():
                logger.warning("找不到模型元數據文件")
                return False
            
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            self.model_weights = metadata.get('model_weights', self.model_weights)
            self.feature_columns = metadata.get('feature_columns', [])
            self.performance_metrics = metadata.get('performance_metrics', self.performance_metrics)
            self.training_data_size = metadata.get('training_data_size', 0)
            self.is_trained = metadata.get('is_trained', False)
            
            if metadata.get('last_training_date'):
                self.last_training_date = datetime.fromisoformat(metadata['last_training_date'])
            
            # 載入回歸模型
            loaded_regression = 0
            for model_name in self.models['regression'].keys():
                try:
                    model_path = self.model_dir / f'regression_{model_name}.joblib'
                    if model_path.exists():
                        self.models['regression'][model_name] = joblib.load(model_path)
                        loaded_regression += 1
                except Exception as e:
                    logger.error(f"載入回歸模型 {model_name} 失敗: {e}")
            
            # 載入分類模型
            loaded_classification = 0
            for model_name in self.models['classification'].keys():
                try:
                    model_path = self.model_dir / f'classification_{model_name}.joblib'
                    if model_path.exists():
                        self.models['classification'][model_name] = joblib.load(model_path)
                        loaded_classification += 1
                except Exception as e:
                    logger.error(f"載入分類模型 {model_name} 失敗: {e}")
            
            # 載入預處理器
            for scaler_name in self.scalers.keys():
                try:
                    scaler_path = self.model_dir / f'{scaler_name}_scaler.joblib'
                    if scaler_path.exists():
                        self.scalers[scaler_name] = joblib.load(scaler_path)
                except Exception as e:
                    logger.error(f"載入預處理器 {scaler_name} 失敗: {e}")
            
            success = loaded_regression > 0 and loaded_classification > 0
            
            if success:
                logger.info(f"集成模型載入成功 - 回歸: {loaded_regression}, 分類: {loaded_classification}")
            else:
                logger.warning("集成模型載入失敗或不完整")
            
            return success
            
        except Exception as e:
            logger.error(f"集成模型載入失敗: {e}")
            return False
    
    def _generate_performance_summary(self) -> Dict:
        """生成性能摘要"""
        try:
            summary = {
                'training_status': 'completed' if self.is_trained else 'not_trained',
                'training_date': self.last_training_date.isoformat() if self.last_training_date else None,
                'training_samples': self.training_data_size,
                'feature_count': len(self.feature_columns),
                'model_performance': {}
            }
            
            # 回歸性能
            for target, metrics in self.performance_metrics['regression'].items():
                if metrics['samples'] > 0:
                    summary['model_performance'][f'{target}_regression'] = {
                        'mae': metrics['mae'],
                        'rmse': metrics['rmse'],
                        'r2_score': metrics['r2'],
                        'samples': metrics['samples']
                    }
            
            # 分類性能
            win_metrics = self.performance_metrics['classification']['win_prediction']
            if win_metrics['samples'] > 0:
                summary['model_performance']['win_classification'] = {
                    'accuracy': win_metrics['accuracy'],
                    'f1_score': win_metrics['f1'],
                    'samples': win_metrics['samples']
                }
            
            # 整體性能
            summary['overall_accuracy'] = self.performance_metrics['ensemble']['overall_accuracy']
            
            return summary
            
        except Exception as e:
            logger.error(f"性能摘要生成失敗: {e}")
            return {'error': str(e)}
    
    def get_model_info(self) -> Dict:
        """獲取模型信息"""
        return {
            'is_trained': self.is_trained,
            'last_training_date': self.last_training_date.isoformat() if self.last_training_date else None,
            'training_data_size': self.training_data_size,
            'feature_count': len(self.feature_columns),
            'model_weights': self.model_weights.copy(),
            'performance_metrics': self.performance_metrics.copy(),
            'available_models': {
                'regression': list(self.models['regression'].keys()),
                'classification': list(self.models['classification'].keys())
            }
        }


class TimeSeriesFeatureExtractor:
    """時間序列特徵提取器"""
    
    def __init__(self):
        self.lookback_days = 30
    
    def extract_features(self, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """提取時間序列特徵"""
        try:
            if 'game_date' not in data.columns:
                return None
            
            # 確保日期格式正確
            data = data.copy()
            data['game_date'] = pd.to_datetime(data['game_date'])
            data = data.sort_values('game_date')
            
            ts_features = pd.DataFrame(index=data.index)
            
            # 日期特徵
            ts_features['day_of_week'] = data['game_date'].dt.dayofweek
            ts_features['month'] = data['game_date'].dt.month
            ts_features['day_of_year'] = data['game_date'].dt.dayofyear
            
            # 週期性特徵
            ts_features['week_sin'] = np.sin(2 * np.pi * data['game_date'].dt.dayofweek / 7)
            ts_features['week_cos'] = np.cos(2 * np.pi * data['game_date'].dt.dayofweek / 7)
            
            ts_features['month_sin'] = np.sin(2 * np.pi * data['game_date'].dt.month / 12)
            ts_features['month_cos'] = np.cos(2 * np.pi * data['game_date'].dt.month / 12)
            
            # 賽季進度 (假設4月-10月)
            season_day = np.where(
                data['game_date'].dt.month >= 4,
                (data['game_date'].dt.month - 4) * 30 + data['game_date'].dt.day,
                (data['game_date'].dt.month + 8) * 30 + data['game_date'].dt.day
            )
            ts_features['season_progress'] = season_day / 210  # 正規化到0-1
            
            return ts_features
            
        except Exception as e:
            logger.error(f"時間序列特徵提取失敗: {e}")
            return None