"""
MLB 勝負預測邏輯優化模組
解決 51% 勝負預測錯誤率問題

目標：
- 改善勝負預測邏輯，從51%錯誤率提升至準確預測
- 整合多種因素：主場優勢、近期狀態、投手對戰
- 建立綜合勝率計算系統
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import json
from collections import defaultdict

logger = logging.getLogger(__name__)

class EnhancedWinPrediction:
    """增強勝負預測器 - 優化的勝負預測邏輯"""
    
    def __init__(self):
        """初始化勝負預測器"""
        # 預測因子權重配置
        self.prediction_weights = {
            'score_based': 0.60,        # 基於分數預測的勝率 (60%)
            'home_advantage': 0.20,     # 主場優勢 (20%)
            'recent_form': 0.10,        # 近期狀態 (10%)
            'pitcher_matchup': 0.10     # 投手對戰 (10%)
        }
        
        # MLB 統計參數
        self.mlb_constants = {
            'home_advantage': 0.54,     # MLB 平均主場優勢
            'home_advantage_range': (0.45, 0.65),  # 主場優勢合理範圍
            'pythagorean_exponent': 1.83,  # 畢氏定理指數 (MLB)
        }
        
        # 球隊特定主場優勢 (基於統計)
        self.team_home_advantages = {
            'COL': 0.62, 'BOS': 0.58, 'LAD': 0.57, 'HOU': 0.57, 'STL': 0.56,
            'ATL': 0.55, 'NYY': 0.55, 'SF': 0.55, 'TB': 0.54, 'CLE': 0.54,
            'CHC': 0.54, 'MIL': 0.54, 'SD': 0.54, 'MIN': 0.53, 'TEX': 0.53,
            'SEA': 0.53, 'PHI': 0.53, 'WSH': 0.52, 'TOR': 0.52, 'ARI': 0.52,
            'PIT': 0.52, 'CIN': 0.52, 'NYM': 0.51, 'LAA': 0.51, 'DET': 0.51,
            'KC': 0.50, 'CWS': 0.50, 'BAL': 0.49, 'OAK': 0.49, 'MIA': 0.48
        }
        
        # 近期狀態評估參數
        self.recent_form_config = {
            'lookback_games': 10,       # 評估最近10場比賽
            'weight_decay': 0.9,        # 權重衰減係數
            'momentum_threshold': 0.7   # 動量閾值
        }
        
        # 投手對戰評估參數  
        self.pitcher_matchup_config = {
            'era_weight': 0.4,          # ERA 權重
            'whip_weight': 0.3,         # WHIP 權重
            'recent_starts_weight': 0.3 # 近期先發權重
        }
        
        # 預測歷史追蹤
        self.prediction_history = []
        
        # 性能指標
        self.performance_metrics = {
            'total_predictions': 0,
            'correct_predictions': 0,
            'accuracy': 0.0,
            'by_confidence': {
                'high': {'correct': 0, 'total': 0},
                'medium': {'correct': 0, 'total': 0}, 
                'low': {'correct': 0, 'total': 0}
            }
        }
        
        logger.info("增強勝負預測器初始化完成")
    
    def predict_winner(self, 
                      home_score: float, 
                      away_score: float, 
                      features: Dict,
                      detailed_data: Dict = None) -> Dict:
        """
        預測比賽勝負
        
        Args:
            home_score: 預測主隊得分
            away_score: 預測客隊得分
            features: 比賽特徵數據
            detailed_data: 詳細數據 (可選)
            
        Returns:
            預測結果字典
        """
        try:
            # 步驟1: 基於分數的基礎勝率計算
            score_based_prob = self._calculate_score_based_probability(home_score, away_score)
            
            # 步驟2: 主場優勢調整
            home_advantage_factor = self._calculate_home_advantage(features)
            
            # 步驟3: 近期狀態調整
            recent_form_factor = self._calculate_recent_form(features, detailed_data)
            
            # 步驟4: 投手對戰調整
            pitcher_matchup_factor = self._analyze_pitcher_matchup(features, detailed_data)
            
            # 步驟5: 綜合計算最終勝率
            final_win_probability = self._combine_prediction_factors({
                'score_based': score_based_prob,
                'home_advantage': home_advantage_factor,
                'recent_form': recent_form_factor,
                'pitcher_matchup': pitcher_matchup_factor
            })
            
            # 步驟6: 應用邊界約束和調整
            final_win_probability = self._apply_probability_constraints(final_win_probability, features)
            
            # 步驟7: 生成預測結果
            prediction_result = self._generate_prediction_result(
                final_win_probability, home_score, away_score, features, detailed_data
            )
            
            # 步驟8: 記錄預測歷史
            self._record_prediction(prediction_result, features)
            
            return prediction_result
            
        except Exception as e:
            logger.error(f"勝負預測失敗: {e}")
            # 返回備用預測
            return self._generate_fallback_prediction(home_score, away_score)
    
    def _calculate_score_based_probability(self, home_score: float, away_score: float) -> float:
        """基於分數的勝率計算 (60% 權重)"""
        try:
            if home_score + away_score == 0:
                return 0.5  # 無法預測時返回50%
            
            # 基礎機率計算
            basic_prob = home_score / (home_score + away_score)
            
            # 使用畢氏定理優化 (Bill James 公式)
            pythagorean_prob = (home_score ** self.mlb_constants['pythagorean_exponent']) / \
                              (home_score ** self.mlb_constants['pythagorean_exponent'] + 
                               away_score ** self.mlb_constants['pythagorean_exponent'])
            
            # 結合兩種方法 (基礎70% + 畢氏30%)
            combined_prob = basic_prob * 0.7 + pythagorean_prob * 0.3
            
            return np.clip(combined_prob, 0.05, 0.95)
            
        except Exception as e:
            logger.error(f"分數勝率計算失敗: {e}")
            return 0.5
    
    def _calculate_home_advantage(self, features: Dict) -> float:
        """計算主場優勢 (20% 權重)"""
        try:
            home_team = features.get('home_team', '')
            
            # 獲取球隊特定主場優勢
            base_home_advantage = self.team_home_advantages.get(home_team, 
                                                              self.mlb_constants['home_advantage'])
            
            # 根據比賽情境調整
            adjusted_advantage = base_home_advantage
            
            # 季後賽或重要比賽主場優勢更明顯
            if features.get('game_importance') in ['playoff', 'division_critical']:
                adjusted_advantage += 0.03
            
            # 週末比賽主場優勢略高 (球迷更多)
            game_date = features.get('game_date')
            if game_date and isinstance(game_date, date):
                if game_date.weekday() >= 5:  # 週六日
                    adjusted_advantage += 0.01
            
            # 特殊球場因素 (Coors Field, Fenway Park 等)
            park_factors = {
                'COL': 0.05,  # Coors Field 高海拔
                'BOS': 0.03,  # Fenway Park Green Monster
                'HOU': 0.02,  # 室內球場
                'TB': 0.02,   # 室內球場
                'MIN': 0.02,  # 室內球場
            }
            
            park_boost = park_factors.get(home_team, 0)
            adjusted_advantage += park_boost
            
            # 確保主場優勢在合理範圍內
            min_adv, max_adv = self.mlb_constants['home_advantage_range']
            adjusted_advantage = np.clip(adjusted_advantage, min_adv, max_adv)
            
            return adjusted_advantage
            
        except Exception as e:
            logger.error(f"主場優勢計算失敗: {e}")
            return self.mlb_constants['home_advantage']
    
    def _calculate_recent_form(self, features: Dict, detailed_data: Dict = None) -> float:
        """計算近期狀態影響 (10% 權重)"""
        try:
            if detailed_data is None:
                return 0.5  # 無詳細數據時返回中性
            
            home_team = features.get('home_team', '')
            away_team = features.get('away_team', '') 
            
            # 獲取近期比賽記錄
            home_recent_games = detailed_data.get('home_recent_games', [])
            away_recent_games = detailed_data.get('away_recent_games', [])
            
            # 計算近期表現
            home_form_score = self._calculate_team_form_score(home_recent_games)
            away_form_score = self._calculate_team_form_score(away_recent_games)
            
            # 正規化為相對優勢
            if home_form_score + away_form_score == 0:
                return 0.5
                
            recent_form_advantage = home_form_score / (home_form_score + away_form_score)
            
            # 考慮動量因素
            home_momentum = self._calculate_momentum(home_recent_games)
            away_momentum = self._calculate_momentum(away_recent_games)
            
            momentum_factor = (home_momentum - away_momentum) * 0.1  # 動量影響10%
            recent_form_advantage += momentum_factor
            
            return np.clip(recent_form_advantage, 0.2, 0.8)
            
        except Exception as e:
            logger.error(f"近期狀態計算失敗: {e}")
            return 0.5
    
    def _calculate_team_form_score(self, recent_games: List[Dict]) -> float:
        """計算球隊狀態得分"""
        try:
            if not recent_games:
                return 0.5
                
            form_score = 0.0
            total_weight = 0.0
            
            # 最多考慮最近10場比賽
            games_to_analyze = recent_games[-self.recent_form_config['lookback_games']:]
            
            for i, game in enumerate(reversed(games_to_analyze)):
                # 權重遞減 (越近期權重越高)
                weight = self.recent_form_config['weight_decay'] ** i
                
                # 比賽結果得分
                if game.get('won', False):
                    game_score = 1.0
                else:
                    game_score = 0.0
                    
                # 考慮勝負差距 (大勝加分，慘敗減分)
                runs_differential = game.get('runs_differential', 0)
                if runs_differential > 5:
                    game_score += 0.2  # 大勝加分
                elif runs_differential < -5:
                    game_score -= 0.2  # 慘敗減分
                
                form_score += game_score * weight
                total_weight += weight
                
            return form_score / total_weight if total_weight > 0 else 0.5
            
        except Exception as e:
            logger.error(f"球隊狀態得分計算失敗: {e}")
            return 0.5
    
    def _calculate_momentum(self, recent_games: List[Dict]) -> float:
        """計算球隊動量"""
        try:
            if len(recent_games) < 3:
                return 0.0
                
            recent_5 = recent_games[-5:]  # 最近5場
            wins = sum(1 for game in recent_5 if game.get('won', False))
            
            momentum_score = 0.0
            
            # 連勝/連敗判斷
            if wins >= 4:  # 5場贏4場或以上
                momentum_score = 0.3
            elif wins <= 1:  # 5場贏1場或以下
                momentum_score = -0.3
                
            # 檢查連勝連敗
            consecutive_wins = 0
            for game in reversed(recent_games):
                if game.get('won', False):
                    consecutive_wins += 1
                else:
                    break
                    
            consecutive_losses = 0
            for game in reversed(recent_games):
                if not game.get('won', True):
                    consecutive_losses += 1
                else:
                    break
            
            # 連勝連敗加成
            if consecutive_wins >= 3:
                momentum_score += 0.2
            elif consecutive_losses >= 3:
                momentum_score -= 0.2
            
            return np.clip(momentum_score, -0.5, 0.5)
            
        except Exception as e:
            logger.error(f"動量計算失敗: {e}")
            return 0.0
    
    def _analyze_pitcher_matchup(self, features: Dict, detailed_data: Dict = None) -> float:
        """分析投手對戰 (10% 權重)"""
        try:
            if detailed_data is None:
                return 0.5
                
            pitcher_data = detailed_data.get('pitcher_data', {})
            home_pitcher = pitcher_data.get('home_pitcher', {})
            away_pitcher = pitcher_data.get('away_pitcher', {})
            
            if not home_pitcher or not away_pitcher:
                return 0.5
            
            # 計算投手優勢得分
            home_pitcher_score = self._calculate_pitcher_score(home_pitcher)
            away_pitcher_score = self._calculate_pitcher_score(away_pitcher)
            
            # 正規化為相對優勢 
            total_score = home_pitcher_score + away_pitcher_score
            if total_score == 0:
                return 0.5
                
            pitcher_advantage = home_pitcher_score / total_score
            
            # 考慮投手對特定球隊的表現
            home_vs_opponent = home_pitcher.get('vs_opponent_era', None)
            away_vs_opponent = away_pitcher.get('vs_opponent_era', None)
            
            if home_vs_opponent is not None and away_vs_opponent is not None:
                # ERA 越低越好，所以需要反轉
                opponent_factor = away_vs_opponent / (home_vs_opponent + away_vs_opponent)
                pitcher_advantage = pitcher_advantage * 0.7 + opponent_factor * 0.3
            
            return np.clip(pitcher_advantage, 0.2, 0.8)
            
        except Exception as e:
            logger.error(f"投手對戰分析失敗: {e}")
            return 0.5
    
    def _calculate_pitcher_score(self, pitcher_data: Dict) -> float:
        """計算投手得分"""
        try:
            era = pitcher_data.get('era', 4.50)  # MLB 平均 ERA
            whip = pitcher_data.get('whip', 1.30)  # MLB 平均 WHIP
            recent_starts = pitcher_data.get('recent_starts', [])
            
            # ERA 得分 (越低越好)
            era_score = max(0, 6.0 - era) / 6.0  # 6.00 ERA 為 0 分
            
            # WHIP 得分 (越低越好)
            whip_score = max(0, 2.0 - whip) / 2.0  # 2.00 WHIP 為 0 分
            
            # 近期先發表現
            recent_score = 0.5  # 預設中等表現
            if recent_starts:
                recent_eras = [start.get('era', 4.50) for start in recent_starts[-5:]]  # 最近5次先發
                avg_recent_era = np.mean(recent_eras)
                recent_score = max(0, 6.0 - avg_recent_era) / 6.0
            
            # 加權計算總得分
            total_score = (era_score * self.pitcher_matchup_config['era_weight'] +
                          whip_score * self.pitcher_matchup_config['whip_weight'] +
                          recent_score * self.pitcher_matchup_config['recent_starts_weight'])
            
            return np.clip(total_score, 0.0, 1.0)
            
        except Exception as e:
            logger.error(f"投手得分計算失敗: {e}")
            return 0.5
    
    def _combine_prediction_factors(self, factors: Dict) -> float:
        """結合預測因子"""
        try:
            # 加權平均計算
            final_probability = 0.0
            
            for factor_name, factor_value in factors.items():
                weight = self.prediction_weights.get(factor_name, 0.0)
                final_probability += factor_value * weight
                
            # 確保機率在合理範圍內
            return np.clip(final_probability, 0.05, 0.95)
            
        except Exception as e:
            logger.error(f"預測因子結合失敗: {e}")
            return 0.5
    
    def _apply_probability_constraints(self, probability: float, features: Dict) -> float:
        """應用機率約束和調整"""
        try:
            adjusted_prob = probability
            
            # 極端情況調整
            if adjusted_prob < 0.1:
                adjusted_prob = 0.1  # 最低 10%
            elif adjusted_prob > 0.9:
                adjusted_prob = 0.9  # 最高 90%
                
            # 不確定情況的保守調整
            uncertainty_factors = []
            
            # 缺少關鍵數據時降低極端預測
            if not features.get('pitcher_data_complete', True):
                uncertainty_factors.append(0.95)
                
            if not features.get('recent_form_available', True):
                uncertainty_factors.append(0.95)
            
            # 應用不確定性調整
            for factor in uncertainty_factors:
                if adjusted_prob > 0.5:
                    adjusted_prob = 0.5 + (adjusted_prob - 0.5) * factor
                else:
                    adjusted_prob = 0.5 - (0.5 - adjusted_prob) * factor
            
            return np.clip(adjusted_prob, 0.05, 0.95)
            
        except Exception as e:
            logger.error(f"機率約束應用失敗: {e}")
            return probability
    
    def _generate_prediction_result(self, win_probability: float, home_score: float, 
                                  away_score: float, features: Dict, 
                                  detailed_data: Dict = None) -> Dict:
        """生成預測結果"""
        try:
            home_team = features.get('home_team', 'HOME')
            away_team = features.get('away_team', 'AWAY')
            
            # 預測獲勝隊伍
            predicted_winner = home_team if win_probability > 0.5 else away_team
            winner_confidence = max(win_probability, 1 - win_probability)
            
            # 信心度等級
            if winner_confidence >= 0.75:
                confidence_level = 'high'
            elif winner_confidence >= 0.6:
                confidence_level = 'medium'
            else:
                confidence_level = 'low'
            
            # 預測摘要
            prediction_summary = self._generate_prediction_summary(
                win_probability, home_team, away_team, winner_confidence
            )
            
            result = {
                'home_team': home_team,
                'away_team': away_team,
                'home_win_probability': win_probability,
                'away_win_probability': 1 - win_probability,
                'predicted_winner': predicted_winner,
                'winner_confidence': winner_confidence,
                'confidence_level': confidence_level,
                'predicted_scores': {
                    'home': home_score,
                    'away': away_score
                },
                'prediction_summary': prediction_summary,
                'factors_breakdown': self._get_factors_breakdown(features, detailed_data),
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"預測結果生成失敗: {e}")
            return self._generate_fallback_prediction(home_score, away_score)
    
    def _generate_prediction_summary(self, win_prob: float, home_team: str, 
                                   away_team: str, confidence: float) -> str:
        """生成預測摘要"""
        try:
            winner = home_team if win_prob > 0.5 else away_team
            prob_pct = confidence * 100
            
            if confidence >= 0.75:
                strength = "強烈看好"
            elif confidence >= 0.6:
                strength = "略為看好"
            else:
                strength = "微幅看好"
                
            summary = f"{strength} {winner} 獲勝，勝率 {prob_pct:.1f}%"
            
            return summary
            
        except Exception as e:
            logger.error(f"預測摘要生成失敗: {e}")
            return "預測摘要生成失敗"
    
    def _get_factors_breakdown(self, features: Dict, detailed_data: Dict = None) -> Dict:
        """獲取因子分解"""
        try:
            breakdown = {}
            
            # 重新計算各因子以提供詳細分解
            home_score = features.get('predicted_home_score', 0)
            away_score = features.get('predicted_away_score', 0) 
            
            breakdown['score_based'] = {
                'value': self._calculate_score_based_probability(home_score, away_score),
                'weight': self.prediction_weights['score_based'],
                'description': f"基於預測分數 {home_score:.1f} - {away_score:.1f}"
            }
            
            breakdown['home_advantage'] = {
                'value': self._calculate_home_advantage(features),
                'weight': self.prediction_weights['home_advantage'],
                'description': f"主場優勢 ({features.get('home_team', '')})"
            }
            
            breakdown['recent_form'] = {
                'value': self._calculate_recent_form(features, detailed_data),
                'weight': self.prediction_weights['recent_form'],
                'description': "近期狀態比較"
            }
            
            breakdown['pitcher_matchup'] = {
                'value': self._analyze_pitcher_matchup(features, detailed_data),
                'weight': self.prediction_weights['pitcher_matchup'],
                'description': "投手對戰分析"
            }
            
            return breakdown
            
        except Exception as e:
            logger.error(f"因子分解失敗: {e}")
            return {}
    
    def _record_prediction(self, prediction_result: Dict, features: Dict):
        """記錄預測結果"""
        try:
            record = {
                'prediction': prediction_result.copy(),
                'features': features.copy(),
                'timestamp': datetime.now()
            }
            
            self.prediction_history.append(record)
            
            # 保留最近1000筆記錄
            if len(self.prediction_history) > 1000:
                self.prediction_history = self.prediction_history[-1000:]
                
        except Exception as e:
            logger.error(f"記錄預測失敗: {e}")
    
    def _generate_fallback_prediction(self, home_score: float, away_score: float) -> Dict:
        """生成備用預測"""
        try:
            # 簡單基於分數的預測
            if home_score + away_score == 0:
                win_prob = 0.54  # 預設主場優勢
            else:
                win_prob = home_score / (home_score + away_score)
                
            return {
                'home_win_probability': win_prob,
                'away_win_probability': 1 - win_prob,
                'predicted_winner': 'HOME' if win_prob > 0.5 else 'AWAY',
                'winner_confidence': abs(win_prob - 0.5) * 2,
                'confidence_level': 'low',
                'predicted_scores': {'home': home_score, 'away': away_score},
                'prediction_summary': '備用預測模式',
                'timestamp': datetime.now().isoformat(),
                'fallback_mode': True
            }
            
        except Exception as e:
            logger.error(f"備用預測生成失敗: {e}")
            return {'error': str(e)}
    
    def update_performance_metrics(self, actual_results: List[Dict]):
        """更新性能指標"""
        try:
            for result in actual_results:
                prediction_id = result.get('prediction_id')
                actual_winner = result.get('actual_winner')
                predicted_winner = result.get('predicted_winner')
                confidence_level = result.get('confidence_level', 'medium')
                
                # 更新總體統計
                self.performance_metrics['total_predictions'] += 1
                
                if actual_winner == predicted_winner:
                    self.performance_metrics['correct_predictions'] += 1
                    self.performance_metrics['by_confidence'][confidence_level]['correct'] += 1
                
                self.performance_metrics['by_confidence'][confidence_level]['total'] += 1
            
            # 重新計算準確率
            total = self.performance_metrics['total_predictions']
            if total > 0:
                self.performance_metrics['accuracy'] = \
                    self.performance_metrics['correct_predictions'] / total
            
            # 記錄性能指標
            self._log_performance_metrics()
            
        except Exception as e:
            logger.error(f"性能指標更新失敗: {e}")
    
    def _log_performance_metrics(self):
        """記錄性能指標"""
        try:
            total = self.performance_metrics['total_predictions']
            correct = self.performance_metrics['correct_predictions']
            accuracy = self.performance_metrics['accuracy']
            
            logger.info(f"勝負預測準確率: {accuracy:.3f} ({correct}/{total})")
            
            # 各信心度區間的準確率
            for level, stats in self.performance_metrics['by_confidence'].items():
                if stats['total'] > 0:
                    level_accuracy = stats['correct'] / stats['total']
                    logger.info(f"{level} 信心度準確率: {level_accuracy:.3f} ({stats['correct']}/{stats['total']})")
                    
        except Exception as e:
            logger.error(f"性能指標記錄失敗: {e}")
    
    def get_prediction_report(self) -> Dict:
        """獲取預測報告"""
        try:
            return {
                'performance_metrics': self.performance_metrics.copy(),
                'prediction_weights': self.prediction_weights.copy(),
                'mlb_constants': self.mlb_constants.copy(),
                'prediction_count': len(self.prediction_history),
                'system_status': 'active'
            }
            
        except Exception as e:
            logger.error(f"預測報告生成失敗: {e}")
            return {'error': str(e)}
    
    def optimize_weights(self, validation_data: List[Dict]) -> bool:
        """優化預測權重"""
        try:
            if len(validation_data) < 50:
                logger.warning("驗證數據不足，無法優化權重")
                return False
            
            logger.info(f"開始優化預測權重，使用 {len(validation_data)} 筆驗證數據")
            
            # 這裡可以實施權重優化算法
            # 例如網格搜索找到最佳權重組合
            
            # 記錄當前權重表現作為基準
            current_accuracy = self._evaluate_weights_performance(validation_data)
            logger.info(f"當前權重準確率: {current_accuracy:.3f}")
            
            # 簡化版本：記錄結果
            return True
            
        except Exception as e:
            logger.error(f"權重優化失敗: {e}")
            return False
    
    def _evaluate_weights_performance(self, validation_data: List[Dict]) -> float:
        """評估權重配置性能"""
        try:
            correct = 0
            total = 0
            
            for data in validation_data:
                prediction = self.predict_winner(
                    data.get('predicted_home_score', 0),
                    data.get('predicted_away_score', 0),
                    data.get('features', {}),
                    data.get('detailed_data', {})
                )
                
                predicted_winner = prediction.get('predicted_winner')
                actual_winner = data.get('actual_winner')
                
                if predicted_winner == actual_winner:
                    correct += 1
                total += 1
            
            return correct / total if total > 0 else 0.0
            
        except Exception as e:
            logger.error(f"權重性能評估失敗: {e}")
            return 0.0