"""
MLB 投手影響力分析模組
增強特徵工程 - 投手分析

目標：
- 深度分析投手對比賽結果的影響
- 建立投手對特定打線的歷史表現分析
- 提供投手當前狀態評估
- 計算投手優勢和預期得分影響
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import json
from collections import defaultdict

logger = logging.getLogger(__name__)

class PitcherImpactAnalyzer:
    """投手影響力分析器 - 深度投手分析系統"""
    
    def __init__(self):
        """初始化投手分析器"""
        # 投手評估權重
        self.evaluation_weights = {
            'era_performance': 0.25,        # ERA 表現
            'whip_control': 0.20,          # WHIP 控制力
            'strikeout_ability': 0.15,     # 三振能力  
            'recent_form': 0.15,           # 近期狀態
            'opponent_history': 0.15,      # 對戰歷史
            'situational_stats': 0.10      # 情境統計
        }
        
        # MLB 投手統計基準
        self.mlb_benchmarks = {
            'era': {
                'excellent': 2.50,
                'good': 3.50, 
                'average': 4.20,
                'poor': 5.00
            },
            'whip': {
                'excellent': 1.00,
                'good': 1.20,
                'average': 1.35,
                'poor': 1.50
            },
            'k9': {  # 每9局三振數
                'excellent': 10.0,
                'good': 8.5,
                'average': 7.5,
                'poor': 6.5
            },
            'hr9': {  # 每9局被全壘打數
                'excellent': 0.8,
                'good': 1.0,
                'average': 1.3,
                'poor': 1.6
            }
        }
        
        # 投手類型分類
        self.pitcher_types = {
            'ace': {'era_max': 3.00, 'whip_max': 1.15, 'k9_min': 9.0},
            'quality': {'era_max': 3.75, 'whip_max': 1.25, 'k9_min': 7.5},
            'average': {'era_max': 4.50, 'whip_max': 1.40, 'k9_min': 6.5},
            'below_average': {'era_max': 5.50, 'whip_max': 1.60, 'k9_min': 5.5}
        }
        
        # 情境調整因子
        self.situational_factors = {
            'home_vs_away': {'home_boost': 0.05, 'away_penalty': 0.03},
            'day_vs_night': {'day_adjustment': -0.02, 'night_adjustment': 0.02},
            'rest_days': {
                'well_rested': (5, 0.03),     # 5天以上休息
                'normal_rest': (4, 0.0),      # 4天標準休息
                'short_rest': (3, -0.05),     # 3天短休息
                'very_short': (0, -0.15)      # 0-2天極短休息
            }
        }
        
        # 分析歷史記錄
        self.analysis_history = []
        
        logger.info("投手影響力分析器初始化完成")
    
    def analyze_pitcher_impact(self, 
                              home_pitcher: Dict, 
                              away_pitcher: Dict,
                              opposing_lineups: Dict = None,
                              game_context: Dict = None) -> Dict:
        """
        分析投手對比賽的影響
        
        Args:
            home_pitcher: 主隊投手數據
            away_pitcher: 客隊投手數據
            opposing_lineups: 對方打線數據
            game_context: 比賽情境
            
        Returns:
            投手影響分析結果
        """
        try:
            # 步驟1: 個別投手分析
            home_analysis = self._analyze_individual_pitcher(
                home_pitcher, 'home', opposing_lineups, game_context
            )
            away_analysis = self._analyze_individual_pitcher(
                away_pitcher, 'away', opposing_lineups, game_context
            )
            
            # 步驟2: 投手對戰比較
            matchup_comparison = self._compare_pitcher_matchup(
                home_analysis, away_analysis
            )
            
            # 步驟3: 計算預期得分影響
            runs_impact = self._estimate_runs_impact(
                home_analysis, away_analysis, opposing_lineups, game_context
            )
            
            # 步驟4: 綜合投手優勢計算
            pitcher_advantage = self._calculate_pitcher_advantage(
                home_analysis, away_analysis, matchup_comparison
            )
            
            # 步驟5: 生成分析報告
            analysis_result = {
                'home_pitcher_analysis': home_analysis,
                'away_pitcher_analysis': away_analysis,
                'matchup_comparison': matchup_comparison,
                'pitcher_advantage': pitcher_advantage,
                'expected_runs_impact': runs_impact,
                'analysis_summary': self._generate_analysis_summary(
                    matchup_comparison, pitcher_advantage, runs_impact
                ),
                'timestamp': datetime.now().isoformat()
            }
            
            # 記錄分析歷史
            self._record_analysis(analysis_result)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"投手影響分析失敗: {e}")
            return self._generate_fallback_analysis()
    
    def _analyze_individual_pitcher(self, 
                                   pitcher_data: Dict, 
                                   team_type: str,
                                   opposing_lineups: Dict = None,
                                   game_context: Dict = None) -> Dict:
        """分析個別投手"""
        try:
            if not pitcher_data:
                return self._create_default_pitcher_analysis(team_type)
            
            analysis = {
                'pitcher_name': pitcher_data.get('name', 'Unknown'),
                'team_type': team_type,
                'pitcher_type': self._classify_pitcher_type(pitcher_data),
                'current_season_stats': self._analyze_season_stats(pitcher_data),
                'recent_form': self._analyze_recent_form(pitcher_data),
                'opponent_history': self._analyze_opponent_history(pitcher_data, opposing_lineups),
                'situational_performance': self._analyze_situational_performance(pitcher_data, game_context),
                'overall_rating': 0.0,
                'strengths': [],
                'weaknesses': [],
                'risk_factors': []
            }
            
            # 計算綜合評分
            analysis['overall_rating'] = self._calculate_pitcher_rating(analysis)
            
            # 識別優勢和弱點
            analysis['strengths'], analysis['weaknesses'] = self._identify_strengths_weaknesses(analysis)
            
            # 識別風險因子
            analysis['risk_factors'] = self._identify_risk_factors(analysis, game_context)
            
            return analysis
            
        except Exception as e:
            logger.error(f"個別投手分析失敗: {e}")
            return self._create_default_pitcher_analysis(team_type)
    
    def _classify_pitcher_type(self, pitcher_data: Dict) -> str:
        """分類投手類型"""
        try:
            era = pitcher_data.get('era', 4.50)
            whip = pitcher_data.get('whip', 1.35)
            k9 = pitcher_data.get('k9', pitcher_data.get('strikeouts_per_9', 7.0))
            
            # 檢查是否符合各類型標準
            for pitcher_type, criteria in self.pitcher_types.items():
                if (era <= criteria['era_max'] and 
                    whip <= criteria['whip_max'] and 
                    k9 >= criteria['k9_min']):
                    return pitcher_type
            
            return 'below_average'
            
        except Exception as e:
            logger.error(f"投手類型分類失敗: {e}")
            return 'average'
    
    def _analyze_season_stats(self, pitcher_data: Dict) -> Dict:
        """分析賽季統計"""
        try:
            stats = {
                'era': pitcher_data.get('era', 4.50),
                'whip': pitcher_data.get('whip', 1.35),
                'k9': pitcher_data.get('k9', pitcher_data.get('strikeouts_per_9', 7.0)),
                'hr9': pitcher_data.get('hr9', pitcher_data.get('home_runs_per_9', 1.2)),
                'bb9': pitcher_data.get('bb9', pitcher_data.get('walks_per_9', 3.0)),
                'wins': pitcher_data.get('wins', 8),
                'losses': pitcher_data.get('losses', 8),
                'innings_pitched': pitcher_data.get('innings_pitched', 150.0),
                'games_started': pitcher_data.get('games_started', 25)
            }
            
            # 計算各項統計的評級
            ratings = {}
            for stat_name, value in stats.items():
                if stat_name in ['wins', 'losses', 'innings_pitched', 'games_started']:
                    continue  # 跳過非評級統計
                    
                ratings[f'{stat_name}_rating'] = self._rate_stat_performance(stat_name, value)
            
            return {
                'raw_stats': stats,
                'performance_ratings': ratings,
                'workload': self._assess_workload(stats),
                'consistency': self._assess_consistency(pitcher_data)
            }
            
        except Exception as e:
            logger.error(f"賽季統計分析失敗: {e}")
            return {'raw_stats': {}, 'performance_ratings': {}, 'workload': 'normal', 'consistency': 0.5}
    
    def _rate_stat_performance(self, stat_name: str, value: float) -> str:
        """評級統計表現"""
        try:
            if stat_name not in self.mlb_benchmarks:
                return 'average'
            
            benchmarks = self.mlb_benchmarks[stat_name]
            
            # 對於越小越好的統計 (ERA, WHIP, HR9)
            if stat_name in ['era', 'whip', 'hr9', 'bb9']:
                if value <= benchmarks['excellent']:
                    return 'excellent'
                elif value <= benchmarks['good']:
                    return 'good'
                elif value <= benchmarks['average']:
                    return 'average'
                else:
                    return 'poor'
            
            # 對於越大越好的統計 (K9)
            elif stat_name in ['k9']:
                if value >= benchmarks['excellent']:
                    return 'excellent'
                elif value >= benchmarks['good']:
                    return 'good'
                elif value >= benchmarks['average']:
                    return 'average'
                else:
                    return 'poor'
            
            return 'average'
            
        except Exception as e:
            logger.error(f"統計評級失敗: {e}")
            return 'average'
    
    def _assess_workload(self, stats: Dict) -> str:
        """評估投手工作負荷"""
        try:
            innings = stats.get('innings_pitched', 150.0)
            games = stats.get('games_started', 25)
            
            if innings > 200 or games > 32:
                return 'heavy'
            elif innings > 160 or games > 25:
                return 'normal'
            else:
                return 'light'
                
        except Exception as e:
            logger.error(f"工作負荷評估失敗: {e}")
            return 'normal'
    
    def _assess_consistency(self, pitcher_data: Dict) -> float:
        """評估投手一致性"""
        try:
            # 使用近期先發的變異性評估一致性
            recent_starts = pitcher_data.get('recent_starts', [])
            
            if len(recent_starts) < 5:
                return 0.5  # 數據不足，返回平均值
            
            # 計算 ERA 的變異係數
            eras = [start.get('era', 4.50) for start in recent_starts[-10:]]
            
            if len(eras) == 0:
                return 0.5
                
            mean_era = np.mean(eras)
            std_era = np.std(eras)
            
            if mean_era == 0:
                return 0.5
                
            # 變異係數越小，一致性越好
            cv = std_era / mean_era
            consistency = max(0, 1 - cv / 0.5)  # 0.5 為基準變異係數
            
            return min(consistency, 1.0)
            
        except Exception as e:
            logger.error(f"一致性評估失敗: {e}")
            return 0.5
    
    def _analyze_recent_form(self, pitcher_data: Dict) -> Dict:
        """分析近期狀態"""
        try:
            recent_starts = pitcher_data.get('recent_starts', [])
            
            if not recent_starts:
                return {
                    'form_rating': 'average',
                    'trend': 'stable',
                    'last_5_era': 4.50,
                    'momentum': 0.0,
                    'data_available': False
                }
            
            # 分析最近5次先發
            last_5 = recent_starts[-5:] if len(recent_starts) >= 5 else recent_starts
            
            # 計算近期 ERA
            recent_eras = [start.get('era', 4.50) for start in last_5]
            avg_recent_era = np.mean(recent_eras)
            
            # 評估狀態等級
            if avg_recent_era <= 3.00:
                form_rating = 'excellent'
            elif avg_recent_era <= 3.75:
                form_rating = 'good'
            elif avg_recent_era <= 4.50:
                form_rating = 'average'
            else:
                form_rating = 'poor'
            
            # 分析趨勢
            if len(recent_eras) >= 3:
                early_era = np.mean(recent_eras[:len(recent_eras)//2])
                late_era = np.mean(recent_eras[len(recent_eras)//2:])
                
                if late_era < early_era - 0.5:
                    trend = 'improving'
                elif late_era > early_era + 0.5:
                    trend = 'declining'
                else:
                    trend = 'stable'
            else:
                trend = 'stable'
            
            # 計算動量
            momentum = self._calculate_pitcher_momentum(last_5)
            
            return {
                'form_rating': form_rating,
                'trend': trend,
                'last_5_era': avg_recent_era,
                'momentum': momentum,
                'data_available': True,
                'recent_starts_count': len(last_5)
            }
            
        except Exception as e:
            logger.error(f"近期狀態分析失敗: {e}")
            return {
                'form_rating': 'average',
                'trend': 'stable', 
                'last_5_era': 4.50,
                'momentum': 0.0,
                'data_available': False
            }
    
    def _calculate_pitcher_momentum(self, recent_starts: List[Dict]) -> float:
        """計算投手動量"""
        try:
            if len(recent_starts) < 3:
                return 0.0
            
            momentum_factors = []
            
            # 質量先發 (QS) 比例
            quality_starts = sum(1 for start in recent_starts 
                                if start.get('innings_pitched', 0) >= 6 and start.get('earned_runs', 10) <= 3)
            qs_rate = quality_starts / len(recent_starts)
            
            if qs_rate >= 0.8:
                momentum_factors.append(0.3)
            elif qs_rate >= 0.6:
                momentum_factors.append(0.1)
            elif qs_rate <= 0.2:
                momentum_factors.append(-0.3)
            
            # 勝投比例
            wins = sum(1 for start in recent_starts if start.get('decision') == 'W')
            win_rate = wins / len(recent_starts)
            
            if win_rate >= 0.8:
                momentum_factors.append(0.2)
            elif win_rate <= 0.2:
                momentum_factors.append(-0.2)
            
            # 近期表現趨勢
            eras = [start.get('era', 4.50) for start in recent_starts]
            if len(eras) >= 3:
                trend_slope = np.polyfit(range(len(eras)), eras, 1)[0]
                if trend_slope < -0.5:  # ERA 下降趨勢
                    momentum_factors.append(0.2)
                elif trend_slope > 0.5:  # ERA 上升趨勢  
                    momentum_factors.append(-0.2)
            
            return np.sum(momentum_factors) if momentum_factors else 0.0
            
        except Exception as e:
            logger.error(f"投手動量計算失敗: {e}")
            return 0.0
    
    def _analyze_opponent_history(self, pitcher_data: Dict, opposing_lineups: Dict = None) -> Dict:
        """分析對戰歷史"""
        try:
            if not opposing_lineups:
                return {
                    'vs_opponent_available': False,
                    'vs_opponent_era': None,
                    'historical_advantage': 0.0
                }
            
            # 獲取對戰歷史數據
            opponent_history = pitcher_data.get('vs_opponent_history', {})
            
            if not opponent_history:
                return {
                    'vs_opponent_available': False,
                    'vs_opponent_era': None,
                    'historical_advantage': 0.0
                }
            
            # 計算對特定球隊的表現
            vs_era = opponent_history.get('era', pitcher_data.get('era', 4.50))
            vs_whip = opponent_history.get('whip', pitcher_data.get('whip', 1.35))
            vs_innings = opponent_history.get('innings_pitched', 0)
            
            # 計算相對優勢 (與整體表現比較)
            era_advantage = (pitcher_data.get('era', 4.50) - vs_era) / pitcher_data.get('era', 4.50)
            whip_advantage = (pitcher_data.get('whip', 1.35) - vs_whip) / pitcher_data.get('whip', 1.35)
            
            historical_advantage = (era_advantage + whip_advantage) / 2
            
            return {
                'vs_opponent_available': True,
                'vs_opponent_era': vs_era,
                'vs_opponent_whip': vs_whip,
                'vs_opponent_innings': vs_innings,
                'historical_advantage': historical_advantage,
                'sample_size_adequate': vs_innings >= 18.0  # 至少3次先發
            }
            
        except Exception as e:
            logger.error(f"對戰歷史分析失敗: {e}")
            return {
                'vs_opponent_available': False,
                'vs_opponent_era': None,
                'historical_advantage': 0.0
            }
    
    def _analyze_situational_performance(self, pitcher_data: Dict, game_context: Dict = None) -> Dict:
        """分析情境表現"""
        try:
            situational_stats = {
                'home_away_split': self._analyze_home_away_split(pitcher_data),
                'day_night_split': self._analyze_day_night_split(pitcher_data),
                'rest_impact': self._analyze_rest_impact(pitcher_data, game_context),
                'weather_impact': self._analyze_weather_impact(pitcher_data, game_context),
                'season_timing': self._analyze_season_timing(pitcher_data, game_context)
            }
            
            return situational_stats
            
        except Exception as e:
            logger.error(f"情境表現分析失敗: {e}")
            return {}
    
    def _analyze_home_away_split(self, pitcher_data: Dict) -> Dict:
        """分析主客場表現差異"""
        try:
            home_stats = pitcher_data.get('home_stats', {})
            away_stats = pitcher_data.get('away_stats', {})
            
            if not home_stats or not away_stats:
                return {'split_available': False}
            
            home_era = home_stats.get('era', 4.50)
            away_era = away_stats.get('era', 4.50)
            
            era_diff = home_era - away_era  # 正值表示主場表現較差
            
            return {
                'split_available': True,
                'home_era': home_era,
                'away_era': away_era,
                'era_difference': era_diff,
                'home_advantage': era_diff < -0.25,  # 主場 ERA 低於客場 0.25
                'significant_split': abs(era_diff) > 0.5
            }
            
        except Exception as e:
            logger.error(f"主客場分析失敗: {e}")
            return {'split_available': False}
    
    def _analyze_day_night_split(self, pitcher_data: Dict) -> Dict:
        """分析日夜場表現差異"""
        try:
            day_stats = pitcher_data.get('day_stats', {})
            night_stats = pitcher_data.get('night_stats', {})
            
            if not day_stats or not night_stats:
                return {'split_available': False}
            
            day_era = day_stats.get('era', 4.50)
            night_era = night_stats.get('era', 4.50)
            
            era_diff = day_era - night_era  # 正值表示日場表現較差
            
            return {
                'split_available': True,
                'day_era': day_era,
                'night_era': night_era,
                'era_difference': era_diff,
                'night_preference': era_diff > 0.25,
                'day_preference': era_diff < -0.25
            }
            
        except Exception as e:
            logger.error(f"日夜場分析失敗: {e}")
            return {'split_available': False}
    
    def _analyze_rest_impact(self, pitcher_data: Dict, game_context: Dict = None) -> Dict:
        """分析休息日數影響"""
        try:
            if not game_context:
                return {'rest_analysis_available': False}
            
            last_start_date = pitcher_data.get('last_start_date')
            game_date = game_context.get('game_date')
            
            if not last_start_date or not game_date:
                return {'rest_analysis_available': False}
            
            # 計算休息天數
            if isinstance(last_start_date, str):
                last_start_date = datetime.strptime(last_start_date, '%Y-%m-%d').date()
            if isinstance(game_date, str):
                game_date = datetime.strptime(game_date, '%Y-%m-%d').date()
            
            rest_days = (game_date - last_start_date).days
            
            # 評估休息狀況
            rest_category = 'normal_rest'
            expected_impact = 0.0
            
            for category, (threshold, impact) in self.situational_factors['rest_days'].items():
                if rest_days <= threshold:
                    rest_category = category
                    expected_impact = impact
                    break
            
            return {
                'rest_analysis_available': True,
                'rest_days': rest_days,
                'rest_category': rest_category,
                'expected_impact': expected_impact,
                'well_rested': rest_days >= 5,
                'short_rest': rest_days <= 3
            }
            
        except Exception as e:
            logger.error(f"休息影響分析失敗: {e}")
            return {'rest_analysis_available': False}
    
    def _analyze_weather_impact(self, pitcher_data: Dict, game_context: Dict = None) -> Dict:
        """分析天氣影響"""
        try:
            if not game_context:
                return {'weather_analysis_available': False}
            
            weather = game_context.get('weather', {})
            venue = game_context.get('venue', '')
            
            impact_factors = []
            
            # 溫度影響
            temperature = weather.get('temperature')
            if temperature:
                if temperature > 85:  # 華氏 85 度以上
                    impact_factors.append(('hot_weather', -0.02))  # 高溫對投手略不利
                elif temperature < 50:  # 華氏 50 度以下
                    impact_factors.append(('cold_weather', -0.01))  # 低溫對控球影響
            
            # 風向風速影響
            wind_speed = weather.get('wind_speed', 0)
            wind_direction = weather.get('wind_direction', '')
            
            if wind_speed > 15:  # 強風
                impact_factors.append(('strong_wind', -0.01))
                
            # 降雨影響
            precipitation = weather.get('precipitation', 0)
            if precipitation > 0:
                impact_factors.append(('rain_impact', -0.03))
            
            # 室內球場無天氣影響
            indoor_venues = ['HOU', 'TB', 'MIN', 'ARI', 'SEA', 'MIA', 'TOR']
            if any(venue_code in venue for venue_code in indoor_venues):
                impact_factors = [('indoor_venue', 0.0)]
            
            total_impact = sum(impact for _, impact in impact_factors)
            
            return {
                'weather_analysis_available': True,
                'weather_factors': impact_factors,
                'total_weather_impact': total_impact,
                'indoor_venue': any(venue_code in venue for venue_code in indoor_venues)
            }
            
        except Exception as e:
            logger.error(f"天氣影響分析失敗: {e}")
            return {'weather_analysis_available': False}
    
    def _analyze_season_timing(self, pitcher_data: Dict, game_context: Dict = None) -> Dict:
        """分析賽季時機"""
        try:
            if not game_context:
                return {'timing_analysis_available': False}
            
            game_date = game_context.get('game_date')
            if not game_date:
                return {'timing_analysis_available': False}
            
            if isinstance(game_date, str):
                game_date = datetime.strptime(game_date, '%Y-%m-%d').date()
            
            month = game_date.month
            
            # 賽季階段影響
            season_phase = 'regular'
            phase_impact = 0.0
            
            if month in [3, 4]:  # 賽季初期
                season_phase = 'early_season'
                phase_impact = -0.01  # 投手可能未完全熱身
            elif month in [9, 10]:  # 賽季後期
                season_phase = 'late_season'
                phase_impact = -0.02  # 疲勞累積
            elif month in [5, 6, 7, 8]:  # 賽季中期
                season_phase = 'mid_season'
                phase_impact = 0.01  # 最佳狀態
            
            # 工作負荷影響 (累積局數)
            innings_pitched = pitcher_data.get('innings_pitched', 0)
            workload_impact = 0.0
            
            if innings_pitched > 180:  # 高工作負荷
                workload_impact = -0.02
            elif innings_pitched > 160:
                workload_impact = -0.01
            
            return {
                'timing_analysis_available': True,
                'season_phase': season_phase,
                'phase_impact': phase_impact,
                'workload_impact': workload_impact,
                'total_timing_impact': phase_impact + workload_impact
            }
            
        except Exception as e:
            logger.error(f"賽季時機分析失敗: {e}")
            return {'timing_analysis_available': False}
    
    def _calculate_pitcher_rating(self, analysis: Dict) -> float:
        """計算投手綜合評分"""
        try:
            rating_components = []
            
            # ERA 表現 (25%)
            season_stats = analysis.get('current_season_stats', {})
            era_rating = season_stats.get('performance_ratings', {}).get('era_rating', 'average')
            era_score = {'excellent': 1.0, 'good': 0.8, 'average': 0.6, 'poor': 0.4}.get(era_rating, 0.6)
            rating_components.append(('era_performance', era_score, self.evaluation_weights['era_performance']))
            
            # WHIP 控制力 (20%)
            whip_rating = season_stats.get('performance_ratings', {}).get('whip_rating', 'average')
            whip_score = {'excellent': 1.0, 'good': 0.8, 'average': 0.6, 'poor': 0.4}.get(whip_rating, 0.6)
            rating_components.append(('whip_control', whip_score, self.evaluation_weights['whip_control']))
            
            # 三振能力 (15%)
            k9_rating = season_stats.get('performance_ratings', {}).get('k9_rating', 'average')
            k9_score = {'excellent': 1.0, 'good': 0.8, 'average': 0.6, 'poor': 0.4}.get(k9_rating, 0.6)
            rating_components.append(('strikeout_ability', k9_score, self.evaluation_weights['strikeout_ability']))
            
            # 近期狀態 (15%)
            recent_form = analysis.get('recent_form', {})
            form_rating = recent_form.get('form_rating', 'average')
            form_score = {'excellent': 1.0, 'good': 0.8, 'average': 0.6, 'poor': 0.4}.get(form_rating, 0.6)
            
            # 動量調整
            momentum = recent_form.get('momentum', 0.0)
            form_score += momentum * 0.1  # 動量影響10%
            form_score = np.clip(form_score, 0.0, 1.0)
            
            rating_components.append(('recent_form', form_score, self.evaluation_weights['recent_form']))
            
            # 對戰歷史 (15%)
            opponent_history = analysis.get('opponent_history', {})
            if opponent_history.get('vs_opponent_available', False):
                historical_advantage = opponent_history.get('historical_advantage', 0.0)
                history_score = 0.6 + historical_advantage  # 基準0.6，根據優勢調整
                history_score = np.clip(history_score, 0.2, 1.0)
            else:
                history_score = 0.6  # 無歷史數據時使用平均值
            
            rating_components.append(('opponent_history', history_score, self.evaluation_weights['opponent_history']))
            
            # 情境統計 (10%)
            situational_score = 0.6  # 基準分數
            situational_performance = analysis.get('situational_performance', {})
            
            # 主客場適應性
            home_away = situational_performance.get('home_away_split', {})
            if home_away.get('split_available', False):
                if not home_away.get('significant_split', False):
                    situational_score += 0.1  # 主客場表現穩定
            
            # 休息日數適應性
            rest_impact = situational_performance.get('rest_impact', {})
            if rest_impact.get('rest_analysis_available', False):
                if rest_impact.get('well_rested', False):
                    situational_score += 0.05
                elif rest_impact.get('short_rest', False):
                    situational_score -= 0.1
            
            situational_score = np.clip(situational_score, 0.2, 1.0)
            rating_components.append(('situational_stats', situational_score, self.evaluation_weights['situational_stats']))
            
            # 計算加權平均
            total_rating = sum(score * weight for _, score, weight in rating_components)
            
            return np.clip(total_rating, 0.0, 1.0)
            
        except Exception as e:
            logger.error(f"投手評分計算失敗: {e}")
            return 0.6  # 返回平均評分
    
    def _identify_strengths_weaknesses(self, analysis: Dict) -> Tuple[List[str], List[str]]:
        """識別投手優勢和弱點"""
        try:
            strengths = []
            weaknesses = []
            
            # 檢查各項統計表現
            season_stats = analysis.get('current_season_stats', {})
            ratings = season_stats.get('performance_ratings', {})
            
            # 優勢識別
            if ratings.get('era_rating') in ['excellent', 'good']:
                strengths.append('出色的防禦率')
            if ratings.get('whip_rating') in ['excellent', 'good']:
                strengths.append('良好的控球能力')
            if ratings.get('k9_rating') in ['excellent', 'good']:
                strengths.append('強力三振能力')
            
            # 弱點識別
            if ratings.get('era_rating') == 'poor':
                weaknesses.append('防禦率偏高')
            if ratings.get('whip_rating') == 'poor':
                weaknesses.append('控球不穩定')
            if ratings.get('k9_rating') == 'poor':
                weaknesses.append('三振能力不足')
            
            # 近期狀態
            recent_form = analysis.get('recent_form', {})
            if recent_form.get('trend') == 'improving':
                strengths.append('近期狀態上升')
            elif recent_form.get('trend') == 'declining':
                weaknesses.append('近期狀態下滑')
            
            # 一致性
            consistency = season_stats.get('consistency', 0.5)
            if consistency >= 0.8:
                strengths.append('表現穩定一致')
            elif consistency <= 0.3:
                weaknesses.append('表現起伏較大')
            
            return strengths, weaknesses
            
        except Exception as e:
            logger.error(f"優劣勢識別失敗: {e}")
            return [], []
    
    def _identify_risk_factors(self, analysis: Dict, game_context: Dict = None) -> List[str]:
        """識別風險因子"""
        try:
            risk_factors = []
            
            # 工作負荷風險
            season_stats = analysis.get('current_season_stats', {})
            if season_stats.get('workload') == 'heavy':
                risk_factors.append('工作負荷過重')
            
            # 近期表現風險
            recent_form = analysis.get('recent_form', {})
            if recent_form.get('form_rating') == 'poor':
                risk_factors.append('近期表現不佳')
            
            momentum = recent_form.get('momentum', 0.0)
            if momentum < -0.2:
                risk_factors.append('負面動量明顯')
            
            # 休息不足風險
            situational_performance = analysis.get('situational_performance', {})
            rest_impact = situational_performance.get('rest_impact', {})
            if rest_impact.get('short_rest', False):
                risk_factors.append('休息時間不足')
            
            # 天氣風險
            weather_impact = situational_performance.get('weather_impact', {})
            if weather_impact.get('total_weather_impact', 0) < -0.02:
                risk_factors.append('不利天氣條件')
            
            # 賽季疲勞風險
            season_timing = situational_performance.get('season_timing', {})
            if season_timing.get('total_timing_impact', 0) < -0.03:
                risk_factors.append('賽季疲勞累積')
            
            return risk_factors
            
        except Exception as e:
            logger.error(f"風險因子識別失敗: {e}")
            return []
    
    def _compare_pitcher_matchup(self, home_analysis: Dict, away_analysis: Dict) -> Dict:
        """比較投手對戰"""
        try:
            home_rating = home_analysis.get('overall_rating', 0.6)
            away_rating = away_analysis.get('overall_rating', 0.6)
            
            rating_difference = home_rating - away_rating
            
            # 確定優勢方
            if abs(rating_difference) < 0.1:
                advantage = 'balanced'
                advantage_magnitude = 'slight'
            elif rating_difference > 0:
                advantage = 'home'
                if rating_difference > 0.3:
                    advantage_magnitude = 'significant'
                elif rating_difference > 0.2:
                    advantage_magnitude = 'moderate'
                else:
                    advantage_magnitude = 'slight'
            else:
                advantage = 'away'
                rating_difference = abs(rating_difference)
                if rating_difference > 0.3:
                    advantage_magnitude = 'significant'
                elif rating_difference > 0.2:
                    advantage_magnitude = 'moderate'
                else:
                    advantage_magnitude = 'slight'
            
            # 比較具體統計
            comparison_details = self._detailed_pitcher_comparison(home_analysis, away_analysis)
            
            return {
                'pitcher_advantage': advantage,
                'advantage_magnitude': advantage_magnitude,
                'rating_difference': abs(rating_difference),
                'home_pitcher_rating': home_rating,
                'away_pitcher_rating': away_rating,
                'comparison_details': comparison_details,
                'matchup_type': self._classify_matchup_type(home_analysis, away_analysis)
            }
            
        except Exception as e:
            logger.error(f"投手對戰比較失敗: {e}")
            return {
                'pitcher_advantage': 'balanced',
                'advantage_magnitude': 'slight',
                'rating_difference': 0.0
            }
    
    def _detailed_pitcher_comparison(self, home_analysis: Dict, away_analysis: Dict) -> Dict:
        """詳細投手比較"""
        try:
            comparison = {}
            
            # 比較主要統計
            home_stats = home_analysis.get('current_season_stats', {}).get('raw_stats', {})
            away_stats = away_analysis.get('current_season_stats', {}).get('raw_stats', {})
            
            for stat in ['era', 'whip', 'k9', 'hr9']:
                home_value = home_stats.get(stat, 0)
                away_value = away_stats.get(stat, 0)
                
                if stat in ['era', 'whip', 'hr9']:  # 越小越好
                    if home_value < away_value:
                        comparison[stat] = 'home_better'
                    elif away_value < home_value:
                        comparison[stat] = 'away_better'
                    else:
                        comparison[stat] = 'similar'
                else:  # k9 越大越好
                    if home_value > away_value:
                        comparison[stat] = 'home_better'
                    elif away_value > home_value:
                        comparison[stat] = 'away_better'
                    else:
                        comparison[stat] = 'similar'
            
            # 比較近期狀態
            home_form = home_analysis.get('recent_form', {}).get('form_rating', 'average')
            away_form = away_analysis.get('recent_form', {}).get('form_rating', 'average')
            
            form_order = {'poor': 0, 'average': 1, 'good': 2, 'excellent': 3}
            home_form_score = form_order.get(home_form, 1)
            away_form_score = form_order.get(away_form, 1)
            
            if home_form_score > away_form_score:
                comparison['recent_form'] = 'home_better'
            elif away_form_score > home_form_score:
                comparison['recent_form'] = 'away_better'
            else:
                comparison['recent_form'] = 'similar'
            
            return comparison
            
        except Exception as e:
            logger.error(f"詳細比較失敗: {e}")
            return {}
    
    def _classify_matchup_type(self, home_analysis: Dict, away_analysis: Dict) -> str:
        """分類對戰類型"""
        try:
            home_type = home_analysis.get('pitcher_type', 'average')
            away_type = away_analysis.get('pitcher_type', 'average')
            
            # 王牌對戰
            if home_type == 'ace' and away_type == 'ace':
                return 'ace_vs_ace'
            elif home_type == 'ace' or away_type == 'ace':
                return 'ace_vs_normal'
            
            # 優質投手對戰
            elif home_type == 'quality' and away_type == 'quality':
                return 'quality_vs_quality'
            elif home_type == 'quality' or away_type == 'quality':
                return 'quality_vs_normal'
            
            # 一般對戰
            elif home_type == 'average' and away_type == 'average':
                return 'average_vs_average'
            
            # 弱投對戰
            elif home_type == 'below_average' or away_type == 'below_average':
                return 'weak_pitcher_involved'
            
            return 'mixed_matchup'
            
        except Exception as e:
            logger.error(f"對戰類型分類失敗: {e}")
            return 'average_matchup'
    
    def _estimate_runs_impact(self, 
                             home_analysis: Dict, 
                             away_analysis: Dict,
                             opposing_lineups: Dict = None,
                             game_context: Dict = None) -> Dict:
        """估算得分影響"""
        try:
            # 基於投手評分計算預期得分影響
            home_rating = home_analysis.get('overall_rating', 0.6)
            away_rating = away_analysis.get('overall_rating', 0.6)
            
            # MLB 平均每場得分約 4.5 分
            base_runs = 4.5
            
            # 投手評分影響得分 (評分越高，失分越少)
            home_pitcher_effect = (0.6 - home_rating) * 3.0  # 主隊投手對客隊得分影響
            away_pitcher_effect = (0.6 - away_rating) * 3.0  # 客隊投手對主隊得分影響
            
            expected_home_runs = base_runs + away_pitcher_effect
            expected_away_runs = base_runs + home_pitcher_effect
            
            # 確保得分合理
            expected_home_runs = max(1.0, min(expected_home_runs, 12.0))
            expected_away_runs = max(1.0, min(expected_away_runs, 12.0))
            
            # 計算投手優勢對總分的影響
            total_expected = expected_home_runs + expected_away_runs
            base_total = base_runs * 2
            
            runs_impact = {
                'expected_home_runs': expected_home_runs,
                'expected_away_runs': expected_away_runs,
                'total_expected_runs': total_expected,
                'runs_impact_vs_average': total_expected - base_total,
                'pitcher_effect_summary': self._summarize_pitcher_effect(
                    home_analysis, away_analysis, expected_home_runs, expected_away_runs
                )
            }
            
            return runs_impact
            
        except Exception as e:
            logger.error(f"得分影響估算失敗: {e}")
            return {
                'expected_home_runs': 4.5,
                'expected_away_runs': 4.5,
                'total_expected_runs': 9.0,
                'runs_impact_vs_average': 0.0
            }
    
    def _summarize_pitcher_effect(self, 
                                 home_analysis: Dict, 
                                 away_analysis: Dict,
                                 expected_home: float,
                                 expected_away: float) -> str:
        """總結投手影響效果"""
        try:
            home_name = home_analysis.get('pitcher_name', '主隊投手')
            away_name = away_analysis.get('pitcher_name', '客隊投手')
            
            home_rating = home_analysis.get('overall_rating', 0.6)
            away_rating = away_analysis.get('overall_rating', 0.6)
            
            if home_rating > away_rating + 0.2:
                summary = f"{home_name} 具明顯優勢，預期壓制客隊打線"
            elif away_rating > home_rating + 0.2:
                summary = f"{away_name} 具明顯優勢，預期壓制主隊打線"
            elif expected_home + expected_away < 8.0:
                summary = "雙方投手實力均衡，預期為低比分比賽"
            elif expected_home + expected_away > 10.0:
                summary = "投手實力相對較弱，預期為高比分比賽"
            else:
                summary = "投手對戰勢均力敵，預期正常比分"
            
            return summary
            
        except Exception as e:
            logger.error(f"投手效果總結失敗: {e}")
            return "投手影響分析無法生成"
    
    def _calculate_pitcher_advantage(self, 
                                   home_analysis: Dict, 
                                   away_analysis: Dict,
                                   matchup_comparison: Dict) -> float:
        """計算投手優勢"""
        try:
            # 基於評分差異計算優勢
            home_rating = home_analysis.get('overall_rating', 0.6)
            away_rating = away_analysis.get('overall_rating', 0.6)
            
            rating_advantage = home_rating - away_rating
            
            # 正規化到 -1 到 1 範圍
            # 正值表示主隊投手優勢，負值表示客隊投手優勢
            normalized_advantage = np.tanh(rating_advantage * 3)  # 使用 tanh 進行軟性正規化
            
            return normalized_advantage
            
        except Exception as e:
            logger.error(f"投手優勢計算失敗: {e}")
            return 0.0
    
    def _generate_analysis_summary(self, 
                                  matchup_comparison: Dict, 
                                  pitcher_advantage: float,
                                  runs_impact: Dict) -> str:
        """生成分析摘要"""
        try:
            advantage = matchup_comparison.get('pitcher_advantage', 'balanced')
            magnitude = matchup_comparison.get('advantage_magnitude', 'slight')
            matchup_type = matchup_comparison.get('matchup_type', 'average_matchup')
            
            # 構建摘要
            if advantage == 'balanced':
                advantage_text = "投手實力均衡"
            elif advantage == 'home':
                advantage_text = f"主隊投手具{magnitude}優勢"
            else:  # away
                advantage_text = f"客隊投手具{magnitude}優勢"
            
            # 對戰類型描述
            matchup_descriptions = {
                'ace_vs_ace': '王牌投手對決',
                'ace_vs_normal': '王牌對一般投手',
                'quality_vs_quality': '優質投手對戰',
                'quality_vs_normal': '優質對一般投手',
                'average_vs_average': '一般投手對戰',
                'weak_pitcher_involved': '有弱投手參與',
                'mixed_matchup': '混合類型對戰'
            }
            
            matchup_text = matchup_descriptions.get(matchup_type, '投手對戰')
            
            # 得分預期
            total_expected = runs_impact.get('total_expected_runs', 9.0)
            if total_expected < 8.0:
                score_expectation = "預期低比分"
            elif total_expected > 10.0:
                score_expectation = "預期高比分"
            else:
                score_expectation = "預期正常比分"
            
            summary = f"{matchup_text}，{advantage_text}，{score_expectation}（預期總分 {total_expected:.1f}）"
            
            return summary
            
        except Exception as e:
            logger.error(f"分析摘要生成失敗: {e}")
            return "投手分析摘要生成失敗"
    
    def _record_analysis(self, analysis_result: Dict):
        """記錄分析結果"""
        try:
            record = {
                'analysis': analysis_result.copy(),
                'timestamp': datetime.now()
            }
            
            self.analysis_history.append(record)
            
            # 保留最近500筆分析記錄
            if len(self.analysis_history) > 500:
                self.analysis_history = self.analysis_history[-500:]
                
        except Exception as e:
            logger.error(f"分析記錄失敗: {e}")
    
    def _generate_fallback_analysis(self) -> Dict:
        """生成備用分析"""
        return {
            'home_pitcher_analysis': self._create_default_pitcher_analysis('home'),
            'away_pitcher_analysis': self._create_default_pitcher_analysis('away'),
            'matchup_comparison': {
                'pitcher_advantage': 'balanced',
                'advantage_magnitude': 'slight',
                'rating_difference': 0.0
            },
            'pitcher_advantage': 0.0,
            'expected_runs_impact': {
                'expected_home_runs': 4.5,
                'expected_away_runs': 4.5,
                'total_expected_runs': 9.0,
                'runs_impact_vs_average': 0.0
            },
            'analysis_summary': '投手分析數據不足，使用預設值',
            'timestamp': datetime.now().isoformat(),
            'fallback_mode': True
        }
    
    def _create_default_pitcher_analysis(self, team_type: str) -> Dict:
        """創建預設投手分析"""
        return {
            'pitcher_name': f'{team_type.upper()} 投手',
            'team_type': team_type,
            'pitcher_type': 'average',
            'current_season_stats': {
                'raw_stats': {'era': 4.50, 'whip': 1.35, 'k9': 7.0},
                'performance_ratings': {'era_rating': 'average', 'whip_rating': 'average', 'k9_rating': 'average'}
            },
            'recent_form': {
                'form_rating': 'average',
                'trend': 'stable',
                'momentum': 0.0,
                'data_available': False
            },
            'opponent_history': {
                'vs_opponent_available': False,
                'historical_advantage': 0.0
            },
            'situational_performance': {},
            'overall_rating': 0.6,
            'strengths': [],
            'weaknesses': [],
            'risk_factors': []
        }
    
    def get_analysis_report(self) -> Dict:
        """獲取分析報告"""
        try:
            return {
                'analysis_count': len(self.analysis_history),
                'evaluation_weights': self.evaluation_weights.copy(),
                'mlb_benchmarks': self.mlb_benchmarks.copy(),
                'pitcher_types': self.pitcher_types.copy(),
                'system_status': 'active'
            }
            
        except Exception as e:
            logger.error(f"分析報告生成失敗: {e}")
            return {'error': str(e)}