"""
MLB 動態特徵權重系統
根據比賽情境動態調整特徵權重

目標：
- 根據比賽情境調整特徵重要性
- 提高模型對不同情況的適應性
- 優化預測準確性
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import json
from collections import defaultdict

logger = logging.getLogger(__name__)

class DynamicFeatureWeights:
    """動態特徵權重系統 - 情境感知的權重調整"""
    
    def __init__(self):
        """初始化動態權重系統"""
        # 基礎特徵權重
        self.base_weights = {
            'team_stats': 0.25,        # 球隊統計
            'recent_form': 0.20,       # 近期狀態  
            'pitcher_matchup': 0.20,   # 投手對戰
            'home_advantage': 0.15,    # 主場優勢
            'head_to_head': 0.10,      # 對戰歷史
            'situational': 0.10        # 情境因子
        }
        
        # 情境調整規則
        self.context_adjustments = {
            'ace_pitcher_game': {
                'pitcher_matchup': +0.1,
                'team_stats': -0.05,
                'recent_form': -0.05,
                'description': '王牌投手比賽'
            },
            'playoff_race': {
                'recent_form': +0.1,
                'situational': +0.05,
                'head_to_head': -0.15,
                'description': '季後賽競爭'
            },
            'division_rivalry': {
                'head_to_head': +0.15,
                'home_advantage': +0.05,
                'team_stats': -0.1,
                'recent_form': -0.1,
                'description': '同區對戰'
            },
            'interleague_play': {
                'head_to_head': -0.08,
                'team_stats': +0.05,
                'pitcher_matchup': +0.03,
                'description': '跨聯盟比賽'
            },
            'rookie_pitcher': {
                'pitcher_matchup': -0.1,
                'recent_form': +0.05,
                'situational': +0.05,
                'description': '新人投手先發'
            },
            'bullpen_game': {
                'pitcher_matchup': -0.15,
                'recent_form': +0.08,
                'team_stats': +0.07,
                'description': '牛棚比賽'
            },
            'weather_factor': {
                'situational': +0.08,
                'home_advantage': +0.02,
                'team_stats': -0.05,
                'recent_form': -0.05,
                'description': '天氣因素影響'
            },
            'doubleheader': {
                'recent_form': +0.15,
                'situational': +0.05,
                'pitcher_matchup': -0.1,
                'team_stats': -0.1,
                'description': '雙重賽'
            },
            'season_start': {  # 賽季前30場
                'team_stats': -0.05,
                'recent_form': -0.05,
                'pitcher_matchup': +0.05,
                'situational': +0.05,
                'description': '賽季初期'
            },
            'season_end': {    # 賽季最後30場
                'recent_form': +0.1,
                'situational': +0.05,
                'team_stats': -0.15,
                'description': '賽季末期'
            }
        }
        
        # 球隊特性調整
        self.team_characteristics = {
            'power_team': {  # 高全壘打球隊
                'affected_features': ['team_stats', 'pitcher_matchup'],
                'weather_sensitive': True,
                'park_factor_important': True
            },
            'speed_team': {  # 高盜壘球隊
                'affected_features': ['recent_form', 'situational'],
                'weather_sensitive': True,
                'turf_sensitive': True
            },
            'pitching_team': {  # 投手導向球隊
                'affected_features': ['pitcher_matchup'],
                'boost_factor': 0.05
            },
            'streaky_team': {  # 起伏較大球隊
                'affected_features': ['recent_form'],
                'boost_factor': 0.08
            }
        }
        
        # 權重調整歷史
        self.adjustment_history = []
        
        # 性能追蹤
        self.performance_tracking = {
            'total_adjustments': 0,
            'accuracy_with_adjustments': [],
            'accuracy_without_adjustments': [],
            'best_performing_contexts': defaultdict(list)
        }
        
        logger.info("動態特徵權重系統初始化完成")
    
    def adjust_weights(self, game_context: Dict) -> Dict:
        """
        根據比賽情境調整特徵權重
        
        Args:
            game_context: 比賽情境資料
            
        Returns:
            調整後的權重字典
        """
        try:
            # 從基礎權重開始
            adjusted_weights = self.base_weights.copy()
            
            # 記錄應用的調整
            applied_adjustments = []
            
            # 步驟1: 應用情境調整
            context_adjustments = self._identify_contexts(game_context)
            for context_name in context_adjustments:
                adjustment = self._apply_context_adjustment(
                    adjusted_weights, context_name
                )
                if adjustment:
                    applied_adjustments.append(adjustment)
            
            # 步驟2: 應用球隊特性調整
            team_adjustments = self._apply_team_characteristics(
                adjusted_weights, game_context
            )
            applied_adjustments.extend(team_adjustments)
            
            # 步驟3: 應用時間和賽季調整
            temporal_adjustments = self._apply_temporal_adjustments(
                adjusted_weights, game_context
            )
            applied_adjustments.extend(temporal_adjustments)
            
            # 步驟4: 正規化權重
            adjusted_weights = self._normalize_weights(adjusted_weights)
            
            # 步驟5: 記錄調整歷史
            adjustment_record = {
                'original_weights': self.base_weights.copy(),
                'adjusted_weights': adjusted_weights.copy(),
                'applied_adjustments': applied_adjustments,
                'game_context': game_context.copy(),
                'timestamp': datetime.now()
            }
            self._record_adjustment(adjustment_record)
            
            logger.debug(f"權重調整完成，應用 {len(applied_adjustments)} 項調整")
            
            return adjusted_weights
            
        except Exception as e:
            logger.error(f"權重調整失敗: {e}")
            return self.base_weights.copy()
    
    def _identify_contexts(self, game_context: Dict) -> List[str]:
        """識別適用的情境"""
        try:
            identified_contexts = []
            
            # 王牌投手比賽
            if self._is_ace_pitcher_game(game_context):
                identified_contexts.append('ace_pitcher_game')
            
            # 季後賽競爭
            if self._is_playoff_race(game_context):
                identified_contexts.append('playoff_race')
            
            # 同區對戰
            if self._is_division_rivalry(game_context):
                identified_contexts.append('division_rivalry')
            
            # 跨聯盟比賽
            if self._is_interleague_play(game_context):
                identified_contexts.append('interleague_play')
            
            # 新人投手
            if self._has_rookie_pitcher(game_context):
                identified_contexts.append('rookie_pitcher')
            
            # 牛棚比賽
            if self._is_bullpen_game(game_context):
                identified_contexts.append('bullpen_game')
            
            # 天氣因素
            if self._has_weather_factor(game_context):
                identified_contexts.append('weather_factor')
            
            # 雙重賽
            if self._is_doubleheader(game_context):
                identified_contexts.append('doubleheader')
            
            # 賽季時期
            season_context = self._get_season_context(game_context)
            if season_context:
                identified_contexts.append(season_context)
            
            return identified_contexts
            
        except Exception as e:
            logger.error(f"情境識別失敗: {e}")
            return []
    
    def _is_ace_pitcher_game(self, context: Dict) -> bool:
        """判斷是否為王牌投手比賽"""
        try:
            pitcher_data = context.get('pitcher_data', {})
            home_pitcher = pitcher_data.get('home_pitcher', {})
            away_pitcher = pitcher_data.get('away_pitcher', {})
            
            # 王牌投手標準: ERA < 3.0, WHIP < 1.15, K9 > 9.0
            def is_ace(pitcher: Dict) -> bool:
                era = pitcher.get('era', 5.0)
                whip = pitcher.get('whip', 1.5)
                k9 = pitcher.get('k9', 5.0)
                return era < 3.0 and whip < 1.15 and k9 > 9.0
            
            return is_ace(home_pitcher) or is_ace(away_pitcher)
            
        except Exception as e:
            logger.error(f"王牌投手判斷失敗: {e}")
            return False
    
    def _is_playoff_race(self, context: Dict) -> bool:
        """判斷是否為季後賽競爭"""
        try:
            game_date = context.get('game_date')
            if not game_date:
                return False
            
            # 賽季後期 (8月之後) 且涉及競爭球隊
            if isinstance(game_date, str):
                game_date = datetime.strptime(game_date, '%Y-%m-%d').date()
            
            if game_date.month >= 8:  # 8月後
                # 檢查球隊是否在季後賽競爭中
                standings = context.get('standings', {})
                home_team = context.get('home_team', '')
                away_team = context.get('away_team', '')
                
                # 簡化判斷：假設前5名球隊都在競爭
                home_rank = standings.get(home_team, {}).get('division_rank', 6)
                away_rank = standings.get(away_team, {}).get('division_rank', 6)
                
                return home_rank <= 3 or away_rank <= 3
                
            return False
            
        except Exception as e:
            logger.error(f"季後賽競爭判斷失敗: {e}")
            return False
    
    def _is_division_rivalry(self, context: Dict) -> bool:
        """判斷是否為同區對戰"""
        try:
            home_team = context.get('home_team', '')
            away_team = context.get('away_team', '')
            
            # MLB 分區對應
            divisions = {
                'AL_East': ['BOS', 'NYY', 'TB', 'TOR', 'BAL'],
                'AL_Central': ['CLE', 'MIN', 'CWS', 'DET', 'KC'],
                'AL_West': ['HOU', 'SEA', 'TEX', 'LAA', 'OAK'],
                'NL_East': ['ATL', 'NYM', 'PHI', 'WSH', 'MIA'],
                'NL_Central': ['MIL', 'STL', 'CHC', 'CIN', 'PIT'],
                'NL_West': ['LAD', 'SD', 'SF', 'COL', 'ARI']
            }
            
            for division, teams in divisions.items():
                if home_team in teams and away_team in teams:
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"同區對戰判斷失敗: {e}")
            return False
    
    def _is_interleague_play(self, context: Dict) -> bool:
        """判斷是否為跨聯盟比賽"""
        try:
            home_team = context.get('home_team', '')
            away_team = context.get('away_team', '')
            
            # 美聯球隊
            al_teams = ['BOS', 'NYY', 'TB', 'TOR', 'BAL', 'CLE', 'MIN', 'CWS', 'DET', 'KC',
                       'HOU', 'SEA', 'TEX', 'LAA', 'OAK']
            
            # 國聯球隊  
            nl_teams = ['ATL', 'NYM', 'PHI', 'WSH', 'MIA', 'MIL', 'STL', 'CHC', 'CIN', 'PIT',
                       'LAD', 'SD', 'SF', 'COL', 'ARI']
            
            home_al = home_team in al_teams
            away_al = away_team in al_teams
            
            # 跨聯盟：一隊美聯一隊國聯
            return (home_al and not away_al) or (not home_al and away_al)
            
        except Exception as e:
            logger.error(f"跨聯盟判斷失敗: {e}")
            return False
    
    def _has_rookie_pitcher(self, context: Dict) -> bool:
        """判斷是否有新人投手"""
        try:
            pitcher_data = context.get('pitcher_data', {})
            home_pitcher = pitcher_data.get('home_pitcher', {})
            away_pitcher = pitcher_data.get('away_pitcher', {})
            
            # 新人標準：MLB 經驗 < 50 局
            home_rookie = home_pitcher.get('mlb_innings', 200) < 50
            away_rookie = away_pitcher.get('mlb_innings', 200) < 50
            
            return home_rookie or away_rookie
            
        except Exception as e:
            logger.error(f"新人投手判斷失敗: {e}")
            return False
    
    def _is_bullpen_game(self, context: Dict) -> bool:
        """判斷是否為牛棚比賽"""
        try:
            pitcher_data = context.get('pitcher_data', {})
            home_pitcher = pitcher_data.get('home_pitcher', {})
            away_pitcher = pitcher_data.get('away_pitcher', {})
            
            # 牛棚比賽標準：沒有指定先發投手或先發投手預期局數 < 3
            home_bullpen = (not home_pitcher.get('confirmed_starter', True) or 
                           home_pitcher.get('expected_innings', 5) < 3)
            away_bullpen = (not away_pitcher.get('confirmed_starter', True) or 
                           away_pitcher.get('expected_innings', 5) < 3)
            
            return home_bullpen or away_bullpen
            
        except Exception as e:
            logger.error(f"牛棚比賽判斷失敗: {e}")
            return False
    
    def _has_weather_factor(self, context: Dict) -> bool:
        """判斷是否有天氣影響"""
        try:
            weather = context.get('weather', {})
            
            # 顯著天氣因素
            temperature = weather.get('temperature', 70)
            wind_speed = weather.get('wind_speed', 5)
            precipitation = weather.get('precipitation_chance', 0)
            
            # 極端溫度、強風或降雨
            extreme_temp = temperature < 45 or temperature > 90
            strong_wind = wind_speed > 15
            rain_likely = precipitation > 30
            
            return extreme_temp or strong_wind or rain_likely
            
        except Exception as e:
            logger.error(f"天氣因素判斷失敗: {e}")
            return False
    
    def _is_doubleheader(self, context: Dict) -> bool:
        """判斷是否為雙重賽"""
        try:
            return context.get('doubleheader', False) or context.get('game_number', 1) > 1
            
        except Exception as e:
            logger.error(f"雙重賽判斷失敗: {e}")
            return False
    
    def _get_season_context(self, context: Dict) -> Optional[str]:
        """獲取賽季情境"""
        try:
            game_date = context.get('game_date')
            if not game_date:
                return None
            
            if isinstance(game_date, str):
                game_date = datetime.strptime(game_date, '%Y-%m-%d').date()
            
            # 賽季開始 (4月前30天)
            if game_date.month == 3 or (game_date.month == 4 and game_date.day <= 30):
                return 'season_start'
            
            # 賽季結束 (9月後)  
            if game_date.month >= 9:
                return 'season_end'
            
            return None
            
        except Exception as e:
            logger.error(f"賽季情境獲取失敗: {e}")
            return None
    
    def _apply_context_adjustment(self, weights: Dict, context_name: str) -> Optional[Dict]:
        """應用情境調整"""
        try:
            if context_name not in self.context_adjustments:
                return None
            
            adjustment_rules = self.context_adjustments[context_name]
            applied_changes = {}
            
            for feature, change in adjustment_rules.items():
                if feature in weights and isinstance(change, (int, float)):
                    old_weight = weights[feature]
                    weights[feature] += change
                    applied_changes[feature] = {'old': old_weight, 'new': weights[feature], 'change': change}
            
            return {
                'context': context_name,
                'description': adjustment_rules.get('description', context_name),
                'changes': applied_changes
            }
            
        except Exception as e:
            logger.error(f"情境調整應用失敗: {e}")
            return None
    
    def _apply_team_characteristics(self, weights: Dict, context: Dict) -> List[Dict]:
        """應用球隊特性調整"""
        try:
            adjustments = []
            
            home_team = context.get('home_team', '')
            away_team = context.get('away_team', '')
            
            # 獲取球隊特性
            home_characteristics = self._get_team_characteristics(home_team, context)
            away_characteristics = self._get_team_characteristics(away_team, context)
            
            all_characteristics = home_characteristics + away_characteristics
            
            for char_type in set(all_characteristics):
                if char_type in self.team_characteristics:
                    char_config = self.team_characteristics[char_type]
                    boost_factor = char_config.get('boost_factor', 0.03)
                    
                    for feature in char_config.get('affected_features', []):
                        if feature in weights:
                            old_weight = weights[feature]
                            weights[feature] += boost_factor
                            
                            adjustments.append({
                                'type': 'team_characteristic',
                                'characteristic': char_type,
                                'feature': feature,
                                'old_weight': old_weight,
                                'new_weight': weights[feature],
                                'change': boost_factor
                            })
            
            return adjustments
            
        except Exception as e:
            logger.error(f"球隊特性調整失敗: {e}")
            return []
    
    def _get_team_characteristics(self, team: str, context: Dict) -> List[str]:
        """獲取球隊特性"""
        try:
            characteristics = []
            
            # 從統計數據推斷特性
            team_stats = context.get('team_stats', {}).get(team, {})
            
            # 強力球隊 (高全壘打)
            hr_per_game = team_stats.get('home_runs_per_game', 1.0)
            if hr_per_game > 1.5:
                characteristics.append('power_team')
            
            # 速度球隊 (高盜壘)
            sb_per_game = team_stats.get('stolen_bases_per_game', 0.5)
            if sb_per_game > 1.0:
                characteristics.append('speed_team')
            
            # 投手導向球隊 (低 ERA)
            team_era = team_stats.get('era', 4.50)
            if team_era < 3.50:
                characteristics.append('pitching_team')
            
            # 起伏球隊 (高變異性)
            recent_form_variance = context.get('recent_form_variance', {}).get(team, 0.3)
            if recent_form_variance > 0.5:
                characteristics.append('streaky_team')
            
            return characteristics
            
        except Exception as e:
            logger.error(f"球隊特性獲取失敗: {e}")
            return []
    
    def _apply_temporal_adjustments(self, weights: Dict, context: Dict) -> List[Dict]:
        """應用時間調整"""
        try:
            adjustments = []
            
            game_date = context.get('game_date')
            if not game_date:
                return adjustments
            
            if isinstance(game_date, str):
                game_date = datetime.strptime(game_date, '%Y-%m-%d').date()
            
            # 平日 vs 週末調整
            if game_date.weekday() >= 5:  # 週末
                old_home = weights.get('home_advantage', 0.15)
                weights['home_advantage'] += 0.01  # 週末主場優勢略增
                
                adjustments.append({
                    'type': 'temporal',
                    'factor': 'weekend_game',
                    'feature': 'home_advantage',
                    'old_weight': old_home,
                    'new_weight': weights['home_advantage'],
                    'change': 0.01
                })
            
            # 月份調整
            month_adjustments = {
                4: {'recent_form': -0.02, 'description': '4月賽季初'},  # 賽季初
                8: {'recent_form': +0.03, 'description': '8月關鍵期'},  # 交易截止後
                9: {'situational': +0.05, 'description': '9月季後賽競爭'}  # 季後賽競爭
            }
            
            if game_date.month in month_adjustments:
                month_adj = month_adjustments[game_date.month]
                for feature, change in month_adj.items():
                    if feature in weights and isinstance(change, (int, float)):
                        old_weight = weights[feature]
                        weights[feature] += change
                        
                        adjustments.append({
                            'type': 'temporal',
                            'factor': f'month_{game_date.month}',
                            'feature': feature,
                            'old_weight': old_weight,
                            'new_weight': weights[feature],
                            'change': change,
                            'description': month_adj.get('description', f'月份{game_date.month}調整')
                        })
            
            return adjustments
            
        except Exception as e:
            logger.error(f"時間調整失敗: {e}")
            return []
    
    def _normalize_weights(self, weights: Dict) -> Dict:
        """正規化權重確保總和為1"""
        try:
            total_weight = sum(weights.values())
            
            if total_weight == 0:
                return self.base_weights.copy()
            
            normalized_weights = {
                feature: weight / total_weight
                for feature, weight in weights.items()
            }
            
            # 確保沒有負權重
            for feature in normalized_weights:
                normalized_weights[feature] = max(0.01, normalized_weights[feature])  # 最小1%
            
            # 重新正規化
            total_weight = sum(normalized_weights.values())
            normalized_weights = {
                feature: weight / total_weight
                for feature, weight in normalized_weights.items()
            }
            
            return normalized_weights
            
        except Exception as e:
            logger.error(f"權重正規化失敗: {e}")
            return self.base_weights.copy()
    
    def _record_adjustment(self, adjustment_record: Dict):
        """記錄調整歷史"""
        try:
            self.adjustment_history.append(adjustment_record)
            self.performance_tracking['total_adjustments'] += 1
            
            # 保留最近1000筆記錄
            if len(self.adjustment_history) > 1000:
                self.adjustment_history = self.adjustment_history[-1000:]
                
        except Exception as e:
            logger.error(f"調整記錄失敗: {e}")
    
    def evaluate_adjustment_effectiveness(self, predictions: List[Dict], actuals: List[Dict]):
        """評估調整效果"""
        try:
            if len(predictions) != len(actuals):
                logger.warning("預測和實際結果數量不符")
                return
            
            # 分組統計：有調整 vs 無調整
            with_adjustments = []
            without_adjustments = []
            
            for pred, actual in zip(predictions, actuals):
                accuracy = 1 if pred.get('correct') else 0
                
                if pred.get('weight_adjustments_applied', False):
                    with_adjustments.append(accuracy)
                else:
                    without_adjustments.append(accuracy)
            
            # 更新性能追蹤
            if with_adjustments:
                avg_with = np.mean(with_adjustments)
                self.performance_tracking['accuracy_with_adjustments'].append(avg_with)
            
            if without_adjustments:
                avg_without = np.mean(without_adjustments)
                self.performance_tracking['accuracy_without_adjustments'].append(avg_without)
            
            # 記錄最佳表現情境
            for pred in predictions:
                if pred.get('correct') and pred.get('weight_adjustments_applied'):
                    contexts = pred.get('applied_contexts', [])
                    for context in contexts:
                        self.performance_tracking['best_performing_contexts'][context].append(1)
            
            # 輸出評估結果
            self._log_effectiveness_summary()
            
        except Exception as e:
            logger.error(f"調整效果評估失敗: {e}")
    
    def _log_effectiveness_summary(self):
        """記錄效果摘要"""
        try:
            with_adj = self.performance_tracking['accuracy_with_adjustments']
            without_adj = self.performance_tracking['accuracy_without_adjustments']
            
            if with_adj and without_adj:
                avg_with = np.mean(with_adj[-50:])  # 最近50次
                avg_without = np.mean(without_adj[-50:])
                improvement = avg_with - avg_without
                
                logger.info(f"動態權重效果 - 有調整: {avg_with:.3f}, 無調整: {avg_without:.3f}, 改善: {improvement:+.3f}")
            
            # 最佳表現情境
            best_contexts = self.performance_tracking['best_performing_contexts']
            if best_contexts:
                top_contexts = sorted(best_contexts.items(), 
                                    key=lambda x: len(x[1]), 
                                    reverse=True)[:3]
                
                logger.info("最佳表現情境:")
                for context, results in top_contexts:
                    accuracy = np.mean(results) if results else 0
                    logger.info(f"  {context}: {accuracy:.3f} ({len(results)}次)")
                    
        except Exception as e:
            logger.error(f"效果摘要記錄失敗: {e}")
    
    def get_weights_report(self) -> Dict:
        """獲取權重系統報告"""
        try:
            # 計算調整統計
            adjustment_stats = defaultdict(int)
            for record in self.adjustment_history[-100:]:  # 最近100次
                for adj in record.get('applied_adjustments', []):
                    if isinstance(adj, dict) and 'context' in adj:
                        adjustment_stats[adj['context']] += 1
                    elif isinstance(adj, dict) and 'characteristic' in adj:
                        adjustment_stats[adj['characteristic']] += 1
            
            # 性能統計
            with_adj = self.performance_tracking['accuracy_with_adjustments']
            without_adj = self.performance_tracking['accuracy_without_adjustments']
            
            performance_summary = {
                'with_adjustments': {
                    'count': len(with_adj),
                    'avg_accuracy': np.mean(with_adj[-50:]) if with_adj else 0.0,
                    'recent_trend': np.mean(with_adj[-10:]) - np.mean(with_adj[-20:-10]) if len(with_adj) >= 20 else 0.0
                },
                'without_adjustments': {
                    'count': len(without_adj),
                    'avg_accuracy': np.mean(without_adj[-50:]) if without_adj else 0.0,
                    'recent_trend': np.mean(without_adj[-10:]) - np.mean(without_adj[-20:-10]) if len(without_adj) >= 20 else 0.0
                }
            }
            
            if with_adj and without_adj:
                performance_summary['improvement'] = (
                    performance_summary['with_adjustments']['avg_accuracy'] - 
                    performance_summary['without_adjustments']['avg_accuracy']
                )
            else:
                performance_summary['improvement'] = 0.0
            
            return {
                'base_weights': self.base_weights.copy(),
                'total_adjustments': self.performance_tracking['total_adjustments'],
                'adjustment_frequency': dict(adjustment_stats),
                'performance_summary': performance_summary,
                'available_contexts': list(self.context_adjustments.keys()),
                'team_characteristics': list(self.team_characteristics.keys()),
                'system_status': 'active'
            }
            
        except Exception as e:
            logger.error(f"權重報告生成失敗: {e}")
            return {'error': str(e)}
    
    def optimize_weights(self, validation_data: List[Dict]) -> bool:
        """優化基礎權重"""
        try:
            if len(validation_data) < 100:
                logger.warning("驗證數據不足，無法優化權重")
                return False
            
            logger.info(f"開始優化基礎權重，使用 {len(validation_data)} 筆驗證數據")
            
            # 記錄當前表現
            current_performance = self._evaluate_base_weights(validation_data)
            logger.info(f"當前基礎權重準確率: {current_performance:.3f}")
            
            # 這裡可以實施權重優化算法
            # 例如網格搜索或遺傳算法
            
            return True
            
        except Exception as e:
            logger.error(f"權重優化失敗: {e}")
            return False
    
    def _evaluate_base_weights(self, validation_data: List[Dict]) -> float:
        """評估基礎權重表現"""
        try:
            correct = 0
            total = 0
            
            for data in validation_data:
                # 使用基礎權重進行預測
                weights = self.base_weights.copy()
                # 這裡需要實際的預測邏輯
                # 簡化版本：假設已有預測結果
                if data.get('prediction_correct', False):
                    correct += 1
                total += 1
            
            return correct / total if total > 0 else 0.0
            
        except Exception as e:
            logger.error(f"基礎權重評估失敗: {e}")
            return 0.0