"""
MLB 分數校正模組
解決預測總分偏高問題 (11.3 vs 9.5)

目標：
- 修正預測分數偏差 1.8 分
- 建立球隊特定調整機制  
- 提高分數預測準確性
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import json
from collections import defaultdict

logger = logging.getLogger(__name__)

class ScoreCalibrationModule:
    """分數校正模組 - 修正預測分數偏差"""
    
    def __init__(self):
        """初始化校正模組"""
        # 基礎校正參數 (從改善計劃獲得)
        self.historical_bias = 1.8  # 預測偏高 1.8 分
        self.base_calibration_factor = 0.85  # 降低 15%
        
        # 球隊特定調整
        self.team_specific_adjustments = {}
        self.team_scoring_tendencies = {}
        
        # 統計數據
        self.calibration_history = []
        self.performance_metrics = {
            'before_calibration': {'mae': 0, 'rmse': 0, 'samples': 0},
            'after_calibration': {'mae': 0, 'rmse': 0, 'samples': 0}
        }
        
        # 載入歷史數據進行初始化
        self._initialize_team_adjustments()
        
    def _initialize_team_adjustments(self):
        """初始化球隊特定調整參數"""
        try:
            # 嘗試從數據庫載入歷史比賽數據進行分析
            try:
                from ..database import db, Game
                from sqlalchemy import func
                
                # 計算過去60天的球隊得分傾向
                cutoff_date = date.today() - timedelta(days=60)
                
                # 查詢球隊平均得分
                team_scoring = db.session.query(
                    Game.home_team.label('team'),
                    func.avg(Game.home_score).label('avg_home_score'),
                    func.count(Game.id).label('home_games')
                ).filter(
                    Game.date >= cutoff_date,
                    Game.game_status == 'completed',
                    Game.home_score.isnot(None)
                ).group_by(Game.home_team).subquery()
                
                # 同樣查詢客場得分
                away_scoring = db.session.query(
                    Game.away_team.label('team'),
                    func.avg(Game.away_score).label('avg_away_score'),
                    func.count(Game.id).label('away_games')
                ).filter(
                    Game.date >= cutoff_date,
                    Game.game_status == 'completed',
                    Game.away_score.isnot(None)  
                ).group_by(Game.away_team).subquery()
                
                # 計算聯盟平均得分
                league_avg = db.session.query(
                    func.avg((Game.home_score + Game.away_score) / 2).label('league_avg')
                ).filter(
                    Game.date >= cutoff_date,
                    Game.game_status == 'completed',
                    Game.home_score.isnot(None),
                    Game.away_score.isnot(None)
                ).scalar()
                
                if league_avg is None:
                    league_avg = 4.5  # MLB 平均得分預設值
                    
                # 建立球隊調整參數
                self._build_team_adjustments(team_scoring, away_scoring, league_avg)
                
                logger.info(f"球隊調整參數初始化完成，聯盟平均: {league_avg:.2f}")
                
            except ImportError:
                logger.warning("無法載入數據庫模組，使用預設調整參數")
                self._load_default_adjustments()
            
        except Exception as e:
            logger.warning(f"球隊調整參數初始化失敗: {e}")
            # 使用預設值
            self._load_default_adjustments()
    
    def _build_team_adjustments(self, home_stats, away_stats, league_avg):
        """建立球隊特定調整參數"""
        try:
            from ..database import db
            
            # 獲取所有球隊
            teams = ['ARI', 'ATL', 'BAL', 'BOS', 'CHC', 'CWS', 'CIN', 'CLE', 'COL', 'DET',
                    'HOU', 'KC', 'LAA', 'LAD', 'MIA', 'MIL', 'MIN', 'NYM', 'NYY', 'OAK',
                    'PHI', 'PIT', 'SD', 'SF', 'SEA', 'STL', 'TB', 'TEX', 'TOR', 'WSH']
            
            for team in teams:
                # 計算球隊得分傾向
                home_avg = db.session.execute(
                    f"SELECT AVG(home_score) FROM games WHERE home_team='{team}' AND game_status='completed' AND date >= date('now', '-60 days')"
                ).scalar()
                
                away_avg = db.session.execute(
                    f"SELECT AVG(away_score) FROM games WHERE away_team='{team}' AND game_status='completed' AND date >= date('now', '-60 days')"
                ).scalar()
                
                if home_avg is not None and away_avg is not None:
                    team_avg = (home_avg + away_avg) / 2
                    # 相對於聯盟平均的調整
                    adjustment = (team_avg - league_avg) * 0.1  # 10% 的影響
                    self.team_specific_adjustments[team] = adjustment
                    self.team_scoring_tendencies[team] = {
                        'home_avg': home_avg,
                        'away_avg': away_avg,
                        'team_avg': team_avg,
                        'adjustment': adjustment
                    }
                else:
                    self.team_specific_adjustments[team] = 0.0
                    
        except Exception as e:
            logger.error(f"建立球隊調整參數失敗: {e}")
            self._load_default_adjustments()
    
    def _load_default_adjustments(self):
        """載入預設球隊調整參數"""
        # 基於一般認知的球隊得分傾向
        default_adjustments = {
            # 高得分球隊
            'COL': 0.3, 'TEX': 0.2, 'CIN': 0.2, 'LAD': 0.15, 'HOU': 0.15,
            'ATL': 0.1, 'NYY': 0.1, 'TOR': 0.1, 'SD': 0.1, 'BOS': 0.1,
            
            # 中等得分球隊  
            'ARI': 0.0, 'CHC': 0.0, 'MIL': 0.0, 'MIN': 0.0, 'STL': 0.0,
            'PHI': 0.0, 'SF': 0.0, 'SEA': 0.0, 'WSH': 0.0, 'NYM': 0.0,
            
            # 低得分球隊
            'OAK': -0.1, 'MIA': -0.1, 'BAL': -0.1, 'DET': -0.15, 'KC': -0.15,
            'CWS': -0.1, 'CLE': -0.1, 'LAA': -0.1, 'PIT': -0.1, 'TB': -0.05
        }
        
        self.team_specific_adjustments = default_adjustments
        logger.info("載入預設球隊調整參數")
    
    def get_team_adjustment(self, team: str) -> float:
        """獲取球隊特定調整值"""
        return self.team_specific_adjustments.get(team, 0.0)
    
    def calibrate_scores(self, 
                        predicted_home: float, 
                        predicted_away: float, 
                        home_team: str, 
                        away_team: str,
                        game_context: Dict = None) -> Tuple[float, float]:
        """
        校正預測分數
        
        Args:
            predicted_home: 原始主隊預測得分
            predicted_away: 原始客隊預測得分  
            home_team: 主隊代碼
            away_team: 客隊代碼
            game_context: 比賽情境資料
            
        Returns:
            校正後的 (主隊得分, 客隊得分)
        """
        try:
            # 記錄原始預測
            original_total = predicted_home + predicted_away
            
            # 步驟1: 應用基礎校正 (降低15%)
            calibrated_home = predicted_home * self.base_calibration_factor
            calibrated_away = predicted_away * self.base_calibration_factor
            
            # 步驟2: 應用球隊特定調整
            home_adjustment = self.get_team_adjustment(home_team)
            away_adjustment = self.get_team_adjustment(away_team)
            
            calibrated_home += home_adjustment
            calibrated_away += away_adjustment
            
            # 步驟3: 應用情境調整 (如果有的話)
            if game_context:
                calibrated_home, calibrated_away = self._apply_context_adjustments(
                    calibrated_home, calibrated_away, game_context
                )
            
            # 步驟4: 確保分數合理性
            calibrated_home = max(0.5, min(25.0, calibrated_home))  # 0.5-25分範圍
            calibrated_away = max(0.5, min(25.0, calibrated_away))
            
            # 記錄校正歷史
            self._record_calibration(
                original_home=predicted_home,
                original_away=predicted_away,
                calibrated_home=calibrated_home,
                calibrated_away=calibrated_away,
                home_team=home_team,
                away_team=away_team
            )
            
            calibrated_total = calibrated_home + calibrated_away
            logger.debug(f"分數校正: {original_total:.1f} -> {calibrated_total:.1f} "
                        f"({home_team}: {predicted_home:.1f}->{calibrated_home:.1f}, "
                        f"{away_team}: {predicted_away:.1f}->{calibrated_away:.1f})")
            
            return {
                'calibrated_home_score': calibrated_home,
                'calibrated_away_score': calibrated_away,
                'original_home_score': predicted_home,
                'original_away_score': predicted_away,
                'calibration_applied': True,
                'total_adjustment': calibrated_total - original_total
            }
            
        except Exception as e:
            logger.error(f"分數校正失敗: {e}")
            # 返回基礎校正版本
            calibrated_home = predicted_home * self.base_calibration_factor
            calibrated_away = predicted_away * self.base_calibration_factor
            return {
                'calibrated_home_score': calibrated_home,
                'calibrated_away_score': calibrated_away,
                'original_home_score': predicted_home,
                'original_away_score': predicted_away,
                'calibration_applied': False,
                'total_adjustment': (calibrated_home + calibrated_away) - (predicted_home + predicted_away)
            }
    
    def _apply_context_adjustments(self, 
                                  home_score: float, 
                                  away_score: float, 
                                  context: Dict) -> Tuple[float, float]:
        """應用情境調整"""
        try:
            # 天氣影響
            weather = context.get('weather', '').lower()
            if 'rain' in weather or 'wind' in weather:
                # 惡劣天氣通常降低得分
                home_score *= 0.95
                away_score *= 0.95
            
            # 投手影響
            if context.get('ace_pitcher_matchup'):
                # 王牌投手對戰降低得分
                home_score *= 0.90
                away_score *= 0.90
            
            # 重要比賽影響
            if context.get('playoff_race') or context.get('division_rivalry'):
                # 重要比賽可能更謹慎，得分略低
                home_score *= 0.97
                away_score *= 0.97
                
            return home_score, away_score
            
        except Exception as e:
            logger.error(f"情境調整失敗: {e}")
            return home_score, away_score
    
    def _record_calibration(self, **kwargs):
        """記錄校正歷史"""
        record = {
            'timestamp': datetime.now(),
            **kwargs
        }
        self.calibration_history.append(record)
        
        # 保留最近1000筆記錄
        if len(self.calibration_history) > 1000:
            self.calibration_history = self.calibration_history[-1000:]
    
    def update_performance_metrics(self, 
                                  predictions: List[Dict], 
                                  actuals: List[Dict]):
        """更新性能指標"""
        try:
            if not predictions or not actuals:
                return
                
            # 計算校正前的誤差
            before_errors = []
            after_errors = []
            
            for pred, actual in zip(predictions, actuals):
                # 校正前誤差 
                original_total = pred['original_home'] + pred['original_away']
                actual_total = actual['home_score'] + actual['away_score']
                before_errors.append(abs(original_total - actual_total))
                
                # 校正後誤差
                calibrated_total = pred['calibrated_home'] + pred['calibrated_away'] 
                after_errors.append(abs(calibrated_total - actual_total))
            
            # 更新指標
            self.performance_metrics['before_calibration'].update({
                'mae': np.mean(before_errors),
                'rmse': np.sqrt(np.mean([e**2 for e in before_errors])),
                'samples': len(before_errors)
            })
            
            self.performance_metrics['after_calibration'].update({
                'mae': np.mean(after_errors),
                'rmse': np.sqrt(np.mean([e**2 for e in after_errors])),
                'samples': len(after_errors)
            })
            
            improvement = (self.performance_metrics['before_calibration']['mae'] - 
                          self.performance_metrics['after_calibration']['mae'])
            
            logger.info(f"校正效果更新 - MAE改善: {improvement:.3f}")
            
        except Exception as e:
            logger.error(f"更新性能指標失敗: {e}")
    
    def get_calibration_summary(self) -> Dict:
        """獲取校正摘要統計"""
        return {
            'base_calibration_factor': self.base_calibration_factor,
            'historical_bias': self.historical_bias,
            'team_adjustments_count': len(self.team_specific_adjustments),
            'calibration_records': len(self.calibration_history),
            'performance_metrics': self.performance_metrics.copy()
        }
    
    def export_team_adjustments(self, filepath: str = None):
        """匯出球隊調整參數"""
        if filepath is None:
            filepath = f"team_adjustments_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
        data = {
            'team_adjustments': self.team_specific_adjustments,
            'team_tendencies': self.team_scoring_tendencies,
            'export_time': datetime.now().isoformat(),
            'base_parameters': {
                'historical_bias': self.historical_bias,
                'base_calibration_factor': self.base_calibration_factor
            }
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"球隊調整參數已匯出至: {filepath}")
        except Exception as e:
            logger.error(f"匯出球隊調整參數失敗: {e}")