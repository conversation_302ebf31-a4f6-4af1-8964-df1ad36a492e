"""
MLB 信心度計算重設計模組
解決高信心度預測準確率反而低的問題

目標：
- 重新設計信心度計算邏輯
- 建立多因素信心度評估系統
- 確保信心度與準確率正相關
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import json
from collections import defaultdict

logger = logging.getLogger(__name__)

class ConfidenceCalculator:
    """信心度計算器 - 重新設計的信心度評估系統"""
    
    def __init__(self):
        """初始化信心度計算器"""
        # 信心度權重配置
        self.confidence_weights = {
            'data_quality': 0.30,        # 數據完整性
            'model_agreement': 0.30,     # 模型一致性
            'historical_accuracy': 0.25, # 歷史準確率
            'feature_strength': 0.15     # 特徵強度
        }
        
        # 歷史準確率追蹤
        self.accuracy_tracker = {
            'high_confidence': {'correct': 0, 'total': 0},    # >0.8 信心度
            'medium_confidence': {'correct': 0, 'total': 0},  # 0.5-0.8 信心度  
            'low_confidence': {'correct': 0, 'total': 0}      # <0.5 信心度
        }
        
        # 特徵重要性歷史
        self.feature_importance_history = defaultdict(list)
        
        # 模型性能基準
        self.baseline_performance = {
            'overall_accuracy': 0.457,  # 當前系統準確率
            'confidence_accuracy': {    # 各信心度區間的目標準確率
                'high': 0.80,     # 高信心度應達到80%
                'medium': 0.65,   # 中信心度應達到65%
                'low': 0.40       # 低信心度可接受40%
            }
        }
        
        # 數據品質標準
        self.data_quality_standards = {
            'pitcher_data_required': ['era', 'whip', 'recent_starts'],
            'team_stats_required': ['batting_avg', 'ops', 'era', 'recent_form'],
            'historical_data_threshold': 10  # 最少需要10場歷史數據
        }
        
        logger.info("信心度計算器初始化完成")
    
    def calculate_confidence(self, 
                           prediction_data: Dict, 
                           historical_performance: Dict = None) -> float:
        """
        計算預測信心度
        
        Args:
            prediction_data: 預測相關數據
            historical_performance: 歷史表現數據
            
        Returns:
            信心度 (0.0-1.0)
        """
        try:
            # 計算各個因子
            factors = {
                'data_quality': self.assess_data_completeness(prediction_data),
                'model_agreement': self.check_model_consensus(prediction_data),
                'historical_accuracy': self.get_historical_accuracy(prediction_data, historical_performance),
                'feature_strength': self.evaluate_feature_strength(prediction_data)
            }
            
            # 加權計算總信心度
            confidence = sum(
                factors[factor] * self.confidence_weights[factor]
                for factor in factors
            )
            
            # 應用調整和約束
            confidence = self._apply_confidence_adjustments(confidence, prediction_data)
            confidence = np.clip(confidence, 0.05, 0.95)  # 限制在 5%-95% 範圍
            
            # 記錄計算詳情
            self._log_confidence_calculation(confidence, factors, prediction_data)
            
            return confidence
            
        except Exception as e:
            logger.error(f"信心度計算失敗: {e}")
            return 0.5  # 返回中等信心度作為備用
    
    def assess_data_completeness(self, prediction_data: Dict) -> float:
        """評估數據完整性 (30% 權重)"""
        try:
            score = 0.0
            max_score = 1.0
            
            # 檢查投手數據完整性 (40%)
            pitcher_score = self._check_pitcher_data_quality(prediction_data)
            score += pitcher_score * 0.4
            
            # 檢查球隊統計完整性 (35%)  
            team_score = self._check_team_data_quality(prediction_data)
            score += team_score * 0.35
            
            # 檢查歷史數據充足性 (25%)
            historical_score = self._check_historical_data_sufficiency(prediction_data)
            score += historical_score * 0.25
            
            return min(score, max_score)
            
        except Exception as e:
            logger.error(f"數據完整性評估失敗: {e}")
            return 0.3  # 低品質數據
    
    def _check_pitcher_data_quality(self, data: Dict) -> float:
        """檢查投手數據品質"""
        try:
            pitcher_data = data.get('pitcher_data', {})
            home_pitcher = pitcher_data.get('home_pitcher', {})
            away_pitcher = pitcher_data.get('away_pitcher', {})
            
            score = 0.0
            
            # 檢查主場投手數據
            home_score = 0.0
            for field in self.data_quality_standards['pitcher_data_required']:
                if field in home_pitcher and home_pitcher[field] is not None:
                    home_score += 1
            home_score /= len(self.data_quality_standards['pitcher_data_required'])
            
            # 檢查客場投手數據  
            away_score = 0.0
            for field in self.data_quality_standards['pitcher_data_required']:
                if field in away_pitcher and away_pitcher[field] is not None:
                    away_score += 1
            away_score /= len(self.data_quality_standards['pitcher_data_required'])
            
            score = (home_score + away_score) / 2
            return score
            
        except Exception as e:
            logger.error(f"投手數據品質檢查失敗: {e}")
            return 0.2
    
    def _check_team_data_quality(self, data: Dict) -> float:
        """檢查球隊數據品質"""
        try:
            team_data = data.get('team_stats', {})
            home_stats = team_data.get('home_team', {})
            away_stats = team_data.get('away_team', {})
            
            score = 0.0
            
            # 檢查主隊數據
            home_score = 0.0
            for field in self.data_quality_standards['team_stats_required']:
                if field in home_stats and home_stats[field] is not None:
                    home_score += 1
            home_score /= len(self.data_quality_standards['team_stats_required'])
            
            # 檢查客隊數據
            away_score = 0.0  
            for field in self.data_quality_standards['team_stats_required']:
                if field in away_stats and away_stats[field] is not None:
                    away_score += 1
            away_score /= len(self.data_quality_standards['team_stats_required'])
            
            score = (home_score + away_score) / 2
            return score
            
        except Exception as e:
            logger.error(f"球隊數據品質檢查失敗: {e}")
            return 0.3
    
    def _check_historical_data_sufficiency(self, data: Dict) -> float:
        """檢查歷史數據充足性"""
        try:
            historical_data = data.get('historical_data', {})
            home_games = len(historical_data.get('home_team_games', []))
            away_games = len(historical_data.get('away_team_games', []))
            head_to_head = len(historical_data.get('head_to_head', []))
            
            threshold = self.data_quality_standards['historical_data_threshold']
            
            # 計算數據充足性得分
            home_score = min(home_games / threshold, 1.0)
            away_score = min(away_games / threshold, 1.0)  
            h2h_score = min(head_to_head / 5, 1.0)  # 至少5場對戰記錄
            
            return (home_score + away_score + h2h_score) / 3
            
        except Exception as e:
            logger.error(f"歷史數據充足性檢查失敗: {e}")
            return 0.4
    
    def check_model_consensus(self, prediction_data: Dict) -> float:
        """檢查模型一致性 (30% 權重)"""
        try:
            model_predictions = prediction_data.get('model_predictions', {})
            
            if len(model_predictions) < 2:
                return 0.3  # 少於2個模型，一致性低
            
            # 收集各模型的預測結果
            home_scores = []
            away_scores = []
            win_probabilities = []
            
            for model_name, pred in model_predictions.items():
                if 'home_score' in pred and pred['home_score'] is not None:
                    home_scores.append(pred['home_score'])
                if 'away_score' in pred and pred['away_score'] is not None:
                    away_scores.append(pred['away_score'])
                if 'win_probability' in pred and pred['win_probability'] is not None:
                    win_probabilities.append(pred['win_probability'])
            
            # 計算各項預測的一致性
            consensus_scores = []
            
            if len(home_scores) >= 2:
                home_consensus = self._calculate_prediction_consensus(home_scores)
                consensus_scores.append(home_consensus)
            
            if len(away_scores) >= 2:
                away_consensus = self._calculate_prediction_consensus(away_scores)
                consensus_scores.append(away_consensus)
                
            if len(win_probabilities) >= 2:
                win_consensus = self._calculate_prediction_consensus(win_probabilities)
                consensus_scores.append(win_consensus)
            
            return np.mean(consensus_scores) if consensus_scores else 0.3
            
        except Exception as e:
            logger.error(f"模型一致性檢查失敗: {e}")
            return 0.3
    
    def _calculate_prediction_consensus(self, predictions: List[float]) -> float:
        """計算預測值一致性"""
        try:
            if len(predictions) < 2:
                return 0.0
                
            mean_pred = np.mean(predictions)
            std_pred = np.std(predictions)
            
            # 變異係數越小，一致性越高
            if mean_pred == 0:
                return 0.0
                
            cv = std_pred / abs(mean_pred)  # 變異係數
            
            # 將變異係數轉換為一致性得分 (0-1)
            # CV < 0.1: 高一致性, CV > 0.5: 低一致性
            consensus = max(0, 1 - cv / 0.5)
            return min(consensus, 1.0)
            
        except Exception as e:
            logger.error(f"預測一致性計算失敗: {e}")
            return 0.3
    
    def get_historical_accuracy(self, 
                               prediction_data: Dict, 
                               historical_performance: Dict = None) -> float:
        """獲取歷史準確率 (25% 權重)"""
        try:
            if historical_performance is None:
                return 0.5  # 無歷史數據時返回中等值
            
            # 獲取類似情境下的歷史表現
            similar_scenarios = self._find_similar_scenarios(prediction_data, historical_performance)
            
            if not similar_scenarios:
                return 0.4  # 無相似情境，返回較低信心度
            
            # 計算相似情境下的準確率
            correct = sum(1 for scenario in similar_scenarios if scenario.get('accurate', False))
            total = len(similar_scenarios)
            
            accuracy = correct / total if total > 0 else 0.4
            
            # 根據樣本數調整信心度
            sample_adjustment = min(total / 20, 1.0)  # 20個樣本達到完全信心
            
            return accuracy * sample_adjustment
            
        except Exception as e:
            logger.error(f"歷史準確率獲取失敗: {e}")
            return 0.4
    
    def _find_similar_scenarios(self, prediction_data: Dict, historical_data: Dict) -> List[Dict]:
        """尋找類似的歷史情境"""
        try:
            similar_scenarios = []
            
            # 這裡應該實施複雜的相似性搜尋邏輯
            # 基於球隊、投手、比賽情境等因素
            current_context = {
                'home_team': prediction_data.get('home_team'),
                'away_team': prediction_data.get('away_team'),
                'pitcher_matchup_type': prediction_data.get('pitcher_matchup_type'),
                'game_importance': prediction_data.get('game_importance', 'regular')
            }
            
            # 簡化版本：返回相關的歷史記錄
            for record in historical_data.get('recent_predictions', [])[:50]:  # 檢查最近50場
                similarity_score = self._calculate_scenario_similarity(current_context, record)
                if similarity_score > 0.6:  # 相似度閾值
                    similar_scenarios.append(record)
            
            return similar_scenarios
            
        except Exception as e:
            logger.error(f"尋找相似情境失敗: {e}")
            return []
    
    def _calculate_scenario_similarity(self, current: Dict, historical: Dict) -> float:
        """計算情境相似度"""
        try:
            similarity = 0.0
            factors = 0
            
            # 球隊匹配
            if current.get('home_team') == historical.get('home_team'):
                similarity += 0.3
            factors += 0.3
            
            if current.get('away_team') == historical.get('away_team'):
                similarity += 0.3
            factors += 0.3
            
            # 投手匹配類型
            if current.get('pitcher_matchup_type') == historical.get('pitcher_matchup_type'):
                similarity += 0.2
            factors += 0.2
            
            # 比賽重要性
            if current.get('game_importance') == historical.get('game_importance'):
                similarity += 0.2  
            factors += 0.2
            
            return similarity / factors if factors > 0 else 0.0
            
        except Exception as e:
            logger.error(f"情境相似度計算失敗: {e}")
            return 0.0
    
    def evaluate_feature_strength(self, prediction_data: Dict) -> float:
        """評估特徵強度 (15% 權重)"""
        try:
            features = prediction_data.get('features', {})
            
            if not features:
                return 0.3
            
            strength_factors = []
            
            # 統計顯著性
            statistical_features = ['team_era_diff', 'ops_diff', 'recent_form_diff']
            for feature in statistical_features:
                if feature in features and abs(features[feature]) > 0.1:  # 顯著差異
                    strength_factors.append(0.8)
                elif feature in features:
                    strength_factors.append(0.5)
                else:
                    strength_factors.append(0.2)
            
            # 投手優勢明確性
            pitcher_advantage = features.get('pitcher_advantage', 0)
            if abs(pitcher_advantage) > 0.3:
                strength_factors.append(0.9)  # 明確的投手優勢
            elif abs(pitcher_advantage) > 0.1:
                strength_factors.append(0.7)
            else:
                strength_factors.append(0.4)
            
            # 主場優勢
            home_advantage = features.get('home_field_advantage', 0.54)  # MLB 平均主場優勢
            if abs(home_advantage - 0.54) > 0.1:  # 異常的主場優勢
                strength_factors.append(0.8)
            else:
                strength_factors.append(0.6)
            
            return np.mean(strength_factors) if strength_factors else 0.3
            
        except Exception as e:
            logger.error(f"特徵強度評估失敗: {e}")
            return 0.3
    
    def _apply_confidence_adjustments(self, base_confidence: float, prediction_data: Dict) -> float:
        """應用信心度調整"""
        try:
            adjusted_confidence = base_confidence
            
            # 調整1：極端預測降低信心度
            predictions = prediction_data.get('model_predictions', {})
            if predictions:
                avg_total = np.mean([
                    p.get('home_score', 0) + p.get('away_score', 0) 
                    for p in predictions.values() 
                    if p.get('home_score') is not None and p.get('away_score') is not None
                ])
                
                if avg_total > 15 or avg_total < 3:  # 極端總分預測
                    adjusted_confidence *= 0.85
            
            # 調整2：新球隊對戰降低信心度
            h2h_games = len(prediction_data.get('historical_data', {}).get('head_to_head', []))
            if h2h_games < 3:  # 交手記錄少於3場
                adjusted_confidence *= 0.9
            
            # 調整3：關鍵傷兵影響
            injury_impact = prediction_data.get('injury_impact', {})
            if injury_impact.get('key_players_injured', False):
                adjusted_confidence *= 0.8
            
            return adjusted_confidence
            
        except Exception as e:
            logger.error(f"信心度調整失敗: {e}")
            return base_confidence
    
    def _log_confidence_calculation(self, confidence: float, factors: Dict, prediction_data: Dict):
        """記錄信心度計算詳情"""
        try:
            teams = f"{prediction_data.get('away_team', 'UNK')} @ {prediction_data.get('home_team', 'UNK')}"
            
            logger.debug(f"信心度計算 {teams}: {confidence:.3f} | " + 
                        " | ".join([f"{k}: {v:.3f}" for k, v in factors.items()]))
            
        except Exception as e:
            logger.error(f"記錄信心度計算失敗: {e}")
    
    def update_accuracy_tracking(self, predictions: List[Dict]):
        """更新準確率追蹤"""
        try:
            for pred in predictions:
                confidence = pred.get('confidence', 0.5)
                accurate = pred.get('accurate', False)
                
                # 根據信心度分組
                if confidence >= 0.8:
                    category = 'high_confidence'
                elif confidence >= 0.5:
                    category = 'medium_confidence'
                else:
                    category = 'low_confidence'
                
                # 更新統計
                self.accuracy_tracker[category]['total'] += 1
                if accurate:
                    self.accuracy_tracker[category]['correct'] += 1
            
            # 記錄更新後的準確率
            self._log_accuracy_stats()
            
        except Exception as e:
            logger.error(f"準確率追蹤更新失敗: {e}")
    
    def _log_accuracy_stats(self):
        """記錄準確率統計"""
        for category, stats in self.accuracy_tracker.items():
            if stats['total'] > 0:
                accuracy = stats['correct'] / stats['total']
                logger.info(f"{category} 準確率: {accuracy:.3f} ({stats['correct']}/{stats['total']})")
    
    def get_confidence_report(self) -> Dict:
        """獲取信心度系統報告"""
        try:
            report = {
                'accuracy_tracking': {},
                'confidence_weights': self.confidence_weights.copy(),
                'baseline_performance': self.baseline_performance.copy(),
                'system_health': self._assess_system_health()
            }
            
            # 計算各信心度區間的實際準確率
            for category, stats in self.accuracy_tracker.items():
                if stats['total'] > 0:
                    accuracy = stats['correct'] / stats['total']
                    target = self.baseline_performance['confidence_accuracy'][category.split('_')[0]]
                    
                    report['accuracy_tracking'][category] = {
                        'accuracy': accuracy,
                        'target': target,
                        'meets_target': accuracy >= target,
                        'sample_size': stats['total']
                    }
                else:
                    report['accuracy_tracking'][category] = {
                        'accuracy': None,
                        'target': self.baseline_performance['confidence_accuracy'][category.split('_')[0]],
                        'meets_target': False,
                        'sample_size': 0
                    }
            
            return report
            
        except Exception as e:
            logger.error(f"生成信心度報告失敗: {e}")
            return {'error': str(e)}
    
    def _assess_system_health(self) -> Dict:
        """評估系統健康狀況"""
        try:
            health = {
                'overall_status': 'healthy',
                'issues': [],
                'recommendations': []
            }
            
            # 檢查信心度準確率是否正相關
            categories = ['high_confidence', 'medium_confidence', 'low_confidence']
            accuracies = []
            
            for cat in categories:
                stats = self.accuracy_tracker[cat]
                if stats['total'] > 0:
                    accuracy = stats['correct'] / stats['total']
                    accuracies.append(accuracy)
                else:
                    accuracies.append(None)
            
            # 檢查是否高信心度準確率 > 中信心度準確率 > 低信心度準確率
            if all(acc is not None for acc in accuracies):
                if not (accuracies[0] >= accuracies[1] >= accuracies[2]):
                    health['issues'].append("信心度與準確率未保持正相關")
                    health['overall_status'] = 'warning'
            
            # 檢查樣本數充足性
            for cat, stats in self.accuracy_tracker.items():
                if stats['total'] < 20:  # 樣本數太少
                    health['recommendations'].append(f"需要增加 {cat} 的樣本數")
            
            return health
            
        except Exception as e:
            logger.error(f"系統健康評估失敗: {e}")
            return {'overall_status': 'error', 'issues': [str(e)]}
    
    def calibrate_confidence_weights(self, validation_data: List[Dict]) -> bool:
        """根據驗證數據校準信心度權重"""
        try:
            if len(validation_data) < 100:  # 需要足夠的驗證數據
                logger.warning("驗證數據不足，無法校準權重")
                return False
            
            logger.info(f"開始校準信心度權重，使用 {len(validation_data)} 筆驗證數據")
            
            # 這裡可以實施權重優化算法
            # 例如網格搜索或貝葉斯優化
            # 目標是最大化信心度與準確率的相關性
            
            # 簡化版本：記錄當前權重的表現
            current_performance = self._evaluate_weight_performance(validation_data)
            logger.info(f"當前權重配置表現: {current_performance}")
            
            return True
            
        except Exception as e:
            logger.error(f"校準信心度權重失敗: {e}")
            return False
    
    def _evaluate_weight_performance(self, validation_data: List[Dict]) -> Dict:
        """評估權重配置的表現"""
        try:
            confidences = []
            accuracies = []
            
            for data in validation_data:
                confidence = self.calculate_confidence(data)
                accurate = data.get('actual_accurate', False)
                
                confidences.append(confidence)
                accuracies.append(1.0 if accurate else 0.0)
            
            # 計算相關係數
            correlation = np.corrcoef(confidences, accuracies)[0, 1]
            
            # 計算各信心度區間的表現
            high_conf_mask = np.array(confidences) >= 0.8
            med_conf_mask = (np.array(confidences) >= 0.5) & (np.array(confidences) < 0.8)
            low_conf_mask = np.array(confidences) < 0.5
            
            performance = {
                'correlation': correlation,
                'high_confidence_accuracy': np.mean(np.array(accuracies)[high_conf_mask]) if high_conf_mask.any() else None,
                'medium_confidence_accuracy': np.mean(np.array(accuracies)[med_conf_mask]) if med_conf_mask.any() else None,
                'low_confidence_accuracy': np.mean(np.array(accuracies)[low_conf_mask]) if low_conf_mask.any() else None
            }
            
            return performance
            
        except Exception as e:
            logger.error(f"權重表現評估失敗: {e}")
            return {'error': str(e)}