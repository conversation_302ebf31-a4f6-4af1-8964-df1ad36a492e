#!/usr/bin/env python3
"""
智能對戰分析引擎 (Intelligent Matchup Analysis Engine)

核心功能：
1.  分析一場比賽的對戰組合，特別是先發投手。
2.  判斷比賽類型（如投手戰、打擊戰）。
3.  為機器學習模型提供額外的、基於情境的特徵。
"""

import logging
from typing import Dict, Optional
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

# 假設您的資料庫模型和配置是這樣導入的
from models.database import db, Game, PlayerStats, TeamStats
from models.enhanced_data_models import PlayerPerformanceTrends

logger = logging.getLogger(__name__)

class MatchupAnalyzer:
    """智能對戰分析器"""

    def __init__(self, game_id: str, session: Optional[db.Session] = None):
        """
        初始化分析器
        :param game_id: 要分析的比賽ID
        :param session: 資料庫會話，如果為None，將創建一個新的會話
        """
        self.game_id = game_id
        self.session = session or self._create_session()
        self.game_info = self._get_game_info()

        if not self.game_info:
            raise ValueError(f"找不到比賽ID為 {game_id} 的比賽")

        # 假設先發投手資訊儲存在 Game 或相關模型中
        # 這裡我們先用一個 placeholder，後續可以從 GameDetail 或其他地方獲取
        self.home_pitcher_name = self.game_info.get('home_starting_pitcher', 'N/A')
        self.away_pitcher_name = self.game_info.get('away_starting_pitcher', 'N/A')

    def _create_session(self):
        """如果沒有提供，則創建一個新的資料庫會話"""
        # 這裡需要您的資料庫連接URI
        # 假設它儲存在 config.py 中
        from config import config
        engine = create_engine(config['development'].SQLALCHEMY_DATABASE_URI)
        Session = sessionmaker(bind=engine)
        return Session()

    def _get_game_info(self) -> Optional[Dict]:
        """獲取比賽基本資訊"""
        game = self.session.query(Game).filter_by(game_id=self.game_id).first()
        if game:
            # 為了簡化，我們假設 Game 模型能提供先發投手名字
            # 在實際應用中，可能需要關聯 GameDetail
            from models.database import GameDetail
            game_detail = self.session.query(GameDetail).filter_by(game_id=self.game_id).first()
            return {
                'home_team': game.home_team,
                'away_team': game.away_team,
                'home_starting_pitcher': game_detail.home_starting_pitcher if game_detail else 'N/A',
                'away_starting_pitcher': game_detail.away_starting_pitcher if game_detail else 'N/A',
            }
        return None

    def _get_pitcher_stats(self, pitcher_name: str) -> Optional[Dict]:
        """獲取單一投手的賽季統計和近期趨勢"""
        if not pitcher_name or pitcher_name == 'N/A':
            return None

        # 查詢賽季數據
        season_stats = self.session.query(PlayerStats).filter(
            PlayerStats.player_name.like(f'%{pitcher_name}%'),
            PlayerStats.innings_pitched > 10 # 至少投10局才有參考價值
        ).order_by(PlayerStats.season.desc()).first()

        if not season_stats:
            return None

        # 查詢近期趨勢數據
        trends = self.session.query(PlayerPerformanceTrends).filter(
            PlayerPerformanceTrends.player_id == season_stats.player_id
        ).order_by(PlayerPerformanceTrends.date_calculated.desc()).first()

        # 計算進階指標
        k_per_9 = (season_stats.strikeouts_pitching / season_stats.innings_pitched) * 9 if season_stats.innings_pitched > 0 else 0
        bb_per_9 = (season_stats.walks_allowed / season_stats.innings_pitched) * 9 if season_stats.innings_pitched > 0 else 0

        return {
            'season_era': season_stats.era,
            'season_whip': season_stats.whip,
            'season_k_per_9': k_per_9,
            'season_bb_per_9': bb_per_9,
            'last_5_starts_era': trends.last_5_starts_era if trends else None,
            'rest_days': trends.rest_days if trends else None
        }

    def _calculate_pitcher_strength_score(self, stats: Dict) -> float:
        """根據投手統計數據計算強度分數 (0-100)"""
        if not stats:
            return 50.0  # 返回平均分

        score = 100.0

        # 1. ERA (越低越好, 聯盟平均約 4.0)
        era = stats.get('season_era', 4.0)
        score -= (era - 4.0) * 10  # ERA每高1點，扣10分

        # 2. WHIP (越低越好, 聯盟平均約 1.3)
        whip = stats.get('season_whip', 1.3)
        score -= (whip - 1.3) * 20 # WHIP每高0.1，扣2分

        # 3. K/9 (越高越好, 聯盟平均約 8.5)
        k_per_9 = stats.get('season_k_per_9', 8.5)
        score += (k_per_9 - 8.5) * 2 # K/9每高1，加2分

        # 4. 近期ERA (last_5_starts_era)
        last_5_era = stats.get('last_5_starts_era')
        if last_5_era is not None:
            # 近期狀態權重更高
            score -= (last_5_era - era) * 5

        # 將分數限制在 0-100 之間
        return max(0, min(100, score))

    def analyze(self) -> Dict:
        """執行對戰分析"""
        home_pitcher_stats = self._get_pitcher_stats(self.home_pitcher_name)
        away_pitcher_stats = self._get_pitcher_stats(self.away_pitcher_name)

        home_pitcher_score = self._calculate_pitcher_strength_score(home_pitcher_stats)
        away_pitcher_score = self._calculate_pitcher_strength_score(away_pitcher_stats)

        avg_pitcher_score = (home_pitcher_score + away_pitcher_score) / 2

        # 判斷比賽類型
        matchup_type = 'EVEN_MATCH'
        run_environment = 'MEDIUM'
        summary = '勢均力敵的比賽。'

        if avg_pitcher_score > 75:
            matchup_type = 'PITCHER_DUEL'
            run_environment = 'LOW'
            summary = f"頂級投手對決 ({self.away_pitcher_name} vs {self.home_pitcher_name})，預期為一場低分投手戰。"
        elif avg_pitcher_score < 40:
            matchup_type = 'SLUGFEST'
            run_environment = 'HIGH'
            summary = "雙方投手實力較弱或近期狀態不佳，可能形成一場高比分的打擊戰。"
        elif abs(home_pitcher_score - away_pitcher_score) > 25:
            matchup_type = 'MISMATCH'
            stronger_pitcher = self.home_pitcher_name if home_pitcher_score > away_pitcher_score else self.away_pitcher_name
            summary = f"投手實力存在明顯差距，{stronger_pitcher} 擁有顯著優勢。"

        return {
            'game_id': self.game_id,
            'matchup_type': matchup_type, # PITCHER_DUEL, SLUGFEST, MISMATCH, EVEN_MATCH
            'run_environment': run_environment, # LOW, MEDIUM, HIGH
            'summary': summary,
            'pitcher_scores': {
                'home': {
                    'name': self.home_pitcher_name,
                    'score': round(home_pitcher_score, 2),
                    'stats': home_pitcher_stats
                },
                'away': {
                    'name': self.away_pitcher_name,
                    'score': round(away_pitcher_score, 2),
                    'stats': away_pitcher_stats
                }
            }
        }

# 使用範例
if __name__ == '__main__':
    # 假設我們有一個有效的 game_id 和資料庫連線
    # 這段代碼需要一個 Flask App Context 來運行
    # from app import create_app
    # app = create_app()
    # with app.app_context():
    #     # 找一個最近有比賽的 game_id 來測試
    #     game = Game.query.order_by(Game.date.desc()).first()
    #     if game:
    #         analyzer = MatchupAnalyzer(game.game_id)
    #         analysis_report = analyzer.analyze()
    #         import json
    #         print(json.dumps(analysis_report, indent=4))
    pass
