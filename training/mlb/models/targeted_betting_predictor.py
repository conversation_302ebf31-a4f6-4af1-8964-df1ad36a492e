"""
目標性博彩預測系統
專門針對6月28日後的比賽，使用真實博彩盤口進行預測
"""

import logging
from datetime import date, datetime, timedelta
from typing import Dict, List
from flask import current_app

from models.database import db, Game, Prediction
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from models.over_under_predictor import OverUnderPredictor
from models.run_line_predictor import RunLinePredictor
from models.daily_lineup_fetcher import DailyLineupFetcher

logger = logging.getLogger(__name__)

class TargetedBettingPredictor:
    """目標性博彩預測器 - 專注於真實盤口預測"""
    
    def __init__(self, app=None):
        self.app = app or current_app
        self.betting_odds_fetcher = RealBettingOddsFetcher(app)
        self.over_under_predictor = OverUnderPredictor(app)
        self.run_line_predictor = RunLinePredictor(app)
        self.lineup_fetcher = DailyLineupFetcher()
        
        # 預測目標日期 (6月28日後)
        self.target_start_date = date(2025, 6, 28)
    
    def get_upcoming_games_with_odds(self, days_ahead: int = 7) -> Dict:
        """獲取未來幾天有博彩盤口的比賽"""
        try:
            logger.info(f"獲取未來 {days_ahead} 天的比賽和博彩盤口...")
            
            upcoming_games = []
            
            for i in range(days_ahead):
                target_date = date.today() + timedelta(days=i)
                
                # 只處理6月28日後的比賽
                if target_date < self.target_start_date:
                    continue
                
                logger.info(f"檢查 {target_date} 的比賽...")
                
                # 獲取該日期的比賽
                with self.app.app_context():
                    games = Game.query.filter(
                        Game.date == target_date,
                        Game.game_status.in_(['scheduled', 'in_progress'])
                    ).all()
                
                if not games:
                    logger.info(f"{target_date} 沒有比賽")
                    continue
                
                # 獲取該日期的博彩盤口
                odds_data = self.betting_odds_fetcher.get_mlb_odds_today(target_date)
                
                # 整合比賽和盤口數據
                for game in games:
                    game_odds = self._find_game_odds(game, odds_data)
                    
                    if game_odds:
                        upcoming_games.append({
                            'date': target_date,
                            'game': game,
                            'odds': game_odds,
                            'has_real_odds': not game_odds.get('is_simulated', True)
                        })
            
            return {
                'total_games': len(upcoming_games),
                'games_with_real_odds': sum(1 for g in upcoming_games if g['has_real_odds']),
                'games': upcoming_games,
                'date_range': {
                    'start': self.target_start_date,
                    'end': date.today() + timedelta(days=days_ahead-1)
                }
            }
            
        except Exception as e:
            logger.error(f"獲取未來比賽失敗: {e}")
            return {'error': str(e)}
    
    def _find_game_odds(self, game: Game, odds_data: Dict) -> Dict:
        """為特定比賽找到對應的博彩盤口"""
        try:
            for odds_game in odds_data.get('games', []):
                # 嘗試匹配球隊名稱
                if (self._match_team_names(game.home_team, odds_game['home_team']) and
                    self._match_team_names(game.away_team, odds_game['away_team'])):
                    return odds_game['odds']
            
            return None
            
        except Exception as e:
            logger.error(f"匹配比賽盤口失敗: {e}")
            return None
    
    def _match_team_names(self, db_team: str, odds_team: str) -> bool:
        """匹配球隊名稱"""
        # 簡單的名稱匹配邏輯
        db_team_clean = db_team.lower().replace(' ', '')
        odds_team_clean = odds_team.lower().replace(' ', '')
        
        return db_team_clean in odds_team_clean or odds_team_clean in db_team_clean
    
    def generate_targeted_predictions(self, target_date: date = None) -> Dict:
        """生成目標性預測 - 專注於有真實盤口的比賽"""
        try:
            if target_date is None:
                target_date = date.today()
            
            # 確保是6月28日後的日期
            if target_date < self.target_start_date:
                target_date = self.target_start_date
            
            logger.info(f"生成 {target_date} 的目標性預測...")
            
            # 獲取該日期的比賽和盤口
            games_with_odds = self.get_upcoming_games_with_odds(1)
            
            if 'error' in games_with_odds:
                return games_with_odds
            
            predictions = []
            
            for game_data in games_with_odds['games']:
                if game_data['date'] != target_date:
                    continue
                
                game = game_data['game']
                odds = game_data['odds']
                has_real_odds = game_data['has_real_odds']
                
                logger.info(f"預測比賽: {game.away_team} @ {game.home_team}")
                
                # 生成大小分預測
                over_under_result = self.over_under_predictor.predict_over_under(
                    game.game_id, target_date
                )
                
                # 生成讓分盤預測
                run_line_result = self.run_line_predictor.predict_run_line(
                    game.game_id, target_date
                )
                
                # 整合預測結果
                prediction = {
                    'game_id': game.game_id,
                    'date': target_date.isoformat(),
                    'teams': {
                        'home': game.home_team,
                        'away': game.away_team
                    },
                    'has_real_odds': has_real_odds,
                    'odds_source': '真實博彩商' if has_real_odds else '模擬數據',
                    'over_under': over_under_result if 'error' not in over_under_result else None,
                    'run_line': run_line_result if 'error' not in run_line_result else None,
                    'prediction_quality': self._assess_prediction_quality(
                        has_real_odds, over_under_result, run_line_result
                    )
                }
                
                predictions.append(prediction)
            
            return {
                'date': target_date.isoformat(),
                'total_predictions': len(predictions),
                'real_odds_predictions': sum(1 for p in predictions if p['has_real_odds']),
                'predictions': predictions,
                'summary': self._generate_prediction_summary(predictions)
            }
            
        except Exception as e:
            logger.error(f"生成目標性預測失敗: {e}")
            return {'error': str(e)}
    
    def _assess_prediction_quality(self, has_real_odds: bool,
                                 over_under_result: Dict, run_line_result: Dict) -> Dict:
        """評估預測質量 - 改進版本"""
        quality_score = 0
        quality_factors = []

        # 基礎分數 - 預測完整性
        base_score = 20
        quality_score += base_score
        quality_factors.append("基礎預測完整性")

        # 真實盤口加分 (提高權重)
        if has_real_odds:
            quality_score += 50  # 從40提高到50
            quality_factors.append("✅ 使用真實博彩盤口")
        else:
            quality_score += 10  # 模擬盤口也給一些分數
            quality_factors.append("⚠️ 使用模擬盤口")

        # 大小分預測質量 (改進評分邏輯)
        if over_under_result and 'error' not in over_under_result:
            confidence = over_under_result.get('confidence', 0)
            # 提高信心度權重，並設置最低分數
            confidence_score = max(confidence * 20, 5)  # 最低5分
            quality_score += confidence_score

            # 根據推薦強度調整分數
            recommendation = over_under_result.get('recommendation', '')
            if '推薦' in recommendation and '中性' not in recommendation:
                quality_score += 5  # 有明確推薦加分
                quality_factors.append(f"大小分: {confidence:.1%} 信心度 + 明確推薦")
            else:
                quality_factors.append(f"大小分: {confidence:.1%} 信心度")
        else:
            quality_factors.append("大小分: 預測失敗")

        # 讓分盤預測質量 (改進評分邏輯)
        if run_line_result and 'error' not in run_line_result:
            prediction = run_line_result.get('prediction', {})
            confidence = prediction.get('confidence', 0)
            # 提高信心度權重，並設置最低分數
            confidence_score = max(confidence * 20, 5)  # 最低5分
            quality_score += confidence_score

            # 根據推薦強度調整分數
            recommendation = prediction.get('recommendation', '')
            if '推薦' in recommendation and '中性' not in recommendation:
                quality_score += 5  # 有明確推薦加分
                quality_factors.append(f"讓分盤: {confidence:.1%} 信心度 + 明確推薦")
            else:
                quality_factors.append(f"讓分盤: {confidence:.1%} 信心度")
        else:
            quality_factors.append("讓分盤: 預測失敗")

        # 數據完整性加分
        if over_under_result and run_line_result:
            quality_score += 5
            quality_factors.append("雙重預測完整")

        return {
            'score': min(quality_score, 100),
            'grade': self._get_quality_grade(quality_score),
            'factors': quality_factors
        }
    
    def _get_quality_grade(self, score: float) -> str:
        """獲取質量等級 - 調整後的評分標準"""
        if score >= 85:
            return "A級 (優秀)"  # 真實盤口 + 高信心度
        elif score >= 70:
            return "B級 (良好)"  # 真實盤口 + 中等信心度 或 模擬盤口 + 高信心度
        elif score >= 55:
            return "C級 (一般)"  # 模擬盤口 + 中等信心度
        elif score >= 40:
            return "D級 (較差)"  # 低信心度或部分預測失敗
        else:
            return "F級 (失敗)"  # 預測嚴重問題
    
    def _generate_prediction_summary(self, predictions: List[Dict]) -> Dict:
        """生成預測摘要"""
        if not predictions:
            return {'message': '沒有可預測的比賽'}
        
        total = len(predictions)
        real_odds_count = sum(1 for p in predictions if p['has_real_odds'])
        
        # 統計推薦
        over_recommendations = []
        under_recommendations = []
        home_cover_recommendations = []
        away_cover_recommendations = []
        
        for pred in predictions:
            # 大小分推薦
            if pred['over_under']:
                recommendation = pred['over_under'].get('recommendation', '')
                if '大分' in recommendation or 'Over' in recommendation:
                    over_recommendations.append(pred['teams'])
                elif '小分' in recommendation or 'Under' in recommendation:
                    under_recommendations.append(pred['teams'])
            
            # 讓分盤推薦
            if pred['run_line']:
                recommendation = pred['run_line'].get('prediction', {}).get('recommendation', '')
                if '主隊' in recommendation:
                    home_cover_recommendations.append(pred['teams'])
                elif '客隊' in recommendation:
                    away_cover_recommendations.append(pred['teams'])
        
        return {
            'total_games': total,
            'real_odds_coverage': f"{real_odds_count}/{total} ({real_odds_count/total*100:.1f}%)",
            'recommendations': {
                'over_under': {
                    'over_count': len(over_recommendations),
                    'under_count': len(under_recommendations),
                    'over_games': over_recommendations,
                    'under_games': under_recommendations
                },
                'run_line': {
                    'home_cover_count': len(home_cover_recommendations),
                    'away_cover_count': len(away_cover_recommendations),
                    'home_cover_games': home_cover_recommendations,
                    'away_cover_games': away_cover_recommendations
                }
            }
        }
    
    def get_prediction_targets_for_week(self) -> Dict:
        """獲取本週的預測目標"""
        try:
            logger.info("獲取本週預測目標...")
            
            week_data = self.get_upcoming_games_with_odds(7)
            
            if 'error' in week_data:
                return week_data
            
            # 按日期分組
            daily_targets = {}
            
            for game_data in week_data['games']:
                date_str = game_data['date'].isoformat()
                
                if date_str not in daily_targets:
                    daily_targets[date_str] = {
                        'date': date_str,
                        'games': [],
                        'real_odds_count': 0,
                        'total_games': 0
                    }
                
                daily_targets[date_str]['games'].append(game_data)
                daily_targets[date_str]['total_games'] += 1
                
                if game_data['has_real_odds']:
                    daily_targets[date_str]['real_odds_count'] += 1
            
            return {
                'week_summary': {
                    'total_days': len(daily_targets),
                    'total_games': week_data['total_games'],
                    'real_odds_games': week_data['games_with_real_odds']
                },
                'daily_targets': daily_targets,
                'recommendation': self._get_week_recommendation(daily_targets)
            }
            
        except Exception as e:
            logger.error(f"獲取週預測目標失敗: {e}")
            return {'error': str(e)}
    
    def _get_week_recommendation(self, daily_targets: Dict) -> str:
        """獲取週預測建議"""
        total_games = sum(day['total_games'] for day in daily_targets.values())
        real_odds_games = sum(day['real_odds_count'] for day in daily_targets.values())
        
        if real_odds_games == 0:
            return "本週沒有真實博彩盤口，建議等待或使用模擬預測"
        elif real_odds_games / total_games >= 0.8:
            return "本週真實盤口覆蓋率高，適合進行目標性預測"
        elif real_odds_games / total_games >= 0.5:
            return "本週有部分真實盤口，建議選擇性預測"
        else:
            return "本週真實盤口較少，建議謹慎預測"


def main():
    """測試目標性預測器"""
    from flask import Flask
    from models.database import db
    
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///mlb_data.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    with app.app_context():
        predictor = TargetedBettingPredictor(app)
        
        print("=" * 80)
        print("🎯 目標性博彩預測系統測試")
        print("=" * 80)
        
        # 測試週預測目標
        week_targets = predictor.get_prediction_targets_for_week()
        
        if 'error' in week_targets:
            print(f"❌ 獲取週目標失敗: {week_targets['error']}")
        else:
            summary = week_targets['week_summary']
            print(f"📊 本週預測目標:")
            print(f"總天數: {summary['total_days']}")
            print(f"總比賽: {summary['total_games']}")
            print(f"真實盤口比賽: {summary['real_odds_games']}")
            print(f"建議: {week_targets['recommendation']}")
        
        # 測試今日預測
        today_predictions = predictor.generate_targeted_predictions()
        
        if 'error' in today_predictions:
            print(f"❌ 今日預測失敗: {today_predictions['error']}")
        else:
            print(f"\n🎯 今日預測結果:")
            print(f"總預測: {today_predictions['total_predictions']}")
            print(f"真實盤口預測: {today_predictions['real_odds_predictions']}")


if __name__ == "__main__":
    main()
