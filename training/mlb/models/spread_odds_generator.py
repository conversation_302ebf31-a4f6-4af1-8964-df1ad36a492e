#!/usr/bin/env python3
"""
讓分盤口數據生成器
為缺失讓分盤數據的比賽自動生成合理的讓分盤口
"""

import logging
from datetime import datetime, date
from typing import Dict, List, Optional
from models.database import db, Game, BettingOdds

logger = logging.getLogger(__name__)

class SpreadOddsGenerator:
    """讓分盤口數據生成器"""
    
    def __init__(self, app=None):
        self.app = app
        
        # MLB標準讓分盤設置
        self.standard_spread = 1.5  # MLB標準讓分
        self.standard_odds = -110   # 標準讓分盤賠率
        
    def generate_missing_spreads(self, target_date: date = None) -> Dict:
        """禁用模擬讓分盤數據生成 - 只使用真實博彩數據"""
        if target_date is None:
            target_date = date.today()

        logger.warning(f"讓分盤數據生成已禁用 - 只使用真實博彩數據")

        return {
            'success': False,
            'message': '讓分盤數據生成已禁用 - 只使用真實博彩數據',
            'generated_count': 0,
            'target_date': target_date.strftime('%Y-%m-%d'),
            'note': '請使用真實博彩API獲取讓分盤數據'
        }
    
    def _find_games_without_spreads(self, target_date: date) -> List[Game]:
        """查找沒有讓分盤數據的比賽"""
        
        # 查詢指定日期的所有比賽
        all_games = Game.query.filter(Game.date == target_date).all()
        
        games_without_spreads = []
        
        for game in all_games:
            # 檢查是否已有讓分盤數據
            existing_spread = BettingOdds.query.filter_by(
                game_id=game.game_id,
                market_type='spreads'
            ).first()
            
            if not existing_spread:
                games_without_spreads.append(game)
        
        logger.info(f"找到 {len(games_without_spreads)} 場比賽缺少讓分盤數據")
        return games_without_spreads
    
    def _generate_spread_for_game(self, game: Game) -> bool:
        """為單場比賽生成讓分盤數據"""
        try:
            # 計算讓分盤口
            spread_data = self._calculate_spread(game)
            
            # 創建讓分盤記錄
            spread_odds = BettingOdds(
                game_id=game.game_id,
                bookmaker='estimated',
                market_type='spreads',
                home_spread_point=spread_data['home_spread'],
                away_spread_point=spread_data['away_spread'],
                home_spread_price=self.standard_odds,
                away_spread_price=self.standard_odds,
                odds_time=datetime.now(),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            db.session.add(spread_odds)
            
            logger.info(f"為比賽 {game.away_team}@{game.home_team} 生成讓分盤: "
                       f"主隊{spread_data['home_spread']:+.1f}")
            
            return True
            
        except Exception as e:
            logger.error(f"為比賽 {game.game_id} 生成讓分盤失敗: {e}")
            return False
    
    def _calculate_spread(self, game: Game) -> Dict[str, float]:
        """計算比賽的讓分盤口"""
        
        # 獲取球隊實力評估
        home_strength = self._get_team_strength(game.home_team)
        away_strength = self._get_team_strength(game.away_team)
        
        # 計算實力差距
        strength_diff = home_strength - away_strength
        
        # 加上主場優勢（通常約0.3-0.5分）
        home_advantage = 0.4
        total_advantage = strength_diff + home_advantage
        
        # 根據實力差距決定讓分
        if abs(total_advantage) < 0.5:
            # 實力接近，使用標準讓分
            home_spread = -self.standard_spread
        elif total_advantage > 0.5:
            # 主隊較強，讓分更多
            home_spread = -self.standard_spread
        else:
            # 客隊較強，主隊受讓
            home_spread = self.standard_spread
        
        return {
            'home_spread': home_spread,
            'away_spread': -home_spread
        }
    
    def _get_team_strength(self, team_code: str) -> float:
        """獲取球隊實力評估（簡化版本）"""
        
        # 強隊列表（基於2024賽季表現）
        strong_teams = {
            'LAD': 0.8,  # 道奇
            'NYY': 0.7,  # 洋基
            'HOU': 0.6,  # 太空人
            'ATL': 0.6,  # 勇士
            'PHI': 0.5,  # 費城人
            'SD': 0.5,   # 教士
            'BAL': 0.5,  # 金鶯
            'CLE': 0.4,  # 守護者
        }
        
        # 弱隊列表
        weak_teams = {
            'CWS': -0.8, # 白襪
            'MIA': -0.6, # 馬林魚
            'COL': -0.5, # 洛磯
            'KC': -0.4,  # 皇家
            'DET': -0.4, # 老虎
        }
        
        # 返回球隊實力評估
        if team_code in strong_teams:
            return strong_teams[team_code]
        elif team_code in weak_teams:
            return weak_teams[team_code]
        else:
            return 0.0  # 中等實力球隊
    
    def verify_spread_data(self, target_date: date = None) -> Dict:
        """驗證讓分盤數據完整性"""
        if target_date is None:
            target_date = date.today()
            
        try:
            with self.app.app_context():
                # 查詢指定日期的比賽和讓分盤數據
                games = Game.query.filter(Game.date == target_date).all()
                
                complete_count = 0
                incomplete_games = []
                
                for game in games:
                    spread_odds = BettingOdds.query.filter_by(
                        game_id=game.game_id,
                        market_type='spreads'
                    ).first()
                    
                    if spread_odds:
                        complete_count += 1
                    else:
                        incomplete_games.append(f"{game.away_team}@{game.home_team}")
                
                completion_rate = (complete_count / len(games) * 100) if games else 0
                
                return {
                    'success': True,
                    'total_games': len(games),
                    'complete_count': complete_count,
                    'completion_rate': completion_rate,
                    'incomplete_games': incomplete_games,
                    'target_date': target_date.strftime('%Y-%m-%d')
                }
                
        except Exception as e:
            logger.error(f"驗證讓分盤數據失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }

def auto_generate_spreads_for_date(target_date: date, app) -> Dict:
    """為指定日期自動生成讓分盤數據的便捷函數"""
    generator = SpreadOddsGenerator(app)
    return generator.generate_missing_spreads(target_date)

def verify_spreads_for_date(target_date: date, app) -> Dict:
    """驗證指定日期讓分盤數據完整性的便捷函數"""
    generator = SpreadOddsGenerator(app)
    return generator.verify_spread_data(target_date)
