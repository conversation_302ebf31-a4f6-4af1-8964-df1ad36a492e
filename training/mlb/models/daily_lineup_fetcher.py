#!/usr/bin/env python3
"""
獲取MLB官方每日先發陣容
包括先發投手和打線陣容
"""

import logging
import requests
import sys
import os
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional
from bs4 import BeautifulSoup
import re

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import db

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DailyLineupFetcher:
    """每日先發陣容獲取器"""
    
    def __init__(self, app=None):
        self.app = app
        self.base_url = "https://statsapi.mlb.com/api/v1"
        
    def get_games_for_date(self, target_date: date) -> List[Dict]:
        """獲取指定日期的比賽列表"""
        try:
            date_str = target_date.strftime('%Y-%m-%d')
            url = f"{self.base_url}/schedule"
            params = {
                'sportId': 1,  # MLB
                'date': date_str,
                'hydrate': 'probablePitchers,lineups'
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            games = []
            
            for date_info in data.get('dates', []):
                for game in date_info.get('games', []):
                    games.append(game)
            
            logger.info(f"獲取到 {len(games)} 場比賽 ({date_str})")
            return games
            
        except Exception as e:
            logger.error(f"獲取比賽列表失敗 ({target_date}): {e}")
            return []
    
    def extract_probable_pitchers(self, game: Dict, mlb_com_data: Dict = None) -> Dict:
        """提取先發投手信息"""
        pitchers = {
            'home_pitcher': None,
            'away_pitcher': None,
            'home_pitcher_id': None,
            'away_pitcher_id': None
        }

        try:
            # 獲取隊伍名稱用於匹配MLB.com數據
            home_team_name = game.get('teams', {}).get('home', {}).get('team', {}).get('name', '')
            away_team_name = game.get('teams', {}).get('away', {}).get('team', {}).get('name', '')

            # 首先嘗試從MLB.com數據獲取
            if mlb_com_data:
                for game_key, game_data in mlb_com_data.items():
                    if (game_data['home_team'] in home_team_name or home_team_name in game_data['home_team']) and \
                       (game_data['away_team'] in away_team_name or away_team_name in game_data['away_team']):
                        pitchers['home_pitcher'] = game_data['home_pitcher']
                        pitchers['away_pitcher'] = game_data['away_pitcher']
                        logger.info(f"從MLB.com獲取投手信息: {away_team_name} @ {home_team_name}")
                        break

            # 如果MLB.com沒有數據，嘗試從probablePitchers獲取
            if not pitchers['home_pitcher'] or not pitchers['away_pitcher']:
                probable_pitchers = game.get('probablePitchers', {})

                # 主隊投手
                home_pitcher = probable_pitchers.get('home')
                if home_pitcher and not pitchers['home_pitcher']:
                    pitchers['home_pitcher'] = home_pitcher.get('fullName')
                    pitchers['home_pitcher_id'] = home_pitcher.get('id')

                # 客隊投手
                away_pitcher = probable_pitchers.get('away')
                if away_pitcher and not pitchers['away_pitcher']:
                    pitchers['away_pitcher'] = away_pitcher.get('fullName')
                    pitchers['away_pitcher_id'] = away_pitcher.get('id')

            # 如果還是沒有信息，嘗試從boxscore獲取
            if not pitchers['home_pitcher'] or not pitchers['away_pitcher']:
                game_id = game.get('gamePk')
                if game_id:
                    boxscore_pitchers = self.get_starting_pitchers_from_boxscore(game_id)

                    if not pitchers['home_pitcher'] and boxscore_pitchers['home_pitcher']:
                        pitchers['home_pitcher'] = boxscore_pitchers['home_pitcher']
                        pitchers['home_pitcher_id'] = boxscore_pitchers['home_pitcher_id']

                    if not pitchers['away_pitcher'] and boxscore_pitchers['away_pitcher']:
                        pitchers['away_pitcher'] = boxscore_pitchers['away_pitcher']
                        pitchers['away_pitcher_id'] = boxscore_pitchers['away_pitcher_id']

        except Exception as e:
            logger.error(f"提取投手信息失敗: {e}")

        return pitchers

    def get_starting_pitchers_from_boxscore(self, game_id: int) -> Dict:
        """從boxscore獲取先發投手信息"""
        pitchers = {
            'home_pitcher': None,
            'away_pitcher': None,
            'home_pitcher_id': None,
            'away_pitcher_id': None
        }

        try:
            boxscore_url = f"{self.base_url}/game/{game_id}/boxscore"
            response = requests.get(boxscore_url, timeout=30)
            response.raise_for_status()

            data = response.json()

            if 'teams' in data:
                for team_type in ['home', 'away']:
                    team_data = data['teams'][team_type]

                    # 獲取投手列表（按出場順序）
                    if 'pitchers' in team_data and 'players' in team_data:
                        pitcher_ids = team_data['pitchers']
                        players = team_data['players']

                        # 查找先發投手（第一個投手且投球局數較多）
                        for pitcher_id in pitcher_ids:
                            player_key = f"ID{pitcher_id}"
                            if player_key in players:
                                player_info = players[player_key]

                                # 檢查投球局數
                                innings_pitched = 0
                                if 'stats' in player_info and 'pitching' in player_info['stats']:
                                    pitching_stats = player_info['stats']['pitching']
                                    innings_pitched = float(pitching_stats.get('inningsPitched', 0))

                                # 先發投手通常投球局數≥4.0
                                if innings_pitched >= 4.0:
                                    pitcher_name = player_info.get('person', {}).get('fullName')

                                    if team_type == 'home':
                                        pitchers['home_pitcher'] = pitcher_name
                                        pitchers['home_pitcher_id'] = pitcher_id
                                    else:
                                        pitchers['away_pitcher'] = pitcher_name
                                        pitchers['away_pitcher_id'] = pitcher_id

                                    break  # 找到先發投手就停止

        except Exception as e:
            logger.error(f"從boxscore獲取投手信息失敗 (Game {game_id}): {e}")

        return pitchers
    
    def extract_lineups(self, game: Dict) -> Dict:
        """提取打線陣容信息"""
        lineups = {
            'home_lineup': [],
            'away_lineup': [],
            'home_lineup_confirmed': False,
            'away_lineup_confirmed': False
        }

        try:
            # 檢查是否有打線信息
            lineups_data = game.get('lineups', {})

            # 主隊打線
            home_lineup = lineups_data.get('homePlayers', [])
            if home_lineup:
                lineups['home_lineup_confirmed'] = True
                for i, player in enumerate(home_lineup):
                    player_info = {
                        'batting_order': i + 1,  # 使用索引作為打擊順序
                        'position': player.get('primaryPosition', {}).get('name'),
                        'position_code': player.get('primaryPosition', {}).get('code'),
                        'player_name': player.get('fullName'),
                        'player_id': player.get('id')
                    }
                    lineups['home_lineup'].append(player_info)

            # 客隊打線
            away_lineup = lineups_data.get('awayPlayers', [])
            if away_lineup:
                lineups['away_lineup_confirmed'] = True
                for i, player in enumerate(away_lineup):
                    player_info = {
                        'batting_order': i + 1,  # 使用索引作為打擊順序
                        'position': player.get('primaryPosition', {}).get('name'),
                        'position_code': player.get('primaryPosition', {}).get('code'),
                        'player_name': player.get('fullName'),
                        'player_id': player.get('id')
                    }
                    lineups['away_lineup'].append(player_info)

        except Exception as e:
            logger.error(f"提取打線信息失敗: {e}")

        return lineups
    
    def get_daily_lineups(self, target_date: date) -> Dict:
        """獲取指定日期的完整陣容信息"""
        # 首先從MLB.com獲取先發投手信息
        mlb_com_data = self._get_probable_pitchers_from_mlb_com(target_date)

        games = self.get_games_for_date(target_date)

        daily_data = {
            'date': target_date.strftime('%Y-%m-%d'),
            'total_games': len(games),
            'games_with_pitchers': 0,
            'games_with_lineups': 0,
            'games': []
        }

        for game in games:
            try:
                game_info = {
                    'game_id': game.get('gamePk'),
                    'game_date': game.get('gameDate'),
                    'status': game.get('status', {}).get('detailedState'),
                    'home_team': game.get('teams', {}).get('home', {}).get('team', {}).get('name'),
                    'away_team': game.get('teams', {}).get('away', {}).get('team', {}).get('name'),
                    'home_team_id': game.get('teams', {}).get('home', {}).get('team', {}).get('id'),
                    'away_team_id': game.get('teams', {}).get('away', {}).get('team', {}).get('id')
                }
                
                # 獲取投手信息
                pitchers = self.extract_probable_pitchers(game, mlb_com_data)
                game_info.update(pitchers)
                
                if pitchers['home_pitcher'] or pitchers['away_pitcher']:
                    daily_data['games_with_pitchers'] += 1
                
                # 獲取打線信息
                lineups = self.extract_lineups(game)
                game_info.update(lineups)
                
                if lineups['home_lineup_confirmed'] or lineups['away_lineup_confirmed']:
                    daily_data['games_with_lineups'] += 1
                
                daily_data['games'].append(game_info)
                
            except Exception as e:
                logger.error(f"處理比賽數據失敗 (Game ID: {game.get('gamePk')}): {e}")
        
        return daily_data

    def _get_probable_pitchers_from_mlb_com(self, target_date: date) -> Dict:
        """從MLB.com網站獲取先發投手信息"""
        try:
            # MLB.com的先發投手頁面
            url = "https://www.mlb.com/probable-pitchers/"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # 查找比賽信息
            games_data = {}

            # 查找所有比賽容器 - 使用正確的選擇器
            matchup_containers = soup.select('div[class*="matchup"]')
            logger.info(f"找到 {len(matchup_containers)} 個比賽容器")

            for container in matchup_containers:
                try:
                    # 使用正確的CSS選擇器提取隊伍名稱
                    away_team_elem = container.select_one('.probable-pitchers__team-name--away')
                    home_team_elem = container.select_one('.probable-pitchers__team-name--home')

                    if away_team_elem and home_team_elem:
                        away_team = away_team_elem.text.strip()
                        home_team = home_team_elem.text.strip()

                        # 提取投手信息 - 查找球員連結
                        pitcher_links = container.find_all('a', href=re.compile(r'/player/'))

                        if len(pitcher_links) >= 2:
                            away_pitcher = pitcher_links[0].text.strip()
                            home_pitcher = pitcher_links[1].text.strip()

                            # 提取投手統計信息
                            container_text = container.get_text()
                            era_pattern = r'(\d+\.\d+)\s*ERA'
                            eras = re.findall(era_pattern, container_text)

                            away_pitcher_era = eras[0] if len(eras) > 0 else None
                            home_pitcher_era = eras[1] if len(eras) > 1 else None

                            game_key = f"{away_team}@{home_team}"
                            games_data[game_key] = {
                                'away_team': away_team,
                                'home_team': home_team,
                                'away_pitcher': away_pitcher,
                                'home_pitcher': home_pitcher,
                                'away_pitcher_era': away_pitcher_era,
                                'home_pitcher_era': home_pitcher_era
                            }
                            logger.info(f"解析比賽: {away_team} @ {home_team} - {away_pitcher} vs {home_pitcher}")
                        else:
                            logger.debug(f"未找到足夠的投手信息: {away_team} @ {home_team}")
                    else:
                        logger.debug(f"未找到隊伍名稱元素")

                except Exception as e:
                    logger.debug(f"解析比賽容器失敗: {e}")
                    continue

            logger.info(f"從MLB.com獲取到 {len(games_data)} 場比賽的先發投手信息")
            return games_data

        except Exception as e:
            logger.error(f"從MLB.com獲取先發投手信息失敗: {e}")
            return {}

    def fetch_recent_lineups(self, days: int = 7) -> Dict:
        """獲取最近幾天的陣容信息"""
        results = {}
        
        for i in range(days):
            target_date = date.today() - timedelta(days=i)
            logger.info(f"獲取 {target_date} 的陣容信息...")
            
            daily_data = self.get_daily_lineups(target_date)
            results[target_date.strftime('%Y-%m-%d')] = daily_data
        
        return results
    
    def test_api_availability(self) -> Dict:
        """測試API可用性和數據質量"""
        logger.info("測試MLB API可用性...")
        
        # 測試今天的數據
        today = date.today()
        today_data = self.get_daily_lineups(today)
        
        # 測試昨天的數據
        yesterday = today - timedelta(days=1)
        yesterday_data = self.get_daily_lineups(yesterday)
        
        # 測試前天的數據
        day_before = today - timedelta(days=2)
        day_before_data = self.get_daily_lineups(day_before)
        
        summary = {
            'test_date': today.strftime('%Y-%m-%d'),
            'api_accessible': True,
            'today': {
                'date': today_data['date'],
                'total_games': today_data['total_games'],
                'games_with_pitchers': today_data['games_with_pitchers'],
                'games_with_lineups': today_data['games_with_lineups'],
                'pitcher_coverage': today_data['games_with_pitchers'] / today_data['total_games'] * 100 if today_data['total_games'] > 0 else 0,
                'lineup_coverage': today_data['games_with_lineups'] / today_data['total_games'] * 100 if today_data['total_games'] > 0 else 0
            },
            'yesterday': {
                'date': yesterday_data['date'],
                'total_games': yesterday_data['total_games'],
                'games_with_pitchers': yesterday_data['games_with_pitchers'],
                'games_with_lineups': yesterday_data['games_with_lineups'],
                'pitcher_coverage': yesterday_data['games_with_pitchers'] / yesterday_data['total_games'] * 100 if yesterday_data['total_games'] > 0 else 0,
                'lineup_coverage': yesterday_data['games_with_lineups'] / yesterday_data['total_games'] * 100 if yesterday_data['total_games'] > 0 else 0
            },
            'day_before': {
                'date': day_before_data['date'],
                'total_games': day_before_data['total_games'],
                'games_with_pitchers': day_before_data['games_with_pitchers'],
                'games_with_lineups': day_before_data['games_with_lineups'],
                'pitcher_coverage': day_before_data['games_with_pitchers'] / day_before_data['total_games'] * 100 if day_before_data['total_games'] > 0 else 0,
                'lineup_coverage': day_before_data['games_with_lineups'] / day_before_data['total_games'] * 100 if day_before_data['total_games'] > 0 else 0
            }
        }
        
        return summary

def main():
    """主函數 - 測試每日陣容獲取"""
    fetcher = DailyLineupFetcher()
    
    print("=" * 80)
    print("⚾ MLB每日先發陣容獲取測試")
    print("=" * 80)
    
    # 測試API可用性
    summary = fetcher.test_api_availability()
    
    print(f"\n📊 API測試結果:")
    print(f"API可訪問: {'✅' if summary['api_accessible'] else '❌'}")
    
    for period in ['today', 'yesterday', 'day_before']:
        data = summary[period]
        print(f"\n📅 {data['date']} ({period}):")
        print(f"  總比賽數: {data['total_games']}")
        print(f"  有投手信息: {data['games_with_pitchers']} ({data['pitcher_coverage']:.1f}%)")
        print(f"  有打線信息: {data['games_with_lineups']} ({data['lineup_coverage']:.1f}%)")
    
    # 顯示詳細樣本
    print(f"\n🔍 今日比賽詳細信息樣本:")
    today_data = fetcher.get_daily_lineups(date.today())
    
    for i, game in enumerate(today_data['games'][:3]):  # 只顯示前3場
        print(f"\n比賽 {i+1}: {game['away_team']} @ {game['home_team']}")
        print(f"  狀態: {game['status']}")
        print(f"  主隊投手: {game['home_pitcher'] or '未確定'}")
        print(f"  客隊投手: {game['away_pitcher'] or '未確定'}")
        print(f"  主隊打線確定: {'✅' if game['home_lineup_confirmed'] else '❌'}")
        print(f"  客隊打線確定: {'✅' if game['away_lineup_confirmed'] else '❌'}")
        
        if game['home_lineup_confirmed'] and game['home_lineup']:
            print(f"  主隊打線前3棒:")
            for j, player in enumerate(game['home_lineup'][:3]):
                print(f"    {player['batting_order']}. {player['player_name']} ({player['position']})")

if __name__ == "__main__":
    main()
