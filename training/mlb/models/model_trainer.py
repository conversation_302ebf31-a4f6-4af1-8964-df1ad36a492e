import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import os
import json

from .ml_predictor import MLBPredictor
from .feature_engineer import FeatureEngineer
from .database import db, Game, Team, TeamStats

logger = logging.getLogger(__name__)

class ModelTrainer:
    """MLB模型訓練器 - 整合特徵工程和模型訓練"""
    
    def __init__(self):
        self.predictor = MLBPredictor()
        self.feature_engineer = FeatureEngineer()
        self.training_history = []
        
    def train_full_pipeline(self, 
                          start_date: date = None, 
                          end_date: date = None,
                          save_models: bool = True) -> Dict:
        """執行完整的訓練流程"""
        try:
            logger.info("開始執行完整訓練流程...")
            
            # 設置默認日期
            if end_date is None:
                end_date = date.today() - timedelta(days=1)
            if start_date is None:
                start_date = end_date - timedelta(days=730)  # 2年數據
            
            # 步驟1: 準備訓練數據
            logger.info("步驟1: 準備訓練數據...")
            X, y = self._prepare_enhanced_training_data(start_date, end_date)
            
            if X.empty or y.empty:
                raise ValueError("無法準備訓練數據")
            
            logger.info(f"訓練數據準備完成: {len(X)} 場比賽, {len(X.columns)} 個特徵")
            
            # 設置特徵列名（重要！）
            self.predictor.feature_columns = list(X.columns)

            # 步驟2: 訓練模型
            logger.info("步驟2: 訓練預測模型...")
            training_results = self.predictor.train_models(X, y)
            
            # 步驟3: 評估模型
            logger.info("步驟3: 評估模型性能...")
            evaluation_results = self._evaluate_models(X, y)
            
            # 步驟4: 保存模型
            if save_models:
                logger.info("步驟4: 保存訓練好的模型...")
                model_saved = self.predictor.save_models()
                if not model_saved:
                    logger.warning("模型保存失敗")
            
            # 記錄訓練歷史
            training_record = {
                'timestamp': datetime.now().isoformat(),
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'training_samples': len(X),
                'features_count': len(X.columns),
                'training_results': training_results,
                'evaluation_results': evaluation_results
            }
            
            self.training_history.append(training_record)
            
            logger.info("完整訓練流程執行完成")
            return training_record
            
        except Exception as e:
            logger.error(f"訓練流程失敗: {e}")
            raise
    
    def _prepare_enhanced_training_data(self, start_date: date, end_date: date) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """使用增強特徵工程準備訓練數據"""
        try:
            # 查詢已完成的比賽
            games = Game.query.filter(
                Game.date >= start_date,
                Game.date <= end_date,
                Game.game_status == 'completed',
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            if not games:
                raise ValueError("沒有找到訓練數據")
            
            logger.info(f"找到 {len(games)} 場已完成的比賽")
            
            # 提取增強特徵
            features_list = []
            targets_list = []
            
            for i, game in enumerate(games):
                if i % 1000 == 0:
                    logger.info(f"處理進度: {i}/{len(games)}")
                
                try:
                    # 執行智能對戰分析
                    try:
                        analyzer = MatchupAnalyzer(game.game_id, session=db.session)
                        matchup_analysis = analyzer.analyze()
                    except Exception as e:
                        logger.warning(f"訓練時智能對戰分析失敗 for game {game.game_id}: {e}")
                        matchup_analysis = None

                    # 使用增強特徵工程
                    features = self.feature_engineer.extract_comprehensive_features(
                        game.home_team, game.away_team, game.date, matchup_analysis=matchup_analysis
                    )
                    
                    if not features:
                        continue
                    
                    # 添加目標變量
                    targets = {
                        'home_score': game.home_score,
                        'away_score': game.away_score,
                        'total_runs': game.home_score + game.away_score,
                        'home_win': 1 if game.home_score > game.away_score else 0
                    }
                    
                    features_list.append(features)
                    targets_list.append(targets)
                    
                except Exception as e:
                    logger.warning(f"處理比賽失敗 {game.game_id}: {e}")
                    continue
            
            if not features_list:
                raise ValueError("特徵提取失敗")
            
            # 轉換為DataFrame
            X = pd.DataFrame(features_list)
            y = pd.DataFrame(targets_list)
            
            # 處理缺失值
            X = X.fillna(X.median())
            
            logger.info(f"特徵提取完成: {len(X)} 個樣本, {len(X.columns)} 個特徵")
            
            return X, y
            
        except Exception as e:
            logger.error(f"準備增強訓練數據失敗: {e}")
            raise
    
    def _evaluate_models(self, X: pd.DataFrame, y: pd.DataFrame) -> Dict:
        """評估模型性能"""
        try:
            from sklearn.model_selection import cross_val_score
            from sklearn.metrics import mean_squared_error, accuracy_score
            
            evaluation = {}
            
            # 準備數據
            X_scaled = self.predictor.scalers['features'].transform(X)
            
            # 評估主隊得分預測
            if self.predictor.models['home_score'] is not None:
                home_score_cv = cross_val_score(
                    self.predictor.models['home_score'], 
                    X_scaled, y['home_score'], 
                    cv=5, scoring='neg_mean_squared_error'
                )
                evaluation['home_score_cv_rmse'] = np.sqrt(-home_score_cv.mean())
                evaluation['home_score_cv_std'] = home_score_cv.std()
            
            # 評估客隊得分預測
            if self.predictor.models['away_score'] is not None:
                away_score_cv = cross_val_score(
                    self.predictor.models['away_score'], 
                    X_scaled, y['away_score'], 
                    cv=5, scoring='neg_mean_squared_error'
                )
                evaluation['away_score_cv_rmse'] = np.sqrt(-away_score_cv.mean())
                evaluation['away_score_cv_std'] = away_score_cv.std()
            
            # 評估勝負預測
            if self.predictor.models['win_probability'] is not None:
                win_prob_cv = cross_val_score(
                    self.predictor.models['win_probability'], 
                    X_scaled, y['home_win'], 
                    cv=5, scoring='accuracy'
                )
                evaluation['win_prob_cv_accuracy'] = win_prob_cv.mean()
                evaluation['win_prob_cv_std'] = win_prob_cv.std()
            
            # 特徵重要性分析
            evaluation['feature_importance'] = self._analyze_feature_importance(X)
            
            return evaluation
            
        except Exception as e:
            logger.error(f"模型評估失敗: {e}")
            return {}
    
    def _analyze_feature_importance(self, X: pd.DataFrame) -> Dict:
        """分析特徵重要性"""
        try:
            importance_analysis = {}
            
            # 如果是基於樹的模型，獲取特徵重要性
            for model_name, model in self.predictor.models.items():
                if model is not None and hasattr(model, 'feature_importances_'):
                    importances = model.feature_importances_
                    feature_names = X.columns
                    
                    # 獲取前10個最重要的特徵
                    top_indices = np.argsort(importances)[-10:][::-1]
                    top_features = [(feature_names[i], importances[i]) for i in top_indices]
                    
                    importance_analysis[model_name] = top_features
            
            return importance_analysis
            
        except Exception as e:
            logger.warning(f"特徵重要性分析失敗: {e}")
            return {}
    
    def quick_retrain(self, days_back: int = 30) -> Dict:
        """快速重新訓練（使用最近的數據）"""
        try:
            end_date = date.today() - timedelta(days=1)
            start_date = end_date - timedelta(days=days_back)
            
            logger.info(f"快速重新訓練: {start_date} 到 {end_date}")
            
            return self.train_full_pipeline(start_date, end_date, save_models=True)
            
        except Exception as e:
            logger.error(f"快速重新訓練失敗: {e}")
            raise
    
    def validate_on_recent_games(self, days: int = 7) -> Dict:
        """在最近的比賽上驗證模型"""
        try:
            # 載入模型
            if not self.predictor.load_models():
                raise ValueError("無法載入模型")
            
            # 獲取最近的比賽
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            recent_games = Game.query.filter(
                Game.date >= start_date,
                Game.date < end_date,
                Game.game_status == 'completed',
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            if not recent_games:
                return {'error': '沒有找到最近的比賽數據'}
            
            predictions = []
            actuals = []
            
            for game in recent_games:
                try:
                    # 進行預測
                    prediction = self.predictor.predict_game(
                        game.home_team, game.away_team, game.date
                    )
                    
                    predictions.append({
                        'game_id': game.game_id,
                        'predicted_home_score': prediction['predicted_home_score'],
                        'predicted_away_score': prediction['predicted_away_score'],
                        'home_win_probability': prediction['home_win_probability'],
                        'actual_home_score': game.home_score,
                        'actual_away_score': game.away_score,
                        'actual_home_win': 1 if game.home_score > game.away_score else 0
                    })
                    
                except Exception as e:
                    logger.warning(f"預測比賽失敗 {game.game_id}: {e}")
                    continue
            
            if not predictions:
                return {'error': '無法生成預測'}
            
            # 計算驗證指標
            validation_metrics = self._calculate_validation_metrics(predictions)
            
            return {
                'validation_period': f"{start_date} 到 {end_date}",
                'games_validated': len(predictions),
                'metrics': validation_metrics,
                'predictions': predictions
            }
            
        except Exception as e:
            logger.error(f"最近比賽驗證失敗: {e}")
            return {'error': str(e)}
    
    def _calculate_validation_metrics(self, predictions: List[Dict]) -> Dict:
        """計算驗證指標"""
        try:
            # 得分預測準確性
            home_score_errors = [abs(p['predicted_home_score'] - p['actual_home_score']) for p in predictions]
            away_score_errors = [abs(p['predicted_away_score'] - p['actual_away_score']) for p in predictions]
            
            # 勝負預測準確性
            win_predictions = [1 if p['home_win_probability'] > 0.5 else 0 for p in predictions]
            actual_wins = [p['actual_home_win'] for p in predictions]
            win_accuracy = sum(1 for i in range(len(win_predictions)) if win_predictions[i] == actual_wins[i]) / len(predictions)
            
            return {
                'home_score_mae': np.mean(home_score_errors),
                'away_score_mae': np.mean(away_score_errors),
                'total_score_mae': np.mean(home_score_errors + away_score_errors),
                'win_prediction_accuracy': win_accuracy,
                'average_confidence': np.mean([p.get('confidence', 0.5) for p in predictions])
            }
            
        except Exception as e:
            logger.warning(f"計算驗證指標失敗: {e}")
            return {}
    
    def get_training_summary(self) -> Dict:
        """獲取訓練摘要"""
        if not self.training_history:
            return {'message': '尚未進行訓練'}
        
        latest_training = self.training_history[-1]
        
        return {
            'latest_training_date': latest_training['timestamp'],
            'training_data_period': f"{latest_training['start_date']} 到 {latest_training['end_date']}",
            'training_samples': latest_training['training_samples'],
            'features_count': latest_training['features_count'],
            'model_performance': latest_training.get('training_results', {}),
            'total_trainings': len(self.training_history)
        }
