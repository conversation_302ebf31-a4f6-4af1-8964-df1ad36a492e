#!/usr/bin/env python3
"""
統一預測引擎
整合歷史數據優化、投手分析、上下文管理的完整預測系統
解決投手資料缺失和預測準確性問題
"""

import logging
import json
import asyncio
from datetime import date, datetime
from typing import Dict, List, Optional, Tuple, Any
from flask import current_app

from models.prediction_context import PredictionContext, create_prediction_context
from models.enhanced_historical_predictor import EnhancedHistoricalPredictor
from models.historical_data_optimizer import HistoricalDataOptimizer
from models.starting_pitcher_tracker import StartingPitcherTracker
from models.database import db, Game, Prediction, PlayerStats

logger = logging.getLogger(__name__)

class UnifiedPredictionEngine:
    """統一預測引擎 - 整合所有預測功能"""
    
    def __init__(self, app=None):
        self.app = app or current_app
        self.historical_predictor = EnhancedHistoricalPredictor()
        self.data_optimizer = HistoricalDataOptimizer()
        self.pitcher_tracker = StartingPitcherTracker()
        
        # 預測策略配置
        self.prediction_strategies = {
            'historical': self._historical_prediction_strategy,
            'live': self._live_prediction_strategy,
            'future': self._future_prediction_strategy
        }
    
    async def predict_game(self, 
                          game_id: str, 
                          target_date: Optional[date] = None,
                          prediction_type: str = "comprehensive",
                          **kwargs) -> Dict[str, Any]:
        """
        統一的比賽預測接口
        
        Args:
            game_id: 比賽ID
            target_date: 目標日期 (None將從game_id推斷或使用當前日期)
            prediction_type: 預測類型 (comprehensive, quick, pitcher_focused)
            
        Returns:
            完整的預測結果字典
        """
        
        logger.info(f"🎯 開始統一預測: {game_id} | 日期: {target_date}")
        
        try:
            # 1. 創建預測上下文
            context = create_prediction_context(
                game_id=game_id,
                target_date=target_date,
                **kwargs
            )
            
            logger.info(f"📋 預測上下文: {context.prediction_mode} | 目標日期: {context.target_date}")
            
            # 2. 選擇預測策略
            strategy = self.prediction_strategies.get(
                context.prediction_mode, 
                self._historical_prediction_strategy
            )
            
            # 3. 執行預測
            prediction_result = await strategy(context, prediction_type)
            
            # 4. 增強結果信息
            enhanced_result = await self._enhance_prediction_result(
                prediction_result, context
            )
            
            # 5. 保存預測記錄
            if enhanced_result.get('save_to_db', True):
                await self._save_prediction_to_db(enhanced_result, context)
            
            logger.info(f"✅ 預測完成: {context.execution_id}")
            
            return enhanced_result
            
        except Exception as e:
            error_msg = f"預測失敗: {e}"
            logger.error(error_msg, exc_info=True)
            
            return {
                'success': False,
                'error': error_msg,
                'game_id': game_id,
                'prediction': None,
                'context': None
            }
    
    async def _historical_prediction_strategy(self, 
                                            context: PredictionContext, 
                                            prediction_type: str) -> Dict[str, Any]:
        """歷史預測策略 - 使用增強歷史預測引擎"""
        
        logger.info(f"📊 執行歷史預測策略: {context.game_id}")
        
        try:
            # 使用增強歷史預測引擎
            historical_result = await self.historical_predictor.predict_game_with_historical_analysis(
                context.game_id, 
                context.target_date
            )
            
            # 補充投手分析 (如果歷史引擎未包含)
            pitcher_analysis = await self._analyze_starting_pitchers(context)
            
            # 整合結果
            integrated_result = {
                'success': True,
                'prediction_strategy': 'historical_enhanced',
                'game_id': context.game_id,
                'target_date': context.target_date.isoformat(),
                'prediction': historical_result.get('prediction', {}),
                'confidence': historical_result.get('confidence', {}),
                'pitcher_analysis': pitcher_analysis,
                'data_sources': historical_result.get('data_sources', {}),
                'analysis_details': historical_result.get('analysis_details', {}),
                'context_info': context.to_dict()
            }
            
            return integrated_result
            
        except Exception as e:
            logger.error(f"歷史預測策略失敗: {e}")
            # 回退到基礎預測
            return await self._fallback_prediction(context, str(e))
    
    async def _live_prediction_strategy(self, 
                                      context: PredictionContext, 
                                      prediction_type: str) -> Dict[str, Any]:
        """即時預測策略 - 結合實時數據和歷史分析"""
        
        logger.info(f"🔴 執行即時預測策略: {context.game_id}")
        
        try:
            # 即時預測可以使用歷史引擎 + 實時數據調整
            base_prediction = await self.historical_predictor.predict_game_with_historical_analysis(
                context.game_id, 
                context.target_date
            )
            
            # 實時調整 (天氣、傷病、臨時變更等)
            live_adjustments = await self._apply_live_adjustments(context, base_prediction)
            
            result = {
                'success': True,
                'prediction_strategy': 'live_enhanced',
                'game_id': context.game_id,
                'target_date': context.target_date.isoformat(),
                'prediction': base_prediction.get('prediction', {}),
                'live_adjustments': live_adjustments,
                'confidence': base_prediction.get('confidence', {}),
                'context_info': context.to_dict()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"即時預測策略失敗: {e}")
            return await self._fallback_prediction(context, str(e))
    
    async def _future_prediction_strategy(self, 
                                        context: PredictionContext, 
                                        prediction_type: str) -> Dict[str, Any]:
        """未來預測策略 - 基於趨勢分析和投手輪值"""
        
        logger.info(f"🔮 執行未來預測策略: {context.game_id}")
        
        try:
            # 未來預測主要依賴歷史趨勢和已知的投手輪值
            trend_based_prediction = await self._predict_based_on_trends(context)
            
            result = {
                'success': True,
                'prediction_strategy': 'future_trend',
                'game_id': context.game_id,
                'target_date': context.target_date.isoformat(),
                'prediction': trend_based_prediction.get('prediction', {}),
                'confidence': trend_based_prediction.get('confidence', {}),
                'context_info': context.to_dict(),
                'note': '未來預測基於歷史趨勢，實際結果可能因臨時變更而不同'
            }
            
            return result
            
        except Exception as e:
            logger.error(f"未來預測策略失敗: {e}")
            return await self._fallback_prediction(context, str(e))
    
    async def _analyze_starting_pitchers(self, context: PredictionContext) -> Dict[str, Any]:
        """分析先發投手"""
        
        pitcher_analysis = {
            'home_pitcher': {
                'name': context.home_starting_pitcher,
                'data_source': context.pitcher_data_source,
                'analysis': None
            },
            'away_pitcher': {
                'name': context.away_starting_pitcher,
                'data_source': context.pitcher_data_source,
                'analysis': None
            },
            'matchup_advantage': 'neutral',
            'confidence': 'medium'
        }
        
        try:
            if context.home_starting_pitcher and context.away_starting_pitcher:
                # 獲取投手歷史表現
                home_pitcher_performance = self.data_optimizer.get_weighted_historical_performance(
                    context.home_starting_pitcher, context.target_date
                )
                
                away_pitcher_performance = self.data_optimizer.get_weighted_historical_performance(
                    context.away_starting_pitcher, context.target_date
                )
                
                pitcher_analysis['home_pitcher']['analysis'] = home_pitcher_performance
                pitcher_analysis['away_pitcher']['analysis'] = away_pitcher_performance
                pitcher_analysis['confidence'] = 'high'
                
                # 簡單的對戰優勢判斷
                home_era = home_pitcher_performance.get('aggregated_stats', {}).get('era', 4.50)
                away_era = away_pitcher_performance.get('aggregated_stats', {}).get('era', 4.50)
                
                if home_era < away_era - 0.5:
                    pitcher_analysis['matchup_advantage'] = 'home'
                elif away_era < home_era - 0.5:
                    pitcher_analysis['matchup_advantage'] = 'away'
                
            else:
                context.add_warning("投手信息不完整，使用團隊統計進行預測")
                
        except Exception as e:
            context.add_error(f"投手分析失敗: {e}")
            pitcher_analysis['error'] = str(e)
        
        return pitcher_analysis
    
    async def _apply_live_adjustments(self, context: PredictionContext, base_prediction: Dict) -> Dict:
        """應用即時調整"""
        
        adjustments = {
            'weather_impact': 0.0,
            'injury_impact': 0.0,
            'lineup_changes': 0.0,
            'total_adjustment': 0.0
        }
        
        try:
            # 這裡可以添加實時調整邏輯
            # 例如: 天氣API、傷病報告API、陣容變更API等
            pass
            
        except Exception as e:
            logger.error(f"應用即時調整失敗: {e}")
        
        return adjustments
    
    async def _predict_based_on_trends(self, context: PredictionContext) -> Dict:
        """基於趨勢的預測"""
        
        try:
            # 使用歷史預測引擎，但降低置信度
            trend_prediction = await self.historical_predictor.predict_game_with_historical_analysis(
                context.game_id, context.target_date
            )
            
            # 調整置信度 (未來預測不確定性更高)
            if trend_prediction.get('confidence'):
                confidence = trend_prediction['confidence']
                confidence['overall_confidence'] = max(0.3, confidence.get('overall_confidence', 0.5) * 0.8)
                confidence['confidence_level'] = 'low' if confidence['overall_confidence'] < 0.5 else 'medium'
            
            return trend_prediction
            
        except Exception as e:
            logger.error(f"趨勢預測失敗: {e}")
            return {'prediction': {}, 'confidence': {'confidence_level': 'low'}}
    
    async def _fallback_prediction(self, context: PredictionContext, error_reason: str) -> Dict[str, Any]:
        """回退預測策略"""
        
        logger.warning(f"使用回退預測策略: {error_reason}")
        
        # 提供基礎的預測結果
        fallback_result = {
            'success': True,
            'prediction_strategy': 'fallback',
            'game_id': context.game_id,
            'target_date': context.target_date.isoformat(),
            'prediction': {
                'home_predicted_runs': 4.5,
                'away_predicted_runs': 4.5,
                'predicted_total_runs': 9.0,
                'home_win_probability': 0.52 if context.home_team else 0.5,  # 主場輕微優勢
                'away_win_probability': 0.48 if context.home_team else 0.5
            },
            'confidence': {
                'overall_confidence': 0.4,
                'confidence_level': 'low'
            },
            'pitcher_analysis': {
                'home_pitcher': {'name': context.home_starting_pitcher or '未確認'},
                'away_pitcher': {'name': context.away_starting_pitcher or '未確認'},
                'note': '投手資料不足，使用團隊平均值預測'
            },
            'error_reason': error_reason,
            'context_info': context.to_dict()
        }
        
        return fallback_result
    
    async def _enhance_prediction_result(self, 
                                       prediction_result: Dict, 
                                       context: PredictionContext) -> Dict[str, Any]:
        """增強預測結果"""
        
        try:
            # 添加格式化的顯示信息
            prediction = prediction_result.get('prediction', {})
            
            enhanced_result = prediction_result.copy()
            enhanced_result.update({
                'formatted_display': {
                    'game_info': f"{context.away_team or 'Away'} @ {context.home_team or 'Home'}",
                    'predicted_score': f"{prediction.get('away_predicted_runs', 0):.1f} - {prediction.get('home_predicted_runs', 0):.1f}",
                    'total_prediction': f"{prediction.get('predicted_total_runs', 0):.1f}",
                    'win_probability': f"{prediction.get('home_win_probability', 0.5)*100:.1f}% (主) vs {prediction.get('away_win_probability', 0.5)*100:.1f}% (客)",
                    'confidence_display': prediction_result.get('confidence', {}).get('confidence_level', 'medium').upper()
                },
                'recommendation': {
                    'betting_suggestion': self._generate_betting_suggestion(prediction_result, context),
                    'risk_level': self._assess_risk_level(prediction_result, context)
                }
            })
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"增強預測結果失敗: {e}")
            return prediction_result
    
    def _generate_betting_suggestion(self, prediction_result: Dict, context: PredictionContext) -> str:
        """生成投注建議"""
        
        confidence = prediction_result.get('confidence', {})
        confidence_level = confidence.get('confidence_level', 'low')
        
        if confidence_level == 'high':
            return "高信心度預測，建議參考"
        elif confidence_level == 'medium':
            return "中等信心度，謹慎參考"
        else:
            return "信心度較低，僅供參考"
    
    def _assess_risk_level(self, prediction_result: Dict, context: PredictionContext) -> str:
        """評估風險等級"""
        
        confidence = prediction_result.get('confidence', {}).get('overall_confidence', 0.5)
        
        if confidence >= 0.8:
            return "低風險"
        elif confidence >= 0.6:
            return "中等風險"
        else:
            return "高風險"
    
    async def _save_prediction_to_db(self, prediction_result: Dict, context: PredictionContext):
        """保存預測結果到數據庫 - 整合歷史管理系統"""
        
        try:
            with self.app.app_context():
                prediction = prediction_result.get('prediction', {})
                confidence_info = prediction_result.get('confidence', {})
                
                # 🔥 使用歷史管理系統保存增強預測
                from models.prediction_history_manager import PredictionHistoryManager
                
                history_manager = PredictionHistoryManager()
                
                # 確定預測類型
                prediction_strategy = prediction_result.get('prediction_strategy', 'unknown')
                if 'bullpen' in prediction_strategy or 'pitcher_analysis' in prediction_result:
                    prediction_type = 'enhanced_with_bullpen'
                elif 'enhanced' in prediction_strategy:
                    prediction_type = 'enhanced'
                else:
                    prediction_type = 'basic'
                
                # 保存到歷史管理系統 (會自動更新主Prediction表)
                history_saved = history_manager.save_enhanced_prediction(
                    game_id=context.game_id,
                    prediction_result=prediction_result,
                    prediction_type=prediction_type
                )
                
                if history_saved:
                    logger.info(f"✅ 預測結果已保存到歷史管理系統: {context.game_id} ({prediction_type})")
                else:
                    # 如果歷史保存失敗，回退到原始方法
                    logger.warning(f"⚠️ 歷史保存失敗，使用備用保存方法: {context.game_id}")
                    self._fallback_save_to_db(prediction_result, context)
                
        except Exception as e:
            logger.error(f"💥 預測結果保存失敗: {e}")
            # 嘗試備用保存方法
            try:
                self._fallback_save_to_db(prediction_result, context)
            except Exception as fallback_error:
                logger.error(f"💥 備用保存也失敗: {fallback_error}")
                db.session.rollback()
    
    def _fallback_save_to_db(self, prediction_result: Dict, context: PredictionContext):
        """備用保存方法 - 原始Prediction表保存"""
        
        prediction = prediction_result.get('prediction', {})
        confidence_info = prediction_result.get('confidence', {})
        
        # 創建Prediction記錄
        pred_record = Prediction(
            game_id=context.game_id,
            predicted_home_score=prediction.get('home_predicted_runs', 0),
            predicted_away_score=prediction.get('away_predicted_runs', 0),
            home_win_probability=prediction.get('home_win_probability', 0.5),
            away_win_probability=prediction.get('away_win_probability', 0.5),
            confidence=confidence_info.get('overall_confidence', 0.5),
            predicted_total_runs=prediction.get('predicted_total_runs', 0),
            starting_pitcher_home=context.home_starting_pitcher,
            starting_pitcher_away=context.away_starting_pitcher,
            model_version=f"unified_v1.0_{prediction_result.get('prediction_strategy', 'unknown')}",
            features_used=json.dumps(context.to_dict()),
            prediction_date=datetime.now()
        )
        
        db.session.add(pred_record)
        db.session.commit()
        
        logger.info(f"📄 預測結果已保存到主表 (備用方法): {context.game_id}")

# 全局統一預測引擎實例
unified_engine = UnifiedPredictionEngine()

async def predict_game(game_id: str, target_date: Optional[date] = None, **kwargs) -> Dict[str, Any]:
    """統一預測函數的便捷接口"""
    return await unified_engine.predict_game(game_id, target_date, **kwargs)

# 使用示例
if __name__ == "__main__":
    async def test_unified_prediction():
        """測試統一預測引擎"""
        
        # 測試歷史預測 (解決您界面中的問題)
        print("🔍 測試歷史預測 (8/23)...")
        historical_result = await predict_game(
            game_id="test_game_20250823",
            target_date=date(2025, 8, 23)  # 明確指定歷史日期
        )
        
        print("歷史預測結果:")
        print(json.dumps(historical_result, indent=2, ensure_ascii=False, default=str))
        
        print("\n" + "="*50 + "\n")
        
        # 測試即時預測
        print("🔴 測試即時預測 (今天)...")
        live_result = await predict_game(
            game_id="test_game_today",
            target_date=None  # 會自動判斷為今天
        )
        
        print("即時預測結果:")
        print(json.dumps(live_result, indent=2, ensure_ascii=False, default=str))
    
    import asyncio
    asyncio.run(test_unified_prediction())