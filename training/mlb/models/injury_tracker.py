#!/usr/bin/env python3
"""
MLB傷兵報告追蹤系統
"""

import requests
import sys
import os
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional
import logging

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models.database import db

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InjuryTracker:
    """MLB傷兵報告追蹤器"""
    
    def __init__(self):
        self.app = create_app()
        self.base_url = "https://statsapi.mlb.com/api/v1"
        
        # 傷兵狀態代碼映射
        self.injury_status_codes = {
            'D7': {'description': 'Injured 7-Day', 'severity': 'Minor', 'days': 7},
            'D10': {'description': 'Injured 10-Day', 'severity': 'Minor', 'days': 10},
            'D15': {'description': 'Injured 15-Day', 'severity': 'Moderate', 'days': 15},
            'D60': {'description': 'Injured 60-Day', 'severity': 'Major', 'days': 60},
            'ILF': {'description': 'Injured - Full Season', 'severity': 'Season-Ending', 'days': 365},
            'RA': {'description': 'Rehab Assignment', 'severity': 'Recovering', 'days': 0},
            'A': {'description': 'Active', 'severity': 'Healthy', 'days': 0}
        }
    
    def get_all_team_injuries(self) -> Dict:
        """獲取所有球隊的傷兵報告"""
        logger.info("開始獲取所有球隊的傷兵報告...")
        
        injury_report = {
            'total_injured_players': 0,
            'teams': {},
            'injury_summary': {
                'minor': 0,      # 7-15天
                'moderate': 0,   # 15天
                'major': 0,      # 60天
                'season_ending': 0,  # 整季
                'recovering': 0  # 復健中
            },
            'position_impact': {
                'pitchers': 0,
                'catchers': 0,
                'infielders': 0,
                'outfielders': 0
            }
        }
        
        try:
            with self.app.app_context():
                from models.database import Team
                
                # 獲取所有球隊
                teams = db.session.query(Team).all()
                
                for team in teams:
                    team_injuries = self.get_team_injuries(team.team_id)
                    
                    if team_injuries['injured_players']:
                        injury_report['teams'][team.team_id] = {
                            'team_name': team.name,
                            'abbreviation': team.abbreviation,
                            'injuries': team_injuries
                        }
                        
                        # 統計總數
                        injury_report['total_injured_players'] += len(team_injuries['injured_players'])
                        
                        # 按嚴重程度統計
                        for player in team_injuries['injured_players']:
                            severity = player['severity']
                            if severity == 'Minor':
                                injury_report['injury_summary']['minor'] += 1
                            elif severity == 'Moderate':
                                injury_report['injury_summary']['moderate'] += 1
                            elif severity == 'Major':
                                injury_report['injury_summary']['major'] += 1
                            elif severity == 'Season-Ending':
                                injury_report['injury_summary']['season_ending'] += 1
                            elif severity == 'Recovering':
                                injury_report['injury_summary']['recovering'] += 1
                            
                            # 按位置統計
                            position_type = player['position_type']
                            if position_type == 'Pitcher':
                                injury_report['position_impact']['pitchers'] += 1
                            elif position_type == 'Catcher':
                                injury_report['position_impact']['catchers'] += 1
                            elif position_type == 'Infielder':
                                injury_report['position_impact']['infielders'] += 1
                            elif position_type == 'Outfielder':
                                injury_report['position_impact']['outfielders'] += 1
                    
                    logger.info(f"處理球隊 {team.abbreviation}: {len(team_injuries['injured_players'])} 名傷兵")
                
        except Exception as e:
            logger.error(f"獲取傷兵報告失敗: {e}")
        
        return injury_report
    
    def get_team_injuries(self, team_id: int) -> Dict:
        """獲取特定球隊的傷兵報告"""
        team_injuries = {
            'team_id': team_id,
            'injured_players': [],
            'total_injured': 0,
            'by_severity': {
                'minor': 0,
                'moderate': 0,
                'major': 0,
                'season_ending': 0,
                'recovering': 0
            }
        }
        
        try:
            # 使用depthChart獲取完整名單（包含傷兵）
            url = f"{self.base_url}/teams/{team_id}/roster"
            params = {'rosterType': 'depthChart'}
            
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'roster' in data:
                    for player in data['roster']:
                        status = player.get('status', {})
                        status_code = status.get('code', 'A')
                        
                        # 檢查是否為傷兵狀態
                        if status_code in ['D7', 'D10', 'D15', 'D60', 'ILF', 'RA']:
                            injury_info = self.injury_status_codes.get(status_code, {})
                            
                            player_info = {
                                'player_id': player.get('person', {}).get('id'),
                                'player_name': player.get('person', {}).get('fullName', 'Unknown'),
                                'position': player.get('position', {}).get('name', 'Unknown'),
                                'position_type': player.get('position', {}).get('type', 'Unknown'),
                                'position_abbr': player.get('position', {}).get('abbreviation', 'Unknown'),
                                'status_code': status_code,
                                'status_description': status.get('description', 'Unknown'),
                                'severity': injury_info.get('severity', 'Unknown'),
                                'expected_days': injury_info.get('days', 0),
                                'jersey_number': player.get('jerseyNumber', '')
                            }
                            
                            team_injuries['injured_players'].append(player_info)
                            
                            # 統計按嚴重程度
                            severity = injury_info.get('severity', '').lower()
                            if severity in team_injuries['by_severity']:
                                team_injuries['by_severity'][severity] += 1
                    
                    team_injuries['total_injured'] = len(team_injuries['injured_players'])
                    
        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 傷兵報告失敗: {e}")
        
        return team_injuries
    
    def get_player_injury_history(self, player_id: int, days_back: int = 365) -> Dict:
        """獲取球員的傷兵歷史"""
        injury_history = {
            'player_id': player_id,
            'player_name': '',
            'injury_periods': [],
            'total_days_injured': 0,
            'injury_frequency': 0
        }
        
        try:
            # 獲取球員的交易/異動記錄
            end_date = date.today()
            start_date = end_date - timedelta(days=days_back)
            
            url = f"{self.base_url}/transactions"
            params = {
                'startDate': start_date.strftime('%Y-%m-%d'),
                'endDate': end_date.strftime('%Y-%m-%d'),
                'playerId': player_id
            }
            
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'transactions' in data:
                    injury_transactions = []
                    
                    for transaction in data['transactions']:
                        description = transaction.get('description', '').lower()
                        
                        # 檢查是否為傷兵相關異動
                        if any(keyword in description for keyword in ['injured', 'disabled', 'il', 'rehab']):
                            injury_transactions.append({
                                'date': transaction.get('date'),
                                'description': transaction.get('description'),
                                'type': transaction.get('typeCode')
                            })
                    
                    injury_history['injury_periods'] = injury_transactions
                    injury_history['injury_frequency'] = len(injury_transactions)
                    
                    # 獲取球員姓名
                    if injury_transactions:
                        player_url = f"{self.base_url}/people/{player_id}"
                        player_response = requests.get(player_url, timeout=30)
                        
                        if player_response.status_code == 200:
                            player_data = player_response.json()
                            if 'people' in player_data and player_data['people']:
                                injury_history['player_name'] = player_data['people'][0].get('fullName', 'Unknown')
                
        except Exception as e:
            logger.error(f"獲取球員 {player_id} 傷兵歷史失敗: {e}")
        
        return injury_history
    
    def analyze_injury_impact_on_predictions(self, game_date: str = None) -> Dict:
        """分析傷兵對比賽預測的影響"""
        if game_date is None:
            game_date = date.today().strftime('%Y-%m-%d')
        
        impact_analysis = {
            'date': game_date,
            'games_affected': [],
            'total_impact_score': 0,
            'recommendations': []
        }
        
        try:
            with self.app.app_context():
                from models.database import Game
                
                # 獲取指定日期的比賽
                games = db.session.query(Game).filter(
                    Game.date == datetime.strptime(game_date, '%Y-%m-%d').date()
                ).all()
                
                for game in games:
                    # 需要通過team abbreviation找到team_id
                    from models.database import Team

                    home_team_obj = db.session.query(Team).filter_by(team_code=game.home_team).first()
                    away_team_obj = db.session.query(Team).filter_by(team_code=game.away_team).first()

                    if home_team_obj and away_team_obj:
                        # 獲取兩隊的傷兵情況
                        home_injuries = self.get_team_injuries(home_team_obj.team_id)
                        away_injuries = self.get_team_injuries(away_team_obj.team_id)

                        # 計算影響分數
                        home_impact = self.calculate_injury_impact_score(home_injuries)
                        away_impact = self.calculate_injury_impact_score(away_injuries)

                        game_impact = {
                            'game_id': game.game_id,
                            'home_team': game.home_team,
                            'away_team': game.away_team,
                            'home_injury_impact': home_impact,
                            'away_injury_impact': away_impact,
                            'total_impact': home_impact + away_impact,
                            'advantage': 'home' if away_impact > home_impact else 'away' if home_impact > away_impact else 'neutral'
                        }
                    
                    impact_analysis['games_affected'].append(game_impact)
                    impact_analysis['total_impact_score'] += game_impact['total_impact']
                
                # 生成建議
                if impact_analysis['total_impact_score'] > 10:
                    impact_analysis['recommendations'].append("高傷兵影響日，建議調整預測權重")
                
                for game in impact_analysis['games_affected']:
                    if game['total_impact'] > 5:
                        impact_analysis['recommendations'].append(
                            f"{game['home_team']} vs {game['away_team']}: 傷兵影響顯著，{game['advantage']}隊有優勢"
                        )
                
        except Exception as e:
            logger.error(f"分析傷兵影響失敗: {e}")
        
        return impact_analysis
    
    def calculate_injury_impact_score(self, team_injuries: Dict) -> float:
        """計算球隊傷兵影響分數"""
        impact_score = 0.0
        
        # 根據位置和嚴重程度計算影響分數
        position_weights = {
            'Pitcher': 3.0,      # 投手影響最大
            'Catcher': 2.0,      # 捕手次之
            'Infielder': 1.5,    # 內野手
            'Outfielder': 1.0    # 外野手
        }
        
        severity_weights = {
            'Minor': 1.0,
            'Moderate': 2.0,
            'Major': 3.0,
            'Season-Ending': 4.0,
            'Recovering': 0.5
        }
        
        for player in team_injuries.get('injured_players', []):
            position_weight = position_weights.get(player['position_type'], 1.0)
            severity_weight = severity_weights.get(player['severity'], 1.0)
            
            impact_score += position_weight * severity_weight
        
        return impact_score
