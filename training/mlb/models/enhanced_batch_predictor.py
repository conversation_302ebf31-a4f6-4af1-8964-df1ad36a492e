#!/usr/bin/env python3
"""
增強批次預測適配器
使用新的EnhancedMLBPredictor算法進行批次預測
"""

import logging
import asyncio
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
from models.enhanced_prediction_service import get_enhanced_service

logger = logging.getLogger(__name__)

class EnhancedBatchPredictor:
    """增強批次預測器"""
    
    def __init__(self, model_path: str = None):
        self.enhanced_service = None
        self.model_path = model_path
        self.is_initialized = False
        
    def initialize(self):
        """初始化增強預測服務"""
        try:
            if not self.model_path:
                # 尋找最新的增強模型
                import glob
                model_files = glob.glob("enhanced_mlb_predictor_*.joblib")
                if model_files:
                    self.model_path = sorted(model_files)[-1]
                else:
                    raise FileNotFoundError("找不到增強模型檔案")
            
            # 獲取並初始化增強服務
            self.enhanced_service = get_enhanced_service(self.model_path)
            success = self.enhanced_service.load_model(self.model_path)
            
            if success:
                self.is_initialized = True
                logger.info(f"✅ 增強批次預測器初始化成功: {self.model_path}")
                return True
            else:
                logger.error("❌ 增強批次預測器初始化失敗")
                return False
                
        except Exception as e:
            logger.error(f"❌ 初始化增強批次預測器時出錯: {e}")
            return False
    
    async def enhanced_predict_game(
        self, 
        game_id: str,
        away_team: str,
        home_team: str,
        target_date: date,
        training_end_date: date = None,
        save_to_db: bool = True
    ) -> Dict:
        """使用增強算法預測單場比賽"""
        try:
            if not self.is_initialized:
                success = self.initialize()
                if not success:
                    return {
                        'success': False,
                        'error': '增強預測服務未初始化',
                        'game_id': game_id
                    }
            
            # 準備遊戲特徵數據
            game_features = self._prepare_game_features(
                away_team, home_team, target_date, training_end_date
            )
            
            # 使用增強算法進行預測
            prediction = self.enhanced_service.predict_game(game_features)
            
            if 'error' in prediction:
                logger.warning(f"⚠️ 增強預測失敗 {game_id}: {prediction['error']}")
                return {
                    'success': False,
                    'error': prediction['error'],
                    'game_id': game_id
                }
            
            # 如果需要保存到數據庫
            if save_to_db:
                save_success = self._save_prediction_to_db(game_id, prediction)
                if not save_success:
                    logger.warning(f"⚠️ 保存預測到數據庫失敗: {game_id}")
            
            logger.info(f"✅ 增強預測成功: {away_team} @ {home_team} ({target_date})")
            
            return {
                'success': True,
                'game_id': game_id,
                'prediction': prediction,
                'algorithm': 'enhanced_ensemble_v2',
                'confidence': prediction.get('confidence', 0.0),
                'predicted_home_score': prediction.get('predicted_home_score', 0),
                'predicted_away_score': prediction.get('predicted_away_score', 0),
                'predicted_total_runs': prediction.get('predicted_total_runs', 0),
                'home_win_probability': prediction.get('home_win_probability', 0.5),
                'away_win_probability': prediction.get('away_win_probability', 0.5)
            }
            
        except Exception as e:
            logger.error(f"❌ 增強預測過程中出錯 {game_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'game_id': game_id
            }
    
    def _prepare_game_features(
        self, 
        away_team: str, 
        home_team: str, 
        target_date: date,
        training_end_date: date = None
    ) -> Dict:
        """準備遊戲特徵數據"""
        try:
            # 這裡可以集成真實的特徵工程邏輯
            # 目前使用基礎特徵數據
            from models.database import Game, TeamStats
            import numpy as np
            
            # 獲取球隊歷史統計數據（截止到training_end_date）
            cutoff_date = training_end_date or target_date - timedelta(days=1)
            
            # 查詢球隊近期表現
            home_recent_games = Game.query.filter(
                Game.home_team == home_team,
                Game.date < cutoff_date,
                Game.home_score.isnot(None)
            ).order_by(Game.date.desc()).limit(10).all()
            
            away_recent_games = Game.query.filter(
                Game.away_team == away_team,
                Game.date < cutoff_date,
                Game.away_score.isnot(None)
            ).order_by(Game.date.desc()).limit(10).all()
            
            # 計算基礎統計
            home_runs_scored = np.mean([g.home_score for g in home_recent_games]) if home_recent_games else 4.5
            home_runs_allowed = np.mean([g.away_score for g in home_recent_games]) if home_recent_games else 4.5
            
            away_runs_scored = np.mean([g.away_score for g in away_recent_games]) if away_recent_games else 4.5
            away_runs_allowed = np.mean([g.home_score for g in away_recent_games]) if away_recent_games else 4.5
            
            # 準備特徵字典
            features = {
                'game_id': f"{away_team}@{home_team}_{target_date}",
                'home_team': home_team,
                'away_team': away_team,
                'prediction_timestamp': target_date.isoformat(),
                
                # 球隊統計特徵
                'home_runs_scored_avg': home_runs_scored,
                'home_runs_allowed_avg': home_runs_allowed,
                'home_batting_avg': 0.250,  # 默認值，實際可以從TeamStats獲取
                'home_era': 4.20,
                'home_win_percentage': 0.500,
                'home_recent_form': len(home_recent_games) / 10.0 if home_recent_games else 0.5,
                
                'away_runs_scored_avg': away_runs_scored,
                'away_runs_allowed_avg': away_runs_allowed,
                'away_batting_avg': 0.250,
                'away_era': 4.20,
                'away_win_percentage': 0.500,
                'away_recent_form': len(away_recent_games) / 10.0 if away_recent_games else 0.5
            }
            
            logger.debug(f"準備特徵數據完成: {away_team} @ {home_team}")
            return features
            
        except Exception as e:
            logger.error(f"準備特徵數據時出錯: {e}")
            # 返回默認特徵
            return {
                'game_id': f"{away_team}@{home_team}_{target_date}",
                'home_team': home_team,
                'away_team': away_team,
                'prediction_timestamp': target_date.isoformat(),
                'home_runs_scored_avg': 4.5,
                'home_runs_allowed_avg': 4.5,
                'home_batting_avg': 0.250,
                'home_era': 4.20,
                'home_win_percentage': 0.500,
                'home_recent_form': 0.500,
                'away_runs_scored_avg': 4.5,
                'away_runs_allowed_avg': 4.5,
                'away_batting_avg': 0.250,
                'away_era': 4.20,
                'away_win_percentage': 0.500,
                'away_recent_form': 0.500
            }
    
    def _save_prediction_to_db(self, game_id: str, prediction: Dict) -> bool:
        """保存預測結果到數據庫"""
        try:
            from models.database import db, PredictionHistory
            from datetime import datetime
            
            # 創建或更新預測記錄
            existing_prediction = PredictionHistory.query.filter_by(game_id=game_id).first()
            
            if existing_prediction:
                # 更新現有記錄
                existing_prediction.predicted_home_score = prediction.get('predicted_home_score', 0)
                existing_prediction.predicted_away_score = prediction.get('predicted_away_score', 0) 
                existing_prediction.home_win_probability = prediction.get('home_win_probability', 0.5)
                existing_prediction.overall_confidence = prediction.get('confidence', 0.0)
                existing_prediction.model_version = 'enhanced_v2'
                existing_prediction.updated_at = datetime.utcnow()
            else:
                # 創建新記錄
                new_prediction = PredictionHistory(
                    game_id=game_id,
                    predicted_home_score=prediction.get('predicted_home_score', 0),
                    predicted_away_score=prediction.get('predicted_away_score', 0),
                    home_win_probability=prediction.get('home_win_probability', 0.5),
                    overall_confidence=prediction.get('confidence', 0.0),
                    model_version='enhanced_v2',
                    prediction_date=datetime.utcnow().date(),
                    created_at=datetime.utcnow()
                )
                db.session.add(new_prediction)
            
            db.session.commit()
            logger.debug(f"✅ 預測已保存到數據庫: {game_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存預測到數據庫失敗 {game_id}: {e}")
            db.session.rollback()
            return False

# 創建全局實例
enhanced_batch_predictor = EnhancedBatchPredictor()

def get_enhanced_batch_predictor():
    """獲取增強批次預測器單例"""
    return enhanced_batch_predictor

def initialize_enhanced_batch_predictor(model_path: str = None):
    """初始化增強批次預測器"""
    global enhanced_batch_predictor
    if model_path:
        enhanced_batch_predictor.model_path = model_path
    
    return enhanced_batch_predictor.initialize()
