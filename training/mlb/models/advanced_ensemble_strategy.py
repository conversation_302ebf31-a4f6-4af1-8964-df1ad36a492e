"""
高級集成策略 - 動態模型權重和優化集成方法
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, ElasticNet
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class AdvancedEnsembleStrategy:
    """高級集成策略"""
    
    def __init__(self):
        self.base_models = {
            'xgboost': xgb.XGBRegressor(
                n_estimators=200, max_depth=6, learning_rate=0.1,
                subsample=0.8, colsample_bytree=0.8, random_state=42
            ),
            'random_forest': RandomForestRegressor(
                n_estimators=200, max_depth=10, min_samples_split=5,
                min_samples_leaf=2, random_state=42
            ),
            'gradient_boost': GradientBoostingRegressor(
                n_estimators=200, max_depth=6, learning_rate=0.1,
                subsample=0.8, random_state=42
            ),
            'neural_network': MLPRegressor(
                hidden_layer_sizes=(100, 50), max_iter=500,
                alpha=0.01, random_state=42
            ),
            'support_vector': SVR(
                kernel='rbf', C=1.0, gamma='scale'
            )
        }
        
        self.meta_models = {
            'linear': LinearRegression(),
            'ridge': Ridge(alpha=1.0),
            'elastic_net': ElasticNet(alpha=0.1, l1_ratio=0.5)
        }
        
        self.model_weights = {}
        self.model_performance = {}
        self.ensemble_performance = {}
        self.optimal_weights = {}
        
    def train_ensemble_with_optimization(self, X: np.ndarray, y: np.ndarray, 
                                       cv_splits: int = 5) -> Dict:
        """訓練優化的集成模型"""
        logger.info("開始訓練高級集成模型...")
        
        # 時間序列交叉驗證
        tscv = TimeSeriesSplit(n_splits=cv_splits)
        
        # 1. 評估基礎模型性能
        base_performance = self._evaluate_base_models(X, y, tscv)
        
        # 2. 生成元特徵
        meta_features, meta_targets = self._generate_meta_features(X, y, tscv)
        
        # 3. 優化模型權重
        optimal_weights = self._optimize_model_weights(meta_features, meta_targets)
        
        # 4. 訓練最終集成模型
        final_models = self._train_final_ensemble(X, y, optimal_weights)
        
        # 5. 評估集成性能
        ensemble_performance = self._evaluate_ensemble_performance(
            X, y, final_models, optimal_weights, tscv
        )
        
        return {
            'base_performance': base_performance,
            'optimal_weights': optimal_weights,
            'final_models': final_models,
            'ensemble_performance': ensemble_performance,
            'meta_features_shape': meta_features.shape if meta_features is not None else None
        }
    
    def _evaluate_base_models(self, X: np.ndarray, y: np.ndarray, 
                            cv: TimeSeriesSplit) -> Dict:
        """評估基礎模型性能"""
        logger.info("評估基礎模型性能...")
        
        performance = {}
        
        for model_name, model in self.base_models.items():
            try:
                # 交叉驗證評分
                cv_scores = cross_val_score(
                    model, X, y, cv=cv, 
                    scoring='neg_mean_absolute_error', n_jobs=-1
                )
                
                mae_scores = -cv_scores
                
                performance[model_name] = {
                    'cv_mae_mean': np.mean(mae_scores),
                    'cv_mae_std': np.std(mae_scores),
                    'cv_scores': mae_scores.tolist(),
                    'stability': 1 / (1 + np.std(mae_scores))  # 穩定性指標
                }
                
                logger.info(f"{model_name}: MAE = {np.mean(mae_scores):.3f} ± {np.std(mae_scores):.3f}")
                
            except Exception as e:
                logger.warning(f"評估 {model_name} 失敗: {e}")
                performance[model_name] = {
                    'cv_mae_mean': float('inf'),
                    'cv_mae_std': float('inf'),
                    'cv_scores': [],
                    'stability': 0
                }
        
        self.model_performance = performance
        return performance
    
    def _generate_meta_features(self, X: np.ndarray, y: np.ndarray, 
                              cv: TimeSeriesSplit) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """生成元特徵用於集成學習"""
        logger.info("生成元特徵...")
        
        meta_features_list = []
        meta_targets_list = []
        
        for train_idx, val_idx in cv.split(X):
            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            fold_predictions = []
            
            for model_name, model in self.base_models.items():
                try:
                    # 訓練模型
                    model_copy = type(model)(**model.get_params())
                    model_copy.fit(X_train, y_train)
                    
                    # 預測驗證集
                    val_pred = model_copy.predict(X_val)
                    fold_predictions.append(val_pred)
                    
                except Exception as e:
                    logger.warning(f"生成 {model_name} 元特徵失敗: {e}")
                    # 使用平均值作為備用預測
                    val_pred = np.full(len(X_val), np.mean(y_train))
                    fold_predictions.append(val_pred)
            
            if fold_predictions:
                # 堆疊預測結果
                fold_meta_features = np.column_stack(fold_predictions)
                meta_features_list.append(fold_meta_features)
                meta_targets_list.append(y_val)
        
        if meta_features_list:
            meta_features = np.vstack(meta_features_list)
            meta_targets = np.concatenate(meta_targets_list)
            return meta_features, meta_targets
        else:
            logger.error("無法生成元特徵")
            return None, None
    
    def _optimize_model_weights(self, meta_features: np.ndarray, 
                              meta_targets: np.ndarray) -> Dict:
        """優化模型權重"""
        logger.info("優化模型權重...")
        
        if meta_features is None or meta_targets is None:
            logger.warning("元特徵為空，使用等權重")
            return {name: 1.0/len(self.base_models) for name in self.base_models.keys()}
        
        n_models = meta_features.shape[1]
        model_names = list(self.base_models.keys())
        
        def objective(weights):
            """優化目標函數"""
            weights = weights / np.sum(weights)  # 標準化權重
            ensemble_pred = np.dot(meta_features, weights)
            mae = mean_absolute_error(meta_targets, ensemble_pred)
            return mae
        
        # 約束條件：權重和為1，每個權重非負
        constraints = {'type': 'eq', 'fun': lambda w: np.sum(w) - 1}
        bounds = [(0, 1) for _ in range(n_models)]
        
        # 初始權重（基於模型性能）
        initial_weights = []
        for model_name in model_names:
            if model_name in self.model_performance:
                # 性能越好，初始權重越高
                mae = self.model_performance[model_name]['cv_mae_mean']
                stability = self.model_performance[model_name]['stability']
                weight = stability / (mae + 1e-8)
            else:
                weight = 1.0
            initial_weights.append(weight)
        
        # 標準化初始權重
        initial_weights = np.array(initial_weights)
        initial_weights = initial_weights / np.sum(initial_weights)
        
        try:
            # 優化權重
            result = minimize(
                objective, initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000}
            )
            
            if result.success:
                optimal_weights = result.x / np.sum(result.x)
                logger.info(f"權重優化成功，最優MAE: {result.fun:.4f}")
            else:
                logger.warning("權重優化失敗，使用基於性能的權重")
                optimal_weights = initial_weights
                
        except Exception as e:
            logger.warning(f"權重優化異常: {e}，使用等權重")
            optimal_weights = np.ones(n_models) / n_models
        
        # 轉換為字典格式
        weight_dict = {}
        for i, model_name in enumerate(model_names):
            weight_dict[model_name] = float(optimal_weights[i])
        
        self.optimal_weights = weight_dict
        
        # 記錄權重信息
        logger.info("最優模型權重:")
        for model_name, weight in weight_dict.items():
            logger.info(f"  {model_name}: {weight:.4f}")
        
        return weight_dict
    
    def _train_final_ensemble(self, X: np.ndarray, y: np.ndarray, 
                            optimal_weights: Dict) -> Dict:
        """訓練最終集成模型"""
        logger.info("訓練最終集成模型...")
        
        final_models = {}
        
        # 訓練所有基礎模型
        for model_name, model in self.base_models.items():
            try:
                model_copy = type(model)(**model.get_params())
                model_copy.fit(X, y)
                final_models[model_name] = model_copy
                logger.info(f"✅ {model_name} 訓練完成")
            except Exception as e:
                logger.error(f"❌ {model_name} 訓練失敗: {e}")
        
        return final_models
    
    def _evaluate_ensemble_performance(self, X: np.ndarray, y: np.ndarray,
                                     final_models: Dict, optimal_weights: Dict,
                                     cv: TimeSeriesSplit) -> Dict:
        """評估集成模型性能"""
        logger.info("評估集成模型性能...")
        
        ensemble_predictions = []
        true_values = []
        
        for train_idx, val_idx in cv.split(X):
            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            # 訓練折疊模型
            fold_models = {}
            for model_name, model in self.base_models.items():
                if model_name in optimal_weights:
                    try:
                        model_copy = type(model)(**model.get_params())
                        model_copy.fit(X_train, y_train)
                        fold_models[model_name] = model_copy
                    except Exception as e:
                        logger.warning(f"折疊模型 {model_name} 訓練失敗: {e}")
            
            # 集成預測
            if fold_models:
                fold_ensemble_pred = self._ensemble_predict(X_val, fold_models, optimal_weights)
                ensemble_predictions.extend(fold_ensemble_pred)
                true_values.extend(y_val)
        
        if ensemble_predictions:
            mae = mean_absolute_error(true_values, ensemble_predictions)
            mse = mean_squared_error(true_values, ensemble_predictions)
            r2 = r2_score(true_values, ensemble_predictions)
            
            performance = {
                'cv_mae': mae,
                'cv_mse': mse,
                'cv_rmse': np.sqrt(mse),
                'cv_r2': r2,
                'prediction_count': len(ensemble_predictions)
            }
            
            logger.info(f"集成模型性能: MAE={mae:.4f}, R²={r2:.4f}")
            
        else:
            performance = {
                'cv_mae': float('inf'),
                'cv_mse': float('inf'),
                'cv_rmse': float('inf'),
                'cv_r2': 0,
                'prediction_count': 0
            }
            logger.warning("無法評估集成模型性能")
        
        self.ensemble_performance = performance
        return performance
    
    def _ensemble_predict(self, X: np.ndarray, models: Dict, weights: Dict) -> np.ndarray:
        """使用權重進行集成預測"""
        predictions = []
        total_weight = 0

        # 處理不同的模型結構
        actual_models = models
        if 'base_models' in models:
            actual_models = models['base_models']

        for model_name, model in actual_models.items():
            if model_name in weights and hasattr(model, 'predict'):
                try:
                    pred = model.predict(X)
                    weight = weights[model_name]
                    predictions.append(pred * weight)
                    total_weight += weight
                    logger.debug(f"模型 {model_name} 預測成功，權重: {weight}")
                except Exception as e:
                    logger.warning(f"模型 {model_name} 預測失敗: {e}")

        if predictions and total_weight > 0:
            ensemble_pred = np.sum(predictions, axis=0) / total_weight
            logger.debug(f"集成預測成功，使用 {len(predictions)} 個模型")
            return ensemble_pred
        else:
            logger.warning("集成預測失敗，返回零預測")
            return np.zeros(len(X))
    
    def predict_with_ensemble(self, X: np.ndarray, models: Dict, weights: Dict) -> Dict:
        """使用集成模型進行預測"""
        try:
            # 處理不同的模型結構
            actual_models = models
            if 'base_models' in models:
                actual_models = models['base_models']
            elif not any(hasattr(v, 'predict') for v in models.values()):
                # 如果models是嵌套字典，嘗試展平
                actual_models = {}
                for k, v in models.items():
                    if isinstance(v, dict):
                        actual_models.update(v)
                    elif hasattr(v, 'predict'):
                        actual_models[k] = v

            # 獲取各模型預測
            individual_predictions = {}
            for model_name, model in actual_models.items():
                if model_name in weights and hasattr(model, 'predict'):
                    try:
                        pred = model.predict(X)
                        individual_predictions[model_name] = pred
                    except Exception as e:
                        logger.warning(f"模型 {model_name} 預測失敗: {e}")

            # 集成預測
            ensemble_pred = self._ensemble_predict(X, actual_models, weights)
            
            # 計算預測不確定性
            if len(individual_predictions) > 1:
                pred_matrix = np.column_stack(list(individual_predictions.values()))
                prediction_std = np.std(pred_matrix, axis=1)
                prediction_confidence = 1 / (1 + prediction_std)
            else:
                prediction_std = np.zeros(len(ensemble_pred))
                prediction_confidence = np.ones(len(ensemble_pred))
            
            return {
                'ensemble_prediction': ensemble_pred,
                'individual_predictions': individual_predictions,
                'prediction_std': prediction_std,
                'prediction_confidence': prediction_confidence,
                'models_used': list(individual_predictions.keys()),
                'weights_used': {k: v for k, v in weights.items() if k in individual_predictions}
            }
            
        except Exception as e:
            logger.error(f"集成預測失敗: {e}")
            return {
                'ensemble_prediction': np.zeros(len(X)),
                'individual_predictions': {},
                'prediction_std': np.zeros(len(X)),
                'prediction_confidence': np.zeros(len(X)),
                'models_used': [],
                'weights_used': {}
            }
    
    def get_ensemble_summary(self) -> Dict:
        """獲取集成模型摘要"""
        return {
            'base_models': list(self.base_models.keys()),
            'model_performance': self.model_performance,
            'optimal_weights': self.optimal_weights,
            'ensemble_performance': self.ensemble_performance,
            'total_models': len(self.base_models)
        }
