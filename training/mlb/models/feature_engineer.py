"""
基礎特徵工程器 - MLB 預測系統
提取比賽相關的基礎特徵
"""

import logging
import numpy as np
import pandas as pd
from datetime import date, timedelta
from typing import Dict, List, Optional
from sqlalchemy import text
from .database import db, Game, Team, TeamStats, BoxScore, Player, PlayerStats

logger = logging.getLogger(__name__)

class FeatureEngineer:
    """基礎特徵工程器"""

    def __init__(self):
        self.logger = logger

    def extract_comprehensive_features(self, home_team: str, away_team: str, game_date: date, matchup_analysis: Optional[Dict] = None) -> Dict:
        """提取全面的比賽特徵 - 智能分析比賽類型"""
        try:
            features = {}

            # 1. 球隊戰績分析
            features.update(self._get_team_records(home_team, away_team, game_date))

            # 2. 投手對戰分析（投手戰 vs 打擊戰）
            features.update(self._analyze_pitching_matchup(home_team, away_team, game_date))

            # 3. 打擊火力分析
            features.update(self._analyze_offensive_strength(home_team, away_team, game_date))

            # 4. 比賽類型預測
            features.update(self._predict_game_scenario(features))

            # 如果有智能對戰分析，添加相關特徵
            if matchup_analysis:
                features.update(self._get_matchup_analysis_features(matchup_analysis))

            return features

        except Exception as e:
            self.logger.error(f"特徵提取失敗 {away_team} @ {home_team}: {e}")
            return self._get_safe_default_features()

    def _get_team_basic_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取球隊基礎特徵"""
        features = {}

        try:
            # 獲取球隊統計
            home_stats = self._get_team_stats(home_team, game_date)
            away_stats = self._get_team_stats(away_team, game_date)

            # 基礎統計特徵
            features.update({
                'home_wins': home_stats.get('wins', 0),
                'home_losses': home_stats.get('losses', 0),
                'home_win_pct': home_stats.get('win_percentage', 0.5),
                'away_wins': away_stats.get('wins', 0),
                'away_losses': away_stats.get('losses', 0),
                'away_win_pct': away_stats.get('win_percentage', 0.5),
                'home_runs_scored': home_stats.get('runs_scored', 4.5),
                'home_runs_allowed': home_stats.get('runs_allowed', 4.5),
                'away_runs_scored': away_stats.get('runs_scored', 4.5),
                'away_runs_allowed': away_stats.get('runs_allowed', 4.5),
            })

        except Exception as e:
            self.logger.warning(f"球隊基礎特徵提取失敗: {e}")

        return features

    def _get_team_stats(self, team: str, game_date: date) -> Dict:
        """獲取球隊統計數據"""
        try:
            # 查詢球隊統計
            team_obj = Team.query.filter_by(team_code=team).first()
            if not team_obj:
                return {}

            # 獲取最新的球隊統計
            stats = TeamStats.query.filter_by(team_id=team_obj.team_id).first()
            if not stats:
                return {}

            return {
                'wins': stats.wins or 0,
                'losses': stats.losses or 0,
                'win_percentage': stats.win_percentage or 0.5,
                'runs_scored': stats.runs_scored or 4.5,
                'runs_allowed': stats.runs_allowed or 4.5,
                'batting_avg': stats.batting_avg or 0.250,
                'era': stats.era or 4.50,
            }

        except Exception as e:
            self.logger.warning(f"獲取球隊統計失敗 {team}: {e}")
            return {}

    def _get_head_to_head_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取歷史對戰特徵"""
        features = {}

        try:
            # 查詢歷史對戰記錄
            h2h_games = Game.query.filter(
                ((Game.home_team == home_team) & (Game.away_team == away_team)) |
                ((Game.home_team == away_team) & (Game.away_team == home_team)),
                Game.date < game_date,
                Game.game_status == 'completed'
            ).order_by(Game.date.desc()).limit(10).all()

            if h2h_games:
                home_wins = sum(1 for g in h2h_games if
                               (g.home_team == home_team and g.home_score > g.away_score) or
                               (g.away_team == home_team and g.away_score > g.home_score))

                features.update({
                    'h2h_games_count': len(h2h_games),
                    'h2h_home_wins': home_wins,
                    'h2h_home_win_pct': home_wins / len(h2h_games) if h2h_games else 0.5,
                })
            else:
                features.update({
                    'h2h_games_count': 0,
                    'h2h_home_wins': 0,
                    'h2h_home_win_pct': 0.5,
                })

        except Exception as e:
            self.logger.warning(f"歷史對戰特徵提取失敗: {e}")

        return features

    def _get_recent_performance_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取最近表現特徵"""
        features = {}

        try:
            # 獲取最近10場比賽
            home_recent = self._get_recent_games(home_team, game_date, 10)
            away_recent = self._get_recent_games(away_team, game_date, 10)

            # 計算最近表現
            features.update({
                'home_recent_wins': sum(1 for g in home_recent if self._is_team_win(g, home_team)),
                'home_recent_win_pct': sum(1 for g in home_recent if self._is_team_win(g, home_team)) / len(home_recent) if home_recent else 0.5,
                'away_recent_wins': sum(1 for g in away_recent if self._is_team_win(g, away_team)),
                'away_recent_win_pct': sum(1 for g in away_recent if self._is_team_win(g, away_team)) / len(away_recent) if away_recent else 0.5,
            })

        except Exception as e:
            self.logger.warning(f"最近表現特徵提取失敗: {e}")

        return features

    def _get_recent_games(self, team: str, game_date: date, limit: int = 10) -> List:
        """獲取球隊最近的比賽"""
        try:
            return Game.query.filter(
                ((Game.home_team == team) | (Game.away_team == team)),
                Game.date < game_date,
                Game.game_status == 'completed'
            ).order_by(Game.date.desc()).limit(limit).all()
        except Exception as e:
            self.logger.warning(f"獲取最近比賽失敗 {team}: {e}")
            return []

    def _is_team_win(self, game, team: str) -> bool:
        """判斷球隊是否贏得比賽"""
        # 處理 None 值
        home_score = game.home_score if game.home_score is not None else 0
        away_score = game.away_score if game.away_score is not None else 0

        if game.home_team == team:
            return home_score > away_score
        else:
            return away_score > home_score

    def _get_matchup_analysis_features(self, matchup_analysis: Dict) -> Dict:
        """從智能對戰分析報告中提取特徵"""
        features = {}

        try:
            # 投手強度分數
            if 'pitcher_scores' in matchup_analysis:
                features['home_pitcher_strength_score'] = matchup_analysis['pitcher_scores']['home'].get('score', 50.0)
                features['away_pitcher_strength_score'] = matchup_analysis['pitcher_scores']['away'].get('score', 50.0)
                features['pitcher_strength_diff'] = features['home_pitcher_strength_score'] - features['away_pitcher_strength_score']

            # 比賽類型 (One-Hot Encoding)
            matchup_type = matchup_analysis.get('matchup_type', 'EVEN_MATCH')
            features['matchup_type_PITCHER_DUEL'] = 1 if matchup_type == 'PITCHER_DUEL' else 0
            features['matchup_type_SLUGFEST'] = 1 if matchup_type == 'SLUGFEST' else 0
            features['matchup_type_MISMATCH'] = 1 if matchup_type == 'MISMATCH' else 0

            # 預期總分環境 (One-Hot Encoding)
            run_environment = matchup_analysis.get('run_environment', 'MEDIUM')
            features['run_env_LOW'] = 1 if run_environment == 'LOW' else 0
            features['run_env_HIGH'] = 1 if run_environment == 'HIGH' else 0

        except Exception as e:
            self.logger.warning(f"對戰分析特徵提取失敗: {e}")

        return features

    def _get_team_records(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取球隊戰績"""
        features = {}
        try:
            # 最近20場比賽戰績
            for team, prefix in [(home_team, 'home'), (away_team, 'away')]:
                recent_games = Game.query.filter(
                    ((Game.home_team == team) | (Game.away_team == team)),
                    Game.date < game_date,
                    Game.game_status == 'completed'
                ).order_by(Game.date.desc()).limit(20).all()

                wins = 0
                total_runs = 0
                total_allowed = 0

                for game in recent_games:
                    home_score = game.home_score or 0
                    away_score = game.away_score or 0

                    if game.home_team == team:
                        if home_score > away_score:
                            wins += 1
                        total_runs += home_score
                        total_allowed += away_score
                    else:
                        if away_score > home_score:
                            wins += 1
                        total_runs += away_score
                        total_allowed += home_score

                games_count = len(recent_games)
                if games_count > 0:
                    features[f'{prefix}_win_pct'] = wins / games_count
                    features[f'{prefix}_avg_runs'] = total_runs / games_count
                    features[f'{prefix}_avg_allowed'] = total_allowed / games_count
                    features[f'{prefix}_run_diff'] = (total_runs - total_allowed) / games_count
                else:
                    features[f'{prefix}_win_pct'] = 0.5
                    features[f'{prefix}_avg_runs'] = 4.5
                    features[f'{prefix}_avg_allowed'] = 4.5
                    features[f'{prefix}_run_diff'] = 0.0

        except Exception as e:
            self.logger.warning(f"球隊戰績分析失敗: {e}")

        return features

    def _analyze_pitching_matchup(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """分析投手對戰 - 判斷是否為投手戰"""
        features = {}
        try:
            # 分析兩隊最近的投手表現
            for team, prefix in [(home_team, 'home'), (away_team, 'away')]:
                recent_games = Game.query.filter(
                    ((Game.home_team == team) | (Game.away_team == team)),
                    Game.date < game_date,
                    Game.game_status == 'completed'
                ).order_by(Game.date.desc()).limit(10).all()

                total_allowed = 0
                low_scoring_games = 0  # 失分少於4分的比賽

                for game in recent_games:
                    home_score = game.home_score or 0
                    away_score = game.away_score or 0

                    if game.home_team == team:
                        allowed = away_score
                    else:
                        allowed = home_score

                    total_allowed += allowed
                    if allowed < 4:
                        low_scoring_games += 1

                games_count = len(recent_games)
                if games_count > 0:
                    features[f'{prefix}_pitcher_era'] = total_allowed / games_count
                    features[f'{prefix}_pitcher_stability'] = low_scoring_games / games_count
                else:
                    features[f'{prefix}_pitcher_era'] = 4.5
                    features[f'{prefix}_pitcher_stability'] = 0.3

        except Exception as e:
            self.logger.warning(f"投手分析失敗: {e}")

        return features

    def _analyze_offensive_strength(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """分析打擊火力"""
        features = {}
        try:
            for team, prefix in [(home_team, 'home'), (away_team, 'away')]:
                recent_games = Game.query.filter(
                    ((Game.home_team == team) | (Game.away_team == team)),
                    Game.date < game_date,
                    Game.game_status == 'completed'
                ).order_by(Game.date.desc()).limit(10).all()

                total_runs = 0
                high_scoring_games = 0  # 得分超過6分的比賽

                for game in recent_games:
                    home_score = game.home_score or 0
                    away_score = game.away_score or 0

                    if game.home_team == team:
                        runs = home_score
                    else:
                        runs = away_score

                    total_runs += runs
                    if runs > 6:
                        high_scoring_games += 1

                games_count = len(recent_games)
                if games_count > 0:
                    features[f'{prefix}_offensive_power'] = total_runs / games_count
                    features[f'{prefix}_explosive_rate'] = high_scoring_games / games_count
                else:
                    features[f'{prefix}_offensive_power'] = 4.5
                    features[f'{prefix}_explosive_rate'] = 0.2

        except Exception as e:
            self.logger.warning(f"打擊分析失敗: {e}")

        return features

    def _predict_game_scenario(self, features: Dict) -> Dict:
        """預測比賽類型 - 投手戰 vs 打擊戰"""
        scenario_features = {}

        try:
            # 投手戰指標
            home_era = features.get('home_pitcher_era', 4.5)
            away_era = features.get('away_pitcher_era', 4.5)
            home_stability = features.get('home_pitcher_stability', 0.3)
            away_stability = features.get('away_pitcher_stability', 0.3)

            # 打擊戰指標
            home_offense = features.get('home_offensive_power', 4.5)
            away_offense = features.get('away_offensive_power', 4.5)
            home_explosive = features.get('home_explosive_rate', 0.2)
            away_explosive = features.get('away_explosive_rate', 0.2)

            # 投手戰概率 (兩隊投手都穩定且ERA低)
            pitcher_duel_prob = 0.0
            if home_era < 3.5 and away_era < 3.5 and home_stability > 0.6 and away_stability > 0.6:
                pitcher_duel_prob = 0.8
            elif home_era < 4.0 and away_era < 4.0 and (home_stability > 0.5 or away_stability > 0.5):
                pitcher_duel_prob = 0.6
            elif home_era < 4.5 and away_era < 4.5:
                pitcher_duel_prob = 0.4
            else:
                pitcher_duel_prob = 0.2

            # 打擊戰概率 (兩隊打擊都強勢)
            slugfest_prob = 0.0
            if home_offense > 6.0 and away_offense > 6.0 and home_explosive > 0.4 and away_explosive > 0.4:
                slugfest_prob = 0.8
            elif home_offense > 5.5 and away_offense > 5.5 and (home_explosive > 0.3 or away_explosive > 0.3):
                slugfest_prob = 0.6
            elif home_offense > 5.0 and away_offense > 5.0:
                slugfest_prob = 0.4
            else:
                slugfest_prob = 0.2

            # 預測總分範圍
            expected_total = (home_offense + away_offense + home_era + away_era) / 2

            # 比賽類型判斷
            if pitcher_duel_prob > 0.6:
                game_type = 'pitcher_duel'
                expected_total = min(expected_total, 7.5)  # 投手戰通常低分
            elif slugfest_prob > 0.6:
                game_type = 'slugfest'
                expected_total = max(expected_total, 10.5)  # 打擊戰通常高分
            else:
                game_type = 'balanced'

            scenario_features.update({
                'pitcher_duel_probability': pitcher_duel_prob,
                'slugfest_probability': slugfest_prob,
                'expected_total_score': expected_total,
                'game_type': game_type,
                'is_pitcher_duel': 1 if game_type == 'pitcher_duel' else 0,
                'is_slugfest': 1 if game_type == 'slugfest' else 0,
                'is_balanced': 1 if game_type == 'balanced' else 0
            })

        except Exception as e:
            self.logger.warning(f"比賽類型預測失敗: {e}")
            scenario_features = {
                'pitcher_duel_probability': 0.3,
                'slugfest_probability': 0.3,
                'expected_total_score': 9.0,
                'game_type': 'balanced',
                'is_pitcher_duel': 0,
                'is_slugfest': 0,
                'is_balanced': 1
            }

        return scenario_features

    def _get_safe_default_features(self) -> Dict:
        """安全的默認特徵"""
        return {
            'home_win_pct': 0.5,
            'away_win_pct': 0.5,
            'home_avg_runs': 4.5,
            'away_avg_runs': 4.5,
            'home_avg_allowed': 4.5,
            'away_avg_allowed': 4.5,
            'home_run_diff': 0.0,
            'away_run_diff': 0.0,
            'home_pitcher_era': 4.5,
            'away_pitcher_era': 4.5,
            'home_pitcher_stability': 0.3,
            'away_pitcher_stability': 0.3,
            'home_offensive_power': 4.5,
            'away_offensive_power': 4.5,
            'home_explosive_rate': 0.2,
            'away_explosive_rate': 0.2,
            'pitcher_duel_probability': 0.3,
            'slugfest_probability': 0.3,
            'expected_total_score': 9.0,
            'game_type': 'balanced',
            'is_pitcher_duel': 0,
            'is_slugfest': 0,
            'is_balanced': 1
        }