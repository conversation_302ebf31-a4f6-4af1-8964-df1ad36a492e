def _get_matchup_analysis_features(self, matchup_analysis: Dict) -> Dict:
        """從智能對戰分析報告中提取特徵"""
        features = {}

        # 投手強度分數
        features['home_pitcher_strength_score'] = matchup_analysis['pitcher_scores']['home'].get('score', 50.0)
        features['away_pitcher_strength_score'] = matchup_analysis['pitcher_scores']['away'].get('score', 50.0)
        features['pitcher_strength_diff'] = features['home_pitcher_strength_score'] - features['away_pitcher_strength_score']

        # 比賽類型 (One-Hot Encoding)
        matchup_type = matchup_analysis.get('matchup_type', 'EVEN_MATCH')
        features['matchup_type_PITCHER_DUEL'] = 1 if matchup_type == 'PITCHER_DUEL' else 0
        features['matchup_type_SLUGFEST'] = 1 if matchup_type == 'SLUGFEST' else 0
        features['matchup_type_MISMATCH'] = 1 if matchup_type == 'MISMATCH' else 0

        # 預期總分環境 (One-Hot Encoding)
        run_environment = matchup_analysis.get('run_environment', 'MEDIUM')
        features['run_env_LOW'] = 1 if run_environment == 'LOW' else 0
        features['run_env_HIGH'] = 1 if run_environment == 'HIGH' else 0

        return features