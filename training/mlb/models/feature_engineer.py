

"""
統一且增強的特徵工程器 - MLB 預測系統 v2.0
"""

import logging
import numpy as np
import pandas as pd
from datetime import date, timedelta
from typing import Dict, List, Optional
from sqlalchemy import text
from .database import db, Game, Team, TeamStats, BoxScore, Player, PlayerStats, GameDetail

logger = logging.getLogger(__name__)

class FeatureEngineer:
    """統一特徵工程器 (Core_v1)"""

    def __init__(self):
        self.logger = logger

    def extract_core_features(self, home_team: str, away_team: str, game_date: date, game_id: str, matchup_analysis: Optional[Dict] = None) -> Dict:
        """提取 Core_v1 模型所需的所有特徵"""
        try:
            features = {}

            # 1. 基礎比賽特徵 (日期等)
            features.update(self._get_basic_game_features(game_date))

            # 2. 球隊賽季統計
            features.update(self._get_team_season_features(home_team, away_team, game_date))

            # 3. 近期表現 (基於最近比賽)
            features.update(self._get_recent_performance_features(home_team, away_team, game_date))

            # 4. 歷史對戰 (H2H)
            features.update(self._get_head_to_head_features(home_team, away_team, game_date))

            # 5. 投手質量 (先發 + 牛棚)
            features.update(self._get_pitcher_quality_features(home_team, away_team, game_id))

            # 6. 進階攻防評級
            features.update(self._get_advanced_statistical_features(home_team, away_team))

            # 7. 智能對戰分析特徵
            if matchup_analysis:
                features.update(self._get_matchup_analysis_features(matchup_analysis))

            # 8. 最終差異特徵
            features.update(self._calculate_differential_features(features))

            return features

        except Exception as e:
            self.logger.error(f"Core_v1 特徵提取失敗 {away_team} @ {home_team}: {e}")
            return {}

    # --- Private Feature Extraction Methods ---

    def _get_basic_game_features(self, game_date: date) -> Dict:
        return {
            'is_weekend': 1 if game_date.weekday() >= 5 else 0,
            'month': game_date.month,
            'day_of_week': game_date.weekday(),
        }

    def _get_team_season_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        home_stats = self._get_team_stats(home_team, game_date.year)
        away_stats = self._get_team_stats(away_team, game_date.year)
        features = {}
        for team_stats, prefix in [(home_stats, 'home'), (away_stats, 'away')]:
            for key, value in team_stats.items():
                features[f'{prefix}_{key}'] = value
        return features

    def _get_team_stats(self, team_code: str, season: int) -> Dict:
        team = Team.query.filter_by(team_code=team_code).first()
        if not team: return {}
        stats = TeamStats.query.filter_by(team_id=team.team_id, season=season).first()
        if not stats: return {}
        return {
            'win_pct': stats.win_percentage, 'runs_scored_avg': stats.runs_scored,
            'runs_allowed_avg': stats.runs_allowed, 'batting_avg': stats.batting_avg,
            'ops': stats.ops, 'era': stats.era, 'whip': stats.whip,
        }

    def _get_recent_performance_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        features = {}
        for team, prefix in [(home_team, 'home'), (away_team, 'away')]:
            recent_games = self._get_recent_games(team, game_date, 10)
            if not recent_games: continue
            wins = sum(1 for g in recent_games if self._is_team_win(g, team))
            features[f'{prefix}_recent_win_pct'] = wins / len(recent_games)
        return features

    def _get_recent_games(self, team: str, game_date: date, limit: int) -> List:
        return Game.query.filter(
            (Game.home_team == team) | (Game.away_team == team),
            Game.date < game_date, Game.game_status == 'completed'
        ).order_by(Game.date.desc()).limit(limit).all()

    def _is_team_win(self, game, team: str) -> bool:
        home_score = game.home_score or 0
        away_score = game.away_score or 0
        return (game.home_team == team and home_score > away_score) or \
               (game.away_team == team and away_score > home_score)

    def _get_head_to_head_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        h2h_games = Game.query.filter(
            ((Game.home_team == home_team) & (Game.away_team == away_team)) |
            ((Game.home_team == away_team) & (Game.away_team == home_team)),
            Game.date < game_date, Game.game_status == 'completed'
        ).order_by(Game.date.desc()).limit(10).all()
        if not h2h_games: return {'h2h_home_win_pct': 0.5}
        home_wins = sum(1 for g in h2h_games if self._is_team_win(g, home_team))
        return {'h2h_home_win_pct': home_wins / len(h2h_games)}

    def _get_pitcher_quality_features(self, home_team: str, away_team: str, game_id: str) -> Dict:
        features = {}
        game_detail = GameDetail.query.filter_by(game_id=game_id).first()
        if not game_detail: return {}
        
        home_starter_stats = self._find_pitcher_stats(game_detail.home_starting_pitcher, home_team)
        if home_starter_stats: features['home_starter_quality'] = self._calculate_pitcher_quality_score(home_starter_stats)

        away_starter_stats = self._find_pitcher_stats(game_detail.away_starting_pitcher, away_team)
        if away_starter_stats: features['away_starter_quality'] = self._calculate_pitcher_quality_score(away_starter_stats)

        features['home_bullpen_quality'] = self._calculate_bullpen_quality(home_team)
        features['away_bullpen_quality'] = self._calculate_bullpen_quality(away_team)
        return features

    def _find_pitcher_stats(self, name: str, code: str) -> Optional[PlayerStats]:
        if not name or not code: return None
        team = Team.query.filter_by(team_code=code).first()
        if not team: return None
        return PlayerStats.query.filter(PlayerStats.team_id == team.team_id, PlayerStats.player_name.like(f'%{name}%')).order_by(PlayerStats.season.desc()).first()

    def _calculate_pitcher_quality_score(self, stats: PlayerStats) -> float:
        era = stats.era or 4.5; whip = stats.whip or 1.3
        k_per_9 = (stats.strikeouts_pitching / stats.innings_pitched) * 9 if stats.innings_pitched and stats.innings_pitched > 0 else 7.0
        era_score = max(0, 100 - (era - 2.0) * 15)
        whip_score = max(0, 100 - (whip - 1.0) * 40)
        k9_score = min(100, max(0, (k_per_9 - 5.0) * 10))
        return (era_score * 0.5 + whip_score * 0.3 + k9_score * 0.2)

    def _calculate_bullpen_quality(self, team_code: str) -> float:
        team = Team.query.filter_by(team_code=team_code).first()
        if not team: return 50.0
        relief_pitchers = PlayerStats.query.filter(PlayerStats.team_id == team.team_id, PlayerStats.era.isnot(None), (PlayerStats.games_started < PlayerStats.games_played * 0.3)).all()
        if not relief_pitchers: return 50.0
        total_ip = sum(p.innings_pitched or 0 for p in relief_pitchers)
        if total_ip == 0: return 50.0
        weighted_era = sum((p.era or 4.5) * (p.innings_pitched or 0) for p in relief_pitchers) / total_ip
        return max(0, 100 - (weighted_era - 3.5) * 20)

    def _get_advanced_statistical_features(self, home_team: str, away_team: str) -> Dict:
        home_stats = self._get_team_stats(home_team, date.today().year)
        away_stats = self._get_team_stats(away_team, date.today().year)
        features = {}
        if home_stats and away_stats:
            features['home_offense_rating'] = (home_stats.get('runs_scored_avg', 4.5) - 4.5) * 10 + (home_stats.get('batting_avg', 0.25) - 0.25) * 100
            features['away_offense_rating'] = (away_stats.get('runs_scored_avg', 4.5) - 4.5) * 10 + (away_stats.get('batting_avg', 0.25) - 0.25) * 100
        return features

    def _get_matchup_analysis_features(self, analysis: Dict) -> Dict:
        features = {}
        if 'pitcher_scores' in analysis:
            features['home_pitcher_strength'] = analysis['pitcher_scores']['home'].get('score', 50)
            features['away_pitcher_strength'] = analysis['pitcher_scores']['away'].get('score', 50)
        matchup_type = analysis.get('matchup_type', 'EVEN_MATCH')
        features['is_pitcher_duel'] = 1 if matchup_type == 'PITCHER_DUEL' else 0
        features['is_slugfest'] = 1 if matchup_type == 'SLUGFEST' else 0
        return features

    def _calculate_differential_features(self, features: Dict) -> Dict:
        diffs = {}
        for key in ['win_pct', 'runs_scored_avg', 'runs_allowed_avg', 'recent_win_pct', 'starter_quality', 'bullpen_quality', 'offense_rating']:
            if f'home_{key}' in features and f'away_{key}' in features:
                diffs[f'{key}_diff'] = features[f'home_{key}'] - features[f'away_{key}']
        return diffs
