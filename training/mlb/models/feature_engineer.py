import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional
import logging

from .database import db, Game, Team, TeamStats, PlayerStats, PlayerGameStats, Player

logger = logging.getLogger(__name__)

class FeatureEngineer:
    """MLB特徵工程器 - 專門處理特徵提取和工程"""
    
    def __init__(self):
        self.feature_cache = {}
        
    def extract_comprehensive_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """提取全面的比賽特徵"""
        try:
            features = {}
            
            # 基本特徵
            features.update(self._get_basic_features(game_date))
            
            # 球隊統計特徵
            features.update(self._get_team_features(home_team, away_team, game_date))
            
            # 對戰歷史特徵
            features.update(self._get_head_to_head_features(home_team, away_team, game_date))
            
            # 近期表現特徵
            features.update(self._get_recent_performance_features(home_team, away_team, game_date))
            
            # 主場優勢特徵
            features.update(self._get_home_field_features(home_team, game_date))
            
            # 趨勢特徵
            features.update(self._get_trend_features(home_team, away_team, game_date))
            
            # 實力對比特徵
            features.update(self._get_strength_comparison_features(home_team, away_team, game_date))

            # 球員對戰特徵
            features.update(self._get_player_matchup_features(home_team, away_team, game_date))

            # 投打對戰特徵
            features.update(self._get_pitcher_batter_features(home_team, away_team, game_date))

            return features
            
        except Exception as e:
            logger.error(f"提取特徵失敗 {away_team} @ {home_team}: {e}")
            return {}
    
    def _get_basic_features(self, game_date: date) -> Dict:
        """獲取基本特徵"""
        return {
            'is_weekend': 1 if game_date.weekday() >= 5 else 0,
            'month': game_date.month,
            'day_of_week': game_date.weekday(),
            'is_april': 1 if game_date.month == 4 else 0,
            'is_summer': 1 if game_date.month in [6, 7, 8] else 0,
            'is_september': 1 if game_date.month == 9 else 0,
            'days_from_season_start': (game_date - date(game_date.year, 4, 1)).days
        }
    
    def _get_team_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取球隊統計特徵"""
        features = {}
        
        # 獲取主隊統計
        home_stats = self._get_team_season_stats(home_team, game_date.year)
        for key, value in home_stats.items():
            features[f'home_{key}'] = value
        
        # 獲取客隊統計
        away_stats = self._get_team_season_stats(away_team, game_date.year)
        for key, value in away_stats.items():
            features[f'away_{key}'] = value
        
        return features
    
    def _get_team_season_stats(self, team_code: str, season: int) -> Dict:
        """獲取球隊賽季統計"""
        try:
            team = Team.query.filter_by(team_code=team_code).first()
            if not team:
                return self._get_default_team_stats()
            
            stats = TeamStats.query.filter_by(
                team_id=team.team_id,
                season=season
            ).first()
            
            if not stats:
                return self._get_default_team_stats()
            
            return {
                'win_pct': stats.win_percentage,
                'runs_scored_avg': stats.runs_scored,
                'runs_allowed_avg': stats.runs_allowed,
                'batting_avg': stats.batting_avg,
                'on_base_pct': stats.on_base_percentage,
                'slugging_pct': stats.slugging_percentage,
                'ops': stats.ops,
                'era': stats.era,
                'whip': stats.whip,
                'home_runs': stats.home_runs,
                'stolen_bases': stats.stolen_bases,
                'strikeouts': stats.strikeouts,
                'walks': stats.walks,
                'saves': stats.saves,
                'home_win_pct': stats.home_wins / max(stats.home_wins + stats.home_losses, 1),
                'away_win_pct': stats.away_wins / max(stats.away_wins + stats.away_losses, 1)
            }
            
        except Exception as e:
            logger.warning(f"獲取球隊統計失敗 {team_code}: {e}")
            return self._get_default_team_stats()
    
    def _get_default_team_stats(self) -> Dict:
        """獲取默認球隊統計"""
        return {
            'win_pct': 0.500,
            'runs_scored_avg': 4.5,
            'runs_allowed_avg': 4.5,
            'batting_avg': 0.250,
            'on_base_pct': 0.320,
            'slugging_pct': 0.420,
            'ops': 0.740,
            'era': 4.50,
            'whip': 1.30,
            'home_runs': 150,
            'stolen_bases': 80,
            'strikeouts': 1400,
            'walks': 500,
            'saves': 40,
            'home_win_pct': 0.540,
            'away_win_pct': 0.460
        }
    
    def _get_head_to_head_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取對戰歷史特徵"""
        try:
            # 查詢過去3年的對戰記錄
            cutoff_date = game_date - timedelta(days=1095)
            
            h2h_games = Game.query.filter(
                Game.date >= cutoff_date,
                Game.date < game_date,
                Game.home_team == home_team,
                Game.away_team == away_team,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            if not h2h_games:
                return {
                    'h2h_games_count': 0,
                    'h2h_home_wins': 0,
                    'h2h_away_wins': 0,
                    'h2h_home_win_pct': 0.5,
                    'h2h_avg_total_runs': 9.0,
                    'h2h_avg_home_score': 4.5,
                    'h2h_avg_away_score': 4.5,
                    'h2h_high_scoring_games': 0,
                    'h2h_blowout_games': 0
                }
            
            home_wins = sum(1 for g in h2h_games if g.home_score > g.away_score)
            away_wins = len(h2h_games) - home_wins
            total_runs = [g.home_score + g.away_score for g in h2h_games]
            home_scores = [g.home_score for g in h2h_games]
            away_scores = [g.away_score for g in h2h_games]
            
            # 高分比賽（總分>10）
            high_scoring = sum(1 for runs in total_runs if runs > 10)
            
            # 大比分差距比賽（分差>5）
            blowouts = sum(1 for g in h2h_games if abs(g.home_score - g.away_score) > 5)
            
            return {
                'h2h_games_count': len(h2h_games),
                'h2h_home_wins': home_wins,
                'h2h_away_wins': away_wins,
                'h2h_home_win_pct': home_wins / len(h2h_games),
                'h2h_avg_total_runs': np.mean(total_runs),
                'h2h_avg_home_score': np.mean(home_scores),
                'h2h_avg_away_score': np.mean(away_scores),
                'h2h_high_scoring_games': high_scoring / len(h2h_games),
                'h2h_blowout_games': blowouts / len(h2h_games)
            }
            
        except Exception as e:
            logger.warning(f"獲取對戰歷史失敗 {home_team} vs {away_team}: {e}")
            return {
                'h2h_games_count': 0,
                'h2h_home_wins': 0,
                'h2h_away_wins': 0,
                'h2h_home_win_pct': 0.5,
                'h2h_avg_total_runs': 9.0,
                'h2h_avg_home_score': 4.5,
                'h2h_avg_away_score': 4.5,
                'h2h_high_scoring_games': 0,
                'h2h_blowout_games': 0
            }
    
    def _get_recent_performance_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取近期表現特徵"""
        features = {}
        
        # 獲取不同時間窗口的表現
        for days, suffix in [(7, '_7d'), (14, '_14d'), (30, '_30d')]:
            home_perf = self._get_team_recent_performance(home_team, game_date, days)
            away_perf = self._get_team_recent_performance(away_team, game_date, days)
            
            features.update({
                f'home_win_pct{suffix}': home_perf['win_pct'],
                f'home_runs_avg{suffix}': home_perf['runs_avg'],
                f'home_runs_allowed_avg{suffix}': home_perf['runs_allowed_avg'],
                f'away_win_pct{suffix}': away_perf['win_pct'],
                f'away_runs_avg{suffix}': away_perf['runs_avg'],
                f'away_runs_allowed_avg{suffix}': away_perf['runs_allowed_avg']
            })
        
        return features
    
    def _get_team_recent_performance(self, team_code: str, game_date: date, days: int) -> Dict:
        """獲取球隊近期表現"""
        try:
            cutoff_date = game_date - timedelta(days=days)
            
            recent_games = Game.query.filter(
                Game.date >= cutoff_date,
                Game.date < game_date,
                db.or_(Game.home_team == team_code, Game.away_team == team_code),
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            if not recent_games:
                return {'win_pct': 0.5, 'runs_avg': 4.5, 'runs_allowed_avg': 4.5}
            
            wins = 0
            total_runs_scored = 0
            total_runs_allowed = 0
            
            for game in recent_games:
                if game.home_team == team_code:
                    if game.home_score > game.away_score:
                        wins += 1
                    total_runs_scored += game.home_score
                    total_runs_allowed += game.away_score
                else:
                    if game.away_score > game.home_score:
                        wins += 1
                    total_runs_scored += game.away_score
                    total_runs_allowed += game.home_score
            
            return {
                'win_pct': wins / len(recent_games),
                'runs_avg': total_runs_scored / len(recent_games),
                'runs_allowed_avg': total_runs_allowed / len(recent_games)
            }
            
        except Exception as e:
            logger.warning(f"獲取近期表現失敗 {team_code}: {e}")
            return {'win_pct': 0.5, 'runs_avg': 4.5, 'runs_allowed_avg': 4.5}
    
    def _get_home_field_features(self, home_team: str, game_date: date) -> Dict:
        """獲取主場優勢特徵"""
        try:
            # 獲取主隊最近一年的主場戰績
            cutoff_date = game_date - timedelta(days=365)
            
            home_games = Game.query.filter(
                Game.date >= cutoff_date,
                Game.date < game_date,
                Game.home_team == home_team,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            if not home_games:
                return {
                    'home_field_advantage': 0.54,
                    'home_avg_attendance': 30000,
                    'home_runs_scored_at_home': 4.6,
                    'home_runs_allowed_at_home': 4.4
                }
            
            home_wins = sum(1 for g in home_games if g.home_score > g.away_score)
            home_advantage = home_wins / len(home_games)
            
            avg_runs_scored = np.mean([g.home_score for g in home_games])
            avg_runs_allowed = np.mean([g.away_score for g in home_games])
            
            return {
                'home_field_advantage': home_advantage,
                'home_avg_attendance': 30000,  # 可以從GameDetail獲取實際數據
                'home_runs_scored_at_home': avg_runs_scored,
                'home_runs_allowed_at_home': avg_runs_allowed
            }
            
        except Exception as e:
            logger.warning(f"獲取主場優勢失敗 {home_team}: {e}")
            return {
                'home_field_advantage': 0.54,
                'home_avg_attendance': 30000,
                'home_runs_scored_at_home': 4.6,
                'home_runs_allowed_at_home': 4.4
            }

    def _get_trend_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取趨勢特徵"""
        features = {}

        # 獲取最近的連勝/連敗情況
        home_streak = self._get_win_streak(home_team, game_date)
        away_streak = self._get_win_streak(away_team, game_date)

        features.update({
            'home_win_streak': max(0, home_streak),
            'home_loss_streak': max(0, -home_streak),
            'away_win_streak': max(0, away_streak),
            'away_loss_streak': max(0, -away_streak)
        })

        # 獲取得分趨勢
        home_scoring_trend = self._get_scoring_trend(home_team, game_date)
        away_scoring_trend = self._get_scoring_trend(away_team, game_date)

        features.update({
            'home_scoring_trend': home_scoring_trend,
            'away_scoring_trend': away_scoring_trend
        })

        return features

    def _get_win_streak(self, team_code: str, game_date: date, max_games: int = 10) -> int:
        """獲取連勝/連敗數（正數為連勝，負數為連敗）"""
        try:
            recent_games = Game.query.filter(
                Game.date < game_date,
                db.or_(Game.home_team == team_code, Game.away_team == team_code),
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).order_by(Game.date.desc()).limit(max_games).all()

            if not recent_games:
                return 0

            streak = 0
            last_result = None

            for game in recent_games:
                if game.home_team == team_code:
                    won = game.home_score > game.away_score
                else:
                    won = game.away_score > game.home_score

                if last_result is None:
                    last_result = won
                    streak = 1 if won else -1
                elif last_result == won:
                    if won:
                        streak += 1
                    else:
                        streak -= 1
                else:
                    break

            return streak

        except Exception as e:
            logger.warning(f"獲取連勝連敗失敗 {team_code}: {e}")
            return 0

    def _get_scoring_trend(self, team_code: str, game_date: date, days: int = 14) -> float:
        """獲取得分趨勢（正數表示上升趨勢）"""
        try:
            cutoff_date = game_date - timedelta(days=days)

            recent_games = Game.query.filter(
                Game.date >= cutoff_date,
                Game.date < game_date,
                db.or_(Game.home_team == team_code, Game.away_team == team_code),
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).order_by(Game.date.asc()).all()

            if len(recent_games) < 3:
                return 0.0

            scores = []
            for game in recent_games:
                if game.home_team == team_code:
                    scores.append(game.home_score)
                else:
                    scores.append(game.away_score)

            # 計算線性趨勢
            x = np.arange(len(scores))
            trend = np.polyfit(x, scores, 1)[0]  # 斜率

            return trend

        except Exception as e:
            logger.warning(f"獲取得分趨勢失敗 {team_code}: {e}")
            return 0.0

    def _get_strength_comparison_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取實力對比特徵"""
        try:
            home_stats = self._get_team_season_stats(home_team, game_date.year)
            away_stats = self._get_team_season_stats(away_team, game_date.year)

            return {
                # 勝率差距
                'win_pct_diff': home_stats['win_pct'] - away_stats['win_pct'],

                # 攻擊力對比
                'runs_scored_diff': home_stats['runs_scored_avg'] - away_stats['runs_scored_avg'],
                'batting_avg_diff': home_stats['batting_avg'] - away_stats['batting_avg'],
                'ops_diff': home_stats['ops'] - away_stats['ops'],
                'home_runs_diff': home_stats['home_runs'] - away_stats['home_runs'],

                # 防守力對比
                'runs_allowed_diff': away_stats['runs_allowed_avg'] - home_stats['runs_allowed_avg'],
                'era_diff': away_stats['era'] - home_stats['era'],
                'whip_diff': away_stats['whip'] - home_stats['whip'],

                # 綜合實力指標
                'offensive_rating_diff': (home_stats['ops'] * home_stats['runs_scored_avg']) -
                                        (away_stats['ops'] * away_stats['runs_scored_avg']),
                'defensive_rating_diff': (away_stats['era'] * away_stats['runs_allowed_avg']) -
                                       (home_stats['era'] * home_stats['runs_allowed_avg']),

                # 主客場優勢對比
                'home_advantage_diff': home_stats['home_win_pct'] - away_stats['away_win_pct']
            }

        except Exception as e:
            logger.warning(f"獲取實力對比失敗 {home_team} vs {away_team}: {e}")
            return {
                'win_pct_diff': 0.0,
                'runs_scored_diff': 0.0,
                'batting_avg_diff': 0.0,
                'ops_diff': 0.0,
                'home_runs_diff': 0,
                'runs_allowed_diff': 0.0,
                'era_diff': 0.0,
                'whip_diff': 0.0,
                'offensive_rating_diff': 0.0,
                'defensive_rating_diff': 0.0,
                'home_advantage_diff': 0.0
            }

    def get_feature_importance_analysis(self, features: Dict) -> Dict:
        """分析特徵重要性"""
        try:
            analysis = {
                'key_factors': [],
                'risk_factors': [],
                'confidence_indicators': []
            }

            # 分析關鍵因素
            if abs(features.get('win_pct_diff', 0)) > 0.1:
                team = "主隊" if features['win_pct_diff'] > 0 else "客隊"
                analysis['key_factors'].append(f"{team}勝率優勢明顯 ({features['win_pct_diff']:.3f})")

            if abs(features.get('era_diff', 0)) > 0.5:
                team = "主隊" if features['era_diff'] < 0 else "客隊"
                analysis['key_factors'].append(f"{team}投手實力較強")

            if abs(features.get('ops_diff', 0)) > 0.05:
                team = "主隊" if features['ops_diff'] > 0 else "客隊"
                analysis['key_factors'].append(f"{team}打擊火力較強")

            # 分析風險因素
            if features.get('h2h_games_count', 0) < 3:
                analysis['risk_factors'].append("對戰歷史數據不足")

            if abs(features.get('home_win_streak', 0)) > 5 or abs(features.get('away_win_streak', 0)) > 5:
                analysis['risk_factors'].append("存在極端連勝/連敗情況")

            # 信心度指標
            if features.get('home_field_advantage', 0.5) > 0.6:
                analysis['confidence_indicators'].append("主場優勢明顯")

            if features.get('h2h_games_count', 0) >= 5:
                analysis['confidence_indicators'].append("對戰歷史數據充足")

            return analysis

        except Exception as e:
            logger.warning(f"特徵重要性分析失敗: {e}")
            return {'key_factors': [], 'risk_factors': [], 'confidence_indicators': []}

    def _get_player_matchup_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取球員對戰特徵（簡化版本）"""
        try:
            # 暫時返回默認值，避免PlayerGameStats查詢問題
            return {
                'home_core_batters_avg': 0.250,
                'home_core_batters_ops': 0.750,
                'home_core_batters_hr_rate': 0.05,
                'away_core_batters_avg': 0.250,
                'away_core_batters_ops': 0.750,
                'away_core_batters_hr_rate': 0.05,
                'home_pitchers_era': 4.50,
                'home_pitchers_whip': 1.30,
                'home_pitchers_k_rate': 0.20,
                'away_pitchers_era': 4.50,
                'away_pitchers_whip': 1.30,
                'away_pitchers_k_rate': 0.20
            }

        except Exception as e:
            logger.warning(f"獲取球員對戰特徵失敗: {e}")
            return {}

    def _get_core_batters_performance(self, team_code: str, game_date: date, days: int = 30) -> Dict:
        """獲取核心打者近期表現"""
        try:
            cutoff_date = game_date - timedelta(days=days)

            # 獲取球隊ID
            team = Team.query.filter_by(team_code=team_code).first()
            if not team:
                return {}

            # 獲取近期比賽中的打者統計
            recent_stats = db.session.query(PlayerGameStats, Game).join(
                Game, PlayerGameStats.game_id == Game.game_id
            ).filter(
                Game.date >= cutoff_date,
                Game.date < game_date,
                PlayerGameStats.team_id == team.team_id,
                PlayerGameStats.at_bats > 0
            ).all()

            if not recent_stats:
                return {}

            # 計算核心打者統計
            total_at_bats = sum(stat.at_bats for stat, game in recent_stats)
            total_hits = sum(stat.hits for stat, game in recent_stats)
            total_walks = sum(stat.walks for stat, game in recent_stats)
            total_home_runs = sum(stat.home_runs for stat, game in recent_stats)

            if total_at_bats == 0:
                return {}

            avg_batting_avg = total_hits / total_at_bats
            avg_ops = (total_hits + total_walks) / total_at_bats + (total_hits + total_home_runs) / total_at_bats
            hr_rate = total_home_runs / total_at_bats

            return {
                'avg_batting_avg': avg_batting_avg,
                'avg_ops': avg_ops,
                'hr_rate': hr_rate,
                'games_count': len(set(stat.game_id for stat, game in recent_stats))
            }

        except Exception as e:
            logger.warning(f"獲取核心打者表現失敗 {team_code}: {e}")
            return {}

    def _get_pitchers_performance(self, team_code: str, game_date: date, days: int = 30) -> Dict:
        """獲取投手近期表現"""
        try:
            cutoff_date = game_date - timedelta(days=days)

            # 獲取球隊ID
            team = Team.query.filter_by(team_code=team_code).first()
            if not team:
                return {}

            # 獲取近期比賽中的投手統計
            recent_stats = db.session.query(PlayerGameStats, Game).join(
                Game, PlayerGameStats.game_id == Game.game_id
            ).filter(
                Game.date >= cutoff_date,
                Game.date < game_date,
                PlayerGameStats.team_id == team.team_id,
                PlayerGameStats.innings_pitched > 0
            ).all()

            if not recent_stats:
                return {}

            # 計算投手統計
            total_innings = sum(stat.innings_pitched for stat, game in recent_stats)
            total_earned_runs = sum(stat.earned_runs for stat, game in recent_stats)
            total_hits_allowed = sum(stat.hits_allowed for stat, game in recent_stats)
            total_walks_allowed = sum(stat.walks_allowed for stat, game in recent_stats)
            total_strikeouts = sum(stat.strikeouts_pitched for stat, game in recent_stats)
            total_batters_faced = sum(stat.at_bats for stat, game in recent_stats)  # 近似值

            if total_innings == 0:
                return {}

            avg_era = (total_earned_runs * 9) / total_innings
            avg_whip = (total_hits_allowed + total_walks_allowed) / total_innings
            k_rate = total_strikeouts / max(total_batters_faced, 1)

            return {
                'avg_era': avg_era,
                'avg_whip': avg_whip,
                'k_rate': k_rate,
                'games_count': len(set(stat.game_id for stat, game in recent_stats))
            }

        except Exception as e:
            logger.warning(f"獲取投手表現失敗 {team_code}: {e}")
            return {}

    def _get_pitcher_batter_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取投打對戰特徵（簡化版本）"""
        try:
            # 暫時返回默認值，避免PlayerGameStats查詢問題
            return {
                'home_pitcher_vs_away_era': 4.50,
                'home_pitcher_vs_away_whip': 1.30,
                'away_pitcher_vs_home_era': 4.50,
                'away_pitcher_vs_home_whip': 1.30
            }

        except Exception as e:
            logger.warning(f"獲取投打對戰特徵失敗: {e}")
            return {}

    def _get_pitcher_vs_team_stats(self, pitcher_team: str, opponent_team: str, game_date: date) -> Dict:
        """獲取投手對特定球隊的統計"""
        try:
            cutoff_date = game_date - timedelta(days=365)  # 過去一年

            # 獲取球隊ID
            pitcher_team_obj = Team.query.filter_by(team_code=pitcher_team).first()
            if not pitcher_team_obj:
                return {}

            # 獲取投手對該球隊的歷史表現
            pitcher_stats = db.session.query(PlayerGameStats, Game).join(
                Game, PlayerGameStats.game_id == Game.game_id
            ).filter(
                Game.date >= cutoff_date,
                Game.date < game_date,
                PlayerGameStats.team_id == pitcher_team_obj.team_id,
                PlayerGameStats.innings_pitched > 0,
                db.or_(
                    db.and_(Game.home_team == pitcher_team, Game.away_team == opponent_team),
                    db.and_(Game.away_team == pitcher_team, Game.home_team == opponent_team)
                )
            ).all()

            if not pitcher_stats:
                return {}

            # 計算對戰統計
            total_innings = sum(stat.innings_pitched for stat, game in pitcher_stats)
            total_earned_runs = sum(stat.earned_runs for stat, game in pitcher_stats)
            total_hits_allowed = sum(stat.hits_allowed for stat, game in pitcher_stats)
            total_walks_allowed = sum(stat.walks_allowed for stat, game in pitcher_stats)

            if total_innings == 0:
                return {}

            era = (total_earned_runs * 9) / total_innings
            whip = (total_hits_allowed + total_walks_allowed) / total_innings

            return {
                'era': era,
                'whip': whip,
                'games_count': len(pitcher_stats)
            }

        except Exception as e:
            logger.warning(f"獲取投手對戰統計失敗: {e}")
            return {}
