"""
基礎特徵工程器 - MLB 預測系統
提取比賽相關的基礎特徵
"""

import logging
import numpy as np
import pandas as pd
from datetime import date, timedelta
from typing import Dict, List, Optional
from sqlalchemy import text
from .database import db, Game, Team, TeamStats, BoxScore, Player, PlayerStats

logger = logging.getLogger(__name__)

class FeatureEngineer:
    """基礎特徵工程器"""

    def __init__(self):
        self.logger = logger

    def extract_comprehensive_features(self, home_team: str, away_team: str, game_date: date, matchup_analysis: Optional[Dict] = None) -> Dict:
        """提取全面的比賽特徵"""
        try:
            features = {}

            # 基礎球隊特徵
            features.update(self._get_team_basic_features(home_team, away_team, game_date))

            # 簡化的最近表現特徵
            features.update(self._get_simple_recent_features(home_team, away_team, game_date))

            # 如果有智能對戰分析，添加相關特徵
            if matchup_analysis:
                features.update(self._get_matchup_analysis_features(matchup_analysis))

            return features

        except Exception as e:
            self.logger.error(f"特徵提取失敗 {away_team} @ {home_team}: {e}")
            return {}

    def _get_team_basic_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取球隊基礎特徵"""
        features = {}

        try:
            # 獲取球隊統計
            home_stats = self._get_team_stats(home_team, game_date)
            away_stats = self._get_team_stats(away_team, game_date)

            # 基礎統計特徵
            features.update({
                'home_wins': home_stats.get('wins', 0),
                'home_losses': home_stats.get('losses', 0),
                'home_win_pct': home_stats.get('win_percentage', 0.5),
                'away_wins': away_stats.get('wins', 0),
                'away_losses': away_stats.get('losses', 0),
                'away_win_pct': away_stats.get('win_percentage', 0.5),
                'home_runs_scored': home_stats.get('runs_scored', 4.5),
                'home_runs_allowed': home_stats.get('runs_allowed', 4.5),
                'away_runs_scored': away_stats.get('runs_scored', 4.5),
                'away_runs_allowed': away_stats.get('runs_allowed', 4.5),
            })

        except Exception as e:
            self.logger.warning(f"球隊基礎特徵提取失敗: {e}")

        return features

    def _get_team_stats(self, team: str, game_date: date) -> Dict:
        """獲取球隊統計數據"""
        try:
            # 查詢球隊統計
            team_obj = Team.query.filter_by(team_code=team).first()
            if not team_obj:
                return {}

            # 獲取最新的球隊統計
            stats = TeamStats.query.filter_by(team_id=team_obj.team_id).first()
            if not stats:
                return {}

            return {
                'wins': stats.wins or 0,
                'losses': stats.losses or 0,
                'win_percentage': stats.win_percentage or 0.5,
                'runs_scored': stats.runs_scored or 4.5,
                'runs_allowed': stats.runs_allowed or 4.5,
                'batting_avg': stats.batting_avg or 0.250,
                'era': stats.era or 4.50,
            }

        except Exception as e:
            self.logger.warning(f"獲取球隊統計失敗 {team}: {e}")
            return {}

    def _get_head_to_head_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取歷史對戰特徵"""
        features = {}

        try:
            # 查詢歷史對戰記錄
            h2h_games = Game.query.filter(
                ((Game.home_team == home_team) & (Game.away_team == away_team)) |
                ((Game.home_team == away_team) & (Game.away_team == home_team)),
                Game.date < game_date,
                Game.game_status == 'completed'
            ).order_by(Game.date.desc()).limit(10).all()

            if h2h_games:
                home_wins = sum(1 for g in h2h_games if
                               (g.home_team == home_team and g.home_score > g.away_score) or
                               (g.away_team == home_team and g.away_score > g.home_score))

                features.update({
                    'h2h_games_count': len(h2h_games),
                    'h2h_home_wins': home_wins,
                    'h2h_home_win_pct': home_wins / len(h2h_games) if h2h_games else 0.5,
                })
            else:
                features.update({
                    'h2h_games_count': 0,
                    'h2h_home_wins': 0,
                    'h2h_home_win_pct': 0.5,
                })

        except Exception as e:
            self.logger.warning(f"歷史對戰特徵提取失敗: {e}")

        return features

    def _get_recent_performance_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取最近表現特徵"""
        features = {}

        try:
            # 獲取最近10場比賽
            home_recent = self._get_recent_games(home_team, game_date, 10)
            away_recent = self._get_recent_games(away_team, game_date, 10)

            # 計算最近表現
            features.update({
                'home_recent_wins': sum(1 for g in home_recent if self._is_team_win(g, home_team)),
                'home_recent_win_pct': sum(1 for g in home_recent if self._is_team_win(g, home_team)) / len(home_recent) if home_recent else 0.5,
                'away_recent_wins': sum(1 for g in away_recent if self._is_team_win(g, away_team)),
                'away_recent_win_pct': sum(1 for g in away_recent if self._is_team_win(g, away_team)) / len(away_recent) if away_recent else 0.5,
            })

        except Exception as e:
            self.logger.warning(f"最近表現特徵提取失敗: {e}")

        return features

    def _get_recent_games(self, team: str, game_date: date, limit: int = 10) -> List:
        """獲取球隊最近的比賽"""
        try:
            return Game.query.filter(
                ((Game.home_team == team) | (Game.away_team == team)),
                Game.date < game_date,
                Game.game_status == 'completed'
            ).order_by(Game.date.desc()).limit(limit).all()
        except Exception as e:
            self.logger.warning(f"獲取最近比賽失敗 {team}: {e}")
            return []

    def _is_team_win(self, game, team: str) -> bool:
        """判斷球隊是否贏得比賽"""
        # 處理 None 值
        home_score = game.home_score if game.home_score is not None else 0
        away_score = game.away_score if game.away_score is not None else 0

        if game.home_team == team:
            return home_score > away_score
        else:
            return away_score > home_score

    def _get_matchup_analysis_features(self, matchup_analysis: Dict) -> Dict:
        """從智能對戰分析報告中提取特徵"""
        features = {}

        try:
            # 投手強度分數
            if 'pitcher_scores' in matchup_analysis:
                features['home_pitcher_strength_score'] = matchup_analysis['pitcher_scores']['home'].get('score', 50.0)
                features['away_pitcher_strength_score'] = matchup_analysis['pitcher_scores']['away'].get('score', 50.0)
                features['pitcher_strength_diff'] = features['home_pitcher_strength_score'] - features['away_pitcher_strength_score']

            # 比賽類型 (One-Hot Encoding)
            matchup_type = matchup_analysis.get('matchup_type', 'EVEN_MATCH')
            features['matchup_type_PITCHER_DUEL'] = 1 if matchup_type == 'PITCHER_DUEL' else 0
            features['matchup_type_SLUGFEST'] = 1 if matchup_type == 'SLUGFEST' else 0
            features['matchup_type_MISMATCH'] = 1 if matchup_type == 'MISMATCH' else 0

            # 預期總分環境 (One-Hot Encoding)
            run_environment = matchup_analysis.get('run_environment', 'MEDIUM')
            features['run_env_LOW'] = 1 if run_environment == 'LOW' else 0
            features['run_env_HIGH'] = 1 if run_environment == 'HIGH' else 0

        except Exception as e:
            self.logger.warning(f"對戰分析特徵提取失敗: {e}")

        return features