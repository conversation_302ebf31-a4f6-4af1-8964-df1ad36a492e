#!/usr/bin/env python3
"""
2025年MLB預測啟動器
整合投手公告檢查和智能預測時機判斷
"""

import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional
from models.daily_pitcher_announcements import DailyPitcherAnnouncementChecker
from models.improved_predictor import ImprovedMLBPredictor
from models.database import Game, db
import json

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MLBPredictionLauncher2025:
    """2025年MLB預測啟動器"""

    def __init__(self, app=None):
        self.announcement_checker = DailyPitcherAnnouncementChecker()
        self.predictor = None
        self.app = app

        # 如果沒有提供app，嘗試從Flask的current_app獲取
        if self.app is None:
            try:
                from flask import current_app
                self.app = current_app._get_current_object()
            except RuntimeError:
                # 如果不在應用上下文中，app將保持為None
                # 在使用時需要手動設置或在app_context中調用
                pass
        
    def initialize_predictor(self):
        """初始化預測器"""
        try:
            self.predictor = ImprovedMLBPredictor()
            if not self.predictor.is_trained:
                logger.info("載入增強預測模型...")
                self.predictor.load_models()
            logger.info("預測器初始化完成")
            return True
        except Exception as e:
            logger.error(f"預測器初始化失敗: {e}")
            return False
    
    def check_prediction_readiness(self, target_date: date = None) -> Dict:
        """檢查預測準備狀況"""
        if target_date is None:
            target_date = date.today()
        
        logger.info(f"檢查 {target_date} 的預測準備狀況...")
        
        # 1. 檢查投手公告狀況
        announcement_status = self.announcement_checker.check_pitcher_announcements_for_date(target_date)
        
        # 2. 檢查數據庫中的比賽數據
        db_games = []
        try:
            if self.app:
                with self.app.app_context():
                    db_games = Game.query.filter(
                        Game.date == target_date,
                        Game.game_status.in_(['scheduled', 'postponed'])
                    ).all()
            else:
                # 如果沒有app實例，嘗試直接查詢（假設已在app_context中）
                db_games = Game.query.filter(
                    Game.date == target_date,
                    Game.game_status.in_(['scheduled', 'postponed'])
                ).all()
        except Exception as e:
            logger.warning(f"無法查詢數據庫比賽數據: {e}")
            db_games = []
        
        # 3. 分析預測可行性
        readiness_analysis = {
            'date': target_date.strftime('%Y-%m-%d'),
            'announcement_data': announcement_status,
            'database_games': len(db_games),
            'prediction_feasibility': self._analyze_prediction_feasibility(announcement_status, db_games),
            'recommendations': []
        }
        
        return readiness_analysis
    
    def _analyze_prediction_feasibility(self, announcement_status: Dict, db_games: List) -> Dict:
        """分析預測可行性"""
        feasibility = {
            'overall_score': 0,
            'data_quality': 0,      # 數據質量 (0-40分)
            'timing': 0,            # 時機 (0-30分)
            'coverage': 0,          # 覆蓋率 (0-30分)
            'status': 'not_ready',
            'ready_games': 0,
            'total_games': announcement_status['total_games']
        }
        
        # 評估數據質量
        if announcement_status['announcement_coverage'] >= 80:
            feasibility['data_quality'] = 40
        elif announcement_status['announcement_coverage'] >= 60:
            feasibility['data_quality'] = 30
        elif announcement_status['announcement_coverage'] >= 40:
            feasibility['data_quality'] = 20
        else:
            feasibility['data_quality'] = 10
        
        # 評估時機
        current_hour = datetime.now().hour
        if 9 <= current_hour <= 15:  # 上午9點到下午3點是最佳預測時間
            feasibility['timing'] = 30
        elif 6 <= current_hour <= 18:  # 早上6點到晚上6點是良好時間
            feasibility['timing'] = 20
        else:
            feasibility['timing'] = 10
        
        # 評估覆蓋率
        ready_games = sum(1 for game in announcement_status['games'] 
                         if game['prediction_readiness']['overall_score'] >= 70)
        feasibility['ready_games'] = ready_games
        
        if feasibility['total_games'] > 0:
            coverage_rate = ready_games / feasibility['total_games']
            if coverage_rate >= 0.8:
                feasibility['coverage'] = 30
            elif coverage_rate >= 0.6:
                feasibility['coverage'] = 20
            elif coverage_rate >= 0.4:
                feasibility['coverage'] = 15
            else:
                feasibility['coverage'] = 5
        
        # 計算總分
        feasibility['overall_score'] = (
            feasibility['data_quality'] + 
            feasibility['timing'] + 
            feasibility['coverage']
        )
        
        # 判斷狀態
        if feasibility['overall_score'] >= 80:
            feasibility['status'] = 'excellent'
        elif feasibility['overall_score'] >= 60:
            feasibility['status'] = 'good'
        elif feasibility['overall_score'] >= 40:
            feasibility['status'] = 'fair'
        else:
            feasibility['status'] = 'poor'
        
        return feasibility
    
    def launch_predictions(self, target_date: date = None, force: bool = False) -> Dict:
        """啟動預測流程"""
        if target_date is None:
            target_date = date.today()
        
        logger.info(f"啟動 {target_date} 的預測流程...")
        
        # 1. 檢查準備狀況
        readiness = self.check_prediction_readiness(target_date)
        
        if not force and readiness['prediction_feasibility']['status'] in ['poor', 'fair']:
            return {
                'success': False,
                'reason': 'prediction_conditions_not_met',
                'readiness': readiness,
                'recommendation': '建議等待更多投手公告信息'
            }
        
        # 2. 初始化預測器
        if not self.initialize_predictor():
            return {
                'success': False,
                'reason': 'predictor_initialization_failed',
                'readiness': readiness
            }
        
        # 3. 執行預測
        prediction_results = self._execute_predictions(target_date, readiness)
        
        return {
            'success': True,
            'date': target_date.strftime('%Y-%m-%d'),
            'readiness': readiness,
            'predictions': prediction_results
        }
    
    def _execute_predictions(self, target_date: date, readiness: Dict) -> Dict:
        """執行預測"""
        results = {
            'total_games': 0,
            'successful_predictions': 0,
            'failed_predictions': 0,
            'predictions': [],
            'summary': {}
        }
        
        # 獲取準備好的比賽
        ready_games = [
            game for game in readiness['announcement_data']['games']
            if game['prediction_readiness']['overall_score'] >= 70
        ]
        
        results['total_games'] = len(ready_games)

        def execute_predictions_in_context():
            for game_info in ready_games:
                try:
                    # 從數據庫獲取比賽對象
                    game = Game.query.filter_by(game_id=game_info['game_id']).first()

                    if game:
                        # 執行預測
                        prediction = self.predictor.predict_game(game)

                        if prediction:
                            results['predictions'].append({
                                'game_id': game.game_id,
                                'matchup': game_info['matchup'],
                                'prediction': prediction,
                                'pitcher_info': {
                                    'home_pitcher': game_info['home_pitcher'],
                                    'away_pitcher': game_info['away_pitcher']
                                },
                                'confidence': prediction.get('confidence', 0)
                            })
                            results['successful_predictions'] += 1
                        else:
                            results['failed_predictions'] += 1

                except Exception as e:
                    logger.error(f"預測比賽失敗 (Game ID: {game_info['game_id']}): {e}")
                    results['failed_predictions'] += 1

        try:
            if self.app:
                with self.app.app_context():
                    execute_predictions_in_context()
            else:
                # 如果沒有app實例，假設已在app_context中
                execute_predictions_in_context()
        except Exception as e:
            logger.error(f"執行預測時發生錯誤: {e}")
            results['failed_predictions'] = len(ready_games)
        
        # 生成預測摘要
        if results['successful_predictions'] > 0:
            avg_confidence = sum(p['confidence'] for p in results['predictions']) / results['successful_predictions']
            results['summary'] = {
                'success_rate': results['successful_predictions'] / results['total_games'] * 100,
                'average_confidence': avg_confidence,
                'high_confidence_predictions': sum(1 for p in results['predictions'] if p['confidence'] >= 0.75),
                'medium_confidence_predictions': sum(1 for p in results['predictions'] if 0.6 <= p['confidence'] < 0.75),
                'low_confidence_predictions': sum(1 for p in results['predictions'] if p['confidence'] < 0.6)
            }
        
        return results
    
    def get_daily_prediction_report(self, target_date: date = None) -> Dict:
        """獲取每日預測報告"""
        if target_date is None:
            target_date = date.today()
        
        # 檢查準備狀況
        readiness = self.check_prediction_readiness(target_date)
        
        # 獲取時機建議
        timing_rec = self.announcement_checker.get_prediction_timing_recommendation(target_date)
        
        report = {
            'date': target_date.strftime('%Y-%m-%d'),
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'readiness_analysis': readiness,
            'timing_recommendation': timing_rec,
            'action_plan': self._generate_action_plan(readiness, timing_rec)
        }
        
        return report
    
    def _generate_action_plan(self, readiness: Dict, timing_rec: Dict) -> Dict:
        """生成行動計劃"""
        feasibility = readiness['prediction_feasibility']
        
        action_plan = {
            'immediate_action': '',
            'next_steps': [],
            'optimal_prediction_time': '',
            'risk_assessment': ''
        }
        
        if feasibility['status'] == 'excellent':
            action_plan['immediate_action'] = '🚀 立即開始預測'
            action_plan['next_steps'] = [
                '執行全部比賽預測',
                '監控預測結果',
                '準備比賽開始前的最終確認'
            ]
            action_plan['optimal_prediction_time'] = '現在'
            action_plan['risk_assessment'] = '低風險 - 數據完整，預測條件優秀'
            
        elif feasibility['status'] == 'good':
            action_plan['immediate_action'] = '⏰ 可以開始預測，但建議再等待30分鐘'
            action_plan['next_steps'] = [
                '檢查剩餘投手公告',
                '執行已準備好的比賽預測',
                '設置提醒檢查未公告的比賽'
            ]
            action_plan['optimal_prediction_time'] = timing_rec['next_check_time']
            action_plan['risk_assessment'] = '中等風險 - 部分數據缺失'
            
        elif feasibility['status'] == 'fair':
            action_plan['immediate_action'] = '🔄 等待更多投手公告'
            action_plan['next_steps'] = [
                '每小時檢查投手公告更新',
                '準備預測系統',
                '監控MLB官方公告'
            ]
            action_plan['optimal_prediction_time'] = timing_rec['next_check_time']
            action_plan['risk_assessment'] = '高風險 - 數據不足，預測準確性可能受影響'
            
        else:
            action_plan['immediate_action'] = '❌ 暫停預測，等待明天'
            action_plan['next_steps'] = [
                '明天早上重新檢查',
                '檢查是否有比賽延期',
                '準備備用預測方案'
            ]
            action_plan['optimal_prediction_time'] = '明天 09:00'
            action_plan['risk_assessment'] = '極高風險 - 數據嚴重不足'
        
        return action_plan

def main():
    """測試2025年預測啟動器"""
    launcher = MLBPredictionLauncher2025()
    
    print("=" * 80)
    print("🚀 2025年MLB預測啟動器")
    print("=" * 80)
    
    # 獲取每日預測報告
    report = launcher.get_daily_prediction_report()
    
    print(f"\n📊 {report['date']} 預測準備報告")
    print(f"生成時間: {report['generated_at']}")
    
    # 顯示準備狀況
    feasibility = report['readiness_analysis']['prediction_feasibility']
    print(f"\n🎯 預測可行性分析:")
    print(f"總體評分: {feasibility['overall_score']}/100")
    print(f"狀態: {feasibility['status']}")
    print(f"準備好的比賽: {feasibility['ready_games']}/{feasibility['total_games']}")
    
    # 顯示行動計劃
    action_plan = report['action_plan']
    print(f"\n📋 行動計劃:")
    print(f"立即行動: {action_plan['immediate_action']}")
    print(f"最佳預測時間: {action_plan['optimal_prediction_time']}")
    print(f"風險評估: {action_plan['risk_assessment']}")
    
    print(f"\n📝 下一步驟:")
    for i, step in enumerate(action_plan['next_steps'], 1):
        print(f"  {i}. {step}")
    
    # 如果條件允許，詢問是否執行預測
    if feasibility['status'] in ['excellent', 'good']:
        print(f"\n🤔 是否要執行預測？(y/n)")
        # 在實際使用中，這裡可以接受用戶輸入
        # user_input = input().lower()
        # if user_input == 'y':
        #     result = launcher.launch_predictions()
        #     print(f"預測結果: {result}")

if __name__ == "__main__":
    main()
