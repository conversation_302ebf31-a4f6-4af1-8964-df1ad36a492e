#!/usr/bin/env python3
"""
每日先發投手公告檢查系統
專門用於2025年預測時檢查當天是否有先發投手公告
"""

import requests
import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
from models.database import Game, Team, db
from models.daily_lineup_fetcher import DailyLineupFetcher
import json

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DailyPitcherAnnouncementChecker:
    """每日先發投手公告檢查器"""
    
    def __init__(self):
        self.base_url = "https://statsapi.mlb.com/api/v1"
        self.lineup_fetcher = DailyLineupFetcher()
        
    def check_today_pitcher_announcements(self) -> Dict:
        """檢查今天的先發投手公告狀況"""
        today = date.today()
        return self.check_pitcher_announcements_for_date(today)
    
    def check_pitcher_announcements_for_date(self, target_date: date) -> Dict:
        """檢查指定日期的先發投手公告狀況"""
        logger.info(f"檢查 {target_date} 的先發投手公告...")
        
        # 獲取當日比賽和投手信息
        daily_data = self.lineup_fetcher.get_daily_lineups(target_date)
        
        announcement_status = {
            'date': target_date.strftime('%Y-%m-%d'),
            'total_games': daily_data['total_games'],
            'games_with_pitcher_announcements': 0,
            'announcement_coverage': 0.0,
            'games': [],
            'summary': {
                'fully_announced': 0,      # 雙方投手都已公告
                'partially_announced': 0,  # 只有一方投手公告
                'no_announcements': 0,     # 雙方投手都未公告
                'games_started': 0         # 已開始的比賽
            }
        }
        
        for game_info in daily_data['games']:
            game_status = self._analyze_game_pitcher_status(game_info)
            announcement_status['games'].append(game_status)
            
            # 統計各種狀況
            if game_status['status'] in ['In Progress', 'Final', 'Completed']:
                announcement_status['summary']['games_started'] += 1
            elif game_status['both_pitchers_announced']:
                announcement_status['summary']['fully_announced'] += 1
                announcement_status['games_with_pitcher_announcements'] += 1
            elif game_status['home_pitcher_announced'] or game_status['away_pitcher_announced']:
                announcement_status['summary']['partially_announced'] += 1
                announcement_status['games_with_pitcher_announcements'] += 0.5
            else:
                announcement_status['summary']['no_announcements'] += 1
        
        # 計算公告覆蓋率
        if announcement_status['total_games'] > 0:
            announcement_status['announcement_coverage'] = (
                announcement_status['games_with_pitcher_announcements'] / 
                announcement_status['total_games'] * 100
            )
        
        return announcement_status
    
    def _analyze_game_pitcher_status(self, game_info: Dict) -> Dict:
        """分析單場比賽的投手公告狀況"""
        home_pitcher_announced = bool(game_info.get('home_pitcher'))
        away_pitcher_announced = bool(game_info.get('away_pitcher'))
        
        # 判斷公告時間（如果有的話）
        announcement_time = self._estimate_announcement_time(game_info)
        
        return {
            'game_id': game_info.get('game_id'),
            'matchup': f"{game_info.get('away_team')} @ {game_info.get('home_team')}",
            'game_time': game_info.get('game_time'),
            'status': game_info.get('status'),
            'home_team': game_info.get('home_team'),
            'away_team': game_info.get('away_team'),
            'home_pitcher': game_info.get('home_pitcher'),
            'away_pitcher': game_info.get('away_pitcher'),
            'home_pitcher_announced': home_pitcher_announced,
            'away_pitcher_announced': away_pitcher_announced,
            'both_pitchers_announced': home_pitcher_announced and away_pitcher_announced,
            'announcement_time': announcement_time,
            'prediction_readiness': self._assess_prediction_readiness(game_info)
        }
    
    def _estimate_announcement_time(self, game_info: Dict) -> Optional[str]:
        """估算投手公告時間"""
        # 這裡可以根據比賽時間推算公告時間
        # 通常先發投手在比賽前24-48小時公告
        game_time = game_info.get('game_time')
        if game_time:
            try:
                # 假設公告時間是比賽前一天
                game_dt = datetime.fromisoformat(game_time.replace('Z', '+00:00'))
                announcement_dt = game_dt - timedelta(days=1)
                return announcement_dt.strftime('%Y-%m-%d %H:%M')
            except:
                pass
        return None
    
    def _assess_prediction_readiness(self, game_info: Dict) -> Dict:
        """評估比賽的預測準備度"""
        readiness = {
            'overall_score': 0,
            'factors': {
                'pitcher_info': 0,      # 投手信息完整度 (0-40分)
                'lineup_info': 0,       # 打線信息完整度 (0-30分)
                'timing': 0,            # 時間因素 (0-20分)
                'game_status': 0        # 比賽狀態 (0-10分)
            },
            'recommendation': '',
            'missing_elements': []
        }
        
        # 評估投手信息 (40分)
        if game_info.get('home_pitcher') and game_info.get('away_pitcher'):
            readiness['factors']['pitcher_info'] = 40
        elif game_info.get('home_pitcher') or game_info.get('away_pitcher'):
            readiness['factors']['pitcher_info'] = 20
            readiness['missing_elements'].append('部分先發投手未公告')
        else:
            readiness['missing_elements'].append('先發投手未公告')
        
        # 評估打線信息 (30分)
        home_lineup = game_info.get('home_lineup_confirmed', False)
        away_lineup = game_info.get('away_lineup_confirmed', False)
        
        if home_lineup and away_lineup:
            readiness['factors']['lineup_info'] = 30
        elif home_lineup or away_lineup:
            readiness['factors']['lineup_info'] = 15
            readiness['missing_elements'].append('部分打線未確定')
        else:
            readiness['missing_elements'].append('打線未確定')
        
        # 評估時間因素 (20分)
        game_status = game_info.get('status', '')
        if game_status in ['Scheduled', 'Pre-Game']:
            readiness['factors']['timing'] = 20
        elif game_status in ['Warmup', 'Delayed']:
            readiness['factors']['timing'] = 15
        elif game_status in ['In Progress']:
            readiness['factors']['timing'] = 5
            readiness['missing_elements'].append('比賽已開始')
        else:
            readiness['missing_elements'].append('比賽狀態異常')
        
        # 評估比賽狀態 (10分)
        if game_status == 'Scheduled':
            readiness['factors']['game_status'] = 10
        elif game_status in ['Pre-Game', 'Warmup']:
            readiness['factors']['game_status'] = 8
        elif game_status == 'Delayed':
            readiness['factors']['game_status'] = 6
        
        # 計算總分
        readiness['overall_score'] = sum(readiness['factors'].values())
        
        # 生成建議
        if readiness['overall_score'] >= 80:
            readiness['recommendation'] = '✅ 預測條件完備，可以進行高精度預測'
        elif readiness['overall_score'] >= 60:
            readiness['recommendation'] = '⚠️ 預測條件良好，建議等待更多信息'
        elif readiness['overall_score'] >= 40:
            readiness['recommendation'] = '🔄 預測條件一般，建議稍後再檢查'
        else:
            readiness['recommendation'] = '❌ 預測條件不足，不建議進行預測'
        
        return readiness
    
    def get_prediction_timing_recommendation(self, target_date: date = None) -> Dict:
        """獲取預測時機建議"""
        if target_date is None:
            target_date = date.today()
        
        announcement_status = self.check_pitcher_announcements_for_date(target_date)
        
        # 分析最佳預測時機
        ready_games = []
        waiting_games = []
        
        for game in announcement_status['games']:
            if game['prediction_readiness']['overall_score'] >= 80:
                ready_games.append(game)
            elif game['prediction_readiness']['overall_score'] >= 40:
                waiting_games.append(game)
        
        recommendation = {
            'date': target_date.strftime('%Y-%m-%d'),
            'total_games': announcement_status['total_games'],
            'ready_for_prediction': len(ready_games),
            'waiting_for_info': len(waiting_games),
            'overall_readiness': len(ready_games) / max(announcement_status['total_games'], 1) * 100,
            'timing_advice': '',
            'next_check_time': '',
            'ready_games': ready_games,
            'waiting_games': waiting_games
        }
        
        # 生成時機建議
        if recommendation['overall_readiness'] >= 70:
            recommendation['timing_advice'] = '🎯 現在是進行預測的好時機'
            recommendation['next_check_time'] = '比賽開始前1小時再次確認'
        elif recommendation['overall_readiness'] >= 40:
            recommendation['timing_advice'] = '⏰ 建議等待2-4小時後再檢查'
            recommendation['next_check_time'] = (datetime.now() + timedelta(hours=3)).strftime('%H:%M')
        else:
            recommendation['timing_advice'] = '📅 建議明天早上再檢查投手公告'
            recommendation['next_check_time'] = '明天 09:00'
        
        return recommendation

def main():
    """測試每日投手公告檢查功能"""
    checker = DailyPitcherAnnouncementChecker()
    
    print("=" * 80)
    print("⚾ 每日先發投手公告檢查系統")
    print("=" * 80)
    
    # 檢查今天的投手公告狀況
    today_status = checker.check_today_pitcher_announcements()
    
    print(f"\n📅 {today_status['date']} 投手公告狀況:")
    print(f"總比賽數: {today_status['total_games']}")
    print(f"公告覆蓋率: {today_status['announcement_coverage']:.1f}%")
    
    summary = today_status['summary']
    print(f"\n📊 詳細統計:")
    print(f"  完全公告: {summary['fully_announced']} 場")
    print(f"  部分公告: {summary['partially_announced']} 場")
    print(f"  未公告: {summary['no_announcements']} 場")
    print(f"  已開始: {summary['games_started']} 場")
    
    # 顯示預測時機建議
    timing_rec = checker.get_prediction_timing_recommendation()
    
    print(f"\n🎯 預測時機建議:")
    print(f"整體準備度: {timing_rec['overall_readiness']:.1f}%")
    print(f"可預測比賽: {timing_rec['ready_for_prediction']} 場")
    print(f"等待信息: {timing_rec['waiting_for_info']} 場")
    print(f"建議: {timing_rec['timing_advice']}")
    print(f"下次檢查: {timing_rec['next_check_time']}")
    
    # 顯示具體比賽狀況
    print(f"\n🔍 比賽詳細狀況:")
    for i, game in enumerate(today_status['games'][:5]):  # 只顯示前5場
        print(f"\n比賽 {i+1}: {game['matchup']}")
        print(f"  狀態: {game['status']}")
        print(f"  主隊投手: {game['home_pitcher'] or '❌ 未公告'}")
        print(f"  客隊投手: {game['away_pitcher'] or '❌ 未公告'}")
        
        readiness = game['prediction_readiness']
        print(f"  預測準備度: {readiness['overall_score']}/100")
        print(f"  建議: {readiness['recommendation']}")
        
        if readiness['missing_elements']:
            print(f"  缺少: {', '.join(readiness['missing_elements'])}")

if __name__ == "__main__":
    main()
