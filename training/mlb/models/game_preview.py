"""
比賽預覽功能 - 類似MLB官網的比賽預覽頁面
顯示先發投手信息和打線對戰統計
"""

import logging
from datetime import date, datetime
from typing import Dict, List, Optional, Tuple
from sqlalchemy import text, and_, or_
from models.database import db, Game, Team, PlayerStats, BoxScore, GameDetail
import requests

logger = logging.getLogger(__name__)

class GamePreview:
    """比賽預覽類 - 提供MLB官網風格的比賽預覽功能"""
    
    def __init__(self):
        self.base_url = "https://statsapi.mlb.com/api/v1"
        self.logger = logger
    
    def get_game_preview(self, game_id: str) -> Dict:
        """獲取比賽預覽信息"""
        self.logger.info(f"獲取比賽 {game_id} 的預覽信息...")
        
        try:
            # 獲取比賽基本信息
            game = Game.query.filter_by(game_id=game_id).first()
            if not game:
                return self._empty_preview()
            
            # 獲取球隊信息
            home_team = Team.query.filter_by(team_code=game.home_team).first()
            away_team = Team.query.filter_by(team_code=game.away_team).first()
            
            # 獲取先發投手信息
            probable_pitchers = self._get_probable_pitchers(game)
            
            # 獲取打線對戰統計
            matchups = self._get_lineup_matchups(game, probable_pitchers)
            
            # 組合預覽數據
            preview_data = {
                'game': {
                    'game_id': game.game_id,
                    'date': game.date.strftime('%Y-%m-%d'),
                    'time': getattr(game, 'time', 'TBD') or 'TBD',
                    'venue': game.venue or 'Unknown',
                    'status': game.game_status
                },
                'teams': {
                    'away': {
                        'name': away_team.team_name if away_team else game.away_team,
                        'code': game.away_team,
                        'record': self._get_team_record(away_team) if away_team else '0-0'
                    },
                    'home': {
                        'name': home_team.team_name if home_team else game.home_team,
                        'code': game.home_team,
                        'record': self._get_team_record(home_team) if home_team else '0-0'
                    }
                },
                'probable_pitchers': probable_pitchers,
                'matchups': matchups
            }
            
            return preview_data
            
        except Exception as e:
            self.logger.error(f"獲取比賽預覽失敗: {e}")
            return self._empty_preview()
    
    def _get_probable_pitchers(self, game: Game) -> Dict:
        """獲取先發投手信息"""
        pitchers = {
            'away': None,
            'home': None
        }
        
        try:
            # 從GameDetail獲取先發投手
            game_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
            
            if game_detail:
                # 客隊投手
                if game_detail.away_starting_pitcher:
                    pitchers['away'] = self._get_pitcher_stats(
                        game_detail.away_starting_pitcher, 
                        game.away_team
                    )
                
                # 主隊投手
                if game_detail.home_starting_pitcher:
                    pitchers['home'] = self._get_pitcher_stats(
                        game_detail.home_starting_pitcher, 
                        game.home_team
                    )
            
            # 如果沒有GameDetail數據，嘗試從MLB API獲取
            if not pitchers['away'] or not pitchers['home']:
                api_pitchers = self._get_pitchers_from_api(game.game_id)
                if api_pitchers:
                    if not pitchers['away'] and api_pitchers.get('away'):
                        pitchers['away'] = api_pitchers['away']
                    if not pitchers['home'] and api_pitchers.get('home'):
                        pitchers['home'] = api_pitchers['home']
                        
        except Exception as e:
            self.logger.warning(f"獲取先發投手信息失敗: {e}")
        
        return pitchers
    
    def _get_pitcher_stats(self, pitcher_name: str, team_code: str) -> Optional[Dict]:
        """獲取投手統計信息"""
        try:
            # 查詢投手統計
            query = text("""
                SELECT 
                    player_name,
                    SUM(wins) as wins,
                    SUM(losses) as losses,
                    AVG(era) as era,
                    SUM(strikeouts_pitching) as strikeouts,
                    SUM(innings_pitched) as innings_pitched,
                    AVG(whip) as whip
                FROM player_stats 
                WHERE player_name LIKE :pitcher_name 
                AND (innings_pitched > 0 OR era > 0 OR strikeouts_pitching > 0)
                AND season = 2024
                GROUP BY player_name
            """)
            
            result = db.session.execute(query, {'pitcher_name': f'%{pitcher_name}%'}).fetchone()
            
            if result:
                return {
                    'name': pitcher_name,
                    'wins': int(result.wins or 0),
                    'losses': int(result.losses or 0),
                    'era': round(float(result.era or 0), 2),
                    'strikeouts': int(result.strikeouts or 0),
                    'innings_pitched': round(float(result.innings_pitched or 0), 1),
                    'whip': round(float(result.whip or 0), 3),
                    'record': f"{int(result.wins or 0)}-{int(result.losses or 0)}"
                }
            else:
                # 如果沒有統計數據，返回基本信息
                return {
                    'name': pitcher_name,
                    'wins': 0,
                    'losses': 0,
                    'era': 0.00,
                    'strikeouts': 0,
                    'innings_pitched': 0.0,
                    'whip': 0.000,
                    'record': '0-0'
                }
                
        except Exception as e:
            self.logger.warning(f"獲取投手 {pitcher_name} 統計失敗: {e}")
            return None
    
    def _get_pitchers_from_api(self, game_id: str) -> Optional[Dict]:
        """從MLB API獲取先發投手信息"""
        try:
            url = f"{self.base_url}/schedule?gamePk={game_id}&hydrate=probablePitcher"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'dates' in data and data['dates']:
                    games = data['dates'][0].get('games', [])
                    if games:
                        game_data = games[0]
                        probable_pitchers = game_data.get('teams', {})
                        
                        pitchers = {}
                        
                        # 客隊投手
                        away_pitcher = probable_pitchers.get('away', {}).get('probablePitcher')
                        if away_pitcher:
                            pitchers['away'] = {
                                'name': away_pitcher.get('fullName', 'Unknown'),
                                'wins': 0,
                                'losses': 0,
                                'era': 0.00,
                                'strikeouts': 0,
                                'record': '0-0'
                            }
                        
                        # 主隊投手
                        home_pitcher = probable_pitchers.get('home', {}).get('probablePitcher')
                        if home_pitcher:
                            pitchers['home'] = {
                                'name': home_pitcher.get('fullName', 'Unknown'),
                                'wins': 0,
                                'losses': 0,
                                'era': 0.00,
                                'strikeouts': 0,
                                'record': '0-0'
                            }
                        
                        return pitchers
                        
        except Exception as e:
            self.logger.warning(f"從API獲取投手信息失敗: {e}")
        
        return None
    
    def _get_lineup_matchups(self, game: Game, probable_pitchers: Dict) -> Dict:
        """獲取打線對戰統計"""
        matchups = {
            'away_vs_home_pitcher': [],
            'home_vs_away_pitcher': []
        }
        
        try:
            # 獲取客隊對主隊投手的統計
            if probable_pitchers.get('home'):
                home_pitcher_name = probable_pitchers['home']['name']
                matchups['away_vs_home_pitcher'] = self._get_team_vs_pitcher_stats(
                    game.away_team, home_pitcher_name
                )
            
            # 獲取主隊對客隊投手的統計
            if probable_pitchers.get('away'):
                away_pitcher_name = probable_pitchers['away']['name']
                matchups['home_vs_away_pitcher'] = self._get_team_vs_pitcher_stats(
                    game.home_team, away_pitcher_name
                )
                
        except Exception as e:
            self.logger.warning(f"獲取打線對戰統計失敗: {e}")
        
        return matchups
    
    def _get_team_vs_pitcher_stats(self, team_code: str, pitcher_name: str) -> List[Dict]:
        """獲取球隊打者對投手的統計"""
        matchup_stats = []
        
        try:
            # 查詢該球隊的主要打者
            query = text("""
                SELECT 
                    player_name,
                    SUM(at_bats) as at_bats,
                    SUM(hits) as hits,
                    SUM(home_runs) as home_runs,
                    SUM(rbi) as rbi,
                    AVG(batting_avg) as batting_avg
                FROM player_stats ps
                JOIN teams t ON ps.team_id = t.team_id
                WHERE t.team_code = :team_code
                AND ps.season = 2024
                AND ps.at_bats > 50
                GROUP BY player_name
                ORDER BY SUM(at_bats) DESC
                LIMIT 8
            """)
            
            results = db.session.execute(query, {'team_code': team_code}).fetchall()
            
            for result in results:
                # 這裡簡化處理，實際應該查詢具體的對戰數據
                matchup_stats.append({
                    'player_name': result.player_name,
                    'at_bats': int(result.at_bats or 0),
                    'hits': int(result.hits or 0),
                    'batting_avg': round(float(result.batting_avg or 0), 3),
                    'vs_pitcher_ab': 0,  # 對該投手的打數
                    'vs_pitcher_hits': 0,  # 對該投手的安打
                    'vs_pitcher_avg': 0.000  # 對該投手的打擊率
                })
                
        except Exception as e:
            self.logger.warning(f"獲取球隊 {team_code} 對投手 {pitcher_name} 統計失敗: {e}")
        
        return matchup_stats
    
    def _get_team_record(self, team: Team) -> str:
        """獲取球隊戰績"""
        try:
            # 簡化處理，返回基本戰績
            return "0-0"  # 實際應該從TeamStats獲取
        except:
            return "0-0"
    
    def _empty_preview(self) -> Dict:
        """返回空的預覽數據"""
        return {
            'game': {},
            'teams': {'away': {}, 'home': {}},
            'probable_pitchers': {'away': None, 'home': None},
            'matchups': {'away_vs_home_pitcher': [], 'home_vs_away_pitcher': []}
        }
