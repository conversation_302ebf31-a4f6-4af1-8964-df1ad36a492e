"""
真實博彩盤口數據獲取器
從真實博彩網站獲取MLB的讓分盤和大小分盤口
整合多 API 自動切換功能
"""

import requests
import logging
from datetime import datetime, date
from typing import Dict, List, Optional
import json
from flask import current_app

logger = logging.getLogger(__name__)

class RealBettingOddsFetcher:
    """真實博彩盤口數據獲取器"""
    
    def __init__(self, app=None):
        self.app = app or current_app

        # 整合多 API 系統
        try:
            from .reliable_odds_fetcher import ReliableOddsFetcher
            self.multi_api_fetcher = ReliableOddsFetcher()
            logger.info("成功初始化多 API 系統")
        except ImportError as e:
            logger.warning(f"無法導入多 API 系統: {e}")
            self.multi_api_fetcher = None

        # 投手信息獲取系統 - 根據用戶要求在下載盤口時同時下載投手信息
        self.pitcher_fetchers = []
        try:
            from .sbr_pitcher_scraper import SBRPitcherScraper
            self.pitcher_fetchers.append(SBRPitcherScraper())
            logger.info("成功初始化 SBR 投手資訊抓取器")
        except ImportError as e:
            logger.warning(f"無法導入 SBR 投手抓取器: {e}")
        
        try:
            from .daily_lineup_fetcher import DailyLineupFetcher
            self.daily_lineup_fetcher = DailyLineupFetcher(app)
            logger.info("成功初始化每日陣容獲取器")
        except ImportError as e:
            logger.warning(f"無法導入每日陣容獲取器: {e}")
            self.daily_lineup_fetcher = None

        # The Odds API 配置（保留原有邏輯作為備用）
        self.odds_api_key = None  # 需要用戶提供API密鑰
        self.odds_api_base_url = "https://api.the-odds-api.com/v4"

        # 嘗試從多個來源獲取API密鑰
        import os
        self.odds_api_key = os.getenv('ODDS_API_KEY')

        # 如果環境變量沒有，嘗試從文件讀取
        if not self.odds_api_key:
            try:
                api_key_file = os.path.join(os.path.dirname(__file__), 'odds-api.txt')
                if os.path.exists(api_key_file):
                    with open(api_key_file, 'r') as f:
                        self.odds_api_key = f.read().strip()
                        logger.info("從 odds-api.txt 文件讀取API密鑰")
            except Exception as e:
                logger.warning(f"讀取API密鑰文件失敗: {e}")
        
        # 支持的博彩商
        self.supported_bookmakers = [
            'fanduel',      # FanDuel
            'draftkings',   # DraftKings
            'betmgm',       # BetMGM
            'caesars',      # Caesars
            'bovada',       # Bovada
            'pinnacle',     # Pinnacle
            'unibet_us'     # Unibet US
        ]
        
        # 支持的投注市場
        self.betting_markets = [
            'h2h',          # 勝負盤 (Moneyline)
            'spreads',      # 讓分盤 (Run Line)
            'totals'        # 大小分盤 (Over/Under)
        ]
    
    def set_api_key(self, api_key: str):
        """設置The Odds API密鑰"""
        self.odds_api_key = api_key
        logger.info("博彩盤口API密鑰已設置")
    
    def get_mlb_odds_today(self, target_date: date = None) -> Dict:
        """獲取今日MLB所有比賽的真實博彩盤口 - 優先使用網頁抓取，同時獲取投手信息"""
        if target_date is None:
            target_date = date.today()

        logger.info(f"獲取 {target_date} 的真實博彩盤口數據（包含投手信息）...")

        # 1. 優先使用網頁抓取器獲取真實數據
        web_result = self._get_odds_from_web_scraping(target_date)
        if web_result and web_result.get('success') and web_result.get('games'):
            logger.info(f"網頁抓取成功獲取 {len(web_result['games'])} 場比賽的真實盤口數據")
            # 增強盤口數據與投手信息
            enhanced_result = self._enhance_odds_with_pitcher_info(web_result, target_date)
            # 保存抓取的盤口數據到數據庫
            self._save_odds_to_database(enhanced_result, target_date)
            return enhanced_result

        # 2. 檢查數據庫中是否有真實盤口數據
        db_result = self._get_odds_from_database(target_date)
        if db_result and db_result.get('games'):
            logger.info(f"從數據庫獲取 {len(db_result['games'])} 場比賽的真實盤口數據")
            # 增強盤口數據與投手信息
            enhanced_result = self._enhance_odds_with_pitcher_info(db_result, target_date)
            return enhanced_result

        # 3. 使用API但只接受真實數據（非免費API）
        if self.multi_api_fetcher:
            try:
                logger.info(f"嘗試從API獲取 {target_date} 的真實博彩盤口...")

                # 使用多 API 系統獲取數據
                result = self.multi_api_fetcher.fetch_mlb_odds(target_date)

                if result.get('success'):
                    # 檢查是否為真實博彩數據（非免費API）
                    if not result.get('is_free_api', True):
                        games = result.get('games', [])
                        betting_odds = self._convert_to_betting_format(games, target_date, result)
                        logger.info(f"API成功獲取 {len(games)} 場比賽的真實盤口數據")
                        # 增強盤口數據與投手信息
                        enhanced_result = self._enhance_odds_with_pitcher_info(betting_odds, target_date)
                        # 保存API獲取的盤口數據到數據庫
                        self._save_odds_to_database(enhanced_result, target_date)
                        return enhanced_result
                    else:
                        logger.warning("API返回的是模擬數據，跳過使用")
                else:
                    logger.warning(f"API獲取失敗: {result.get('error')}")

            except Exception as e:
                logger.error(f"API獲取出錯: {e}")

        # 4. 如果都沒有真實數據，返回空結果而不是模擬數據
        logger.warning(f"無法獲取 {target_date} 的真實博彩盤口數據")
        return {
            'success': False,
            'message': '無真實博彩盤口數據可用',
            'games': [],
            'summary': {
                'total_games': 0,
                'games_with_odds': 0,
                'data_source': 'none',
                'is_real_data': False,
                'note': '拒絕使用模擬數據，僅提供真實博彩盤口'
            }
        }

    def _get_odds_from_web_scraping(self, target_date: date) -> Dict:
        """使用網頁抓取器獲取真實博彩盤口數據"""
        try:
            # 優先使用 JSON SBR 抓取器（最新，基於真實賠率數據）
            try:
                from .json_sbr_scraper import JSONSBRScraper
                scraper = JSONSBRScraper()
                result = scraper.fetch_historical_odds(target_date, bookmaker='fanduel')

                if result.get('success') and result.get('games'):
                    logger.info(f"JSON SBR抓取器成功: {len(result['games'])} 場比賽的真實賠率")
                    return self._convert_json_scraper_result_to_betting_format(result, target_date, 'sportsbookreview.com')
            except Exception as e:
                logger.warning(f"JSON SBR抓取失敗: {e}")

            # 嘗試使用covers.com抓取器
            try:
                from .covers_scraper import CoversMLBScraper
                scraper = CoversMLBScraper()
                result = scraper.fetch_mlb_games_for_date(target_date)

                if result.get('success') and result.get('games'):
                    logger.info(f"Covers.com抓取成功: {len(result['games'])} 場比賽")
                    return self._convert_scraper_result_to_betting_format(result, target_date, 'covers.com')
            except Exception as e:
                logger.warning(f"Covers.com抓取失敗: {e}")

            # 嘗試使用sportsbookreview抓取器
            try:
                from .sportsbookreview_scraper import SportsBookReviewScraper
                scraper = SportsBookReviewScraper()
                result = scraper.fetch_historical_odds(target_date, bookmaker='bet365')

                if result.get('success') and result.get('games'):
                    logger.info(f"SportsBookReview抓取成功: {len(result['games'])} 場比賽")
                    return self._convert_scraper_result_to_betting_format(result, target_date, 'sportsbookreview.com')
            except Exception as e:
                logger.warning(f"SportsBookReview抓取失敗: {e}")

            # 嘗試使用現代化抓取器
            try:
                from .modern_sbr_scraper import ModernSBRScraper
                scraper = ModernSBRScraper()
                result = scraper.fetch_historical_odds(target_date, bookmaker='bet365')

                if result.get('success') and result.get('games'):
                    logger.info(f"現代化抓取器成功: {len(result['games'])} 場比賽")
                    return self._convert_scraper_result_to_betting_format(result, target_date, 'modern_scraper')
            except Exception as e:
                logger.warning(f"現代化抓取器失敗: {e}")

        except Exception as e:
            logger.error(f"網頁抓取出錯: {e}")

        return None

    def _get_odds_from_database(self, target_date: date) -> Dict:
        """從數據庫獲取真實博彩盤口數據"""
        try:
            from flask import current_app
            import sqlite3
            import os

            # 獲取數據庫路徑
            basedir = os.path.abspath(os.path.dirname(current_app.instance_path))
            db_path = os.path.join(basedir, "instance", "mlb_data.db")

            if not os.path.exists(db_path):
                logger.warning("數據庫文件不存在")
                return None

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查詢真實博彩盤口數據
            date_str = target_date.strftime('%Y-%m-%d')
            query = """
                SELECT * FROM betting_odds
                WHERE game_date = ?
                AND data_source != 'simulated'
                AND data_source != 'algorithm'
                AND is_real_data = 1
            """

            cursor.execute(query, (date_str,))
            rows = cursor.fetchall()

            if rows:
                # 轉換為標準格式
                games = []
                for row in rows:
                    # 根據數據庫結構解析數據
                    game_data = self._parse_db_row_to_game_data(row)
                    if game_data:
                        games.append(game_data)

                conn.close()

                if games:
                    logger.info(f"從數據庫獲取 {len(games)} 場比賽的真實盤口數據")
                    return {
                        'success': True,
                        'games': games,
                        'summary': {
                            'total_games': len(games),
                            'games_with_odds': len(games),
                            'data_source': 'database',
                            'is_real_data': True
                        }
                    }

            conn.close()

        except Exception as e:
            logger.error(f"從數據庫獲取數據失敗: {e}")

        return None

    def _convert_scraper_result_to_betting_format(self, scraper_result: Dict, target_date: date, source: str) -> Dict:
        """將抓取器結果轉換為博彩盤口格式"""
        try:
            games = scraper_result.get('games', [])
            converted_games = []

            for game in games:
                converted_game = {
                    'id': f"{game.get('away_team', '')}_{game.get('home_team', '')}_{target_date.strftime('%Y%m%d')}",
                    'sport_key': 'baseball_mlb',
                    'sport_title': 'MLB',
                    'commence_time': target_date.isoformat(),
                    'home_team': game.get('home_team', ''),
                    'away_team': game.get('away_team', ''),
                    'bookmakers': []
                }

                # 處理賠率數據
                odds_info = game.get('odds', {})
                if odds_info:
                    bookmaker_data = {
                        'key': source,
                        'title': source.title(),
                        'last_update': target_date.isoformat(),
                        'markets': []
                    }

                    # 添加spread市場
                    if odds_info.get('spread_line'):
                        spread_market = {
                            'key': 'spreads',
                            'outcomes': [
                                {
                                    'name': game.get('away_team', ''),
                                    'point': float(odds_info.get('spread_line', 0)) * -1,
                                    'price': odds_info.get('spread_away_odds', 100)
                                },
                                {
                                    'name': game.get('home_team', ''),
                                    'point': float(odds_info.get('spread_line', 0)),
                                    'price': odds_info.get('spread_home_odds', 100)
                                }
                            ]
                        }
                        bookmaker_data['markets'].append(spread_market)

                    # 添加totals市場
                    if odds_info.get('total_line'):
                        totals_market = {
                            'key': 'totals',
                            'outcomes': [
                                {
                                    'name': 'Over',
                                    'point': float(odds_info.get('total_line', 0)),
                                    'price': odds_info.get('over_odds', 100)
                                },
                                {
                                    'name': 'Under',
                                    'point': float(odds_info.get('total_line', 0)),
                                    'price': odds_info.get('under_odds', 100)
                                }
                            ]
                        }
                        bookmaker_data['markets'].append(totals_market)

                    # 添加moneyline市場
                    if odds_info.get('moneyline_away') or odds_info.get('moneyline_home'):
                        moneyline_market = {
                            'key': 'h2h',
                            'outcomes': [
                                {
                                    'name': game.get('away_team', ''),
                                    'price': odds_info.get('moneyline_away', 100)
                                },
                                {
                                    'name': game.get('home_team', ''),
                                    'price': odds_info.get('moneyline_home', 100)
                                }
                            ]
                        }
                        bookmaker_data['markets'].append(moneyline_market)

                    if bookmaker_data['markets']:
                        converted_game['bookmakers'].append(bookmaker_data)

                if converted_game['bookmakers']:
                    converted_games.append(converted_game)

            return {
                'success': True,
                'games': converted_games,
                'summary': {
                    'total_games': len(converted_games),
                    'games_with_odds': len(converted_games),
                    'data_source': source,
                    'is_real_data': True,
                    'note': f'真實博彩盤口數據來自 {source}'
                }
            }

        except Exception as e:
            logger.error(f"轉換抓取器結果失敗: {e}")
            return None

    def _parse_db_row_to_game_data(self, row) -> Dict:
        """將數據庫行數據解析為比賽數據格式"""
        try:
            # 這裡需要根據實際的數據庫結構來解析
            # 假設數據庫結構包含基本的比賽和盤口信息
            return {
                'away_team': row[1] if len(row) > 1 else '',
                'home_team': row[2] if len(row) > 2 else '',
                'odds': {
                    'spread_line': row[3] if len(row) > 3 else None,
                    'total_line': row[4] if len(row) > 4 else None,
                    'moneyline_away': row[5] if len(row) > 5 else None,
                    'moneyline_home': row[6] if len(row) > 6 else None
                }
            }
        except Exception as e:
            logger.warning(f"解析數據庫行失敗: {e}")
            return None

            logger.info(f"成功獲取 {len(integrated_odds.get('games', []))} 場比賽的博彩盤口")
            return integrated_odds

        except Exception as e:
            logger.error(f"獲取真實博彩盤口失敗: {e}")
            return self._get_empty_odds_response(target_date)

    def _convert_to_betting_format(self, games: List, target_date: date, api_result: Dict) -> Dict:
        """將多 API 系統的數據轉換為博彩盤口格式"""
        betting_data = {
            'date': target_date.strftime('%Y-%m-%d'),
            'fetched_at': datetime.now().isoformat(),
            'games': [],
            'summary': {
                'total_games': len(games),
                'games_with_odds': 0,
                'data_source': 'multi_api_system',
                'is_free_api': api_result.get('is_free_api', False),
                'api_sources': api_result.get('sources', [])
            }
        }

        for game in games:
            home_team = game.get('home_team', {})
            away_team = game.get('away_team', {})

            # 基本比賽信息
            betting_game = {
                'game_id': game.get('id', ''),
                'home_team': home_team.get('abbreviation', home_team.get('name', '')),
                'away_team': away_team.get('abbreviation', away_team.get('name', '')),
                'game_time': game.get('date', ''),
                'status': game.get('status', ''),
                'venue': game.get('venue', ''),
                'odds': {}
            }

            # 處理賠率信息
            odds_info = game.get('odds', {})
            if odds_info:
                betting_data['summary']['games_with_odds'] += 1

                # 勝負盤 (Moneyline)
                if odds_info.get('home_odds') and odds_info.get('away_odds'):
                    betting_game['odds']['moneyline'] = {
                        'home': odds_info.get('home_odds'),
                        'away': odds_info.get('away_odds'),
                        'bookmaker': odds_info.get('provider', 'Unknown')
                    }

                # 免費 API 不提供博彩盤口數據，跳過模擬數據生成
                if api_result.get('is_free_api'):
                    logger.info("免費 API 不提供博彩盤口數據，跳過讓分盤和大小分盤")
                    # 不再生成模擬數據

            betting_data['games'].append(betting_game)

        return betting_data

    def _convert_json_scraper_result_to_betting_format(self, scraper_result: Dict, target_date: date, source: str) -> Dict:
        """將 JSON 抓取器結果轉換為博彩盤口格式"""
        try:
            games = scraper_result.get('games', [])
            converted_games = []

            for game in games:
                converted_game = {
                    'id': f"{game.get('away_team', '')}_{game.get('home_team', '')}_{target_date.strftime('%Y%m%d')}",
                    'sport_key': 'baseball_mlb',
                    'sport_title': 'MLB',
                    'commence_time': game.get('start_time', target_date.isoformat()),
                    'home_team': game.get('home_team', ''),
                    'away_team': game.get('away_team', ''),
                    'bookmakers': []
                }

                # 處理博彩商數據
                bookmaker_data = {
                    'key': source,
                    'title': source.title(),
                    'last_update': target_date.isoformat(),
                    'markets': []
                }

                # 添加 totals 市場
                if 'totals' in game:
                    totals_info = game['totals']
                    totals_market = {
                        'key': 'totals',
                        'outcomes': [
                            {
                                'name': 'Over',
                                'point': float(totals_info.get('total_line', 0)),
                                'price': int(totals_info.get('over_odds', 100))
                            },
                            {
                                'name': 'Under',
                                'point': float(totals_info.get('total_line', 0)),
                                'price': int(totals_info.get('under_odds', 100))
                            }
                        ]
                    }
                    bookmaker_data['markets'].append(totals_market)

                # 添加 spreads 市場
                if 'spreads' in game:
                    spreads_info = game['spreads']
                    spreads_market = {
                        'key': 'spreads',
                        'outcomes': [
                            {
                                'name': game.get('away_team', ''),
                                'point': float(spreads_info.get('away_spread', 0)),
                                'price': int(spreads_info.get('away_odds', 100))
                            },
                            {
                                'name': game.get('home_team', ''),
                                'point': float(spreads_info.get('home_spread', 0)),
                                'price': int(spreads_info.get('home_odds', 100))
                            }
                        ]
                    }
                    bookmaker_data['markets'].append(spreads_market)

                if bookmaker_data['markets']:
                    converted_game['bookmakers'].append(bookmaker_data)
                    converted_games.append(converted_game)

            logger.info(f"成功轉換 {len(converted_games)} 場比賽的真實博彩盤口數據")
            
            return {
                'success': True,
                'date': target_date.strftime('%Y-%m-%d'),
                'source': source,
                'data_type': 'real_odds',
                'is_real_data': True,
                'games': converted_games,
                'total_games': len(converted_games),
                'bookmaker': scraper_result.get('bookmaker', 'FanDuel'),
                'message': f'成功獲取 {len(converted_games)} 場比賽的真實博彩盤口數據'
            }

        except Exception as e:
            logger.error(f"轉換 JSON 抓取器結果失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'date': target_date.strftime('%Y-%m-%d'),
                'source': source
            }

    def _fetch_odds_for_market(self, market: str) -> Optional[Dict]:
        """獲取特定市場的盤口數據"""
        try:
            url = f"{self.odds_api_base_url}/sports/baseball_mlb/odds"
            
            params = {
                'apiKey': self.odds_api_key,
                'regions': 'us',  # 美國地區
                'markets': market,
                'oddsFormat': 'american',  # 美式賠率
                'bookmakers': ','.join(self.supported_bookmakers[:3])  # 限制博彩商數量
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"成功獲取 {market} 市場數據: {len(data)} 場比賽")
            
            return {
                'market': market,
                'games': data,
                'fetched_at': datetime.now().isoformat()
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"獲取 {market} 市場數據失敗: {e}")
            return None
        except Exception as e:
            logger.error(f"處理 {market} 市場數據失敗: {e}")
            return None
    
    def _integrate_odds_data(self, all_odds: Dict, target_date: date) -> Dict:
        """整合不同市場的盤口數據"""
        integrated_data = {
            'date': target_date.strftime('%Y-%m-%d'),
            'fetched_at': datetime.now().isoformat(),
            'games': [],
            'summary': {
                'total_games': 0,
                'markets_available': list(all_odds.keys()),
                'bookmakers_count': 0
            }
        }
        
        # 以勝負盤為基礎，整合其他市場數據
        h2h_data = all_odds.get('h2h', {}).get('games', [])
        spreads_data = all_odds.get('spreads', {}).get('games', [])
        totals_data = all_odds.get('totals', {}).get('games', [])
        
        # 創建遊戲ID映射
        spreads_map = {self._create_game_key(game): game for game in spreads_data}
        totals_map = {self._create_game_key(game): game for game in totals_data}
        
        for h2h_game in h2h_data:
            game_key = self._create_game_key(h2h_game)
            
            # 整合比賽數據
            integrated_game = {
                'game_id': h2h_game.get('id'),
                'home_team': h2h_game.get('home_team'),
                'away_team': h2h_game.get('away_team'),
                'commence_time': h2h_game.get('commence_time'),
                'odds': {
                    'moneyline': self._extract_moneyline_odds(h2h_game),
                    'run_line': self._extract_run_line_odds(spreads_map.get(game_key)),
                    'total': self._extract_total_odds(totals_map.get(game_key)),
                    'is_simulated': False  # 標記為真實博彩盤口
                }
            }
            
            integrated_data['games'].append(integrated_game)
        
        integrated_data['summary']['total_games'] = len(integrated_data['games'])
        
        return integrated_data
    
    def _create_game_key(self, game: Dict) -> str:
        """創建比賽唯一標識"""
        home = game.get('home_team', '').replace(' ', '').lower()
        away = game.get('away_team', '').replace(' ', '').lower()
        return f"{away}@{home}"
    
    def _extract_moneyline_odds(self, game: Dict) -> Dict:
        """提取勝負盤賠率"""
        if not game or not game.get('bookmakers'):
            return {'home': None, 'away': None, 'bookmaker': None}
        
        # 使用第一個博彩商的數據
        bookmaker = game['bookmakers'][0]
        h2h_market = None
        
        for market in bookmaker.get('markets', []):
            if market.get('key') == 'h2h':
                h2h_market = market
                break
        
        if not h2h_market:
            return {'home': None, 'away': None, 'bookmaker': None}
        
        outcomes = h2h_market.get('outcomes', [])
        home_odds = None
        away_odds = None
        
        for outcome in outcomes:
            if outcome.get('name') == game.get('home_team'):
                home_odds = outcome.get('price')
            elif outcome.get('name') == game.get('away_team'):
                away_odds = outcome.get('price')
        
        return {
            'home': home_odds,
            'away': away_odds,
            'bookmaker': bookmaker.get('title')
        }
    
    def _extract_run_line_odds(self, game: Dict) -> Dict:
        """提取讓分盤賠率"""
        if not game or not game.get('bookmakers'):
            return {'home_line': None, 'home_odds': None, 'away_line': None, 'away_odds': None, 'bookmaker': None}
        
        bookmaker = game['bookmakers'][0]
        spreads_market = None
        
        for market in bookmaker.get('markets', []):
            if market.get('key') == 'spreads':
                spreads_market = market
                break
        
        if not spreads_market:
            return {'home_line': None, 'home_odds': None, 'away_line': None, 'away_odds': None, 'bookmaker': None}
        
        outcomes = spreads_market.get('outcomes', [])
        home_data = {}
        away_data = {}
        
        for outcome in outcomes:
            if outcome.get('name') == game.get('home_team'):
                home_data = {'line': outcome.get('point'), 'odds': outcome.get('price')}
            elif outcome.get('name') == game.get('away_team'):
                away_data = {'line': outcome.get('point'), 'odds': outcome.get('price')}
        
        return {
            'home_line': home_data.get('line'),
            'home_odds': home_data.get('odds'),
            'away_line': away_data.get('line'),
            'away_odds': away_data.get('odds'),
            'bookmaker': bookmaker.get('title')
        }
    
    def _extract_total_odds(self, game: Dict) -> Dict:
        """提取大小分盤賠率"""
        if not game or not game.get('bookmakers'):
            return {'line': None, 'over_odds': None, 'under_odds': None, 'bookmaker': None}
        
        bookmaker = game['bookmakers'][0]
        totals_market = None
        
        for market in bookmaker.get('markets', []):
            if market.get('key') == 'totals':
                totals_market = market
                break
        
        if not totals_market:
            return {'line': None, 'over_odds': None, 'under_odds': None, 'bookmaker': None}
        
        outcomes = totals_market.get('outcomes', [])
        over_data = {}
        under_data = {}
        total_line = None
        
        for outcome in outcomes:
            if outcome.get('name') == 'Over':
                over_data = {'odds': outcome.get('price')}
                total_line = outcome.get('point')
            elif outcome.get('name') == 'Under':
                under_data = {'odds': outcome.get('price')}
        
        return {
            'line': total_line,
            'over_odds': over_data.get('odds'),
            'under_odds': under_data.get('odds'),
            'bookmaker': bookmaker.get('title')
        }
    
    def _get_empty_odds_response(self, target_date: date) -> Dict:
        """返回空的博彩盤口響應（不再生成模擬數據）"""
        logger.warning("無法獲取真實博彩盤口數據，返回空響應")

        return {
            'date': target_date.strftime('%Y-%m-%d'),
            'fetched_at': datetime.now().isoformat(),
            'games': [],
            'summary': {
                'total_games': 0,
                'markets_available': [],
                'bookmakers_count': 0,
                'note': '無法獲取真實博彩盤口數據 - 需要有效的API密鑰'
            }
        }
    
    def get_game_odds(self, game_id: str, target_date: date = None) -> Optional[Dict]:
        """獲取特定比賽的博彩盤口 - 通過比賽ID或球隊名稱匹配"""
        if target_date is None:
            target_date = date.today()
        today_odds = self.get_mlb_odds_today(target_date)

        # 首先嘗試通過game_id匹配
        for game in today_odds.get('games', []):
            if game.get('game_id') == game_id:
                return game.get('odds')

        return None

    def get_game_odds_by_teams(self, home_team: str, away_team: str, target_date: date = None) -> Optional[Dict]:
        """通過球隊名稱獲取比賽的博彩盤口"""

        if target_date is None:
            target_date = date.today()

        # 首先嘗試從數據庫獲取讓分盤數據
        try:
            with self.app.app_context():
                from models.database import Game, BettingOdds

                # 查找指定日期的比賽
                game = Game.query.filter_by(
                    home_team=home_team,
                    away_team=away_team,
                    date=target_date
                ).first()

                if game:
                    # 獲取大小分數據
                    totals_odds = BettingOdds.query.filter_by(
                        game_id=game.game_id,
                        market_type='totals'
                    ).first()

                    # 獲取讓分盤數據
                    spreads_odds = BettingOdds.query.filter_by(
                        game_id=game.game_id,
                        market_type='spreads'
                    ).first()

                    if totals_odds or spreads_odds:
                        odds_data = {
                            'home_team': home_team,
                            'away_team': away_team,
                            'game_id': game.game_id
                        }

                        # 添加大小分數據
                        if totals_odds:
                            odds_data['totals'] = {
                                'total_point': totals_odds.total_point,
                                'over_odds': totals_odds.over_price or -110,
                                'under_odds': totals_odds.under_price or -110,
                                'bookmaker': totals_odds.bookmaker
                            }

                        # 添加讓分盤數據
                        if spreads_odds:
                            odds_data['run_line'] = {
                                'home_line': spreads_odds.home_spread_point,
                                'home_odds': spreads_odds.home_spread_price or -110,
                                'away_line': spreads_odds.away_spread_point,
                                'away_odds': spreads_odds.away_spread_price or -110,
                                'bookmaker': spreads_odds.bookmaker
                            }

                        logger.info(f"從數據庫獲取博彩盤口: {home_team}@{away_team}")
                        return odds_data

        except Exception as e:
            logger.warning(f"從數據庫獲取博彩盤口失敗: {e}")

        # 如果數據庫中沒有數據，根據用戶建議：如果不是today-1，就檢查odds table是否有該天的數據
        from datetime import timedelta
        
        yesterday = date.today() - timedelta(days=1)
        
        if target_date != yesterday:
            # 不是昨天的數據，檢查BettingOdds表中是否有該日期的數據
            try:
                from models.database import BettingOdds, Game
                existing_odds = BettingOdds.query.join(Game).filter(
                    Game.date == target_date
                ).first()
                
                if existing_odds:
                    logger.info(f"在BettingOdds表中找到 {target_date} 的盤口數據，構造返回數據")
                    # 構造返回該比賽的盤口數據
                    return self._construct_odds_from_database(home_team, away_team, target_date)
                else:
                    logger.info(f"BettingOdds表中沒有 {target_date} 的盤口數據，也不嘗試實時抓取")
                    return None
            except Exception as e:
                logger.error(f"檢查BettingOdds表失敗: {e}")
                return None
        
        # 只對昨天的日期嘗試從API獲取實時數據
        logger.info(f"目標日期是昨天 {target_date}，嘗試從API獲取博彩盤口數據")
        today_odds = self.get_mlb_odds_today(target_date)

        # 標準化球隊名稱進行匹配
        home_normalized = self._normalize_team_name(home_team)
        away_normalized = self._normalize_team_name(away_team)

        for game in today_odds.get('games', []):
            game_home = self._normalize_team_name(game.get('home_team', ''))
            game_away = self._normalize_team_name(game.get('away_team', ''))

            if game_home == home_normalized and game_away == away_normalized:
                return game.get('odds')

        return None

    def _normalize_team_name(self, team_name: str) -> str:
        """標準化球隊名稱以便匹配"""
        if not team_name:
            return ''

        # 首先檢查是否為3字母代碼
        team_code_mappings = {
            # 3字母代碼到標準化名稱的映射
            'tor': 'bluejays',
            'bos': 'redsox',
            'nyy': 'yankees',
            'tb': 'rays',
            'bal': 'orioles',
            'cws': 'whitesox',
            'cle': 'guardians',
            'det': 'tigers',
            'kc': 'royals',
            'min': 'twins',
            'hou': 'astros',
            'laa': 'angels',
            'oak': 'athletics',
            'sea': 'mariners',
            'tex': 'rangers',
            'atl': 'braves',
            'mia': 'marlins',
            'nym': 'mets',
            'phi': 'phillies',
            'was': 'nationals',
            'chc': 'cubs',
            'cin': 'reds',
            'mil': 'brewers',
            'pit': 'pirates',
            'stl': 'cardinals',
            'ari': 'diamondbacks',
            'col': 'rockies',
            'lad': 'dodgers',
            'sd': 'padres',
            'sf': 'giants'
        }

        # 轉為小寫進行檢查
        team_lower = team_name.lower()

        # 如果是3字母代碼，直接映射
        if team_lower in team_code_mappings:
            return team_code_mappings[team_lower]

        # 移除空格並轉為小寫
        normalized = team_name.replace(' ', '').lower()

        # 處理完整球隊名稱到標準化名稱的映射
        full_name_mappings = {
            'newyorkyankees': 'yankees',
            'newyorkmets': 'mets',
            'losangelesdodgers': 'dodgers',
            'losangelesangels': 'angels',
            'sanfranciscogiants': 'giants',
            'sandiegopadres': 'padres',
            'chicagocubs': 'cubs',
            'chicagowhitesox': 'whitesox',
            'stlouiscardinals': 'cardinals',
            'tampabaydevils': 'rays',
            'tampabayrays': 'rays',
            'torontobluejays': 'bluejays',
            'bostonredsox': 'redsox',
            'philadelphiaphillies': 'phillies',
            'atlantabraves': 'braves',
            'washingtonnationals': 'nationals',
            'miamimarlins': 'marlins',
            'milwaukeebrewers': 'brewers',
            'minnesottwins': 'twins',
            'detroittigers': 'tigers',
            'clevelandguardians': 'guardians',
            'kansascityroyals': 'royals',
            'houstonastros': 'astros',
            'texasrangers': 'rangers',
            'seattlemariners': 'mariners',
            'oaklandathletics': 'athletics',
            'coloradorockies': 'rockies',
            'arizonadiamondbacks': 'diamondbacks',
            'pittsburghpirates': 'pirates',
            'cincinnatireds': 'reds',
            'baltimoreorioles': 'orioles'
        }

        return full_name_mappings.get(normalized, normalized)

    def check_multi_api_status(self) -> Dict:
        """檢查多 API 系統狀態"""
        status = {
            'multi_api_available': bool(self.multi_api_fetcher),
            'multi_api_status': None,
            'original_api_status': None,
            'overall_status': 'unknown',
            'recommendations': []
        }

        # 檢查多 API 系統
        if self.multi_api_fetcher:
            try:
                multi_status = self.multi_api_fetcher.get_status_report()
                status['multi_api_status'] = multi_status

                active_endpoints = multi_status.get('active_endpoints', 0)
                free_apis = multi_status.get('free_apis', [])
                available_free = len([api for api in free_apis if api.get('status') == '可用'])

                if active_endpoints > 0:
                    status['overall_status'] = 'excellent'
                    status['recommendations'].append('付費 API 可用，可獲取完整博彩賠率')
                elif available_free > 0:
                    status['overall_status'] = 'good'
                    status['recommendations'].append('免費 API 可用，提供基本比賽數據')
                else:
                    status['overall_status'] = 'poor'
                    status['recommendations'].append('所有 API 都不可用，建議檢查網絡連接')

            except Exception as e:
                status['multi_api_status'] = f'錯誤: {str(e)}'
                status['overall_status'] = 'error'
                status['recommendations'].append('多 API 系統出錯，建議重新初始化')
        else:
            status['recommendations'].append('多 API 系統未初始化，建議重新啟動應用')

        # 檢查原有 API
        original_status = self.check_api_status()
        status['original_api_status'] = original_status

        if original_status.get('api_accessible'):
            status['recommendations'].append('原有 API 可用作為備用')

        return status

    def check_api_status(self) -> Dict:
        """檢查博彩盤口API狀態"""
        status = {
            'api_key_configured': bool(self.odds_api_key),
            'api_accessible': False,
            'remaining_requests': None,
            'last_check': datetime.now().isoformat()
        }
        
        if not self.odds_api_key:
            status['message'] = '未配置API密鑰，使用模擬數據'
            return status
        
        try:
            # 測試API連接
            url = f"{self.odds_api_base_url}/sports"
            params = {'apiKey': self.odds_api_key}
            
            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            
            status['api_accessible'] = True
            status['remaining_requests'] = response.headers.get('x-requests-remaining')
            status['message'] = 'API正常運行'
            
        except Exception as e:
            status['message'] = f'API連接失敗: {str(e)}'
        
        return status

    def _enhance_odds_with_pitcher_info(self, odds_result: Dict, target_date: date) -> Dict:
        """增強博彩盤口數據，添加投手信息"""
        if not odds_result or not odds_result.get('success'):
            return odds_result
        
        logger.info(f"開始增強盤口數據，添加投手信息...")
        
        try:
            # 獲取投手信息
            pitcher_data = self._fetch_pitcher_information(target_date)
            
            if not pitcher_data:
                logger.warning("未能獲取到投手信息，返回原始盤口數據")
                return odds_result
            
            # 為每場比賽添加投手信息
            enhanced_games = []
            games = odds_result.get('games', [])
            
            for game in games:
                enhanced_game = dict(game)  # 複製原始遊戲數據
                
                # 嘗試匹配投手信息
                pitcher_info = self._match_pitcher_to_game(enhanced_game, pitcher_data)
                
                if pitcher_info:
                    # 添加投手信息到遊戲數據中
                    enhanced_game['pitcher_info'] = pitcher_info
                    logger.info(f"為 {enhanced_game.get('away_team')}@{enhanced_game.get('home_team')} 添加投手信息")
                else:
                    # 提供空的投手信息結構
                    enhanced_game['pitcher_info'] = {
                        'home_pitcher': 'N/A',
                        'away_pitcher': 'N/A',
                        'source': 'none',
                        'confidence': 'none'
                    }
                
                enhanced_games.append(enhanced_game)
            
            # 更新結果
            enhanced_result = dict(odds_result)
            enhanced_result['games'] = enhanced_games
            enhanced_result['pitcher_info_added'] = True
            enhanced_result['pitcher_sources_used'] = len(self.pitcher_fetchers)
            
            logger.info(f"成功為 {len([g for g in enhanced_games if g.get('pitcher_info', {}).get('home_pitcher') != 'N/A'])} 場比賽添加了投手信息")
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"增強盤口數據時出錯: {e}")
            # 出錯時返回原始數據
            return odds_result

    def _fetch_pitcher_information(self, target_date: date) -> Dict:
        """從各種來源獲取投手信息"""
        all_pitcher_info = {}
        
        try:
            # 1. 使用SBR投手抓取器
            for pitcher_fetcher in self.pitcher_fetchers:
                try:
                    pitcher_info = pitcher_fetcher.fetch_pitcher_info(target_date)
                    if pitcher_info:
                        all_pitcher_info.update(pitcher_info)
                        logger.info(f"從 {pitcher_fetcher.__class__.__name__} 獲取 {len(pitcher_info)} 組投手信息")
                except Exception as e:
                    logger.warning(f"使用 {pitcher_fetcher.__class__.__name__} 獲取投手信息失敗: {e}")
            
            # 2. 使用每日陣容獲取器
            if self.daily_lineup_fetcher:
                try:
                    games = self.daily_lineup_fetcher.get_games_for_date(target_date)
                    for game in games:
                        pitcher_info = self.daily_lineup_fetcher.extract_probable_pitchers(game)
                        
                        # 提取球隊名稱
                        home_team = game.get('teams', {}).get('home', {}).get('team', {}).get('abbreviation', '')
                        away_team = game.get('teams', {}).get('away', {}).get('team', {}).get('abbreviation', '')
                        
                        if home_team and away_team and (pitcher_info.get('home_pitcher') or pitcher_info.get('away_pitcher')):
                            matchup_key = f"{away_team}@{home_team}"
                            all_pitcher_info[matchup_key] = {
                                'away_team': away_team,
                                'home_team': home_team,
                                'away_pitcher': pitcher_info.get('away_pitcher', 'N/A'),
                                'home_pitcher': pitcher_info.get('home_pitcher', 'N/A'),
                                'source': 'daily_lineup_fetcher',
                                'confidence': 'high'
                            }
                    
                    logger.info(f"從每日陣容獲取器獲取 {len([g for g in games if self.daily_lineup_fetcher.extract_probable_pitchers(g).get('home_pitcher')])} 組投手信息")
                    
                except Exception as e:
                    logger.warning(f"使用每日陣容獲取器獲取投手信息失敗: {e}")
            
            return all_pitcher_info
            
        except Exception as e:
            logger.error(f"獲取投手信息失敗: {e}")
            return {}

    def _match_pitcher_to_game(self, game: Dict, pitcher_data: Dict) -> Optional[Dict]:
        """將投手信息匹配到特定比賽"""
        try:
            # 提取比賽的球隊信息
            home_team = self._normalize_team_name(game.get('home_team', ''))
            away_team = self._normalize_team_name(game.get('away_team', ''))
            
            if not home_team or not away_team:
                return None
            
            # 嘗試各種匹配模式
            possible_keys = [
                f"{away_team}@{home_team}",
                f"{home_team}@{away_team}",
                f"{away_team.upper()}@{home_team.upper()}",
                f"{home_team.upper()}@{away_team.upper()}"
            ]
            
            # 也嘗試使用標準化的縮寫
            home_abbrev = self._get_team_abbreviation(home_team)
            away_abbrev = self._get_team_abbreviation(away_team)
            
            if home_abbrev and away_abbrev:
                possible_keys.extend([
                    f"{away_abbrev}@{home_abbrev}",
                    f"{home_abbrev}@{away_abbrev}"
                ])
            
            # 尋找匹配
            for key in possible_keys:
                if key in pitcher_data:
                    pitcher_info = pitcher_data[key]
                    # 確保主客場順序正確
                    if key.startswith(away_abbrev or away_team):
                        return pitcher_info
                    else:
                        # 如果順序顛倒，交換投手
                        return {
                            'away_team': away_team,
                            'home_team': home_team,
                            'away_pitcher': pitcher_info.get('home_pitcher', 'N/A'),
                            'home_pitcher': pitcher_info.get('away_pitcher', 'N/A'),
                            'source': pitcher_info.get('source', 'unknown'),
                            'confidence': pitcher_info.get('confidence', 'medium')
                        }
            
            return None
            
        except Exception as e:
            logger.error(f"匹配投手信息失敗: {e}")
            return None

    def _get_team_abbreviation(self, team_name: str) -> Optional[str]:
        """獲取球隊的標準縮寫"""
        abbreviation_mapping = {
            'yankees': 'NYY', 'mets': 'NYM', 'dodgers': 'LAD', 'angels': 'LAA',
            'giants': 'SF', 'padres': 'SD', 'cubs': 'CHC', 'whitesox': 'CWS',
            'cardinals': 'STL', 'rays': 'TB', 'bluejays': 'TOR', 'redsox': 'BOS',
            'phillies': 'PHI', 'braves': 'ATL', 'nationals': 'WSH', 'marlins': 'MIA',
            'brewers': 'MIL', 'twins': 'MIN', 'tigers': 'DET', 'guardians': 'CLE',
            'royals': 'KC', 'astros': 'HOU', 'rangers': 'TEX', 'mariners': 'SEA',
            'athletics': 'OAK', 'rockies': 'COL', 'diamondbacks': 'ARI',
            'pirates': 'PIT', 'reds': 'CIN', 'orioles': 'BAL'
        }
        
        normalized = self._normalize_team_name(team_name)
        return abbreviation_mapping.get(normalized)

    def _save_odds_to_database(self, odds_result: Dict, target_date: date) -> None:
        """將抓取的盤口數據保存到數據庫中"""
        if not odds_result or not odds_result.get('success') or not odds_result.get('games'):
            return

        try:
            logger.info(f"開始保存 {target_date} 的盤口數據到數據庫...")
            
            with self.app.app_context():
                from models.database import db, Game, BettingOdds
                
                games_saved = 0
                odds_saved = 0
                
                for game_data in odds_result.get('games', []):
                    try:
                        # 1. 獲取或創建比賽記錄
                        home_team = game_data.get('home_team', '')
                        away_team = game_data.get('away_team', '')
                        
                        if not home_team or not away_team:
                            continue
                        
                        # 生成game_id（如果沒有的話）
                        game_id = game_data.get('id') or game_data.get('game_id')
                        if not game_id:
                            # 生成簡單的game_id
                            date_str = target_date.strftime('%Y%m%d')
                            game_id = f"{away_team}@{home_team}_{date_str}"
                        
                        # 檢查Game是否存在
                        existing_game = Game.query.filter_by(
                            home_team=home_team,
                            away_team=away_team,
                            date=target_date
                        ).first()
                        
                        if not existing_game:
                            # 創建新的比賽記錄
                            new_game = Game(
                                game_id=game_id,
                                date=target_date,
                                home_team=home_team,
                                away_team=away_team,
                                game_status='scheduled'
                            )
                            db.session.add(new_game)
                            db.session.flush()  # 獲取ID
                            games_saved += 1
                            logger.info(f"創建新比賽記錄: {away_team} @ {home_team}")
                            current_game = new_game
                        else:
                            current_game = existing_game
                        
                        # 2. 保存盤口數據
                        bookmakers = game_data.get('bookmakers', [])
                        for bookmaker in bookmakers:
                            bookmaker_name = bookmaker.get('title', 'Unknown')
                            
                            for market in bookmaker.get('markets', []):
                                market_type = market.get('key')
                                
                                if market_type == 'spreads':
                                    # 保存讓分盤數據
                                    existing_spread = BettingOdds.query.filter_by(
                                        game_id=current_game.game_id,
                                        market_type='spreads',
                                        bookmaker=bookmaker_name
                                    ).first()
                                    
                                    if not existing_spread:
                                        outcomes = market.get('outcomes', [])
                                        home_data = next((o for o in outcomes if o.get('name') == home_team), {})
                                        away_data = next((o for o in outcomes if o.get('name') == away_team), {})
                                        
                                        spread_odds = BettingOdds(
                                            game_id=current_game.game_id,
                                            bookmaker=bookmaker_name,
                                            market_type='spreads',
                                            home_spread_point=home_data.get('point'),
                                            home_spread_price=home_data.get('price'),
                                            away_spread_point=away_data.get('point'),
                                            away_spread_price=away_data.get('price'),
                                            is_real=True,
                                            data_source=odds_result.get('source', 'web_scraping')
                                        )
                                        db.session.add(spread_odds)
                                        odds_saved += 1
                                
                                elif market_type == 'totals':
                                    # 保存大小分盤數據
                                    existing_total = BettingOdds.query.filter_by(
                                        game_id=current_game.game_id,
                                        market_type='totals',
                                        bookmaker=bookmaker_name
                                    ).first()
                                    
                                    if not existing_total:
                                        outcomes = market.get('outcomes', [])
                                        over_data = next((o for o in outcomes if o.get('name') == 'Over'), {})
                                        under_data = next((o for o in outcomes if o.get('name') == 'Under'), {})
                                        
                                        total_odds = BettingOdds(
                                            game_id=current_game.game_id,
                                            bookmaker=bookmaker_name,
                                            market_type='totals',
                                            total_point=over_data.get('point'),
                                            over_price=over_data.get('price'),
                                            under_price=under_data.get('price'),
                                            is_real=True,
                                            data_source=odds_result.get('source', 'web_scraping')
                                        )
                                        db.session.add(total_odds)
                                        odds_saved += 1
                        
                    except Exception as e:
                        logger.warning(f"保存比賽 {away_team}@{home_team} 的盤口數據失敗: {e}")
                        continue
                
                # 提交所有更改
                db.session.commit()
                logger.info(f"成功保存 {target_date} 的盤口數據: {games_saved} 場新比賽, {odds_saved} 條盤口記錄")
                
        except Exception as e:
            logger.error(f"保存盤口數據到數據庫失敗: {e}")
            try:
                db.session.rollback()
            except:
                pass

    def _construct_odds_from_database(self, home_team: str, away_team: str, target_date: date) -> Optional[Dict]:
        """從數據庫中的BettingOdds數據構造盤口返回格式"""
        try:
            with self.app.app_context():
                from models.database import Game, BettingOdds
                
                # 查找對應的比賽
                game = Game.query.filter_by(
                    home_team=home_team,
                    away_team=away_team,
                    date=target_date
                ).first()
                
                if not game:
                    return None
                
                # 獲取該比賽的盤口數據
                totals_odds = BettingOdds.query.filter_by(
                    game_id=game.game_id,
                    market_type='totals'
                ).first()
                
                spreads_odds = BettingOdds.query.filter_by(
                    game_id=game.game_id,
                    market_type='spreads'
                ).first()
                
                # 構造返回格式
                odds_data = {
                    'home_team': home_team,
                    'away_team': away_team,
                    'game_id': game.game_id
                }
                
                # 添加大小分數據
                if totals_odds:
                    odds_data['totals'] = {
                        'total_point': totals_odds.total_point,
                        'over_odds': totals_odds.over_price or -110,
                        'under_odds': totals_odds.under_price or -110,
                        'bookmaker': totals_odds.bookmaker
                    }
                
                # 添加讓分盤數據
                if spreads_odds:
                    odds_data['run_line'] = {
                        'home_line': spreads_odds.home_spread_point,
                        'home_odds': spreads_odds.home_spread_price or -110,
                        'away_line': spreads_odds.away_spread_point,
                        'away_odds': spreads_odds.away_spread_price or -110,
                        'bookmaker': spreads_odds.bookmaker
                    }
                
                logger.info(f"從數據庫構造盤口數據: {home_team}@{away_team}")
                return odds_data
                
        except Exception as e:
            logger.error(f"從數據庫構造盤口數據失敗: {e}")
            return None


def main():
    """測試博彩盤口獲取器"""
    fetcher = RealBettingOddsFetcher()
    
    print("=" * 80)
    print("🎰 真實博彩盤口數據獲取器測試")
    print("=" * 80)
    
    # 檢查API狀態
    status = fetcher.check_api_status()
    print(f"\n📊 API狀態: {status['message']}")
    
    # 獲取今日盤口
    odds_data = fetcher.get_mlb_odds_today()
    
    print(f"\n📈 今日MLB博彩盤口:")
    print(f"比賽數量: {odds_data['summary']['total_games']}")
    print(f"可用市場: {', '.join(odds_data['summary']['markets_available'])}")
    
    # 顯示前3場比賽的盤口
    for i, game in enumerate(odds_data['games'][:3]):
        print(f"\n🏟️  比賽 {i+1}: {game['away_team']} @ {game['home_team']}")
        
        # 勝負盤
        ml = game['odds']['moneyline']
        if ml['home'] and ml['away']:
            print(f"   勝負盤: 主隊 {ml['home']} | 客隊 {ml['away']}")
        
        # 讓分盤
        rl = game['odds']['run_line']
        if rl['home_line'] and rl['away_line']:
            print(f"   讓分盤: 主隊 {rl['home_line']} ({rl['home_odds']}) | 客隊 {rl['away_line']} ({rl['away_odds']})")
        
        # 大小分
        total = game['odds']['total']
        if total['line']:
            print(f"   大小分: {total['line']} (大分 {total['over_odds']} | 小分 {total['under_odds']})")


if __name__ == "__main__":
    main()
