#!/usr/bin/env python3
"""
全面投手信息提取器
從多個數據源獲取投手信息，確保已完成比賽都能找到投手數據
"""

import logging
from datetime import date
from typing import Dict, Optional, List, Tuple

logger = logging.getLogger(__name__)

class ComprehensivePitcherExtractor:
    """全面投手信息提取器"""
    
    def __init__(self):
        self.pitcher_cache = {}
        
    def get_pitcher_info_comprehensive(self, game_id: str, home_team: str, away_team: str) -> Dict:
        """
        全面獲取投手信息 - 多數據源策略
        
        優先級：
        1. Game表的先發投手字段
        2. PlayerGameStats中的投手統計
        3. 勝負投手信息
        4. 球隊最近投手
        5. 默認投手信息
        """
        try:
            from models.database import db, Game, PlayerGameStats, Player, Team
            
            # 檢查緩存
            cache_key = f"comprehensive_{game_id}_{home_team}_{away_team}"
            if cache_key in self.pitcher_cache:
                logger.info(f"📦 使用緩存的全面投手分析: {cache_key}")
                return self.pitcher_cache[cache_key]
            
            # 獲取比賽信息
            game = Game.query.filter_by(game_id=game_id).first()
            if not game:
                logger.warning(f"⚠️ 找不到比賽: {game_id}")
                return self._get_default_analysis(home_team, away_team)
            
            logger.info(f"🔍 全面分析比賽: {away_team} @ {home_team} ({game.date})")
            logger.info(f"   比賽狀態: {game.game_status}")
            if game.home_score is not None and game.away_score is not None:
                logger.info(f"   實際比分: {game.away_score} - {game.home_score}")
            
            # 方法1: 從Game表獲取先發投手
            home_pitcher, away_pitcher = self._get_pitchers_from_game_table(game)
            
            # 方法2: 如果沒有先發投手，從PlayerGameStats獲取
            if not home_pitcher:
                home_pitcher = self._get_pitcher_from_game_stats(game_id, home_team, is_home=True)
            if not away_pitcher:
                away_pitcher = self._get_pitcher_from_game_stats(game_id, away_team, is_home=False)
            
            # 方法3: 如果還沒有，使用勝負投手信息
            if not home_pitcher or not away_pitcher:
                win_home, win_away = self._get_pitchers_from_win_loss(game, home_team, away_team)
                if not home_pitcher:
                    home_pitcher = win_home
                if not away_pitcher:
                    away_pitcher = win_away
            
            # 方法4: 最後使用球隊平均
            if not home_pitcher:
                home_pitcher = self._get_team_default_pitcher(home_team)
            if not away_pitcher:
                away_pitcher = self._get_team_default_pitcher(away_team)
            
            # 分析投手對戰
            analysis = self._analyze_pitcher_matchup(home_pitcher, away_pitcher, home_team, away_team)
            
            # 緩存結果
            self.pitcher_cache[cache_key] = analysis
            
            logger.info(f"✅ 全面投手分析完成: {analysis['home_pitcher']['name']} vs {analysis['away_pitcher']['name']}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 全面投手信息提取失敗: {e}")
            return self._get_default_analysis(home_team, away_team)
    
    def _get_pitchers_from_game_table(self, game) -> Tuple[Optional[Dict], Optional[Dict]]:
        """從GameDetail表獲取先發投手信息"""
        try:
            from models.database import GameDetail

            home_pitcher = None
            away_pitcher = None

            # 查找GameDetail記錄
            game_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
            if not game_detail:
                logger.info(f"⚠️ 找不到比賽詳情: {game.game_id}")
                return None, None

            # 檢查主隊先發投手
            if game_detail.home_starting_pitcher and game_detail.home_starting_pitcher.strip():
                home_pitcher = {
                    'name': game_detail.home_starting_pitcher,
                    'era': 4.50,  # 默認ERA，後續可以查詢詳細統計
                    'quality': 50.0,
                    'source': 'game_detail_starting'
                }
                logger.info(f"✅ 從GameDetail表找到主隊先發投手: {game_detail.home_starting_pitcher}")

            # 檢查客隊先發投手
            if game_detail.away_starting_pitcher and game_detail.away_starting_pitcher.strip():
                away_pitcher = {
                    'name': game_detail.away_starting_pitcher,
                    'era': 4.50,
                    'quality': 50.0,
                    'source': 'game_detail_starting'
                }
                logger.info(f"✅ 從GameDetail表找到客隊先發投手: {game_detail.away_starting_pitcher}")

            return home_pitcher, away_pitcher

        except Exception as e:
            logger.warning(f"⚠️ 從GameDetail表獲取先發投手失敗: {e}")
            return None, None
    
    def _get_pitcher_from_game_stats(self, game_id: str, team: str, is_home: bool) -> Optional[Dict]:
        """從PlayerGameStats獲取投手信息"""
        try:
            from models.database import db, PlayerGameStats, Team
            
            # 獲取球隊ID
            team_obj = Team.query.filter_by(team_code=team).first()
            if not team_obj:
                return None
            
            # 查找該比賽中該球隊的投手，按投球局數排序
            pitchers = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.team_id == team_obj.team_id,
                PlayerGameStats.position == 'P',
                PlayerGameStats.innings_pitched > 0
            ).order_by(PlayerGameStats.innings_pitched.desc()).all()
            
            if pitchers:
                # 取投球局數最多的投手（通常是先發投手）
                starting_pitcher = pitchers[0]
                
                # 計算ERA
                if starting_pitcher.innings_pitched > 0:
                    era = (starting_pitcher.earned_runs * 9.0) / starting_pitcher.innings_pitched
                else:
                    era = 0.0
                
                pitcher_info = {
                    'name': f"投手#{starting_pitcher.player_id}",
                    'era': era if era > 0 else 4.50,
                    'quality': self._calculate_pitcher_quality_from_era(era if era > 0 else 4.50),
                    'innings_pitched': starting_pitcher.innings_pitched,
                    'earned_runs': starting_pitcher.earned_runs,
                    'source': 'game_stats'
                }
                
                logger.info(f"✅ 從PlayerGameStats找到 {team} 投手: {pitcher_info['name']} (ERA: {pitcher_info['era']:.2f})")
                return pitcher_info
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ 從PlayerGameStats獲取投手失敗: {e}")
            return None
    
    def _get_pitchers_from_win_loss(self, game, home_team: str, away_team: str) -> Tuple[Optional[Dict], Optional[Dict]]:
        """從勝負投手信息獲取投手"""
        try:
            from models.database import GameDetail

            home_pitcher = None
            away_pitcher = None

            # 查找GameDetail記錄
            game_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
            if not game_detail:
                return None, None

            # 檢查勝利投手
            if game_detail.winning_pitcher and game_detail.winning_pitcher.strip():
                # 判斷勝利投手屬於哪個球隊
                if game.home_score > game.away_score:
                    # 主隊獲勝，勝利投手是主隊的
                    home_pitcher = {
                        'name': game_detail.winning_pitcher,
                        'era': 3.50,  # 勝利投手通常表現較好
                        'quality': 70.0,
                        'source': 'winning_pitcher'
                    }
                    logger.info(f"✅ 主隊勝利投手: {game_detail.winning_pitcher}")
                else:
                    # 客隊獲勝，勝利投手是客隊的
                    away_pitcher = {
                        'name': game_detail.winning_pitcher,
                        'era': 3.50,
                        'quality': 70.0,
                        'source': 'winning_pitcher'
                    }
                    logger.info(f"✅ 客隊勝利投手: {game_detail.winning_pitcher}")

            # 檢查敗戰投手
            if game_detail.losing_pitcher and game_detail.losing_pitcher.strip():
                # 判斷敗戰投手屬於哪個球隊
                if game.home_score < game.away_score:
                    # 主隊敗戰，敗戰投手是主隊的
                    if not home_pitcher:  # 如果還沒有主隊投手信息
                        home_pitcher = {
                            'name': game_detail.losing_pitcher,
                            'era': 5.50,  # 敗戰投手通常表現較差
                            'quality': 30.0,
                            'source': 'losing_pitcher'
                        }
                        logger.info(f"✅ 主隊敗戰投手: {game_detail.losing_pitcher}")
                else:
                    # 客隊敗戰，敗戰投手是客隊的
                    if not away_pitcher:  # 如果還沒有客隊投手信息
                        away_pitcher = {
                            'name': game_detail.losing_pitcher,
                            'era': 5.50,
                            'quality': 30.0,
                            'source': 'losing_pitcher'
                        }
                        logger.info(f"✅ 客隊敗戰投手: {game_detail.losing_pitcher}")

            return home_pitcher, away_pitcher

        except Exception as e:
            logger.warning(f"⚠️ 從勝負投手獲取信息失敗: {e}")
            return None, None
    
    def _get_team_default_pitcher(self, team: str) -> Dict:
        """獲取球隊默認投手信息"""
        return {
            'name': f'{team} 投手',
            'era': 4.50,
            'quality': 50.0,
            'source': 'team_default'
        }
    
    def _analyze_pitcher_matchup(self, home_pitcher: Dict, away_pitcher: Dict, 
                               home_team: str, away_team: str) -> Dict:
        """分析投手對戰"""
        try:
            # 分類投手強度
            home_strength = self._classify_pitcher_strength(home_pitcher['era'], home_pitcher['quality'])
            away_strength = self._classify_pitcher_strength(away_pitcher['era'], away_pitcher['quality'])
            
            # 確定對戰類型
            matchup_type = self._determine_matchup_type(home_strength, away_strength)
            
            # 添加強度信息
            home_pitcher['strength'] = home_strength
            away_pitcher['strength'] = away_strength
            
            analysis = {
                'home_pitcher': home_pitcher,
                'away_pitcher': away_pitcher,
                'matchup_type': matchup_type,
                'average_era': (home_pitcher['era'] + away_pitcher['era']) / 2,
                'average_quality': (home_pitcher['quality'] + away_pitcher['quality']) / 2,
                'data_sources': {
                    'home': home_pitcher['source'],
                    'away': away_pitcher['source']
                }
            }
            
            logger.info(f"📊 投手對戰分析: {home_pitcher['name']}({home_strength}) vs {away_pitcher['name']}({away_strength}) - {matchup_type}")
            logger.info(f"   數據來源: 主隊={home_pitcher['source']}, 客隊={away_pitcher['source']}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 投手對戰分析失敗: {e}")
            return self._get_default_analysis(home_team, away_team)
    
    def _calculate_pitcher_quality_from_era(self, era: float) -> float:
        """根據ERA計算投手質量分數"""
        return max(0, 100 - (era - 2.0) * 20)
    
    def _classify_pitcher_strength(self, era: float, quality: float) -> str:
        """分類投手強度"""
        if era <= 2.50 and quality >= 80:
            return "王牌"
        elif era <= 3.50 and quality >= 70:
            return "優秀"
        elif era <= 4.50 and quality >= 50:
            return "普通"
        else:
            return "弱勢"
    
    def _determine_matchup_type(self, home_strength: str, away_strength: str) -> str:
        """確定對戰類型"""
        strength_order = {'王牌': 4, '優秀': 3, '普通': 2, '弱勢': 1}
        
        home_level = strength_order.get(home_strength, 2)
        away_level = strength_order.get(away_strength, 2)
        
        if home_level >= 3 and away_level >= 3:
            return "王牌對決"
        elif home_level <= 1 and away_level <= 1:
            return "打擊戰"
        elif abs(home_level - away_level) >= 2:
            return "強弱對戰"
        else:
            return "普通對戰"
    
    def _get_default_analysis(self, home_team: str, away_team: str) -> Dict:
        """獲取默認分析"""
        return {
            'home_pitcher': {
                'name': f'{home_team} 投手',
                'era': 4.50,
                'quality': 50.0,
                'strength': '普通',
                'source': 'default'
            },
            'away_pitcher': {
                'name': f'{away_team} 投手',
                'era': 4.50,
                'quality': 50.0,
                'strength': '普通',
                'source': 'default'
            },
            'matchup_type': '普通對戰',
            'average_era': 4.50,
            'average_quality': 50.0,
            'data_sources': {
                'home': 'default',
                'away': 'default'
            }
        }
    
    def diagnose_pitcher_data_availability(self, game_id: str) -> Dict:
        """診斷投手數據可用性"""
        try:
            from models.database import db, Game, PlayerGameStats, GameDetail

            diagnosis = {
                'game_id': game_id,
                'game_exists': False,
                'game_detail_pitchers': {},
                'player_game_stats_pitchers': 0,
                'recommendations': []
            }

            # 檢查比賽是否存在
            game = Game.query.filter_by(game_id=game_id).first()
            if not game:
                diagnosis['recommendations'].append('比賽不存在於數據庫中')
                return diagnosis

            diagnosis['game_exists'] = True
            diagnosis['game_info'] = {
                'home_team': game.home_team,
                'away_team': game.away_team,
                'date': game.date.isoformat() if game.date else None,
                'status': game.game_status,
                'score': f"{game.away_score}-{game.home_score}" if game.home_score is not None else "未完成"
            }

            # 檢查GameDetail表中的投手信息
            game_detail = GameDetail.query.filter_by(game_id=game_id).first()
            if game_detail:
                diagnosis['game_detail_pitchers'] = {
                    'home_starting': game_detail.home_starting_pitcher,
                    'away_starting': game_detail.away_starting_pitcher,
                    'winning': game_detail.winning_pitcher,
                    'losing': game_detail.losing_pitcher,
                    'save': game_detail.save_pitcher
                }
            else:
                diagnosis['game_detail_pitchers'] = {
                    'home_starting': None,
                    'away_starting': None,
                    'winning': None,
                    'losing': None,
                    'save': None
                }
                diagnosis['recommendations'].append('缺少GameDetail記錄，需要下載比賽詳情')

            # 檢查PlayerGameStats中的投手數據
            pitcher_stats = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.position == 'P'
            ).all()

            diagnosis['player_game_stats_pitchers'] = len(pitcher_stats)

            # 生成建議
            pitchers = diagnosis['game_detail_pitchers']
            if not pitchers['home_starting'] and not pitchers['away_starting']:
                if len(pitcher_stats) > 0:
                    diagnosis['recommendations'].append('GameDetail表缺少先發投手信息，但PlayerGameStats有投手數據，可以從中提取')
                else:
                    diagnosis['recommendations'].append('缺少所有投手信息，需要重新下載比賽數據')

            if game.game_status == 'completed' and len(pitcher_stats) == 0:
                diagnosis['recommendations'].append('已完成比賽但缺少詳細投手統計，建議重新下載BoxScore數據')

            return diagnosis

        except Exception as e:
            logger.error(f"❌ 診斷投手數據可用性失敗: {e}")
            return {'error': str(e)}

# 創建全局實例
comprehensive_pitcher_extractor = ComprehensivePitcherExtractor()
