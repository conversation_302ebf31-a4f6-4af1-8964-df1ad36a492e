"""
現代化的 SportsBookReview.com 爬蟲
使用 Selenium 處理 JavaScript 渲染的內容
"""

import time
import logging
from datetime import date, datetime
from typing import Dict, List, Optional
import re
import json

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.chrome.service import Service
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

logger = logging.getLogger(__name__)

class ModernSBRScraper:
    """現代化的 SportsBookReview 爬蟲，支持 JavaScript 渲染"""
    
    def __init__(self):
        self.base_url = "https://www.sportsbookreview.com"
        self.driver = None
        
        if not SELENIUM_AVAILABLE:
            logger.warning("Selenium 未安裝，無法使用現代化爬蟲")
            
    def _setup_driver(self):
        """設置 Chrome WebDriver"""
        if not SELENIUM_AVAILABLE:
            raise ImportError("需要安裝 selenium: pip install selenium")
            
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 無頭模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        try:
            # 使用 webdriver-manager 自動管理 ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            return True
        except Exception as e:
            logger.error(f"設置 WebDriver 失敗: {e}")
            return False
    
    def fetch_historical_odds(self, target_date: date, bookmaker: str = 'bet365') -> Dict:
        """
        抓取指定日期的歷史盤口數據
        
        Args:
            target_date: 目標日期
            bookmaker: 博彩商名稱
            
        Returns:
            包含盤口數據的字典
        """
        if not self._setup_driver():
            return {'success': False, 'error': 'WebDriver 設置失敗'}
            
        try:
            date_str = target_date.strftime('%Y-%m-%d')
            
            # 獲取總分盤數據
            totals_data = self._fetch_totals_data(date_str, bookmaker)
            
            # 獲取讓分盤數據
            spreads_data = self._fetch_spreads_data(date_str, bookmaker)
            
            # 合併數據
            combined_data = self._combine_data(totals_data, spreads_data)
            
            return {
                'success': True,
                'date': date_str,
                'bookmaker': bookmaker,
                'games': combined_data,
                'total_games': len(combined_data)
            }
            
        except Exception as e:
            logger.error(f"抓取歷史盤口數據失敗: {e}")
            return {'success': False, 'error': str(e)}
        finally:
            if self.driver:
                self.driver.quit()
    
    def _fetch_totals_data(self, date_str: str, bookmaker: str) -> List[Dict]:
        """抓取總分盤數據"""
        url = f"{self.base_url}/betting-odds/mlb-baseball/totals/full-game/?date={date_str}"
        
        try:
            logger.info(f"正在抓取總分盤數據: {url}")
            self.driver.get(url)
            
            # 等待頁面加載 - 增加等待時間
            time.sleep(15)

            # 等待數據表格加載
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By

                WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.TAG_NAME, "table"))
                )
                logger.info("總分盤表格元素已加載")
            except Exception as e:
                logger.warning(f"等待總分盤表格加載失敗: {e}")
            
            # 等待數據表格加載
            try:
                WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.TAG_NAME, "table"))
                )
            except TimeoutException:
                logger.warning("等待表格加載超時")
            
            # 查找比賽數據
            games_data = []
            
            # 嘗試多種選擇器
            selectors = [
                "tr[data-testid*='game']",
                "tr[class*='game']",
                "tr[class*='row']",
                "tbody tr"
            ]
            
            for selector in selectors:
                try:
                    rows = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if rows:
                        logger.info(f"找到 {len(rows)} 行數據，使用選擇器: {selector}")
                        break
                except:
                    continue
            
            if not rows:
                # 嘗試獲取所有表格行
                rows = self.driver.find_elements(By.TAG_NAME, "tr")
                logger.info(f"使用備用方法找到 {len(rows)} 行")
            
            for row in rows:
                try:
                    game_data = self._parse_totals_row(row, bookmaker)
                    if game_data:
                        games_data.append(game_data)
                except Exception as e:
                    logger.debug(f"解析行失敗: {e}")
                    continue
            
            logger.info(f"成功解析 {len(games_data)} 場比賽的總分盤數據")
            return games_data
            
        except Exception as e:
            logger.error(f"抓取總分盤數據失敗: {e}")
            return []
    
    def _fetch_spreads_data(self, date_str: str, bookmaker: str) -> List[Dict]:
        """抓取讓分盤數據"""
        url = f"{self.base_url}/betting-odds/mlb-baseball/pointspread/full-game/?date={date_str}"
        
        try:
            logger.info(f"正在抓取讓分盤數據: {url}")
            self.driver.get(url)
            
            # 等待頁面加載 - 增加等待時間以確保JavaScript完全加載
            time.sleep(15)

            # 等待表格元素出現
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By

                # 等待包含比賽數據的元素出現
                wait = WebDriverWait(self.driver, 20)
                wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
                logger.info("表格元素已加載")
            except Exception as e:
                logger.warning(f"等待表格元素失敗: {e}")
                # 繼續執行，可能數據已經加載
            
            # 類似的邏輯處理讓分盤數據
            games_data = []
            
            # 查找比賽行
            rows = self.driver.find_elements(By.TAG_NAME, "tr")
            
            for row in rows:
                try:
                    game_data = self._parse_spreads_row(row, bookmaker)
                    if game_data:
                        games_data.append(game_data)
                except Exception as e:
                    logger.debug(f"解析讓分盤行失敗: {e}")
                    continue
            
            logger.info(f"成功解析 {len(games_data)} 場比賽的讓分盤數據")
            return games_data
            
        except Exception as e:
            logger.error(f"抓取讓分盤數據失敗: {e}")
            return []
    
    def _parse_totals_row(self, row, bookmaker: str) -> Optional[Dict]:
        """解析總分盤行數據"""
        try:
            row_text = row.text.strip()
            
            # 跳過空行或標題行
            if not row_text or 'Team' in row_text or 'Game' in row_text:
                return None
            
            # 查找隊伍名稱
            team_elements = row.find_elements(By.TAG_NAME, "a")
            if len(team_elements) < 2:
                return None
            
            away_team = team_elements[0].text.strip()
            home_team = team_elements[1].text.strip()
            
            # 查找總分線
            total_pattern = r'(\d+\.?\d*)'
            total_matches = re.findall(total_pattern, row_text)
            
            if total_matches:
                total_line = float(total_matches[0])
                
                return {
                    'away_team': self._normalize_team_name(away_team),
                    'home_team': self._normalize_team_name(home_team),
                    'market_type': 'totals',
                    'bookmaker': bookmaker,
                    'totals_data': {
                        'total_point': total_line,
                        'over_price': -110,  # 默認賠率
                        'under_price': -110
                    }
                }
            
            return None
            
        except Exception as e:
            logger.debug(f"解析總分盤行失敗: {e}")
            return None
    
    def _parse_spreads_row(self, row, bookmaker: str) -> Optional[Dict]:
        """解析讓分盤行數據"""
        try:
            row_text = row.text.strip()
            
            # 跳過空行或標題行
            if not row_text or 'Team' in row_text or 'Game' in row_text:
                return None
            
            # 查找隊伍名稱
            team_elements = row.find_elements(By.TAG_NAME, "a")
            if len(team_elements) < 2:
                return None
            
            away_team = team_elements[0].text.strip()
            home_team = team_elements[1].text.strip()
            
            # 查找讓分線
            spread_pattern = r'([+-]?\d+\.?\d*)'
            spread_matches = re.findall(spread_pattern, row_text)
            
            if spread_matches:
                spread_line = float(spread_matches[0])
                
                return {
                    'away_team': self._normalize_team_name(away_team),
                    'home_team': self._normalize_team_name(home_team),
                    'market_type': 'spreads',
                    'bookmaker': bookmaker,
                    'spreads_data': {
                        'home_spread_point': spread_line,
                        'home_spread_price': -110,
                        'away_spread_point': -spread_line,
                        'away_spread_price': -110
                    }
                }
            
            return None
            
        except Exception as e:
            logger.debug(f"解析讓分盤行失敗: {e}")
            return None
    
    def _normalize_team_name(self, team_name: str) -> str:
        """標準化隊伍名稱"""
        # 移除特殊字符和空格
        team_name = re.sub(r'[^\w\s]', '', team_name).strip()
        
        # 簡單的隊名映射
        team_mapping = {
            'Yankees': 'NYY',
            'Red Sox': 'BOS',
            'Dodgers': 'LAD',
            'Angels': 'LAA',
            # 可以添加更多映射
        }
        
        for full_name, abbr in team_mapping.items():
            if full_name.lower() in team_name.lower():
                return abbr
        
        return team_name[:3].upper()  # 返回前3個字符作為縮寫
    
    def _combine_data(self, totals_data: List[Dict], spreads_data: List[Dict]) -> List[Dict]:
        """合併總分盤和讓分盤數據"""
        combined = {}
        
        # 處理總分盤數據
        for game in totals_data:
            key = f"{game['away_team']}@{game['home_team']}"
            if key not in combined:
                combined[key] = {
                    'away_team': game['away_team'],
                    'home_team': game['home_team'],
                    'bookmaker': game['bookmaker']
                }
            combined[key]['totals'] = game['totals_data']
        
        # 處理讓分盤數據
        for game in spreads_data:
            key = f"{game['away_team']}@{game['home_team']}"
            if key not in combined:
                combined[key] = {
                    'away_team': game['away_team'],
                    'home_team': game['home_team'],
                    'bookmaker': game['bookmaker']
                }
            combined[key]['spreads'] = game['spreads_data']
        
        return list(combined.values())
