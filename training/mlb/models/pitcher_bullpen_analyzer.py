#!/usr/bin/env python3
"""
投手+牛棚綜合分析系統
考慮先發投手通常只投5-7局，牛棚接力的影響
"""

import logging
from datetime import date, datetime
from typing import Dict, List, Optional, Tuple
import numpy as np
from sqlalchemy import and_, or_, func

from models.database import db, PlayerStats, TeamStats
from models.pitcher_vs_team_analyzer import PitcherVsTeamAnalyzer
from models.historical_data_optimizer import HistoricalDataOptimizer

logger = logging.getLogger(__name__)

class PitcherBullpenAnalyzer:
    """投手+牛棚綜合分析器 - 考慮先發投手局數限制"""
    
    def __init__(self):
        self.data_optimizer = HistoricalDataOptimizer()
        self.pitcher_analyzer = PitcherVsTeamAnalyzer()
        
        # MLB先發投手典型局數分布
        self.starter_innings_distribution = {
            'ace_pitcher': {'avg_innings': 6.8, 'quality_start_rate': 0.75},  # 王牌投手
            'solid_starter': {'avg_innings': 6.2, 'quality_start_rate': 0.65},  # 穩定先發
            'average_starter': {'avg_innings': 5.8, 'quality_start_rate': 0.50},  # 平均先發
            'struggling_starter': {'avg_innings': 5.2, 'quality_start_rate': 0.35}  # 掙扎先發
        }
        
        # 牛棚ERA典型範圍
        self.bullpen_performance_tiers = {
            'elite': {'era_range': (2.50, 3.20), 'reliability': 0.85},
            'good': {'era_range': (3.20, 3.80), 'reliability': 0.75},
            'average': {'era_range': (3.80, 4.40), 'reliability': 0.65},
            'poor': {'era_range': (4.40, 5.50), 'reliability': 0.50}
        }
    
    async def analyze_complete_pitching_staff(self, 
                                            starting_pitcher: str,
                                            team: str,
                                            opposing_team: str,
                                            target_date: date,
                                            is_home: bool = True) -> Dict:
        """
        分析完整投手陣容 (先發 + 牛棚)
        
        Args:
            starting_pitcher: 先發投手名稱
            team: 投手所屬球隊  
            opposing_team: 對戰球隊
            target_date: 目標日期
            is_home: 是否主場
        """
        
        logger.info(f"🎯 分析完整投手陣容: {starting_pitcher} + {team} 牛棚 vs {opposing_team}")
        
        analysis_result = {
            'starting_pitcher': starting_pitcher,
            'team': team,
            'opposing_team': opposing_team,
            'is_home': is_home,
            'starter_analysis': {},
            'bullpen_analysis': {},
            'combined_prediction': {},
            'innings_breakdown': {},
            'confidence_assessment': {}
        }
        
        try:
            # 1. 先發投手詳細分析
            starter_analysis = await self.pitcher_analyzer.analyze_pitcher_vs_team(
                pitcher_name=starting_pitcher,
                opposing_team=opposing_team,
                target_date=target_date,
                is_home_pitcher=is_home
            )
            
            # 2. 判斷先發投手類型和預期局數
            starter_profile = self._assess_starter_profile(
                starting_pitcher, starter_analysis, target_date
            )
            
            # 3. 牛棚分析
            bullpen_analysis = await self._analyze_team_bullpen(
                team, opposing_team, target_date
            )
            
            # 4. 計算局數分配
            innings_breakdown = self._calculate_innings_distribution(
                starter_profile, bullpen_analysis
            )
            
            # 5. 綜合預測計算
            combined_prediction = self._calculate_combined_pitching_prediction(
                starter_analysis, bullpen_analysis, innings_breakdown
            )
            
            # 6. 信心度評估
            confidence = self._assess_combined_confidence(
                starter_analysis, bullpen_analysis, innings_breakdown
            )
            
            analysis_result.update({
                'starter_analysis': starter_analysis,
                'starter_profile': starter_profile,
                'bullpen_analysis': bullpen_analysis,
                'innings_breakdown': innings_breakdown,
                'combined_prediction': combined_prediction,
                'confidence_assessment': confidence
            })
            
            logger.info(f"✅ 完整投手分析完成: 預期失分 {combined_prediction.get('expected_runs_allowed', 0):.1f}")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"完整投手分析失敗: {e}")
            analysis_result['error'] = str(e)
            return analysis_result
    
    def _assess_starter_profile(self, 
                               starting_pitcher: str, 
                               starter_analysis: Dict, 
                               target_date: date) -> Dict:
        """評估先發投手類型和預期表現"""
        
        profile = {
            'pitcher_name': starting_pitcher,
            'pitcher_tier': 'average_starter',
            'expected_innings': 6.0,
            'quality_start_probability': 0.50,
            'early_exit_risk': 0.20
        }
        
        try:
            # 基於歷史對戰表現判斷投手類型
            performance = starter_analysis.get('performance_vs_team', {})
            confidence = starter_analysis.get('confidence_level', 'low')
            
            era_vs_team = performance.get('era_vs_team', 4.50)
            games_pitched = performance.get('games_pitched', 0)
            avg_innings = performance.get('avg_innings_per_start', 6.0)
            
            # 根據ERA分類投手
            if era_vs_team <= 3.20 and games_pitched >= 5:
                profile.update({
                    'pitcher_tier': 'ace_pitcher',
                    'expected_innings': 6.8,
                    'quality_start_probability': 0.75,
                    'early_exit_risk': 0.10
                })
            elif era_vs_team <= 3.80 and games_pitched >= 3:
                profile.update({
                    'pitcher_tier': 'solid_starter',
                    'expected_innings': 6.2,
                    'quality_start_probability': 0.65,
                    'early_exit_risk': 0.15
                })
            elif era_vs_team >= 5.00:
                profile.update({
                    'pitcher_tier': 'struggling_starter',
                    'expected_innings': 5.2,
                    'quality_start_probability': 0.35,
                    'early_exit_risk': 0.35
                })
            
            # 使用實際歷史平均局數調整
            if avg_innings > 0:
                profile['expected_innings'] = min(7.5, max(4.5, avg_innings))
            
            # 信心度影響
            if confidence == 'low':
                profile['early_exit_risk'] += 0.10
                profile['quality_start_probability'] *= 0.9
            
            logger.info(f"投手類型評估: {starting_pitcher} = {profile['pitcher_tier']} (預期{profile['expected_innings']:.1f}局)")
            
        except Exception as e:
            logger.error(f"投手類型評估失敗: {e}")
        
        return profile
    
    async def _analyze_team_bullpen(self, 
                                  team: str, 
                                  opposing_team: str, 
                                  target_date: date) -> Dict:
        """分析球隊牛棚表現 - 包含三層救援投手架構"""
        
        bullpen_analysis = {
            'team': team,
            'opposing_team': opposing_team,
            'overall_bullpen_era': 4.20,  # MLB平均
            'bullpen_whip': 1.35,
            'bullpen_tier': 'average',
            
            # 🔥 三層牛棚架構分析
            'middle_relievers': {
                'era': 4.00,
                'reliability': 0.65,
                'innings_coverage': '7th',
                'usage_rate': 0.70  # 70%的比賽會用到中繼
            },
            'setup_man': {
                'era': 3.50,
                'reliability': 0.75,
                'innings_coverage': '8th', 
                'usage_rate': 0.80  # 80%的比賽會用到setup man
            },
            'closer': {
                'era': 3.20,
                'save_rate': 0.85,
                'reliability': 0.85,
                'innings_coverage': '9th',
                'usage_rate': 0.90  # 90%的比賽會用到closer
            },
            
            'vs_opposing_team_era': 4.20,
            'bullpen_depth_score': 0.70  # 牛棚深度評分
        }
        
        try:
            # 獲取詳細牛棚統計 (分角色分析)
            detailed_bullpen_stats = await self._get_detailed_bullpen_stats(team, target_date)
            
            if detailed_bullpen_stats:
                # 更新三層架構數據
                bullpen_analysis['middle_relievers'].update({
                    'era': detailed_bullpen_stats.get('middle_relief_era', 4.00),
                    'reliability': detailed_bullpen_stats.get('middle_relief_reliability', 0.65)
                })
                
                bullpen_analysis['setup_man'].update({
                    'era': detailed_bullpen_stats.get('setup_era', 3.50),
                    'reliability': detailed_bullpen_stats.get('setup_reliability', 0.75)
                })
                
                bullpen_analysis['closer'].update({
                    'era': detailed_bullpen_stats.get('closer_era', 3.20),
                    'save_rate': detailed_bullpen_stats.get('save_percentage', 0.85),
                    'reliability': detailed_bullpen_stats.get('closer_reliability', 0.85)
                })
                
                # 計算整體牛棚ERA (加權平均)
                overall_era = (
                    bullpen_analysis['middle_relievers']['era'] * 1.0 +  # 1局
                    bullpen_analysis['setup_man']['era'] * 1.0 +          # 1局
                    bullpen_analysis['closer']['era'] * 1.0                # 1局
                ) / 3.0
                
                bullpen_analysis['overall_bullpen_era'] = overall_era
                bullpen_analysis['games_analyzed'] = detailed_bullpen_stats.get('games_count', 0)
                
                # 判斷牛棚等級 (基於三層架構的平均表現)
                if overall_era <= 3.20:
                    bullpen_analysis['bullpen_tier'] = 'elite'
                elif overall_era <= 3.80:
                    bullpen_analysis['bullpen_tier'] = 'good'
                elif overall_era <= 4.40:
                    bullpen_analysis['bullpen_tier'] = 'average'
                else:
                    bullpen_analysis['bullpen_tier'] = 'poor'
            
            # 分析三層牛棚對該對手的表現
            vs_team_performance = await self._analyze_layered_bullpen_vs_team(
                team, opposing_team, target_date
            )
            
            if vs_team_performance:
                # 更新各層對該對手的表現
                bullpen_analysis['middle_relievers']['vs_team_era'] = vs_team_performance.get('middle_relief_vs_era', bullpen_analysis['middle_relievers']['era'])
                bullpen_analysis['setup_man']['vs_team_era'] = vs_team_performance.get('setup_vs_era', bullpen_analysis['setup_man']['era'])
                bullpen_analysis['closer']['vs_team_era'] = vs_team_performance.get('closer_vs_era', bullpen_analysis['closer']['era'])
                
                # 計算整體對戰ERA
                bullpen_analysis['vs_opposing_team_era'] = (
                    bullpen_analysis['middle_relievers']['vs_team_era'] * 1.0 +
                    bullpen_analysis['setup_man']['vs_team_era'] * 1.0 +
                    bullpen_analysis['closer']['vs_team_era'] * 1.0
                ) / 3.0
                
                bullpen_analysis['vs_team_sample_size'] = vs_team_performance.get('total_games', 0)
            
            logger.info(f"🎯 三層牛棚分析: {team} = {bullpen_analysis['bullpen_tier']} (整體ERA {bullpen_analysis['overall_bullpen_era']:.2f})")
            logger.info(f"   中繼: ERA {bullpen_analysis['middle_relievers']['era']:.2f}")
            logger.info(f"   Setup: ERA {bullpen_analysis['setup_man']['era']:.2f}")
            logger.info(f"   Closer: ERA {bullpen_analysis['closer']['era']:.2f} (救援成功率 {bullpen_analysis['closer']['save_rate']:.1%})")
            
        except Exception as e:
            logger.error(f"牛棚分析失敗: {e}")
            bullpen_analysis['error'] = str(e)
        
        return bullpen_analysis
    
    async def _get_detailed_bullpen_stats(self, team: str, target_date: date) -> Dict:
        """獲取詳細的三層牛棚統計數據"""
        
        try:
            team_id = self._get_team_id(team)
            
            # 查詢所有救援投手 (非先發投手)
            relief_pitchers = db.session.query(PlayerStats)\
                .filter(
                    PlayerStats.team_id == team_id,
                    PlayerStats.season >= target_date.year - 1,
                    PlayerStats.innings_pitched > 5,  # 最少投球局數
                    PlayerStats.games_started == 0    # 非先發投手
                )\
                .all()
            
            if not relief_pitchers:
                return {}
            
            # 🔥 分類救援投手角色
            closers = []      # 終結者: saves >= 10
            setup_men = []    # Setup man: holds >= 5 且 saves < 10
            middle_relief = [] # 中繼: 其他救援投手
            
            for pitcher in relief_pitchers:
                saves = pitcher.saves or 0
                holds = pitcher.holds or 0
                
                if saves >= 10:  # 終結者標準
                    closers.append(pitcher)
                elif holds >= 5 and saves < 10:  # Setup man標準
                    setup_men.append(pitcher)
                else:  # 中繼投手
                    middle_relief.append(pitcher)
            
            # 計算各層統計
            def calculate_layer_stats(pitchers, layer_name):
                if not pitchers:
                    return {'era': 4.50, 'reliability': 0.50, 'count': 0}
                
                total_innings = sum(p.innings_pitched for p in pitchers)
                total_earned_runs = sum(p.earned_runs for p in pitchers)
                
                era = (total_earned_runs / total_innings * 9) if total_innings > 0 else 4.50
                reliability = min(0.95, max(0.30, 1.0 - (era - 2.0) / 4.0))  # ERA越低，可靠性越高
                
                return {
                    'era': era,
                    'reliability': reliability,
                    'count': len(pitchers),
                    'total_innings': total_innings
                }
            
            closer_stats = calculate_layer_stats(closers, 'closer')
            setup_stats = calculate_layer_stats(setup_men, 'setup')
            middle_stats = calculate_layer_stats(middle_relief, 'middle')
            
            # 計算救援成功率
            total_saves = sum(p.saves or 0 for p in closers)
            total_blown_saves = sum(p.blown_saves or 0 for p in closers)
            save_percentage = (total_saves / (total_saves + total_blown_saves)) if (total_saves + total_blown_saves) > 0 else 0.85
            
            return {
                'closer_era': closer_stats['era'],
                'closer_reliability': closer_stats['reliability'],
                'closer_count': closer_stats['count'],
                'setup_era': setup_stats['era'],
                'setup_reliability': setup_stats['reliability'], 
                'setup_count': setup_stats['count'],
                'middle_relief_era': middle_stats['era'],
                'middle_relief_reliability': middle_stats['reliability'],
                'middle_relief_count': middle_stats['count'],
                'save_percentage': save_percentage,
                'total_relief_pitchers': len(relief_pitchers),
                'games_count': sum(p.games_played or 0 for p in relief_pitchers)
            }
            
        except Exception as e:
            logger.error(f"獲取牛棚統計失敗: {e}")
            return {}
    
    async def _analyze_layered_bullpen_vs_team(self, 
                                             team: str, 
                                             opposing_team: str, 
                                             target_date: date) -> Dict:
        """分析三層牛棚對特定球隊的表現"""
        
        try:
            # 查詢救援投手對該對手的歷史表現
            # 由於牛棚對戰數據複雜性，這裡使用智能推算
            
            # 獲取兩隊近期對戰記錄
            from models.database import Game
            recent_matchups = Game.query.filter(
                and_(
                    or_(
                        and_(Game.home_team == team, Game.away_team == opposing_team),
                        and_(Game.home_team == opposing_team, Game.away_team == team)
                    ),
                    Game.date >= date(target_date.year - 2, 1, 1),  # 近2年
                    Game.date < target_date,
                    Game.home_score.isnot(None)
                )
            ).limit(15).all()
            
            if not recent_matchups:
                # 沒有歷史數據時返回平均值
                return {
                    'middle_relief_vs_era': 4.00,
                    'setup_vs_era': 3.50, 
                    'closer_vs_era': 3.20,
                    'total_games': 0,
                    'note': 'No recent matchup data available'
                }
            
            # 基於對戰歷史推算各層表現
            total_games = len(recent_matchups)
            
            # 分析對戰中的得分模式 (簡化邏輯)
            late_inning_runs = 0  # 7-9局得分 (牛棚負責)
            total_late_innings = total_games * 3  # 每場3局 (7,8,9)
            
            for game in recent_matchups:
                # 假設後3局平均得分 (簡化計算)
                if game.home_team == opposing_team:
                    opponent_score = game.home_score
                else:
                    opponent_score = game.away_score
                
                # 估計後3局得分 (約佔總得分的1/3)
                late_inning_runs += opponent_score / 3
            
            # 計算對戰ERA (9局制)
            vs_era = (late_inning_runs / total_late_innings * 9) if total_late_innings > 0 else 4.20
            
            # 分配給各層 (考慮投手素質差異)
            middle_relief_vs_era = vs_era * 1.1   # 中繼投手相對較弱
            setup_vs_era = vs_era * 0.95          # Setup man較強
            closer_vs_era = vs_era * 0.90         # 終結者最強
            
            return {
                'middle_relief_vs_era': round(middle_relief_vs_era, 2),
                'setup_vs_era': round(setup_vs_era, 2),
                'closer_vs_era': round(closer_vs_era, 2),
                'total_games': total_games,
                'calculated_from': 'recent_matchup_analysis',
                'late_inning_era': round(vs_era, 2)
            }
            
        except Exception as e:
            logger.error(f"分層牛棚對戰分析失敗: {e}")
            return {
                'middle_relief_vs_era': 4.00,
                'setup_vs_era': 3.50,
                'closer_vs_era': 3.20,
                'total_games': 0,
                'error': str(e)
            }
    
    def _calculate_innings_distribution(self, 
                                      starter_profile: Dict, 
                                      bullpen_analysis: Dict) -> Dict:
        """計算局數分配"""
        
        expected_starter_innings = starter_profile.get('expected_innings', 6.0)
        early_exit_risk = starter_profile.get('early_exit_risk', 0.20)
        
        # 考慮提前退場風險
        if early_exit_risk > 0.25:
            expected_starter_innings *= 0.9  # 高風險投手預期局數減少
        
        # 確保局數在合理範圍
        expected_starter_innings = max(4.0, min(8.0, expected_starter_innings))
        expected_bullpen_innings = 9.0 - expected_starter_innings
        
        return {
            'starter_innings': round(expected_starter_innings, 1),
            'bullpen_innings': round(expected_bullpen_innings, 1),
            'starter_percentage': expected_starter_innings / 9.0,
            'bullpen_percentage': expected_bullpen_innings / 9.0,
            'quality_start_probability': starter_profile.get('quality_start_probability', 0.50),
            'early_exit_risk': early_exit_risk
        }
    
    def _calculate_combined_pitching_prediction(self, 
                                              starter_analysis: Dict,
                                              bullpen_analysis: Dict,
                                              innings_breakdown: Dict) -> Dict:
        """計算綜合投手預測 (先發 + 三層牛棚)"""
        
        try:
            # 🔥 先發投手預期失分
            starter_predicted = starter_analysis.get('predicted_impact', {})
            starter_runs_per_game = starter_predicted.get('expected_runs_allowed', 4.5)
            starter_innings = innings_breakdown.get('starter_innings', 6.0)
            
            # 將先發投手失分按局數比例分配
            starter_runs_per_inning = starter_runs_per_game / starter_innings if starter_innings > 0 else 0.75
            actual_starter_contribution = starter_runs_per_inning * starter_innings
            
            # 🎯 三層牛棚預期失分計算
            bullpen_innings = innings_breakdown.get('bullpen_innings', 3.0)
            
            # 中繼投手 (通常投第7局)
            middle_relief = bullpen_analysis.get('middle_relievers', {})
            middle_era = middle_relief.get('vs_team_era', middle_relief.get('era', 4.00))
            middle_innings = min(1.0, bullpen_innings)  # 最多1局
            middle_runs = (middle_era / 9.0) * middle_innings
            
            # Setup man (通常投第8局)  
            setup_man = bullpen_analysis.get('setup_man', {})
            setup_era = setup_man.get('vs_team_era', setup_man.get('era', 3.50))
            setup_innings = min(1.0, max(0, bullpen_innings - 1.0))  # 第二局
            setup_runs = (setup_era / 9.0) * setup_innings
            
            # 終結者 (通常投第9局)
            closer = bullpen_analysis.get('closer', {})
            closer_era = closer.get('vs_team_era', closer.get('era', 3.20))
            closer_innings = max(0, bullpen_innings - 2.0)  # 剩餘局數
            closer_runs = (closer_era / 9.0) * closer_innings
            
            # 綜合牛棚貢獻
            total_bullpen_runs = middle_runs + setup_runs + closer_runs
            
            # 🚨 考慮早退風險 (先發提前退場的連鎖效應)
            early_exit_risk = innings_breakdown.get('early_exit_risk', 0.20)
            if early_exit_risk > 0.25:
                # 高風險時，中繼投手可能需要投更多局數 (ERA通常較高)
                additional_middle_innings = 0.5 * early_exit_risk
                additional_runs = (middle_era / 9.0) * additional_middle_innings
                total_bullpen_runs += additional_runs
                logger.warning(f"早退風險調整: +{additional_runs:.2f}分 (中繼投手額外{additional_middle_innings:.1f}局)")
            
            # 最終計算
            total_expected_runs = actual_starter_contribution + total_bullpen_runs
            
            # 主場優勢調整
            if starter_analysis.get('is_home_pitcher', False):
                total_expected_runs *= 0.97  # 主場投手組合輕微優勢
            
            # 構建詳細結果
            combined_prediction = {
                'total_expected_runs_allowed': round(total_expected_runs, 2),
                
                # 各層貢獻明細
                'starter_contribution': round(actual_starter_contribution, 2),
                'bullpen_contribution': round(total_bullpen_runs, 2),
                
                # 三層牛棚明細
                'bullpen_breakdown': {
                    'middle_relief': {
                        'innings': round(middle_innings, 1),
                        'era': round(middle_era, 2),
                        'expected_runs': round(middle_runs, 2)
                    },
                    'setup_man': {
                        'innings': round(setup_innings, 1),
                        'era': round(setup_era, 2),
                        'expected_runs': round(setup_runs, 2)
                    },
                    'closer': {
                        'innings': round(closer_innings, 1),
                        'era': round(closer_era, 2),
                        'expected_runs': round(closer_runs, 2)
                    }
                },
                
                'innings_breakdown': innings_breakdown,
                'risk_factors': {
                    'early_exit_risk': early_exit_risk,
                    'middle_relief_reliability': middle_relief.get('reliability', 0.65),
                    'setup_reliability': setup_man.get('reliability', 0.75),
                    'closer_reliability': closer.get('reliability', 0.85)
                },
                'prediction_method': 'starter_plus_layered_bullpen_analysis'
            }
            
            logger.info(f"🎯 綜合投手預測 (三層): 先發{actual_starter_contribution:.1f} + 中繼{middle_runs:.1f} + Setup{setup_runs:.1f} + 終結{closer_runs:.1f} = 總失分{total_expected_runs:.1f}")
            
            return combined_prediction
            
        except Exception as e:
            logger.error(f"綜合預測計算失敗: {e}")
            return {
                'total_expected_runs_allowed': 4.5,
                'error': str(e),
                'prediction_method': 'fallback'
            }
    
    def _assess_combined_confidence(self, 
                                  starter_analysis: Dict,
                                  bullpen_analysis: Dict,
                                  innings_breakdown: Dict) -> Dict:
        """評估綜合信心度"""
        
        confidence_factors = []
        
        # 先發投手分析信心度
        starter_confidence = starter_analysis.get('confidence_level', 'low')
        starter_games = starter_analysis.get('performance_vs_team', {}).get('games_pitched', 0)
        
        if starter_confidence == 'high' and starter_games >= 5:
            confidence_factors.append(0.8)
        elif starter_confidence == 'medium' and starter_games >= 3:
            confidence_factors.append(0.6)
        else:
            confidence_factors.append(0.4)
        
        # 牛棚分析信心度
        bullpen_games = bullpen_analysis.get('games_analyzed', 0)
        if bullpen_games >= 20:
            confidence_factors.append(0.7)
        elif bullpen_games >= 10:
            confidence_factors.append(0.5)
        else:
            confidence_factors.append(0.3)
        
        # 局數預測可靠性
        quality_start_prob = innings_breakdown.get('quality_start_probability', 0.50)
        if quality_start_prob >= 0.65:
            confidence_factors.append(0.7)
        else:
            confidence_factors.append(0.5)
        
        overall_confidence = np.mean(confidence_factors)
        
        if overall_confidence >= 0.7:
            confidence_level = 'high'
        elif overall_confidence >= 0.5:
            confidence_level = 'medium'
        else:
            confidence_level = 'low'
        
        return {
            'overall_confidence': round(overall_confidence, 3),
            'confidence_level': confidence_level,
            'confidence_factors': {
                'starter_analysis': confidence_factors[0],
                'bullpen_analysis': confidence_factors[1],
                'innings_prediction': confidence_factors[2]
            }
        }
    
    def _get_team_id(self, team_code: str) -> int:
        """獲取球隊ID"""
        team_mapping = {
            'BOS': 111, 'NYY': 147, 'TB': 139, 'TOR': 141, 'BAL': 110,
            'CWS': 145, 'CLE': 114, 'DET': 116, 'KC': 118, 'MIN': 142,
            'HOU': 117, 'LAA': 108, 'OAK': 133, 'SEA': 136, 'TEX': 140,
            'ATL': 144, 'NYM': 121, 'PHI': 143, 'WSH': 120, 'MIA': 146,
            'CHC': 112, 'MIL': 158, 'STL': 138, 'PIT': 134, 'CIN': 113,
            'LAD': 119, 'SD': 135, 'SF': 137, 'COL': 115, 'AZ': 109
        }
        return team_mapping.get(team_code, 1)

# 使用示例
if __name__ == "__main__":
    import asyncio
    
    async def test_pitcher_bullpen_analysis():
        analyzer = PitcherBullpenAnalyzer()
        
        result = await analyzer.analyze_complete_pitching_staff(
            starting_pitcher="Gerrit Cole",
            team="NYY",
            opposing_team="BOS", 
            target_date=date(2025, 8, 23),
            is_home=True
        )
        
        print("完整投手陣容分析:")
        print(f"先發預期局數: {result['innings_breakdown']['starter_innings']}")
        print(f"牛棚預期局數: {result['innings_breakdown']['bullpen_innings']}")
        print(f"總預期失分: {result['combined_prediction']['total_expected_runs_allowed']}")
        
    asyncio.run(test_pitcher_bullpen_analysis())