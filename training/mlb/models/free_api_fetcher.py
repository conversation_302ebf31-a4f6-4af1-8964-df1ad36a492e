"""
免費 API 數據獲取器
整合 ESPN API、MLB Stats API 等免費數據源
當付費 API 不可用時提供備用數據
"""

import logging
import requests
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class FreeAPIFetcher:
    """免費 API 數據獲取器"""
    
    def __init__(self):
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """創建 HTTP 會話"""
        session = requests.Session()
        session.timeout = 30
        session.headers.update({
            'User-Agent': 'MLB-Prediction-System/1.0'
        })
        return session
    
    def get_espn_mlb_data(self, target_date: date = None) -> Dict[str, Any]:
        """從 ESPN API 獲取 MLB 數據"""
        try:
            if not target_date:
                target_date = date.today()
            
            # ESPN API 端點
            url = "https://site.api.espn.com/apis/site/v2/sports/baseball/mlb/scoreboard"
            
            # 如果指定日期，添加日期參數
            params = {}
            if target_date != date.today():
                params['dates'] = target_date.strftime('%Y%m%d')
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # 轉換為標準格式
            games = []
            events = data.get('events', [])
            
            for event in events:
                game_info = self._parse_espn_game(event)
                if game_info:
                    games.append(game_info)
            
            return {
                "success": True,
                "source": "ESPN API",
                "games": games,
                "total_games": len(games),
                "timestamp": datetime.now().isoformat(),
                "date": target_date.isoformat()
            }
            
        except Exception as e:
            logger.error(f"ESPN API 獲取失敗: {e}")
            return {
                "success": False,
                "error": f"ESPN API 獲取失敗: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _parse_espn_game(self, event: Dict) -> Optional[Dict]:
        """解析 ESPN 比賽數據"""
        try:
            competitions = event.get('competitions', [])
            if not competitions:
                return None
            
            competition = competitions[0]
            competitors = competition.get('competitors', [])
            
            if len(competitors) < 2:
                return None
            
            # 找出主隊和客隊
            home_team = None
            away_team = None
            
            for competitor in competitors:
                if competitor.get('homeAway') == 'home':
                    home_team = competitor
                elif competitor.get('homeAway') == 'away':
                    away_team = competitor
            
            if not home_team or not away_team:
                return None
            
            # 提取比賽信息
            game_info = {
                "id": event.get('id'),
                "date": event.get('date'),
                "status": competition.get('status', {}).get('type', {}).get('description', 'Unknown'),
                "home_team": {
                    "name": home_team.get('team', {}).get('displayName', ''),
                    "abbreviation": home_team.get('team', {}).get('abbreviation', ''),
                    "score": home_team.get('score', '0'),
                    "record": home_team.get('records', [{}])[0].get('summary', '') if home_team.get('records') else ''
                },
                "away_team": {
                    "name": away_team.get('team', {}).get('displayName', ''),
                    "abbreviation": away_team.get('team', {}).get('abbreviation', ''),
                    "score": away_team.get('score', '0'),
                    "record": away_team.get('records', [{}])[0].get('summary', '') if away_team.get('records') else ''
                },
                "venue": competition.get('venue', {}).get('fullName', ''),
                "weather": self._extract_weather(competition),
                "odds": self._extract_odds(competition)
            }
            
            return game_info
            
        except Exception as e:
            logger.warning(f"解析 ESPN 比賽數據失敗: {e}")
            return None
    
    def _extract_weather(self, competition: Dict) -> Optional[Dict]:
        """提取天氣信息"""
        try:
            weather = competition.get('weather')
            if weather:
                return {
                    "temperature": weather.get('temperature'),
                    "condition": weather.get('conditionId'),
                    "description": weather.get('shortPhrase', '')
                }
        except Exception:
            pass
        return None
    
    def _extract_odds(self, competition: Dict) -> Optional[Dict]:
        """提取賠率信息（如果有）"""
        try:
            odds = competition.get('odds')
            if odds and len(odds) > 0:
                odd = odds[0]
                return {
                    "provider": odd.get('provider', {}).get('name', ''),
                    "home_odds": odd.get('homeTeamOdds', {}).get('moneyLine'),
                    "away_odds": odd.get('awayTeamOdds', {}).get('moneyLine'),
                    "over_under": odd.get('overUnder')
                }
        except Exception:
            pass
        return None
    
    def get_mlb_stats_api_data(self, target_date: date = None) -> Dict[str, Any]:
        """從 MLB Stats API 獲取數據"""
        try:
            if not target_date:
                target_date = date.today()
            
            # MLB Stats API 端點
            url = "https://statsapi.mlb.com/api/v1/schedule"
            params = {
                "sportId": 1,  # MLB
                "date": target_date.strftime('%Y-%m-%d'),
                "hydrate": "team,linescore,boxscore"
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # 轉換為標準格式
            games = []
            dates = data.get('dates', [])
            
            for date_info in dates:
                for game in date_info.get('games', []):
                    game_info = self._parse_mlb_stats_game(game)
                    if game_info:
                        games.append(game_info)
            
            return {
                "success": True,
                "source": "MLB Stats API",
                "games": games,
                "total_games": len(games),
                "timestamp": datetime.now().isoformat(),
                "date": target_date.isoformat()
            }
            
        except Exception as e:
            logger.error(f"MLB Stats API 獲取失敗: {e}")
            return {
                "success": False,
                "error": f"MLB Stats API 獲取失敗: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _parse_mlb_stats_game(self, game: Dict) -> Optional[Dict]:
        """解析 MLB Stats API 比賽數據"""
        try:
            teams = game.get('teams', {})
            home_team = teams.get('home', {})
            away_team = teams.get('away', {})
            
            game_info = {
                "id": game.get('gamePk'),
                "date": game.get('gameDate'),
                "status": game.get('status', {}).get('detailedState', 'Unknown'),
                "home_team": {
                    "name": home_team.get('team', {}).get('name', ''),
                    "abbreviation": home_team.get('team', {}).get('abbreviation', ''),
                    "score": home_team.get('score', 0),
                    "record": f"{home_team.get('leagueRecord', {}).get('wins', 0)}-{home_team.get('leagueRecord', {}).get('losses', 0)}"
                },
                "away_team": {
                    "name": away_team.get('team', {}).get('name', ''),
                    "abbreviation": away_team.get('team', {}).get('abbreviation', ''),
                    "score": away_team.get('score', 0),
                    "record": f"{away_team.get('leagueRecord', {}).get('wins', 0)}-{away_team.get('leagueRecord', {}).get('losses', 0)}"
                },
                "venue": game.get('venue', {}).get('name', ''),
                "inning": game.get('linescore', {}).get('currentInning'),
                "inning_state": game.get('linescore', {}).get('inningState'),
                "probable_pitchers": self._extract_probable_pitchers(game)
            }
            
            return game_info
            
        except Exception as e:
            logger.warning(f"解析 MLB Stats API 比賽數據失敗: {e}")
            return None
    
    def _extract_probable_pitchers(self, game: Dict) -> Dict:
        """提取可能的先發投手"""
        try:
            teams = game.get('teams', {})
            home_pitcher = teams.get('home', {}).get('probablePitcher')
            away_pitcher = teams.get('away', {}).get('probablePitcher')
            
            result = {}
            
            if home_pitcher:
                result['home'] = {
                    "name": home_pitcher.get('fullName', ''),
                    "id": home_pitcher.get('id'),
                    "era": home_pitcher.get('stats', [{}])[0].get('era') if home_pitcher.get('stats') else None
                }
            
            if away_pitcher:
                result['away'] = {
                    "name": away_pitcher.get('fullName', ''),
                    "id": away_pitcher.get('id'),
                    "era": away_pitcher.get('stats', [{}])[0].get('era') if away_pitcher.get('stats') else None
                }
            
            return result
            
        except Exception:
            return {}
    
    def get_combined_free_data(self, target_date: date = None) -> Dict[str, Any]:
        """獲取合併的免費數據"""
        if not target_date:
            target_date = date.today()
        
        logger.info(f"獲取 {target_date} 的免費 MLB 數據...")
        
        results = {
            "success": False,
            "sources": [],
            "games": [],
            "timestamp": datetime.now().isoformat(),
            "date": target_date.isoformat()
        }
        
        # 嘗試 ESPN API
        espn_data = self.get_espn_mlb_data(target_date)
        if espn_data.get('success'):
            results["sources"].append("ESPN API")
            results["games"].extend(espn_data.get('games', []))
            results["success"] = True
            logger.info(f"ESPN API 成功獲取 {len(espn_data.get('games', []))} 場比賽")
        
        # 嘗試 MLB Stats API
        mlb_data = self.get_mlb_stats_api_data(target_date)
        if mlb_data.get('success'):
            results["sources"].append("MLB Stats API")
            # 合併數據，避免重複
            mlb_games = mlb_data.get('games', [])
            results["games"] = self._merge_game_data(results["games"], mlb_games)
            results["success"] = True
            logger.info(f"MLB Stats API 成功獲取 {len(mlb_games)} 場比賽")
        
        results["total_games"] = len(results["games"])
        
        if results["success"]:
            logger.info(f"免費 API 總共獲取 {results['total_games']} 場比賽，數據源: {', '.join(results['sources'])}")
        else:
            logger.error("所有免費 API 都失敗")
            results["error"] = "所有免費 API 都失敗"
        
        return results
    
    def _merge_game_data(self, games1: List[Dict], games2: List[Dict]) -> List[Dict]:
        """合併比賽數據，避免重複"""
        merged = games1.copy()
        
        # 創建已有比賽的索引
        existing_games = set()
        for game in games1:
            # 使用隊伍名稱作為唯一標識
            home_team = game.get('home_team', {}).get('abbreviation', '')
            away_team = game.get('away_team', {}).get('abbreviation', '')
            if home_team and away_team:
                existing_games.add(f"{away_team}@{home_team}")
        
        # 添加新比賽
        for game in games2:
            home_team = game.get('home_team', {}).get('abbreviation', '')
            away_team = game.get('away_team', {}).get('abbreviation', '')
            game_key = f"{away_team}@{home_team}"
            
            if game_key not in existing_games:
                merged.append(game)
                existing_games.add(game_key)
        
        return merged
    
    def get_status_report(self) -> Dict[str, Any]:
        """獲取免費 API 狀態報告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "apis": []
        }
        
        # 測試 ESPN API
        try:
            espn_result = self.get_espn_mlb_data()
            report["apis"].append({
                "name": "ESPN API",
                "status": "可用" if espn_result.get('success') else "不可用",
                "games_count": len(espn_result.get('games', [])),
                "error": espn_result.get('error') if not espn_result.get('success') else None
            })
        except Exception as e:
            report["apis"].append({
                "name": "ESPN API",
                "status": "錯誤",
                "error": str(e)
            })
        
        # 測試 MLB Stats API
        try:
            mlb_result = self.get_mlb_stats_api_data()
            report["apis"].append({
                "name": "MLB Stats API",
                "status": "可用" if mlb_result.get('success') else "不可用",
                "games_count": len(mlb_result.get('games', [])),
                "error": mlb_result.get('error') if not mlb_result.get('success') else None
            })
        except Exception as e:
            report["apis"].append({
                "name": "MLB Stats API",
                "status": "錯誤",
                "error": str(e)
            })
        
        return report
