"""
預測準備狀況檢查器
檢查比賽預測前的先發陣容確認狀況
"""

import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional
from models.database import db, Game, GameDetail, Team
from models.daily_lineup_fetcher import DailyLineupFetcher
from models.daily_pitcher_announcements import DailyPitcherAnnouncementChecker
import requests

logger = logging.getLogger(__name__)

class PredictionReadinessChecker:
    """預測準備狀況檢查器"""
    
    def __init__(self):
        self.lineup_fetcher = DailyLineupFetcher()
        self.announcement_checker = DailyPitcherAnnouncementChecker()
        self.base_url = "https://statsapi.mlb.com/api/v1"
    
    def check_prediction_readiness(self, target_date: date = None) -> Dict:
        """檢查指定日期的預測準備狀況"""
        if target_date is None:
            target_date = date.today()
        
        logger.info(f"檢查 {target_date} 的預測準備狀況...")
        
        # 1. 獲取當日比賽和先發陣容信息
        daily_lineups = self.lineup_fetcher.get_daily_lineups(target_date)
        
        # 2. 分析每場比賽的準備狀況
        games_analysis = []
        ready_count = 0
        partial_ready_count = 0
        not_ready_count = 0
        
        for game in daily_lineups['games']:
            game_readiness = self._analyze_game_readiness(game)
            games_analysis.append(game_readiness)
            
            if game_readiness['readiness_score'] >= 80:
                ready_count += 1
            elif game_readiness['readiness_score'] >= 40:
                partial_ready_count += 1
            else:
                not_ready_count += 1
        
        # 3. 計算整體準備狀況
        total_games = len(games_analysis)
        overall_readiness = (ready_count / total_games * 100) if total_games > 0 else 0
        
        readiness_status = {
            'date': target_date.strftime('%Y-%m-%d'),
            'checked_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_games': total_games,
            'ready_games': ready_count,
            'partial_ready_games': partial_ready_count,
            'not_ready_games': not_ready_count,
            'overall_readiness_percentage': round(overall_readiness, 1),
            'prediction_recommendation': self._get_prediction_recommendation(overall_readiness, ready_count, total_games),
            'games': games_analysis
        }
        
        return readiness_status
    
    def _analyze_game_readiness(self, game: Dict) -> Dict:
        """分析單場比賽的準備狀況"""
        readiness_factors = {
            'starting_pitchers': 0,      # 先發投手確認 (40分)
            'batting_lineups': 0,        # 打線確認 (30分)
            'game_status': 0,            # 比賽狀態 (20分)
            'timing': 0                  # 時間因素 (10分)
        }
        
        # 1. 先發投手確認 (40分)
        pitcher_score = 0
        if game.get('home_pitcher'):
            pitcher_score += 20
        if game.get('away_pitcher'):
            pitcher_score += 20
        readiness_factors['starting_pitchers'] = pitcher_score
        
        # 2. 打線確認 (30分)
        lineup_score = 0
        if game.get('home_lineup_confirmed'):
            lineup_score += 15
        if game.get('away_lineup_confirmed'):
            lineup_score += 15
        readiness_factors['batting_lineups'] = lineup_score
        
        # 3. 比賽狀態 (20分)
        status_score = 0
        game_status = game.get('status', '').lower()
        if game_status in ['scheduled', 'pre-game']:
            status_score = 20
        elif game_status in ['warmup', 'delayed']:
            status_score = 10
        elif game_status in ['in progress', 'live']:
            status_score = 0  # 已開始的比賽不適合預測
        readiness_factors['game_status'] = status_score
        
        # 4. 時間因素 (10分)
        timing_score = 10  # 簡化處理，實際可根據比賽開始時間調整
        readiness_factors['timing'] = timing_score
        
        # 計算總分
        total_score = sum(readiness_factors.values())
        
        # 生成建議
        suggestions = []
        if readiness_factors['starting_pitchers'] < 40:
            missing_pitchers = []
            if not game.get('home_pitcher'):
                missing_pitchers.append('主隊')
            if not game.get('away_pitcher'):
                missing_pitchers.append('客隊')
            suggestions.append(f"等待{'/'.join(missing_pitchers)}先發投手公告")
        
        if readiness_factors['batting_lineups'] < 30:
            missing_lineups = []
            if not game.get('home_lineup_confirmed'):
                missing_lineups.append('主隊')
            if not game.get('away_lineup_confirmed'):
                missing_lineups.append('客隊')
            suggestions.append(f"等待{'/'.join(missing_lineups)}打線公告")
        
        if readiness_factors['game_status'] < 20:
            suggestions.append("比賽狀態不適合預測")
        
        if not suggestions:
            suggestions.append("可以進行預測")
        
        return {
            'game_id': game.get('game_id'),
            'matchup': f"{game.get('away_team')} @ {game.get('home_team')}",
            'game_time': game.get('game_time', 'TBD'),
            'status': game.get('status'),
            'readiness_score': total_score,
            'readiness_level': self._get_readiness_level(total_score),
            'factors': readiness_factors,
            'starting_pitchers': {
                'home': game.get('home_pitcher'),
                'away': game.get('away_pitcher')
            },
            'lineups_confirmed': {
                'home': game.get('home_lineup_confirmed', False),
                'away': game.get('away_lineup_confirmed', False)
            },
            'suggestions': suggestions
        }
    
    def _get_readiness_level(self, score: int) -> str:
        """根據分數獲取準備程度等級"""
        if score >= 80:
            return "完全準備"
        elif score >= 60:
            return "基本準備"
        elif score >= 40:
            return "部分準備"
        else:
            return "未準備"
    
    def _get_prediction_recommendation(self, overall_readiness: float, ready_count: int, total_games: int) -> Dict:
        """獲取預測建議"""
        if overall_readiness >= 70:
            return {
                'action': 'proceed',
                'message': f'建議進行預測 ({ready_count}/{total_games} 場比賽已準備)',
                'confidence': 'high'
            }
        elif overall_readiness >= 40:
            return {
                'action': 'wait_or_partial',
                'message': f'可進行部分預測或等待更多信息 ({ready_count}/{total_games} 場比賽已準備)',
                'confidence': 'medium'
            }
        else:
            return {
                'action': 'wait',
                'message': f'建議等待先發陣容公告 ({ready_count}/{total_games} 場比賽已準備)',
                'confidence': 'low'
            }
    
    def get_lineup_update_schedule(self, target_date: date = None) -> Dict:
        """獲取陣容更新時間表"""
        if target_date is None:
            target_date = date.today()
        
        # 一般MLB先發陣容公告時間
        schedule = {
            'date': target_date.strftime('%Y-%m-%d'),
            'typical_announcement_times': [
                {
                    'time': '10:00 AM ET',
                    'description': '早場比賽先發投手通常公告時間',
                    'games_affected': '下午1:00前開始的比賽'
                },
                {
                    'time': '2:00 PM ET', 
                    'description': '晚場比賽先發投手通常公告時間',
                    'games_affected': '晚上7:00後開始的比賽'
                },
                {
                    'time': '比賽前2-3小時',
                    'description': '打線陣容通常公告時間',
                    'games_affected': '所有比賽'
                }
            ],
            'next_check_recommendation': self._get_next_check_time(),
            'auto_refresh_interval': '30分鐘'
        }
        
        return schedule
    
    def _get_next_check_time(self) -> str:
        """獲取下次檢查建議時間"""
        now = datetime.now()
        
        # 如果是早上，建議下午2點檢查
        if now.hour < 14:
            return "下午2:00 (晚場比賽投手公告時間)"
        # 如果是下午，建議晚上6點檢查
        elif now.hour < 18:
            return "晚上6:00 (打線陣容公告時間)"
        # 如果是晚上，建議明天早上檢查
        else:
            return "明天上午10:00"
    
    def check_specific_game_readiness(self, game_id: str) -> Dict:
        """檢查特定比賽的準備狀況"""
        try:
            # 從MLB API獲取比賽詳細信息
            url = f"{self.base_url}/schedule"
            params = {
                'gamePk': game_id,
                'hydrate': 'probablePitchers,lineups'
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'dates' in data and data['dates']:
                    games = data['dates'][0].get('games', [])
                    if games:
                        game_data = games[0]
                        
                        # 提取先發投手信息
                        teams = game_data.get('teams', {})
                        home_pitcher = teams.get('home', {}).get('probablePitcher', {}).get('fullName')
                        away_pitcher = teams.get('away', {}).get('probablePitcher', {}).get('fullName')
                        
                        # 提取打線信息
                        lineups = game_data.get('lineups', {})
                        home_lineup = lineups.get('home')
                        away_lineup = lineups.get('away')
                        
                        game_info = {
                            'game_id': game_id,
                            'away_team': teams.get('away', {}).get('team', {}).get('abbreviation'),
                            'home_team': teams.get('home', {}).get('team', {}).get('abbreviation'),
                            'status': game_data.get('status', {}).get('detailedState'),
                            'game_time': game_data.get('gameDate'),
                            'home_pitcher': home_pitcher,
                            'away_pitcher': away_pitcher,
                            'home_lineup_confirmed': bool(home_lineup),
                            'away_lineup_confirmed': bool(away_lineup)
                        }
                        
                        return self._analyze_game_readiness(game_info)
            
            return {'error': '無法獲取比賽信息'}
            
        except Exception as e:
            logger.error(f"檢查比賽 {game_id} 準備狀況失敗: {e}")
            return {'error': str(e)}
