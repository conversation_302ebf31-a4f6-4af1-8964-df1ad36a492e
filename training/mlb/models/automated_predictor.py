import logging
from datetime import datetime, date, timedelta
from typing import Dict, List
import schedule
import time
import threading

from .prediction_service import PredictionService
from .model_trainer import ModelTrainer
from .data_fetcher import MLBDataFetcher
from .detailed_data_fetcher import DetailedDataFetcher

logger = logging.getLogger(__name__)

class AutomatedPredictor:
    """自動化預測系統 - 處理定時任務和自動化流程"""
    
    def __init__(self):
        self.prediction_service = PredictionService()
        self.model_trainer = ModelTrainer()
        self.data_fetcher = MLBDataFetcher()
        self.detailed_data_fetcher = DetailedDataFetcher()
        self.is_running = False
        self.scheduler_thread = None
        
    def start_scheduler(self):
        """啟動定時任務調度器"""
        if self.is_running:
            logger.warning("調度器已在運行中")
            return
        
        try:
            # 初始化預測服務
            self.prediction_service.initialize_service()
            
            # 設置定時任務
            self._setup_scheduled_jobs()
            
            # 啟動調度器線程
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            logger.info("自動化預測調度器已啟動")
            
        except Exception as e:
            logger.error(f"啟動調度器失敗: {e}")
            self.is_running = False
    
    def stop_scheduler(self):
        """停止定時任務調度器"""
        self.is_running = False
        schedule.clear()
        logger.info("自動化預測調度器已停止")
    
    def _setup_scheduled_jobs(self):
        """設置定時任務"""
        # 每日早上6點更新數據
        schedule.every().day.at("06:00").do(self._daily_data_update)
        
        # 每日早上7點生成預測
        schedule.every().day.at("07:00").do(self._daily_prediction_generation)
        
        # 每日晚上11點更新預測結果
        schedule.every().day.at("23:00").do(self._update_prediction_results)
        
        # 每週日凌晨2點重新訓練模型
        schedule.every().sunday.at("02:00").do(self._weekly_model_retrain)
        
        # 每小時檢查並生成當日比賽預測
        schedule.every().hour.do(self._hourly_prediction_check)
        
        logger.info("定時任務設置完成")
    
    def _run_scheduler(self):
        """運行調度器"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分鐘檢查一次
            except Exception as e:
                logger.error(f"調度器運行錯誤: {e}")
                time.sleep(60)
    
    def _daily_data_update(self):
        """每日數據更新任務"""
        try:
            logger.info("開始每日數據更新...")

            # 更新昨天和今天的比賽數據
            yesterday = date.today() - timedelta(days=1)
            today = date.today()

            # 1. 更新比賽行程（處理時間變更）
            logger.info("更新比賽行程...")
            self.data_fetcher.update_games_for_date(yesterday)
            self.data_fetcher.update_games_for_date(today)

            # 2. 下載昨天已完成比賽的Box Score
            logger.info("下載昨天的Box Score...")
            self.detailed_data_fetcher.fetch_completed_games_boxscores(yesterday)

            # 3. 下載今天已完成比賽的Box Score（如果有的話）
            logger.info("檢查今天的已完成比賽...")
            self.detailed_data_fetcher.fetch_completed_games_boxscores(today)

            # 4. 更新詳細比賽數據
            logger.info("更新詳細比賽數據...")
            self.detailed_data_fetcher.fetch_games_by_date(yesterday)

            # 5. 更新球隊統計（每週一次）
            if date.today().weekday() == 0:  # 週一
                logger.info("每週球隊統計更新...")
                self.data_fetcher.update_team_stats()

            logger.info("每日數據更新完成")

        except Exception as e:
            logger.error(f"每日數據更新失敗: {e}")

    def manual_daily_update(self, target_date: date = None) -> Dict:
        """手動每日更新"""
        try:
            if target_date is None:
                target_date = date.today()

            logger.info(f"手動執行 {target_date} 的每日更新...")

            # 1. 更新比賽行程
            schedule_result = self.data_fetcher.update_games_for_date(target_date)

            # 2. 下載Box Score
            boxscore_result = self.detailed_data_fetcher.fetch_completed_games_boxscores(target_date)

            # 3. 更新詳細數據
            detailed_result = self.detailed_data_fetcher.fetch_games_by_date(target_date)

            return {
                'date': target_date.isoformat(),
                'schedule_updated': schedule_result,
                'boxscores_downloaded': boxscore_result,
                'detailed_data_updated': detailed_result,
                'status': 'success'
            }

        except Exception as e:
            logger.error(f"手動每日更新失敗: {e}")
            return {'error': str(e)}
    
    def _daily_prediction_generation(self):
        """每日預測生成任務"""
        try:
            logger.info("開始生成每日預測...")
            
            # 生成今天的預測
            today_result = self.prediction_service.generate_daily_predictions(date.today())
            
            # 生成明天的預測
            tomorrow = date.today() + timedelta(days=1)
            tomorrow_result = self.prediction_service.generate_daily_predictions(tomorrow)
            
            logger.info(f"每日預測生成完成 - 今天: {today_result.get('successful_predictions', 0)} 場, "
                       f"明天: {tomorrow_result.get('successful_predictions', 0)} 場")
            
        except Exception as e:
            logger.error(f"每日預測生成失敗: {e}")
    
    def _update_prediction_results(self):
        """更新預測結果任務"""
        try:
            logger.info("開始更新預測結果...")
            
            result = self.prediction_service.update_prediction_results(days_back=3)
            
            logger.info(f"預測結果更新完成 - 更新了 {result.get('updated_predictions', 0)} 個預測")
            
        except Exception as e:
            logger.error(f"更新預測結果失敗: {e}")
    
    def _weekly_model_retrain(self):
        """每週模型重新訓練任務"""
        try:
            logger.info("開始每週模型重新訓練...")
            
            # 使用最近60天的數據重新訓練
            training_result = self.model_trainer.quick_retrain(days_back=60)
            
            if training_result:
                logger.info("每週模型重新訓練完成")
                
                # 驗證新模型
                validation_result = self.model_trainer.validate_on_recent_games(days=7)
                logger.info(f"模型驗證結果: {validation_result.get('metrics', {})}")
            else:
                logger.error("每週模型重新訓練失敗")
            
        except Exception as e:
            logger.error(f"每週模型重新訓練失敗: {e}")
    
    def _hourly_prediction_check(self):
        """每小時預測檢查任務"""
        try:
            # 只在比賽日執行
            today = date.today()
            
            # 檢查今天是否有比賽
            from .database import Game
            today_games = Game.query.filter(
                Game.date == today,
                Game.game_status.in_(['scheduled', 'pre-game'])
            ).count()
            
            if today_games > 0:
                logger.info(f"檢查今日比賽預測 - 發現 {today_games} 場比賽")
                
                # 生成或更新今日預測
                result = self.prediction_service.generate_daily_predictions(today)
                
                if result.get('successful_predictions', 0) > 0:
                    logger.info(f"每小時檢查完成 - 處理了 {result['successful_predictions']} 場比賽")
            
        except Exception as e:
            logger.error(f"每小時預測檢查失敗: {e}")
    
    def manual_generate_predictions(self, target_date: date = None) -> Dict:
        """手動生成預測"""
        try:
            if target_date is None:
                target_date = date.today()
            
            logger.info(f"手動生成 {target_date} 的預測...")
            
            result = self.prediction_service.generate_daily_predictions(target_date)
            
            logger.info(f"手動預測生成完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"手動生成預測失敗: {e}")
            return {'error': str(e)}
    
    def manual_retrain_models(self, days_back: int = 60) -> Dict:
        """手動重新訓練模型"""
        try:
            logger.info(f"手動重新訓練模型 - 使用最近 {days_back} 天數據...")
            
            training_result = self.model_trainer.quick_retrain(days_back=days_back)
            
            if training_result:
                # 重新初始化預測服務
                self.prediction_service.initialize_service()
                
                logger.info("手動模型重新訓練完成")
                return training_result
            else:
                logger.error("手動模型重新訓練失敗")
                return {'error': '訓練失敗'}
            
        except Exception as e:
            logger.error(f"手動重新訓練模型失敗: {e}")
            return {'error': str(e)}
    
    def get_system_status(self) -> Dict:
        """獲取系統狀態"""
        try:
            # 檢查模型狀態
            models_ready = self.prediction_service.models_loaded
            
            # 檢查最近的預測
            from .database import Prediction
            recent_predictions = Prediction.query.filter(
                Prediction.prediction_date >= datetime.utcnow() - timedelta(days=1)
            ).count()
            
            # 檢查準確率統計
            accuracy_stats = self.prediction_service.get_prediction_accuracy_stats(days=7)
            
            # 獲取訓練歷史
            training_summary = self.model_trainer.get_training_summary()
            
            return {
                'scheduler_running': self.is_running,
                'models_ready': models_ready,
                'recent_predictions_24h': recent_predictions,
                'accuracy_stats': accuracy_stats,
                'training_summary': training_summary,
                'last_check': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"獲取系統狀態失敗: {e}")
            return {'error': str(e)}
    
    def force_data_update(self, days_back: int = 3) -> Dict:
        """強制數據更新"""
        try:
            logger.info(f"強制更新最近 {days_back} 天的數據...")
            
            updated_dates = []
            for i in range(days_back):
                target_date = date.today() - timedelta(days=i)
                self.data_fetcher.update_games_for_date(target_date)
                updated_dates.append(target_date.isoformat())
            
            # 更新球隊統計
            self.data_fetcher.update_team_stats()
            
            logger.info(f"強制數據更新完成: {updated_dates}")
            
            return {
                'updated_dates': updated_dates,
                'team_stats_updated': True,
                'status': 'success'
            }

        except Exception as e:
            logger.error(f"強制數據更新失敗: {e}")
            return {'error': str(e)}

    def fetch_detailed_data_for_date(self, target_date: date) -> Dict:
        """獲取指定日期的詳細比賽數據"""
        try:
            logger.info(f"獲取 {target_date} 的詳細比賽數據...")

            success = self.detailed_data_fetcher.fetch_games_by_date(target_date)

            if success:
                logger.info(f"成功獲取 {target_date} 的詳細比賽數據")
                return {
                    'date': target_date.isoformat(),
                    'status': 'success',
                    'message': '詳細數據獲取完成'
                }
            else:
                return {
                    'date': target_date.isoformat(),
                    'status': 'failed',
                    'message': '詳細數據獲取失敗'
                }

        except Exception as e:
            logger.error(f"獲取詳細數據失敗 {target_date}: {e}")
            return {'error': str(e)}

    def batch_fetch_detailed_data(self, start_date: date, end_date: date) -> Dict:
        """批量獲取詳細比賽數據"""
        try:
            logger.info(f"批量獲取 {start_date} 到 {end_date} 的詳細比賽數據...")

            current_date = start_date
            results = {}
            success_count = 0

            while current_date <= end_date:
                try:
                    result = self.fetch_detailed_data_for_date(current_date)
                    results[current_date.isoformat()] = result

                    if result.get('status') == 'success':
                        success_count += 1

                    # 避免請求過於頻繁
                    import time
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"批量獲取 {current_date} 失敗: {e}")
                    results[current_date.isoformat()] = {'error': str(e)}

                current_date += timedelta(days=1)

            total_days = (end_date - start_date).days + 1

            return {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'total_days': total_days,
                'success_count': success_count,
                'results': results
            }

        except Exception as e:
            logger.error(f"批量獲取詳細數據失敗: {e}")
            return {'error': str(e)}

# 全局自動化預測器實例
automated_predictor = AutomatedPredictor()
