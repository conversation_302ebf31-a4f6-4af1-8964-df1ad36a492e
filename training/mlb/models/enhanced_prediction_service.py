#!/usr/bin/env python3
"""
增強預測服務適配器
將增強算法整合到現有的預測服務架構中
"""

import joblib
import numpy as np
from typing import Dict, Any
from enhanced_prediction_algorithm import EnhancedMLBPredictor
import logging

logger = logging.getLogger(__name__)

class EnhancedPredictionService:
    """增強預測服務"""
    
    def __init__(self, model_path: str = None):
        self.enhanced_predictor = EnhancedMLBPredictor()
        self.model_path = model_path
        self.is_loaded = False
        
    def load_model(self, model_path: str = None):
        """載入增強模型"""
        try:
            if model_path:
                self.model_path = model_path
                
            if not self.model_path:
                raise ValueError("未指定模型路徑")
                
            self.enhanced_predictor.load_models(self.model_path)
            self.is_loaded = True
            logger.info(f"增強預測模型載入成功: {self.model_path}")
            return True
            
        except Exception as e:
            logger.error(f"載入增強模型失敗: {e}")
            return False
    
    def predict_game(self, game_data: Dict) -> Dict:
        """預測單場比賽"""
        if not self.is_loaded:
            raise RuntimeError("模型尚未載入")
            
        try:
            # 準備特徵數據
            features = self._prepare_features(game_data)
            
            # 使用增強算法預測
            prediction = self.enhanced_predictor.predict_enhanced(features)
            
            # 轉換為與現有系統兼容的格式
            result = self._format_prediction(prediction, game_data)
            
            logger.info(f"增強預測完成: {game_data.get('game_id', 'unknown')}")
            return result
            
        except Exception as e:
            logger.error(f"增強預測失敗: {e}")
            # 返回錯誤指示
            return {
                'error': str(e),
                'predicted_home_score': 0,
                'predicted_away_score': 0,
                'confidence': 0.0
            }
    
    def _prepare_features(self, game_data: Dict) -> Dict:
        """準備特徵數據"""
        # 從遊戲數據中提取或計算特徵
        # 這裡需要根據實際的數據結構進行調整
        
        features = {
            'home_runs_scored_avg': game_data.get('home_runs_scored_avg', 4.5),
            'home_runs_allowed_avg': game_data.get('home_runs_allowed_avg', 4.5),
            'home_batting_avg': game_data.get('home_batting_avg', 0.250),
            'home_era': game_data.get('home_era', 4.20),
            'home_win_percentage': game_data.get('home_win_percentage', 0.500),
            'home_recent_form': game_data.get('home_recent_form', 0.500),
            'away_runs_scored_avg': game_data.get('away_runs_scored_avg', 4.5),
            'away_runs_allowed_avg': game_data.get('away_runs_allowed_avg', 4.5),
            'away_batting_avg': game_data.get('away_batting_avg', 0.250),
            'away_era': game_data.get('away_era', 4.20),
            'away_win_percentage': game_data.get('away_win_percentage', 0.500),
            'away_recent_form': game_data.get('away_recent_form', 0.500)
        }
        
        return features
    
    def _format_prediction(self, enhanced_prediction: Dict, game_data: Dict) -> Dict:
        """格式化預測結果以兼容現有系統"""
        return {
            'game_id': game_data.get('game_id'),
            'home_team': game_data.get('home_team'),
            'away_team': game_data.get('away_team'),
            'predicted_home_score': enhanced_prediction['predicted_home_score'],
            'predicted_away_score': enhanced_prediction['predicted_away_score'],
            'predicted_total_runs': enhanced_prediction['predicted_total_runs'],
            'home_win_probability': enhanced_prediction['home_win_probability'],
            'away_win_probability': enhanced_prediction['away_win_probability'],
            'confidence': enhanced_prediction['confidence'],
            'algorithm_info': enhanced_prediction['model_info'],
            'bias_corrections': enhanced_prediction['bias_corrections'],
            'raw_predictions': enhanced_prediction['raw_predictions'],
            'algorithm_version': 'enhanced_v2',
            'prediction_timestamp': game_data.get('prediction_timestamp')
        }

# 全局增強預測服務實例
enhanced_service = None

def get_enhanced_service(model_path: str = None) -> EnhancedPredictionService:
    """獲取增強預測服務單例"""
    global enhanced_service
    
    if enhanced_service is None:
        enhanced_service = EnhancedPredictionService(model_path)
        
    return enhanced_service

def initialize_enhanced_service(model_path: str):
    """初始化增強預測服務"""
    service = get_enhanced_service(model_path)
    success = service.load_model(model_path)
    
    if success:
        logger.info("增強預測服務初始化成功")
    else:
        logger.error("增強預測服務初始化失敗")
        
    return success
