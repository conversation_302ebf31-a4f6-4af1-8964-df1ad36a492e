#!/usr/bin/env python3
"""
打線順序分析器
分析打線深度、位置配置對比賽結果的影響
"""

import sys
import os
from datetime import date, datetime
from typing import Dict, List, Optional
import statistics

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models.database import db
from models.daily_lineup_fetcher import DailyLineupFetcher

class BattingOrderAnalyzer:
    """打線順序分析器"""
    
    def __init__(self):
        self.app = create_app()
        self.lineup_fetcher = DailyLineupFetcher()
        
        # 位置權重（基於防守重要性）
        self.position_weights = {
            'Pitcher': 0.0,      # 投手不參與打擊分析
            'Catcher': 1.2,      # 捕手
            'First Baseman': 1.0,    # 一壘手
            'Second Baseman': 1.1,   # 二壘手
            'Third Baseman': 1.3,    # 三壘手
            'Shortstop': 1.4,        # 游擊手
            'Left Field': 1.0,       # 左外野
            'Center Field': 1.2,     # 中外野
            'Right Field': 1.0,      # 右外野
            'Designated Hitter': 1.5  # 指定打擊
        }
        
        # 打線順序權重（基於打擊重要性）
        self.batting_order_weights = {
            1: 1.3,  # 第1棒：上壘能力
            2: 1.2,  # 第2棒：推進能力
            3: 1.5,  # 第3棒：核心打者
            4: 1.6,  # 第4棒：強打者
            5: 1.4,  # 第5棒：長打能力
            6: 1.2,  # 第6棒：穩定打擊
            7: 1.1,  # 第7棒：輔助角色
            8: 1.0,  # 第8棒：基本打擊
            9: 0.9   # 第9棒：通常較弱
        }
    
    def get_lineup_features_for_game(self, home_team_code: str, away_team_code: str, game_date: str = None) -> Dict:
        """獲取比賽的打線特徵"""
        if game_date is None:
            game_date = date.today().strftime('%Y-%m-%d')

        # 轉換字符串日期為date對象
        if isinstance(game_date, str):
            game_date_obj = datetime.strptime(game_date, '%Y-%m-%d').date()
        else:
            game_date_obj = game_date

        # 獲取當日打線
        lineups_data = self.lineup_fetcher.get_daily_lineups(game_date_obj)

        # 找到對應比賽的打線
        target_game = None
        for game in lineups_data.get('games', []):
            if (game.get('home_team_code') == home_team_code and
                game.get('away_team_code') == away_team_code):
                target_game = game
                break
        
        if not target_game:
            return self._get_default_lineup_features()
        
        # 分析打線特徵
        lineup_features = {
            # 基本打線確認狀態
            'home_lineup_confirmed': 1 if target_game.get('home_lineup_confirmed', False) else 0,
            'away_lineup_confirmed': 1 if target_game.get('away_lineup_confirmed', False) else 0,
            
            # 打線深度分析
            'home_lineup_depth_score': self._calculate_lineup_depth_score(target_game.get('home_lineup', [])),
            'away_lineup_depth_score': self._calculate_lineup_depth_score(target_game.get('away_lineup', [])),
            
            # 位置配置分析
            'home_position_strength': self._calculate_position_strength(target_game.get('home_lineup', [])),
            'away_position_strength': self._calculate_position_strength(target_game.get('away_lineup', [])),
            
            # 打線順序優化程度
            'home_batting_order_optimization': self._calculate_batting_order_optimization(target_game.get('home_lineup', [])),
            'away_batting_order_optimization': self._calculate_batting_order_optimization(target_game.get('away_lineup', [])),
            
            # 核心打者分析（1-5棒）
            'home_core_hitters_strength': self._calculate_core_hitters_strength(target_game.get('home_lineup', [])),
            'away_core_hitters_strength': self._calculate_core_hitters_strength(target_game.get('away_lineup', [])),
            
            # 下位打線分析（6-9棒）
            'home_bottom_lineup_strength': self._calculate_bottom_lineup_strength(target_game.get('home_lineup', [])),
            'away_bottom_lineup_strength': self._calculate_bottom_lineup_strength(target_game.get('away_lineup', [])),
            
            # 指定打擊使用
            'home_has_dh': 1 if self._has_designated_hitter(target_game.get('home_lineup', [])) else 0,
            'away_has_dh': 1 if self._has_designated_hitter(target_game.get('away_lineup', [])) else 0,
            
            # 打線平衡性
            'home_lineup_balance': self._calculate_lineup_balance(target_game.get('home_lineup', [])),
            'away_lineup_balance': self._calculate_lineup_balance(target_game.get('away_lineup', [])),
            
            # 打線優勢比較
            'lineup_advantage': self._calculate_lineup_advantage(
                target_game.get('home_lineup', []), 
                target_game.get('away_lineup', [])
            )
        }
        
        return lineup_features
    
    def _calculate_lineup_depth_score(self, lineup: List[Dict]) -> float:
        """計算打線深度分數"""
        if not lineup or len(lineup) < 9:
            return 0.0
        
        depth_score = 0.0
        
        for player in lineup:
            batting_order = player.get('batting_order', 9)
            position = player.get('position', 'Unknown')
            
            # 基礎分數
            base_score = 1.0
            
            # 位置權重
            position_weight = self.position_weights.get(position, 1.0)
            
            # 打線順序權重
            order_weight = self.batting_order_weights.get(batting_order, 1.0)
            
            # 計算該球員的貢獻分數
            player_score = base_score * position_weight * order_weight
            depth_score += player_score
        
        # 標準化分數（除以理論最大值）
        max_possible_score = sum(self.batting_order_weights.values()) * max(self.position_weights.values())
        normalized_score = (depth_score / max_possible_score) * 100
        
        return min(normalized_score, 100.0)
    
    def _calculate_position_strength(self, lineup: List[Dict]) -> float:
        """計算位置配置強度"""
        if not lineup:
            return 0.0
        
        position_strength = 0.0
        positions_covered = set()
        
        for player in lineup:
            position = player.get('position', 'Unknown')
            if position != 'Pitcher':  # 排除投手
                positions_covered.add(position)
                position_strength += self.position_weights.get(position, 1.0)
        
        # 位置覆蓋完整性獎勵
        expected_positions = 8  # 除投手外的8個位置
        coverage_bonus = len(positions_covered) / expected_positions
        
        return position_strength * coverage_bonus
    
    def _calculate_batting_order_optimization(self, lineup: List[Dict]) -> float:
        """計算打線順序優化程度"""
        if not lineup or len(lineup) < 9:
            return 0.0
        
        optimization_score = 0.0
        
        # 檢查關鍵位置的打線安排
        for player in lineup:
            batting_order = player.get('batting_order', 9)
            position = player.get('position', 'Unknown')
            
            # 指定打擊應該在前5棒
            if position == 'Designated Hitter' and batting_order <= 5:
                optimization_score += 2.0
            
            # 捕手通常不在前3棒
            if position == 'Catcher' and batting_order > 3:
                optimization_score += 1.0
            
            # 游擊手和三壘手通常在中間順序
            if position in ['Shortstop', 'Third Baseman'] and 3 <= batting_order <= 6:
                optimization_score += 1.5
        
        return min(optimization_score, 10.0)
    
    def _calculate_core_hitters_strength(self, lineup: List[Dict]) -> float:
        """計算核心打者（1-5棒）強度"""
        if not lineup:
            return 0.0
        
        core_strength = 0.0
        core_count = 0
        
        for player in lineup:
            batting_order = player.get('batting_order', 9)
            if 1 <= batting_order <= 5:
                position = player.get('position', 'Unknown')
                position_weight = self.position_weights.get(position, 1.0)
                order_weight = self.batting_order_weights.get(batting_order, 1.0)
                
                core_strength += position_weight * order_weight
                core_count += 1
        
        return core_strength / max(core_count, 1)
    
    def _calculate_bottom_lineup_strength(self, lineup: List[Dict]) -> float:
        """計算下位打線（6-9棒）強度"""
        if not lineup:
            return 0.0
        
        bottom_strength = 0.0
        bottom_count = 0
        
        for player in lineup:
            batting_order = player.get('batting_order', 9)
            if 6 <= batting_order <= 9:
                position = player.get('position', 'Unknown')
                position_weight = self.position_weights.get(position, 1.0)
                order_weight = self.batting_order_weights.get(batting_order, 1.0)
                
                bottom_strength += position_weight * order_weight
                bottom_count += 1
        
        return bottom_strength / max(bottom_count, 1)
    
    def _has_designated_hitter(self, lineup: List[Dict]) -> bool:
        """檢查是否使用指定打擊"""
        for player in lineup:
            if player.get('position') == 'Designated Hitter':
                return True
        return False
    
    def _calculate_lineup_balance(self, lineup: List[Dict]) -> float:
        """計算打線平衡性"""
        if not lineup or len(lineup) < 9:
            return 0.0
        
        # 計算各棒次的強度分佈
        order_strengths = []
        for player in lineup:
            batting_order = player.get('batting_order', 9)
            position = player.get('position', 'Unknown')
            
            position_weight = self.position_weights.get(position, 1.0)
            order_weight = self.batting_order_weights.get(batting_order, 1.0)
            
            strength = position_weight * order_weight
            order_strengths.append(strength)
        
        # 計算標準差（越小越平衡）
        if len(order_strengths) > 1:
            std_dev = statistics.stdev(order_strengths)
            # 轉換為平衡分數（標準差越小，平衡性越高）
            balance_score = max(0, 10 - std_dev * 5)
            return min(balance_score, 10.0)
        
        return 5.0
    
    def _calculate_lineup_advantage(self, home_lineup: List[Dict], away_lineup: List[Dict]) -> float:
        """計算打線優勢（正值=主隊優勢）"""
        home_depth = self._calculate_lineup_depth_score(home_lineup)
        away_depth = self._calculate_lineup_depth_score(away_lineup)
        
        return home_depth - away_depth
    
    def _get_default_lineup_features(self) -> Dict:
        """獲取默認打線特徵"""
        return {
            'home_lineup_confirmed': 0,
            'away_lineup_confirmed': 0,
            'home_lineup_depth_score': 50.0,
            'away_lineup_depth_score': 50.0,
            'home_position_strength': 8.0,
            'away_position_strength': 8.0,
            'home_batting_order_optimization': 5.0,
            'away_batting_order_optimization': 5.0,
            'home_core_hitters_strength': 1.3,
            'away_core_hitters_strength': 1.3,
            'home_bottom_lineup_strength': 1.0,
            'away_bottom_lineup_strength': 1.0,
            'home_has_dh': 1,
            'away_has_dh': 1,
            'home_lineup_balance': 5.0,
            'away_lineup_balance': 5.0,
            'lineup_advantage': 0.0
        }
    
    def get_lineup_summary_for_display(self, home_team_code: str, away_team_code: str, game_date: str = None) -> Dict:
        """獲取用於顯示的打線摘要"""
        if game_date is None:
            game_date = date.today().strftime('%Y-%m-%d')

        # 轉換字符串日期為date對象
        if isinstance(game_date, str):
            game_date_obj = datetime.strptime(game_date, '%Y-%m-%d').date()
        else:
            game_date_obj = game_date

        # 獲取當日打線
        lineups_data = self.lineup_fetcher.get_daily_lineups(game_date_obj)

        # 找到對應比賽
        target_game = None
        for game in lineups_data.get('games', []):
            if (game.get('home_team_code') == home_team_code and
                game.get('away_team_code') == away_team_code):
                target_game = game
                break
        
        if not target_game:
            return {'error': '無法找到比賽打線信息'}
        
        summary = {
            'game_date': game_date,
            'home_team': {
                'code': home_team_code,
                'lineup_confirmed': target_game.get('home_lineup_confirmed', False),
                'starting_pitcher': target_game.get('home_probable_pitcher', {}),
                'lineup': target_game.get('home_lineup', [])[:9],  # 只顯示前9棒
                'depth_score': self._calculate_lineup_depth_score(target_game.get('home_lineup', [])),
                'core_strength': self._calculate_core_hitters_strength(target_game.get('home_lineup', [])),
                'has_dh': self._has_designated_hitter(target_game.get('home_lineup', []))
            },
            'away_team': {
                'code': away_team_code,
                'lineup_confirmed': target_game.get('away_lineup_confirmed', False),
                'starting_pitcher': target_game.get('away_probable_pitcher', {}),
                'lineup': target_game.get('away_lineup', [])[:9],  # 只顯示前9棒
                'depth_score': self._calculate_lineup_depth_score(target_game.get('away_lineup', [])),
                'core_strength': self._calculate_core_hitters_strength(target_game.get('away_lineup', [])),
                'has_dh': self._has_designated_hitter(target_game.get('away_lineup', []))
            },
            'advantage': self._get_lineup_advantage_description(target_game.get('home_lineup', []), target_game.get('away_lineup', [])),
            'analysis': self._get_lineup_analysis(target_game.get('home_lineup', []), target_game.get('away_lineup', []))
        }
        
        return summary
    
    def _get_lineup_advantage_description(self, home_lineup: List[Dict], away_lineup: List[Dict]) -> str:
        """獲取打線優勢描述"""
        advantage = self._calculate_lineup_advantage(home_lineup, away_lineup)
        
        if abs(advantage) < 5:
            return "打線實力相當"
        elif advantage > 0:
            if advantage > 15:
                return "主隊打線明顯優勢"
            else:
                return "主隊打線輕微優勢"
        else:
            if advantage < -15:
                return "客隊打線明顯優勢"
            else:
                return "客隊打線輕微優勢"
    
    def _get_lineup_analysis(self, home_lineup: List[Dict], away_lineup: List[Dict]) -> List[str]:
        """獲取打線分析要點"""
        analysis = []
        
        # 分析指定打擊使用
        home_has_dh = self._has_designated_hitter(home_lineup)
        away_has_dh = self._has_designated_hitter(away_lineup)
        
        if home_has_dh and not away_has_dh:
            analysis.append("主隊使用指定打擊，進攻火力較強")
        elif away_has_dh and not home_has_dh:
            analysis.append("客隊使用指定打擊，進攻火力較強")
        
        # 分析核心打者強度
        home_core = self._calculate_core_hitters_strength(home_lineup)
        away_core = self._calculate_core_hitters_strength(away_lineup)
        
        if home_core > away_core + 0.3:
            analysis.append("主隊核心打者（1-5棒）實力較強")
        elif away_core > home_core + 0.3:
            analysis.append("客隊核心打者（1-5棒）實力較強")
        
        # 分析打線深度
        home_depth = self._calculate_lineup_depth_score(home_lineup)
        away_depth = self._calculate_lineup_depth_score(away_lineup)
        
        if home_depth > 70:
            analysis.append("主隊打線深度優秀")
        elif home_depth < 40:
            analysis.append("主隊打線深度不足")
        
        if away_depth > 70:
            analysis.append("客隊打線深度優秀")
        elif away_depth < 40:
            analysis.append("客隊打線深度不足")
        
        return analysis
