#!/usr/bin/env python3
"""
投手姓名匹配算法改進
解決先發投手統計關聯成功率低的問題
"""

import re
import logging
import sys
import os
from typing import Optional, List, Dict, Tuple
from difflib import SequenceMatcher

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models.database import db, PlayerStats

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PitcherNameMatcher:
    """智能投手姓名匹配器"""
    
    def __init__(self):
        self.app = create_app()
        
        # 常見的姓名變化模式
        self.name_patterns = {
            # 縮寫處理
            'jr': ['jr.', 'junior'],
            'sr': ['sr.', 'senior'],
            'iii': ['3rd', 'third'],
            'ii': ['2nd', 'second'],
            
            # 常見別名
            'alex': ['alexander', 'alejandro'],
            'mike': ['michael'],
            'joe': ['joseph'],
            'bob': ['robert'],
            'bill': ['william'],
            'jim': ['james'],
            'tom': ['thomas'],
            'dan': ['daniel'],
            'dave': ['david'],
            'chris': ['christopher'],
            'matt': ['matthew'],
            'nick': ['nicholas'],
            'tony': ['anthony'],
            'rick': ['richard'],
            'rob': ['robert'],
            'steve': ['steven', 'stephen'],
            'andy': ['andrew'],
            'pat': ['patrick'],
            'ed': ['edward'],
            'tim': ['timothy']
        }
        
        # 西班牙語姓名模式
        self.spanish_patterns = {
            'josé': ['jose'],
            'jesús': ['jesus'],
            'ángel': ['angel'],
            'josé luis': ['jose luis'],
            'carlos': ['carlos'],
            'luis': ['luis'],
            'miguel': ['miguel'],
            'rafael': ['rafael'],
            'francisco': ['francisco'],
            'fernando': ['fernando']
        }
    
    def normalize_name(self, name: str) -> str:
        """標準化姓名"""
        if not name:
            return ""
        
        # 轉小寫並移除多餘空格
        name = name.lower().strip()
        
        # 移除標點符號
        name = re.sub(r'[^\w\s]', '', name)
        
        # 移除多餘空格
        name = re.sub(r'\s+', ' ', name)
        
        return name
    
    def extract_name_parts(self, full_name: str) -> Dict[str, str]:
        """提取姓名組成部分"""
        normalized = self.normalize_name(full_name)
        parts = normalized.split()
        
        if len(parts) == 0:
            return {'first': '', 'last': '', 'middle': '', 'suffix': ''}
        elif len(parts) == 1:
            return {'first': parts[0], 'last': '', 'middle': '', 'suffix': ''}
        elif len(parts) == 2:
            return {'first': parts[0], 'last': parts[1], 'middle': '', 'suffix': ''}
        elif len(parts) == 3:
            # 檢查是否有後綴
            if parts[2] in ['jr', 'sr', 'ii', 'iii', 'iv']:
                return {'first': parts[0], 'last': parts[1], 'middle': '', 'suffix': parts[2]}
            else:
                return {'first': parts[0], 'last': parts[2], 'middle': parts[1], 'suffix': ''}
        else:
            # 4個或更多部分
            suffix = ''
            if parts[-1] in ['jr', 'sr', 'ii', 'iii', 'iv']:
                suffix = parts[-1]
                parts = parts[:-1]
            
            return {
                'first': parts[0],
                'last': parts[-1],
                'middle': ' '.join(parts[1:-1]),
                'suffix': suffix
            }
    
    def generate_name_variations(self, name: str) -> List[str]:
        """生成姓名變化形式"""
        variations = [name]
        parts = self.extract_name_parts(name)
        
        first = parts['first']
        last = parts['last']
        middle = parts['middle']
        suffix = parts['suffix']
        
        # 基本組合
        if first and last:
            variations.extend([
                f"{first} {last}",
                f"{last} {first}",
                f"{first}{last}",
                f"{last}{first}"
            ])
        
        # 包含中間名的組合
        if middle:
            variations.extend([
                f"{first} {middle} {last}",
                f"{first} {last}",  # 省略中間名
                f"{first[0]} {middle} {last}",  # 名字縮寫
                f"{first} {middle[0]} {last}"   # 中間名縮寫
            ])
        
        # 包含後綴的組合
        if suffix:
            base_name = f"{first} {last}"
            variations.extend([
                f"{base_name} {suffix}",
                f"{base_name}",  # 省略後綴
                f"{first} {last} {suffix}"
            ])
        
        # 處理常見別名
        first_variations = self._get_name_variations(first)
        for first_var in first_variations:
            if first_var != first:
                variations.append(f"{first_var} {last}")
        
        # 移除重複並返回
        return list(set([v.strip() for v in variations if v.strip()]))
    
    def _get_name_variations(self, name: str) -> List[str]:
        """獲取單個名字的變化形式"""
        variations = [name]
        
        # 檢查常見別名
        for short_name, full_names in self.name_patterns.items():
            if name == short_name:
                variations.extend(full_names)
            elif name in full_names:
                variations.append(short_name)
        
        # 檢查西班牙語姓名
        for spanish_name, english_names in self.spanish_patterns.items():
            if name == spanish_name:
                variations.extend(english_names)
            elif name in english_names:
                variations.append(spanish_name)
        
        return variations
    
    def calculate_similarity(self, name1: str, name2: str) -> float:
        """計算兩個姓名的相似度"""
        # 標準化姓名
        norm1 = self.normalize_name(name1)
        norm2 = self.normalize_name(name2)
        
        # 完全匹配
        if norm1 == norm2:
            return 1.0
        
        # 使用序列匹配器
        similarity = SequenceMatcher(None, norm1, norm2).ratio()
        
        # 檢查姓名部分匹配
        parts1 = self.extract_name_parts(name1)
        parts2 = self.extract_name_parts(name2)
        
        # 姓氏匹配加分
        if parts1['last'] and parts2['last']:
            last_similarity = SequenceMatcher(None, parts1['last'], parts2['last']).ratio()
            if last_similarity > 0.8:
                similarity += 0.2
        
        # 名字匹配加分
        if parts1['first'] and parts2['first']:
            first_similarity = SequenceMatcher(None, parts1['first'], parts2['first']).ratio()
            if first_similarity > 0.8:
                similarity += 0.1
        
        return min(similarity, 1.0)
    
    def find_best_pitcher_match(self, pitcher_name: str, season: int,
                               min_similarity: float = 0.6) -> Optional[PlayerStats]:
        """找到最佳的投手匹配"""
        with self.app.app_context():
            try:
                # 首先嘗試精確匹配
                exact_match = PlayerStats.query.filter(
                    PlayerStats.player_name == pitcher_name,
                    PlayerStats.innings_pitched > 0,
                    PlayerStats.season.in_([season, season-1, season-2])  # 擴展到近3年
                ).first()

                if exact_match:
                    logger.info(f"精確匹配: {pitcher_name} -> {exact_match.player_name}")
                    return exact_match

                # 生成姓名變化形式
                name_variations = self.generate_name_variations(pitcher_name)

                # 獲取近3年的投手統計（擴大搜索範圍）
                pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.innings_pitched > 0,
                    PlayerStats.season.in_([season, season-1, season-2])
                ).all()

                best_match = None
                best_similarity = 0.0

                # 對每個投手統計進行匹配
                for stats in pitcher_stats:
                    if not stats.player_name:
                        continue

                    # 檢查變化形式的直接匹配
                    normalized_db_name = self.normalize_name(stats.player_name)
                    for variation in name_variations:
                        normalized_variation = self.normalize_name(variation)
                        if normalized_variation == normalized_db_name:
                            logger.info(f"變化形式匹配: {pitcher_name} -> {stats.player_name}")
                            return stats

                    # 檢查包含關係
                    pitcher_parts = pitcher_name.split()
                    if len(pitcher_parts) >= 2:
                        first_name = pitcher_parts[0].lower()
                        last_name = pitcher_parts[-1].lower()
                        db_name_lower = stats.player_name.lower()

                        if first_name in db_name_lower and last_name in db_name_lower:
                            logger.info(f"部分匹配: {pitcher_name} -> {stats.player_name}")
                            return stats

                    # 計算相似度
                    similarity = self.calculate_similarity(pitcher_name, stats.player_name)

                    if similarity > best_similarity and similarity >= min_similarity:
                        best_similarity = similarity
                        best_match = stats

                if best_match:
                    logger.info(f"相似度匹配: {pitcher_name} -> {best_match.player_name} (相似度: {best_similarity:.2f})")

                return best_match

            except Exception as e:
                logger.error(f"投手匹配失敗 {pitcher_name}: {e}")
                return None
    
    def batch_test_matching(self, limit: int = 50) -> Dict:
        """批量測試匹配效果"""
        with self.app.app_context():
            try:
                from models.database import Game, GameDetail

                # 獲取測試樣本 - 使用最新的比賽數據
                sample_games = db.session.query(Game, GameDetail).join(
                    GameDetail, Game.game_id == GameDetail.game_id
                ).filter(
                    GameDetail.home_starting_pitcher.isnot(None),
                    GameDetail.away_starting_pitcher.isnot(None)
                ).order_by(Game.date.desc()).limit(limit).all()

                total_pitchers = 0
                successful_matches = 0
                results = []

                # 使用最新賽季進行匹配（2024年）
                current_season = 2024

                for game, detail in sample_games:
                    # 測試主隊投手
                    if detail.home_starting_pitcher:
                        total_pitchers += 1
                        match = self.find_best_pitcher_match(
                            detail.home_starting_pitcher,
                            current_season  # 使用當前賽季而不是比賽年份
                        )
                        if match:
                            successful_matches += 1
                            results.append({
                                'original': detail.home_starting_pitcher,
                                'matched': match.player_name,
                                'era': match.era,
                                'season': match.season,
                                'game_year': game.date.year,
                                'success': True
                            })
                        else:
                            results.append({
                                'original': detail.home_starting_pitcher,
                                'matched': None,
                                'era': None,
                                'season': None,
                                'game_year': game.date.year,
                                'success': False
                            })

                    # 測試客隊投手
                    if detail.away_starting_pitcher:
                        total_pitchers += 1
                        match = self.find_best_pitcher_match(
                            detail.away_starting_pitcher,
                            current_season  # 使用當前賽季而不是比賽年份
                        )
                        if match:
                            successful_matches += 1
                            results.append({
                                'original': detail.away_starting_pitcher,
                                'matched': match.player_name,
                                'era': match.era,
                                'season': match.season,
                                'game_year': game.date.year,
                                'success': True
                            })
                        else:
                            results.append({
                                'original': detail.away_starting_pitcher,
                                'matched': None,
                                'era': None,
                                'season': None,
                                'game_year': game.date.year,
                                'success': False
                            })

                success_rate = successful_matches / total_pitchers * 100 if total_pitchers > 0 else 0

                return {
                    'total_pitchers': total_pitchers,
                    'successful_matches': successful_matches,
                    'success_rate': success_rate,
                    'results': results
                }

            except Exception as e:
                logger.error(f"批量測試失敗: {e}")
                return {'error': str(e)}

def main():
    """主函數 - 測試匹配算法"""
    matcher = PitcherNameMatcher()
    
    print("=" * 80)
    print("🎯 投手姓名匹配算法測試")
    print("=" * 80)
    
    # 批量測試
    results = matcher.batch_test_matching(30)
    
    if 'error' in results:
        print(f"❌ 測試失敗: {results['error']}")
        return
    
    print(f"\n📊 測試結果:")
    print(f"總投手數: {results['total_pitchers']}")
    print(f"成功匹配: {results['successful_matches']}")
    print(f"成功率: {results['success_rate']:.1f}%")
    
    print(f"\n✅ 成功匹配樣本:")
    success_count = 0
    for result in results['results']:
        if result['success'] and success_count < 10:
            print(f"  {result['original']} -> {result['matched']} (ERA: {result['era']:.2f})")
            success_count += 1
    
    print(f"\n❌ 失敗匹配樣本:")
    fail_count = 0
    for result in results['results']:
        if not result['success'] and fail_count < 5:
            print(f"  {result['original']} -> 無匹配")
            fail_count += 1

if __name__ == "__main__":
    main()
