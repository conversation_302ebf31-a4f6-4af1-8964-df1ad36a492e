#!/usr/bin/env python3
"""
[DEPRECATED] - 此檔案已棄用

此模組中的進階統計特徵和計算邏輯已被統一整合到 `models/feature_engineer.py` 中。
新的 `Core_v1` 模型將通過 `FeatureEngineer` 來使用這些特徵。

此檔案僅為歷史參考保留。
"""

#!/usr/bin/env python3
"""
進階MLB預測系統 - 使用詳細棒球統計指標
"""

import sys
import os
from datetime import date, timedelta
from collections import defaultdict
import json
import numpy as np
from typing import Dict, List, Tuple, Optional

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import db, Game, Team, TeamStats, PlayerStats, BoxScore, PlayerGameStats

class AdvancedMLBPredictor:
    """進階MLB預測系統"""
    
    def __init__(self):
        self.team_cache = {}
        self.stats_cache = {}
    
    def predict_game_advanced(self, home_team: str, away_team: str, game_date: date = None) -> Dict:
        """
        使用進階統計指標預測比賽
        
        考慮因素：
        1. 球隊平均得分能力
        2. 殘壘率 (LOB%)
        3. 得點圈打擊率 (RISP)
        4. 打點能力 (RBI)
        5. 投手防禦率和WHIP
        6. 牛棚深度
        7. 主客場差異
        8. 最近表現趨勢
        """
        
        if game_date is None:
            game_date = date.today()
        
        print(f"🎯 進階預測: {away_team} @ {home_team}")
        
        # 1. 獲取球隊基本信息
        home_team_info = self._get_team_info(home_team)
        away_team_info = self._get_team_info(away_team)
        
        if not home_team_info or not away_team_info:
            return self._default_prediction(home_team, away_team)
        
        # 2. 提取進階特徵
        home_features = self._extract_advanced_features(home_team_info, is_home=True)
        away_features = self._extract_advanced_features(away_team_info, is_home=False)
        
        # 3. 計算預測得分
        home_predicted_score = self._calculate_predicted_score(
            home_features, away_features, is_home=True
        )
        away_predicted_score = self._calculate_predicted_score(
            away_features, home_features, is_home=False
        )
        
        # 4. 應用情境調整
        home_predicted_score, away_predicted_score = self._apply_situational_adjustments(
            home_predicted_score, away_predicted_score, home_features, away_features
        )
        
        # 5. 計算勝率
        win_probability = self._calculate_win_probability(
            home_predicted_score, away_predicted_score
        )
        
        return {
            'home_team': home_team,
            'away_team': away_team,
            'predicted_home_score': round(home_predicted_score, 1),
            'predicted_away_score': round(away_predicted_score, 1),
            'home_win_probability': round(win_probability, 3),
            'total_score': round(home_predicted_score + away_predicted_score, 1),
            'prediction_factors': {
                'home_offense_rating': home_features.get('offense_rating', 0),
                'away_offense_rating': away_features.get('offense_rating', 0),
                'home_pitching_rating': home_features.get('pitching_rating', 0),
                'away_pitching_rating': away_features.get('pitching_rating', 0),
                'home_field_advantage': home_features.get('home_field_advantage', 0),
                'recent_form_impact': (home_features.get('recent_form', 0) + away_features.get('recent_form', 0)) / 2
            }
        }
    
    def _get_team_info(self, team_code: str) -> Optional[Dict]:
        """獲取球隊信息"""
        if team_code in self.team_cache:
            return self.team_cache[team_code]
        
        team = Team.query.filter_by(team_code=team_code).first()
        if not team:
            print(f"❌ 找不到球隊: {team_code}")
            return None
        
        team_stats = TeamStats.query.filter_by(team_id=team.team_id).first()
        
        self.team_cache[team_code] = {
            'team': team,
            'stats': team_stats
        }
        
        return self.team_cache[team_code]
    
    def _extract_advanced_features(self, team_info: Dict, is_home: bool) -> Dict:
        """提取進階特徵"""
        team = team_info['team']
        stats = team_info['stats']
        
        features = {}
        
        if not stats:
            return self._default_features(is_home)
        
        # 1. 基本得分能力
        features['avg_runs_scored'] = stats.runs_scored
        features['avg_runs_allowed'] = stats.runs_allowed
        
        # 2. 計算進攻評級
        features['offense_rating'] = self._calculate_offense_rating(team.team_id, stats)
        
        # 3. 計算投手評級
        features['pitching_rating'] = self._calculate_pitching_rating(team.team_id, stats)
        
        # 4. 主客場優勢
        if is_home:
            features['home_field_advantage'] = self._calculate_home_field_advantage(stats)
        else:
            features['home_field_advantage'] = 0
        
        # 5. 最近表現
        features['recent_form'] = self._calculate_recent_form(team.team_id)
        
        # 6. 殘壘能力 (基於BoxScore數據)
        features['lob_efficiency'] = self._calculate_lob_efficiency(team.team_id)
        
        # 7. 得點圈打擊能力
        features['risp_ability'] = self._calculate_risp_ability(team.team_id)
        
        return features
    
    def _calculate_offense_rating(self, team_id: int, stats: TeamStats) -> float:
        """計算進攻評級 (0-100)"""
        base_rating = 50.0
        
        # 基於平均得分調整
        runs_factor = (stats.runs_scored - 4.5) * 10  # 4.5是聯盟平均
        
        # 基於打擊率調整
        avg_factor = (stats.batting_avg - 0.250) * 100
        
        # 基於勝率調整
        win_factor = (stats.win_percentage - 0.500) * 20
        
        rating = base_rating + runs_factor + avg_factor + win_factor
        return max(0, min(100, rating))
    
    def _calculate_pitching_rating(self, team_id: int, stats: TeamStats) -> float:
        """計算投手評級 (0-100)"""
        base_rating = 50.0
        
        # 基於ERA調整 (越低越好)
        era_factor = (4.50 - stats.era) * 10  # 4.50是聯盟平均
        
        # 基於失分調整
        runs_allowed_factor = (4.5 - stats.runs_allowed) * 8
        
        rating = base_rating + era_factor + runs_allowed_factor
        return max(0, min(100, rating))
    
    def _calculate_home_field_advantage(self, stats: TeamStats) -> float:
        """計算主場優勢"""
        if stats.home_wins + stats.home_losses == 0:
            return 0.3  # 默認主場優勢
        
        home_win_pct = stats.home_wins / (stats.home_wins + stats.home_losses)
        away_win_pct = stats.away_wins / (stats.away_wins + stats.away_losses) if (stats.away_wins + stats.away_losses) > 0 else 0.5
        
        # 主場優勢 = 主場勝率 - 客場勝率
        advantage = home_win_pct - away_win_pct
        return max(0, min(1.0, advantage + 0.3))  # 基礎0.3分優勢
    
    def _calculate_recent_form(self, team_id: int) -> float:
        """計算最近表現 (最近10場)"""
        recent_date = date.today() - timedelta(days=20)
        
        recent_games = Game.query.filter(
            db.or_(Game.home_team_id == team_id, Game.away_team_id == team_id),
            Game.date >= recent_date,
            Game.game_status == 'completed'
        ).order_by(Game.date.desc()).limit(10).all()
        
        if not recent_games:
            return 0.0
        
        wins = 0
        total_runs = 0
        total_allowed = 0
        
        for game in recent_games:
            if game.home_team_id == team_id:
                team_score = game.home_score
                opp_score = game.away_score
            else:
                team_score = game.away_score
                opp_score = game.home_score
            
            if team_score > opp_score:
                wins += 1
            
            total_runs += team_score
            total_allowed += opp_score
        
        win_rate = wins / len(recent_games)
        avg_runs = total_runs / len(recent_games)
        avg_allowed = total_allowed / len(recent_games)
        
        # 綜合評分
        form_score = (win_rate - 0.5) * 2 + (avg_runs - avg_allowed) * 0.2
        return max(-1.0, min(1.0, form_score))
    
    def _calculate_lob_efficiency(self, team_id: int) -> float:
        """計算殘壘效率 (基於BoxScore數據)"""
        box_scores = BoxScore.query.filter_by(team_id=team_id).limit(50).all()
        
        if not box_scores:
            return 0.5  # 默認值
        
        total_lob = sum(bs.left_on_base for bs in box_scores if bs.left_on_base)
        total_hits = sum(bs.hits for bs in box_scores if bs.hits)
        total_runs = sum(bs.runs for bs in box_scores if bs.runs)
        
        if total_hits == 0:
            return 0.5
        
        # 得分效率 = 得分 / (安打 + 殘壘)
        scoring_efficiency = total_runs / (total_hits + total_lob) if (total_hits + total_lob) > 0 else 0.5
        return max(0, min(1.0, scoring_efficiency))
    
    def _calculate_risp_ability(self, team_id: int) -> float:
        """計算得點圈打擊能力"""
        # 這裡可以基於PlayerGameStats計算
        # 暫時使用簡化版本
        return 0.5 + (np.random.random() - 0.5) * 0.3  # 模擬RISP能力
    
    def _calculate_predicted_score(self, team_features: Dict, opp_features: Dict, is_home: bool) -> float:
        """計算預測得分"""
        base_score = team_features.get('avg_runs_scored', 4.5)
        
        # 進攻能力調整
        offense_adj = (team_features.get('offense_rating', 50) - 50) * 0.04
        
        # 對手投手能力調整
        pitching_adj = (50 - opp_features.get('pitching_rating', 50)) * 0.03
        
        # 主場優勢
        home_adj = team_features.get('home_field_advantage', 0) * 0.5 if is_home else 0
        
        # 最近表現
        form_adj = team_features.get('recent_form', 0) * 0.3
        
        # 殘壘效率
        lob_adj = (team_features.get('lob_efficiency', 0.5) - 0.5) * 1.0
        
        # 得點圈能力
        risp_adj = (team_features.get('risp_ability', 0.5) - 0.5) * 0.8
        
        predicted_score = base_score + offense_adj + pitching_adj + home_adj + form_adj + lob_adj + risp_adj
        
        return max(1.0, min(12.0, predicted_score))
    
    def _apply_situational_adjustments(self, home_score: float, away_score: float, 
                                     home_features: Dict, away_features: Dict) -> Tuple[float, float]:
        """應用情境調整"""
        # 投手對決調整
        if (home_features.get('pitching_rating', 50) > 75 and 
            away_features.get('pitching_rating', 50) > 75):
            # 王牌對決，降低得分
            home_score *= 0.85
            away_score *= 0.85
        
        # 打擊大戰調整
        if (home_features.get('offense_rating', 50) > 75 and 
            away_features.get('offense_rating', 50) > 75):
            # 強打對決，提高得分
            home_score *= 1.15
            away_score *= 1.15
        
        return home_score, away_score
    
    def _calculate_win_probability(self, home_score: float, away_score: float) -> float:
        """計算勝率"""
        score_diff = home_score - away_score
        # 使用邏輯函數轉換分差為勝率
        win_prob = 1 / (1 + np.exp(-score_diff * 0.8))
        return win_prob
    
    def _default_features(self, is_home: bool) -> Dict:
        """默認特徵值"""
        return {
            'avg_runs_scored': 4.5,
            'avg_runs_allowed': 4.5,
            'offense_rating': 50.0,
            'pitching_rating': 50.0,
            'home_field_advantage': 0.3 if is_home else 0,
            'recent_form': 0.0,
            'lob_efficiency': 0.5,
            'risp_ability': 0.5
        }
    
    def _default_prediction(self, home_team: str, away_team: str) -> Dict:
        """默認預測"""
        return {
            'home_team': home_team,
            'away_team': away_team,
            'predicted_home_score': 4.5,
            'predicted_away_score': 4.5,
            'home_win_probability': 0.55,  # 主場小優勢
            'total_score': 9.0,
            'prediction_factors': {
                'home_offense_rating': 50,
                'away_offense_rating': 50,
                'home_pitching_rating': 50,
                'away_pitching_rating': 50,
                'home_field_advantage': 0.3,
                'recent_form_impact': 0.0
            }
        }
