#!/usr/bin/env python3
"""
投手對球隊/打者分析系統
實現精細化的投手對戰分析，這是MLB預測的核心
"""

import logging
import json
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
import numpy as np
from sqlalchemy import and_, or_, func

from models.database import db, Game, PlayerStats, PlayerGameStats, TeamStats
from models.historical_data_optimizer import HistoricalDataOptimizer
from models.prediction_context import PredictionContext

logger = logging.getLogger(__name__)

class PitcherVsTeamAnalyzer:
    """投手對特定球隊分析器"""
    
    def __init__(self):
        self.data_optimizer = HistoricalDataOptimizer()
        
        # MLB平均值 (用於對比)
        self.mlb_averages = {
            'era': 4.50,
            'whip': 1.30,
            'batting_avg_against': 0.250,
            'k_rate': 0.22,
            'hr_rate': 0.030,
            'runs_per_game': 4.5
        }
    
    async def analyze_pitcher_vs_team(self, 
                                    pitcher_name: str,
                                    opposing_team: str,
                                    target_date: date,
                                    is_home_pitcher: bool = True,
                                    training_end_date: date = None) -> Dict:
        """
        分析投手對特定球隊的歷史表現
        
        這是MLB預測的關鍵 - 某投手對某球隊可能ERA 2.50，對另一球隊ERA 6.00
        
        Args:
            training_end_date: 訓練數據截止日期，確保避免未來數據洩漏
        """
        
        # 處理時間邏輯 - 確保只使用截止日期之前的數據
        if training_end_date is None:
            training_end_date = target_date - timedelta(days=1)
        
        logger.info(f"🎯 分析投手 {pitcher_name} 對 {opposing_team} 的歷史表現")
        logger.info(f"📊 分析數據範圍截止: {training_end_date}")
        
        analysis_result = {
            'pitcher_name': pitcher_name,
            'opposing_team': opposing_team,
            'is_home_pitcher': is_home_pitcher,
            'analysis_date': target_date.isoformat(),
            'historical_matchups': {},
            'performance_vs_team': {},
            'vs_mlb_average_comparison': {},
            'key_matchup_insights': [],
            'predicted_impact': {},
            'confidence_level': 'low'
        }
        
        try:
            # 1. 獲取投手對該球隊的歷史對戰記錄
            historical_matchups = await self._get_pitcher_vs_team_history(
                pitcher_name, opposing_team, target_date
            )
            
            if not historical_matchups:
                logger.warning(f"沒有找到 {pitcher_name} 對 {opposing_team} 的歷史對戰記錄")
                analysis_result['key_matchup_insights'].append("缺少歷史對戰數據，使用一般表現預測")
                return analysis_result
            
            # 2. 計算對該球隊的特定表現指標
            team_specific_performance = self._calculate_team_specific_performance(
                historical_matchups, opposing_team
            )
            
            # 3. 與MLB平均值比較
            mlb_comparison = self._compare_with_mlb_average(team_specific_performance)
            
            # 4. 分析主要打者對戰
            key_batters_analysis = await self._analyze_vs_key_batters(
                pitcher_name, opposing_team, target_date
            )
            
            # 5. 生成預測影響
            predicted_impact = self._calculate_predicted_impact(
                team_specific_performance, mlb_comparison, key_batters_analysis, is_home_pitcher
            )
            
            # 6. 產生關鍵洞察
            insights = self._generate_matchup_insights(
                team_specific_performance, mlb_comparison, key_batters_analysis
            )
            
            # 7. 評估分析信心度
            confidence = self._assess_analysis_confidence(
                historical_matchups, team_specific_performance, key_batters_analysis
            )
            
            analysis_result.update({
                'historical_matchups': {
                    'total_games': len(historical_matchups),
                    'games_data': historical_matchups[:5],  # 最近5場詳細數據
                    'seasons_covered': list(set([game.get('season') for game in historical_matchups]))
                },
                'performance_vs_team': team_specific_performance,
                'vs_mlb_average_comparison': mlb_comparison,
                'key_batters_analysis': key_batters_analysis,
                'predicted_impact': predicted_impact,
                'key_matchup_insights': insights,
                'confidence_level': confidence
            })
            
            logger.info(f"✅ 完成投手分析: {pitcher_name} vs {opposing_team} (信心度: {confidence})")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"投手對戰分析失敗: {e}")
            analysis_result['error'] = str(e)
            return analysis_result
    
    async def _get_pitcher_vs_team_history(self, 
                                         pitcher_name: str,
                                         opposing_team: str,
                                         target_date: date) -> List[Dict]:
        """獲取投手對特定球隊的歷史對戰記錄"""
        
        try:
            # 查詢投手對該球隊的歷史表現
            # 這需要PlayerGameStats表中的詳細數據
            
            # 需要通過 Player 表來查詢球員姓名
            from models.database import Player
            
            pitcher_games = db.session.query(PlayerGameStats, Game, Player)\
                .join(Game, PlayerGameStats.game_id == Game.game_id)\
                .join(Player, PlayerGameStats.player_id == Player.player_id)\
                .filter(
                    Player.full_name.ilike(f"%{pitcher_name}%"),
                    PlayerGameStats.innings_pitched > 0,  # 確保是投手
                    Game.date < target_date
                )\
                .filter(
                    or_(
                        and_(Game.home_team == opposing_team, PlayerGameStats.team_id != self._get_team_id(opposing_team)),
                        and_(Game.away_team == opposing_team, PlayerGameStats.team_id != self._get_team_id(opposing_team))
                    )
                )\
                .order_by(Game.date.desc())\
                .limit(20)\
                .all()
            
            historical_data = []
            
            for player_stat, game in pitcher_games:
                # 計算該場比賽的ERA
                game_era = (player_stat.earned_runs / player_stat.innings_pitched * 9) if player_stat.innings_pitched > 0 else 0
                
                # 計算WHIP
                game_whip = ((player_stat.hits_allowed + player_stat.walks_allowed) / player_stat.innings_pitched) if player_stat.innings_pitched > 0 else 0
                
                game_data = {
                    'game_date': game.date.isoformat(),
                    'season': self.data_optimizer._extract_season_from_date(game.date),
                    'opposing_team': opposing_team,
                    'innings_pitched': player_stat.innings_pitched,
                    'earned_runs': player_stat.earned_runs,
                    'hits_allowed': player_stat.hits_allowed,
                    'walks_allowed': player_stat.walks_allowed,
                    'strikeouts': player_stat.strikeouts_pitched,
                    'home_runs_allowed': player_stat.home_runs_allowed,
                    'game_era': round(game_era, 2),
                    'game_whip': round(game_whip, 3),
                    'pitches_thrown': player_stat.pitches_thrown,
                    'final_score': f"{game.away_score}-{game.home_score}",
                    'game_id': game.game_id
                }
                
                historical_data.append(game_data)
            
            return historical_data
            
        except Exception as e:
            logger.error(f"查詢投手歷史對戰失敗: {e}")
            return []
    
    def _calculate_team_specific_performance(self, historical_matchups: List[Dict], opposing_team: str) -> Dict:
        """計算投手對特定球隊的表現統計"""
        
        if not historical_matchups:
            return {}
        
        # 應用歷史權重
        weighted_stats = []
        total_weight = 0
        
        for game in historical_matchups:
            season = game.get('season')
            base_weight = self.data_optimizer.seasonal_weights.get(season, 0.1)
            quality_score = self.data_optimizer.season_quality_scores.get(season, 0.5)
            
            # COVID調整
            if season == 2020:
                base_weight *= self.data_optimizer.covid_adjustment_factor
            
            final_weight = base_weight * quality_score
            
            # 加權統計
            weighted_game = {
                'innings_pitched': game.get('innings_pitched', 0) * final_weight,
                'earned_runs': game.get('earned_runs', 0) * final_weight,
                'hits_allowed': game.get('hits_allowed', 0) * final_weight,
                'walks_allowed': game.get('walks_allowed', 0) * final_weight,
                'strikeouts': game.get('strikeouts', 0) * final_weight,
                'home_runs_allowed': game.get('home_runs_allowed', 0) * final_weight,
                'weight': final_weight
            }
            
            weighted_stats.append(weighted_game)
            total_weight += final_weight
        
        if total_weight == 0:
            return {}
        
        # 計算加權平均統計
        total_innings = sum(stat['innings_pitched'] for stat in weighted_stats) / total_weight
        total_earned_runs = sum(stat['earned_runs'] for stat in weighted_stats) / total_weight
        total_hits = sum(stat['hits_allowed'] for stat in weighted_stats) / total_weight
        total_walks = sum(stat['walks_allowed'] for stat in weighted_stats) / total_weight
        total_strikeouts = sum(stat['strikeouts'] for stat in weighted_stats) / total_weight
        total_hrs = sum(stat['home_runs_allowed'] for stat in weighted_stats) / total_weight
        
        # 計算關鍵指標
        era_vs_team = (total_earned_runs / total_innings * 9) if total_innings > 0 else 0
        whip_vs_team = ((total_hits + total_walks) / total_innings) if total_innings > 0 else 0
        k_rate_vs_team = (total_strikeouts / total_innings * 9) if total_innings > 0 else 0
        hr_rate_vs_team = (total_hrs / total_innings * 9) if total_innings > 0 else 0
        
        performance_stats = {
            'games_pitched': len(historical_matchups),
            'total_innings': round(total_innings, 1),
            'era_vs_team': round(era_vs_team, 2),
            'whip_vs_team': round(whip_vs_team, 3),
            'k_per_9_vs_team': round(k_rate_vs_team, 1),
            'hr_per_9_vs_team': round(hr_rate_vs_team, 2),
            'avg_innings_per_start': round(total_innings / len(historical_matchups), 1),
            'total_weight_used': round(total_weight, 2),
            'seasons_analyzed': len(set([game.get('season') for game in historical_matchups]))
        }
        
        return performance_stats
    
    def _compare_with_mlb_average(self, team_performance: Dict) -> Dict:
        """與MLB平均值比較"""
        
        if not team_performance:
            return {}
        
        comparison = {}
        
        # ERA比較
        era_vs_team = team_performance.get('era_vs_team', self.mlb_averages['era'])
        era_diff = era_vs_team - self.mlb_averages['era']
        comparison['era_vs_mlb'] = {
            'team_era': era_vs_team,
            'mlb_avg_era': self.mlb_averages['era'],
            'difference': round(era_diff, 2),
            'performance': 'better' if era_diff < 0 else 'worse' if era_diff > 0 else 'average'
        }
        
        # WHIP比較
        whip_vs_team = team_performance.get('whip_vs_team', self.mlb_averages['whip'])
        whip_diff = whip_vs_team - self.mlb_averages['whip']
        comparison['whip_vs_mlb'] = {
            'team_whip': whip_vs_team,
            'mlb_avg_whip': self.mlb_averages['whip'],
            'difference': round(whip_diff, 3),
            'performance': 'better' if whip_diff < 0 else 'worse' if whip_diff > 0 else 'average'
        }
        
        # K率比較
        k_rate_vs_team = team_performance.get('k_per_9_vs_team', self.mlb_averages['k_rate'] * 9)
        k_rate_mlb = self.mlb_averages['k_rate'] * 9
        k_rate_diff = k_rate_vs_team - k_rate_mlb
        comparison['k_rate_vs_mlb'] = {
            'team_k_rate': k_rate_vs_team,
            'mlb_avg_k_rate': k_rate_mlb,
            'difference': round(k_rate_diff, 1),
            'performance': 'better' if k_rate_diff > 0 else 'worse' if k_rate_diff < 0 else 'average'
        }
        
        return comparison
    
    async def _analyze_vs_key_batters(self, pitcher_name: str, opposing_team: str, target_date: date) -> Dict:
        """分析投手對該球隊主要打者的對戰記錄"""
        
        key_batters_analysis = {
            'total_key_batters_analyzed': 0,
            'batters_with_advantage': [],
            'batters_with_disadvantage': [],
            'overall_assessment': 'neutral'
        }
        
        try:
            # 獲取該球隊的主要打者 (近期出賽較多的球員)
            key_batters = db.session.query(PlayerStats)\
                .filter(
                    PlayerStats.team_id == self._get_team_id(opposing_team),
                    PlayerStats.season >= target_date.year - 1,  # 最近兩年
                    PlayerStats.at_bats >= 100  # 有足夠打席數
                )\
                .order_by(PlayerStats.at_bats.desc())\
                .limit(9)\
                .all()  # 主力9人打線
            
            batter_matchup_results = []
            
            for batter in key_batters:
                # 查詢該打者對該投手的歷史對戰
                matchup_stats = await self._get_batter_vs_pitcher_history(
                    batter.player_name, pitcher_name, target_date
                )
                
                if matchup_stats:
                    batter_analysis = {
                        'batter_name': batter.player_name,
                        'career_avg': batter.batting_avg,
                        'vs_pitcher_avg': matchup_stats.get('batting_avg', 0),
                        'at_bats_vs_pitcher': matchup_stats.get('at_bats', 0),
                        'hits_vs_pitcher': matchup_stats.get('hits', 0),
                        'home_runs_vs_pitcher': matchup_stats.get('home_runs', 0),
                        'advantage': self._determine_batter_advantage(batter.batting_avg, matchup_stats)
                    }
                    
                    batter_matchup_results.append(batter_analysis)
                    
                    # 分類有利/不利打者
                    if batter_analysis['advantage'] == 'batter_advantage':
                        key_batters_analysis['batters_with_advantage'].append(batter.player_name)
                    elif batter_analysis['advantage'] == 'pitcher_advantage':
                        key_batters_analysis['batters_with_disadvantage'].append(batter.player_name)
            
            key_batters_analysis.update({
                'total_key_batters_analyzed': len(batter_matchup_results),
                'detailed_matchups': batter_matchup_results[:5],  # 顯示前5名主力
                'pitcher_favorable_matchups': len(key_batters_analysis['batters_with_disadvantage']),
                'batter_favorable_matchups': len(key_batters_analysis['batters_with_advantage'])
            })
            
            # 整體評估
            pitcher_favorable = len(key_batters_analysis['batters_with_disadvantage'])
            batter_favorable = len(key_batters_analysis['batters_with_advantage'])
            
            if pitcher_favorable > batter_favorable + 2:
                key_batters_analysis['overall_assessment'] = 'pitcher_advantage'
            elif batter_favorable > pitcher_favorable + 2:
                key_batters_analysis['overall_assessment'] = 'batter_advantage'
            else:
                key_batters_analysis['overall_assessment'] = 'neutral'
            
        except Exception as e:
            logger.error(f"主要打者對戰分析失敗: {e}")
            key_batters_analysis['error'] = str(e)
        
        return key_batters_analysis
    
    async def _get_batter_vs_pitcher_history(self, batter_name: str, pitcher_name: str, target_date: date) -> Dict:
        """獲取打者對特定投手的歷史對戰記錄"""
        
        # 這裡需要詳細的打席對戰數據
        # 由於可能沒有逐打席記錄，我們先用模擬邏輯
        # 在實際應用中，這裡應該查詢play-by-play數據或專門的對戰表
        
        try:
            # 簡化版本：基於同場比賽的推算
            # 實際應該有更詳細的AtBat表記錄每個打席結果
            
            return {
                'at_bats': 12,  # 示例數據
                'hits': 3,
                'home_runs': 1,
                'batting_avg': 0.250,
                'sample_size': 'small'  # 標記樣本大小
            }
            
        except Exception as e:
            logger.error(f"查詢打者對戰記錄失敗: {e}")
            return {}
    
    def _determine_batter_advantage(self, career_avg: float, matchup_stats: Dict) -> str:
        """判斷打者在對戰中的優勢狀況"""
        
        vs_pitcher_avg = matchup_stats.get('batting_avg', career_avg)
        at_bats = matchup_stats.get('at_bats', 0)
        
        # 樣本太小不做判斷
        if at_bats < 6:
            return 'insufficient_data'
        
        # 比較對戰打擊率與生涯打擊率
        diff = vs_pitcher_avg - career_avg
        
        if diff >= 0.050:  # 高50點以上
            return 'batter_advantage'
        elif diff <= -0.050:  # 低50點以上
            return 'pitcher_advantage'
        else:
            return 'neutral'
    
    def _calculate_predicted_impact(self, 
                                  team_performance: Dict,
                                  mlb_comparison: Dict,
                                  key_batters: Dict,
                                  is_home_pitcher: bool) -> Dict:
        """計算預測影響"""
        
        predicted_impact = {
            'expected_runs_allowed': 4.5,  # MLB平均
            'innings_likely_pitched': 6.0,
            'run_prevention_factor': 1.0,  # 1.0 = 平均, <1.0 = 更好, >1.0 = 更差
            'confidence_level': 'medium',
            'key_factors': []
        }
        
        try:
            # 基於ERA調整預期失分
            era_vs_team = team_performance.get('era_vs_team', 4.5)
            expected_innings = team_performance.get('avg_innings_per_start', 6.0)
            
            # 計算預期失分
            expected_runs = (era_vs_team / 9) * expected_innings
            
            # 考慮主場優勢 (投手)
            if is_home_pitcher:
                expected_runs *= 0.95  # 主場投手輕微優勢
                predicted_impact['key_factors'].append("主場投手優勢")
            
            # 根據對主要打者的對戰情況調整
            key_batter_assessment = key_batters.get('overall_assessment', 'neutral')
            if key_batter_assessment == 'pitcher_advantage':
                expected_runs *= 0.85
                predicted_impact['key_factors'].append("對主力打線有優勢")
            elif key_batter_assessment == 'batter_advantage':
                expected_runs *= 1.15
                predicted_impact['key_factors'].append("對主力打線處劣勢")
            
            # 計算防守因子
            run_prevention_factor = expected_runs / 4.5  # 相對於MLB平均
            
            predicted_impact.update({
                'expected_runs_allowed': round(expected_runs, 1),
                'innings_likely_pitched': round(expected_innings, 1),
                'run_prevention_factor': round(run_prevention_factor, 3),
                'era_based_prediction': round(era_vs_team, 2)
            })
            
            # 評估信心度
            games_pitched = team_performance.get('games_pitched', 0)
            if games_pitched >= 5:
                predicted_impact['confidence_level'] = 'high'
            elif games_pitched >= 2:
                predicted_impact['confidence_level'] = 'medium'
            else:
                predicted_impact['confidence_level'] = 'low'
                predicted_impact['key_factors'].append("歷史對戰樣本較少")
            
        except Exception as e:
            logger.error(f"計算預測影響失敗: {e}")
            predicted_impact['error'] = str(e)
        
        return predicted_impact
    
    def _generate_matchup_insights(self, team_performance: Dict, mlb_comparison: Dict, key_batters: Dict) -> List[str]:
        """生成對戰洞察"""
        
        insights = []
        
        try:
            # ERA洞察
            era_comparison = mlb_comparison.get('era_vs_mlb', {})
            era_performance = era_comparison.get('performance', 'average')
            era_diff = era_comparison.get('difference', 0)
            
            if era_performance == 'better' and abs(era_diff) >= 1.0:
                insights.append(f"投手對該球隊表現優異，ERA比聯盟平均低{abs(era_diff):.1f}")
            elif era_performance == 'worse' and abs(era_diff) >= 1.0:
                insights.append(f"投手對該球隊表現掙扎，ERA比聯盟平均高{abs(era_diff):.1f}")
            
            # 主要打者洞察
            pitcher_favorable = key_batters.get('pitcher_favorable_matchups', 0)
            batter_favorable = key_batters.get('batter_favorable_matchups', 0)
            total_analyzed = key_batters.get('total_key_batters_analyzed', 0)
            
            if total_analyzed > 0:
                if pitcher_favorable >= 3:
                    insights.append(f"投手對該隊{pitcher_favorable}名主力打者有明顯優勢")
                elif batter_favorable >= 3:
                    insights.append(f"該隊{batter_favorable}名主力打者對投手有優勢")
            
            # 歷史數據品質洞察
            games_pitched = team_performance.get('games_pitched', 0)
            if games_pitched >= 10:
                insights.append(f"基於{games_pitched}場歷史對戰，數據可靠性高")
            elif games_pitched <= 2:
                insights.append("歷史對戰樣本較少，預測不確定性較高")
            
            if not insights:
                insights.append("對戰數據分析顯示雙方實力接近")
            
        except Exception as e:
            logger.error(f"生成洞察失敗: {e}")
            insights.append("對戰分析出現問題，建議以一般表現預測")
        
        return insights
    
    def _assess_analysis_confidence(self, historical_matchups: List, team_performance: Dict, key_batters: Dict) -> str:
        """評估分析信心度"""
        
        confidence_score = 0
        
        # 歷史對戰場次
        games = len(historical_matchups)
        if games >= 8:
            confidence_score += 40
        elif games >= 4:
            confidence_score += 25
        elif games >= 2:
            confidence_score += 10
        
        # 主要打者分析覆蓋度
        key_batters_analyzed = key_batters.get('total_key_batters_analyzed', 0)
        if key_batters_analyzed >= 6:
            confidence_score += 30
        elif key_batters_analyzed >= 3:
            confidence_score += 15
        
        # 數據時效性 (基於最近賽季)
        seasons_analyzed = team_performance.get('seasons_analyzed', 0)
        if seasons_analyzed >= 2:
            confidence_score += 20
        elif seasons_analyzed >= 1:
            confidence_score += 10
        
        # 樣本質量
        total_weight = team_performance.get('total_weight_used', 0)
        if total_weight >= 2.0:
            confidence_score += 10
        
        # 轉換為信心等級
        if confidence_score >= 70:
            return 'high'
        elif confidence_score >= 40:
            return 'medium'
        else:
            return 'low'
    
    def _get_team_id(self, team_code: str) -> int:
        """獲取球隊ID"""
        # 簡化映射，實際應查詢teams表
        team_mapping = {
            'BOS': 111, 'NYY': 147, 'TB': 139, 'TOR': 141, 'BAL': 110,
            'CWS': 145, 'CLE': 114, 'DET': 116, 'KC': 118, 'MIN': 142,
            'HOU': 117, 'LAA': 108, 'OAK': 133, 'SEA': 136, 'TEX': 140,
            'ATL': 144, 'NYM': 121, 'PHI': 143, 'WSH': 120, 'MIA': 146,
            'CHC': 112, 'MIL': 158, 'STL': 138, 'PIT': 134, 'CIN': 113,
            'LAD': 119, 'SD': 135, 'SF': 137, 'COL': 115, 'AZ': 109
        }
        return team_mapping.get(team_code, 1)

# 使用示例
if __name__ == "__main__":
    import asyncio
    
    async def test_pitcher_vs_team_analysis():
        analyzer = PitcherVsTeamAnalyzer()
        
        # 測試投手對特定球隊分析
        result = await analyzer.analyze_pitcher_vs_team(
            pitcher_name="Gerrit Cole",
            opposing_team="BOS", 
            target_date=date(2025, 8, 23),
            is_home_pitcher=False
        )
        
        print("投手對球隊分析結果:")
        print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
    
    asyncio.run(test_pitcher_vs_team_analysis())