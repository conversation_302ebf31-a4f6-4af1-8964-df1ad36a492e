#!/usr/bin/env python3
"""
傷兵數據與預測系統整合模組
"""

import sys
import os
from datetime import date, datetime
from typing import Dict, List

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models.database import db
from models.injury_tracker import InjuryTracker

class InjuryPredictionIntegration:
    """傷兵數據與預測系統整合器"""
    
    def __init__(self):
        self.app = create_app()
        self.injury_tracker = InjuryTracker()
    
    def get_injury_features_for_game(self, home_team_code: str, away_team_code: str) -> Dict:
        """為特定比賽獲取傷兵相關特徵"""
        
        with self.app.app_context():
            from models.database import Team
            
            # 獲取球隊ID
            home_team = db.session.query(Team).filter_by(team_code=home_team_code).first()
            away_team = db.session.query(Team).filter_by(team_code=away_team_code).first()
            
            if not home_team or not away_team:
                return self._get_default_injury_features()
            
            # 獲取兩隊傷兵情況
            home_injuries = self.injury_tracker.get_team_injuries(home_team.team_id)
            away_injuries = self.injury_tracker.get_team_injuries(away_team.team_id)
            
            # 計算傷兵特徵
            injury_features = {
                # 基本傷兵數量
                'home_total_injured': home_injuries['total_injured'],
                'away_total_injured': away_injuries['total_injured'],
                
                # 按嚴重程度分類
                'home_minor_injuries': home_injuries['by_severity']['minor'],
                'home_moderate_injuries': home_injuries['by_severity']['moderate'],
                'home_major_injuries': home_injuries['by_severity']['major'],
                'home_season_ending_injuries': home_injuries['by_severity']['season_ending'],
                
                'away_minor_injuries': away_injuries['by_severity']['minor'],
                'away_moderate_injuries': away_injuries['by_severity']['moderate'],
                'away_major_injuries': away_injuries['by_severity']['major'],
                'away_season_ending_injuries': away_injuries['by_severity']['season_ending'],
                
                # 按位置分類的傷兵數
                'home_injured_pitchers': self._count_injured_by_position(home_injuries, 'Pitcher'),
                'home_injured_catchers': self._count_injured_by_position(home_injuries, 'Catcher'),
                'home_injured_infielders': self._count_injured_by_position(home_injuries, 'Infielder'),
                'home_injured_outfielders': self._count_injured_by_position(home_injuries, 'Outfielder'),
                
                'away_injured_pitchers': self._count_injured_by_position(away_injuries, 'Pitcher'),
                'away_injured_catchers': self._count_injured_by_position(away_injuries, 'Catcher'),
                'away_injured_infielders': self._count_injured_by_position(away_injuries, 'Infielder'),
                'away_injured_outfielders': self._count_injured_by_position(away_injuries, 'Outfielder'),
                
                # 傷兵影響分數
                'home_injury_impact_score': self.injury_tracker.calculate_injury_impact_score(home_injuries),
                'away_injury_impact_score': self.injury_tracker.calculate_injury_impact_score(away_injuries),
                
                # 相對優勢
                'injury_advantage': self._calculate_injury_advantage(home_injuries, away_injuries),
                
                # 關鍵球員傷兵標記
                'home_key_player_injured': self._has_key_player_injured(home_injuries),
                'away_key_player_injured': self._has_key_player_injured(away_injuries),
                
                # 先發投手傷兵風險
                'home_starting_pitcher_risk': self._calculate_starting_pitcher_risk(home_injuries),
                'away_starting_pitcher_risk': self._calculate_starting_pitcher_risk(away_injuries)
            }
            
            return injury_features
    
    def _count_injured_by_position(self, team_injuries: Dict, position_type: str) -> int:
        """計算特定位置的傷兵數量"""
        count = 0
        for player in team_injuries.get('injured_players', []):
            if player['position_type'] == position_type:
                count += 1
        return count
    
    def _calculate_injury_advantage(self, home_injuries: Dict, away_injuries: Dict) -> float:
        """計算傷兵優勢（正值表示主隊優勢，負值表示客隊優勢）"""
        home_impact = self.injury_tracker.calculate_injury_impact_score(home_injuries)
        away_impact = self.injury_tracker.calculate_injury_impact_score(away_injuries)
        
        # 返回客隊影響 - 主隊影響（正值表示主隊優勢）
        return away_impact - home_impact
    
    def _has_key_player_injured(self, team_injuries: Dict) -> int:
        """檢查是否有關鍵球員受傷（1=有，0=無）"""
        for player in team_injuries.get('injured_players', []):
            # 關鍵位置：先發投手、捕手、核心打者
            if (player['position_type'] == 'Pitcher' and 'SP' in player['position_abbr']) or \
               (player['position_type'] == 'Catcher') or \
               (player['severity'] in ['Major', 'Season-Ending']):
                return 1
        return 0
    
    def _calculate_starting_pitcher_risk(self, team_injuries: Dict) -> float:
        """計算先發投手傷兵風險分數"""
        risk_score = 0.0
        
        for player in team_injuries.get('injured_players', []):
            if player['position_type'] == 'Pitcher':
                # 先發投手權重更高
                if 'SP' in player['position_abbr'] or 'Starting' in player['position']:
                    if player['severity'] == 'Major':
                        risk_score += 3.0
                    elif player['severity'] == 'Moderate':
                        risk_score += 2.0
                    elif player['severity'] == 'Minor':
                        risk_score += 1.0
                else:
                    # 牛棚投手
                    if player['severity'] == 'Major':
                        risk_score += 1.5
                    elif player['severity'] == 'Moderate':
                        risk_score += 1.0
                    elif player['severity'] == 'Minor':
                        risk_score += 0.5
        
        return risk_score
    
    def _get_default_injury_features(self) -> Dict:
        """獲取默認的傷兵特徵（當無法獲取數據時）"""
        return {
            'home_total_injured': 0,
            'away_total_injured': 0,
            'home_minor_injuries': 0,
            'home_moderate_injuries': 0,
            'home_major_injuries': 0,
            'home_season_ending_injuries': 0,
            'away_minor_injuries': 0,
            'away_moderate_injuries': 0,
            'away_major_injuries': 0,
            'away_season_ending_injuries': 0,
            'home_injured_pitchers': 0,
            'home_injured_catchers': 0,
            'home_injured_infielders': 0,
            'home_injured_outfielders': 0,
            'away_injured_pitchers': 0,
            'away_injured_catchers': 0,
            'away_injured_infielders': 0,
            'away_injured_outfielders': 0,
            'home_injury_impact_score': 0.0,
            'away_injury_impact_score': 0.0,
            'injury_advantage': 0.0,
            'home_key_player_injured': 0,
            'away_key_player_injured': 0,
            'home_starting_pitcher_risk': 0.0,
            'away_starting_pitcher_risk': 0.0
        }
    
    def get_injury_summary_for_display(self, home_team_code: str, away_team_code: str) -> Dict:
        """獲取用於顯示的傷兵摘要"""
        
        with self.app.app_context():
            from models.database import Team
            
            # 獲取球隊信息
            home_team = db.session.query(Team).filter_by(team_code=home_team_code).first()
            away_team = db.session.query(Team).filter_by(team_code=away_team_code).first()
            
            if not home_team or not away_team:
                return {'error': '無法找到球隊信息'}
            
            # 獲取傷兵數據
            home_injuries = self.injury_tracker.get_team_injuries(home_team.team_id)
            away_injuries = self.injury_tracker.get_team_injuries(away_team.team_id)
            
            # 整理顯示數據
            summary = {
                'home_team': {
                    'name': home_team.team_name,
                    'code': home_team_code,
                    'total_injured': home_injuries['total_injured'],
                    'impact_score': self.injury_tracker.calculate_injury_impact_score(home_injuries),
                    'key_injuries': self._get_key_injuries(home_injuries),
                    'pitcher_injuries': self._count_injured_by_position(home_injuries, 'Pitcher')
                },
                'away_team': {
                    'name': away_team.team_name,
                    'code': away_team_code,
                    'total_injured': away_injuries['total_injured'],
                    'impact_score': self.injury_tracker.calculate_injury_impact_score(away_injuries),
                    'key_injuries': self._get_key_injuries(away_injuries),
                    'pitcher_injuries': self._count_injured_by_position(away_injuries, 'Pitcher')
                },
                'advantage': self._get_advantage_description(home_injuries, away_injuries),
                'impact_level': self._get_overall_impact_level(home_injuries, away_injuries)
            }
            
            return summary
    
    def _get_key_injuries(self, team_injuries: Dict) -> List[Dict]:
        """獲取關鍵傷兵信息"""
        key_injuries = []
        
        for player in team_injuries.get('injured_players', []):
            # 只顯示重要傷兵
            if (player['severity'] in ['Major', 'Season-Ending']) or \
               (player['position_type'] == 'Pitcher' and 'SP' in player['position_abbr']):
                key_injuries.append({
                    'name': player['player_name'],
                    'position': player['position_abbr'],
                    'severity': player['severity'],
                    'status': player['status_description']
                })
        
        return key_injuries[:5]  # 最多顯示5個關鍵傷兵
    
    def _get_advantage_description(self, home_injuries: Dict, away_injuries: Dict) -> str:
        """獲取優勢描述"""
        home_impact = self.injury_tracker.calculate_injury_impact_score(home_injuries)
        away_impact = self.injury_tracker.calculate_injury_impact_score(away_injuries)
        
        difference = abs(home_impact - away_impact)
        
        if difference < 2:
            return "傷兵影響相當"
        elif home_impact > away_impact:
            if difference > 5:
                return "客隊明顯優勢（主隊傷兵較多）"
            else:
                return "客隊輕微優勢"
        else:
            if difference > 5:
                return "主隊明顯優勢（客隊傷兵較多）"
            else:
                return "主隊輕微優勢"
    
    def _get_overall_impact_level(self, home_injuries: Dict, away_injuries: Dict) -> str:
        """獲取整體影響等級"""
        total_impact = (self.injury_tracker.calculate_injury_impact_score(home_injuries) + 
                       self.injury_tracker.calculate_injury_impact_score(away_injuries))
        
        if total_impact < 5:
            return "低影響"
        elif total_impact < 15:
            return "中等影響"
        else:
            return "高影響"
