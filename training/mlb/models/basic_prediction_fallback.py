#!/usr/bin/env python3
"""
基本預測系統回退服務
當增強預測系統不可用時使用
"""

from datetime import date, datetime
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class BasicPredictionFallback:
    """基本預測回退服務"""
    
    def __init__(self):
        logger.info("初始化基本預測回退服務")
    
    def predict_all_games_enhanced(self, target_date: date) -> Dict:
        """模擬增強預測接口"""
        logger.info(f"使用基本預測回退服務進行預測: {target_date}")
        
        # 返回基本格式，表示系統可用但功能有限
        return {
            'success': True,
            'message': '使用基本預測服務（增強功能暫時不可用）',
            'target_date': target_date.isoformat(),
            'predictions': [],
            'total_games': 0,
            'enhanced_features': False,
            'fallback_mode': True
        }
    
    def get_daily_predictions(self, target_date: date) -> Dict:
        """獲取日常預測"""
        return self.predict_all_games_enhanced(target_date)