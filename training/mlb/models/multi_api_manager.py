"""
多 API 管理器 - 自動切換不同的數據源 API
支援 RapidAPI、The Odds API、以及其他數據源的自動切換
"""

import logging
import time
import json
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

logger = logging.getLogger(__name__)

class APIStatus(Enum):
    """API 狀態枚舉"""
    ACTIVE = "active"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"
    DISABLED = "disabled"

@dataclass
class APIConfig:
    """API 配置類"""
    name: str
    api_type: str  # 'rapidapi', 'odds_api', 'custom'
    base_url: str
    api_key: str
    headers: Dict[str, str]
    rate_limit: int  # 每分鐘請求限制
    priority: int  # 優先級 (1=最高)
    status: APIStatus = APIStatus.ACTIVE
    last_error: Optional[str] = None
    last_used: Optional[datetime] = None
    requests_count: int = 0
    requests_reset_time: Optional[datetime] = None

class MultiAPIManager:
    """多 API 管理器"""
    
    def __init__(self):
        self.apis: Dict[str, APIConfig] = {}
        self.session = self._create_session()
        self.load_api_configs()
    
    def _create_session(self) -> requests.Session:
        """創建帶重試機制的 HTTP 會話"""
        session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session
    
    def load_api_configs(self):
        """載入 API 配置"""
        try:
            # The Odds API 配置
            odds_api_key = self._load_odds_api_key()
            if odds_api_key:
                self.add_api_config(APIConfig(
                    name="the_odds_api",
                    api_type="odds_api",
                    base_url="https://api.the-odds-api.com/v4",
                    api_key=odds_api_key,
                    headers={
                        "Content-Type": "application/json"
                    },
                    rate_limit=500,  # 每月 500 credits
                    priority=1
                ))
            
            # RapidAPI MLB 配置 (需要用戶提供 API key)
            rapidapi_key = self._load_rapidapi_key()
            if rapidapi_key:
                self.add_api_config(APIConfig(
                    name="rapidapi_mlb",
                    api_type="rapidapi",
                    base_url="https://major-league-baseball-mlb.p.rapidapi.com",
                    api_key=rapidapi_key,
                    headers={
                        "X-RapidAPI-Key": rapidapi_key,
                        "X-RapidAPI-Host": "major-league-baseball-mlb.p.rapidapi.com"
                    },
                    rate_limit=100,  # 假設每分鐘 100 請求
                    priority=2
                ))
            
            # 備用 API 配置 (可以添加更多)
            self._add_backup_apis()
            
            logger.info(f"已載入 {len(self.apis)} 個 API 配置")
            
        except Exception as e:
            logger.error(f"載入 API 配置失敗: {e}")
    
    def _load_odds_api_key(self) -> Optional[str]:
        """載入 The Odds API 密鑰"""
        try:
            with open('models/odds-api.txt', 'r') as f:
                return f.read().strip()
        except FileNotFoundError:
            logger.warning("未找到 odds-api.txt 文件")
            return None
        except Exception as e:
            logger.error(f"讀取 odds API 密鑰失敗: {e}")
            return None
    
    def _load_rapidapi_key(self) -> Optional[str]:
        """載入 RapidAPI 密鑰"""
        try:
            with open('models/rapidapi-key.txt', 'r') as f:
                return f.read().strip()
        except FileNotFoundError:
            logger.warning("未找到 rapidapi-key.txt 文件，請創建該文件並添加您的 RapidAPI 密鑰")
            return None
        except Exception as e:
            logger.error(f"讀取 RapidAPI 密鑰失敗: {e}")
            return None
    
    def _add_backup_apis(self):
        """添加備用 API 配置"""
        # 可以在這裡添加更多備用 API
        pass
    
    def add_api_config(self, config: APIConfig):
        """添加 API 配置"""
        self.apis[config.name] = config
        logger.info(f"已添加 API 配置: {config.name} (優先級: {config.priority})")
    
    def get_available_apis(self, api_type: Optional[str] = None) -> List[APIConfig]:
        """獲取可用的 API 列表"""
        available = []
        for api in self.apis.values():
            if api.status == APIStatus.ACTIVE:
                if api_type is None or api.api_type == api_type:
                    if self._check_rate_limit(api):
                        available.append(api)
        
        # 按優先級排序
        return sorted(available, key=lambda x: x.priority)
    
    def _check_rate_limit(self, api: APIConfig) -> bool:
        """檢查 API 是否超過速率限制"""
        if api.requests_reset_time and datetime.now() > api.requests_reset_time:
            # 重置計數器
            api.requests_count = 0
            api.requests_reset_time = None
        
        return api.requests_count < api.rate_limit
    
    def _update_api_usage(self, api: APIConfig):
        """更新 API 使用統計"""
        api.last_used = datetime.now()
        api.requests_count += 1
        
        if api.requests_reset_time is None:
            # 設置重置時間 (1小時後)
            api.requests_reset_time = datetime.now().replace(
                minute=0, second=0, microsecond=0
            ).replace(hour=datetime.now().hour + 1)
    
    def _mark_api_error(self, api: APIConfig, error: str):
        """標記 API 錯誤"""
        api.last_error = error
        if "rate limit" in error.lower() or "429" in error:
            api.status = APIStatus.RATE_LIMITED
            logger.warning(f"API {api.name} 達到速率限制")
        else:
            api.status = APIStatus.ERROR
            logger.error(f"API {api.name} 發生錯誤: {error}")
    
    def fetch_mlb_odds(self, target_date: date = None) -> Dict[str, Any]:
        """獲取 MLB 博彩賠率數據 - 自動切換 API"""
        if not target_date:
            target_date = date.today()
        
        available_apis = self.get_available_apis("odds_api")
        
        for api in available_apis:
            try:
                logger.info(f"嘗試使用 API: {api.name}")
                
                if api.name == "the_odds_api":
                    result = self._fetch_from_odds_api(api, target_date)
                else:
                    result = self._fetch_from_custom_api(api, target_date)
                
                if result:
                    self._update_api_usage(api)
                    logger.info(f"成功從 {api.name} 獲取數據")
                    return result
                    
            except Exception as e:
                error_msg = str(e)
                self._mark_api_error(api, error_msg)
                logger.warning(f"API {api.name} 失敗，嘗試下一個: {error_msg}")
                continue
        
        logger.error("所有 API 都無法使用")
        return {"success": False, "error": "所有 API 都無法使用"}
    
    def _fetch_from_odds_api(self, api: APIConfig, target_date: date) -> Dict[str, Any]:
        """從 The Odds API 獲取數據"""
        url = f"{api.base_url}/sports/baseball_mlb/odds"
        params = {
            "apiKey": api.api_key,
            "regions": "us",
            "markets": "h2h,spreads,totals",
            "oddsFormat": "decimal",
            "dateFormat": "iso"
        }
        
        response = self.session.get(url, params=params, headers=api.headers, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        return {
            "success": True,
            "source": api.name,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
    
    def _fetch_from_custom_api(self, api: APIConfig, target_date: date) -> Dict[str, Any]:
        """從自定義 API 獲取數據"""
        # 這裡可以實現其他 API 的數據獲取邏輯
        logger.warning(f"自定義 API {api.name} 尚未實現")
        return None
    
    def get_api_status(self) -> Dict[str, Any]:
        """獲取所有 API 的狀態"""
        status = {}
        for name, api in self.apis.items():
            status[name] = {
                "status": api.status.value,
                "priority": api.priority,
                "requests_count": api.requests_count,
                "rate_limit": api.rate_limit,
                "last_used": api.last_used.isoformat() if api.last_used else None,
                "last_error": api.last_error
            }
        return status
    
    def reset_api_status(self, api_name: str) -> bool:
        """重置 API 狀態"""
        if api_name in self.apis:
            api = self.apis[api_name]
            api.status = APIStatus.ACTIVE
            api.last_error = None
            api.requests_count = 0
            api.requests_reset_time = None
            logger.info(f"已重置 API {api_name} 狀態")
            return True
        return False
