"""
高級特徵選擇器 - 優化特徵選擇和模型集成策略
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from sklearn.feature_selection import (
    SelectKBest, f_regression, mutual_info_regression,
    RFE, RFECV, SelectFromModel
)
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LassoCV, ElasticNetCV
from sklearn.metrics import mean_absolute_error, r2_score
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
import xgboost as xgb
from scipy.stats import spearmanr, pearsonr

logger = logging.getLogger(__name__)

class AdvancedFeatureSelector:
    """高級特徵選擇器"""
    
    def __init__(self):
        self.feature_importance_scores = {}
        self.selected_features = {}
        self.feature_correlations = {}
        self.selection_methods = {
            'univariate_f': SelectKBest(f_regression, k=50),
            'univariate_mi': SelectKBest(mutual_info_regression, k=50),
            'lasso': SelectFromModel(LassoCV(cv=5, random_state=42)),
            'elastic_net': SelectFromModel(ElasticNetCV(cv=5, random_state=42)),
            'random_forest': SelectFromModel(RandomForestRegressor(n_estimators=100, random_state=42)),
            'xgboost': SelectFromModel(xgb.XGBRegressor(random_state=42))
        }
        
    def analyze_feature_importance(self, X: pd.DataFrame, y: pd.Series, 
                                 target_name: str = 'target') -> Dict:
        """全面分析特徵重要性"""
        logger.info(f"分析 {target_name} 的特徵重要性...")
        
        importance_results = {
            'target': target_name,
            'total_features': len(X.columns),
            'methods': {}
        }
        
        # 1. 統計相關性分析
        correlations = self._calculate_correlations(X, y)
        importance_results['methods']['correlation'] = correlations
        
        # 2. 單變量特徵選擇
        univariate_scores = self._univariate_feature_selection(X, y)
        importance_results['methods']['univariate'] = univariate_scores
        
        # 3. 基於模型的特徵重要性
        model_importance = self._model_based_importance(X, y)
        importance_results['methods']['model_based'] = model_importance
        
        # 4. 遞歸特徵消除
        rfe_scores = self._recursive_feature_elimination(X, y)
        importance_results['methods']['rfe'] = rfe_scores
        
        # 5. 綜合評分
        combined_scores = self._combine_importance_scores(importance_results['methods'])
        importance_results['combined_scores'] = combined_scores
        
        # 保存結果
        self.feature_importance_scores[target_name] = importance_results
        
        return importance_results
    
    def _calculate_correlations(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """計算特徵與目標變量的相關性"""
        correlations = {}
        
        for column in X.columns:
            try:
                # Pearson相關係數
                pearson_corr, pearson_p = pearsonr(X[column], y)
                
                # Spearman相關係數
                spearman_corr, spearman_p = spearmanr(X[column], y)
                
                correlations[column] = {
                    'pearson': abs(pearson_corr),
                    'pearson_p': pearson_p,
                    'spearman': abs(spearman_corr),
                    'spearman_p': spearman_p,
                    'avg_correlation': (abs(pearson_corr) + abs(spearman_corr)) / 2
                }
            except Exception as e:
                logger.warning(f"計算 {column} 相關性失敗: {e}")
                correlations[column] = {
                    'pearson': 0, 'pearson_p': 1,
                    'spearman': 0, 'spearman_p': 1,
                    'avg_correlation': 0
                }
        
        return correlations
    
    def _univariate_feature_selection(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """單變量特徵選擇"""
        scores = {}
        
        # F-統計量
        try:
            f_selector = SelectKBest(f_regression, k='all')
            f_selector.fit(X, y)
            f_scores = f_selector.scores_
            
            for i, column in enumerate(X.columns):
                if column not in scores:
                    scores[column] = {}
                scores[column]['f_score'] = f_scores[i] if not np.isnan(f_scores[i]) else 0
        except Exception as e:
            logger.warning(f"F-統計量計算失敗: {e}")
        
        # 互信息
        try:
            mi_selector = SelectKBest(mutual_info_regression, k='all')
            mi_selector.fit(X, y)
            mi_scores = mi_selector.scores_
            
            for i, column in enumerate(X.columns):
                if column not in scores:
                    scores[column] = {}
                scores[column]['mutual_info'] = mi_scores[i] if not np.isnan(mi_scores[i]) else 0
        except Exception as e:
            logger.warning(f"互信息計算失敗: {e}")
        
        return scores
    
    def _model_based_importance(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """基於模型的特徵重要性"""
        importance_scores = {}
        
        # Random Forest重要性
        try:
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)
            rf_importance = rf.feature_importances_
            
            for i, column in enumerate(X.columns):
                if column not in importance_scores:
                    importance_scores[column] = {}
                importance_scores[column]['random_forest'] = rf_importance[i]
        except Exception as e:
            logger.warning(f"Random Forest重要性計算失敗: {e}")
        
        # XGBoost重要性
        try:
            xgb_model = xgb.XGBRegressor(random_state=42)
            xgb_model.fit(X, y)
            xgb_importance = xgb_model.feature_importances_
            
            for i, column in enumerate(X.columns):
                if column not in importance_scores:
                    importance_scores[column] = {}
                importance_scores[column]['xgboost'] = xgb_importance[i]
        except Exception as e:
            logger.warning(f"XGBoost重要性計算失敗: {e}")
        
        # Lasso係數
        try:
            lasso = LassoCV(cv=5, random_state=42)
            lasso.fit(X, y)
            lasso_coef = np.abs(lasso.coef_)
            
            for i, column in enumerate(X.columns):
                if column not in importance_scores:
                    importance_scores[column] = {}
                importance_scores[column]['lasso'] = lasso_coef[i]
        except Exception as e:
            logger.warning(f"Lasso係數計算失敗: {e}")
        
        return importance_scores
    
    def _recursive_feature_elimination(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """遞歸特徵消除"""
        rfe_scores = {}
        
        try:
            # 使用Random Forest進行RFE
            estimator = RandomForestRegressor(n_estimators=50, random_state=42)
            rfe = RFECV(estimator, step=1, cv=TimeSeriesSplit(n_splits=3), 
                       scoring='neg_mean_absolute_error', n_jobs=-1)
            rfe.fit(X, y)
            
            for i, column in enumerate(X.columns):
                rfe_scores[column] = {
                    'selected': rfe.support_[i],
                    'ranking': rfe.ranking_[i],
                    'score': 1.0 / rfe.ranking_[i]  # 排名越高分數越高
                }
        except Exception as e:
            logger.warning(f"RFE計算失敗: {e}")
            for column in X.columns:
                rfe_scores[column] = {'selected': True, 'ranking': 1, 'score': 1.0}
        
        return rfe_scores
    
    def _combine_importance_scores(self, methods_results: Dict) -> Dict:
        """綜合多種方法的重要性評分"""
        combined_scores = {}
        
        # 獲取所有特徵名稱
        all_features = set()
        for method_results in methods_results.values():
            if isinstance(method_results, dict):
                all_features.update(method_results.keys())
        
        # 為每個特徵計算綜合評分
        for feature in all_features:
            scores = []
            
            # 相關性評分
            if 'correlation' in methods_results and feature in methods_results['correlation']:
                corr_score = methods_results['correlation'][feature]['avg_correlation']
                scores.append(corr_score)
            
            # 單變量評分
            if 'univariate' in methods_results and feature in methods_results['univariate']:
                uni_data = methods_results['univariate'][feature]
                f_score = uni_data.get('f_score', 0)
                mi_score = uni_data.get('mutual_info', 0)
                # 標準化評分
                uni_score = (f_score / 100 + mi_score) / 2  # 簡單標準化
                scores.append(uni_score)
            
            # 模型重要性評分
            if 'model_based' in methods_results and feature in methods_results['model_based']:
                model_data = methods_results['model_based'][feature]
                rf_score = model_data.get('random_forest', 0)
                xgb_score = model_data.get('xgboost', 0)
                lasso_score = model_data.get('lasso', 0)
                model_score = (rf_score + xgb_score + lasso_score) / 3
                scores.append(model_score)
            
            # RFE評分
            if 'rfe' in methods_results and feature in methods_results['rfe']:
                rfe_score = methods_results['rfe'][feature]['score']
                scores.append(rfe_score)
            
            # 計算綜合評分
            if scores:
                combined_scores[feature] = {
                    'individual_scores': scores,
                    'combined_score': np.mean(scores),
                    'score_std': np.std(scores),
                    'confidence': 1 - (np.std(scores) / (np.mean(scores) + 1e-8))
                }
            else:
                combined_scores[feature] = {
                    'individual_scores': [0],
                    'combined_score': 0,
                    'score_std': 0,
                    'confidence': 0
                }
        
        return combined_scores
    
    def select_optimal_features(self, target_name: str, top_k: int = 30, 
                              min_confidence: float = 0.3) -> List[str]:
        """選擇最優特徵"""
        if target_name not in self.feature_importance_scores:
            raise ValueError(f"尚未分析 {target_name} 的特徵重要性")
        
        combined_scores = self.feature_importance_scores[target_name]['combined_scores']
        
        # 過濾低信心度特徵
        filtered_features = {
            feature: data for feature, data in combined_scores.items()
            if data['confidence'] >= min_confidence
        }
        
        # 按綜合評分排序
        sorted_features = sorted(
            filtered_features.items(),
            key=lambda x: x[1]['combined_score'],
            reverse=True
        )
        
        # 選擇前top_k個特徵
        selected = [feature for feature, _ in sorted_features[:top_k]]
        
        self.selected_features[target_name] = selected
        
        logger.info(f"{target_name} 選擇了 {len(selected)} 個特徵 (從 {len(combined_scores)} 個中選擇)")
        
        return selected
    
    def get_feature_selection_report(self, target_name: str) -> Dict:
        """獲取特徵選擇報告"""
        if target_name not in self.feature_importance_scores:
            return {'error': f'尚未分析 {target_name} 的特徵重要性'}
        
        results = self.feature_importance_scores[target_name]
        selected = self.selected_features.get(target_name, [])
        
        # 生成前10個最重要特徵的詳細報告
        combined_scores = results['combined_scores']
        top_features = sorted(
            combined_scores.items(),
            key=lambda x: x[1]['combined_score'],
            reverse=True
        )[:10]
        
        return {
            'target': target_name,
            'total_features_analyzed': results['total_features'],
            'features_selected': len(selected),
            'top_10_features': [
                {
                    'feature': feature,
                    'score': data['combined_score'],
                    'confidence': data['confidence'],
                    'selected': feature in selected
                }
                for feature, data in top_features
            ],
            'selection_summary': {
                'avg_score_selected': np.mean([
                    combined_scores[f]['combined_score'] for f in selected
                ]) if selected else 0,
                'avg_confidence_selected': np.mean([
                    combined_scores[f]['confidence'] for f in selected
                ]) if selected else 0
            }
        }
