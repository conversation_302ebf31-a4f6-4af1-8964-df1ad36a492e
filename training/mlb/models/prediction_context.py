#!/usr/bin/env python3
"""
預測上下文管理系統
統一管理預測過程中的日期、參數和狀態傳遞，解決日期邏輯錯誤問題
"""

import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from flask import current_app
import json

logger = logging.getLogger(__name__)

@dataclass
class PredictionContext:
    """預測上下文 - 統一管理預測過程中的所有參數"""
    
    # 核心參數
    game_id: str
    target_date: date
    request_date: date = field(default_factory=date.today)
    
    # 比賽基本信息
    home_team: Optional[str] = None
    away_team: Optional[str] = None
    venue: Optional[str] = None
    
    # 預測模式
    prediction_mode: str = "historical"  # historical, live, quick
    use_historical_optimization: bool = True
    enable_pitcher_analysis: bool = True
    enable_weather_factors: bool = True
    
    # 歷史數據範圍
    historical_seasons: List[int] = field(default_factory=lambda: list(range(2019, 2026)))
    apply_covid_adjustments: bool = True
    
    # 投手信息
    home_starting_pitcher: Optional[str] = None
    away_starting_pitcher: Optional[str] = None
    pitcher_data_source: Optional[str] = None
    
    # 博彩信息
    betting_line_total: Optional[float] = None
    betting_spread: Optional[float] = None
    
    # 置信度要求
    minimum_confidence: float = 0.6
    require_pitcher_data: bool = False
    
    # 執行狀態
    execution_id: str = field(default_factory=lambda: f"pred_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    created_at: datetime = field(default_factory=datetime.now)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """初始化後的驗證和設定"""
        # 驗證日期邏輯
        if self.target_date > self.request_date:
            self.warnings.append(f"目標日期 {self.target_date} 晚於請求日期 {self.request_date}，這是未來預測")
        
        # 設定歷史預測模式
        if (self.request_date - self.target_date).days > 1:
            self.prediction_mode = "historical"
            logger.info(f"設定為歷史預測模式: {self.target_date}")
        elif self.target_date == self.request_date:
            self.prediction_mode = "live"
            logger.info(f"設定為即時預測模式: {self.target_date}")
        else:
            self.prediction_mode = "future"
            logger.info(f"設定為未來預測模式: {self.target_date}")
    
    def is_historical_prediction(self) -> bool:
        """判斷是否為歷史預測"""
        return self.prediction_mode == "historical"
    
    def is_live_prediction(self) -> bool:
        """判斷是否為即時預測"""
        return self.prediction_mode == "live"
    
    def is_future_prediction(self) -> bool:
        """判斷是否為未來預測"""
        return self.prediction_mode == "future"
    
    def get_season(self) -> int:
        """獲取目標日期對應的MLB賽季"""
        # MLB賽季通常從3月開始
        if self.target_date.month >= 3:
            return self.target_date.year
        else:
            return self.target_date.year - 1
    
    def add_error(self, error: str):
        """添加錯誤信息"""
        self.errors.append(f"[{datetime.now().strftime('%H:%M:%S')}] {error}")
        logger.error(error)
    
    def add_warning(self, warning: str):
        """添加警告信息"""
        self.warnings.append(f"[{datetime.now().strftime('%H:%M:%S')}] {warning}")
        logger.warning(warning)
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'game_id': self.game_id,
            'target_date': self.target_date.isoformat(),
            'request_date': self.request_date.isoformat(),
            'home_team': self.home_team,
            'away_team': self.away_team,
            'venue': self.venue,
            'prediction_mode': self.prediction_mode,
            'season': self.get_season(),
            'execution_id': self.execution_id,
            'home_starting_pitcher': self.home_starting_pitcher,
            'away_starting_pitcher': self.away_starting_pitcher,
            'pitcher_data_source': self.pitcher_data_source,
            'betting_line_total': self.betting_line_total,
            'betting_spread': self.betting_spread,
            'use_historical_optimization': self.use_historical_optimization,
            'apply_covid_adjustments': self.apply_covid_adjustments,
            'historical_seasons': self.historical_seasons,
            'errors_count': len(self.errors),
            'warnings_count': len(self.warnings),
            'created_at': self.created_at.isoformat()
        }

class PredictionContextManager:
    """預測上下文管理器"""
    
    def __init__(self, app=None):
        self.app = app or current_app
        self.active_contexts: Dict[str, PredictionContext] = {}
        
    def create_context(self, 
                      game_id: str, 
                      target_date: Union[date, str, None] = None,
                      **kwargs) -> PredictionContext:
        """
        創建預測上下文
        
        Args:
            game_id: 比賽ID
            target_date: 目標日期，可以是date對象、字符串或None
            **kwargs: 其他上下文參數
        """
        
        # 處理日期參數
        if target_date is None:
            # ❌ 舊邏輯: target_date = date.today()
            # ✅ 新邏輯: 從game_id推斷日期或使用明確的歷史日期
            parsed_date = self._extract_date_from_game_id(game_id)
            if parsed_date:
                target_date = parsed_date
                logger.info(f"從game_id {game_id} 推斷目標日期: {target_date}")
            else:
                target_date = date.today()
                logger.warning(f"無法從game_id推斷日期，使用當前日期: {target_date}")
                
        elif isinstance(target_date, str):
            try:
                target_date = datetime.strptime(target_date, '%Y-%m-%d').date()
            except ValueError:
                logger.error(f"無效的日期格式: {target_date}")
                target_date = date.today()
        
        # 創建上下文
        context = PredictionContext(
            game_id=game_id,
            target_date=target_date,
            **kwargs
        )
        
        # 填充比賽基本信息
        self._populate_game_info(context)
        
        # 儲存上下文
        self.active_contexts[context.execution_id] = context
        
        logger.info(f"創建預測上下文: {context.execution_id} | 比賽: {game_id} | 日期: {target_date}")
        
        return context
    
    def get_context(self, execution_id: str) -> Optional[PredictionContext]:
        """獲取預測上下文"""
        return self.active_contexts.get(execution_id)
    
    def _extract_date_from_game_id(self, game_id: str) -> Optional[date]:
        """從game_id提取日期"""
        try:
            # 處理不同的game_id格式
            if len(game_id) >= 8 and game_id[:8].isdigit():
                # YYYYMMDD格式
                date_str = game_id[:8]
                return datetime.strptime(date_str, '%Y%m%d').date()
            elif game_id.startswith('777'):
                # 特殊格式 777xxx (假設為2025年某日)
                # 這裡需要根據實際的game_id格式規則調整
                return date(2025, 7, 7)  # 示例
            else:
                # 嘗試從數據庫查詢
                from models.database import Game
                with self.app.app_context():
                    game = Game.query.filter_by(game_id=game_id).first()
                    if game:
                        return game.date
                        
        except Exception as e:
            logger.error(f"從game_id提取日期失敗: {e}")
            
        return None
    
    def _populate_game_info(self, context: PredictionContext):
        """填充比賽基本信息"""
        try:
            from models.database import Game, GameDetail
            from models.starting_pitcher_tracker import StartingPitcherTracker
            
            with self.app.app_context():
                # 獲取比賽基本信息
                game = Game.query.filter_by(game_id=context.game_id).first()
                if game:
                    context.home_team = game.home_team
                    context.away_team = game.away_team
                    context.venue = game.venue
                
                # 獲取投手信息
                pitcher_tracker = StartingPitcherTracker()
                pitcher_info = pitcher_tracker.get_starting_pitchers(context.game_id)
                
                if pitcher_info:
                    context.home_starting_pitcher = pitcher_info['home_starting_pitcher']
                    context.away_starting_pitcher = pitcher_info['away_starting_pitcher']
                    context.pitcher_data_source = pitcher_info['data_source']
                    
                    logger.info(f"找到投手信息: {context.away_starting_pitcher} vs {context.home_starting_pitcher}")
                else:
                    context.add_warning("未找到先發投手信息")
                
        except Exception as e:
            context.add_error(f"填充比賽信息失敗: {e}")
    
    def cleanup_old_contexts(self, max_age_hours: int = 24):
        """清理過期的上下文"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        old_contexts = [
            execution_id for execution_id, ctx in self.active_contexts.items()
            if ctx.created_at < cutoff_time
        ]
        
        for execution_id in old_contexts:
            del self.active_contexts[execution_id]
        
        if old_contexts:
            logger.info(f"清理了 {len(old_contexts)} 個過期上下文")

# 全局上下文管理器實例
context_manager = PredictionContextManager()

def create_prediction_context(game_id: str, target_date: Union[date, str, None] = None, **kwargs) -> PredictionContext:
    """創建預測上下文的便捷函數"""
    return context_manager.create_context(game_id, target_date, **kwargs)

# 使用示例和測試
if __name__ == "__main__":
    # 測試歷史預測上下文
    historical_context = create_prediction_context(
        game_id="20250823_BOS_NYY",
        target_date="2025-08-23",  # 明確指定歷史日期
        prediction_mode="historical",
        use_historical_optimization=True
    )
    
    print("歷史預測上下文:")
    print(json.dumps(historical_context.to_dict(), indent=2, ensure_ascii=False))
    
    # 測試即時預測上下文
    live_context = create_prediction_context(
        game_id="20250827_TOR_MIA",
        target_date=None,  # 將自動設定為今天
        prediction_mode="live"
    )
    
    print("\n即時預測上下文:")
    print(json.dumps(live_context.to_dict(), indent=2, ensure_ascii=False))