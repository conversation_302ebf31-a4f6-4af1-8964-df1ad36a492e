#!/usr/bin/env python3
"""
賠率數據抓取器
專門抓取讓分盤 (spreads) 和大小分 (totals) 賠率數據
"""

import requests
import logging
import os
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
from flask import current_app

from .database import db, Game, BettingOdds

logger = logging.getLogger(__name__)

class OddsDataFetcher:
    """賠率數據抓取器"""
    
    def __init__(self, app=None):
        self.app = app or current_app
        
        # The Odds API 配置
        self.odds_api_key = None
        self.odds_api_base_url = "https://api.the-odds-api.com/v4"
        
        # 載入API密鑰
        self._load_api_key()
        
        # 支持的市場
        self.target_markets = ['spreads', 'totals']  # 讓分盤和大小分
        
        # 支持的博彩商
        self.bookmakers = ['draftkings', 'fanduel', 'betmgm']
        
    def _load_api_key(self):
        """載入API密鑰"""
        # 嘗試從環境變量獲取
        self.odds_api_key = os.getenv('ODDS_API_KEY')
        
        # 如果環境變量沒有，嘗試從文件讀取
        if not self.odds_api_key:
            try:
                api_key_file = os.path.join(os.path.dirname(__file__), 'odds-api.txt')
                if os.path.exists(api_key_file):
                    with open(api_key_file, 'r') as f:
                        self.odds_api_key = f.read().strip()
                        logger.info("從 odds-api.txt 文件讀取API密鑰")
            except Exception as e:
                logger.warning(f"讀取API密鑰文件失敗: {e}")
        
        if not self.odds_api_key:
            logger.warning("未設置賠率API密鑰，將無法獲取真實賠率數據")
    
    def fetch_odds_for_games(self, target_date: date = None) -> Dict:
        """
        抓取指定日期的比賽賠率數據
        
        Args:
            target_date: 目標日期，默認為今天
            
        Returns:
            包含賠率數據的字典
        """
        if target_date is None:
            target_date = date.today()
        
        logger.info(f"開始抓取 {target_date} 的賠率數據...")
        
        if not self.odds_api_key:
            logger.error("沒有API密鑰，無法抓取賠率數據")
            return {'success': False, 'error': '沒有API密鑰'}
        
        try:
            # 獲取該日期的比賽
            games = Game.query.filter(Game.date == target_date).all()
            
            if not games:
                logger.info(f"{target_date} 沒有比賽記錄")
                return {'success': False, 'error': '沒有比賽記錄'}
            
            logger.info(f"找到 {len(games)} 場比賽")
            
            # 抓取賠率數據
            odds_data = self._fetch_mlb_odds()
            
            if not odds_data:
                return {'success': False, 'error': '無法獲取賠率數據'}
            
            # 匹配比賽和賠率
            matched_data = self._match_games_with_odds(games, odds_data, target_date)
            
            return {
                'success': True,
                'date': target_date.isoformat(),
                'total_games': len(games),
                'matched_games': len(matched_data['games']),
                'games': matched_data['games'],
                'summary': matched_data['summary']
            }
            
        except Exception as e:
            logger.error(f"抓取賠率數據失敗: {e}")
            return {'success': False, 'error': str(e)}
    
    def _fetch_mlb_odds(self) -> Optional[List]:
        """從 The Odds API 獲取 MLB 賠率數據"""
        try:
            url = f"{self.odds_api_base_url}/sports/baseball_mlb/odds"
            
            params = {
                'apiKey': self.odds_api_key,
                'regions': 'us',  # 美國地區
                'markets': ','.join(self.target_markets),  # spreads,totals
                'oddsFormat': 'american',  # 美式賠率
                'bookmakers': ','.join(self.bookmakers)
            }
            
            logger.info(f"請求賠率API: {url}")
            logger.info(f"參數: markets={params['markets']}, bookmakers={params['bookmakers']}")
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"成功獲取 {len(data)} 場比賽的賠率數據")
            
            # 檢查API配額
            remaining = response.headers.get('x-requests-remaining')
            if remaining:
                logger.info(f"剩餘API請求次數: {remaining}")
            
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API請求失敗: {e}")
            return None
        except Exception as e:
            logger.error(f"處理賠率數據失敗: {e}")
            return None
    
    def _match_games_with_odds(self, games: List[Game], odds_data: List, target_date: date) -> Dict:
        """匹配比賽和賠率數據"""
        matched_games = []
        unmatched_games = []
        
        # 創建球隊名稱映射
        team_mapping = self._create_team_mapping()
        
        for game in games:
            # 查找對應的賠率數據
            game_odds = self._find_odds_for_game(game, odds_data, team_mapping)
            
            if game_odds:
                matched_games.append({
                    'game_id': game.game_id,
                    'home_team': game.home_team,
                    'away_team': game.away_team,
                    'game_time': game.game_time.isoformat() if game.game_time else None,
                    'odds': game_odds
                })
            else:
                unmatched_games.append({
                    'game_id': game.game_id,
                    'home_team': game.home_team,
                    'away_team': game.away_team
                })
        
        summary = {
            'total_games': len(games),
            'matched_count': len(matched_games),
            'unmatched_count': len(unmatched_games),
            'match_rate': len(matched_games) / len(games) * 100 if games else 0,
            'available_markets': self.target_markets,
            'bookmakers': self.bookmakers
        }
        
        logger.info(f"匹配結果: {len(matched_games)}/{len(games)} 場比賽 ({summary['match_rate']:.1f}%)")
        
        return {
            'games': matched_games,
            'unmatched_games': unmatched_games,
            'summary': summary
        }
    
    def _find_odds_for_game(self, game: Game, odds_data: List, team_mapping: Dict) -> Optional[Dict]:
        """為特定比賽查找賠率數據"""
        # 數據庫中的球隊是縮寫格式 (如 STL, CLE)
        # API返回的是全名格式 (如 St. Louis Cardinals, Cleveland Guardians)
        # 需要反向查找：從API全名找到對應的縮寫

        for odds_game in odds_data:
            odds_home = odds_game.get('home_team', '')
            odds_away = odds_game.get('away_team', '')

            # 查找API球隊名稱對應的縮寫
            mapped_home = team_mapping.get(odds_home)
            mapped_away = team_mapping.get(odds_away)

            # 檢查是否匹配數據庫中的球隊縮寫
            home_match = (mapped_home == game.home_team) if mapped_home else False
            away_match = (mapped_away == game.away_team) if mapped_away else False
            
            if home_match and away_match:
                # 提取賠率信息
                return self._extract_odds_info(odds_game)
        
        return None
    
    def _extract_odds_info(self, odds_game: Dict) -> Dict:
        """提取賠率信息"""
        odds_info = {
            'game_id': odds_game.get('id'),
            'commence_time': odds_game.get('commence_time'),
            'spreads': {},
            'totals': {},
            'bookmakers': []
        }
        
        for bookmaker in odds_game.get('bookmakers', []):
            bookmaker_name = bookmaker.get('key')
            bookmaker_info = {
                'name': bookmaker_name,
                'title': bookmaker.get('title'),
                'last_update': bookmaker.get('last_update'),
                'markets': {}
            }
            
            for market in bookmaker.get('markets', []):
                market_key = market.get('key')
                outcomes = market.get('outcomes', [])
                
                if market_key == 'spreads':
                    # 讓分盤
                    spreads_data = {}
                    for outcome in outcomes:
                        team = outcome.get('name')
                        point = outcome.get('point')
                        price = outcome.get('price')
                        spreads_data[team] = {'point': point, 'price': price}
                    
                    bookmaker_info['markets']['spreads'] = spreads_data
                    odds_info['spreads'][bookmaker_name] = spreads_data
                
                elif market_key == 'totals':
                    # 大小分
                    totals_data = {}
                    for outcome in outcomes:
                        name = outcome.get('name')  # Over/Under
                        point = outcome.get('point')
                        price = outcome.get('price')
                        totals_data[name] = {'point': point, 'price': price}
                    
                    bookmaker_info['markets']['totals'] = totals_data
                    odds_info['totals'][bookmaker_name] = totals_data
            
            odds_info['bookmakers'].append(bookmaker_info)
        
        return odds_info
    
    def _create_team_mapping(self) -> Dict:
        """創建球隊名稱映射表 - API全名到數據庫縮寫的映射"""
        return {
            # 美國聯盟東區
            'New York Yankees': 'NYY',
            'Yankees': 'NYY',
            'Boston Red Sox': 'BOS',
            'Red Sox': 'BOS',
            'Toronto Blue Jays': 'TOR',
            'Blue Jays': 'TOR',
            'Baltimore Orioles': 'BAL',
            'Orioles': 'BAL',
            'Tampa Bay Rays': 'TB',
            'Rays': 'TB',

            # 美國聯盟中區
            'Chicago White Sox': 'CWS',
            'White Sox': 'CWS',
            'Cleveland Guardians': 'CLE',
            'Guardians': 'CLE',
            'Detroit Tigers': 'DET',
            'Tigers': 'DET',
            'Kansas City Royals': 'KC',
            'Royals': 'KC',
            'Minnesota Twins': 'MIN',
            'Twins': 'MIN',

            # 美國聯盟西區
            'Houston Astros': 'HOU',
            'Astros': 'HOU',
            'Los Angeles Angels': 'LAA',
            'Angels': 'LAA',
            'Oakland Athletics': 'ATH',  # 映射到數據庫中的 ATH
            'Athletics': 'ATH',
            'A\'s': 'ATH',
            'Oakland A\'s': 'ATH',
            'Seattle Mariners': 'SEA',
            'Mariners': 'SEA',
            'Texas Rangers': 'TEX',
            'Rangers': 'TEX',

            # 國家聯盟東區
            'Atlanta Braves': 'ATL',
            'Braves': 'ATL',
            'Miami Marlins': 'MIA',
            'Marlins': 'MIA',
            'New York Mets': 'NYM',
            'Mets': 'NYM',
            'Philadelphia Phillies': 'PHI',
            'Phillies': 'PHI',
            'Washington Nationals': 'WSH',
            'Nationals': 'WSH',

            # 國家聯盟中區
            'Chicago Cubs': 'CHC',
            'Cubs': 'CHC',
            'Cincinnati Reds': 'CIN',
            'Reds': 'CIN',
            'Milwaukee Brewers': 'MIL',
            'Brewers': 'MIL',
            'Pittsburgh Pirates': 'PIT',
            'Pirates': 'PIT',
            'St. Louis Cardinals': 'STL',
            'Cardinals': 'STL',

            # 國家聯盟西區
            'Arizona Diamondbacks': 'AZ',
            'Diamondbacks': 'AZ',
            'Colorado Rockies': 'COL',
            'Rockies': 'COL',
            'Los Angeles Dodgers': 'LAD',
            'Dodgers': 'LAD',
            'San Diego Padres': 'SD',
            'Padres': 'SD',
            'San Francisco Giants': 'SF',
            'Giants': 'SF'
        }
    
    def check_api_status(self) -> Dict:
        """檢查API狀態"""
        status = {
            'api_key_configured': bool(self.odds_api_key),
            'api_accessible': False,
            'remaining_requests': None,
            'last_check': datetime.now().isoformat()
        }
        
        if not self.odds_api_key:
            status['message'] = '未配置API密鑰'
            return status
        
        try:
            # 測試API連接
            url = f"{self.odds_api_base_url}/sports"
            params = {'apiKey': self.odds_api_key}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            status['api_accessible'] = True
            status['remaining_requests'] = response.headers.get('x-requests-remaining')
            status['message'] = 'API正常運行'
            
        except Exception as e:
            status['message'] = f'API連接失敗: {str(e)}'
        
        return status

    def save_odds_to_database(self, odds_result: Dict) -> Dict:
        """將賠率數據保存到數據庫"""
        if not odds_result.get('success'):
            return {'success': False, 'error': '賠率數據無效'}

        try:
            saved_count = 0
            updated_count = 0

            for game_data in odds_result.get('games', []):
                game_id = game_data['game_id']
                odds_info = game_data['odds']

                # 保存讓分盤數據
                if odds_info.get('spreads'):
                    for bookmaker, spreads_data in odds_info['spreads'].items():
                        result = self._save_spreads_odds(game_id, bookmaker, spreads_data)
                        if result['created']:
                            saved_count += 1
                        else:
                            updated_count += 1

                # 保存大小分數據
                if odds_info.get('totals'):
                    for bookmaker, totals_data in odds_info['totals'].items():
                        result = self._save_totals_odds(game_id, bookmaker, totals_data)
                        if result['created']:
                            saved_count += 1
                        else:
                            updated_count += 1

            db.session.commit()

            logger.info(f"賠率數據保存完成: 新增 {saved_count} 筆, 更新 {updated_count} 筆")

            return {
                'success': True,
                'saved_count': saved_count,
                'updated_count': updated_count,
                'total_processed': saved_count + updated_count
            }

        except Exception as e:
            db.session.rollback()
            logger.error(f"保存賠率數據失敗: {e}")
            return {'success': False, 'error': str(e)}

    def _save_spreads_odds(self, game_id: str, bookmaker: str, spreads_data: Dict) -> Dict:
        """保存讓分盤數據"""
        try:
            # 檢查是否已存在
            existing = BettingOdds.query.filter_by(
                game_id=game_id,
                bookmaker=bookmaker,
                market_type='spreads'
            ).first()

            # 提取讓分數據
            home_data = None
            away_data = None

            for team, data in spreads_data.items():
                if data.get('point', 0) > 0:  # 正數通常是客隊讓分
                    away_data = data
                else:  # 負數是主隊讓分
                    home_data = data

            if existing:
                # 更新現有記錄
                if home_data:
                    existing.home_spread_point = home_data.get('point')
                    existing.home_spread_price = home_data.get('price')
                if away_data:
                    existing.away_spread_point = away_data.get('point')
                    existing.away_spread_price = away_data.get('price')
                existing.last_update = datetime.now()

                return {'created': False, 'updated': True}
            else:
                # 創建新記錄
                odds_record = BettingOdds(
                    game_id=game_id,
                    bookmaker=bookmaker,
                    market_type='spreads',
                    home_spread_point=home_data.get('point') if home_data else None,
                    home_spread_price=home_data.get('price') if home_data else None,
                    away_spread_point=away_data.get('point') if away_data else None,
                    away_spread_price=away_data.get('price') if away_data else None,
                    odds_time=datetime.now(),
                    last_update=datetime.now()
                )

                db.session.add(odds_record)
                return {'created': True, 'updated': False}

        except Exception as e:
            logger.error(f"保存讓分盤數據失敗: {e}")
            raise

    def _save_totals_odds(self, game_id: str, bookmaker: str, totals_data: Dict) -> Dict:
        """保存大小分數據"""
        try:
            # 檢查是否已存在
            existing = BettingOdds.query.filter_by(
                game_id=game_id,
                bookmaker=bookmaker,
                market_type='totals'
            ).first()

            # 提取大小分數據
            over_data = totals_data.get('Over', {})
            under_data = totals_data.get('Under', {})

            total_point = over_data.get('point') or under_data.get('point')
            over_price = over_data.get('price')
            under_price = under_data.get('price')

            if existing:
                # 更新現有記錄
                existing.total_point = total_point
                existing.over_price = over_price
                existing.under_price = under_price
                existing.last_update = datetime.now()

                return {'created': False, 'updated': True}
            else:
                # 創建新記錄
                odds_record = BettingOdds(
                    game_id=game_id,
                    bookmaker=bookmaker,
                    market_type='totals',
                    total_point=total_point,
                    over_price=over_price,
                    under_price=under_price,
                    odds_time=datetime.now(),
                    last_update=datetime.now()
                )

                db.session.add(odds_record)
                return {'created': True, 'updated': False}

        except Exception as e:
            logger.error(f"保存大小分數據失敗: {e}")
            raise

    def get_odds_for_game(self, game_id: str) -> Dict:
        """獲取特定比賽的賠率數據"""
        try:
            odds_records = BettingOdds.query.filter_by(game_id=game_id).all()

            if not odds_records:
                return {'success': False, 'error': '沒有找到賠率數據'}

            # 組織數據
            spreads = {}
            totals = {}

            for record in odds_records:
                if record.market_type == 'spreads':
                    spreads[record.bookmaker] = {
                        'home_spread_point': record.home_spread_point,
                        'home_spread_price': record.home_spread_price,
                        'away_spread_point': record.away_spread_point,
                        'away_spread_price': record.away_spread_price,
                        'last_update': record.last_update.isoformat() if record.last_update else None
                    }
                elif record.market_type == 'totals':
                    totals[record.bookmaker] = {
                        'total_point': record.total_point,
                        'over_price': record.over_price,
                        'under_price': record.under_price,
                        'last_update': record.last_update.isoformat() if record.last_update else None
                    }

            return {
                'success': True,
                'game_id': game_id,
                'spreads': spreads,
                'totals': totals,
                'bookmakers': list(set([r.bookmaker for r in odds_records]))
            }

        except Exception as e:
            logger.error(f"獲取比賽賠率數據失敗: {e}")
            return {'success': False, 'error': str(e)}
