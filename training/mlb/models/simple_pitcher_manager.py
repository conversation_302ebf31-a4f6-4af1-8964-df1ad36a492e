#!/usr/bin/env python3
"""
簡單投手管理器
預先下載並存儲投手信息，提供快速預測邏輯
"""

import logging
from datetime import date
from typing import Dict, Optional, Tuple

logger = logging.getLogger(__name__)

class SimplePitcherManager:
    """簡單投手管理器"""
    
    def __init__(self):
        pass
    
    def get_starting_pitchers_simple(self, game_id: str, home_team: str, away_team: str) -> <PERSON><PERSON>[Dict, Dict]:
        """
        簡單獲取先發投手信息
        
        Returns:
            (home_pitcher, away_pitcher) - 兩個投手信息字典
        """
        try:
            from models.database import db, PlayerGameStats, Team
            
            # 獲取球隊ID
            home_team_obj = Team.query.filter_by(team_code=home_team).first()
            away_team_obj = Team.query.filter_by(team_code=away_team).first()
            
            if not home_team_obj or not away_team_obj:
                return self._get_default_pitchers(home_team, away_team)
            
            # 獲取主隊先發投手（投球局數最多的）
            home_pitcher_stat = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.team_id == home_team_obj.team_id,
                PlayerGameStats.position == 'P',
                PlayerGameStats.innings_pitched > 0
            ).order_by(PlayerGameStats.innings_pitched.desc()).first()
            
            # 獲取客隊先發投手
            away_pitcher_stat = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.team_id == away_team_obj.team_id,
                PlayerGameStats.position == 'P',
                PlayerGameStats.innings_pitched > 0
            ).order_by(PlayerGameStats.innings_pitched.desc()).first()
            
            # 處理主隊投手
            if home_pitcher_stat:
                home_era = (home_pitcher_stat.earned_runs * 9.0) / home_pitcher_stat.innings_pitched if home_pitcher_stat.innings_pitched > 0 else 4.50
                home_pitcher = {
                    'name': f"投手#{home_pitcher_stat.player_id}",
                    'era': home_era,
                    'innings': home_pitcher_stat.innings_pitched,
                    'earned_runs': home_pitcher_stat.earned_runs,
                    'quality': self._era_to_quality(home_era)
                }
            else:
                home_pitcher = self._get_default_pitcher(home_team)
            
            # 處理客隊投手
            if away_pitcher_stat:
                away_era = (away_pitcher_stat.earned_runs * 9.0) / away_pitcher_stat.innings_pitched if away_pitcher_stat.innings_pitched > 0 else 4.50
                away_pitcher = {
                    'name': f"投手#{away_pitcher_stat.player_id}",
                    'era': away_era,
                    'innings': away_pitcher_stat.innings_pitched,
                    'earned_runs': away_pitcher_stat.earned_runs,
                    'quality': self._era_to_quality(away_era)
                }
            else:
                away_pitcher = self._get_default_pitcher(away_team)
            
            logger.info(f"✅ 找到先發投手: {home_team}={home_pitcher['name']}(ERA:{home_pitcher['era']:.2f}) vs {away_team}={away_pitcher['name']}(ERA:{away_pitcher['era']:.2f})")
            
            return home_pitcher, away_pitcher
            
        except Exception as e:
            logger.error(f"❌ 獲取先發投手失敗: {e}")
            return self._get_default_pitchers(home_team, away_team)
    
    def predict_game_simple_logic(self, home_pitcher: Dict, away_pitcher: Dict) -> Dict:
        """
        簡單預測邏輯 - 根據投手質量判斷最好/最差情況
        
        邏輯：
        1. 王牌投手 (ERA ≤ 2.50) → 低分比賽
        2. 弱勢投手 (ERA > 5.00) → 高分比賽  
        3. 混合情況 → 中等分數
        """
        try:
            home_era = home_pitcher['era']
            away_era = away_pitcher['era']
            
            # 分類投手等級
            home_level = self._classify_pitcher_level(home_era)
            away_level = self._classify_pitcher_level(away_era)
            
            # 預測邏輯
            if home_level == "王牌" and away_level == "王牌":
                # 王牌對決 - 低分比賽
                scenario = "王牌對決"
                predicted_total = 6.5
                home_score = 3.5
                away_score = 3.0
                confidence = 0.85
                
            elif home_level == "弱勢" and away_level == "弱勢":
                # 打擊戰 - 高分比賽
                scenario = "打擊戰"
                predicted_total = 12.5
                home_score = 7.0
                away_score = 5.5
                confidence = 0.80
                
            elif (home_level == "王牌" and away_level == "弱勢") or (home_level == "弱勢" and away_level == "王牌"):
                # 強弱對戰 - 不平衡
                scenario = "強弱對戰"
                if home_level == "王牌":
                    predicted_total = 8.5
                    home_score = 5.5
                    away_score = 3.0
                else:
                    predicted_total = 8.5
                    home_score = 3.0
                    away_score = 5.5
                confidence = 0.75
                
            else:
                # 普通對戰
                scenario = "普通對戰"
                predicted_total = 9.0
                home_score = 5.0
                away_score = 4.0
                confidence = 0.65
            
            # 主場優勢
            home_score += 0.3
            
            result = {
                'scenario': scenario,
                'predicted_home_score': round(home_score, 1),
                'predicted_away_score': round(away_score, 1),
                'predicted_total': round(predicted_total, 1),
                'confidence': confidence,
                'pitcher_analysis': {
                    'home_pitcher': {
                        'name': home_pitcher['name'],
                        'era': home_pitcher['era'],
                        'level': home_level
                    },
                    'away_pitcher': {
                        'name': away_pitcher['name'],
                        'era': away_pitcher['era'],
                        'level': away_level
                    }
                },
                'logic_explanation': self._get_logic_explanation(scenario, home_level, away_level)
            }
            
            logger.info(f"🎯 預測結果: {scenario} - {away_score:.1f}:{home_score:.1f} (總分:{predicted_total:.1f})")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 簡單預測邏輯失敗: {e}")
            return self._get_default_prediction()
    
    def _classify_pitcher_level(self, era: float) -> str:
        """分類投手等級"""
        if era <= 2.50:
            return "王牌"
        elif era <= 3.50:
            return "優秀"
        elif era <= 4.50:
            return "普通"
        else:
            return "弱勢"
    
    def _era_to_quality(self, era: float) -> float:
        """ERA轉換為質量分數"""
        return max(0, 100 - (era - 2.0) * 20)
    
    def _get_logic_explanation(self, scenario: str, home_level: str, away_level: str) -> str:
        """獲取邏輯說明"""
        explanations = {
            "王牌對決": f"兩位王牌投手對決，預期低分比賽。主隊:{home_level} vs 客隊:{away_level}",
            "打擊戰": f"兩位弱勢投手，預期高分打擊戰。主隊:{home_level} vs 客隊:{away_level}",
            "強弱對戰": f"強弱投手對戰，分數不平衡。主隊:{home_level} vs 客隊:{away_level}",
            "普通對戰": f"普通投手對戰，標準分數。主隊:{home_level} vs 客隊:{away_level}"
        }
        return explanations.get(scenario, f"主隊:{home_level} vs 客隊:{away_level}")
    
    def _get_default_pitcher(self, team: str) -> Dict:
        """獲取默認投手"""
        return {
            'name': f'{team} 投手',
            'era': 4.50,
            'innings': 6.0,
            'earned_runs': 3,
            'quality': 50.0
        }
    
    def _get_default_pitchers(self, home_team: str, away_team: str) -> Tuple[Dict, Dict]:
        """獲取默認投手對"""
        return self._get_default_pitcher(home_team), self._get_default_pitcher(away_team)
    
    def _get_default_prediction(self) -> Dict:
        """獲取默認預測"""
        return {
            'scenario': '普通對戰',
            'predicted_home_score': 5.0,
            'predicted_away_score': 4.0,
            'predicted_total': 9.0,
            'confidence': 0.60,
            'pitcher_analysis': {
                'home_pitcher': {'name': '未知投手', 'era': 4.50, 'level': '普通'},
                'away_pitcher': {'name': '未知投手', 'era': 4.50, 'level': '普通'}
            },
            'logic_explanation': '使用默認預測邏輯'
        }
    
    def batch_extract_pitchers(self, game_ids: list) -> Dict:
        """
        批量提取投手信息並存儲
        為回測準備數據
        """
        try:
            from models.database import db, Game
            
            results = {
                'success': 0,
                'failed': 0,
                'pitchers': {}
            }
            
            for game_id in game_ids:
                try:
                    # 獲取比賽信息
                    game = Game.query.filter_by(game_id=game_id).first()
                    if not game:
                        results['failed'] += 1
                        continue
                    
                    # 提取投手信息
                    home_pitcher, away_pitcher = self.get_starting_pitchers_simple(
                        game_id, game.home_team, game.away_team
                    )
                    
                    # 存儲結果
                    results['pitchers'][game_id] = {
                        'home_team': game.home_team,
                        'away_team': game.away_team,
                        'date': game.date.isoformat() if game.date else None,
                        'home_pitcher': home_pitcher,
                        'away_pitcher': away_pitcher
                    }
                    
                    results['success'] += 1
                    
                except Exception as e:
                    logger.error(f"❌ 提取比賽 {game_id} 投手失敗: {e}")
                    results['failed'] += 1
            
            logger.info(f"📊 批量提取完成: 成功 {results['success']}, 失敗 {results['failed']}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 批量提取投手失敗: {e}")
            return {'success': 0, 'failed': len(game_ids), 'pitchers': {}}

# 創建全局實例
simple_pitcher_manager = SimplePitcherManager()
