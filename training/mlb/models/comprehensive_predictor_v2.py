#!/usr/bin/env python3
"""
全面整合預測器 V2.0
解決歷史統計權重過高、當日打線權重過低、投手對戰數據不足的問題
"""

import logging
import numpy as np
import pandas as pd
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
from flask import current_app

from models.database import db, Game, PlayerStats, Prediction
from models.daily_lineup_fetcher import DailyLineupFetcher
from models.batting_order_analyzer import BattingOrderAnalyzer
from models.pitcher_batter_matchups import PitcherBatterMatchupAnalyzer
from models.pitcher_name_matcher import PitcherNameMatcher
from models.injury_prediction_integration import InjuryPredictionIntegration

logger = logging.getLogger(__name__)

class ComprehensivePredictorV2:
    """全面整合預測器 - 重點解決當日數據權重問題"""
    
    def __init__(self, app=None):
        self.app = app or current_app
        
        # 初始化所有組件
        self.lineup_fetcher = DailyLineupFetcher()
        self.batting_analyzer = BattingOrderAnalyzer()
        self.matchup_analyzer = PitcherBatterMatchupAnalyzer()
        self.pitcher_matcher = PitcherNameMatcher()
        self.injury_integration = InjuryPredictionIntegration()
        
        # 🎯 權重配置 - 解決權重問題
        self.feature_weights = {
            # 當日實際數據 (高權重)
            'starting_pitcher_era': 0.25,      # 先發投手 ERA
            'lineup_depth_score': 0.20,        # 打線深度分數
            'pitcher_vs_batter': 0.15,         # 投手對打者歷史
            'injury_impact': 0.10,              # 傷兵影響
            
            # 近期表現 (中權重)
            'recent_5_games': 0.15,             # 最近5場表現
            'recent_pitcher_form': 0.10,        # 投手近期狀態
            
            # 歷史統計 (低權重)
            'season_stats': 0.05               # 賽季統計 (大幅降低)
        }
        
        logger.info("全面整合預測器 V2.0 初始化完成")
    
    def predict_game_comprehensive(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """使用全面數據進行比賽預測"""
        try:
            logger.info(f"🎯 開始全面預測: {away_team} @ {home_team} ({game_date})")
            
            # 1. 獲取當日實際數據 (最高權重)
            daily_data = self._get_daily_actual_data(home_team, away_team, game_date)
            
            # 2. 分析先發投手對戰
            pitcher_analysis = self._analyze_starting_pitchers(daily_data, game_date)
            
            # 3. 分析打線配置
            lineup_analysis = self._analyze_batting_lineups(daily_data, game_date)
            
            # 4. 投手對打者歷史分析
            matchup_analysis = self._analyze_pitcher_batter_matchups(daily_data)
            
            # 5. 傷兵影響分析
            injury_analysis = self._analyze_injury_impact(home_team, away_team, game_date)
            
            # 6. 近期表現分析 (中權重)
            recent_analysis = self._analyze_recent_performance(home_team, away_team, game_date)
            
            # 7. 整合所有分析結果
            prediction_result = self._integrate_all_analyses(
                daily_data, pitcher_analysis, lineup_analysis, 
                matchup_analysis, injury_analysis, recent_analysis
            )
            
            # 8. 計算信心度 (基於數據完整性)
            confidence = self._calculate_data_completeness_confidence(
                daily_data, pitcher_analysis, lineup_analysis, matchup_analysis
            )
            
            prediction_result['confidence'] = confidence
            prediction_result['prediction_method'] = 'comprehensive_v2'
            prediction_result['data_sources'] = self._get_data_sources_summary(daily_data)
            
            logger.info(f"✅ 全面預測完成: {prediction_result['predicted_away_score']:.1f} - {prediction_result['predicted_home_score']:.1f} (信心度: {confidence:.1%})")
            
            return prediction_result
            
        except Exception as e:
            logger.error(f"全面預測失敗 {away_team} @ {home_team}: {e}")
            return self._get_fallback_prediction(home_team, away_team, game_date)
    
    def _get_daily_actual_data(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取當日實際數據 (最高權重)"""
        try:
            # 獲取當日打線和投手信息
            daily_lineups = self.lineup_fetcher.get_daily_lineups(game_date)
            
            # 查找目標比賽
            target_game = None
            for game_info in daily_lineups.get('games', []):
                if (game_info.get('away_team') == away_team and 
                    game_info.get('home_team') == home_team):
                    target_game = game_info
                    break
            
            if not target_game:
                logger.warning(f"未找到當日比賽數據: {away_team} @ {home_team}")
                return {'has_daily_data': False}
            
            return {
                'has_daily_data': True,
                'home_pitcher': target_game.get('home_pitcher'),
                'away_pitcher': target_game.get('away_pitcher'),
                'home_lineup': target_game.get('home_lineup', []),
                'away_lineup': target_game.get('away_lineup', []),
                'lineup_confirmed': target_game.get('home_lineup_confirmed', False) and target_game.get('away_lineup_confirmed', False)
            }
            
        except Exception as e:
            logger.error(f"獲取當日數據失敗: {e}")
            return {'has_daily_data': False}
    
    def _analyze_starting_pitchers(self, daily_data: Dict, game_date: date) -> Dict:
        """分析先發投手 (高權重)"""
        analysis = {
            'home_pitcher_era': None,
            'away_pitcher_era': None,
            'home_pitcher_whip': None,
            'away_pitcher_whip': None,
            'pitcher_advantage': 'neutral',
            'era_difference': 0.0,
            'has_pitcher_data': False
        }
        
        try:
            if not daily_data.get('has_daily_data'):
                return analysis
            
            home_pitcher = daily_data.get('home_pitcher')
            away_pitcher = daily_data.get('away_pitcher')
            
            if not home_pitcher or not away_pitcher:
                return analysis
            
            # 獲取投手統計數據
            with self.app.app_context():
                # 主隊投手
                home_pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.player_name.ilike(f'%{home_pitcher}%'),
                    PlayerStats.innings_pitched > 30  # 確保是先發投手
                ).first()
                
                # 客隊投手
                away_pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.player_name.ilike(f'%{away_pitcher}%'),
                    PlayerStats.innings_pitched > 30
                ).first()
                
                if home_pitcher_stats and away_pitcher_stats:
                    analysis.update({
                        'home_pitcher_era': home_pitcher_stats.era,
                        'away_pitcher_era': away_pitcher_stats.era,
                        'home_pitcher_whip': home_pitcher_stats.whip,
                        'away_pitcher_whip': away_pitcher_stats.whip,
                        'has_pitcher_data': True
                    })
                    
                    # 計算投手優勢
                    era_diff = away_pitcher_stats.era - home_pitcher_stats.era
                    analysis['era_difference'] = era_diff
                    
                    if era_diff > 1.0:
                        analysis['pitcher_advantage'] = 'home'  # 主隊投手更好
                    elif era_diff < -1.0:
                        analysis['pitcher_advantage'] = 'away'  # 客隊投手更好
                    else:
                        analysis['pitcher_advantage'] = 'neutral'
                        
                    logger.info(f"投手分析: {away_pitcher} (ERA {away_pitcher_stats.era:.2f}) vs {home_pitcher} (ERA {home_pitcher_stats.era:.2f})")
        
        except Exception as e:
            logger.error(f"投手分析失敗: {e}")
        
        return analysis
    
    def _analyze_batting_lineups(self, daily_data: Dict, game_date: date) -> Dict:
        """分析打線配置 (高權重)"""
        analysis = {
            'home_lineup_depth': 0.0,
            'away_lineup_depth': 0.0,
            'lineup_advantage': 'neutral',
            'has_lineup_data': False
        }
        
        try:
            if not daily_data.get('has_daily_data'):
                return analysis
            
            home_lineup = daily_data.get('home_lineup', [])
            away_lineup = daily_data.get('away_lineup', [])
            
            if not home_lineup or not away_lineup:
                return analysis
            
            # 使用打線分析器計算深度分數
            home_depth = self.batting_analyzer._calculate_lineup_depth_score(home_lineup)
            away_depth = self.batting_analyzer._calculate_lineup_depth_score(away_lineup)
            
            analysis.update({
                'home_lineup_depth': home_depth,
                'away_lineup_depth': away_depth,
                'has_lineup_data': True
            })
            
            # 計算打線優勢
            depth_diff = home_depth - away_depth
            if depth_diff > 10:
                analysis['lineup_advantage'] = 'home'
            elif depth_diff < -10:
                analysis['lineup_advantage'] = 'away'
            else:
                analysis['lineup_advantage'] = 'neutral'
            
            logger.info(f"打線分析: 主隊深度 {home_depth:.1f} vs 客隊深度 {away_depth:.1f}")
            
        except Exception as e:
            logger.error(f"打線分析失敗: {e}")
        
        return analysis
    
    def _analyze_pitcher_batter_matchups(self, daily_data: Dict) -> Dict:
        """分析投手對打者歷史 (中權重)"""
        analysis = {
            'home_pitcher_vs_away_batters': 0.0,
            'away_pitcher_vs_home_batters': 0.0,
            'matchup_advantage': 'neutral',
            'has_matchup_data': False
        }
        
        try:
            # 這裡需要實現投手對打者的詳細分析
            # 由於數據複雜性，暫時返回基礎分析
            logger.info("投手對打者分析: 需要進一步實現")
            
        except Exception as e:
            logger.error(f"投手對打者分析失敗: {e}")
        
        return analysis
    
    def _analyze_injury_impact(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """分析傷兵影響 (中權重)"""
        try:
            return self.injury_integration.get_injury_features_for_game(
                home_team, away_team, game_date
            )
        except Exception as e:
            logger.error(f"傷兵分析失敗: {e}")
            return {'has_injury_data': False}
    
    def _analyze_recent_performance(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """分析近期表現 (中權重)"""
        analysis = {
            'home_recent_avg': 0.0,
            'away_recent_avg': 0.0,
            'home_recent_trend': 'stable',
            'away_recent_trend': 'stable'
        }
        
        try:
            with self.app.app_context():
                # 獲取最近5場比賽
                cutoff_date = game_date - timedelta(days=15)
                
                # 主隊最近表現
                home_recent = Game.query.filter(
                    ((Game.home_team == home_team) | (Game.away_team == home_team)),
                    Game.date >= cutoff_date,
                    Game.date < game_date,
                    Game.game_status == 'completed',
                    Game.home_score.isnot(None),
                    Game.away_score.isnot(None)
                ).order_by(Game.date.desc()).limit(5).all()
                
                if home_recent:
                    home_scores = []
                    for game in home_recent:
                        total_score = game.home_score + game.away_score
                        home_scores.append(total_score)
                    
                    analysis['home_recent_avg'] = sum(home_scores) / len(home_scores)
                
                # 客隊最近表現
                away_recent = Game.query.filter(
                    ((Game.home_team == away_team) | (Game.away_team == away_team)),
                    Game.date >= cutoff_date,
                    Game.date < game_date,
                    Game.game_status == 'completed',
                    Game.home_score.isnot(None),
                    Game.away_score.isnot(None)
                ).order_by(Game.date.desc()).limit(5).all()
                
                if away_recent:
                    away_scores = []
                    for game in away_recent:
                        total_score = game.home_score + game.away_score
                        away_scores.append(total_score)
                    
                    analysis['away_recent_avg'] = sum(away_scores) / len(away_scores)
                
        except Exception as e:
            logger.error(f"近期表現分析失敗: {e}")
        
        return analysis
