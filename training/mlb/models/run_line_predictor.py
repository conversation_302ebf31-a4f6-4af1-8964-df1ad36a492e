"""
MLB讓分盤預測系統
基於真實博彩盤口和球隊實力分析進行讓分盤預測
"""

import logging
from datetime import date, datetime
from typing import Dict, List
from flask import current_app

from models.database import db, Game, Prediction, PlayerStats, TeamStats, Team
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from models.pitcher_name_matcher import PitcherNameMatcher
from models.daily_lineup_fetcher import DailyLineupFetcher

logger = logging.getLogger(__name__)

class RunLinePredictor:
    """讓分盤預測器 - 基於真實博彩盤口和球隊實力分析"""
    
    def __init__(self, app=None):
        self.app = app or current_app
        self.betting_odds_fetcher = RealBettingOddsFetcher(app)
        self.pitcher_matcher = PitcherNameMatcher()
        self.lineup_fetcher = DailyLineupFetcher()

        # 默認讓分設定
        self.default_run_line = 1.5  # MLB標準讓分

        # 添加緩存機制避免重複下載
        self._odds_cache = {}  # 格式: {date_str: odds_data}
        self._cache_expiry = {}  # 格式: {date_str: expiry_time}
    
    def predict_run_line(self, game_id: str, target_date: date = None) -> Dict:
        """預測比賽的讓分盤"""
        try:
            if target_date is None:
                target_date = date.today()
            
            logger.info(f"開始預測比賽 {game_id} 的讓分盤...")
            
            # 1. 獲取比賽信息
            with self.app.app_context():
                game = Game.query.filter_by(game_id=game_id).first()
                if not game:
                    raise ValueError(f"找不到比賽 {game_id}")
            
            # 2. 獲取真實博彩盤口
            real_odds = self._get_real_run_line_odds(game)
            
            # 3. 分析球隊實力對比
            team_analysis = self._analyze_team_strength(game)
            
            # 4. 分析投手對比
            pitcher_analysis = self._analyze_pitcher_matchup(game, target_date)
            
            # 5. 計算讓分盤預測
            run_line_prediction = self._calculate_run_line_prediction(
                real_odds, team_analysis, pitcher_analysis, game
            )
            
            # 6. 生成預測結果
            prediction_result = {
                'game_id': game_id,
                'teams': {
                    'home': game.home_team,
                    'away': game.away_team
                },
                'run_line_data': real_odds,
                'team_analysis': team_analysis,
                'pitcher_analysis': pitcher_analysis,
                'prediction': run_line_prediction,
                'prediction_timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"讓分盤預測完成: {run_line_prediction['recommendation']}")
            return prediction_result
            
        except Exception as e:
            logger.error(f"讓分盤預測失敗 {game_id}: {e}")
            return {'error': str(e)}
    
    def _get_real_run_line_odds(self, game: Game) -> Dict:
        """獲取真實讓分盤賠率（帶緩存機制）"""
        try:
            from datetime import datetime, timedelta

            # 檢查緩存
            date_str = game.date.strftime('%Y-%m-%d') if game.date else date.today().strftime('%Y-%m-%d')
            current_time = datetime.now()

            # 如果緩存存在且未過期，直接返回緩存數據
            if (date_str in self._odds_cache and
                date_str in self._cache_expiry and
                current_time < self._cache_expiry[date_str]):

                logger.info(f"使用緩存的讓分盤數據: {date_str}")
                cached_odds = self._odds_cache[date_str]

                # 從緩存中查找對應比賽
                for odds_data in cached_odds:
                    if (odds_data.get('home_team') == game.home_team and
                        odds_data.get('away_team') == game.away_team):
                        return odds_data.get('run_line', {})

            # 緩存不存在或已過期，重新獲取
            logger.info(f"獲取新的讓分盤數據: {date_str}")
            real_odds = self.betting_odds_fetcher.get_game_odds_by_teams(
                game.home_team, game.away_team, game.date
            )

            # 更新緩存
            if real_odds:
                self._odds_cache[date_str] = real_odds if isinstance(real_odds, list) else [real_odds]
                self._cache_expiry[date_str] = current_time + timedelta(hours=1)  # 緩存1小時

            if real_odds and real_odds.get('run_line'):
                run_line_data = real_odds['run_line']

                return {
                    'source': 'real_bookmaker',
                    'bookmaker': run_line_data.get('bookmaker', 'Unknown'),
                    'home_line': run_line_data.get('home_line'),
                    'home_odds': run_line_data.get('home_odds'),
                    'away_line': run_line_data.get('away_line'),
                    'away_odds': run_line_data.get('away_odds'),
                    'is_real_data': True
                }
            else:
                # 使用默認讓分
                logger.info("無法獲取真實讓分盤，使用默認設定")
                return {
                    'source': 'default',
                    'bookmaker': 'Simulated',
                    'home_line': -self.default_run_line,
                    'home_odds': -110,
                    'away_line': self.default_run_line,
                    'away_odds': -110,
                    'is_real_data': False
                }
                
        except Exception as e:
            logger.error(f"獲取讓分盤賠率失敗: {e}")
            return {
                'source': 'error',
                'bookmaker': 'Error',
                'home_line': -self.default_run_line,
                'home_odds': -110,
                'away_line': self.default_run_line,
                'away_odds': -110,
                'is_real_data': False,
                'error': str(e)
            }
    
    def _analyze_team_strength(self, game: Game) -> Dict:
        """分析球隊實力對比"""
        try:
            with self.app.app_context():
                # 獲取球隊信息和統計數據
                home_team = Team.query.filter_by(team_code=game.home_team).first()
                away_team = Team.query.filter_by(team_code=game.away_team).first()

                home_stats = None
                away_stats = None

                if home_team:
                    home_stats = TeamStats.query.filter_by(team_id=home_team.team_id).order_by(TeamStats.season.desc()).first()

                if away_team:
                    away_stats = TeamStats.query.filter_by(team_id=away_team.team_id).order_by(TeamStats.season.desc()).first()
            
            analysis = {
                'home_team': game.home_team,
                'away_team': game.away_team,
                'strength_comparison': {},
                'key_factors': []
            }
            
            if home_stats and away_stats:
                # 比較關鍵指標
                home_win_pct = getattr(home_stats, 'win_percentage', 0.5)
                away_win_pct = getattr(away_stats, 'win_percentage', 0.5)
                
                home_runs_scored = getattr(home_stats, 'runs_scored', 0)
                away_runs_scored = getattr(away_stats, 'runs_scored', 0)
                
                home_runs_allowed = getattr(home_stats, 'runs_allowed', 0)
                away_runs_allowed = getattr(away_stats, 'runs_allowed', 0)
                
                analysis['strength_comparison'] = {
                    'win_percentage': {
                        'home': home_win_pct,
                        'away': away_win_pct,
                        'advantage': 'home' if home_win_pct > away_win_pct else 'away'
                    },
                    'offensive_power': {
                        'home': home_runs_scored,
                        'away': away_runs_scored,
                        'advantage': 'home' if home_runs_scored > away_runs_scored else 'away'
                    },
                    'defensive_strength': {
                        'home': home_runs_allowed,
                        'away': away_runs_allowed,
                        'advantage': 'home' if home_runs_allowed < away_runs_allowed else 'away'
                    }
                }
                
                # 生成關鍵因素
                if abs(home_win_pct - away_win_pct) > 0.1:
                    better_team = 'home' if home_win_pct > away_win_pct else 'away'
                    analysis['key_factors'].append(f"{better_team}隊勝率明顯較高")
                
                if abs(home_runs_scored - away_runs_scored) > 50:
                    better_offense = 'home' if home_runs_scored > away_runs_scored else 'away'
                    analysis['key_factors'].append(f"{better_offense}隊攻擊力較強")
                
                if abs(home_runs_allowed - away_runs_allowed) > 50:
                    better_defense = 'home' if home_runs_allowed < away_runs_allowed else 'away'
                    analysis['key_factors'].append(f"{better_defense}隊防守較佳")
            
            else:
                analysis['key_factors'].append("球隊統計數據不完整")
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析球隊實力失敗: {e}")
            return {
                'home_team': game.home_team,
                'away_team': game.away_team,
                'strength_comparison': {},
                'key_factors': ['球隊實力分析失敗'],
                'error': str(e)
            }
    
    def _analyze_pitcher_matchup(self, game: Game, target_date: date) -> Dict:
        """分析投手對比"""
        try:
            # 獲取先發投手信息
            lineup_data = self.lineup_fetcher.get_daily_lineups(target_date)
            
            pitcher_analysis = {
                'home_pitcher': {'name': '未確認', 'stats': {}},
                'away_pitcher': {'name': '未確認', 'stats': {}},
                'matchup_advantage': 'neutral',
                'key_factors': []
            }
            
            # 查找比賽的投手信息
            for game_lineup in lineup_data.get('games', []):
                if game_lineup.get('game_id') == game.game_id:
                    home_pitcher = game_lineup.get('home_pitcher')
                    away_pitcher = game_lineup.get('away_pitcher')
                    
                    if home_pitcher:
                        pitcher_analysis['home_pitcher']['name'] = home_pitcher
                        pitcher_analysis['home_pitcher']['stats'] = self._get_pitcher_stats(home_pitcher)
                    
                    if away_pitcher:
                        pitcher_analysis['away_pitcher']['name'] = away_pitcher
                        pitcher_analysis['away_pitcher']['stats'] = self._get_pitcher_stats(away_pitcher)
                    
                    break
            
            # 比較投手實力
            home_era = pitcher_analysis['home_pitcher']['stats'].get('era', 4.50)
            away_era = pitcher_analysis['away_pitcher']['stats'].get('era', 4.50)
            
            if abs(home_era - away_era) > 1.0:
                if home_era < away_era:
                    pitcher_analysis['matchup_advantage'] = 'home'
                    pitcher_analysis['key_factors'].append(f"主隊投手ERA較低 ({home_era:.2f} vs {away_era:.2f})")
                else:
                    pitcher_analysis['matchup_advantage'] = 'away'
                    pitcher_analysis['key_factors'].append(f"客隊投手ERA較低 ({away_era:.2f} vs {home_era:.2f})")
            
            return pitcher_analysis
            
        except Exception as e:
            logger.error(f"分析投手對比失敗: {e}")
            return {
                'home_pitcher': {'name': '未確認', 'stats': {}},
                'away_pitcher': {'name': '未確認', 'stats': {}},
                'matchup_advantage': 'neutral',
                'key_factors': ['投手對比分析失敗'],
                'error': str(e)
            }
    
    def _get_pitcher_stats(self, pitcher_name: str) -> Dict:
        """獲取投手統計數據"""
        try:
            # 使用投手姓名匹配器查找投手
            matches = self.pitcher_matcher.find_pitcher_matches(pitcher_name)
            
            if not matches:
                return {'era': 4.50, 'wins': 0, 'losses': 0}
            
            pitcher_id = matches[0]['player_id']
            
            with self.app.app_context():
                # 獲取投手統計
                pitcher_stats = PlayerStats.query.filter_by(
                    player_id=pitcher_id
                ).order_by(PlayerStats.id.desc()).first()
                
                if pitcher_stats:
                    return {
                        'era': getattr(pitcher_stats, 'era', 4.50),
                        'wins': getattr(pitcher_stats, 'wins', 0),
                        'losses': getattr(pitcher_stats, 'losses', 0),
                        'strikeouts': getattr(pitcher_stats, 'strikeouts_pitching', 0),
                        'innings_pitched': getattr(pitcher_stats, 'innings_pitched', 0)
                    }
                else:
                    return {'era': 4.50, 'wins': 0, 'losses': 0}
                    
        except Exception as e:
            logger.error(f"獲取投手統計失敗 {pitcher_name}: {e}")
            return {'era': 4.50, 'wins': 0, 'losses': 0}
    
    def _calculate_run_line_prediction(self, real_odds: Dict, team_analysis: Dict, 
                                     pitcher_analysis: Dict, game: Game) -> Dict:
        """計算讓分盤預測"""
        try:
            home_line = real_odds.get('home_line', -1.5)
            away_line = real_odds.get('away_line', 1.5)
            
            # 基礎分析
            prediction = {
                'home_line': home_line,
                'away_line': away_line,
                'home_cover_probability': 0.5,
                'away_cover_probability': 0.5,
                'confidence': 0.5,
                'recommendation': '中性',
                'key_factors': []
            }
            
            # 根據球隊實力調整概率
            strength_factors = team_analysis.get('strength_comparison', {})
            
            home_advantages = 0
            away_advantages = 0
            
            for factor, data in strength_factors.items():
                if data.get('advantage') == 'home':
                    home_advantages += 1
                elif data.get('advantage') == 'away':
                    away_advantages += 1
            
            # 根據投手對比調整
            pitcher_advantage = pitcher_analysis.get('matchup_advantage', 'neutral')
            if pitcher_advantage == 'home':
                home_advantages += 1
            elif pitcher_advantage == 'away':
                away_advantages += 1
            
            # 計算概率
            total_factors = home_advantages + away_advantages
            if total_factors > 0:
                home_prob = (home_advantages / total_factors) * 0.3 + 0.5  # 最多調整30%
                away_prob = 1 - home_prob
                
                prediction['home_cover_probability'] = round(home_prob, 3)
                prediction['away_cover_probability'] = round(away_prob, 3)
                
                # 計算信心度
                prediction['confidence'] = round(abs(home_prob - 0.5) * 2, 3)
            
            # 生成推薦
            if prediction['home_cover_probability'] > 0.6:
                prediction['recommendation'] = f"推薦主隊讓分 ({home_line})"
            elif prediction['away_cover_probability'] > 0.6:
                prediction['recommendation'] = f"推薦客隊受讓 ({away_line})"
            else:
                prediction['recommendation'] = "中性，不推薦投注"
            
            # 整合關鍵因素
            prediction['key_factors'] = (
                team_analysis.get('key_factors', []) + 
                pitcher_analysis.get('key_factors', [])
            )
            
            return prediction
            
        except Exception as e:
            logger.error(f"計算讓分盤預測失敗: {e}")
            return {
                'home_line': -1.5,
                'away_line': 1.5,
                'home_cover_probability': 0.5,
                'away_cover_probability': 0.5,
                'confidence': 0.5,
                'recommendation': '預測失敗',
                'key_factors': ['讓分盤預測計算失敗'],
                'error': str(e)
            }


def main():
    """測試讓分盤預測器"""
    predictor = RunLinePredictor()
    
    print("=" * 80)
    print("🎯 MLB讓分盤預測器測試")
    print("=" * 80)
    
    # 測試預測功能
    from models.database import Game
    
    with predictor.app.app_context():
        # 獲取今日比賽
        today_games = Game.query.filter(
            Game.date == date.today(),
            Game.game_status.in_(['scheduled', 'pre-game'])
        ).limit(3).all()
        
        for game in today_games:
            print(f"\n🏟️  測試比賽: {game.away_team} @ {game.home_team}")
            
            result = predictor.predict_run_line(game.game_id)
            
            if 'error' in result:
                print(f"❌ 預測失敗: {result['error']}")
            else:
                prediction = result['prediction']
                print(f"讓分盤: 主隊 {prediction['home_line']} | 客隊 {prediction['away_line']}")
                print(f"推薦: {prediction['recommendation']}")
                print(f"信心度: {prediction['confidence']:.1%}")


if __name__ == "__main__":
    main()
