#!/usr/bin/env python3
"""
投手詳細統計系統
提供投手的多年統計、最近表現、對戰記錄等詳細信息
"""

import logging
import requests
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional
from sqlalchemy import text
from models.database import db, Game, Team, PlayerStats, BoxScore
import json

logger = logging.getLogger(__name__)

class PitcherDetailedStats:
    """投手詳細統計分析器"""
    
    def __init__(self):
        self.base_url = "https://statsapi.mlb.com/api/v1"
        
    def get_pitcher_comprehensive_stats(self, pitcher_name: str, pitcher_id: Optional[int] = None) -> Dict:
        """獲取投手的全面統計信息"""
        logger.info(f"獲取投手 {pitcher_name} 的詳細統計...")
        
        pitcher_stats = {
            'basic_info': {},
            'career_stats': {},
            'recent_performance': {},
            'season_by_season': [],
            'vs_teams_stats': {},
            'vs_batters_stats': {},
            'situational_stats': {},
            'error': None
        }
        
        try:
            # 1. 獲取基本信息
            pitcher_stats['basic_info'] = self._get_pitcher_basic_info(pitcher_name, pitcher_id)
            
            # 2. 獲取生涯統計
            pitcher_stats['career_stats'] = self._get_pitcher_career_stats(pitcher_name, pitcher_id)
            
            # 3. 獲取最近表現
            pitcher_stats['recent_performance'] = self._get_pitcher_recent_performance(pitcher_name, pitcher_id)
            
            # 4. 獲取逐年統計
            pitcher_stats['season_by_season'] = self._get_pitcher_season_stats(pitcher_name, pitcher_id)
            
            # 5. 獲取對各隊統計
            pitcher_stats['vs_teams_stats'] = self._get_pitcher_vs_teams_stats(pitcher_name, pitcher_id)
            
            # 6. 獲取對打者統計
            pitcher_stats['vs_batters_stats'] = self._get_pitcher_vs_batters_stats(pitcher_name, pitcher_id)
            
            # 7. 獲取情境統計
            pitcher_stats['situational_stats'] = self._get_pitcher_situational_stats(pitcher_name, pitcher_id)
            
        except Exception as e:
            logger.error(f"獲取投手統計失敗: {e}")
            pitcher_stats['error'] = str(e)
            
        return pitcher_stats
    
    def _get_pitcher_basic_info(self, pitcher_name: str, pitcher_id: Optional[int]) -> Dict:
        """獲取投手基本信息"""
        basic_info = {
            'name': pitcher_name,
            'id': pitcher_id,
            'team': 'Unknown',
            'position': 'P',
            'throws': 'Unknown',
            'age': 0,
            'height': 'Unknown',
            'weight': 'Unknown'
        }
        
        try:
            if pitcher_id:
                # 從MLB API獲取詳細信息
                url = f"{self.base_url}/people/{pitcher_id}"
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if 'people' in data and data['people']:
                        player = data['people'][0]
                        basic_info.update({
                            'name': player.get('fullName', pitcher_name),
                            'team': player.get('currentTeam', {}).get('name', 'Unknown'),
                            'position': player.get('primaryPosition', {}).get('abbreviation', 'P'),
                            'throws': player.get('pitchHand', {}).get('code', 'Unknown'),
                            'age': player.get('currentAge', 0),
                            'height': player.get('height', 'Unknown'),
                            'weight': player.get('weight', 'Unknown')
                        })
        except Exception as e:
            logger.warning(f"獲取投手基本信息失敗: {e}")
            
        return basic_info
    
    def _get_pitcher_career_stats(self, pitcher_name: str, pitcher_id: Optional[int]) -> Dict:
        """獲取投手生涯統計"""
        career_stats = {
            'games': 0,
            'games_started': 0,
            'wins': 0,
            'losses': 0,
            'saves': 0,
            'innings_pitched': 0.0,
            'era': 0.0,
            'whip': 0.0,
            'strikeouts': 0,
            'walks': 0,
            'hits_allowed': 0,
            'home_runs_allowed': 0,
            'k_per_9': 0.0,
            'bb_per_9': 0.0
        }
        
        try:
            # 從數據庫查詢生涯統計
            query = text("""
                SELECT
                    COUNT(*) as games,
                    SUM(games_started) as games_started,
                    SUM(wins) as wins,
                    SUM(losses) as losses,
                    SUM(saves) as saves,
                    SUM(innings_pitched) as innings_pitched,
                    AVG(era) as era,
                    AVG(whip) as whip,
                    SUM(strikeouts_pitching) as strikeouts,
                    SUM(walks_allowed) as walks,
                    SUM(hits_allowed) as hits_allowed,
                    SUM(home_runs_allowed) as home_runs_allowed
                FROM player_stats
                WHERE player_name LIKE :pitcher_name
                AND (innings_pitched > 0 OR era > 0 OR strikeouts_pitching > 0)
            """)
            
            result = db.session.execute(query, {'pitcher_name': f'%{pitcher_name}%'}).fetchone()
            
            if result:
                career_stats.update({
                    'games': result.games or 0,
                    'games_started': result.games_started or 0,
                    'wins': result.wins or 0,
                    'losses': result.losses or 0,
                    'saves': result.saves or 0,
                    'innings_pitched': float(result.innings_pitched or 0),
                    'era': float(result.era or 0),
                    'whip': float(result.whip or 0),
                    'strikeouts': result.strikeouts or 0,
                    'walks': result.walks or 0,
                    'hits_allowed': result.hits_allowed or 0,
                    'home_runs_allowed': result.home_runs_allowed or 0
                })
                
                # 計算衍生統計
                if career_stats['innings_pitched'] > 0:
                    career_stats['k_per_9'] = (career_stats['strikeouts'] / career_stats['innings_pitched']) * 9
                    career_stats['bb_per_9'] = (career_stats['walks'] / career_stats['innings_pitched']) * 9
                    
        except Exception as e:
            logger.warning(f"獲取生涯統計失敗: {e}")
            
        return career_stats
    
    def _get_pitcher_recent_performance(self, pitcher_name: str, pitcher_id: Optional[int]) -> Dict:
        """獲取投手最近表現"""
        recent_performance = {
            'recent_games': [],
            'last_30_days': {
                'games': 0,
                'era': 0.0,
                'whip': 0.0,
                'strikeouts': 0,
                'innings_pitched': 0.0
            },
            'current_season': {
                'games': 0,
                'era': 0.0,
                'whip': 0.0,
                'record': '0-0'
            }
        }
        
        try:
            # 獲取最近30天的表現
            thirty_days_ago = date.today() - timedelta(days=30)
            
            # 由於player_stats表沒有game_date列，我們需要從games表獲取日期
            # 但是player_stats和games的關聯可能有問題，先簡化查詢
            query = text("""
                SELECT
                    ps.season,
                    ps.innings_pitched,
                    ps.era,
                    ps.whip,
                    ps.strikeouts_pitching as strikeouts,
                    ps.walks_allowed as walks,
                    ps.hits_allowed,
                    ps.earned_runs as runs_allowed
                FROM player_stats ps
                WHERE ps.player_name LIKE :pitcher_name
                AND (ps.innings_pitched > 0 OR ps.era > 0 OR ps.strikeouts_pitching > 0)
                AND ps.season >= 2024
                ORDER BY ps.season DESC, ps.id DESC
                LIMIT 10
            """)
            
            results = db.session.execute(query, {
                'pitcher_name': f'%{pitcher_name}%'
            }).fetchall()

            for i, result in enumerate(results):
                game_info = {
                    'date': f"{result.season} Season",
                    'opponent': "Various Teams",
                    'innings_pitched': float(result.innings_pitched or 0),
                    'era': float(result.era or 0),
                    'strikeouts': result.strikeouts or 0,
                    'walks': result.walks or 0,
                    'hits_allowed': result.hits_allowed or 0,
                    'runs_allowed': result.runs_allowed or 0
                }

                recent_performance['recent_games'].append(game_info)
                
        except Exception as e:
            logger.warning(f"獲取最近表現失敗: {e}")
            
        return recent_performance
    
    def _get_pitcher_season_stats(self, pitcher_name: str, pitcher_id: Optional[int]) -> List[Dict]:
        """獲取投手逐年統計"""
        season_stats = []
        
        try:
            query = text("""
                SELECT
                    season,
                    COUNT(*) as games,
                    SUM(wins) as wins,
                    SUM(losses) as losses,
                    SUM(saves) as saves,
                    SUM(innings_pitched) as innings_pitched,
                    AVG(era) as era,
                    AVG(whip) as whip,
                    SUM(strikeouts_pitching) as strikeouts,
                    SUM(walks_allowed) as walks
                FROM player_stats
                WHERE player_name LIKE :pitcher_name
                AND (innings_pitched > 0 OR era > 0 OR strikeouts_pitching > 0)
                GROUP BY season
                ORDER BY season DESC
                LIMIT 5
            """)
            
            results = db.session.execute(query, {'pitcher_name': f'%{pitcher_name}%'}).fetchall()
            
            for result in results:
                season_info = {
                    'season': int(result.season) if result.season else 0,
                    'games': result.games or 0,
                    'wins': result.wins or 0,
                    'losses': result.losses or 0,
                    'saves': result.saves or 0,
                    'innings_pitched': float(result.innings_pitched or 0),
                    'era': float(result.era or 0),
                    'whip': float(result.whip or 0),
                    'strikeouts': result.strikeouts or 0,
                    'walks': result.walks or 0
                }
                season_stats.append(season_info)
                
        except Exception as e:
            logger.warning(f"獲取逐年統計失敗: {e}")
            
        return season_stats
    
    def _get_pitcher_vs_teams_stats(self, pitcher_name: str, pitcher_id: Optional[int]) -> Dict:
        """獲取投手對各隊統計"""
        vs_teams_stats = {}
        
        try:
            # 簡化查詢，按球隊分組統計
            query = text("""
                SELECT
                    team_id,
                    COUNT(*) as games,
                    AVG(era) as era,
                    SUM(strikeouts_pitching) as strikeouts,
                    SUM(innings_pitched) as innings_pitched
                FROM player_stats
                WHERE player_name LIKE :pitcher_name
                AND (innings_pitched > 0 OR era > 0 OR strikeouts_pitching > 0)
                GROUP BY team_id
                HAVING games >= 1
                ORDER BY era ASC
            """)

            results = db.session.execute(query, {'pitcher_name': f'%{pitcher_name}%'}).fetchall()

            for result in results:
                team_name = f"Team {result.team_id}" if result.team_id else "Unknown Team"
                vs_teams_stats[team_name] = {
                    'games': result.games or 0,
                    'era': float(result.era or 0),
                    'strikeouts': result.strikeouts or 0,
                    'innings_pitched': float(result.innings_pitched or 0)
                }

        except Exception as e:
            logger.warning(f"獲取對各隊統計失敗: {e}")
            
        return vs_teams_stats
    
    def _get_pitcher_vs_batters_stats(self, pitcher_name: str, pitcher_id: Optional[int]) -> Dict:
        """獲取投手對打者統計（簡化版）"""
        vs_batters_stats = {
            'total_batters_faced': 0,
            'avg_against': 0.0,
            'strikeout_rate': 0.0,
            'walk_rate': 0.0,
            'top_dominated_batters': [],
            'struggled_against_batters': []
        }
        
        # 這裡可以擴展更詳細的對戰分析
        # 目前返回基本結構
        
        return vs_batters_stats
    
    def _get_pitcher_situational_stats(self, pitcher_name: str, pitcher_id: Optional[int]) -> Dict:
        """獲取投手情境統計"""
        situational_stats = {
            'home_vs_away': {
                'home_era': 0.0,
                'away_era': 0.0,
                'home_games': 0,
                'away_games': 0
            },
            'day_vs_night': {
                'day_era': 0.0,
                'night_era': 0.0
            },
            'vs_handedness': {
                'vs_lefty_avg': 0.0,
                'vs_righty_avg': 0.0
            },
            'rest_days_impact': {
                'normal_rest_era': 0.0,
                'short_rest_era': 0.0,
                'extra_rest_era': 0.0
            }
        }
        
        # 這裡可以擴展更詳細的情境分析
        # 目前返回基本結構
        
        return situational_stats
