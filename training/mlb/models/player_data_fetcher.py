#!/usr/bin/env python3
"""
球員數據獲取器 - 專門獲取球員信息和統計數據
"""

import requests
import json
import logging
from datetime import datetime, date
from typing import Dict, List, Optional
from sqlalchemy.exc import IntegrityError

from .database import db, Team, Player, PlayerStats

logger = logging.getLogger(__name__)

class PlayerDataFetcher:
    """球員數據獲取器"""
    
    def __init__(self):
        self.base_url = "https://statsapi.mlb.com/api/v1"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MLB-Prediction-System/1.0'
        })
    
    def fetch_team_roster(self, team_id: int, season: int = None) -> bool:
        """獲取球隊球員名單"""
        try:
            if season is None:
                season = date.today().year
            
            logger.info(f"獲取球隊 {team_id} 的 {season} 賽季球員名單...")
            
            url = f"{self.base_url}/teams/{team_id}/roster"
            params = {
                'season': season,
                'rosterType': 'active'
            }
            
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code != 200:
                logger.error(f"獲取球員名單失敗: {response.status_code}")
                return False
            
            data = response.json()
            roster = data.get('roster', [])
            
            if not roster:
                logger.warning(f"球隊 {team_id} 沒有找到球員名單")
                return False
            
            success_count = 0
            for player_data in roster:
                try:
                    success = self._process_player_info(player_data, team_id)
                    if success:
                        success_count += 1
                except Exception as e:
                    logger.error(f"處理球員信息失敗: {e}")
            
            logger.info(f"球隊 {team_id}: 成功處理 {success_count}/{len(roster)} 名球員")
            
            if success_count > 0:
                db.session.commit()
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 球員名單失敗: {e}")
            db.session.rollback()
            return False
    
    def _process_player_info(self, player_data: Dict, team_id: int) -> bool:
        """處理球員基本信息"""
        try:
            person = player_data.get('person', {})
            player_id = person.get('id')
            
            if not player_id:
                return False
            
            # 檢查球員是否已存在
            existing_player = Player.query.filter_by(player_id=player_id).first()
            
            if existing_player:
                # 更新現有球員信息
                player = existing_player
            else:
                # 創建新球員
                player = Player(player_id=player_id)
                db.session.add(player)
            
            # 更新球員基本信息
            player.full_name = person.get('fullName', '')
            player.first_name = person.get('firstName', '')
            player.last_name = person.get('lastName', '')
            player.birth_date = self._parse_date(person.get('birthDate'))
            player.birth_city = person.get('birthCity', '')
            player.birth_country = person.get('birthCountry', '')
            player.height = person.get('height', '')
            player.weight = person.get('weight')
            player.bats = person.get('batSide', {}).get('code', '')
            player.throws = person.get('pitchHand', {}).get('code', '')
            player.current_team_id = team_id
            
            # 獲取位置信息
            position = player_data.get('position', {})
            player.primary_position = position.get('abbreviation', '')
            
            # 獲取球衣號碼
            player.jersey_number = player_data.get('jerseyNumber')
            
            # 設置狀態
            player.active = player_data.get('status', {}).get('code') == 'A'
            
            return True
            
        except Exception as e:
            logger.error(f"處理球員信息失敗: {e}")
            return False
    
    def fetch_player_stats(self, player_id: int, season: int = None) -> bool:
        """獲取球員統計數據"""
        try:
            if season is None:
                season = date.today().year
            
            logger.info(f"獲取球員 {player_id} 的 {season} 賽季統計...")
            
            url = f"{self.base_url}/people/{player_id}/stats"
            params = {
                'stats': 'season',
                'season': season,
                'group': 'hitting,pitching,fielding'
            }
            
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code != 200:
                logger.error(f"獲取球員統計失敗: {response.status_code}")
                return False
            
            data = response.json()
            stats_groups = data.get('stats', [])
            
            success = False
            for stats_group in stats_groups:
                group_type = stats_group.get('group', {}).get('displayName', '')
                splits = stats_group.get('splits', [])
                
                for split in splits:
                    try:
                        stat_success = self._process_player_stats(player_id, split, season, group_type)
                        if stat_success:
                            success = True
                    except Exception as e:
                        logger.error(f"處理球員統計失敗: {e}")
            
            if success:
                db.session.commit()
            
            return success
            
        except Exception as e:
            logger.error(f"獲取球員 {player_id} 統計失敗: {e}")
            db.session.rollback()
            return False
    
    def _process_player_stats(self, player_id: int, split_data: Dict, season: int, group_type: str) -> bool:
        """處理球員統計數據"""
        try:
            # 獲取球員信息
            player = Player.query.filter_by(player_id=player_id).first()
            if not player:
                return False
            
            # 檢查是否已存在統計記錄
            existing_stats = PlayerStats.query.filter_by(
                player_id=player_id,
                season=season
            ).first()
            
            if existing_stats:
                stats = existing_stats
            else:
                stats = PlayerStats(
                    player_id=player_id,
                    season=season,
                    player_name=player.full_name,
                    team_id=player.current_team_id
                )
                db.session.add(stats)
            
            # 獲取統計數據
            stat_data = split_data.get('stat', {})
            
            # 處理打擊統計
            if group_type == 'Hitting' or 'hitting' in group_type.lower():
                stats.games_played = stat_data.get('gamesPlayed', 0)
                stats.at_bats = stat_data.get('atBats', 0)
                stats.runs = stat_data.get('runs', 0)
                stats.hits = stat_data.get('hits', 0)
                stats.doubles = stat_data.get('doubles', 0)
                stats.triples = stat_data.get('triples', 0)
                stats.home_runs = stat_data.get('homeRuns', 0)
                stats.rbi = stat_data.get('rbi', 0)
                stats.stolen_bases = stat_data.get('stolenBases', 0)
                stats.caught_stealing = stat_data.get('caughtStealing', 0)
                stats.walks = stat_data.get('baseOnBalls', 0)
                stats.strikeouts = stat_data.get('strikeOuts', 0)
                stats.batting_avg = float(stat_data.get('avg', '0.000'))
                stats.on_base_pct = float(stat_data.get('obp', '0.000'))
                stats.slugging_pct = float(stat_data.get('slg', '0.000'))
                stats.ops = float(stat_data.get('ops', '0.000'))
            
            # 處理投球統計
            elif group_type == 'Pitching' or 'pitching' in group_type.lower():
                stats.games_pitched = stat_data.get('gamesPlayed', 0)
                stats.games_started = stat_data.get('gamesStarted', 0)
                stats.wins = stat_data.get('wins', 0)
                stats.losses = stat_data.get('losses', 0)
                stats.saves = stat_data.get('saves', 0)
                stats.innings_pitched = float(stat_data.get('inningsPitched', '0.0'))
                stats.hits_allowed = stat_data.get('hits', 0)
                stats.runs_allowed = stat_data.get('runs', 0)
                stats.earned_runs = stat_data.get('earnedRuns', 0)
                stats.walks_allowed = stat_data.get('baseOnBalls', 0)
                stats.strikeouts_pitched = stat_data.get('strikeOuts', 0)
                stats.home_runs_allowed = stat_data.get('homeRuns', 0)
                stats.era = float(stat_data.get('era', '0.00'))
                stats.whip = float(stat_data.get('whip', '0.00'))
            
            # 處理守備統計
            elif group_type == 'Fielding' or 'fielding' in group_type.lower():
                stats.fielding_pct = float(stat_data.get('fielding', '0.000'))
                stats.errors = stat_data.get('errors', 0)
            
            return True
            
        except Exception as e:
            logger.error(f"處理球員統計失敗: {e}")
            return False
    
    def fetch_all_teams_rosters(self, season: int = None) -> Dict[str, bool]:
        """獲取所有球隊的球員名單"""
        try:
            if season is None:
                season = date.today().year
            
            logger.info(f"開始獲取所有球隊的 {season} 賽季球員名單...")
            
            teams = Team.query.all()
            results = {}
            
            for team in teams:
                try:
                    success = self.fetch_team_roster(team.team_id, season)
                    results[team.team_name] = success
                    
                    # 避免請求過於頻繁
                    import time
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"獲取球隊 {team.team_name} 失敗: {e}")
                    results[team.team_name] = False
            
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"球員名單獲取完成: {success_count}/{len(teams)} 支球隊成功")
            
            return results
            
        except Exception as e:
            logger.error(f"批量獲取球員名單失敗: {e}")
            return {}
    
    def fetch_all_players_stats(self, season: int = None) -> Dict[str, bool]:
        """獲取所有球員的統計數據"""
        try:
            if season is None:
                season = date.today().year
            
            logger.info(f"開始獲取所有球員的 {season} 賽季統計...")
            
            players = Player.query.filter_by(active=True).all()
            results = {}
            
            for player in players:
                try:
                    success = self.fetch_player_stats(player.player_id, season)
                    results[player.full_name] = success
                    
                    # 避免請求過於頻繁
                    import time
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"獲取球員 {player.full_name} 統計失敗: {e}")
                    results[player.full_name] = False
            
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"球員統計獲取完成: {success_count}/{len(players)} 名球員成功")
            
            return results
            
        except Exception as e:
            logger.error(f"批量獲取球員統計失敗: {e}")
            return {}
    
    def _parse_date(self, date_str: str) -> Optional[date]:
        """解析日期字符串"""
        if not date_str:
            return None
        
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            try:
                return datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%S.%fZ').date()
            except ValueError:
                logger.warning(f"無法解析日期: {date_str}")
                return None
