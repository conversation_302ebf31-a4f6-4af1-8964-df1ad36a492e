#!/usr/bin/env python3
"""
增強投手管理器
正確顯示 game_id 和 player_id，並獲取球員真實姓名
"""

import logging
from datetime import date
from typing import Dict, Optional, Tuple, List

logger = logging.getLogger(__name__)

class EnhancedPitcherManager:
    """增強投手管理器"""
    
    def __init__(self):
        self.player_cache = {}  # 緩存球員信息
        self.team_cache = {}    # 緩存球隊信息
    
    def get_starting_pitchers_enhanced(self, game_id: str, home_team: str, away_team: str) -> Tuple[Dict, Dict]:
        """
        增強版獲取先發投手信息
        包含完整的 game_id, player_id 和球員姓名
        """
        try:
            from models.database import db, PlayerGameStats, Team, Player
            
            logger.info(f"🔍 查找比賽 {game_id} 的先發投手: {away_team} @ {home_team}")
            
            # 獲取球隊ID
            home_team_obj = self._get_team_info(home_team)
            away_team_obj = self._get_team_info(away_team)
            
            if not home_team_obj or not away_team_obj:
                logger.warning(f"⚠️ 找不到球隊信息: {home_team} 或 {away_team}")
                return self._get_default_pitchers_enhanced(game_id, home_team, away_team)
            
            # 獲取主隊先發投手
            home_pitcher = self._get_team_starting_pitcher(
                game_id, home_team_obj, home_team, is_home=True
            )
            
            # 獲取客隊先發投手
            away_pitcher = self._get_team_starting_pitcher(
                game_id, away_team_obj, away_team, is_home=False
            )
            
            # 如果沒有找到投手，使用默認值
            if not home_pitcher:
                home_pitcher = self._get_default_pitcher_enhanced(game_id, home_team, is_home=True)
            if not away_pitcher:
                away_pitcher = self._get_default_pitcher_enhanced(game_id, away_team, is_home=False)
            
            logger.info(f"✅ 找到先發投手:")
            logger.info(f"   主隊 {home_team}: {home_pitcher['display_name']} (ID: {home_pitcher['player_id']})")
            logger.info(f"   客隊 {away_team}: {away_pitcher['display_name']} (ID: {away_pitcher['player_id']})")
            
            return home_pitcher, away_pitcher
            
        except Exception as e:
            logger.error(f"❌ 獲取先發投手失敗: {e}")
            return self._get_default_pitchers_enhanced(game_id, home_team, away_team)
    
    def _get_team_starting_pitcher(self, game_id: str, team_obj, team_code: str, is_home: bool) -> Optional[Dict]:
        """獲取指定球隊的先發投手"""
        try:
            from models.database import PlayerGameStats, Player
            
            # 查找該比賽中該球隊的投手，按投球局數排序
            pitcher_stats = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.team_id == team_obj.team_id,
                PlayerGameStats.position == 'P',
                PlayerGameStats.innings_pitched > 0
            ).order_by(PlayerGameStats.innings_pitched.desc()).all()
            
            if not pitcher_stats:
                logger.warning(f"⚠️ 找不到 {team_code} 的投手統計")
                return None
            
            # 取投球局數最多的投手（通常是先發投手）
            starting_pitcher_stat = pitcher_stats[0]
            
            # 獲取球員詳細信息
            player_info = self._get_player_info(starting_pitcher_stat.player_id)
            
            # 計算ERA
            if starting_pitcher_stat.innings_pitched > 0:
                era = (starting_pitcher_stat.earned_runs * 9.0) / starting_pitcher_stat.innings_pitched
            else:
                era = 0.0
            
            # 構建投手信息
            pitcher_info_dict = {
                'game_id': game_id,
                'player_id': starting_pitcher_stat.player_id,
                'team_code': team_code,
                'team_id': team_obj.team_id,
                'is_home': is_home,
                'player_name': player_info['name'] if player_info else f"球員#{starting_pitcher_stat.player_id}",
                'display_name': player_info['name'] if player_info else f"投手#{starting_pitcher_stat.player_id}",
                'position': starting_pitcher_stat.position,
                'era': era if era > 0 else 4.50,
                'innings_pitched': starting_pitcher_stat.innings_pitched,
                'earned_runs': starting_pitcher_stat.earned_runs,
                'strikeouts': starting_pitcher_stat.strikeouts_pitched or 0,
                'walks': starting_pitcher_stat.walks_allowed or 0,
                'hits_allowed': starting_pitcher_stat.hits_allowed or 0,
                'quality': self._era_to_quality(era if era > 0 else 4.50),
                'level': self._classify_pitcher_level(era if era > 0 else 4.50),
                'data_source': 'player_game_stats'
            }
            
            logger.info(f"✅ 找到 {team_code} 先發投手: {pitcher_info_dict['display_name']} (ERA: {pitcher_info_dict['era']:.2f}, 局數: {pitcher_info_dict['innings_pitched']})")
            
            return pitcher_info_dict
            
        except Exception as e:
            logger.error(f"❌ 獲取 {team_code} 先發投手失敗: {e}")
            return None
    
    def _get_team_info(self, team_code: str) -> Optional[object]:
        """獲取球隊信息（帶緩存）"""
        if team_code in self.team_cache:
            return self.team_cache[team_code]
        
        try:
            from models.database import Team
            team_obj = Team.query.filter_by(team_code=team_code).first()
            self.team_cache[team_code] = team_obj
            return team_obj
        except Exception as e:
            logger.error(f"❌ 獲取球隊 {team_code} 信息失敗: {e}")
            return None
    
    def _get_player_info(self, player_id: str) -> Optional[Dict]:
        """獲取球員信息（帶緩存）"""
        if player_id in self.player_cache:
            return self.player_cache[player_id]
        
        try:
            from models.database import Player
            player = Player.query.filter_by(player_id=player_id).first()
            
            if player:
                player_info = {
                    'name': player.full_name,
                    'position': player.primary_position,
                    'team_id': player.current_team_id
                }
            else:
                player_info = None
            
            self.player_cache[player_id] = player_info
            return player_info
            
        except Exception as e:
            logger.error(f"❌ 獲取球員 {player_id} 信息失敗: {e}")
            return None
    
    def _get_default_pitcher_enhanced(self, game_id: str, team_code: str, is_home: bool) -> Dict:
        """獲取默認投手信息（增強版）"""
        return {
            'game_id': game_id,
            'player_id': f'unknown_{team_code}',
            'team_code': team_code,
            'team_id': None,
            'is_home': is_home,
            'player_name': f'{team_code} 未知投手',
            'display_name': f'{team_code} 投手',
            'position': 'P',
            'era': 4.50,
            'innings_pitched': 6.0,
            'earned_runs': 3,
            'strikeouts': 6,
            'walks': 3,
            'hits_allowed': 6,
            'quality': 50.0,
            'level': '普通',
            'data_source': 'default'
        }
    
    def _get_default_pitchers_enhanced(self, game_id: str, home_team: str, away_team: str) -> Tuple[Dict, Dict]:
        """獲取默認投手對（增強版）"""
        home_pitcher = self._get_default_pitcher_enhanced(game_id, home_team, is_home=True)
        away_pitcher = self._get_default_pitcher_enhanced(game_id, away_team, is_home=False)
        return home_pitcher, away_pitcher
    
    def get_game_pitcher_summary(self, game_id: str, home_team: str, away_team: str) -> Dict:
        """獲取比賽投手摘要"""
        try:
            home_pitcher, away_pitcher = self.get_starting_pitchers_enhanced(game_id, home_team, away_team)
            
            return {
                'game_id': game_id,
                'home_team': home_team,
                'away_team': away_team,
                'pitchers': {
                    'home': {
                        'player_id': home_pitcher['player_id'],
                        'name': home_pitcher['display_name'],
                        'era': home_pitcher['era'],
                        'level': home_pitcher['level'],
                        'innings': home_pitcher['innings_pitched'],
                        'strikeouts': home_pitcher['strikeouts'],
                        'data_source': home_pitcher['data_source']
                    },
                    'away': {
                        'player_id': away_pitcher['player_id'],
                        'name': away_pitcher['display_name'],
                        'era': away_pitcher['era'],
                        'level': away_pitcher['level'],
                        'innings': away_pitcher['innings_pitched'],
                        'strikeouts': away_pitcher['strikeouts'],
                        'data_source': away_pitcher['data_source']
                    }
                },
                'matchup_analysis': self._analyze_pitcher_matchup(home_pitcher, away_pitcher)
            }
            
        except Exception as e:
            logger.error(f"❌ 獲取比賽投手摘要失敗: {e}")
            return {
                'error': str(e),
                'game_id': game_id,
                'home_team': home_team,
                'away_team': away_team
            }
    
    def _analyze_pitcher_matchup(self, home_pitcher: Dict, away_pitcher: Dict) -> Dict:
        """分析投手對戰"""
        home_level = home_pitcher['level']
        away_level = away_pitcher['level']
        
        if home_level == "王牌" and away_level == "王牌":
            matchup_type = "王牌對決"
            expected_total = 6.5
            description = "兩位王牌投手對決，預期低分比賽"
        elif home_level == "弱勢" and away_level == "弱勢":
            matchup_type = "打擊戰"
            expected_total = 12.5
            description = "兩位弱勢投手，預期高分打擊戰"
        elif (home_level == "王牌" and away_level == "弱勢") or (home_level == "弱勢" and away_level == "王牌"):
            matchup_type = "強弱對戰"
            expected_total = 8.5
            description = "強弱投手對戰，分數不平衡"
        else:
            matchup_type = "普通對戰"
            expected_total = 9.0
            description = "普通投手對戰，標準分數"
        
        return {
            'type': matchup_type,
            'description': description,
            'expected_total': expected_total,
            'home_advantage': home_pitcher['level'],
            'away_advantage': away_pitcher['level'],
            'confidence': self._calculate_matchup_confidence(home_pitcher, away_pitcher)
        }
    
    def _calculate_matchup_confidence(self, home_pitcher: Dict, away_pitcher: Dict) -> float:
        """計算對戰信心度"""
        # 根據數據來源和投手等級計算信心度
        base_confidence = 0.65
        
        # 如果有真實數據，提高信心度
        if home_pitcher['data_source'] == 'player_game_stats' and away_pitcher['data_source'] == 'player_game_stats':
            base_confidence += 0.15
        
        # 根據投手等級調整
        if home_pitcher['level'] in ['王牌', '弱勢'] and away_pitcher['level'] in ['王牌', '弱勢']:
            base_confidence += 0.10
        
        return min(0.95, base_confidence)
    
    def _era_to_quality(self, era: float) -> float:
        """ERA轉換為質量分數"""
        return max(0, 100 - (era - 2.0) * 20)
    
    def _classify_pitcher_level(self, era: float) -> str:
        """分類投手等級"""
        if era <= 2.50:
            return "王牌"
        elif era <= 3.50:
            return "優秀"
        elif era <= 4.50:
            return "普通"
        else:
            return "弱勢"
    
    def batch_analyze_games(self, game_ids: List[str]) -> Dict:
        """批量分析比賽投手"""
        try:
            from models.database import Game
            
            results = {
                'success': 0,
                'failed': 0,
                'games': {}
            }
            
            for game_id in game_ids:
                try:
                    # 獲取比賽信息
                    game = Game.query.filter_by(game_id=game_id).first()
                    if not game:
                        results['failed'] += 1
                        continue
                    
                    # 分析投手
                    summary = self.get_game_pitcher_summary(game_id, game.home_team, game.away_team)
                    
                    if 'error' not in summary:
                        results['games'][game_id] = summary
                        results['success'] += 1
                    else:
                        results['failed'] += 1
                        
                except Exception as e:
                    logger.error(f"❌ 分析比賽 {game_id} 失敗: {e}")
                    results['failed'] += 1
            
            logger.info(f"📊 批量分析完成: 成功 {results['success']}, 失敗 {results['failed']}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 批量分析失敗: {e}")
            return {'success': 0, 'failed': len(game_ids), 'games': {}}

# 創建全局實例
enhanced_pitcher_manager = EnhancedPitcherManager()
