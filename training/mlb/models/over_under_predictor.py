"""
MLB大小分預測系統
結合投手對打者歷史數據進行精準的總得分預測
"""

import logging
import json
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
from flask import current_app
import pandas as pd
import numpy as np

from models.database import db, Game, Prediction, PlayerStats
from models.pitcher_batter_matchups import PitcherBatterMatchupAnalyzer
from models.daily_lineup_fetcher import DailyLineupFetcher
from models.pitcher_name_matcher import PitcherNameMatcher
from models.real_betting_odds_fetcher import RealBettingOddsFetcher

logger = logging.getLogger(__name__)

class OverUnderPredictor:
    """大小分預測器 - 基於投手對打者歷史數據"""
    
    def __init__(self, app=None):
        self.app = app or current_app
        self.matchup_analyzer = PitcherBatterMatchupAnalyzer(app)
        self.lineup_fetcher = DailyLineupFetcher(app)
        self.pitcher_matcher = PitcherNameMatcher()
        self.betting_odds_fetcher = RealBettingOddsFetcher(app)

        # 大小分盤口設定
        self.default_total_line = 8.5  # 默認大小分盤口（當無法獲取真實盤口時）
        self.line_adjustments = {
            'pitcher_park': 0.5,  # 投手球場調整
            'weather': 0.3,       # 天氣調整
            'wind': 0.2,          # 風向調整
            'temperature': 0.1    # 溫度調整
        }
    
    def predict_over_under(self, game_id: str, target_date: date = None) -> Dict:
        """預測比賽的大小分"""
        try:
            if target_date is None:
                target_date = date.today()
            
            logger.info(f"開始預測比賽 {game_id} 的大小分...")
            
            # 1. 獲取比賽信息
            with self.app.app_context():
                game = Game.query.filter_by(game_id=game_id).first()
                if not game:
                    raise ValueError(f"找不到比賽 {game_id}")
            
            # 2. 獲取先發投手信息
            pitcher_info = self._get_starting_pitchers(game, target_date)
            
            # 3. 獲取打線信息
            lineup_info = self._get_batting_lineups(game, target_date)
            
            # 4. 分析投手對打者歷史數據
            matchup_analysis = self._analyze_pitcher_vs_batters(
                pitcher_info, lineup_info, game
            )
            
            # 5. 計算預期得分
            expected_runs = self._calculate_expected_runs(matchup_analysis, game)
            
            # 6. 設定大小分盤口
            total_line = self._determine_total_line(expected_runs, game)
            
            # 7. 計算大小分概率
            over_under_probs = self._calculate_over_under_probabilities(
                expected_runs, total_line, matchup_analysis
            )
            
            # 8. 生成預測結果
            prediction_result = {
                'game_id': game_id,
                'game_info': {
                    'home_team': game.home_team,
                    'away_team': game.away_team,
                    'date': game.date.isoformat(),
                    'venue': game.venue
                },
                'pitcher_info': pitcher_info,
                'lineup_analysis': lineup_info,
                'matchup_analysis': matchup_analysis,
                'expected_runs': expected_runs,
                'total_line': total_line,
                'over_probability': over_under_probs['over'],
                'under_probability': over_under_probs['under'],
                'confidence': over_under_probs['confidence'],
                'recommendation': over_under_probs['recommendation'],
                'key_factors': self._identify_key_factors(matchup_analysis),
                'prediction_timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"大小分預測完成: {expected_runs['total']:.1f}分, 盤口{total_line}")
            return prediction_result
            
        except Exception as e:
            logger.error(f"大小分預測失敗 {game_id}: {e}")
            return {'error': str(e)}
    
    def _get_starting_pitchers(self, game: Game, target_date: date) -> Dict:
        """獲取先發投手信息"""
        try:
            # 從每日先發陣容獲取投手信息
            daily_lineups = self.lineup_fetcher.get_daily_lineups(target_date)
            
            pitcher_info = {
                'home_pitcher': None,
                'away_pitcher': None,
                'home_pitcher_stats': {},
                'away_pitcher_stats': {}
            }
            
            # 查找對應比賽的投手信息
            for game_data in daily_lineups.get('games', []):
                if (game_data.get('home_team') == game.home_team and 
                    game_data.get('away_team') == game.away_team):
                    
                    pitcher_info['home_pitcher'] = game_data.get('home_pitcher', {}).get('name')
                    pitcher_info['away_pitcher'] = game_data.get('away_pitcher', {}).get('name')
                    
                    # 獲取投手統計數據
                    if pitcher_info['home_pitcher']:
                        pitcher_info['home_pitcher_stats'] = self._get_pitcher_stats(
                            pitcher_info['home_pitcher']
                        )
                    
                    if pitcher_info['away_pitcher']:
                        pitcher_info['away_pitcher_stats'] = self._get_pitcher_stats(
                            pitcher_info['away_pitcher']
                        )
                    break
            
            return pitcher_info
            
        except Exception as e:
            logger.error(f"獲取先發投手信息失敗: {e}")
            return {'home_pitcher': None, 'away_pitcher': None}
    
    def _get_batting_lineups(self, game: Game, target_date: date) -> Dict:
        """獲取打線信息"""
        try:
            daily_lineups = self.lineup_fetcher.get_daily_lineups(target_date)
            
            lineup_info = {
                'home_lineup': [],
                'away_lineup': [],
                'home_lineup_stats': {},
                'away_lineup_stats': {}
            }
            
            # 查找對應比賽的打線信息
            for game_data in daily_lineups.get('games', []):
                if (game_data.get('home_team') == game.home_team and 
                    game_data.get('away_team') == game.away_team):
                    
                    lineup_info['home_lineup'] = game_data.get('home_lineup', [])
                    lineup_info['away_lineup'] = game_data.get('away_lineup', [])
                    
                    # 計算打線統計
                    lineup_info['home_lineup_stats'] = self._calculate_lineup_stats(
                        lineup_info['home_lineup']
                    )
                    lineup_info['away_lineup_stats'] = self._calculate_lineup_stats(
                        lineup_info['away_lineup']
                    )
                    break
            
            return lineup_info
            
        except Exception as e:
            logger.error(f"獲取打線信息失敗: {e}")
            return {'home_lineup': [], 'away_lineup': []}
    
    def _analyze_pitcher_vs_batters(self, pitcher_info: Dict, lineup_info: Dict, game: Game) -> Dict:
        """分析投手對打者的歷史數據"""
        try:
            analysis = {
                'home_pitcher_vs_away_batters': {},
                'away_pitcher_vs_home_batters': {},
                'matchup_summary': {},
                'advantage_analysis': {}
            }
            
            # 分析主隊投手對客隊打者
            if pitcher_info.get('home_pitcher') and lineup_info.get('away_lineup'):
                analysis['home_pitcher_vs_away_batters'] = self._analyze_pitcher_vs_lineup(
                    pitcher_info['home_pitcher'], 
                    lineup_info['away_lineup'],
                    'home_pitcher_vs_away'
                )
            
            # 分析客隊投手對主隊打者
            if pitcher_info.get('away_pitcher') and lineup_info.get('home_lineup'):
                analysis['away_pitcher_vs_home_batters'] = self._analyze_pitcher_vs_lineup(
                    pitcher_info['away_pitcher'], 
                    lineup_info['home_lineup'],
                    'away_pitcher_vs_home'
                )
            
            # 生成對戰總結
            analysis['matchup_summary'] = self._generate_matchup_summary(analysis)
            
            # 分析優勢
            analysis['advantage_analysis'] = self._analyze_matchup_advantages(analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"投手對打者分析失敗: {e}")
            return {}
    
    def _analyze_pitcher_vs_lineup(self, pitcher_name: str, lineup: List[Dict], context: str) -> Dict:
        """分析投手對整個打線的歷史表現"""
        try:
            lineup_analysis = {
                'pitcher_name': pitcher_name,
                'total_batters': len(lineup),
                'batters_with_history': 0,
                'total_at_bats': 0,
                'total_hits': 0,
                'total_home_runs': 0,
                'total_strikeouts': 0,
                'total_walks': 0,
                'batting_average_against': 0.0,
                'ops_against': 0.0,
                'expected_runs_against': 0.0,
                'individual_matchups': []
            }
            
            # 獲取投手ID（通過姓名匹配）
            pitcher_matches = self.pitcher_matcher.find_pitcher_matches(pitcher_name)
            if not pitcher_matches:
                logger.warning(f"找不到投手 {pitcher_name} 的匹配記錄")
                return lineup_analysis
            
            pitcher_id = pitcher_matches[0]['player_id']  # 使用最佳匹配
            
            # 分析每個打者對這個投手的歷史表現
            for batter in lineup:
                batter_name = batter.get('name', '')
                if not batter_name:
                    continue
                
                # 這裡需要實現打者ID匹配邏輯
                # 暫時使用模擬數據
                matchup_stats = self._get_simulated_matchup_stats(pitcher_name, batter_name)
                
                if matchup_stats['at_bats'] > 0:
                    lineup_analysis['batters_with_history'] += 1
                    lineup_analysis['total_at_bats'] += matchup_stats['at_bats']
                    lineup_analysis['total_hits'] += matchup_stats['hits']
                    lineup_analysis['total_home_runs'] += matchup_stats['home_runs']
                    lineup_analysis['total_strikeouts'] += matchup_stats['strikeouts']
                    lineup_analysis['total_walks'] += matchup_stats['walks']
                
                lineup_analysis['individual_matchups'].append({
                    'batter_name': batter_name,
                    'batting_order': batter.get('batting_order', 0),
                    'position': batter.get('position', ''),
                    'matchup_stats': matchup_stats
                })
            
            # 計算總體統計
            if lineup_analysis['total_at_bats'] > 0:
                lineup_analysis['batting_average_against'] = (
                    lineup_analysis['total_hits'] / lineup_analysis['total_at_bats']
                )
            
            # 估算預期得分
            lineup_analysis['expected_runs_against'] = self._estimate_runs_from_matchups(
                lineup_analysis
            )
            
            return lineup_analysis
            
        except Exception as e:
            logger.error(f"投手對打線分析失敗 {pitcher_name}: {e}")
            return {}
    
    def _get_simulated_matchup_stats(self, pitcher_name: str, batter_name: str) -> Dict:
        """獲取模擬的對戰統計（暫時使用，後續可替換為真實數據）"""
        # 基於投手和打者名稱生成一致的隨機數據
        import hashlib
        seed = int(hashlib.md5(f"{pitcher_name}_{batter_name}".encode()).hexdigest()[:8], 16)
        np.random.seed(seed % (2**32))
        
        # 模擬歷史對戰數據
        at_bats = np.random.randint(0, 15)  # 0-14次打席
        if at_bats == 0:
            return {
                'at_bats': 0, 'hits': 0, 'home_runs': 0, 
                'strikeouts': 0, 'walks': 0, 'batting_average': 0.0
            }
        
        hits = np.random.binomial(at_bats, 0.25)  # 25%打擊率
        home_runs = np.random.binomial(hits, 0.15) if hits > 0 else 0
        strikeouts = np.random.binomial(at_bats, 0.22)
        walks = np.random.randint(0, max(1, at_bats // 3))
        
        return {
            'at_bats': at_bats,
            'hits': hits,
            'home_runs': home_runs,
            'strikeouts': strikeouts,
            'walks': walks,
            'batting_average': hits / at_bats if at_bats > 0 else 0.0
        }

    def _get_pitcher_stats(self, pitcher_name: str) -> Dict:
        """獲取投手統計數據"""
        try:
            with self.app.app_context():
                # 使用投手姓名匹配器查找投手
                matches = self.pitcher_matcher.find_pitcher_matches(pitcher_name)
                if not matches:
                    return {}

                player_id = matches[0]['player_id']

                # 查詢投手統計
                pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.player_id == player_id,
                    PlayerStats.innings_pitched > 0
                ).order_by(PlayerStats.game_date.desc()).limit(10).all()

                if not pitcher_stats:
                    return {}

                # 計算平均統計
                total_innings = sum(ps.innings_pitched or 0 for ps in pitcher_stats)
                total_earned_runs = sum(ps.earned_runs or 0 for ps in pitcher_stats)
                total_hits = sum(ps.hits_allowed or 0 for ps in pitcher_stats)
                total_walks = sum(ps.walks_allowed or 0 for ps in pitcher_stats)
                total_strikeouts = sum(ps.strikeouts_pitching or 0 for ps in pitcher_stats)

                era = (total_earned_runs * 9) / total_innings if total_innings > 0 else 0
                whip = (total_hits + total_walks) / total_innings if total_innings > 0 else 0

                return {
                    'era': round(era, 2),
                    'whip': round(whip, 2),
                    'innings_pitched': round(total_innings, 1),
                    'strikeouts': total_strikeouts,
                    'recent_games': len(pitcher_stats)
                }

        except Exception as e:
            logger.error(f"獲取投手統計失敗 {pitcher_name}: {e}")
            return {}

    def _calculate_lineup_stats(self, lineup: List[Dict]) -> Dict:
        """計算打線統計"""
        try:
            if not lineup:
                return {}

            # 模擬打線統計計算
            total_batters = len(lineup)
            estimated_avg = 0.265  # 聯盟平均打擊率
            estimated_obp = 0.335  # 聯盟平均上壘率
            estimated_slg = 0.435  # 聯盟平均長打率

            return {
                'total_batters': total_batters,
                'estimated_batting_avg': estimated_avg,
                'estimated_obp': estimated_obp,
                'estimated_slg': estimated_slg,
                'estimated_ops': estimated_obp + estimated_slg,
                'lineup_strength': 'average'  # 可以根據實際數據調整
            }

        except Exception as e:
            logger.error(f"計算打線統計失敗: {e}")
            return {}

    def _estimate_runs_from_matchups(self, lineup_analysis: Dict) -> float:
        """根據對戰數據估算得分"""
        try:
            if lineup_analysis['total_at_bats'] == 0:
                return 4.5  # 聯盟平均得分

            # 基於歷史對戰數據的得分估算
            batting_avg = lineup_analysis['batting_average_against']
            home_runs = lineup_analysis['total_home_runs']
            walks = lineup_analysis['total_walks']

            # 簡化的得分估算公式
            base_runs = batting_avg * 9 * 4  # 基礎得分
            power_bonus = home_runs * 0.5    # 長打加成
            patience_bonus = walks * 0.2     # 保送加成

            estimated_runs = base_runs + power_bonus + patience_bonus

            # 應用校正 (單隊得分校正)
            calibrated_runs = estimated_runs * 0.85  # 降低15%

            # 限制在合理範圍內 (校正後的範圍)
            return max(1.0, min(8.0, calibrated_runs))

        except Exception as e:
            logger.error(f"估算得分失敗: {e}")
            return 4.5

    def _generate_matchup_summary(self, analysis: Dict) -> Dict:
        """生成對戰總結"""
        try:
            summary = {
                'total_historical_matchups': 0,
                'pitcher_advantage_situations': 0,
                'batter_advantage_situations': 0,
                'key_matchups': [],
                'overall_trend': 'neutral'
            }

            # 分析主隊投手對客隊打者
            home_pitcher_analysis = analysis.get('home_pitcher_vs_away_batters', {})
            if home_pitcher_analysis:
                summary['total_historical_matchups'] += home_pitcher_analysis.get('batters_with_history', 0)

                # 分析關鍵對戰
                for matchup in home_pitcher_analysis.get('individual_matchups', []):
                    stats = matchup.get('matchup_stats', {})
                    if stats.get('at_bats', 0) >= 3:  # 至少3次對戰
                        batting_avg = stats.get('batting_average', 0)
                        if batting_avg > 0.350:
                            summary['batter_advantage_situations'] += 1
                        elif batting_avg < 0.200:
                            summary['pitcher_advantage_situations'] += 1

                        summary['key_matchups'].append({
                            'pitcher': home_pitcher_analysis.get('pitcher_name'),
                            'batter': matchup.get('batter_name'),
                            'batting_avg': batting_avg,
                            'at_bats': stats.get('at_bats'),
                            'advantage': 'batter' if batting_avg > 0.350 else 'pitcher' if batting_avg < 0.200 else 'neutral'
                        })

            # 分析客隊投手對主隊打者
            away_pitcher_analysis = analysis.get('away_pitcher_vs_home_batters', {})
            if away_pitcher_analysis:
                summary['total_historical_matchups'] += away_pitcher_analysis.get('batters_with_history', 0)

                for matchup in away_pitcher_analysis.get('individual_matchups', []):
                    stats = matchup.get('matchup_stats', {})
                    if stats.get('at_bats', 0) >= 3:
                        batting_avg = stats.get('batting_average', 0)
                        if batting_avg > 0.350:
                            summary['batter_advantage_situations'] += 1
                        elif batting_avg < 0.200:
                            summary['pitcher_advantage_situations'] += 1

                        summary['key_matchups'].append({
                            'pitcher': away_pitcher_analysis.get('pitcher_name'),
                            'batter': matchup.get('batter_name'),
                            'batting_avg': batting_avg,
                            'at_bats': stats.get('at_bats'),
                            'advantage': 'batter' if batting_avg > 0.350 else 'pitcher' if batting_avg < 0.200 else 'neutral'
                        })

            # 確定整體趨勢
            if summary['pitcher_advantage_situations'] > summary['batter_advantage_situations']:
                summary['overall_trend'] = 'pitcher_favored'
            elif summary['batter_advantage_situations'] > summary['pitcher_advantage_situations']:
                summary['overall_trend'] = 'batter_favored'
            else:
                summary['overall_trend'] = 'neutral'

            return summary

        except Exception as e:
            logger.error(f"生成對戰總結失敗: {e}")
            return {}

    def _analyze_matchup_advantages(self, analysis: Dict) -> Dict:
        """分析對戰優勢"""
        try:
            advantages = {
                'home_team_advantages': [],
                'away_team_advantages': [],
                'neutral_factors': [],
                'overall_advantage': 'neutral'
            }

            # 分析主隊優勢
            home_pitcher_data = analysis.get('home_pitcher_vs_away_batters', {})
            if home_pitcher_data:
                batting_avg_against = home_pitcher_data.get('batting_average_against', 0.250)
                if batting_avg_against < 0.220:
                    advantages['home_team_advantages'].append(
                        f"主隊投手對客隊打線有優勢 (被打擊率 {batting_avg_against:.3f})"
                    )
                elif batting_avg_against > 0.300:
                    advantages['away_team_advantages'].append(
                        f"客隊打線對主隊投手有優勢 (打擊率 {batting_avg_against:.3f})"
                    )

            # 分析客隊優勢
            away_pitcher_data = analysis.get('away_pitcher_vs_home_batters', {})
            if away_pitcher_data:
                batting_avg_against = away_pitcher_data.get('batting_average_against', 0.250)
                if batting_avg_against < 0.220:
                    advantages['away_team_advantages'].append(
                        f"客隊投手對主隊打線有優勢 (被打擊率 {batting_avg_against:.3f})"
                    )
                elif batting_avg_against > 0.300:
                    advantages['home_team_advantages'].append(
                        f"主隊打線對客隊投手有優勢 (打擊率 {batting_avg_against:.3f})"
                    )

            # 確定整體優勢
            home_advantages = len(advantages['home_team_advantages'])
            away_advantages = len(advantages['away_team_advantages'])

            if home_advantages > away_advantages:
                advantages['overall_advantage'] = 'home'
            elif away_advantages > home_advantages:
                advantages['overall_advantage'] = 'away'
            else:
                advantages['overall_advantage'] = 'neutral'

            return advantages

        except Exception as e:
            logger.error(f"分析對戰優勢失敗: {e}")
            return {}

    def _calculate_expected_runs(self, matchup_analysis: Dict, game: Game) -> Dict:
        """計算預期得分"""
        try:
            expected_runs = {
                'home_team': 4.5,  # 默認值
                'away_team': 4.5,
                'total': 9.0,
                'calculation_method': 'historical_matchups'
            }

            # 基於投手對打者分析計算主隊得分
            away_pitcher_data = matchup_analysis.get('away_pitcher_vs_home_batters', {})
            if away_pitcher_data:
                expected_runs['home_team'] = away_pitcher_data.get('expected_runs_against', 4.5)

            # 基於投手對打者分析計算客隊得分
            home_pitcher_data = matchup_analysis.get('home_pitcher_vs_away_batters', {})
            if home_pitcher_data:
                expected_runs['away_team'] = home_pitcher_data.get('expected_runs_against', 4.5)

            # 應用得分校正 (與ML預測器保持一致)
            calibrated_home, calibrated_away = self._apply_score_calibration(
                expected_runs['home_team'], expected_runs['away_team']
            )

            expected_runs['home_team'] = calibrated_home
            expected_runs['away_team'] = calibrated_away
            expected_runs['total'] = calibrated_home + calibrated_away

            # 主場優勢調整 (在校正後應用)
            expected_runs['home_team'] += 0.2  # 主場優勢
            expected_runs['total'] += 0.2

            return expected_runs

        except Exception as e:
            logger.error(f"計算預期得分失敗: {e}")
            return {'home_team': 4.5, 'away_team': 4.5, 'total': 9.0}

    def _apply_score_calibration(self, home_score: float, away_score: float) -> tuple:
        """
        改進的得分校正算法 - 基於實際數據分析
        修復嚴重的系統性高估偏差 (+2.12分)
        """
        # 更激進的校正因子 (基於7.9%準確率分析)
        HOME_MULTIPLIER = 0.65   # 大幅降低主隊預測得分35%
        AWAY_MULTIPLIER = 0.75   # 大幅降低客隊預測得分25%
        TOTAL_CAP = 9.5          # 大幅降低總分上限
        LOW_FLOOR = 5.0          # 提高最低分限制
        HIGH_PENALTY = 0.7       # 加強高分懲罰

        # 應用基本校正
        calibrated_home = home_score * HOME_MULTIPLIER
        calibrated_away = away_score * AWAY_MULTIPLIER

        total_score = calibrated_home + calibrated_away

        # 應用總分限制 (更嚴格)
        if total_score > TOTAL_CAP:
            # 按比例降低
            scale_factor = TOTAL_CAP / total_score
            calibrated_home *= scale_factor
            calibrated_away *= scale_factor
            total_score = TOTAL_CAP

        # 應用最低分限制
        if total_score < LOW_FLOOR:
            # 按比例提高
            scale_factor = LOW_FLOOR / total_score
            calibrated_home *= scale_factor
            calibrated_away *= scale_factor

        # 加強高分懲罰 (對8分以上的預測進一步懲罰)
        if total_score > 8:
            calibrated_home *= HIGH_PENALTY
            calibrated_away *= HIGH_PENALTY

        return calibrated_home, calibrated_away

    def _determine_total_line(self, expected_runs: Dict, game: Game) -> float:
        """確定大小分盤口 - 優先使用真實博彩盤口"""
        try:
            # 1. 嘗試通過球隊名稱獲取真實博彩盤口
            real_odds = self.betting_odds_fetcher.get_game_odds_by_teams(
                game.home_team, game.away_team
            )
            if real_odds and real_odds.get('total', {}).get('line'):
                real_total_line = real_odds['total']['line']
                logger.info(f"使用真實博彩盤口: {real_total_line}")
                return float(real_total_line)

            # 2. 如果無法獲取真實盤口，使用算法計算
            logger.info("無法獲取真實盤口，使用算法計算")
            base_total = expected_runs['total']

            # 球場因素調整
            park_factor = self._get_park_factor(game.venue)

            # 天氣因素調整
            weather_adjustment = self._get_weather_adjustment(game)

            # 計算最終盤口
            total_line = base_total + park_factor + weather_adjustment

            # 調整到常見的盤口數值 (x.5)
            total_line = round(total_line * 2) / 2

            # 確保在合理範圍內
            total_line = max(6.5, min(13.5, total_line))

            return total_line

        except Exception as e:
            logger.error(f"確定大小分盤口失敗: {e}")
            return self.default_total_line

    def _get_park_factor(self, venue: str) -> float:
        """獲取球場因素"""
        # 簡化的球場因素
        pitcher_parks = [
            'Petco Park', 'Marlins Park', 'Tropicana Field',
            'Kauffman Stadium', 'Comerica Park'
        ]
        hitter_parks = [
            'Coors Field', 'Fenway Park', 'Yankee Stadium',
            'Minute Maid Park', 'Great American Ball Park'
        ]

        if venue in pitcher_parks:
            return -0.5  # 投手球場，降低總分
        elif venue in hitter_parks:
            return 0.5   # 打者球場，提高總分
        else:
            return 0.0   # 中性球場

    def _get_weather_adjustment(self, game: Game) -> float:
        """獲取天氣調整"""
        try:
            adjustment = 0.0

            # 溫度調整
            if game.temperature:
                if game.temperature > 80:
                    adjustment += 0.2  # 高溫有利打擊
                elif game.temperature < 50:
                    adjustment -= 0.2  # 低溫不利打擊

            # 天氣狀況調整
            if game.weather:
                weather_lower = game.weather.lower()
                if 'wind' in weather_lower and 'out' in weather_lower:
                    adjustment -= 0.3  # 逆風不利打擊
                elif 'wind' in weather_lower and 'in' in weather_lower:
                    adjustment += 0.3  # 順風有利打擊
                elif any(word in weather_lower for word in ['rain', 'storm', 'cloudy']):
                    adjustment -= 0.1  # 惡劣天氣略微不利

            return adjustment

        except Exception as e:
            logger.error(f"獲取天氣調整失敗: {e}")
            return 0.0

    def _calculate_over_under_probabilities(self, expected_runs: Dict, total_line: float,
                                          matchup_analysis: Dict) -> Dict:
        """計算大小分概率"""
        try:
            expected_total = expected_runs['total']

            # 基於預期得分與盤口的差距計算概率
            difference = expected_total - total_line

            # 使用邏輯函數計算概率
            # 當預期得分遠高於盤口時，大分概率高
            # 當預期得分遠低於盤口時，小分概率高
            over_prob = 1 / (1 + np.exp(-difference * 2))
            under_prob = 1 - over_prob

            # 基於對戰分析調整信心度
            confidence = self._calculate_confidence(matchup_analysis, difference)

            # 確定推薦
            if over_prob > 0.6:
                recommendation = f"推薦大分 (Over {total_line})"
            elif under_prob > 0.6:
                recommendation = f"推薦小分 (Under {total_line})"
            else:
                recommendation = "中性，不推薦投注"

            return {
                'over': round(over_prob, 3),
                'under': round(under_prob, 3),
                'confidence': round(confidence, 3),
                'recommendation': recommendation,
                'expected_vs_line': round(difference, 1)
            }

        except Exception as e:
            logger.error(f"計算大小分概率失敗: {e}")
            return {
                'over': 0.5, 'under': 0.5, 'confidence': 0.5,
                'recommendation': '數據不足，無法推薦'
            }

    def _calculate_confidence(self, matchup_analysis: Dict, expected_difference: float) -> float:
        """計算預測信心度"""
        try:
            base_confidence = 0.5

            # 基於歷史對戰數據的信心度調整
            summary = matchup_analysis.get('matchup_summary', {})
            historical_matchups = summary.get('total_historical_matchups', 0)

            # 歷史數據越多，信心度越高
            history_bonus = min(0.3, historical_matchups * 0.02)

            # 預期差距越大，信心度越高
            difference_bonus = min(0.2, abs(expected_difference) * 0.1)

            confidence = base_confidence + history_bonus + difference_bonus

            return min(0.95, confidence)  # 最高95%信心度

        except Exception as e:
            logger.error(f"計算信心度失敗: {e}")
            return 0.5

    def _identify_key_factors(self, matchup_analysis: Dict) -> List[str]:
        """識別關鍵影響因素"""
        try:
            factors = []

            # 分析對戰優勢
            advantages = matchup_analysis.get('advantage_analysis', {})
            overall_advantage = advantages.get('overall_advantage', 'neutral')

            if overall_advantage == 'home':
                factors.append("主隊在投打對戰中佔優勢")
            elif overall_advantage == 'away':
                factors.append("客隊在投打對戰中佔優勢")

            # 分析關鍵對戰
            summary = matchup_analysis.get('matchup_summary', {})
            key_matchups = summary.get('key_matchups', [])

            # 找出最有利的對戰
            for matchup in key_matchups[:3]:  # 只顯示前3個關鍵對戰
                if matchup.get('advantage') == 'batter':
                    factors.append(
                        f"{matchup['batter']} 對 {matchup['pitcher']} 有優勢 "
                        f"(打擊率 {matchup['batting_avg']:.3f})"
                    )
                elif matchup.get('advantage') == 'pitcher':
                    factors.append(
                        f"{matchup['pitcher']} 對 {matchup['batter']} 有優勢 "
                        f"(被打擊率 {matchup['batting_avg']:.3f})"
                    )

            # 如果沒有特殊因素，添加一般性說明
            if not factors:
                factors.append("雙方實力相當，預測基於聯盟平均數據")

            return factors

        except Exception as e:
            logger.error(f"識別關鍵因素失敗: {e}")
            return ["數據分析中出現錯誤"]

    def batch_predict_over_under(self, target_date: date = None) -> Dict:
        """批量預測當日所有比賽的大小分"""
        try:
            if target_date is None:
                target_date = date.today()

            logger.info(f"開始批量預測 {target_date} 的大小分...")

            # 獲取當日比賽
            with self.app.app_context():
                games = Game.query.filter(
                    Game.date == target_date,
                    Game.game_status.in_(['scheduled', 'pre-game'])
                ).all()

            results = {
                'date': target_date.isoformat(),
                'total_games': len(games),
                'successful_predictions': 0,
                'failed_predictions': 0,
                'predictions': []
            }

            for game in games:
                try:
                    prediction = self.predict_over_under(game.game_id, target_date)
                    if 'error' not in prediction:
                        results['successful_predictions'] += 1
                        results['predictions'].append(prediction)
                    else:
                        results['failed_predictions'] += 1
                        logger.error(f"預測失敗 {game.game_id}: {prediction['error']}")

                except Exception as e:
                    results['failed_predictions'] += 1
                    logger.error(f"預測比賽失敗 {game.game_id}: {e}")

            logger.info(f"批量預測完成: {results['successful_predictions']}/{len(games)} 成功")
            return results

        except Exception as e:
            logger.error(f"批量預測失敗: {e}")
            return {'error': str(e)}
