"""
可靠的博彩賠率獲取器 - 多 API 自動切換
不依賴外部包，使用直接 HTTP 請求實現多 API 切換
"""

import logging
import requests
import time
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from .free_api_fetcher import FreeAPIFetcher

logger = logging.getLogger(__name__)

class APIProvider(Enum):
    """API 提供商枚舉"""
    THE_ODDS_API = "the_odds_api"
    RAPIDAPI_MLB = "rapidapi_mlb"
    BACKUP_API = "backup_api"

@dataclass
class APIEndpoint:
    """API 端點配置"""
    name: str
    provider: APIProvider
    base_url: str
    headers: Dict[str, str]
    params: Dict[str, str]
    priority: int
    rate_limit_per_hour: int
    is_active: bool = True
    last_error: Optional[str] = None
    request_count: int = 0
    last_reset_time: datetime = None

class ReliableOddsFetcher:
    """可靠的博彩賠率獲取器"""
    
    def __init__(self):
        self.endpoints: List[APIEndpoint] = []
        self.session = self._create_session()
        self.free_api_fetcher = FreeAPIFetcher()
        self._setup_endpoints()
    
    def _create_session(self) -> requests.Session:
        """創建 HTTP 會話"""
        session = requests.Session()
        session.timeout = 30
        return session
    
    def _setup_endpoints(self):
        """設置 API 端點"""
        # 1. The Odds API (主要)
        odds_api_key = self._load_api_key('models/odds-api.txt')
        if odds_api_key:
            self.endpoints.append(APIEndpoint(
                name="The Odds API",
                provider=APIProvider.THE_ODDS_API,
                base_url="https://api.the-odds-api.com/v4/sports/baseball_mlb/odds",
                headers={"Content-Type": "application/json"},
                params={
                    "apiKey": odds_api_key,
                    "regions": "us",
                    "markets": "h2h,spreads,totals",
                    "oddsFormat": "decimal",
                    "dateFormat": "iso"
                },
                priority=1,
                rate_limit_per_hour=500,
                last_reset_time=datetime.now()
            ))
        
        # 2. RapidAPI MLB (備用)
        rapidapi_key = self._load_api_key('models/rapidapi-key.txt')
        if rapidapi_key:
            self.endpoints.append(APIEndpoint(
                name="RapidAPI MLB",
                provider=APIProvider.RAPIDAPI_MLB,
                base_url="https://major-league-baseball-mlb.p.rapidapi.com/odds",
                headers={
                    "X-RapidAPI-Key": rapidapi_key,
                    "X-RapidAPI-Host": "major-league-baseball-mlb.p.rapidapi.com",
                    "Content-Type": "application/json"
                },
                params={},
                priority=2,
                rate_limit_per_hour=100,
                last_reset_time=datetime.now()
            ))
        
        # 3. 備用 API (可以添加更多)
        self._add_backup_endpoints()
        
        # 按優先級排序
        self.endpoints.sort(key=lambda x: x.priority)
        
        logger.info(f"已設置 {len(self.endpoints)} 個 API 端點")
    
    def _load_api_key(self, file_path: str) -> Optional[str]:
        """載入 API 密鑰"""
        try:
            with open(file_path, 'r') as f:
                key = f.read().strip()
                if key:
                    logger.info(f"成功載入 API 密鑰: {file_path}")
                    return key
        except FileNotFoundError:
            logger.warning(f"API 密鑰文件不存在: {file_path}")
        except Exception as e:
            logger.error(f"讀取 API 密鑰失敗 {file_path}: {e}")
        return None
    
    def _add_backup_endpoints(self):
        """添加備用 API 端點"""
        # 可以在這裡添加更多備用 API
        # 例如：其他免費的體育數據 API
        pass
    
    def _check_rate_limit(self, endpoint: APIEndpoint) -> bool:
        """檢查速率限制"""
        now = datetime.now()
        
        # 如果超過1小時，重置計數器
        if endpoint.last_reset_time and (now - endpoint.last_reset_time).seconds > 3600:
            endpoint.request_count = 0
            endpoint.last_reset_time = now
        
        return endpoint.request_count < endpoint.rate_limit_per_hour
    
    def _update_endpoint_usage(self, endpoint: APIEndpoint, success: bool, error: str = None):
        """更新端點使用統計"""
        endpoint.request_count += 1
        
        if success:
            endpoint.is_active = True
            endpoint.last_error = None
            logger.info(f"API {endpoint.name} 請求成功")
        else:
            endpoint.last_error = error
            if "401" in str(error) or "403" in str(error):
                endpoint.is_active = False
                logger.error(f"API {endpoint.name} 認證失敗，已停用: {error}")
            elif "429" in str(error):
                logger.warning(f"API {endpoint.name} 達到速率限制: {error}")
            else:
                logger.warning(f"API {endpoint.name} 請求失敗: {error}")
    
    def get_available_endpoints(self) -> List[APIEndpoint]:
        """獲取可用的端點"""
        available = []
        for endpoint in self.endpoints:
            if endpoint.is_active and self._check_rate_limit(endpoint):
                available.append(endpoint)
        return available
    
    def fetch_mlb_odds(self, target_date: date = None) -> Dict[str, Any]:
        """獲取 MLB 賠率 - 自動切換 API"""
        if not target_date:
            target_date = date.today()
        
        available_endpoints = self.get_available_endpoints()
        
        if not available_endpoints:
            return {
                "success": False,
                "error": "沒有可用的 API 端點",
                "timestamp": datetime.now().isoformat()
            }
        
        logger.info(f"開始獲取 {target_date} 的 MLB 賠率，可用端點: {len(available_endpoints)}")
        
        for endpoint in available_endpoints:
            try:
                logger.info(f"嘗試使用 API: {endpoint.name}")
                
                result = self._fetch_from_endpoint(endpoint, target_date)
                
                if result and result.get('success'):
                    self._update_endpoint_usage(endpoint, True)
                    result['source'] = endpoint.name
                    result['provider'] = endpoint.provider.value
                    return result
                else:
                    error_msg = result.get('error', '未知錯誤') if result else '返回空結果'
                    self._update_endpoint_usage(endpoint, False, error_msg)
                    
            except Exception as e:
                error_msg = str(e)
                self._update_endpoint_usage(endpoint, False, error_msg)
                logger.warning(f"API {endpoint.name} 異常: {error_msg}")
                continue
        
        # 如果所有付費 API 都失敗，嘗試免費 API
        logger.info("所有付費 API 都失敗，嘗試免費 API...")
        free_result = self.free_api_fetcher.get_combined_free_data(target_date)

        if free_result.get('success'):
            logger.info(f"免費 API 成功獲取數據: {free_result.get('total_games', 0)} 場比賽")
            free_result['is_free_api'] = True
            free_result['note'] = "數據來自免費 API，不包含博彩賠率"
            return free_result

        return {
            "success": False,
            "error": "所有 API 端點（包括免費 API）都失敗",
            "timestamp": datetime.now().isoformat()
        }
    
    def _fetch_from_endpoint(self, endpoint: APIEndpoint, target_date: date) -> Dict[str, Any]:
        """從指定端點獲取數據"""
        try:
            if endpoint.provider == APIProvider.THE_ODDS_API:
                return self._fetch_from_odds_api(endpoint)
            elif endpoint.provider == APIProvider.RAPIDAPI_MLB:
                return self._fetch_from_rapidapi(endpoint)
            else:
                return self._fetch_from_custom_api(endpoint)
                
        except requests.exceptions.RequestException as e:
            logger.error(f"網絡請求失敗: {e}")
            return {"success": False, "error": f"網絡請求失敗: {e}"}
        except Exception as e:
            logger.error(f"數據獲取失敗: {e}")
            return {"success": False, "error": f"數據獲取失敗: {e}"}
    
    def _fetch_from_odds_api(self, endpoint: APIEndpoint) -> Dict[str, Any]:
        """從 The Odds API 獲取數據"""
        response = self.session.get(
            endpoint.base_url,
            params=endpoint.params,
            headers=endpoint.headers
        )
        
        response.raise_for_status()
        data = response.json()
        
        return {
            "success": True,
            "games": data,
            "timestamp": datetime.now().isoformat(),
            "api_info": {
                "requests_remaining": response.headers.get('x-requests-remaining', 'N/A'),
                "requests_used": response.headers.get('x-requests-used', 'N/A')
            }
        }
    
    def _fetch_from_rapidapi(self, endpoint: APIEndpoint) -> Dict[str, Any]:
        """從 RapidAPI 獲取數據"""
        response = self.session.get(
            endpoint.base_url,
            params=endpoint.params,
            headers=endpoint.headers
        )
        
        response.raise_for_status()
        data = response.json()
        
        return {
            "success": True,
            "games": data,
            "timestamp": datetime.now().isoformat()
        }
    
    def _fetch_from_custom_api(self, endpoint: APIEndpoint) -> Dict[str, Any]:
        """從自定義 API 獲取數據"""
        logger.warning(f"自定義 API {endpoint.name} 尚未實現")
        return {"success": False, "error": "自定義 API 尚未實現"}
    
    def get_status_report(self) -> Dict[str, Any]:
        """獲取狀態報告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_endpoints": len(self.endpoints),
            "active_endpoints": len([e for e in self.endpoints if e.is_active]),
            "available_endpoints": len(self.get_available_endpoints()),
            "endpoints": [],
            "free_apis": []
        }

        for endpoint in self.endpoints:
            endpoint_info = {
                "name": endpoint.name,
                "provider": endpoint.provider.value,
                "priority": endpoint.priority,
                "is_active": endpoint.is_active,
                "request_count": endpoint.request_count,
                "rate_limit": endpoint.rate_limit_per_hour,
                "last_error": endpoint.last_error,
                "rate_limit_ok": self._check_rate_limit(endpoint)
            }
            report["endpoints"].append(endpoint_info)

        # 添加免費 API 狀態
        try:
            free_api_report = self.free_api_fetcher.get_status_report()
            report["free_apis"] = free_api_report.get("apis", [])
        except Exception as e:
            logger.warning(f"獲取免費 API 狀態失敗: {e}")

        return report
    
    def reset_endpoint(self, endpoint_name: str) -> bool:
        """重置端點狀態"""
        for endpoint in self.endpoints:
            if endpoint.name == endpoint_name:
                endpoint.is_active = True
                endpoint.last_error = None
                endpoint.request_count = 0
                endpoint.last_reset_time = datetime.now()
                logger.info(f"已重置端點: {endpoint_name}")
                return True
        return False
    
    def add_api_key(self, provider: str, api_key: str) -> bool:
        """動態添加 API 密鑰"""
        try:
            if provider.lower() == "rapidapi":
                file_path = "models/rapidapi-key.txt"
            elif provider.lower() == "odds":
                file_path = "models/odds-api.txt"
            else:
                return False
            
            with open(file_path, 'w') as f:
                f.write(api_key)
            
            # 重新設置端點
            self.endpoints.clear()
            self._setup_endpoints()
            
            logger.info(f"已添加 {provider} API 密鑰並重新初始化端點")
            return True
            
        except Exception as e:
            logger.error(f"添加 API 密鑰失敗: {e}")
            return False
