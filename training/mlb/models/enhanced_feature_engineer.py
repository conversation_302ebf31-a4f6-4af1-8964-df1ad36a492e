"""
增強特徵工程器 - 提升預測準確率
包含投手對戰、天氣、疲勞等高級特徵
"""

import logging
import numpy as np
import pandas as pd
from datetime import date, timedelta
from typing import Dict, List, Optional
from sqlalchemy import text
from .database import db, Game, Team, BoxScore, Player, PlayerStats
from .feature_engineer import FeatureEngineer

logger = logging.getLogger(__name__)

class EnhancedFeatureEngineer(FeatureEngineer):
    """增強版特徵工程器"""
    
    def __init__(self):
        super().__init__()
        self.pitcher_cache = {}
        self.weather_cache = {}
        
    def extract_comprehensive_features(self, home_team: str, away_team: str, game_date: date, game_id: str = None) -> Dict:
        """提取全面的比賽特徵（增強版）"""
        try:
            # 獲取基礎特徵
            features = super().extract_comprehensive_features(home_team, away_team, game_date)

            # 添加增強特徵 - 傳遞game_id確保每場比賽獨立載入投手信息
            features.update(self._get_pitcher_matchup_features(home_team, away_team, game_date, game_id))
            features.update(self._get_ace_pitcher_factors(home_team, away_team, game_date, game_id))  # 🆕 王牌投手因素
            features.update(self._get_fatigue_features(home_team, away_team, game_date))
            features.update(self._get_weather_impact_features(home_team, game_date))
            features.update(self._get_injury_impact_features(home_team, away_team, game_date))
            features.update(self._get_elo_rating_features(home_team, away_team, game_date))
            features.update(self._get_situational_features(home_team, away_team, game_date))

            return features

        except Exception as e:
            logger.error(f"增強特徵提取失敗 {away_team} @ {home_team}: {e}")
            return {}
    
    def _get_pitcher_matchup_features(self, home_team: str, away_team: str, game_date: date, game_id: str = None) -> Dict:
        """獲取投手對戰特徵"""
        try:
            features = {}

            # 獲取可能的先發投手 - 傳遞game_id確保每場比賽獨立載入
            home_pitcher_info = self._get_probable_starter(home_team, game_date, game_id)
            away_pitcher_info = self._get_probable_starter(away_team, game_date, game_id)

            # 提取投手姓名
            home_pitcher = home_pitcher_info.get('name') if home_pitcher_info else None
            away_pitcher = away_pitcher_info.get('name') if away_pitcher_info else None

            if home_pitcher and away_pitcher:
                # 投手vs對手打線歷史表現
                home_pitcher_vs_away = self._get_pitcher_vs_team_stats(home_pitcher, away_team)
                away_pitcher_vs_home = self._get_pitcher_vs_team_stats(away_pitcher, home_team)
                
                features.update({
                    'home_pitcher_vs_opponent_era': home_pitcher_vs_away.get('era', 4.50),
                    'home_pitcher_vs_opponent_whip': home_pitcher_vs_away.get('whip', 1.30),
                    'home_pitcher_vs_opponent_k9': home_pitcher_vs_away.get('k9', 8.0),
                    'away_pitcher_vs_opponent_era': away_pitcher_vs_home.get('era', 4.50),
                    'away_pitcher_vs_opponent_whip': away_pitcher_vs_home.get('whip', 1.30),
                    'away_pitcher_vs_opponent_k9': away_pitcher_vs_home.get('k9', 8.0),
                })
                
                # 投手最近表現
                home_pitcher_recent = self._get_pitcher_recent_performance(home_pitcher)
                away_pitcher_recent = self._get_pitcher_recent_performance(away_pitcher)
                
                features.update({
                    'home_pitcher_recent_era': home_pitcher_recent.get('era', 4.50),
                    'home_pitcher_recent_innings': home_pitcher_recent.get('innings', 5.0),
                    'away_pitcher_recent_era': away_pitcher_recent.get('era', 4.50),
                    'away_pitcher_recent_innings': away_pitcher_recent.get('innings', 5.0),
                })
                
                # 投手優勢對比
                features['pitcher_advantage'] = self._calculate_pitcher_advantage(
                    home_pitcher_vs_away, away_pitcher_vs_home
                )
            
            return features
            
        except Exception as e:
            logger.warning(f"投手對戰特徵提取失敗: {e}")
            return {}
    
    def _get_fatigue_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取疲勞特徵"""
        try:
            features = {}
            
            # 計算球隊疲勞度
            home_fatigue = self._calculate_team_fatigue(home_team, game_date)
            away_fatigue = self._calculate_team_fatigue(away_team, game_date)
            
            features.update({
                'home_team_fatigue': home_fatigue,
                'away_team_fatigue': away_fatigue,
                'fatigue_advantage': home_fatigue - away_fatigue,
            })
            
            # 旅行疲勞
            home_travel_fatigue = self._calculate_travel_fatigue(home_team, game_date)
            away_travel_fatigue = self._calculate_travel_fatigue(away_team, game_date)
            
            features.update({
                'home_travel_fatigue': home_travel_fatigue,
                'away_travel_fatigue': away_travel_fatigue,
            })
            
            return features
            
        except Exception as e:
            logger.warning(f"疲勞特徵提取失敗: {e}")
            return {}
    
    def _get_weather_impact_features(self, home_team: str, game_date: date) -> Dict:
        """獲取天氣影響特徵"""
        try:
            features = {}
            
            # 獲取球場信息
            venue_info = self._get_venue_info(home_team)
            
            if venue_info.get('is_dome', False):
                # 室內球場，天氣影響較小
                features.update({
                    'weather_impact_hitting': 0.0,
                    'weather_impact_pitching': 0.0,
                    'wind_factor': 0.0,
                    'temperature_factor': 0.0,
                })
            else:
                # 室外球場，獲取天氣數據
                weather = self._get_weather_data(home_team, game_date)
                
                features.update({
                    'temperature': weather.get('temperature', 70),
                    'humidity': weather.get('humidity', 50),
                    'wind_speed': weather.get('wind_speed', 5),
                    'weather_impact_hitting': self._calculate_weather_hitting_impact(weather),
                    'weather_impact_pitching': self._calculate_weather_pitching_impact(weather),
                    'wind_factor': self._calculate_wind_factor(weather),
                    'temperature_factor': self._calculate_temperature_factor(weather),
                })
            
            return features
            
        except Exception as e:
            logger.warning(f"天氣特徵提取失敗: {e}")
            return {}
    
    def _get_injury_impact_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取傷兵影響特徵"""
        try:
            features = {}
            
            # 計算傷兵影響分數
            home_injury_impact = self._calculate_injury_impact(home_team, game_date)
            away_injury_impact = self._calculate_injury_impact(away_team, game_date)
            
            features.update({
                'home_injury_impact': home_injury_impact,
                'away_injury_impact': away_injury_impact,
                'injury_advantage': away_injury_impact - home_injury_impact,  # 對手傷兵多對己方有利
            })
            
            return features
            
        except Exception as e:
            logger.warning(f"傷兵影響特徵提取失敗: {e}")
            return {}
    
    def _get_elo_rating_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取ELO評分特徵"""
        try:
            features = {}
            
            # 計算動態ELO評分
            home_elo = self._calculate_team_elo(home_team, game_date)
            away_elo = self._calculate_team_elo(away_team, game_date)
            
            features.update({
                'home_elo_rating': home_elo,
                'away_elo_rating': away_elo,
                'elo_difference': home_elo - away_elo,
                'elo_win_probability': self._elo_win_probability(home_elo, away_elo),
            })
            
            return features
            
        except Exception as e:
            logger.warning(f"ELO評分特徵提取失敗: {e}")
            return {}
    
    def _get_situational_features(self, home_team: str, away_team: str, game_date: date) -> Dict:
        """獲取情境特徵"""
        try:
            features = {}
            
            # 季節情境
            month = game_date.month
            features.update({
                'is_early_season': 1 if month <= 5 else 0,
                'is_mid_season': 1 if 6 <= month <= 8 else 0,
                'is_late_season': 1 if month >= 9 else 0,
                'days_into_season': (game_date - date(game_date.year, 4, 1)).days,
            })
            
            # 排名壓力
            home_standings = self._get_team_standings(home_team, game_date)
            away_standings = self._get_team_standings(away_team, game_date)
            
            features.update({
                'home_playoff_pressure': self._calculate_playoff_pressure(home_standings),
                'away_playoff_pressure': self._calculate_playoff_pressure(away_standings),
                'standings_difference': home_standings.get('rank', 15) - away_standings.get('rank', 15),
            })
            
            return features
            
        except Exception as e:
            logger.warning(f"情境特徵提取失敗: {e}")
            return {}
    
    def _get_team_name_mapping(self, team_code: str) -> str:
        """將球隊代碼映射為全名"""
        team_mapping = {
            'CHC': 'Cubs', 'NYY': 'Yankees', 'SEA': 'Mariners', 'DET': 'Tigers',
            'PIT': 'Pirates', 'MIN': 'Twins', 'ATL': 'Braves', 'STL': 'Cardinals',
            'LAD': 'Dodgers', 'SF': 'Giants', 'MIA': 'Marlins', 'BAL': 'Orioles',
            'COL': 'Rockies', 'CIN': 'Reds', 'NYM': 'Mets', 'KC': 'Royals',
            'TB': 'Rays', 'BOS': 'Red Sox', 'CLE': 'Guardians', 'CWS': 'White Sox',
            'WSH': 'Nationals', 'MIL': 'Brewers', 'TEX': 'Rangers', 'HOU': 'Astros',
            'PHI': 'Phillies', 'SD': 'Padres', 'TOR': 'Blue Jays', 'OAK': 'Athletics',
            'ARI': 'D-backs', 'LAA': 'Angels'
        }
        return team_mapping.get(team_code, team_code)

    def _get_pitcher_from_game_record(self, game_id: str, team: str) -> Optional[str]:
        """從比賽記錄中獲取先發投手"""
        try:
            # 檢查是否已有該比賽的投手緩存
            cache_key = f"{game_id}_{team}"
            if cache_key in self.pitcher_cache:
                return self.pitcher_cache[cache_key]

            # 查詢GameDetail表
            from models.database import GameDetail, Game
            game = Game.query.filter_by(game_id=game_id).first()
            if game:
                game_detail = GameDetail.query.filter_by(game_id=game_id).first()
                if game_detail:
                    if team == game.home_team and game_detail.home_starting_pitcher:
                        return game_detail.home_starting_pitcher
                    elif team == game.away_team and game_detail.away_starting_pitcher:
                        return game_detail.away_starting_pitcher

            return None

        except Exception as e:
            logger.error(f"從比賽記錄獲取投手失敗: {e}")
            return None

    def _is_target_game(self, game_info: dict, team: str, game_id: str, game_date: date) -> bool:
        """判斷是否為目標比賽"""
        try:
            # 簡單的匹配邏輯：檢查球隊是否在比賽中
            home_team = game_info.get('home_team', '')
            away_team = game_info.get('away_team', '')
            team_full_name = self._get_team_name_mapping(team)

            # 如果球隊匹配，認為是目標比賽
            return (team_full_name in home_team or team in home_team or
                   team_full_name in away_team or team in away_team)

        except Exception as e:
            logger.error(f"判斷目標比賽失敗: {e}")
            return False

    def _cache_pitcher_for_game(self, game_id: str, team: str, pitcher_name: str):
        """為特定比賽緩存投手信息"""
        try:
            cache_key = f"{game_id}_{team}"
            self.pitcher_cache[cache_key] = pitcher_name
            logger.debug(f"緩存投手信息: {cache_key} -> {pitcher_name}")

        except Exception as e:
            logger.error(f"緩存投手信息失敗: {e}")

    def _get_probable_starter(self, team: str, game_date: date, game_id: str = None) -> Optional[Dict]:
        """獲取可能的先發投手 - 支持比賽級別的精確匹配，返回投手信息和狀態"""
        try:
            # 🎯 關鍵改進：如果有game_id，優先使用比賽級別的投手查詢
            if game_id:
                # 1. 首先檢查是否已有該比賽的投手記錄
                cached_pitcher = self._get_pitcher_from_game_record(game_id, team)
                if cached_pitcher:
                    logger.info(f"從比賽記錄找到 {team} 先發投手: {cached_pitcher} (game_id: {game_id})")
                    return {
                        'name': cached_pitcher,
                        'status': 'found',
                        'source': 'game_record',
                        'game_id': game_id
                    }

            # 2. 嘗試從DailyLineupFetcher獲取實際先發投手
            from models.daily_lineup_fetcher import DailyLineupFetcher
            lineup_fetcher = DailyLineupFetcher()

            # 獲取當日打線信息
            daily_data = lineup_fetcher.get_daily_lineups(game_date)

            # 將球隊代碼轉換為全名進行匹配
            team_full_name = self._get_team_name_mapping(team)

            # 🎯 關鍵改進：如果有game_id，嘗試精確匹配特定比賽
            target_pitcher = None

            for game_info in daily_data.get('games', []):
                home_team = game_info.get('home_team', '')
                away_team = game_info.get('away_team', '')

                # 檢查是否為目標比賽 (如果有game_id的話)
                if game_id:
                    # 嘗試通過球隊組合匹配特定比賽
                    game_match = self._is_target_game(game_info, team, game_id, game_date)
                    if not game_match:
                        continue

                # 檢查主隊匹配
                if team_full_name in home_team or team in home_team:
                    pitcher_info = game_info.get('home_pitcher')
                    if pitcher_info:
                        # 處理字符串或字典格式的投手信息
                        pitcher_name = pitcher_info if isinstance(pitcher_info, str) else pitcher_info.get('name')
                        if pitcher_name:
                            target_pitcher = {
                                'name': pitcher_name,
                                'status': 'found',
                                'source': 'daily_lineup_api',
                                'position': 'home',
                                'game_id': game_id
                            }
                            logger.info(f"✅ 找到 {team} ({team_full_name}) 主場先發投手: {pitcher_name} (game_id: {game_id or 'N/A'})")
                            break

                # 檢查客隊匹配
                elif team_full_name in away_team or team in away_team:
                    pitcher_info = game_info.get('away_pitcher')
                    if pitcher_info:
                        # 處理字符串或字典格式的投手信息
                        pitcher_name = pitcher_info if isinstance(pitcher_info, str) else pitcher_info.get('name')
                        if pitcher_name:
                            target_pitcher = {
                                'name': pitcher_name,
                                'status': 'found',
                                'source': 'daily_lineup_api',
                                'position': 'away',
                                'game_id': game_id
                            }
                            logger.info(f"✅ 找到 {team} ({team_full_name}) 客場先發投手: {pitcher_name} (game_id: {game_id or 'N/A'})")
                            break

            # 🎯 關鍵改進：如果找到投手且有game_id，緩存結果
            if target_pitcher and game_id:
                pitcher_name = target_pitcher.get('name') if isinstance(target_pitcher, dict) else target_pitcher
                self._cache_pitcher_for_game(game_id, team, pitcher_name)

            if target_pitcher:
                return target_pitcher

            # 如果API沒有數據，嘗試從GameDetail表獲取
            from models.database import GameDetail
            game_detail = GameDetail.query.join(Game).filter(
                Game.date == game_date,
                ((Game.home_team == team) | (Game.away_team == team))
            ).first()

            if game_detail:
                if game_detail.game.home_team == team and game_detail.home_starting_pitcher:
                    logger.info(f"從數據庫找到 {team} 主場先發投手: {game_detail.home_starting_pitcher}")
                    return {
                        'name': game_detail.home_starting_pitcher,
                        'status': 'found',
                        'source': 'database',
                        'position': 'home'
                    }
                elif game_detail.game.away_team == team and game_detail.away_starting_pitcher:
                    logger.info(f"從數據庫找到 {team} 客場先發投手: {game_detail.away_starting_pitcher}")
                    return {
                        'name': game_detail.away_starting_pitcher,
                        'status': 'found',
                        'source': 'database',
                        'position': 'away'
                    }

            logger.warning(f"❌ 無法找到 {team} 在 {game_date} 的先發投手信息")
            return {
                'name': None,
                'status': 'not_found',
                'source': 'none',
                'team': team,
                'game_date': str(game_date),
                'game_id': game_id
            }

        except Exception as e:
            logger.warning(f"❌ 獲取先發投手失敗: {e}")
            return {
                'name': None,
                'status': 'error',
                'source': 'none',
                'team': team,
                'game_date': str(game_date),
                'game_id': game_id,
                'error_message': str(e)
            }

    def _get_pitcher_stats_by_name(self, pitcher_name: str) -> Optional[Dict]:
        """根據投手姓名獲取統計數據"""
        if not pitcher_name:
            return None

        try:
            from models.database import PlayerStats

            # 嘗試多種查詢方式
            pitcher_stats = None
            search_method = None

            # 1. 精確匹配
            pitcher_stats = PlayerStats.query.filter(
                PlayerStats.player_name == pitcher_name,
                PlayerStats.innings_pitched > 0,
                PlayerStats.season == date.today().year
            ).first()
            if pitcher_stats:
                search_method = "exact_match_current_year"

            # 2. 如果沒找到，嘗試模糊匹配
            if not pitcher_stats:
                pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.player_name.like(f'%{pitcher_name}%'),
                    PlayerStats.innings_pitched > 0,
                    PlayerStats.season == date.today().year
                ).first()
                if pitcher_stats:
                    search_method = "fuzzy_match_current_year"

            # 3. 如果還沒找到，嘗試去年的數據
            if not pitcher_stats:
                pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.player_name.like(f'%{pitcher_name}%'),
                    PlayerStats.innings_pitched > 0,
                    PlayerStats.season == date.today().year - 1
                ).first()
                if pitcher_stats:
                    search_method = "fuzzy_match_previous_year"

            if pitcher_stats:
                stats = {
                    'name': pitcher_stats.player_name,
                    'era': pitcher_stats.era or 4.50,
                    'whip': pitcher_stats.whip or 1.30,
                    'wins': pitcher_stats.wins or 0,
                    'losses': pitcher_stats.losses or 0,
                    'innings_pitched': pitcher_stats.innings_pitched or 0,
                    'strikeouts': pitcher_stats.strikeouts_pitching or 0,
                    'walks_allowed': pitcher_stats.walks_allowed or 0,
                    'hits_allowed': pitcher_stats.hits_allowed or 0,
                    'data_status': 'found',
                    'data_source': search_method,
                    'season': pitcher_stats.season
                }
                logger.info(f"✅ 找到投手 {pitcher_name} 的統計數據: ERA={stats['era']}, WHIP={stats['whip']} (來源: {search_method})")
                return stats
            else:
                logger.warning(f"❌ 找不到投手 {pitcher_name} 的統計數據，使用默認值")
                # 返回默認統計數據並標記狀態
                return {
                    'name': pitcher_name,
                    'era': 4.50,
                    'whip': 1.30,
                    'wins': 8,
                    'losses': 8,
                    'innings_pitched': 120,
                    'strikeouts': 100,
                    'walks_allowed': 45,
                    'hits_allowed': 120,
                    'data_status': 'missing_stats',
                    'data_source': 'default_values',
                    'season': date.today().year
                }

        except Exception as e:
            logger.warning(f"❌ 獲取投手統計數據失敗 {pitcher_name}: {e}")
            # 返回默認統計數據並標記錯誤
            return {
                'name': pitcher_name,
                'era': 4.50,
                'whip': 1.30,
                'wins': 8,
                'losses': 8,
                'innings_pitched': 120,
                'strikeouts': 100,
                'walks_allowed': 45,
                'hits_allowed': 120,
                'data_status': 'error',
                'data_source': 'default_values',
                'season': date.today().year,
                'error_message': str(e)
            }
    
    def _calculate_team_fatigue(self, team: str, game_date: date) -> float:
        """計算球隊疲勞度"""
        try:
            # 獲取最近7天的比賽
            recent_games = Game.query.filter(
                Game.date >= game_date - timedelta(days=7),
                Game.date < game_date,
                ((Game.home_team == team) | (Game.away_team == team)),
                Game.game_status == 'completed'
            ).all()
            
            fatigue_score = 0
            for game in recent_games:
                days_ago = (game_date - game.date).days
                # 越近的比賽疲勞影響越大
                fatigue_score += 1.0 / (days_ago + 1)
            
            return min(fatigue_score, 5.0)  # 限制在0-5範圍
            
        except Exception as e:
            logger.warning(f"計算球隊疲勞度失敗: {e}")
            return 1.0
    
    def _calculate_travel_fatigue(self, team: str, game_date: date) -> float:
        """計算旅行疲勞"""
        try:
            # 獲取前一場比賽
            prev_game = Game.query.filter(
                Game.date < game_date,
                ((Game.home_team == team) | (Game.away_team == team)),
                Game.game_status == 'completed'
            ).order_by(Game.date.desc()).first()
            
            if not prev_game:
                return 0.0
            
            # 如果前一場是客場比賽，增加旅行疲勞
            if prev_game.away_team == team:
                days_since = (game_date - prev_game.date).days
                if days_since <= 1:
                    return 2.0  # 高旅行疲勞
                elif days_since <= 2:
                    return 1.0  # 中等旅行疲勞
            
            return 0.0
            
        except Exception as e:
            logger.warning(f"計算旅行疲勞失敗: {e}")
            return 0.0
    
    def _calculate_team_elo(self, team: str, game_date: date) -> float:
        """計算球隊ELO評分"""
        try:
            # 簡化的ELO計算
            # 獲取最近30場比賽
            recent_games = Game.query.filter(
                Game.date < game_date,
                ((Game.home_team == team) | (Game.away_team == team)),
                Game.game_status == 'completed'
            ).order_by(Game.date.desc()).limit(30).all()
            
            if not recent_games:
                return 1500.0  # 默認ELO評分
            
            elo = 1500.0
            for game in reversed(recent_games):  # 從舊到新處理
                # 檢查分數是否有效
                if game.home_score is None or game.away_score is None:
                    continue

                is_home = game.home_team == team
                won = (is_home and game.home_score > game.away_score) or \
                      (not is_home and game.away_score > game.home_score)
                
                # 簡化的ELO更新
                k_factor = 32
                expected = 0.5  # 簡化假設對手實力相等
                actual = 1.0 if won else 0.0
                elo += k_factor * (actual - expected)
            
            return elo
            
        except Exception as e:
            logger.warning(f"計算ELO評分失敗: {e}")
            return 1500.0
    
    def _elo_win_probability(self, home_elo: float, away_elo: float) -> float:
        """根據ELO評分計算勝率"""
        return 1.0 / (1.0 + 10**((away_elo - home_elo) / 400))

    def _get_venue_info(self, team: str) -> Dict:
        """獲取球場信息"""
        # 簡化的球場信息
        dome_teams = ['TB', 'TOR', 'HOU', 'MIA', 'SEA', 'MIN', 'MIL', 'AZ']
        return {
            'is_dome': team in dome_teams,
            'altitude': 0,  # 簡化處理
            'dimensions': 'standard'
        }

    def _calculate_injury_impact(self, team: str, game_date: date) -> float:
        """計算傷兵影響分數"""
        # 簡化實現：返回默認值
        # 實際實現需要傷兵名單數據
        return 0.0

    def _get_team_standings(self, team: str, game_date: date) -> Dict:
        """獲取球隊排名信息"""
        try:
            # 計算球隊在該日期的戰績排名
            games = Game.query.filter(
                Game.date < game_date,
                ((Game.home_team == team) | (Game.away_team == team)),
                Game.game_status == 'completed'
            ).all()

            wins = 0
            losses = 0
            for game in games:
                if game.home_team == team:
                    if game.home_score and game.away_score:
                        if game.home_score > game.away_score:
                            wins += 1
                        else:
                            losses += 1
                else:
                    if game.home_score and game.away_score:
                        if game.away_score > game.home_score:
                            wins += 1
                        else:
                            losses += 1

            win_pct = wins / max(wins + losses, 1)
            return {
                'wins': wins,
                'losses': losses,
                'win_pct': win_pct,
                'rank': 15  # 簡化處理
            }

        except Exception as e:
            logger.warning(f"獲取球隊排名失敗: {e}")
            return {'wins': 0, 'losses': 0, 'win_pct': 0.5, 'rank': 15}

    def _get_weather_data(self, team: str, game_date: date) -> Dict:
        """獲取天氣數據"""
        # 簡化實現：返回默認天氣
        return {
            'temperature': 70,
            'humidity': 50,
            'wind_speed': 5,
            'wind_direction': 'calm'
        }

    def _calculate_weather_hitting_impact(self, weather: Dict) -> float:
        """計算天氣對打擊的影響"""
        return 0.0  # 簡化處理

    def _calculate_weather_pitching_impact(self, weather: Dict) -> float:
        """計算天氣對投球的影響"""
        return 0.0  # 簡化處理

    def _calculate_wind_factor(self, weather: Dict) -> float:
        """計算風力因子"""
        return 0.0  # 簡化處理

    def _calculate_temperature_factor(self, weather: Dict) -> float:
        """計算溫度因子"""
        return 0.0  # 簡化處理

    def _calculate_playoff_pressure(self, standings: Dict) -> float:
        """計算季後賽壓力"""
        win_pct = standings.get('win_pct', 0.5)
        # 簡化計算：勝率越高壓力越大
        return min(win_pct * 2, 1.0)

    def _get_ace_pitcher_factors(self, home_team: str, away_team: str, game_date: date, game_id: str = None) -> Dict:
        """🆕 獲取實際先發投手因素 - 使用當場比賽的實際投手"""
        try:
            features = {}

            # 獲取實際先發投手 - 傳遞game_id確保每場比賽獨立載入
            home_pitcher_info = self._get_probable_starter(home_team, game_date, game_id)
            away_pitcher_info = self._get_probable_starter(away_team, game_date, game_id)

            # 提取投手姓名和狀態
            home_pitcher_name = home_pitcher_info.get('name') if home_pitcher_info else None
            away_pitcher_name = away_pitcher_info.get('name') if away_pitcher_info else None

            # 記錄投手狀態信息到特徵中
            features['home_pitcher_status'] = home_pitcher_info.get('status', 'unknown') if home_pitcher_info else 'not_found'
            features['away_pitcher_status'] = away_pitcher_info.get('status', 'unknown') if away_pitcher_info else 'not_found'
            features['home_pitcher_source'] = home_pitcher_info.get('source', 'none') if home_pitcher_info else 'none'
            features['away_pitcher_source'] = away_pitcher_info.get('source', 'none') if away_pitcher_info else 'none'

            # 獲取實際先發投手統計
            home_pitcher_stats = self._get_pitcher_stats_by_name(home_pitcher_name) if home_pitcher_name else None
            away_pitcher_stats = self._get_pitcher_stats_by_name(away_pitcher_name) if away_pitcher_name else None

            # 記錄投手統計數據狀態
            if home_pitcher_stats:
                features['home_pitcher_data_status'] = home_pitcher_stats.get('data_status', 'unknown')
                features['home_pitcher_data_source'] = home_pitcher_stats.get('data_source', 'unknown')
            else:
                features['home_pitcher_data_status'] = 'no_stats'
                features['home_pitcher_data_source'] = 'none'

            if away_pitcher_stats:
                features['away_pitcher_data_status'] = away_pitcher_stats.get('data_status', 'unknown')
                features['away_pitcher_data_source'] = away_pitcher_stats.get('data_source', 'unknown')
            else:
                features['away_pitcher_data_status'] = 'no_stats'
                features['away_pitcher_data_source'] = 'none'

            # 如果沒有實際投手信息，回退到球隊王牌
            if not home_pitcher_stats:
                home_pitcher_stats = self._get_team_ace_pitcher_stats(home_team)
            if not away_pitcher_stats:
                away_pitcher_stats = self._get_team_ace_pitcher_stats(away_team)

            # 投手質量評分 (0-100)
            home_pitcher_quality = self._calculate_pitcher_quality(home_pitcher_stats) if home_pitcher_stats else 50.0
            away_pitcher_quality = self._calculate_pitcher_quality(away_pitcher_stats) if away_pitcher_stats else 50.0

            features.update({
                'home_pitcher_quality': home_pitcher_quality,
                'away_pitcher_quality': away_pitcher_quality,

                # 王牌投手檢測 (ERA < 3.00)
                'home_has_ace': 1 if home_pitcher_stats and home_pitcher_stats.get('era', 5.0) < 3.0 else 0,
                'away_has_ace': 1 if away_pitcher_stats and away_pitcher_stats.get('era', 5.0) < 3.0 else 0,

                # 投手對戰優勢 (-1到1, 正值表示主隊投手優勢)
                'pitcher_matchup_advantage': self._calculate_pitcher_matchup_advantage(home_pitcher_stats, away_pitcher_stats),

                # 投手ERA差距 (客隊ERA - 主隊ERA)
                'pitcher_era_differential': self._get_era_differential(home_pitcher_stats, away_pitcher_stats),

                # 投手WHIP差距
                'pitcher_whip_differential': self._get_whip_differential(home_pitcher_stats, away_pitcher_stats),

                # 實際投手對決標記
                'ace_pitcher_duel': 1 if (home_pitcher_stats and home_pitcher_stats.get('era', 5.0) < 3.0 and
                                         away_pitcher_stats and away_pitcher_stats.get('era', 5.0) < 3.0) else 0,

                # 記錄使用的投手信息 (兼容舊版本字段名)
                'home_pitcher_name': home_pitcher_name or 'Unknown',
                'away_pitcher_name': away_pitcher_name or 'Unknown',
                'home_probable_starter': home_pitcher_name or 'Unknown',
                'away_probable_starter': away_pitcher_name or 'Unknown',

                # 投手統計數據
                'home_pitcher_era': home_pitcher_stats.get('era') if home_pitcher_stats else None,
                'away_pitcher_era': away_pitcher_stats.get('era') if away_pitcher_stats else None,
                'home_pitcher_whip': home_pitcher_stats.get('whip') if home_pitcher_stats else None,
                'away_pitcher_whip': away_pitcher_stats.get('whip') if away_pitcher_stats else None,

                # 投手壓制力評分 (基於三振率和ERA)
                'home_pitcher_dominance': self._calculate_pitcher_dominance(home_pitcher_stats),
                'away_pitcher_dominance': self._calculate_pitcher_dominance(away_pitcher_stats),
            })

            logger.info(f"王牌投手分析 - {away_team}@{home_team}: "
                       f"主隊質量{home_pitcher_quality:.1f}, 客隊質量{away_pitcher_quality:.1f}, "
                       f"對戰優勢{features['pitcher_matchup_advantage']:.2f}")

            return features

        except Exception as e:
            logger.warning(f"王牌投手因素提取失敗 {away_team}@{home_team}: {e}")
            return {
                'home_pitcher_quality': 50.0,
                'away_pitcher_quality': 50.0,
                'home_has_ace': 0,
                'away_has_ace': 0,
                'pitcher_matchup_advantage': 0.0,
                'pitcher_era_differential': 0.0,
                'pitcher_whip_differential': 0.0,
                'ace_pitcher_duel': 0,
                'home_pitcher_dominance': 50.0,
                'away_pitcher_dominance': 50.0,
            }

    def _get_team_ace_pitcher_stats(self, team_name: str) -> Optional[Dict]:
        """獲取球隊王牌投手統計"""
        try:
            # 獲取team_id
            team = Team.query.filter(
                (Team.team_name == team_name) |
                (Team.team_name_short == team_name) |
                (Team.team_code == team_name)
            ).first()

            if not team:
                return None

            # 查詢該球隊ERA最低的投手（最少5局投球）
            ace_pitcher = PlayerStats.query.filter(
                PlayerStats.team_id == team.team_id,
                PlayerStats.era.isnot(None),
                PlayerStats.era > 0,
                PlayerStats.innings_pitched >= 5  # 至少投5局
            ).order_by(PlayerStats.era.asc()).first()

            if ace_pitcher:
                return {
                    'era': ace_pitcher.era,
                    'whip': ace_pitcher.whip or 1.30,
                    'strikeouts': ace_pitcher.strikeouts_pitching or 0,
                    'innings_pitched': ace_pitcher.innings_pitched or 0,
                    'wins': ace_pitcher.wins or 0,
                    'losses': ace_pitcher.losses or 0
                }

            return None

        except Exception as e:
            logger.warning(f"獲取王牌投手統計失敗 ({team_name}): {e}")
            return None

    def _calculate_pitcher_quality(self, pitcher_stats: Optional[Dict]) -> float:
        """計算投手質量評分 (0-100)"""
        if not pitcher_stats:
            return 50.0

        try:
            era = pitcher_stats.get('era', 4.50)
            whip = pitcher_stats.get('whip', 1.30)

            # ERA評分 (ERA越低分數越高)
            era_score = max(0, 100 - (era - 2.0) * 20)  # ERA 2.0 = 100分

            # WHIP評分 (WHIP越低分數越高)
            whip_score = max(0, 100 - (whip - 1.0) * 50)  # WHIP 1.0 = 100分

            # 綜合評分
            quality_score = (era_score * 0.6 + whip_score * 0.4)
            return max(0, min(100, quality_score))

        except Exception as e:
            logger.warning(f"計算投手質量評分失敗: {e}")
            return 50.0

    def _calculate_pitcher_matchup_advantage(self, home_stats: Optional[Dict], away_stats: Optional[Dict]) -> float:
        """計算投手對戰優勢 (-1到1)"""
        if not home_stats or not away_stats:
            return 0.0

        try:
            home_era = home_stats.get('era', 4.50)
            away_era = away_stats.get('era', 4.50)

            # ERA差距轉換為優勢值
            era_diff = away_era - home_era  # 正值表示主隊投手優勢
            advantage = max(-1.0, min(1.0, era_diff / 2.0))  # 限制在-1到1之間

            return advantage

        except Exception as e:
            logger.warning(f"計算投手對戰優勢失敗: {e}")
            return 0.0

    def _get_era_differential(self, home_stats: Optional[Dict], away_stats: Optional[Dict]) -> float:
        """獲取ERA差距"""
        home_era = home_stats.get('era', 4.50) if home_stats else 4.50
        away_era = away_stats.get('era', 4.50) if away_stats else 4.50
        return away_era - home_era

    def _get_whip_differential(self, home_stats: Optional[Dict], away_stats: Optional[Dict]) -> float:
        """獲取WHIP差距"""
        home_whip = home_stats.get('whip', 1.30) if home_stats else 1.30
        away_whip = away_stats.get('whip', 1.30) if away_stats else 1.30
        return away_whip - home_whip

    def _calculate_pitcher_dominance(self, pitcher_stats: Optional[Dict]) -> float:
        """計算投手壓制力評分 (0-100)"""
        if not pitcher_stats:
            return 50.0

        try:
            era = pitcher_stats.get('era', 4.50)
            strikeouts = pitcher_stats.get('strikeouts', 0)
            innings = pitcher_stats.get('innings_pitched', 1)

            # 三振率 (K/9)
            k9_rate = (strikeouts / innings) * 9 if innings > 0 else 8.0

            # ERA評分
            era_score = max(0, 100 - (era - 2.0) * 15)

            # 三振率評分
            k9_score = min(100, k9_rate * 10)  # 10 K/9 = 100分

            # 綜合壓制力評分
            dominance = (era_score * 0.7 + k9_score * 0.3)
            return max(0, min(100, dominance))

        except Exception as e:
            logger.warning(f"計算投手壓制力失敗: {e}")
            return 50.0
