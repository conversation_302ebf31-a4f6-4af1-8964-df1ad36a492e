#!/usr/bin/env python3
"""
智能自適應預測系統
根據投手質量自動選擇預測策略，無需手動選擇模型
"""

import logging
from datetime import date
from typing import Dict, Tuple, Optional
from models.enhanced_feature_engineer import EnhancedFeatureEngineer
from models.improved_predictor import ImprovedMLBPredictor

logger = logging.getLogger(__name__)

class IntelligentPredictor:
    """智能預測器 - 根據投手質量自動調整預測策略"""
    
    def __init__(self):
        self.feature_engineer = EnhancedFeatureEngineer()
        self.base_predictor = ImprovedMLBPredictor()
        
    def predict_game_intelligent(self, home_team: str, away_team: str, 
                               game_date: date, game_id: str = None) -> Dict:
        """
        智能預測比賽 - 根據投手質量自動調整策略
        
        Args:
            home_team: 主隊代碼
            away_team: 客隊代碼  
            game_date: 比賽日期
            game_id: 比賽ID
            
        Returns:
            包含預測結果和策略說明的字典
        """
        try:
            logger.info(f"🧠 開始智能預測: {away_team} @ {home_team} ({game_date})")
            
            # 1. 分析投手質量
            pitcher_analysis = self._analyze_pitcher_quality(
                home_team, away_team, game_date, game_id
            )
            
            # 2. 根據投手質量選擇預測策略
            strategy = self._determine_prediction_strategy(pitcher_analysis)
            
            # 3. 執行預測
            prediction = self._execute_prediction(
                home_team, away_team, game_date, game_id, strategy
            )
            
            # 4. 組合結果
            result = {
                **prediction,
                'pitcher_analysis': pitcher_analysis,
                'strategy': strategy,
                'explanation': self._generate_explanation(pitcher_analysis, strategy, prediction)
            }
            
            logger.info(f"✅ 智能預測完成: {strategy['name']} - {result['predicted_away_score']:.1f}-{result['predicted_home_score']:.1f}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 智能預測失敗: {e}")
            raise
    
    def _analyze_pitcher_quality(self, home_team: str, away_team: str, 
                               game_date: date, game_id: str = None) -> Dict:
        """分析投手質量"""
        try:
            # 獲取投手特徵
            features = self.feature_engineer.extract_comprehensive_features(
                home_team, away_team, game_date, game_id
            )
            
            # 提取投手信息
            home_pitcher = features.get('home_probable_starter', '未知')
            away_pitcher = features.get('away_probable_starter', '未知')
            home_era = features.get('home_pitcher_era', 4.50)
            away_era = features.get('away_pitcher_era', 4.50)
            home_quality = features.get('home_pitcher_quality', 50.0)
            away_quality = features.get('away_pitcher_quality', 50.0)
            
            # 投手質量分類
            def classify_pitcher_strength(era: float, quality: float) -> str:
                if era <= 2.50 and quality >= 80:
                    return "王牌"
                elif era <= 3.50 and quality >= 70:
                    return "優秀"
                elif era <= 4.50 and quality >= 50:
                    return "普通"
                else:
                    return "弱勢"
            
            home_strength = classify_pitcher_strength(home_era, home_quality)
            away_strength = classify_pitcher_strength(away_era, away_quality)
            
            # 對戰類型分析
            matchup_type = self._determine_matchup_type(home_strength, away_strength)
            
            analysis = {
                'home_pitcher': {
                    'name': home_pitcher,
                    'era': home_era,
                    'quality': home_quality,
                    'strength': home_strength
                },
                'away_pitcher': {
                    'name': away_pitcher,
                    'era': away_era,
                    'quality': away_quality,
                    'strength': away_strength
                },
                'matchup_type': matchup_type,
                'average_era': (home_era + away_era) / 2,
                'average_quality': (home_quality + away_quality) / 2
            }
            
            logger.info(f"📊 投手分析: {home_pitcher}({home_strength}) vs {away_pitcher}({away_strength}) - {matchup_type}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 投手質量分析失敗: {e}")
            # 返回默認分析
            return {
                'home_pitcher': {'name': '未知', 'era': 4.50, 'quality': 50.0, 'strength': '普通'},
                'away_pitcher': {'name': '未知', 'era': 4.50, 'quality': 50.0, 'strength': '普通'},
                'matchup_type': '普通對戰',
                'average_era': 4.50,
                'average_quality': 50.0
            }
    
    def _determine_matchup_type(self, home_strength: str, away_strength: str) -> str:
        """確定對戰類型"""
        strength_order = {'王牌': 4, '優秀': 3, '普通': 2, '弱勢': 1}
        
        home_level = strength_order.get(home_strength, 2)
        away_level = strength_order.get(away_strength, 2)
        
        if home_level >= 3 and away_level >= 3:
            return "王牌對決"
        elif home_level >= 3 or away_level >= 3:
            return "強弱對戰"
        elif home_level <= 1 and away_level <= 1:
            return "打擊戰"
        else:
            return "普通對戰"
    
    def _determine_prediction_strategy(self, pitcher_analysis: Dict) -> Dict:
        """根據投手質量確定預測策略"""
        matchup_type = pitcher_analysis['matchup_type']
        avg_era = pitcher_analysis['average_era']
        avg_quality = pitcher_analysis['average_quality']
        
        if matchup_type == "王牌對決":
            # 兩個強投手 -> 低分比賽
            return {
                'name': '王牌對決策略',
                'type': 'low_scoring',
                'target_total': 7.0,  # 目標總分
                'score_adjustment': -1.5,  # 分數調整
                'confidence_boost': 0.1,  # 信心度提升
                'description': '兩位王牌投手對決，預期低分比賽'
            }
        elif matchup_type == "打擊戰":
            # 兩個弱投手 -> 高分比賽
            return {
                'name': '打擊戰策略',
                'type': 'high_scoring',
                'target_total': 12.0,
                'score_adjustment': 2.0,
                'confidence_boost': 0.1,
                'description': '兩位弱勢投手，預期高分打擊戰'
            }
        elif matchup_type == "強弱對戰":
            # 一強一弱 -> 中等分數，強投手一方得分較少
            return {
                'name': '強弱對戰策略',
                'type': 'unbalanced',
                'target_total': 9.0,
                'score_adjustment': 0.5,
                'confidence_boost': 0.05,
                'description': '強弱投手對戰，分數不平衡'
            }
        else:
            # 普通對戰 -> 標準預測
            return {
                'name': '標準策略',
                'type': 'standard',
                'target_total': 9.5,
                'score_adjustment': 0.0,
                'confidence_boost': 0.0,
                'description': '普通投手對戰，使用標準預測'
            }
    
    def _execute_prediction(self, home_team: str, away_team: str,
                          game_date: date, game_id: str, strategy: Dict) -> Dict:
        """執行預測並根據策略調整"""
        try:
            # 嘗試獲取基礎預測
            try:
                base_prediction = self.base_predictor.predict_game(
                    home_team, away_team, game_date
                )
            except Exception as pred_error:
                logger.warning(f"⚠️ 基礎預測失敗，使用默認預測: {pred_error}")
                # 使用默認預測
                base_prediction = {
                    'predicted_home_score': 5.0,
                    'predicted_away_score': 4.5,
                    'total_runs': 9.5,
                    'confidence': 0.6,
                    'home_win_probability': 0.52
                }

            # 根據策略調整預測
            adjusted_prediction = self._adjust_prediction_by_strategy(
                base_prediction, strategy
            )

            return adjusted_prediction

        except Exception as e:
            logger.error(f"❌ 執行預測失敗: {e}")
            # 返回保守預測
            return {
                'predicted_home_score': 5.0,
                'predicted_away_score': 4.5,
                'total_runs': 9.5,
                'confidence': 0.6,
                'home_win_probability': 0.52
            }
    
    def _adjust_prediction_by_strategy(self, base_prediction: Dict, strategy: Dict) -> Dict:
        """根據策略調整預測結果"""
        try:
            home_score = base_prediction['predicted_home_score']
            away_score = base_prediction['predicted_away_score']
            current_total = home_score + away_score
            target_total = strategy['target_total']
            adjustment = strategy['score_adjustment']
            
            if strategy['type'] == 'low_scoring':
                # 王牌對決 - 降低總分
                if current_total > target_total:
                    scale_factor = target_total / current_total
                    home_score *= scale_factor
                    away_score *= scale_factor
                home_score = max(2.0, home_score + adjustment)
                away_score = max(2.0, away_score + adjustment)
                
            elif strategy['type'] == 'high_scoring':
                # 打擊戰 - 提高總分
                if current_total < target_total:
                    scale_factor = target_total / current_total
                    home_score *= scale_factor
                    away_score *= scale_factor
                home_score = min(15.0, home_score + adjustment)
                away_score = min(15.0, away_score + adjustment)
                
            elif strategy['type'] == 'unbalanced':
                # 強弱對戰 - 調整平衡
                home_score += adjustment * 0.5
                away_score += adjustment * 0.5
                
            # 確保合理範圍
            home_score = max(1.0, min(20.0, round(home_score, 1)))
            away_score = max(1.0, min(20.0, round(away_score, 1)))
            
            # 調整信心度
            confidence = base_prediction.get('confidence', 0.6)
            confidence = min(0.95, confidence + strategy['confidence_boost'])
            
            return {
                'predicted_home_score': home_score,
                'predicted_away_score': away_score,
                'total_runs': home_score + away_score,
                'confidence': confidence,
                'home_win_probability': home_score / (home_score + away_score),
                'strategy_applied': strategy['name']
            }
            
        except Exception as e:
            logger.error(f"❌ 策略調整失敗: {e}")
            return base_prediction
    
    def _generate_explanation(self, pitcher_analysis: Dict, strategy: Dict, prediction: Dict) -> str:
        """生成預測說明"""
        home_pitcher = pitcher_analysis['home_pitcher']
        away_pitcher = pitcher_analysis['away_pitcher']
        
        explanation = f"""
🎯 智能預測分析:

📊 投手分析:
• 主隊: {home_pitcher['name']} (ERA: {home_pitcher['era']:.2f}, 等級: {home_pitcher['strength']})
• 客隊: {away_pitcher['name']} (ERA: {away_pitcher['era']:.2f}, 等級: {away_pitcher['strength']})
• 對戰類型: {pitcher_analysis['matchup_type']}

🧠 預測策略: {strategy['name']}
• {strategy['description']}
• 目標總分: {strategy['target_total']}分

📈 預測結果:
• 比分: {prediction['predicted_away_score']:.1f} - {prediction['predicted_home_score']:.1f}
• 總分: {prediction['total_runs']:.1f}分
• 信心度: {prediction['confidence']:.1%}
        """.strip()
        
        return explanation
