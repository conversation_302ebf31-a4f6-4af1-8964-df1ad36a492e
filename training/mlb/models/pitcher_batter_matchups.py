#!/usr/bin/env python3
"""
投手對打者歷史對戰數據庫
"""

import logging
import requests
import sys
import os
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import db

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PitcherBatterMatchupAnalyzer:
    """投手對打者對戰分析器"""
    
    def __init__(self, app=None):
        self.app = app
        self.base_url = "https://statsapi.mlb.com/api/v1"
        
    def get_pitcher_vs_batter_stats(self, pitcher_id: int, batter_id: int, seasons: List[int] = None) -> Dict:
        """獲取投手對特定打者的歷史統計（基於Play-by-Play數據）"""
        if seasons is None:
            seasons = [2024, 2023, 2022]  # 默認查詢最近3年

        matchup_stats = {
            'pitcher_id': pitcher_id,
            'batter_id': batter_id,
            'total_at_bats': 0,
            'hits': 0,
            'home_runs': 0,
            'strikeouts': 0,
            'walks': 0,
            'doubles': 0,
            'triples': 0,
            'batting_average': 0.0,
            'on_base_percentage': 0.0,
            'slugging_percentage': 0.0,
            'ops': 0.0,
            'plate_appearances': 0,
            'recent_matchups': []
        }

        try:
            # 從數據庫中查詢歷史對戰數據
            with self.app.app_context():
                from models.database import Game

                # 查詢包含這兩個球員的比賽
                games_query = db.session.query(Game).filter(
                    Game.season.in_(seasons),
                    Game.game_status == 'completed'
                ).all()

                for game in games_query:
                    # 獲取這場比賽的play-by-play數據
                    matchups = self.get_game_matchups(game.game_id, pitcher_id, batter_id)

                    for matchup in matchups:
                        matchup_stats['plate_appearances'] += 1

                        # 分析結果
                        result = matchup['result'].lower()

                        # 計算打數（排除保送、觸身球等）
                        if not any(x in result for x in ['walk', 'hit by pitch', 'interference', 'obstruction']):
                            matchup_stats['total_at_bats'] += 1

                            # 計算安打
                            if any(x in result for x in ['single', 'double', 'triple', 'home run', 'ground rule double']):
                                matchup_stats['hits'] += 1

                                if 'double' in result:
                                    matchup_stats['doubles'] += 1
                                elif 'triple' in result:
                                    matchup_stats['triples'] += 1
                                elif 'home run' in result:
                                    matchup_stats['home_runs'] += 1

                        # 計算三振
                        if 'strikeout' in result or 'strikes out' in result:
                            matchup_stats['strikeouts'] += 1

                        # 計算保送
                        if 'walk' in result:
                            matchup_stats['walks'] += 1

                        # 保存最近的對戰記錄
                        if len(matchup_stats['recent_matchups']) < 10:
                            matchup_stats['recent_matchups'].append({
                                'date': game.date.strftime('%Y-%m-%d'),
                                'result': matchup['result'],
                                'inning': matchup.get('inning', 'Unknown')
                            })

            # 計算統計指標
            if matchup_stats['total_at_bats'] > 0:
                matchup_stats['batting_average'] = matchup_stats['hits'] / matchup_stats['total_at_bats']

                # 計算長打率
                total_bases = (matchup_stats['hits'] - matchup_stats['doubles'] - matchup_stats['triples'] - matchup_stats['home_runs'] +
                              matchup_stats['doubles'] * 2 + matchup_stats['triples'] * 3 + matchup_stats['home_runs'] * 4)
                matchup_stats['slugging_percentage'] = total_bases / matchup_stats['total_at_bats']

            if matchup_stats['plate_appearances'] > 0:
                matchup_stats['on_base_percentage'] = (matchup_stats['hits'] + matchup_stats['walks']) / matchup_stats['plate_appearances']
                matchup_stats['ops'] = matchup_stats['on_base_percentage'] + matchup_stats['slugging_percentage']

        except Exception as e:
            logger.error(f"獲取對戰統計失敗 (P:{pitcher_id} vs B:{batter_id}): {e}")

        return matchup_stats

    def get_game_matchups(self, game_id: int, pitcher_id: int, batter_id: int) -> List[Dict]:
        """獲取特定比賽中投手對打者的所有對戰"""
        matchups = []

        try:
            # 獲取比賽的play-by-play數據
            url = f"{self.base_url}/game/{game_id}/playByPlay"
            response = requests.get(url, timeout=30)

            if response.status_code == 200:
                data = response.json()

                if 'allPlays' in data:
                    for play in data['allPlays']:
                        if 'matchup' in play:
                            matchup = play['matchup']

                            # 檢查是否是我們要找的投手對打者組合
                            play_pitcher_id = matchup.get('pitcher', {}).get('id')
                            play_batter_id = matchup.get('batter', {}).get('id')

                            if play_pitcher_id == pitcher_id and play_batter_id == batter_id:
                                result_info = {
                                    'inning': play.get('about', {}).get('inning', 0),
                                    'half_inning': play.get('about', {}).get('halfInning', ''),
                                    'result': play.get('result', {}).get('event', ''),
                                    'description': play.get('result', {}).get('description', ''),
                                    'pitcher_name': matchup.get('pitcher', {}).get('fullName', ''),
                                    'batter_name': matchup.get('batter', {}).get('fullName', '')
                                }
                                matchups.append(result_info)

        except Exception as e:
            logger.error(f"獲取比賽對戰數據失敗 (Game:{game_id}, P:{pitcher_id} vs B:{batter_id}): {e}")

        return matchups

    def build_matchup_database_from_recent_games(self, days_back: int = 30) -> Dict:
        """從最近的比賽中建立對戰數據庫"""
        logger.info(f"開始建立最近{days_back}天的對戰數據庫...")

        matchup_database = {
            'total_games_processed': 0,
            'total_matchups_found': 0,
            'unique_pitcher_batter_pairs': 0,
            'matchups': {}  # key: f"{pitcher_id}_{batter_id}", value: matchup_stats
        }

        try:
            with self.app.app_context():
                from models.database import Game
                from datetime import date, timedelta

                # 獲取最近的比賽
                start_date = date.today() - timedelta(days=days_back)
                recent_games = db.session.query(Game).filter(
                    Game.date >= start_date,
                    Game.game_status == 'completed'
                ).all()

                logger.info(f"找到 {len(recent_games)} 場最近的比賽")

                for game in recent_games:
                    try:
                        # 獲取這場比賽的所有對戰
                        game_matchups = self.get_all_game_matchups(game.game_id)
                        matchup_database['total_games_processed'] += 1

                        for matchup in game_matchups:
                            pitcher_id = matchup['pitcher_id']
                            batter_id = matchup['batter_id']
                            pair_key = f"{pitcher_id}_{batter_id}"

                            if pair_key not in matchup_database['matchups']:
                                matchup_database['matchups'][pair_key] = {
                                    'pitcher_id': pitcher_id,
                                    'batter_id': batter_id,
                                    'pitcher_name': matchup['pitcher_name'],
                                    'batter_name': matchup['batter_name'],
                                    'total_at_bats': 0,
                                    'hits': 0,
                                    'home_runs': 0,
                                    'strikeouts': 0,
                                    'walks': 0,
                                    'plate_appearances': 0,
                                    'recent_results': []
                                }
                                matchup_database['unique_pitcher_batter_pairs'] += 1

                            # 更新統計
                            pair_stats = matchup_database['matchups'][pair_key]
                            pair_stats['plate_appearances'] += 1
                            matchup_database['total_matchups_found'] += 1

                            result = matchup['result'].lower()

                            # 分析結果並更新統計
                            if not any(x in result for x in ['walk', 'hit by pitch']):
                                pair_stats['total_at_bats'] += 1

                                if any(x in result for x in ['single', 'double', 'triple', 'home run']):
                                    pair_stats['hits'] += 1

                                    if 'home run' in result:
                                        pair_stats['home_runs'] += 1

                            if 'strikeout' in result or 'strikes out' in result:
                                pair_stats['strikeouts'] += 1

                            if 'walk' in result:
                                pair_stats['walks'] += 1

                            # 保存最近結果
                            if len(pair_stats['recent_results']) < 5:
                                pair_stats['recent_results'].append({
                                    'date': game.date.strftime('%Y-%m-%d'),
                                    'result': matchup['result']
                                })

                        if matchup_database['total_games_processed'] % 10 == 0:
                            logger.info(f"已處理 {matchup_database['total_games_processed']} 場比賽...")

                    except Exception as e:
                        logger.error(f"處理比賽失敗 (Game:{game.game_id}): {e}")
                        continue

                # 計算最終統計
                for pair_key, stats in matchup_database['matchups'].items():
                    if stats['total_at_bats'] > 0:
                        stats['batting_average'] = stats['hits'] / stats['total_at_bats']
                    if stats['plate_appearances'] > 0:
                        stats['on_base_percentage'] = (stats['hits'] + stats['walks']) / stats['plate_appearances']

        except Exception as e:
            logger.error(f"建立對戰數據庫失敗: {e}")

        return matchup_database

    def get_all_game_matchups(self, game_id: int) -> List[Dict]:
        """獲取比賽中的所有投手對打者對戰"""
        all_matchups = []

        try:
            url = f"{self.base_url}/game/{game_id}/playByPlay"
            response = requests.get(url, timeout=30)

            if response.status_code == 200:
                data = response.json()

                if 'allPlays' in data:
                    for play in data['allPlays']:
                        if 'matchup' in play and 'result' in play:
                            matchup = play['matchup']

                            matchup_info = {
                                'pitcher_id': matchup.get('pitcher', {}).get('id'),
                                'batter_id': matchup.get('batter', {}).get('id'),
                                'pitcher_name': matchup.get('pitcher', {}).get('fullName', ''),
                                'batter_name': matchup.get('batter', {}).get('fullName', ''),
                                'result': play.get('result', {}).get('event', ''),
                                'description': play.get('result', {}).get('description', ''),
                                'inning': play.get('about', {}).get('inning', 0)
                            }

                            if matchup_info['pitcher_id'] and matchup_info['batter_id']:
                                all_matchups.append(matchup_info)

        except Exception as e:
            logger.error(f"獲取比賽所有對戰失敗 (Game:{game_id}): {e}")

        return all_matchups

    def get_season_matchup_stats(self, pitcher_id: int, batter_id: int, season: int) -> Dict:
        """獲取特定賽季的對戰統計"""
        stats = {
            'season': season,
            'at_bats': 0,
            'hits': 0,
            'home_runs': 0,
            'strikeouts': 0,
            'walks': 0,
            'games': []
        }
        
        try:
            # 使用MLB API獲取對戰數據
            url = f"{self.base_url}/people/{batter_id}/stats/vsPlayer/{pitcher_id}"
            params = {
                'season': season,
                'group': 'hitting'
            }
            
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'stats' in data and data['stats']:
                    for stat_group in data['stats']:
                        if 'splits' in stat_group:
                            for split in stat_group['splits']:
                                if 'stat' in split:
                                    stat = split['stat']
                                    stats['at_bats'] = stat.get('atBats', 0)
                                    stats['hits'] = stat.get('hits', 0)
                                    stats['home_runs'] = stat.get('homeRuns', 0)
                                    stats['strikeouts'] = stat.get('strikeOuts', 0)
                                    stats['walks'] = stat.get('baseOnBalls', 0)
                                    break
            
        except Exception as e:
            logger.error(f"獲取賽季對戰統計失敗 (P:{pitcher_id} vs B:{batter_id}, {season}): {e}")
        
        return stats
    
    def analyze_lineup_vs_pitcher(self, pitcher_id: int, lineup_player_ids: List[int]) -> Dict:
        """分析打線對投手的整體表現"""
        analysis = {
            'pitcher_id': pitcher_id,
            'lineup_size': len(lineup_player_ids),
            'players_with_history': 0,
            'total_at_bats': 0,
            'total_hits': 0,
            'total_home_runs': 0,
            'total_strikeouts': 0,
            'lineup_batting_average': 0.0,
            'lineup_power_rating': 0.0,
            'pitcher_dominance_rating': 0.0,
            'individual_matchups': []
        }
        
        try:
            for batter_id in lineup_player_ids:
                matchup = self.get_pitcher_vs_batter_stats(pitcher_id, batter_id)
                
                if matchup['total_at_bats'] > 0:
                    analysis['players_with_history'] += 1
                    analysis['total_at_bats'] += matchup['total_at_bats']
                    analysis['total_hits'] += matchup['hits']
                    analysis['total_home_runs'] += matchup['home_runs']
                    analysis['total_strikeouts'] += matchup['strikeouts']
                
                analysis['individual_matchups'].append(matchup)
            
            # 計算整體統計
            if analysis['total_at_bats'] > 0:
                analysis['lineup_batting_average'] = analysis['total_hits'] / analysis['total_at_bats']
                analysis['lineup_power_rating'] = analysis['total_home_runs'] / analysis['total_at_bats']
                analysis['pitcher_dominance_rating'] = analysis['total_strikeouts'] / analysis['total_at_bats']
            
            # 計算歷史數據覆蓋率
            analysis['history_coverage'] = analysis['players_with_history'] / analysis['lineup_size'] * 100 if analysis['lineup_size'] > 0 else 0
            
        except Exception as e:
            logger.error(f"分析打線對投手表現失敗 (P:{pitcher_id}): {e}")
        
        return analysis
    
    def get_pitcher_vs_team_history(self, pitcher_id: int, team_id: int, seasons: List[int] = None) -> Dict:
        """獲取投手對特定球隊的歷史表現"""
        if seasons is None:
            seasons = [2024, 2023, 2022]
        
        team_stats = {
            'pitcher_id': pitcher_id,
            'team_id': team_id,
            'total_games': 0,
            'total_innings': 0.0,
            'total_hits_allowed': 0,
            'total_runs_allowed': 0,
            'total_strikeouts': 0,
            'total_walks': 0,
            'era_vs_team': 0.0,
            'whip_vs_team': 0.0,
            'strikeout_rate': 0.0,
            'seasons_data': []
        }
        
        try:
            for season in seasons:
                season_stats = self.get_pitcher_vs_team_season_stats(pitcher_id, team_id, season)
                if season_stats['games'] > 0:
                    team_stats['seasons_data'].append(season_stats)
                    
                    # 累計統計
                    team_stats['total_games'] += season_stats['games']
                    team_stats['total_innings'] += season_stats['innings_pitched']
                    team_stats['total_hits_allowed'] += season_stats['hits_allowed']
                    team_stats['total_runs_allowed'] += season_stats['earned_runs']
                    team_stats['total_strikeouts'] += season_stats['strikeouts']
                    team_stats['total_walks'] += season_stats['walks']
            
            # 計算總體統計
            if team_stats['total_innings'] > 0:
                team_stats['era_vs_team'] = (team_stats['total_runs_allowed'] * 9) / team_stats['total_innings']
                team_stats['whip_vs_team'] = (team_stats['total_hits_allowed'] + team_stats['total_walks']) / team_stats['total_innings']
                
                total_batters_faced = team_stats['total_hits_allowed'] + team_stats['total_walks'] + team_stats['total_strikeouts']
                if total_batters_faced > 0:
                    team_stats['strikeout_rate'] = team_stats['total_strikeouts'] / total_batters_faced
                
        except Exception as e:
            logger.error(f"獲取投手對球隊統計失敗 (P:{pitcher_id} vs T:{team_id}): {e}")
        
        return team_stats
    
    def get_pitcher_vs_team_season_stats(self, pitcher_id: int, team_id: int, season: int) -> Dict:
        """獲取投手對特定球隊的單賽季統計"""
        stats = {
            'season': season,
            'games': 0,
            'innings_pitched': 0.0,
            'hits_allowed': 0,
            'earned_runs': 0,
            'strikeouts': 0,
            'walks': 0
        }
        
        try:
            # 使用MLB API獲取投手對球隊的統計
            url = f"{self.base_url}/people/{pitcher_id}/stats/vsTeam/{team_id}"
            params = {
                'season': season,
                'group': 'pitching'
            }
            
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'stats' in data and data['stats']:
                    for stat_group in data['stats']:
                        if 'splits' in stat_group:
                            for split in stat_group['splits']:
                                if 'stat' in split:
                                    stat = split['stat']
                                    stats['games'] = stat.get('gamesPlayed', 0)
                                    stats['innings_pitched'] = float(stat.get('inningsPitched', 0))
                                    stats['hits_allowed'] = stat.get('hits', 0)
                                    stats['earned_runs'] = stat.get('earnedRuns', 0)
                                    stats['strikeouts'] = stat.get('strikeOuts', 0)
                                    stats['walks'] = stat.get('baseOnBalls', 0)
                                    break
            
        except Exception as e:
            logger.error(f"獲取投手對球隊賽季統計失敗 (P:{pitcher_id} vs T:{team_id}, {season}): {e}")
        
        return stats
    
    def test_matchup_api_availability(self) -> Dict:
        """測試對戰API的可用性"""
        logger.info("測試投手對打者API可用性...")
        
        # 使用一些知名球員進行測試
        test_cases = [
            {'pitcher_id': 592789, 'pitcher_name': 'Jacob deGrom', 'batter_id': 592450, 'batter_name': 'Mookie Betts'},
            {'pitcher_id': 545361, 'pitcher_name': 'Mike Trout', 'batter_id': 608070, 'batter_name': 'J.T. Realmuto'},
            {'pitcher_id': 660271, 'pitcher_name': 'Shane Bieber', 'batter_id': 596059, 'batter_name': 'Nolan Arenado'}
        ]
        
        results = {
            'api_accessible': True,
            'successful_tests': 0,
            'total_tests': len(test_cases),
            'test_results': []
        }
        
        for test_case in test_cases:
            try:
                matchup = self.get_pitcher_vs_batter_stats(
                    test_case['pitcher_id'], 
                    test_case['batter_id']
                )
                
                test_result = {
                    'pitcher_name': test_case['pitcher_name'],
                    'batter_name': test_case['batter_name'],
                    'at_bats': matchup['total_at_bats'],
                    'batting_average': matchup['batting_average'],
                    'success': matchup['total_at_bats'] > 0
                }
                
                if test_result['success']:
                    results['successful_tests'] += 1
                
                results['test_results'].append(test_result)
                
            except Exception as e:
                logger.error(f"測試失敗 ({test_case['pitcher_name']} vs {test_case['batter_name']}): {e}")
                results['test_results'].append({
                    'pitcher_name': test_case['pitcher_name'],
                    'batter_name': test_case['batter_name'],
                    'error': str(e),
                    'success': False
                })
        
        results['success_rate'] = results['successful_tests'] / results['total_tests'] * 100
        
        return results

def main():
    """主函數 - 測試投手對打者對戰分析"""
    analyzer = PitcherBatterMatchupAnalyzer()
    
    print("=" * 80)
    print("⚾ 投手對打者歷史對戰分析測試")
    print("=" * 80)
    
    # 測試API可用性
    api_test = analyzer.test_matchup_api_availability()
    
    print(f"\n📊 API測試結果:")
    print(f"API可訪問: {'✅' if api_test['api_accessible'] else '❌'}")
    print(f"成功測試: {api_test['successful_tests']}/{api_test['total_tests']}")
    print(f"成功率: {api_test['success_rate']:.1f}%")
    
    print(f"\n🔍 詳細測試結果:")
    for result in api_test['test_results']:
        if result.get('success', False):
            print(f"✅ {result['pitcher_name']} vs {result['batter_name']}")
            print(f"   打數: {result['at_bats']}, 打擊率: {result['batting_average']:.3f}")
        else:
            print(f"❌ {result['pitcher_name']} vs {result['batter_name']}")
            if 'error' in result:
                print(f"   錯誤: {result['error']}")

if __name__ == "__main__":
    main()
