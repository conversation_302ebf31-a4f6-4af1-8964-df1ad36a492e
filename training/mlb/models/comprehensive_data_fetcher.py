#!/usr/bin/env python3
"""
全面數據抓取器 - 獲取所有可能影響賽果的球隊和球員資料
"""

import requests
import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
import time

from .database import db, Team, Player
from .enhanced_data_models import (
    TeamAdvancedStats, PlayerInjuryReport, PlayerPerformanceTrends,
    WeatherConditions, TeamChemistry
)

logger = logging.getLogger(__name__)

class ComprehensiveDataFetcher:
    """全面數據抓取器"""
    
    def __init__(self):
        self.mlb_api_base = "https://statsapi.mlb.com/api/v1"
        self.weather_api_key = None  # 需要設置天氣API密鑰
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MLB-Prediction-System/1.0'
        })
    
    def fetch_all_team_data(self, team_id: int, season: int = None) -> bool:
        """獲取球隊的所有相關數據"""
        if season is None:
            season = date.today().year
            
        logger.info(f"開始獲取球隊 {team_id} 的 {season} 賽季全面數據...")
        
        success_count = 0
        total_tasks = 5
        
        try:
            # 1. 獲取進階統計數據
            if self.fetch_team_advanced_stats(team_id, season):
                success_count += 1
                logger.info("✅ 球隊進階統計數據獲取成功")
            
            # 2. 獲取傷兵報告
            if self.fetch_team_injury_report(team_id):
                success_count += 1
                logger.info("✅ 球隊傷兵報告獲取成功")
            
            # 3. 獲取球員表現趨勢
            if self.fetch_team_player_trends(team_id, season):
                success_count += 1
                logger.info("✅ 球員表現趨勢獲取成功")
            
            # 4. 獲取球隊化學反應數據
            if self.fetch_team_chemistry(team_id, season):
                success_count += 1
                logger.info("✅ 球隊化學反應數據獲取成功")
            
            # 5. 獲取主場天氣歷史
            if self.fetch_venue_weather_history(team_id):
                success_count += 1
                logger.info("✅ 主場天氣歷史獲取成功")
            
            logger.info(f"球隊 {team_id} 數據獲取完成: {success_count}/{total_tasks} 成功")
            return success_count >= 3  # 至少成功3項
            
        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 數據失敗: {e}")
            return False
    
    def fetch_team_advanced_stats(self, team_id: int, season: int) -> bool:
        """獲取球隊進階統計數據"""
        try:
            # 獲取球隊基本統計
            url = f"{self.mlb_api_base}/teams/{team_id}/stats"
            params = {
                'season': season,
                'stats': 'season',
                'group': 'hitting,pitching,fielding'
            }
            
            response = self.session.get(url, params=params, timeout=30)
            if response.status_code != 200:
                logger.error(f"獲取球隊統計失敗: {response.status_code}")
                return False
            
            data = response.json()
            stats_groups = data.get('stats', [])
            
            # 查找或創建進階統計記錄
            advanced_stats = TeamAdvancedStats.query.filter_by(
                team_id=team_id, season=season
            ).first()
            
            if not advanced_stats:
                advanced_stats = TeamAdvancedStats(team_id=team_id, season=season)
                db.session.add(advanced_stats)
            
            # 處理統計數據
            for stats_group in stats_groups:
                group_type = stats_group.get('group', {}).get('displayName', '')
                splits = stats_group.get('splits', [])
                
                for split in splits:
                    stat_data = split.get('stat', {})
                    
                    if group_type == 'hitting':
                        self._process_hitting_advanced_stats(advanced_stats, stat_data)
                    elif group_type == 'pitching':
                        self._process_pitching_advanced_stats(advanced_stats, stat_data)
                    elif group_type == 'fielding':
                        self._process_fielding_advanced_stats(advanced_stats, stat_data)
            
            # 獲取額外的進階指標
            self._fetch_additional_team_metrics(advanced_stats, team_id, season)
            
            advanced_stats.date_updated = date.today()
            db.session.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 進階統計失敗: {e}")
            db.session.rollback()
            return False
    
    def _process_hitting_advanced_stats(self, stats: TeamAdvancedStats, data: Dict):
        """處理打擊進階統計"""
        try:
            # 核心打線數據 (這裡簡化處理，實際需要更複雜的計算)
            stats.core_lineup_avg = float(data.get('avg', 0))
            stats.core_lineup_ops = float(data.get('ops', 0))
            stats.core_lineup_hr_rate = float(data.get('homeRuns', 0)) / max(float(data.get('atBats', 1)), 1)
            
            # 關鍵時刻表現
            stats.risp_avg = float(data.get('avg', 0))  # 簡化處理
            stats.clutch_avg = float(data.get('avg', 0))  # 需要更詳細的API
            
        except Exception as e:
            logger.error(f"處理打擊統計失敗: {e}")
    
    def _process_pitching_advanced_stats(self, stats: TeamAdvancedStats, data: Dict):
        """處理投手進階統計"""
        try:
            # 先發投手數據
            stats.starter_era = float(data.get('era', 0))
            stats.starter_whip = float(data.get('whip', 0))
            stats.starter_k_rate = float(data.get('strikeoutsPer9Inn', 0))
            
            # 牛棚數據 (簡化處理)
            stats.bullpen_era = float(data.get('era', 0))
            stats.bullpen_whip = float(data.get('whip', 0))
            stats.bullpen_k_rate = float(data.get('strikeoutsPer9Inn', 0))
            
        except Exception as e:
            logger.error(f"處理投手統計失敗: {e}")
    
    def _process_fielding_advanced_stats(self, stats: TeamAdvancedStats, data: Dict):
        """處理守備進階統計"""
        try:
            stats.fielding_percentage = float(data.get('fielding', 0))
            stats.errors_per_game = float(data.get('errors', 0)) / max(float(data.get('gamesPlayed', 1)), 1)
            stats.double_plays = int(data.get('doublePlays', 0))
            
        except Exception as e:
            logger.error(f"處理守備統計失敗: {e}")
    
    def _fetch_additional_team_metrics(self, stats: TeamAdvancedStats, team_id: int, season: int):
        """獲取額外的球隊指標"""
        try:
            # 獲取最近10場戰績
            recent_games_url = f"{self.mlb_api_base}/teams/{team_id}/stats"
            params = {
                'season': season,
                'stats': 'gameLog',
                'limit': 10
            }
            
            response = self.session.get(recent_games_url, params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                # 處理最近戰績數據
                self._calculate_recent_performance(stats, data)
            
            # 計算主場優勢
            self._calculate_home_field_advantage(stats, team_id, season)
            
        except Exception as e:
            logger.error(f"獲取額外指標失敗: {e}")
    
    def _calculate_recent_performance(self, stats: TeamAdvancedStats, data: Dict):
        """計算最近表現"""
        try:
            # 這裡需要根據實際API響應結構來處理
            # 簡化處理
            stats.momentum_score = 0.5  # 預設值，需要實際計算
            stats.last_10_games_record = "5-5"  # 預設值，需要實際計算
            
        except Exception as e:
            logger.error(f"計算最近表現失敗: {e}")
    
    def _calculate_home_field_advantage(self, stats: TeamAdvancedStats, team_id: int, season: int):
        """計算主場優勢"""
        try:
            # 獲取主客場分別統計
            # 這裡需要更複雜的計算
            stats.home_field_advantage = 0.1  # 預設值
            stats.road_performance = 0.0  # 預設值
            
        except Exception as e:
            logger.error(f"計算主場優勢失敗: {e}")
    
    def fetch_team_injury_report(self, team_id: int) -> bool:
        """獲取球隊傷兵報告"""
        try:
            # MLB API 可能沒有直接的傷兵API，需要從其他來源獲取
            # 這裡提供框架，實際需要找到合適的數據源
            
            logger.info(f"獲取球隊 {team_id} 傷兵報告...")
            
            # 模擬數據處理
            # 實際實現需要連接到傷兵報告數據源
            
            return True
            
        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 傷兵報告失敗: {e}")
            return False
    
    def fetch_team_player_trends(self, team_id: int, season: int) -> bool:
        """獲取球隊球員表現趨勢"""
        try:
            # 獲取球隊球員名單
            roster_url = f"{self.mlb_api_base}/teams/{team_id}/roster"
            params = {'season': season}
            
            response = self.session.get(roster_url, params=params, timeout=30)
            if response.status_code != 200:
                return False
            
            data = response.json()
            roster = data.get('roster', [])
            
            success_count = 0
            for player_data in roster:
                player_id = player_data.get('person', {}).get('id')
                if player_id:
                    if self.fetch_player_performance_trends(player_id, team_id, season):
                        success_count += 1
                    
                    # 避免API限制
                    time.sleep(0.1)
            
            logger.info(f"球隊 {team_id} 球員趨勢: {success_count}/{len(roster)} 成功")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 球員趨勢失敗: {e}")
            return False
    
    def fetch_player_performance_trends(self, player_id: int, team_id: int, season: int) -> bool:
        """獲取單個球員表現趨勢"""
        try:
            # 獲取球員最近統計
            url = f"{self.mlb_api_base}/people/{player_id}/stats"
            params = {
                'season': season,
                'stats': 'gameLog',
                'limit': 30  # 最近30場
            }
            
            response = self.session.get(url, params=params, timeout=30)
            if response.status_code != 200:
                return False
            
            data = response.json()
            
            # 查找或創建趨勢記錄
            trends = PlayerPerformanceTrends.query.filter_by(
                player_id=player_id, team_id=team_id, season=season
            ).first()
            
            if not trends:
                trends = PlayerPerformanceTrends(
                    player_id=player_id, team_id=team_id, season=season
                )
                db.session.add(trends)
            
            # 處理趨勢數據
            self._process_player_trends(trends, data)
            
            trends.date_calculated = date.today()
            db.session.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"獲取球員 {player_id} 趨勢失敗: {e}")
            db.session.rollback()
            return False
    
    def _process_player_trends(self, trends: PlayerPerformanceTrends, data: Dict):
        """處理球員趨勢數據"""
        try:
            stats_groups = data.get('stats', [])
            
            for stats_group in stats_groups:
                splits = stats_group.get('splits', [])
                
                # 計算最近表現
                recent_games = splits[:7]  # 最近7場
                if recent_games:
                    avg_sum = sum(float(game.get('stat', {}).get('avg', 0)) for game in recent_games)
                    trends.last_7_days_avg = avg_sum / len(recent_games)
                
                # 計算其他趨勢指標
                # 這裡需要更複雜的計算邏輯
                
        except Exception as e:
            logger.error(f"處理球員趨勢數據失敗: {e}")
    
    def fetch_team_chemistry(self, team_id: int, season: int) -> bool:
        """獲取球隊化學反應數據"""
        try:
            # 球隊化學反應數據通常需要從多個來源綜合分析
            # 這裡提供框架
            
            chemistry = TeamChemistry.query.filter_by(
                team_id=team_id, season=season
            ).first()
            
            if not chemistry:
                chemistry = TeamChemistry(team_id=team_id, season=season)
                db.session.add(chemistry)
            
            # 計算化學反應指標
            self._calculate_team_chemistry_metrics(chemistry, team_id, season)
            
            chemistry.date_assessed = date.today()
            db.session.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"獲取球隊 {team_id} 化學反應數據失敗: {e}")
            db.session.rollback()
            return False
    
    def _calculate_team_chemistry_metrics(self, chemistry: TeamChemistry, team_id: int, season: int):
        """計算球隊化學反應指標"""
        try:
            # 這裡需要複雜的計算邏輯
            # 基於比賽結果、球員表現等數據
            
            chemistry.clubhouse_harmony = 0.7  # 預設值
            chemistry.leadership_quality = 0.6  # 預設值
            chemistry.veteran_presence = 0.5  # 預設值
            
        except Exception as e:
            logger.error(f"計算化學反應指標失敗: {e}")
    
    def fetch_venue_weather_history(self, team_id: int) -> bool:
        """獲取主場天氣歷史"""
        try:
            # 天氣數據需要外部API
            # 這裡提供框架

            logger.info(f"獲取球隊 {team_id} 主場天氣歷史...")

            # 實際實現需要天氣API密鑰和球場位置信息

            return True

        except Exception as e:
            logger.error(f"獲取主場天氣歷史失敗: {e}")
            return False

    def fetch_game_weather_conditions(self, game_id: str, venue_id: int = None) -> bool:
        """獲取比賽天氣條件"""
        try:
            # 檢查是否已有天氣記錄
            existing_weather = WeatherConditions.query.filter_by(game_id=game_id).first()
            if existing_weather:
                return True

            # 獲取比賽信息
            game_url = f"{self.mlb_api_base}/game/{game_id}/linescore"
            response = self.session.get(game_url, timeout=30)

            if response.status_code != 200:
                return False

            game_data = response.json()

            # 創建天氣記錄
            weather = WeatherConditions(game_id=game_id, venue_id=venue_id)

            # 從比賽數據中提取天氣信息（如果有）
            game_info = game_data.get('gameInfo', {})
            weather_info = game_info.get('weather', {})

            if weather_info:
                weather.temperature = weather_info.get('temp')
                weather.weather_condition = weather_info.get('condition')
                weather.wind_speed = weather_info.get('wind')

            # 設置預設值
            weather.dome_stadium = self._is_dome_stadium(venue_id)
            weather.day_night = game_info.get('dayNight', 'day')

            db.session.add(weather)
            db.session.commit()

            return True

        except Exception as e:
            logger.error(f"獲取比賽 {game_id} 天氣條件失敗: {e}")
            db.session.rollback()
            return False

    def _is_dome_stadium(self, venue_id: int) -> bool:
        """判斷是否為巨蛋球場"""
        # 已知的巨蛋球場ID列表
        dome_venues = {
            14,   # Tropicana Field (TB)
            15,   # Rogers Centre (TOR)
            2392, # Minute Maid Park (HOU)
            2394, # Marlins Park (MIA)
            2395, # Chase Field (AZ)
            2681, # T-Mobile Park (SEA) - 可開合屋頂
        }
        return venue_id in dome_venues

    def fetch_all_teams_comprehensive_data(self, season: int = None) -> Dict:
        """獲取所有球隊的全面數據"""
        if season is None:
            season = date.today().year

        logger.info(f"開始獲取所有球隊的 {season} 賽季全面數據...")

        # 獲取所有活躍球隊
        teams = Team.query.filter_by(active=True).all()

        results = {
            'total_teams': len(teams),
            'successful_teams': 0,
            'failed_teams': [],
            'details': {}
        }

        for team in teams:
            logger.info(f"處理球隊: {team.team_code} ({team.team_name})")

            try:
                success = self.fetch_all_team_data(team.team_id, season)

                if success:
                    results['successful_teams'] += 1
                    results['details'][team.team_code] = 'success'
                    logger.info(f"✅ {team.team_code} 數據獲取成功")
                else:
                    results['failed_teams'].append(team.team_code)
                    results['details'][team.team_code] = 'failed'
                    logger.warning(f"❌ {team.team_code} 數據獲取失敗")

                # 避免API限制
                time.sleep(1)

            except Exception as e:
                logger.error(f"處理球隊 {team.team_code} 時發生錯誤: {e}")
                results['failed_teams'].append(team.team_code)
                results['details'][team.team_code] = f'error: {str(e)}'

        logger.info(f"全面數據獲取完成: {results['successful_teams']}/{results['total_teams']} 成功")

        return results
