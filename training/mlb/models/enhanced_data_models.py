#!/usr/bin/env python3
"""
增強的數據模型 - 包含所有可能影響賽果的球隊和球員資料
"""

from datetime import datetime, date
from sqlalchemy import Index
from .database import db

class TeamAdvancedStats(db.Model):
    """球隊進階統計數據 - 影響賽果的關鍵因素"""
    __tablename__ = 'team_advanced_stats'
    
    id = db.Column(db.Integer, primary_key=True)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.team_id'), nullable=False)
    season = db.Column(db.Integer, nullable=False)
    date_updated = db.Column(db.Date, default=date.today)
    
    # === 打線深度和品質 ===
    # 核心打者表現 (1-5棒)
    core_lineup_avg = db.Column(db.Float, default=0.0)  # 核心打線打擊率
    core_lineup_ops = db.Column(db.Float, default=0.0)  # 核心打線OPS
    core_lineup_hr_rate = db.Column(db.Float, default=0.0)  # 核心打線全壘打率
    
    # 板凳深度
    bench_avg = db.Column(db.Float, default=0.0)  # 替補打擊率
    bench_ops = db.Column(db.Float, default=0.0)  # 替補OPS
    pinch_hit_success = db.Column(db.Float, default=0.0)  # 代打成功率
    
    # 關鍵時刻表現
    risp_avg = db.Column(db.Float, default=0.0)  # 得分圈打擊率
    clutch_avg = db.Column(db.Float, default=0.0)  # 關鍵時刻打擊率
    late_inning_avg = db.Column(db.Float, default=0.0)  # 後段局數打擊率
    
    # === 投手輪值和牛棚 ===
    # 先發投手
    starter_era = db.Column(db.Float, default=0.0)  # 先發投手ERA
    starter_whip = db.Column(db.Float, default=0.0)  # 先發投手WHIP
    starter_k_rate = db.Column(db.Float, default=0.0)  # 先發投手三振率
    starter_innings_avg = db.Column(db.Float, default=0.0)  # 先發投手平均局數
    
    # 牛棚強度
    bullpen_era = db.Column(db.Float, default=0.0)  # 牛棚ERA
    bullpen_whip = db.Column(db.Float, default=0.0)  # 牛棚WHIP
    bullpen_k_rate = db.Column(db.Float, default=0.0)  # 牛棚三振率
    closer_save_pct = db.Column(db.Float, default=0.0)  # 終結者救援成功率
    
    # === 守備能力 ===
    fielding_percentage = db.Column(db.Float, default=0.0)  # 守備率
    defensive_efficiency = db.Column(db.Float, default=0.0)  # 守備效率
    errors_per_game = db.Column(db.Float, default=0.0)  # 每場失誤數
    double_plays = db.Column(db.Integer, default=0)  # 雙殺數
    
    # === 特殊情況表現 ===
    # 主客場差異
    home_field_advantage = db.Column(db.Float, default=0.0)  # 主場優勢指數
    road_performance = db.Column(db.Float, default=0.0)  # 客場表現指數
    
    # 對戰記錄
    vs_left_avg = db.Column(db.Float, default=0.0)  # 對左投打擊率
    vs_right_avg = db.Column(db.Float, default=0.0)  # 對右投打擊率
    
    # 近期狀態
    last_10_games_record = db.Column(db.String(20))  # 最近10場戰績
    last_30_days_ops = db.Column(db.Float, default=0.0)  # 最近30天OPS
    momentum_score = db.Column(db.Float, default=0.0)  # 氣勢指數
    
    # === 傷兵情況 ===
    injured_players_count = db.Column(db.Integer, default=0)  # 傷兵人數
    key_players_injured = db.Column(db.Boolean, default=False)  # 關鍵球員受傷
    dl_days_total = db.Column(db.Integer, default=0)  # 總傷兵天數
    
    # === 其他影響因素 ===
    team_chemistry_score = db.Column(db.Float, default=0.0)  # 團隊化學反應
    manager_experience = db.Column(db.Integer, default=0)  # 總教練經驗
    payroll_rank = db.Column(db.Integer, default=0)  # 薪資排名
    
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (Index('idx_team_advanced_season', 'team_id', 'season'),)
    
    def __repr__(self):
        return f'<TeamAdvancedStats {self.team_id} {self.season}>'

class PlayerInjuryReport(db.Model):
    """球員傷病報告"""
    __tablename__ = 'player_injury_reports'
    
    id = db.Column(db.Integer, primary_key=True)
    player_id = db.Column(db.Integer, db.ForeignKey('players.player_id'), nullable=False)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.team_id'), nullable=False)
    
    # 傷病信息
    injury_type = db.Column(db.String(100))  # 傷病類型
    injury_description = db.Column(db.Text)  # 傷病描述
    body_part = db.Column(db.String(50))  # 受傷部位
    severity = db.Column(db.String(20))  # 嚴重程度: minor, moderate, severe
    
    # 時間信息
    injury_date = db.Column(db.Date)  # 受傷日期
    expected_return = db.Column(db.Date)  # 預計復出日期
    actual_return = db.Column(db.Date)  # 實際復出日期
    days_missed = db.Column(db.Integer, default=0)  # 缺席天數
    
    # 狀態
    status = db.Column(db.String(20))  # active, day-to-day, 15-day-dl, 60-day-dl, out
    is_active = db.Column(db.Boolean, default=True)
    
    # 影響評估
    impact_on_team = db.Column(db.String(20))  # high, medium, low
    replacement_quality = db.Column(db.Float, default=0.0)  # 替補球員品質評分
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (Index('idx_player_injury_date', 'player_id', 'injury_date'),)
    
    def __repr__(self):
        return f'<PlayerInjury {self.player_id}: {self.injury_type}>'

class PlayerPerformanceTrends(db.Model):
    """球員表現趨勢"""
    __tablename__ = 'player_performance_trends'
    
    id = db.Column(db.Integer, primary_key=True)
    player_id = db.Column(db.Integer, db.ForeignKey('players.player_id'), nullable=False)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.team_id'), nullable=False)
    season = db.Column(db.Integer, nullable=False)
    date_calculated = db.Column(db.Date, default=date.today)
    
    # === 打者趨勢 ===
    # 最近表現
    last_7_days_avg = db.Column(db.Float, default=0.0)
    last_15_days_avg = db.Column(db.Float, default=0.0)
    last_30_days_avg = db.Column(db.Float, default=0.0)
    
    # 趨勢指標
    hot_streak_games = db.Column(db.Integer, default=0)  # 連續好表現場次
    cold_streak_games = db.Column(db.Integer, default=0)  # 連續低迷場次
    consistency_score = db.Column(db.Float, default=0.0)  # 穩定性評分
    
    # 特殊情況表現
    vs_lefty_avg = db.Column(db.Float, default=0.0)  # 對左投打擊率
    vs_righty_avg = db.Column(db.Float, default=0.0)  # 對右投打擊率
    home_vs_away_diff = db.Column(db.Float, default=0.0)  # 主客場差異
    
    # === 投手趨勢 ===
    # 最近表現
    last_5_starts_era = db.Column(db.Float, default=0.0)
    last_10_starts_era = db.Column(db.Float, default=0.0)
    recent_velocity = db.Column(db.Float, default=0.0)  # 最近球速
    
    # 疲勞指標
    innings_pitched_trend = db.Column(db.Float, default=0.0)  # 投球局數趨勢
    pitch_count_avg = db.Column(db.Float, default=0.0)  # 平均用球數
    rest_days = db.Column(db.Integer, default=0)  # 休息天數
    
    # 對戰優勢
    vs_team_era = db.Column(db.Float, default=0.0)  # 對特定球隊ERA
    vs_division_era = db.Column(db.Float, default=0.0)  # 對分區球隊ERA
    
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (Index('idx_player_trends_season', 'player_id', 'season'),)
    
    def __repr__(self):
        return f'<PlayerTrends {self.player_id} {self.season}>'

class WeatherConditions(db.Model):
    """天氣條件記錄"""
    __tablename__ = 'weather_conditions'
    
    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.String(50), db.ForeignKey('games.game_id'), nullable=False)
    venue_id = db.Column(db.Integer)  # 球場ID
    
    # 基本天氣
    temperature = db.Column(db.Float)  # 溫度 (華氏)
    humidity = db.Column(db.Float)  # 濕度 (%)
    wind_speed = db.Column(db.Float)  # 風速 (mph)
    wind_direction = db.Column(db.String(10))  # 風向
    
    # 天氣狀況
    weather_condition = db.Column(db.String(50))  # sunny, cloudy, rainy, etc.
    precipitation = db.Column(db.Float, default=0.0)  # 降雨量
    visibility = db.Column(db.Float)  # 能見度
    
    # 影響評估
    hitting_friendly = db.Column(db.Boolean)  # 是否有利打擊
    pitching_friendly = db.Column(db.Boolean)  # 是否有利投球
    dome_stadium = db.Column(db.Boolean, default=False)  # 是否為巨蛋球場
    
    # 特殊條件
    day_night = db.Column(db.String(10))  # day, night
    game_time_temp = db.Column(db.Float)  # 比賽時間溫度
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (Index('idx_weather_game', 'game_id'),)
    
    def __repr__(self):
        return f'<Weather {self.game_id}: {self.temperature}°F>'

class TeamChemistry(db.Model):
    """球隊化學反應和氣氛"""
    __tablename__ = 'team_chemistry'
    
    id = db.Column(db.Integer, primary_key=True)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.team_id'), nullable=False)
    season = db.Column(db.Integer, nullable=False)
    date_assessed = db.Column(db.Date, default=date.today)
    
    # 團隊指標
    clubhouse_harmony = db.Column(db.Float, default=0.0)  # 休息室和諧度
    leadership_quality = db.Column(db.Float, default=0.0)  # 領導力品質
    veteran_presence = db.Column(db.Float, default=0.0)  # 老將影響力
    
    # 表現指標
    comeback_wins = db.Column(db.Integer, default=0)  # 逆轉勝場次
    blowout_losses = db.Column(db.Integer, default=0)  # 大敗場次
    close_game_record = db.Column(db.String(20))  # 接戰記錄
    
    # 壓力處理
    playoff_experience = db.Column(db.Float, default=0.0)  # 季後賽經驗
    pressure_performance = db.Column(db.Float, default=0.0)  # 壓力下表現
    
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (Index('idx_team_chemistry_season', 'team_id', 'season'),)
    
    def __repr__(self):
        return f'<TeamChemistry {self.team_id} {self.season}>'
