"""
SportsBookReview.com 歷史盤口數據抓取器
從 sportsbookreview.com 抓取歷史MLB盤口數據
"""

import requests
from bs4 import BeautifulSoup
import re
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
import time
import random

logger = logging.getLogger(__name__)

class SportsBookReviewScraper:
    """從 SportsBookReview.com 抓取歷史MLB盤口數據"""
    
    def __init__(self):
        self.base_url = "https://www.sportsbookreview.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def fetch_historical_odds(self, target_date: date, bookmaker: str = 'bet365') -> Dict:
        """
        抓取指定日期的歷史盤口數據
        
        Args:
            target_date: 目標日期
            bookmaker: 博彩商名稱 (bet365, fanduel, caesars, draftkings, betrivers)
            
        Returns:
            包含盤口數據的字典
        """
        try:
            # 格式化日期
            date_str = target_date.strftime('%Y-%m-%d')
            
            # 獲取讓分盤數據
            spreads_data = self._fetch_spreads(date_str, bookmaker)
            
            # 獲取總分盤數據
            totals_data = self._fetch_totals(date_str, bookmaker)
            
            # 合併數據
            combined_data = self._combine_odds_data(spreads_data, totals_data)
            
            return {
                'success': True,
                'date': date_str,
                'bookmaker': bookmaker,
                'games': combined_data,
                'total_games': len(combined_data)
            }
            
        except Exception as e:
            logger.error(f"抓取 {target_date} 盤口數據失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'date': date_str,
                'bookmaker': bookmaker
            }
    
    def _fetch_spreads(self, date_str: str, bookmaker: str) -> List[Dict]:
        """抓取讓分盤數據"""
        url = f"{self.base_url}/betting-odds/mlb-baseball/pointspread/full-game/?date={date_str}"
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            games_data = []
            
            # 查找比賽行
            game_rows = soup.find_all('tr', {'class': lambda x: x and 'game-row' in x}) or \
                       soup.find_all('tr', {'onclick': True})
            
            if not game_rows:
                # 嘗試其他選擇器
                game_rows = soup.select('tr:has(td)')
            
            for row in game_rows:
                try:
                    game_data = self._parse_spread_row(row, bookmaker)
                    if game_data:
                        games_data.append(game_data)
                except Exception as e:
                    logger.warning(f"解析讓分盤行失敗: {e}")
                    continue
            
            logger.info(f"成功抓取 {date_str} 讓分盤數據: {len(games_data)} 場比賽")
            return games_data
            
        except Exception as e:
            logger.error(f"抓取讓分盤數據失敗: {e}")
            return []
    
    def _fetch_totals(self, date_str: str, bookmaker: str) -> List[Dict]:
        """抓取總分盤數據"""
        url = f"{self.base_url}/betting-odds/mlb-baseball/totals/full-game/?date={date_str}"
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            games_data = []
            
            # 查找比賽行
            game_rows = soup.find_all('tr', {'class': lambda x: x and 'game-row' in x}) or \
                       soup.find_all('tr', {'onclick': True})
            
            if not game_rows:
                # 嘗試其他選擇器
                game_rows = soup.select('tr:has(td)')
            
            for row in game_rows:
                try:
                    game_data = self._parse_totals_row(row, bookmaker)
                    if game_data:
                        games_data.append(game_data)
                except Exception as e:
                    logger.warning(f"解析總分盤行失敗: {e}")
                    continue
            
            logger.info(f"成功抓取 {date_str} 總分盤數據: {len(games_data)} 場比賽")
            return games_data
            
        except Exception as e:
            logger.error(f"抓取總分盤數據失敗: {e}")
            return []
    
    def _parse_spread_row(self, row, bookmaker: str) -> Optional[Dict]:
        """解析讓分盤行數據"""
        try:
            cells = row.find_all('td')
            if len(cells) < 5:
                return None
            
            # 提取隊伍名稱
            teams = self._extract_teams(row)
            if not teams:
                return None
            
            away_team, home_team = teams
            
            # 查找博彩商列
            bookmaker_column = self._find_bookmaker_column(row, bookmaker)
            if not bookmaker_column:
                return None
            
            # 解析讓分盤數據
            spread_data = self._parse_spread_odds(bookmaker_column)
            if not spread_data:
                return None
            
            return {
                'away_team': away_team,
                'home_team': home_team,
                'market_type': 'spreads',
                'bookmaker': bookmaker,
                'spread_data': spread_data
            }
            
        except Exception as e:
            logger.warning(f"解析讓分盤行失敗: {e}")
            return None
    
    def _parse_totals_row(self, row, bookmaker: str) -> Optional[Dict]:
        """解析總分盤行數據"""
        try:
            cells = row.find_all('td')
            if len(cells) < 5:
                return None
            
            # 提取隊伍名稱
            teams = self._extract_teams(row)
            if not teams:
                return None
            
            away_team, home_team = teams
            
            # 查找博彩商列
            bookmaker_column = self._find_bookmaker_column(row, bookmaker)
            if not bookmaker_column:
                return None
            
            # 解析總分盤數據
            totals_data = self._parse_totals_odds(bookmaker_column)
            if not totals_data:
                return None
            
            return {
                'away_team': away_team,
                'home_team': home_team,
                'market_type': 'totals',
                'bookmaker': bookmaker,
                'totals_data': totals_data
            }
            
        except Exception as e:
            logger.warning(f"解析總分盤行失敗: {e}")
            return None
    
    def _extract_teams(self, row) -> Optional[Tuple[str, str]]:
        """從行中提取隊伍名稱"""
        try:
            # 查找隊伍名稱
            team_links = row.find_all('a', href=re.compile(r'/scores/mlb-baseball/matchup/'))
            if len(team_links) >= 2:
                away_team = team_links[0].get_text(strip=True).split(' - ')[0].strip('*')
                home_team = team_links[1].get_text(strip=True).split(' - ')[0].strip('*')
                return self._normalize_team_name(away_team), self._normalize_team_name(home_team)
            
            # 備用方法：查找粗體文字
            bold_texts = row.find_all('strong') or row.find_all('b')
            if len(bold_texts) >= 2:
                away_team = bold_texts[0].get_text(strip=True).strip('*')
                home_team = bold_texts[1].get_text(strip=True).strip('*')
                return self._normalize_team_name(away_team), self._normalize_team_name(home_team)
            
            return None
            
        except Exception as e:
            logger.warning(f"提取隊伍名稱失敗: {e}")
            return None
    
    def _find_bookmaker_column(self, row, bookmaker: str) -> Optional:
        """查找指定博彩商的列"""
        try:
            # 博彩商名稱映射
            bookmaker_map = {
                'bet365': ['bet365', 'Bet365'],
                'fanduel': ['fanduel', 'FanDuel'],
                'caesars': ['caesars', 'Caesars'],
                'draftkings': ['draftkings', 'DraftKings'],
                'betrivers': ['betrivers', 'BetRivers']
            }
            
            target_names = bookmaker_map.get(bookmaker.lower(), [bookmaker])
            
            # 查找包含博彩商鏈接的列
            for cell in row.find_all('td'):
                links = cell.find_all('a')
                for link in links:
                    href = link.get('href', '')
                    for name in target_names:
                        if name.lower() in href.lower():
                            return cell
            
            # 如果沒找到，返回第一個包含賠率的列
            for cell in row.find_all('td'):
                text = cell.get_text(strip=True)
                if re.search(r'[+-]\d+', text):
                    return cell
            
            return None
            
        except Exception as e:
            logger.warning(f"查找博彩商列失敗: {e}")
            return None
    
    def _parse_spread_odds(self, column) -> Optional[Dict]:
        """解析讓分盤賠率"""
        try:
            text = column.get_text(strip=True)
            
            # 查找讓分盤格式: -1.5+136 或 ****-164
            spread_pattern = r'([+-]?\d+\.?\d*)\s*([+-]\d+)'
            matches = re.findall(spread_pattern, text)
            
            if len(matches) >= 2:
                # 第一個通常是客隊，第二個是主隊
                away_point, away_odds = matches[0]
                home_point, home_odds = matches[1]
                
                return {
                    'away_spread_point': float(away_point),
                    'away_spread_odds': int(away_odds),
                    'home_spread_point': float(home_point),
                    'home_spread_odds': int(home_odds)
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"解析讓分盤賠率失敗: {e}")
            return None
    
    def _parse_totals_odds(self, column) -> Optional[Dict]:
        """解析總分盤賠率"""
        try:
            text = column.get_text(strip=True)
            
            # 查找總分盤格式: O 8.5-110 U 8.5-110 或 8.5-110 8.5-110
            totals_pattern = r'(\d+\.?\d*)\s*([+-]\d+)'
            matches = re.findall(totals_pattern, text)
            
            if len(matches) >= 2:
                # 第一個是Over，第二個是Under
                over_point, over_odds = matches[0]
                under_point, under_odds = matches[1]
                
                return {
                    'total_point': float(over_point),
                    'over_odds': int(over_odds),
                    'under_odds': int(under_odds)
                }
            elif len(matches) == 1:
                # 只有一個分數，假設Over和Under相同
                point, odds = matches[0]
                return {
                    'total_point': float(point),
                    'over_odds': int(odds),
                    'under_odds': int(odds)
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"解析總分盤賠率失敗: {e}")
            return None
    
    def _normalize_team_name(self, team_name: str) -> str:
        """標準化隊伍名稱"""
        # 移除特殊字符和空格
        team_name = re.sub(r'[^\w\s]', '', team_name).strip()
        
        # 隊伍名稱映射
        team_mapping = {
            'ATL': 'ATL', 'PHI': 'PHI', 'TOR': 'TOR', 'NYY': 'NYY',
            'BAL': 'BAL', 'BOS': 'BOS', 'STL': 'STL', 'CIN': 'CIN',
            'CLE': 'CLE', 'DET': 'DET', 'MIL': 'MIL', 'CHC': 'CHC',
            'LAD': 'LAD', 'COL': 'COL', 'HOU': 'HOU', 'OAK': 'OAK',
            'ARI': 'ARI', 'SD': 'SD', 'CHW': 'CHW', 'LAA': 'LAA',
            'TB': 'TB', 'MIN': 'MIN', 'WSH': 'WSH', 'NYM': 'NYM',
            'MIA': 'MIA', 'SEA': 'SEA', 'TEX': 'TEX', 'SF': 'SF',
            'KC': 'KC', 'PIT': 'PIT'
        }
        
        return team_mapping.get(team_name.upper(), team_name.upper())
    
    def _combine_odds_data(self, spreads_data: List[Dict], totals_data: List[Dict]) -> List[Dict]:
        """合併讓分盤和總分盤數據"""
        combined = {}
        
        # 處理讓分盤數據
        for game in spreads_data:
            key = f"{game['away_team']}@{game['home_team']}"
            if key not in combined:
                combined[key] = {
                    'away_team': game['away_team'],
                    'home_team': game['home_team'],
                    'bookmaker': game['bookmaker']
                }
            combined[key]['spreads'] = game['spread_data']
        
        # 處理總分盤數據
        for game in totals_data:
            key = f"{game['away_team']}@{game['home_team']}"
            if key not in combined:
                combined[key] = {
                    'away_team': game['away_team'],
                    'home_team': game['home_team'],
                    'bookmaker': game['bookmaker']
                }
            combined[key]['totals'] = game['totals_data']
        
        return list(combined.values())
    
    def add_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """添加隨機延遲以避免被封"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
