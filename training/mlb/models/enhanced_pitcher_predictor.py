"""
增強投手預測模組
整合先發投手個人統計、牛棚強度、投手疲勞度等因素到預測特徵中
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import date, timedelta
import pandas as pd
from sqlalchemy import func, and_, or_

from models.database import (
    db, Game, GameDetail, PlayerStats, Team, BoxScore
)

logger = logging.getLogger(__name__)

class EnhancedPitcherPredictor:
    """增強投手預測器 - 整合先發投手和牛棚分析"""
    
    def __init__(self):
        self.logger = logger
        
    def get_pitcher_enhanced_features(self, game_data: Dict) -> Dict:
        """
        獲取增強的投手相關特徵
        
        Args:
            game_data: 包含 home_team, away_team, date, game_id 的比賽數據
            
        Returns:
            Dict: 投手相關特徵字典
        """
        features = {}
        
        try:
            home_team = game_data.get('home_team')
            away_team = game_data.get('away_team')
            game_date = game_data.get('date')
            game_id = game_data.get('game_id')
            
            # 1. 獲取實際先發投手信息
            starting_pitchers = self._get_actual_starting_pitchers(game_id)
            
            # 2. 先發投手個人統計特徵
            home_starter_features = self._get_starting_pitcher_features(
                starting_pitchers.get('home_starter'), home_team, 'home'
            )
            away_starter_features = self._get_starting_pitcher_features(
                starting_pitchers.get('away_starter'), away_team, 'away'
            )
            
            # 3. 牛棚強度評估
            home_bullpen_features = self._get_bullpen_strength_features(home_team, game_date, 'home')
            away_bullpen_features = self._get_bullpen_strength_features(away_team, game_date, 'away')
            
            # 4. 投手對戰分析
            matchup_features = self._get_pitcher_matchup_features(
                starting_pitchers, home_team, away_team
            )
            
            # 5. 投手疲勞度和最近表現
            fatigue_features = self._get_pitcher_fatigue_features(
                starting_pitchers, game_date
            )
            
            # 整合所有特徵
            features.update(home_starter_features)
            features.update(away_starter_features)
            features.update(home_bullpen_features)
            features.update(away_bullpen_features)
            features.update(matchup_features)
            features.update(fatigue_features)
            
            self.logger.info(f"成功提取投手特徵: {len(features)} 個特徵")
            
        except Exception as e:
            self.logger.error(f"獲取投手特徵失敗: {e}")
            # 返回默認特徵
            features = self._get_default_pitcher_features()
            
        return features
    
    def _get_actual_starting_pitchers(self, game_id: str) -> Dict:
        """獲取實際先發投手信息"""
        try:
            game_detail = GameDetail.query.filter_by(game_id=game_id).first()
            
            if game_detail:
                return {
                    'home_starter': game_detail.home_starting_pitcher,
                    'away_starter': game_detail.away_starting_pitcher,
                    'winning_pitcher': game_detail.winning_pitcher,
                    'losing_pitcher': game_detail.losing_pitcher,
                    'save_pitcher': game_detail.save_pitcher
                }
            else:
                self.logger.warning(f"未找到比賽詳情: {game_id}")
                return {}
                
        except Exception as e:
            self.logger.error(f"獲取先發投手信息失敗: {e}")
            return {}
    
    def _get_starting_pitcher_features(self, pitcher_name: str, team: str, prefix: str) -> Dict:
        """獲取先發投手個人統計特徵"""
        features = {}
        
        try:
            if not pitcher_name:
                return self._get_default_starter_features(prefix)
            
            # 查找投手統計數據
            pitcher_stats = self._find_pitcher_stats(pitcher_name, team)
            
            if pitcher_stats:
                # 基本統計
                features[f'{prefix}_starter_era'] = pitcher_stats.era or 4.50
                features[f'{prefix}_starter_whip'] = pitcher_stats.whip or 1.30
                features[f'{prefix}_starter_strikeouts_per_9'] = self._calculate_k_per_9(pitcher_stats)
                features[f'{prefix}_starter_walks_per_9'] = self._calculate_bb_per_9(pitcher_stats)
                features[f'{prefix}_starter_hr_per_9'] = self._calculate_hr_per_9(pitcher_stats)
                
                # 勝負記錄
                features[f'{prefix}_starter_wins'] = pitcher_stats.wins or 0
                features[f'{prefix}_starter_losses'] = pitcher_stats.losses or 0
                features[f'{prefix}_starter_win_pct'] = self._calculate_win_percentage(pitcher_stats)
                
                # 投手質量評分
                features[f'{prefix}_starter_quality_score'] = self._calculate_pitcher_quality_score(pitcher_stats)
                
                # 王牌投手標記
                features[f'{prefix}_starter_is_ace'] = 1 if (pitcher_stats.era or 5.0) < 3.0 else 0
                
                # 投球局數和經驗
                features[f'{prefix}_starter_innings_pitched'] = pitcher_stats.innings_pitched or 0
                features[f'{prefix}_starter_games_started'] = pitcher_stats.games_started or 0
                
            else:
                features = self._get_default_starter_features(prefix)
                
        except Exception as e:
            self.logger.error(f"獲取先發投手特徵失敗 ({pitcher_name}): {e}")
            features = self._get_default_starter_features(prefix)
            
        return features
    
    def _find_pitcher_stats(self, pitcher_name: str, team: str) -> Optional[PlayerStats]:
        """查找投手統計數據"""
        try:
            # 獲取team_id
            team_obj = Team.query.filter(
                or_(
                    Team.team_code == team,
                    Team.team_name == team,
                    Team.team_name_short == team
                )
            ).first()
            
            if not team_obj:
                self.logger.warning(f"未找到球隊: {team}")
                return None
            
            # 多種姓名匹配策略
            pitcher_stats = PlayerStats.query.filter(
                PlayerStats.team_id == team_obj.team_id,
                PlayerStats.era.isnot(None),
                or_(
                    PlayerStats.player_name == pitcher_name,
                    PlayerStats.player_name.like(f'%{pitcher_name}%'),
                    PlayerStats.player_name.like(f'%{pitcher_name.split()[0]}%')
                )
            ).first()
            
            return pitcher_stats
            
        except Exception as e:
            self.logger.error(f"查找投手統計失敗 ({pitcher_name}, {team}): {e}")
            return None
    
    def _calculate_k_per_9(self, pitcher_stats: PlayerStats) -> float:
        """計算每9局三振數"""
        try:
            if pitcher_stats.innings_pitched and pitcher_stats.innings_pitched > 0:
                return (pitcher_stats.strikeouts_pitching or 0) * 9 / pitcher_stats.innings_pitched
            return 7.0  # 默認值
        except:
            return 7.0
    
    def _calculate_bb_per_9(self, pitcher_stats: PlayerStats) -> float:
        """計算每9局保送數"""
        try:
            if pitcher_stats.innings_pitched and pitcher_stats.innings_pitched > 0:
                return (pitcher_stats.walks_allowed or 0) * 9 / pitcher_stats.innings_pitched
            return 3.5  # 默認值
        except:
            return 3.5
    
    def _calculate_hr_per_9(self, pitcher_stats: PlayerStats) -> float:
        """計算每9局被全壘打數"""
        try:
            if pitcher_stats.innings_pitched and pitcher_stats.innings_pitched > 0:
                return (pitcher_stats.home_runs_allowed or 0) * 9 / pitcher_stats.innings_pitched
            return 1.2  # 默認值
        except:
            return 1.2
    
    def _calculate_win_percentage(self, pitcher_stats: PlayerStats) -> float:
        """計算投手勝率"""
        try:
            wins = pitcher_stats.wins or 0
            losses = pitcher_stats.losses or 0
            total = wins + losses
            if total > 0:
                return wins / total
            return 0.5  # 默認值
        except:
            return 0.5
    
    def _calculate_pitcher_quality_score(self, pitcher_stats: PlayerStats) -> float:
        """計算投手質量評分 (0-100)"""
        try:
            era = pitcher_stats.era or 4.50
            whip = pitcher_stats.whip or 1.30
            k_per_9 = self._calculate_k_per_9(pitcher_stats)
            
            # ERA評分 (越低越好)
            era_score = max(0, 100 - (era - 2.0) * 15)
            
            # WHIP評分 (越低越好)
            whip_score = max(0, 100 - (whip - 1.0) * 40)
            
            # K/9評分 (越高越好)
            k9_score = min(100, max(0, (k_per_9 - 5.0) * 10))
            
            # 綜合評分
            quality_score = (era_score * 0.5 + whip_score * 0.3 + k9_score * 0.2)
            return max(0, min(100, quality_score))
            
        except Exception as e:
            self.logger.warning(f"計算投手質量評分失敗: {e}")
            return 50.0
    
    def _get_default_starter_features(self, prefix: str) -> Dict:
        """獲取默認先發投手特徵"""
        return {
            f'{prefix}_starter_era': 4.50,
            f'{prefix}_starter_whip': 1.30,
            f'{prefix}_starter_strikeouts_per_9': 7.0,
            f'{prefix}_starter_walks_per_9': 3.5,
            f'{prefix}_starter_hr_per_9': 1.2,
            f'{prefix}_starter_wins': 5,
            f'{prefix}_starter_losses': 5,
            f'{prefix}_starter_win_pct': 0.5,
            f'{prefix}_starter_quality_score': 50.0,
            f'{prefix}_starter_is_ace': 0,
            f'{prefix}_starter_innings_pitched': 50.0,
            f'{prefix}_starter_games_started': 10
        }
    
    def _get_default_pitcher_features(self) -> Dict:
        """獲取默認投手特徵"""
        features = {}
        features.update(self._get_default_starter_features('home'))
        features.update(self._get_default_starter_features('away'))
        
        # 添加其他默認特徵
        features.update({
            'home_bullpen_era': 4.50,
            'away_bullpen_era': 4.50,
            'pitcher_matchup_advantage': 0.0,
            'ace_pitcher_duel': 0,
            'home_starter_fatigue': 0.0,
            'away_starter_fatigue': 0.0
        })
        
        return features

    def _get_bullpen_strength_features(self, team: str, game_date: date, prefix: str) -> Dict:
        """獲取牛棚強度評估特徵"""
        features = {}

        try:
            # 獲取team_id
            team_obj = Team.query.filter(
                or_(
                    Team.team_code == team,
                    Team.team_name == team,
                    Team.team_name_short == team
                )
            ).first()

            if not team_obj:
                return self._get_default_bullpen_features(prefix)

            # 查詢救援投手統計（非先發投手）
            relief_pitchers = PlayerStats.query.filter(
                PlayerStats.team_id == team_obj.team_id,
                PlayerStats.era.isnot(None),
                PlayerStats.era > 0,
                or_(
                    PlayerStats.games_started == 0,  # 沒有先發記錄
                    PlayerStats.games_started < (PlayerStats.games_played or 0) * 0.3  # 先發比例低於30%
                )
            ).all()

            if relief_pitchers:
                # 計算牛棚整體ERA
                total_innings = sum(p.innings_pitched or 0 for p in relief_pitchers)
                if total_innings > 0:
                    weighted_era = sum(
                        (p.era or 4.50) * (p.innings_pitched or 0)
                        for p in relief_pitchers
                    ) / total_innings
                else:
                    weighted_era = 4.50

                # 計算牛棚整體WHIP
                weighted_whip = sum(
                    (p.whip or 1.30) * (p.innings_pitched or 0)
                    for p in relief_pitchers
                ) / total_innings if total_innings > 0 else 1.30

                # 計算牛棚深度（可用投手數量）
                bullpen_depth = len([p for p in relief_pitchers if (p.innings_pitched or 0) > 10])

                # 計算關鍵時刻表現（假設saves和holds代表關鍵時刻）
                total_saves = sum(p.saves or 0 for p in relief_pitchers)
                total_blown_saves = sum(p.blown_saves or 0 for p in relief_pitchers)
                save_percentage = total_saves / (total_saves + total_blown_saves) if (total_saves + total_blown_saves) > 0 else 0.8

                features[f'{prefix}_bullpen_era'] = weighted_era
                features[f'{prefix}_bullpen_whip'] = weighted_whip
                features[f'{prefix}_bullpen_depth'] = bullpen_depth
                features[f'{prefix}_bullpen_save_pct'] = save_percentage
                features[f'{prefix}_bullpen_quality_score'] = self._calculate_bullpen_quality(
                    weighted_era, weighted_whip, bullpen_depth, save_percentage
                )

            else:
                features = self._get_default_bullpen_features(prefix)

        except Exception as e:
            self.logger.error(f"獲取牛棚強度特徵失敗 ({team}): {e}")
            features = self._get_default_bullpen_features(prefix)

        return features

    def _calculate_bullpen_quality(self, era: float, whip: float, depth: int, save_pct: float) -> float:
        """計算牛棚質量評分 (0-100)"""
        try:
            # ERA評分
            era_score = max(0, 100 - (era - 3.0) * 20)

            # WHIP評分
            whip_score = max(0, 100 - (whip - 1.1) * 40)

            # 深度評分
            depth_score = min(100, depth * 15)  # 每個可用投手15分，最多100分

            # 救援成功率評分
            save_score = save_pct * 100

            # 綜合評分
            quality_score = (era_score * 0.4 + whip_score * 0.3 + depth_score * 0.2 + save_score * 0.1)
            return max(0, min(100, quality_score))

        except Exception as e:
            self.logger.warning(f"計算牛棚質量評分失敗: {e}")
            return 50.0

    def _get_default_bullpen_features(self, prefix: str) -> Dict:
        """獲取默認牛棚特徵"""
        return {
            f'{prefix}_bullpen_era': 4.50,
            f'{prefix}_bullpen_whip': 1.30,
            f'{prefix}_bullpen_depth': 5,
            f'{prefix}_bullpen_save_pct': 0.8,
            f'{prefix}_bullpen_quality_score': 50.0
        }

    def _get_pitcher_matchup_features(self, starting_pitchers: Dict, home_team: str, away_team: str) -> Dict:
        """獲取投手對戰分析特徵"""
        features = {
            'pitcher_matchup_advantage': 0.0,
            'ace_pitcher_duel': 0,
            'pitcher_era_differential': 0.0,
            'pitcher_whip_differential': 0.0,
            'pitcher_dominance_factor': 0.0
        }

        try:
            home_starter = starting_pitchers.get('home_starter')
            away_starter = starting_pitchers.get('away_starter')

            if home_starter and away_starter:
                # 獲取兩位先發投手的統計
                home_pitcher_stats = self._find_pitcher_stats(home_starter, home_team)
                away_pitcher_stats = self._find_pitcher_stats(away_starter, away_team)

                if home_pitcher_stats and away_pitcher_stats:
                    home_era = home_pitcher_stats.era or 4.50
                    away_era = away_pitcher_stats.era or 4.50
                    home_whip = home_pitcher_stats.whip or 1.30
                    away_whip = away_pitcher_stats.whip or 1.30

                    # ERA差異（正值表示主隊投手優勢）
                    era_diff = away_era - home_era
                    features['pitcher_era_differential'] = era_diff

                    # WHIP差異
                    whip_diff = away_whip - home_whip
                    features['pitcher_whip_differential'] = whip_diff

                    # 投手對戰優勢（綜合ERA和WHIP）
                    matchup_advantage = (era_diff + whip_diff) / 2
                    features['pitcher_matchup_advantage'] = max(-2.0, min(2.0, matchup_advantage))

                    # 王牌投手對決
                    home_is_ace = 1 if home_era < 3.0 else 0
                    away_is_ace = 1 if away_era < 3.0 else 0
                    features['ace_pitcher_duel'] = 1 if (home_is_ace and away_is_ace) else 0

                    # 投手主導因子（兩位投手都很強時，比賽可能低分）
                    if home_era < 3.5 and away_era < 3.5:
                        features['pitcher_dominance_factor'] = 1.0
                    elif home_era > 5.0 or away_era > 5.0:
                        features['pitcher_dominance_factor'] = -1.0
                    else:
                        features['pitcher_dominance_factor'] = 0.0

        except Exception as e:
            self.logger.error(f"獲取投手對戰特徵失敗: {e}")

        return features

    def _get_pitcher_fatigue_features(self, starting_pitchers: Dict, game_date: date) -> Dict:
        """獲取投手疲勞度和最近表現特徵"""
        features = {
            'home_starter_fatigue': 0.0,
            'away_starter_fatigue': 0.0,
            'home_starter_recent_performance': 50.0,
            'away_starter_recent_performance': 50.0
        }

        try:
            home_starter = starting_pitchers.get('home_starter')
            away_starter = starting_pitchers.get('away_starter')

            # 分析主隊先發投手
            if home_starter:
                home_fatigue = self._calculate_pitcher_fatigue(home_starter, game_date)
                home_recent = self._calculate_recent_performance(home_starter, game_date)
                features['home_starter_fatigue'] = home_fatigue
                features['home_starter_recent_performance'] = home_recent

            # 分析客隊先發投手
            if away_starter:
                away_fatigue = self._calculate_pitcher_fatigue(away_starter, game_date)
                away_recent = self._calculate_recent_performance(away_starter, game_date)
                features['away_starter_fatigue'] = away_fatigue
                features['away_starter_recent_performance'] = away_recent

        except Exception as e:
            self.logger.error(f"獲取投手疲勞度特徵失敗: {e}")

        return features

    def _calculate_pitcher_fatigue(self, pitcher_name: str, game_date: date) -> float:
        """計算投手疲勞度 (0-100, 100表示完全休息)"""
        try:
            # 查找投手最近的出場記錄
            recent_games = Game.query.join(GameDetail).filter(
                or_(
                    GameDetail.home_starting_pitcher == pitcher_name,
                    GameDetail.away_starting_pitcher == pitcher_name
                ),
                Game.date < game_date,
                Game.date >= game_date - timedelta(days=14)  # 最近14天
            ).order_by(Game.date.desc()).all()

            if not recent_games:
                return 100.0  # 沒有最近出場記錄，假設完全休息

            # 計算距離上次出場的天數
            last_game = recent_games[0]
            days_rest = (game_date - last_game.date).days

            # 計算最近出場頻率
            games_in_14_days = len(recent_games)

            # 疲勞度計算
            # 休息天數越多，疲勞度越低（分數越高）
            rest_score = min(100, days_rest * 20)  # 每天休息20分，最多100分

            # 出場頻率影響
            frequency_penalty = max(0, (games_in_14_days - 2) * 10)  # 超過2場開始扣分

            fatigue_score = max(0, rest_score - frequency_penalty)
            return fatigue_score

        except Exception as e:
            self.logger.warning(f"計算投手疲勞度失敗 ({pitcher_name}): {e}")
            return 75.0  # 默認值

    def _calculate_recent_performance(self, pitcher_name: str, game_date: date) -> float:
        """計算投手最近表現評分 (0-100)"""
        try:
            # 這裡可以通過BoxScore數據分析投手最近幾場的表現
            # 由於BoxScore數據結構複雜，暫時返回默認值
            # 未來可以擴展為分析最近3-5場比賽的ERA、WHIP等表現

            return 50.0  # 默認中等表現

        except Exception as e:
            self.logger.warning(f"計算投手最近表現失敗 ({pitcher_name}): {e}")
            return 50.0
