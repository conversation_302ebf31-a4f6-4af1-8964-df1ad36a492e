import requests
import json
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional
from sqlalchemy.exc import IntegrityError

from .database import db, Game, BoxScore, PlayerGameStats, Player, Team

logger = logging.getLogger(__name__)

class DetailedDataFetcher:
    """詳細比賽數據獲取器 - 獲取box score和球員表現數據"""
    
    def __init__(self):
        self.base_url = "https://statsapi.mlb.com/api/v1"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MLB-Prediction-System/1.0'
        })
    
    def fetch_game_boxscore(self, game_id: str) -> bool:
        """獲取比賽的box score數據"""
        try:
            logger.info(f"獲取比賽 {game_id} 的box score數據...")
            
            # 獲取比賽詳細數據
            url = f"{self.base_url}/game/{game_id}/boxscore"
            response = self.session.get(url, timeout=30)
            
            if response.status_code != 200:
                logger.error(f"獲取box score失敗: {response.status_code}")
                return False
            
            data = response.json()
            
            # 處理box score數據
            success = self._process_boxscore_data(game_id, data)
            
            if success:
                logger.info(f"比賽 {game_id} box score數據處理完成")
            
            return success
            
        except Exception as e:
            logger.error(f"獲取比賽 {game_id} box score失敗: {e}")
            return False
    
    def _process_boxscore_data(self, game_id: str, data: Dict) -> bool:
        """處理box score數據"""
        try:
            # 獲取比賽信息
            game = Game.query.filter_by(game_id=game_id).first()
            if not game:
                logger.warning(f"找不到比賽記錄: {game_id}")
                return False
            
            # 處理主客隊box score
            teams_data = data.get('teams', {})
            
            # 處理客隊數據
            if 'away' in teams_data:
                self._process_team_boxscore(game_id, teams_data['away'], False)
            
            # 處理主隊數據
            if 'home' in teams_data:
                self._process_team_boxscore(game_id, teams_data['home'], True)
            
            # 處理球員統計
            self._process_player_stats(game_id, data)
            
            db.session.commit()
            return True
            
        except Exception as e:
            logger.error(f"處理box score數據失敗: {e}")
            db.session.rollback()
            return False
    
    def _process_team_boxscore(self, game_id: str, team_data: Dict, is_home: bool) -> None:
        """處理球隊box score數據"""
        try:
            team_info = team_data.get('team', {})
            team_id = team_info.get('id')
            
            if not team_id:
                return
            
            # 獲取球隊統計
            team_stats = team_data.get('teamStats', {})
            batting_stats = team_stats.get('batting', {})
            pitching_stats = team_stats.get('pitching', {})
            
            # 檢查是否已存在box score記錄
            existing_boxscore = BoxScore.query.filter_by(
                game_id=game_id,
                team_id=team_id
            ).first()
            
            if existing_boxscore:
                # 更新現有記錄
                boxscore = existing_boxscore
            else:
                # 創建新記錄
                boxscore = BoxScore(
                    game_id=game_id,
                    team_id=team_id,
                    is_home=is_home
                )
                db.session.add(boxscore)
            
            # 更新統計數據
            boxscore.runs = batting_stats.get('runs', 0)
            boxscore.hits = batting_stats.get('hits', 0)
            boxscore.errors = team_data.get('errors', 0)
            boxscore.left_on_base = batting_stats.get('leftOnBase', 0)
            
            # 投球統計
            boxscore.pitchers_used = pitching_stats.get('numberOfPitchers', 0)
            boxscore.total_pitches = pitching_stats.get('pitchesThrown', 0)
            boxscore.strikes = pitching_stats.get('strikes', 0)
            
            # 處理各局得分
            line_score = team_data.get('lineScore', {})
            if line_score:
                inning_scores = {}
                for inning in line_score.get('innings', []):
                    inning_num = str(inning.get('num', 0))
                    if is_home:
                        inning_scores[inning_num] = inning.get('home', {}).get('runs', 0)
                    else:
                        inning_scores[inning_num] = inning.get('away', {}).get('runs', 0)
                
                boxscore.inning_scores = json.dumps(inning_scores)
            
        except Exception as e:
            logger.error(f"處理球隊box score失敗: {e}")
    
    def _process_player_stats(self, game_id: str, data: Dict) -> None:
        """處理球員統計數據"""
        try:
            teams_data = data.get('teams', {})
            
            # 處理客隊球員
            if 'away' in teams_data:
                self._process_team_player_stats(game_id, teams_data['away'], False)
            
            # 處理主隊球員
            if 'home' in teams_data:
                self._process_team_player_stats(game_id, teams_data['home'], True)
                
        except Exception as e:
            logger.error(f"處理球員統計失敗: {e}")
    
    def _process_team_player_stats(self, game_id: str, team_data: Dict, is_home: bool) -> None:
        """處理單隊球員統計"""
        try:
            team_info = team_data.get('team', {})
            team_id = team_info.get('id')
            
            if not team_id:
                return
            
            # 處理打者統計
            batters = team_data.get('batters', [])
            batting_order = 1
            
            for batter_id in batters:
                player_data = team_data.get('players', {}).get(f'ID{batter_id}', {})
                if player_data:
                    self._process_player_batting_stats(
                        game_id, player_data, team_id, batting_order
                    )
                    batting_order += 1
            
            # 處理投手統計
            pitchers = team_data.get('pitchers', [])
            for pitcher_id in pitchers:
                player_data = team_data.get('players', {}).get(f'ID{pitcher_id}', {})
                if player_data:
                    self._process_player_pitching_stats(
                        game_id, player_data, team_id
                    )
                    
        except Exception as e:
            logger.error(f"處理球隊球員統計失敗: {e}")
    
    def _process_player_batting_stats(self, game_id: str, player_data: Dict, 
                                    team_id: int, batting_order: int) -> None:
        """處理球員打擊統計"""
        try:
            person = player_data.get('person', {})
            player_id = person.get('id')
            
            if not player_id:
                return
            
            # 獲取打擊統計
            batting_stats = player_data.get('stats', {}).get('batting', {})
            
            if not batting_stats:
                return
            
            # 檢查是否已存在記錄
            existing_stats = PlayerGameStats.query.filter_by(
                game_id=game_id,
                player_id=player_id
            ).first()
            
            if existing_stats:
                stats = existing_stats
            else:
                stats = PlayerGameStats(
                    game_id=game_id,
                    player_id=player_id,
                    team_id=team_id
                )
                db.session.add(stats)
            
            # 更新打擊統計
            stats.position = player_data.get('position', {}).get('abbreviation', '')
            stats.batting_order = batting_order
            stats.at_bats = batting_stats.get('atBats', 0)
            stats.runs = batting_stats.get('runs', 0)
            stats.hits = batting_stats.get('hits', 0)
            stats.rbi = batting_stats.get('rbi', 0)
            stats.doubles = batting_stats.get('doubles', 0)
            stats.triples = batting_stats.get('triples', 0)
            stats.home_runs = batting_stats.get('homeRuns', 0)
            stats.walks = batting_stats.get('baseOnBalls', 0)
            stats.strikeouts = batting_stats.get('strikeOuts', 0)
            stats.stolen_bases = batting_stats.get('stolenBases', 0)
            stats.caught_stealing = batting_stats.get('caughtStealing', 0)
            
        except Exception as e:
            logger.error(f"處理球員打擊統計失敗: {e}")
    
    def _process_player_pitching_stats(self, game_id: str, player_data: Dict, 
                                     team_id: int) -> None:
        """處理球員投球統計"""
        try:
            person = player_data.get('person', {})
            player_id = person.get('id')
            
            if not player_id:
                return
            
            # 獲取投球統計
            pitching_stats = player_data.get('stats', {}).get('pitching', {})
            
            if not pitching_stats:
                return
            
            # 檢查是否已存在記錄
            existing_stats = PlayerGameStats.query.filter_by(
                game_id=game_id,
                player_id=player_id
            ).first()
            
            if existing_stats:
                stats = existing_stats
            else:
                stats = PlayerGameStats(
                    game_id=game_id,
                    player_id=player_id,
                    team_id=team_id
                )
                db.session.add(stats)
            
            # 更新投球統計
            stats.position = 'P'  # 投手
            stats.innings_pitched = float(pitching_stats.get('inningsPitched', '0'))
            stats.hits_allowed = pitching_stats.get('hits', 0)
            stats.runs_allowed = pitching_stats.get('runs', 0)
            stats.earned_runs = pitching_stats.get('earnedRuns', 0)
            stats.walks_allowed = pitching_stats.get('baseOnBalls', 0)
            stats.strikeouts_pitched = pitching_stats.get('strikeOuts', 0)
            stats.home_runs_allowed = pitching_stats.get('homeRuns', 0)
            stats.pitches_thrown = pitching_stats.get('pitchesThrown', 0)
            stats.strikes_thrown = pitching_stats.get('strikes', 0)
            
        except Exception as e:
            logger.error(f"處理球員投球統計失敗: {e}")
    
    def fetch_multiple_games_boxscore(self, game_ids: List[str]) -> Dict[str, bool]:
        """批量獲取多場比賽的box score"""
        results = {}
        
        for game_id in game_ids:
            try:
                success = self.fetch_game_boxscore(game_id)
                results[game_id] = success
                
                # 避免請求過於頻繁
                import time
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"批量獲取比賽 {game_id} 失敗: {e}")
                results[game_id] = False
        
        return results
    
    def fetch_games_by_date(self, target_date: date) -> bool:
        """獲取指定日期所有比賽的詳細數據"""
        try:
            # 獲取該日期的所有已完成比賽
            games = Game.query.filter(
                Game.date == target_date,
                Game.game_status == 'completed',
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            if not games:
                logger.info(f"日期 {target_date} 沒有找到已完成的比賽")
                return True
            
            game_ids = [game.game_id for game in games]
            logger.info(f"開始獲取 {target_date} 的 {len(game_ids)} 場比賽詳細數據")
            
            results = self.fetch_multiple_games_boxscore(game_ids)
            
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"日期 {target_date}: 成功獲取 {success_count}/{len(game_ids)} 場比賽詳細數據")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"獲取日期 {target_date} 比賽詳細數據失敗: {e}")
            return False

    def fetch_completed_games_boxscores(self, target_date: date) -> bool:
        """獲取指定日期已完成比賽的Box Score"""
        try:
            from models.database import db, Game, BoxScore

            # 查找指定日期已完成但沒有Box Score的比賽
            completed_games = db.session.query(Game).filter(
                Game.date == target_date,
                Game.game_status == 'completed',
                ~Game.game_id.in_(
                    db.session.query(BoxScore.game_id).distinct()
                )
            ).all()

            if not completed_games:
                logger.info(f"日期 {target_date} 沒有需要下載Box Score的已完成比賽")
                return True

            logger.info(f"找到 {len(completed_games)} 場需要下載Box Score的比賽")

            success_count = 0
            for game in completed_games:
                try:
                    if self.fetch_game_boxscore(game.game_id):
                        success_count += 1
                    import time
                    time.sleep(1)  # 避免請求過於頻繁
                except Exception as e:
                    logger.error(f"下載比賽 {game.game_id} Box Score失敗: {e}")

            db.session.commit()
            logger.info(f"成功下載 {success_count}/{len(completed_games)} 場比賽的Box Score")

            return success_count > 0

        except Exception as e:
            logger.error(f"獲取 {target_date} 已完成比賽Box Score失敗: {e}")
            return False

    def refresh_schedule_for_date_range(self, start_date: date, end_date: date) -> int:
        """刷新指定日期範圍的比賽行程"""
        try:
            from models.data_fetcher import DataFetcher

            game_fetcher = DataFetcher()
            updated_days = 0

            current_date = start_date
            while current_date <= end_date:
                try:
                    # 獲取該日期的比賽行程
                    if game_fetcher.fetch_games_by_date(current_date):
                        updated_days += 1
                        logger.info(f"成功刷新 {current_date} 的比賽行程")

                    import time
                    time.sleep(0.5)  # 避免請求過於頻繁

                except Exception as e:
                    logger.error(f"刷新 {current_date} 行程失敗: {e}")

                current_date += timedelta(days=1)

            logger.info(f"成功刷新 {updated_days} 天的比賽行程")
            return updated_days

        except Exception as e:
            logger.error(f"刷新比賽行程失敗: {e}")
            return 0
