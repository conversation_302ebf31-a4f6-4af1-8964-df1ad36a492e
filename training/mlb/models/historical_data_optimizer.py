#!/usr/bin/env python3
"""
歷史數據整合優化器
基於2019-2025年數據的智能權重系統
"""

import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Tuple
import numpy as np
from sqlalchemy import and_, or_

from models.database import db, Game, PlayerStats, TeamStats, Player
from models.database import Prediction

logger = logging.getLogger(__name__)

class HistoricalDataOptimizer:
    """歷史數據優化器 - 充分利用2019-2025年數據"""
    
    def __init__(self):
        # 基於實際數據範圍的權重配置
        self.seasonal_weights = {
            2025: 1.0,    # 當前賽季 - 最高權重
            2024: 0.8,    # 上一完整賽季 - 高權重
            2023: 0.6,    # 兩年前 - 中高權重
            2022: 0.3,    # 部分數據年 - 低權重
            2021: 0.5,    # COVID後恢復年 - 中等權重
            2020: 0.2,    # COVID年 - 最低權重
            2019: 0.4,    # 基準年 - 中等權重
        }
        
        # COVID-19特殊調整因子
        self.covid_adjustment_factor = 0.7
        
        # 數據質量評分 (基於實際數據分析)
        self.season_quality_scores = {
            2025: 0.95,   # 當前賽季，高質量
            2024: 1.0,    # 完整賽季，最高質量
            2023: 0.9,    # 大量數據，高質量
            2022: 0.6,    # 部分數據，中等質量
            2021: 0.85,   # 完整但過渡期
            2020: 0.5,    # COVID影響，低質量
            2019: 0.9,    # 完整基準年
        }
    
    def get_weighted_historical_performance(self, 
                                          player_id: str, 
                                          target_date: date,
                                          stat_types: List[str] = None) -> Dict:
        """
        獲取球員的加權歷史表現
        
        Args:
            player_id: 球員ID
            target_date: 目標日期
            stat_types: 需要的統計類型列表
        
        Returns:
            加權後的歷史表現數據
        """
        
        logger.info(f"獲取球員 {player_id} 的歷史表現數據")
        
        # 1. 獲取球員的歷史統計數據
        historical_stats = self._get_player_historical_stats(player_id)
        
        if not historical_stats:
            logger.warning(f"球員 {player_id} 沒有歷史數據")
            return {}
        
        # 2. 計算每個賽季的權重
        weighted_stats = {}
        total_weight = 0
        
        for stat_record in historical_stats:
            season = self._extract_season_from_date(stat_record.season or target_date)
            
            # 計算基礎權重
            base_weight = self.seasonal_weights.get(season, 0.1)
            
            # COVID-19特殊處理
            if season == 2020:
                base_weight *= self.covid_adjustment_factor
                logger.debug(f"應用COVID調整: 2020年權重 {base_weight:.2f}")
            
            # 應用數據質量評分
            quality_score = self.season_quality_scores.get(season, 0.5)
            final_weight = base_weight * quality_score
            
            # 時間衰減調整
            time_decay = self._calculate_time_decay(season, target_date)
            adjusted_weight = final_weight * time_decay
            
            weighted_stats[season] = {
                'stats': stat_record,
                'base_weight': base_weight,
                'quality_score': quality_score,
                'time_decay': time_decay,
                'final_weight': adjusted_weight
            }
            
            total_weight += adjusted_weight
        
        # 3. 計算加權平均統計
        if total_weight == 0:
            logger.warning(f"球員 {player_id} 總權重為0")
            return {}
        
        aggregated_stats = self._calculate_weighted_aggregation(
            weighted_stats, total_weight, stat_types
        )
        
        # 4. 添加趨勢分析
        trend_analysis = self._analyze_performance_trends(weighted_stats)
        
        return {
            'player_id': player_id,
            'aggregated_stats': aggregated_stats,
            'trend_analysis': trend_analysis,
            'data_quality': {
                'seasons_used': len(weighted_stats),
                'total_weight': total_weight,
                'covid_adjusted': 2020 in weighted_stats,
                'weight_distribution': {
                    season: data['final_weight'] / total_weight 
                    for season, data in weighted_stats.items()
                }
            },
            'generated_at': datetime.now()
        }
    
    def _get_player_historical_stats(self, player_id: str) -> List:
        """獲取球員歷史統計數據"""
        
        try:
            with db.session() as session:
                stats = session.query(PlayerStats).filter(
                    PlayerStats.player_id == player_id
                ).order_by(PlayerStats.season.desc()).all()
                
                logger.info(f"找到球員 {player_id} 的 {len(stats)} 筆歷史記錄")
                return stats
                
        except Exception as e:
            logger.error(f"獲取球員 {player_id} 歷史數據失敗: {e}")
            return []
    
    def _extract_season_from_date(self, target_date: date) -> int:
        """從日期提取MLB賽季年份"""
        
        if isinstance(target_date, int):
            return target_date
            
        # MLB賽季通常從3月開始
        if target_date.month >= 3:
            return target_date.year
        else:
            return target_date.year - 1
    
    def _calculate_time_decay(self, season: int, target_date: date) -> float:
        """計算時間衰減因子"""
        
        target_season = self._extract_season_from_date(target_date)
        years_diff = abs(target_season - season)
        
        # 指數衰減，但不會完全歸零
        decay_rate = 0.15  # 每年衰減15%
        time_decay = max(0.1, 1.0 - (years_diff * decay_rate))
        
        return time_decay
    
    def _calculate_weighted_aggregation(self, 
                                      weighted_stats: Dict, 
                                      total_weight: float,
                                      stat_types: List[str] = None) -> Dict:
        """計算加權平均統計"""
        
        if not stat_types:
            stat_types = ['batting_avg', 'on_base_percentage', 'slugging_percentage', 
                         'era', 'whip', 'strikeouts_per_9']
        
        aggregated = {}
        
        for stat_type in stat_types:
            weighted_sum = 0
            valid_weight = 0
            
            for season_data in weighted_stats.values():
                stats = season_data['stats']
                weight = season_data['final_weight']
                
                stat_value = getattr(stats, stat_type, None)
                if stat_value is not None and stat_value > 0:
                    weighted_sum += stat_value * weight
                    valid_weight += weight
            
            if valid_weight > 0:
                aggregated[stat_type] = weighted_sum / valid_weight
            else:
                aggregated[stat_type] = None
        
        return aggregated
    
    def _analyze_performance_trends(self, weighted_stats: Dict) -> Dict:
        """分析表現趨勢"""
        
        if len(weighted_stats) < 2:
            return {'trend': 'insufficient_data'}
        
        # 按賽季排序
        sorted_seasons = sorted(weighted_stats.keys(), reverse=True)
        recent_seasons = sorted_seasons[:3]  # 最近3個賽季
        
        trends = {}
        
        # 分析打擊率趨勢 (如果有數據)
        batting_avgs = []
        for season in recent_seasons:
            stats = weighted_stats[season]['stats']
            if hasattr(stats, 'batting_avg') and stats.batting_avg:
                batting_avgs.append((season, stats.batting_avg))
        
        if len(batting_avgs) >= 2:
            # 簡單線性趨勢
            values = [avg for _, avg in batting_avgs]
            if values[-1] > values[0]:
                trends['batting_trend'] = 'improving'
            elif values[-1] < values[0]:
                trends['batting_trend'] = 'declining'
            else:
                trends['batting_trend'] = 'stable'
        
        # 添加整體趨勢評估
        trends['overall_trend'] = self._assess_overall_trend(weighted_stats)
        trends['career_phase'] = self._determine_career_phase(weighted_stats)
        
        return trends
    
    def _assess_overall_trend(self, weighted_stats: Dict) -> str:
        """評估整體趨勢"""
        
        seasons = sorted(weighted_stats.keys(), reverse=True)
        if len(seasons) < 3:
            return 'unknown'
        
        recent_3_seasons = seasons[:3]
        weights = [weighted_stats[season]['final_weight'] for season in recent_3_seasons]
        
        # 基於權重分布判斷趨勢
        if weights[0] > sum(weights[1:]):
            return 'peak_current'
        elif weights[1] > weights[0] and weights[1] > weights[2]:
            return 'recent_peak'
        else:
            return 'stable'
    
    def _determine_career_phase(self, weighted_stats: Dict) -> str:
        """判斷生涯階段"""
        
        total_seasons = len(weighted_stats)
        seasons = sorted(weighted_stats.keys())
        career_span = max(seasons) - min(seasons) + 1
        
        if total_seasons <= 2:
            return 'rookie'
        elif total_seasons <= 4:
            return 'developing'
        elif career_span >= 8:
            return 'veteran'
        else:
            return 'prime'

class COVID19SeasonHandler:
    """COVID-19賽季特殊處理器"""
    
    def __init__(self):
        self.covid_season = 2020
        self.affected_seasons = [2020, 2021]  # 2021也受到一定影響
        
        # 特殊調整因子
        self.adjustments = {
            2020: {
                'weight_factor': 0.7,      # 總體權重降低
                'sample_size_factor': 0.6, # 樣本大小調整
                'variance_factor': 1.3     # 方差增加
            },
            2021: {
                'weight_factor': 0.9,      # 輕微降低權重
                'sample_size_factor': 0.9,
                'variance_factor': 1.1
            }
        }
    
    def apply_covid_adjustments(self, 
                               season: int, 
                               base_weight: float, 
                               stat_value: float,
                               stat_variance: float = None) -> Tuple[float, float]:
        """
        應用COVID相關調整
        
        Returns:
            調整後的權重和統計值
        """
        
        if season not in self.affected_seasons:
            return base_weight, stat_value
        
        adjustments = self.adjustments[season]
        
        # 調整權重
        adjusted_weight = base_weight * adjustments['weight_factor']
        
        # 調整統計值的可信度 (通過方差調整)
        if stat_variance is not None:
            adjusted_variance = stat_variance * adjustments['variance_factor']
        
        logger.debug(f"COVID調整 - 賽季:{season}, 原權重:{base_weight:.3f}, 調整後:{adjusted_weight:.3f}")
        
        return adjusted_weight, stat_value

class PitcherBatterHistoryBuilder:
    """投手對打者歷史資料庫建構器"""
    
    def __init__(self, optimizer: HistoricalDataOptimizer):
        self.optimizer = optimizer
        self.covid_handler = COVID19SeasonHandler()
    
    def build_comprehensive_matchup_database(self) -> Dict:
        """建立完整的投手vs打者對戰資料庫"""
        
        logger.info("開始建構投手vs打者對戰資料庫 (2019-2025)")
        
        try:
            # 1. 獲取所有投手和打者的歷史數據
            pitchers = self._get_all_pitchers()
            batters = self._get_all_batters()
            
            logger.info(f"找到 {len(pitchers)} 名投手, {len(batters)} 名打者")
            
            # 2. 建立對戰矩陣
            matchup_database = {}
            total_combinations = len(pitchers) * len(batters)
            processed = 0
            
            for pitcher in pitchers:
                pitcher_matchups = {}
                
                for batter in batters:
                    # 獲取歷史對戰數據
                    matchup_history = self._get_historical_matchups(
                        pitcher['player_id'], batter['player_id']
                    )
                    
                    if matchup_history:
                        # 應用權重和COVID調整
                        weighted_history = self._apply_matchup_weights(matchup_history)
                        pitcher_matchups[batter['player_id']] = weighted_history
                    
                    processed += 1
                    if processed % 1000 == 0:
                        logger.info(f"處理進度: {processed}/{total_combinations}")
                
                matchup_database[pitcher['player_id']] = pitcher_matchups
            
            # 3. 生成統計報告
            stats_report = self._generate_database_stats(matchup_database)
            
            logger.info("投手vs打者資料庫建構完成")
            return {
                'database': matchup_database,
                'stats': stats_report,
                'build_time': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"建構對戰資料庫失敗: {e}")
            return {}
    
    def _get_all_pitchers(self) -> List[Dict]:
        """獲取所有投手列表"""
        
        try:
            with db.session() as session:
                pitchers = session.query(PlayerStats).filter(
                    and_(
                        PlayerStats.games_pitched > 0,
                        PlayerStats.season >= 2019
                    )
                ).distinct(PlayerStats.player_id).all()
                
                return [{'player_id': p.player_id, 'name': p.full_name} for p in pitchers]
                
        except Exception as e:
            logger.error(f"獲取投手列表失敗: {e}")
            return []
    
    def _get_all_batters(self) -> List[Dict]:
        """獲取所有打者列表"""
        
        try:
            with db.session() as session:
                batters = session.query(PlayerStats).filter(
                    and_(
                        PlayerStats.at_bats > 0,
                        PlayerStats.season >= 2019
                    )
                ).distinct(PlayerStats.player_id).all()
                
                return [{'player_id': b.player_id, 'name': b.full_name} for b in batters]
                
        except Exception as e:
            logger.error(f"獲取打者列表失敗: {e}")
            return []
    
    def _get_historical_matchups(self, pitcher_id: str, batter_id: str) -> List[Dict]:
        """獲取特定投手vs打者的歷史對戰"""
        
        # 這裡需要根據實際的對戰數據表結構來實現
        # 由於當前數據庫可能還沒有詳細的對戰記錄，我們先返回一個框架
        
        try:
            # 基於比賽數據推算對戰
            matchups = []
            
            # TODO: 實現實際的對戰數據獲取邏輯
            # 可能需要分析box score或play-by-play數據
            
            return matchups
            
        except Exception as e:
            logger.error(f"獲取對戰數據失敗 {pitcher_id} vs {batter_id}: {e}")
            return []
    
    def _apply_matchup_weights(self, matchup_history: List[Dict]) -> Dict:
        """對對戰歷史應用權重"""
        
        if not matchup_history:
            return {}
        
        weighted_matchups = []
        
        for matchup in matchup_history:
            season = matchup.get('season')
            base_weight = self.optimizer.seasonal_weights.get(season, 0.1)
            
            # 應用COVID調整
            adjusted_weight, _ = self.covid_handler.apply_covid_adjustments(
                season, base_weight, 1.0
            )
            
            matchup['weight'] = adjusted_weight
            weighted_matchups.append(matchup)
        
        # 計算加權統計
        return self._calculate_weighted_matchup_stats(weighted_matchups)
    
    def _calculate_weighted_matchup_stats(self, weighted_matchups: List[Dict]) -> Dict:
        """計算加權對戰統計"""
        
        total_weight = sum(m['weight'] for m in weighted_matchups)
        
        if total_weight == 0:
            return {}
        
        # 計算各項加權平均
        weighted_stats = {
            'total_at_bats': sum(m.get('at_bats', 0) * m['weight'] for m in weighted_matchups),
            'total_hits': sum(m.get('hits', 0) * m['weight'] for m in weighted_matchups),
            'home_runs': sum(m.get('home_runs', 0) * m['weight'] for m in weighted_matchups),
            'strikeouts': sum(m.get('strikeouts', 0) * m['weight'] for m in weighted_matchups),
            'total_weight': total_weight,
            'sample_seasons': len(weighted_matchups)
        }
        
        # 計算衍生統計
        if weighted_stats['total_at_bats'] > 0:
            weighted_stats['batting_avg'] = weighted_stats['total_hits'] / weighted_stats['total_at_bats']
            weighted_stats['home_run_rate'] = weighted_stats['home_runs'] / weighted_stats['total_at_bats']
            weighted_stats['strikeout_rate'] = weighted_stats['strikeouts'] / weighted_stats['total_at_bats']
        
        return weighted_stats
    
    def _generate_database_stats(self, database: Dict) -> Dict:
        """生成資料庫統計報告"""
        
        total_pitchers = len(database)
        total_matchups = sum(len(matchups) for matchups in database.values())
        
        return {
            'total_pitchers': total_pitchers,
            'total_matchups': total_matchups,
            'avg_matchups_per_pitcher': total_matchups / total_pitchers if total_pitchers > 0 else 0,
            'seasons_covered': list(range(2019, 2026)),
            'covid_adjustments_applied': True
        }

# 使用示例和測試
if __name__ == "__main__":
    # 初始化優化器
    optimizer = HistoricalDataOptimizer()
    
    # 測試歷史數據獲取
    sample_player_id = "12345"  # 替換為實際的player_id
    target_date = date(2025, 8, 27)
    
    historical_performance = optimizer.get_weighted_historical_performance(
        sample_player_id, target_date
    )
    
    print("歷史數據優化結果:", historical_performance)
    
    # 建立投手對戰資料庫
    matchup_builder = PitcherBatterHistoryBuilder(optimizer)
    matchup_database = matchup_builder.build_comprehensive_matchup_database()
    
    print("對戰資料庫統計:", matchup_database.get('stats', {}))