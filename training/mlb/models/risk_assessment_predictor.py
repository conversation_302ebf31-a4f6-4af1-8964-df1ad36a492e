#!/usr/bin/env python3
"""
風險評估預測器 - 識別極端比賽並調整信心度
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime, date
import logging

# 添加項目路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from models.database import db, Game, Prediction, BettingOdds

logger = logging.getLogger(__name__)

class RiskAssessmentPredictor:
    """風險評估預測器"""
    
    def __init__(self):
        self.risk_rules = self.load_risk_rules()
        self.team_profiles = self.load_team_profiles()
        
    def load_risk_rules(self):
        """載入風險評估規則"""
        try:
            rules_path = os.path.join(os.path.dirname(__file__), '..', 'models', 'risk_assessment_rules.json')
            if os.path.exists(rules_path):
                with open(rules_path, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"載入風險規則失敗: {e}")
        
        # 默認風險規則
        return {
            'extreme_score_threshold': {'low': 5, 'high': 20},
            'blowout_threshold': 10,
            'prediction_error_thresholds': {'medium': 5, 'high': 8},
            'betting_line_deviation_threshold': 5,
            'high_risk_score_threshold': 5
        }
    
    def load_team_profiles(self):
        """載入隊伍特性檔案"""
        # 基於分析結果的隊伍特性
        return {
            # 最穩定的隊伍 (低變異係數)
            'stable_teams': ['AZ', 'LAA', 'TOR', 'NYY', 'NYM'],
            
            # 最不穩定的隊伍 (高變異係數)
            'unstable_teams': ['PIT', 'BAL', 'TEX', 'WSH', 'CIN'],
            
            # 低分比賽常見隊伍
            'low_scoring_teams': ['CWS', 'DET', 'SD', 'SF', 'MIA'],
            
            # 高分比賽常見隊伍
            'high_scoring_teams': ['BOS', 'WSH', 'MIA', 'CHC', 'ATH'],
            
            # 主場優勢大的隊伍
            'strong_home_teams': ['BOS', 'BAL', 'PIT', 'ATH', 'MIN'],
            
            # 主場劣勢的隊伍
            'weak_home_teams': ['TEX', 'CLE', 'MIA', 'WSH', 'SEA'],
            
            # 主場穩定的隊伍
            'stable_home_teams': ['SEA', 'CWS', 'CLE', 'SF', 'COL'],
            
            # 客場穩定的隊伍
            'stable_away_teams': ['PIT', 'CWS', 'BAL', 'COL', 'SD']
        }
    
    def assess_game_risk(self, game_data, prediction_data):
        """評估單場比賽的風險"""
        risk_score = 0
        risk_factors = []
        confidence_adjustment = 0
        
        home_team = game_data.get('home_team')
        away_team = game_data.get('away_team')
        predicted_total = prediction_data.get('predicted_total', 0)
        betting_line = game_data.get('betting_line')
        
        # 1. 隊伍穩定性風險
        if home_team in self.team_profiles['unstable_teams']:
            risk_score += 1
            risk_factors.append(f"{home_team}主場不穩定")
            confidence_adjustment -= 0.1
            
        if away_team in self.team_profiles['unstable_teams']:
            risk_score += 1
            risk_factors.append(f"{away_team}客場不穩定")
            confidence_adjustment -= 0.1
        
        # 2. 極端得分風險
        if predicted_total < self.risk_rules['extreme_score_threshold']['low']:
            risk_score += 3
            risk_factors.append("預測極低分")
            confidence_adjustment -= 0.2
        elif predicted_total > self.risk_rules['extreme_score_threshold']['high']:
            risk_score += 2
            risk_factors.append("預測極高分")
            confidence_adjustment -= 0.15
        
        # 3. 低分比賽風險
        if (home_team in self.team_profiles['low_scoring_teams'] and 
            away_team in self.team_profiles['low_scoring_teams']):
            risk_score += 2
            risk_factors.append("雙方都是低分隊伍")
            confidence_adjustment -= 0.1
            
            # 如果預測高分但雙方都是低分隊伍，風險更高
            if predicted_total > 10:
                risk_score += 2
                risk_factors.append("低分隊伍預測高分")
                confidence_adjustment -= 0.2
        
        # 4. 高分比賽風險
        if (home_team in self.team_profiles['high_scoring_teams'] and 
            away_team in self.team_profiles['high_scoring_teams']):
            risk_score += 1
            risk_factors.append("雙方都是高分隊伍")
            
            # 如果預測低分但雙方都是高分隊伍，風險更高
            if predicted_total < 8:
                risk_score += 2
                risk_factors.append("高分隊伍預測低分")
                confidence_adjustment -= 0.15
        
        # 5. 主客場不匹配風險
        if home_team in self.team_profiles['weak_home_teams']:
            risk_score += 1
            risk_factors.append(f"{home_team}主場表現差")
            confidence_adjustment -= 0.05
        
        # 6. 博彩盤口偏差風險
        if betting_line:
            line_deviation = abs(predicted_total - betting_line)
            if line_deviation > self.risk_rules['betting_line_deviation_threshold']:
                risk_score += 3
                risk_factors.append(f"偏離盤口{line_deviation:.1f}分")
                confidence_adjustment -= 0.2
            elif line_deviation > 3:
                risk_score += 1
                risk_factors.append(f"偏離盤口{line_deviation:.1f}分")
                confidence_adjustment -= 0.1
        
        # 7. 特殊組合風險
        # 穩定隊伍 vs 不穩定隊伍
        if ((home_team in self.team_profiles['stable_teams'] and 
             away_team in self.team_profiles['unstable_teams']) or
            (home_team in self.team_profiles['unstable_teams'] and 
             away_team in self.team_profiles['stable_teams'])):
            risk_score += 1
            risk_factors.append("穩定性差異大")
            confidence_adjustment -= 0.05
        
        return {
            'risk_score': risk_score,
            'risk_level': self.get_risk_level(risk_score),
            'risk_factors': risk_factors,
            'confidence_adjustment': max(-0.4, confidence_adjustment),  # 最多降低40%信心度
            'should_exclude': risk_score >= 8  # 超高風險比賽建議排除
        }
    
    def get_risk_level(self, risk_score):
        """根據風險分數獲取風險等級"""
        if risk_score >= 8:
            return "極高風險"
        elif risk_score >= 5:
            return "高風險"
        elif risk_score >= 3:
            return "中風險"
        elif risk_score >= 1:
            return "低風險"
        else:
            return "正常"
    
    def adjust_prediction_confidence(self, prediction_data, risk_assessment):
        """根據風險評估調整預測信心度"""
        original_confidence = prediction_data.get('confidence', 0.5)
        adjustment = risk_assessment['confidence_adjustment']
        
        # 應用調整
        adjusted_confidence = original_confidence + adjustment
        adjusted_confidence = max(0.1, min(0.9, adjusted_confidence))  # 限制在0.1-0.9之間
        
        return {
            'original_confidence': original_confidence,
            'adjusted_confidence': adjusted_confidence,
            'adjustment': adjustment,
            'risk_level': risk_assessment['risk_level']
        }
    
    def should_exclude_prediction(self, risk_assessment):
        """判斷是否應該排除此預測"""
        return risk_assessment['should_exclude']
    
    def get_prediction_recommendation(self, game_data, prediction_data):
        """獲取預測建議"""
        risk_assessment = self.assess_game_risk(game_data, prediction_data)
        confidence_info = self.adjust_prediction_confidence(prediction_data, risk_assessment)
        
        recommendation = {
            'risk_assessment': risk_assessment,
            'confidence_adjustment': confidence_info,
            'should_exclude': self.should_exclude_prediction(risk_assessment),
            'betting_recommendation': self.get_betting_recommendation(risk_assessment, prediction_data)
        }
        
        return recommendation
    
    def get_betting_recommendation(self, risk_assessment, prediction_data):
        """獲取投注建議"""
        risk_level = risk_assessment['risk_level']
        
        if risk_level in ["極高風險", "高風險"]:
            return {
                'action': '避免投注',
                'reason': f'風險等級: {risk_level}',
                'confidence': '極低'
            }
        elif risk_level == "中風險":
            return {
                'action': '謹慎投注',
                'reason': '中等風險，建議小額投注',
                'confidence': '低'
            }
        else:
            return {
                'action': '可以投注',
                'reason': '風險可控',
                'confidence': '正常'
            }
    
    def batch_assess_games(self, games_data):
        """批量評估比賽風險"""
        results = []
        
        for game_data, prediction_data in games_data:
            recommendation = self.get_prediction_recommendation(game_data, prediction_data)
            
            result = {
                'game_id': game_data.get('game_id'),
                'teams': f"{game_data.get('away_team')}@{game_data.get('home_team')}",
                'predicted_total': prediction_data.get('predicted_total'),
                'original_confidence': prediction_data.get('confidence'),
                'adjusted_confidence': recommendation['confidence_adjustment']['adjusted_confidence'],
                'risk_level': recommendation['risk_assessment']['risk_level'],
                'risk_factors': recommendation['risk_assessment']['risk_factors'],
                'should_exclude': recommendation['should_exclude'],
                'betting_action': recommendation['betting_recommendation']['action']
            }
            
            results.append(result)
        
        return results
    
    def generate_risk_report(self, assessment_results):
        """生成風險評估報告"""
        total_games = len(assessment_results)
        excluded_games = sum(1 for r in assessment_results if r['should_exclude'])
        high_risk_games = sum(1 for r in assessment_results if r['risk_level'] in ['高風險', '極高風險'])
        
        risk_distribution = {}
        for result in assessment_results:
            risk_level = result['risk_level']
            risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + 1
        
        report = {
            'total_games': total_games,
            'excluded_games': excluded_games,
            'exclusion_rate': excluded_games / total_games * 100 if total_games > 0 else 0,
            'high_risk_games': high_risk_games,
            'high_risk_rate': high_risk_games / total_games * 100 if total_games > 0 else 0,
            'risk_distribution': risk_distribution,
            'recommendations': {
                'safe_to_bet': total_games - high_risk_games,
                'avoid_betting': excluded_games,
                'cautious_betting': risk_distribution.get('中風險', 0)
            }
        }
        
        return report

# 全局實例
_risk_assessor = None

def get_risk_assessor():
    """獲取風險評估器實例"""
    global _risk_assessor
    if _risk_assessor is None:
        _risk_assessor = RiskAssessmentPredictor()
    return _risk_assessor
