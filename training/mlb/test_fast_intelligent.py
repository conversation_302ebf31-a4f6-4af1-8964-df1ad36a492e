#!/usr/bin/env python3
"""
測試快速智能預測系統
優先從數據庫獲取數據，避免網絡請求，提高預測速度
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
import time
from app import create_app
from models.fast_intelligent_predictor import fast_intelligent_predictor

def test_fast_intelligent_prediction():
    """測試快速智能預測"""
    app = create_app()
    
    with app.app_context():
        print("🚀 測試快速智能預測系統")
        print("=" * 80)
        print("✅ 優勢:")
        print("1. 優先從數據庫獲取數據，避免網絡請求")
        print("2. 使用緩存機制，提高重複查詢速度")
        print("3. 簡化預測流程，減少計算時間")
        print("4. 保持智能策略選擇功能")
        print()
        
        # 測試案例
        test_cases = [
            {
                'title': '🎯 CHC @ NYY - 測試BoxScore快速獲取',
                'home_team': 'NYY',
                'away_team': 'CHC',
                'game_date': date(2025, 7, 13),
                'game_id': '777122'
            },
            {
                'title': '🎯 TB @ BOS - 測試另一場比賽',
                'home_team': 'BOS',
                'away_team': 'TB',
                'game_date': date(2025, 7, 10),
                'game_id': '777170'
            },
            {
                'title': '🎯 無game_id測試 - 測試數據庫查詢',
                'home_team': 'LAD',
                'away_team': 'SF',
                'game_date': date(2025, 7, 12),
                'game_id': None
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{test_case['title']}")
            print("-" * 60)
            
            # 記錄開始時間
            start_time = time.time()
            
            try:
                # 執行快速智能預測
                result = fast_intelligent_predictor.predict_game_intelligent_fast(
                    home_team=test_case['home_team'],
                    away_team=test_case['away_team'],
                    game_date=test_case['game_date'],
                    game_id=test_case['game_id']
                )
                
                # 計算耗時
                elapsed_time = time.time() - start_time
                
                print(f"✅ 快速預測成功! (耗時: {elapsed_time:.3f}秒)")
                print(f"📊 投手分析:")
                print(f"   主隊投手: {result['pitcher_analysis']['home_pitcher']['name']} "
                      f"(ERA: {result['pitcher_analysis']['home_pitcher']['era']:.2f}, "
                      f"等級: {result['pitcher_analysis']['home_pitcher']['strength']})")
                print(f"   客隊投手: {result['pitcher_analysis']['away_pitcher']['name']} "
                      f"(ERA: {result['pitcher_analysis']['away_pitcher']['era']:.2f}, "
                      f"等級: {result['pitcher_analysis']['away_pitcher']['strength']})")
                print(f"   對戰類型: {result['pitcher_analysis']['matchup_type']}")
                
                print(f"\n🧠 智能策略: {result['strategy']['name']}")
                print(f"   策略說明: {result['strategy']['description']}")
                
                print(f"\n📈 預測結果:")
                print(f"   預測比分: {result['predicted_away_score']:.1f} - {result['predicted_home_score']:.1f}")
                print(f"   預測總分: {result['total_runs']:.1f}分")
                print(f"   信心度: {result['confidence']:.1%}")
                print(f"   系統耗時: {result.get('prediction_time', elapsed_time):.3f}秒")
                
                # 性能分析
                if elapsed_time < 0.5:
                    print(f"   🚀 性能優秀: 預測速度非常快")
                elif elapsed_time < 1.0:
                    print(f"   ⚡ 性能良好: 預測速度較快")
                elif elapsed_time < 2.0:
                    print(f"   ⚠️  性能一般: 預測速度中等")
                else:
                    print(f"   🐌 性能較慢: 需要優化")
                
            except Exception as e:
                elapsed_time = time.time() - start_time
                print(f"❌ 快速預測失敗: {e} (耗時: {elapsed_time:.3f}秒)")
                import traceback
                traceback.print_exc()

def test_batch_fast_prediction():
    """測試批量快速預測"""
    print(f"\n\n🔄 測試批量快速預測")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        from models.database import Game
        
        # 獲取最近的比賽
        recent_games = Game.query.filter(
            Game.game_status == 'completed'
        ).order_by(Game.date.desc()).limit(5).all()
        
        if not recent_games:
            print("❌ 找不到最近的比賽")
            return
        
        print(f"📊 找到 {len(recent_games)} 場最近的比賽")
        
        # 記錄開始時間
        start_time = time.time()
        
        successful_predictions = 0
        failed_predictions = 0
        
        for game in recent_games:
            try:
                # 執行快速智能預測
                result = fast_intelligent_predictor.predict_game_intelligent_fast(
                    home_team=game.home_team,
                    away_team=game.away_team,
                    game_date=game.date,
                    game_id=game.game_id
                )
                
                print(f"✅ {game.away_team} @ {game.home_team} ({game.date}) - "
                      f"預測: {result['predicted_away_score']:.1f}-{result['predicted_home_score']:.1f} "
                      f"(策略: {result['strategy']['name']}, "
                      f"耗時: {result.get('prediction_time', 0):.3f}秒)")
                
                successful_predictions += 1
                
            except Exception as e:
                print(f"❌ {game.away_team} @ {game.home_team} ({game.date}) - 預測失敗: {e}")
                failed_predictions += 1
        
        # 計算總耗時
        total_elapsed_time = time.time() - start_time
        
        print(f"\n📈 批量預測統計:")
        print(f"   成功預測: {successful_predictions} 場")
        print(f"   失敗預測: {failed_predictions} 場")
        print(f"   總耗時: {total_elapsed_time:.3f}秒")
        print(f"   平均耗時: {total_elapsed_time / len(recent_games):.3f}秒/場")
        
        # 性能評估
        avg_time = total_elapsed_time / len(recent_games)
        if avg_time < 0.5:
            print(f"   🚀 批量性能優秀: 平均預測速度非常快")
        elif avg_time < 1.0:
            print(f"   ⚡ 批量性能良好: 平均預測速度較快")
        else:
            print(f"   ⚠️  批量性能需要優化: 平均預測速度較慢")

def test_cache_effectiveness():
    """測試緩存效果"""
    print(f"\n\n💾 測試緩存效果")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        # 測試同一場比賽的重複預測
        test_game = {
            'home_team': 'NYY',
            'away_team': 'CHC',
            'game_date': date(2025, 7, 13),
            'game_id': '777122'
        }
        
        print(f"🎯 測試比賽: {test_game['away_team']} @ {test_game['home_team']}")
        
        # 第一次預測（無緩存）
        print(f"\n1️⃣ 第一次預測（無緩存）:")
        start_time = time.time()
        try:
            result1 = fast_intelligent_predictor.predict_game_intelligent_fast(**test_game)
            time1 = time.time() - start_time
            print(f"   ✅ 成功 - 耗時: {time1:.3f}秒")
        except Exception as e:
            time1 = time.time() - start_time
            print(f"   ❌ 失敗: {e} - 耗時: {time1:.3f}秒")
            return
        
        # 第二次預測（有緩存）
        print(f"\n2️⃣ 第二次預測（有緩存）:")
        start_time = time.time()
        try:
            result2 = fast_intelligent_predictor.predict_game_intelligent_fast(**test_game)
            time2 = time.time() - start_time
            print(f"   ✅ 成功 - 耗時: {time2:.3f}秒")
        except Exception as e:
            time2 = time.time() - start_time
            print(f"   ❌ 失敗: {e} - 耗時: {time2:.3f}秒")
            return
        
        # 第三次預測（有緩存）
        print(f"\n3️⃣ 第三次預測（有緩存）:")
        start_time = time.time()
        try:
            result3 = fast_intelligent_predictor.predict_game_intelligent_fast(**test_game)
            time3 = time.time() - start_time
            print(f"   ✅ 成功 - 耗時: {time3:.3f}秒")
        except Exception as e:
            time3 = time.time() - start_time
            print(f"   ❌ 失敗: {e} - 耗時: {time3:.3f}秒")
            return
        
        # 緩存效果分析
        print(f"\n📊 緩存效果分析:")
        print(f"   第一次耗時: {time1:.3f}秒")
        print(f"   第二次耗時: {time2:.3f}秒")
        print(f"   第三次耗時: {time3:.3f}秒")
        
        if time2 < time1 * 0.8:
            print(f"   🚀 緩存效果顯著: 第二次預測速度提升 {((time1 - time2) / time1 * 100):.1f}%")
        elif time2 < time1 * 0.9:
            print(f"   ⚡ 緩存效果良好: 第二次預測速度提升 {((time1 - time2) / time1 * 100):.1f}%")
        else:
            print(f"   ⚠️  緩存效果不明顯: 可能需要優化緩存策略")
        
        if time3 < time2:
            print(f"   ✅ 緩存持續有效: 第三次預測更快")
        else:
            print(f"   ⚠️  緩存可能失效: 第三次預測沒有更快")

def test_comparison_with_original():
    """與原始智能預測系統比較"""
    print(f"\n\n⚖️  與原始智能預測系統比較")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        from models.simple_intelligent_predictor import SimpleIntelligentPredictor
        
        original_predictor = SimpleIntelligentPredictor()
        
        test_game = {
            'home_team': 'NYY',
            'away_team': 'CHC',
            'game_date': date(2025, 7, 13),
            'game_id': '777122'
        }
        
        print(f"🎯 測試比賽: {test_game['away_team']} @ {test_game['home_team']}")
        
        # 測試原始系統
        print(f"\n🐌 原始智能預測系統:")
        start_time = time.time()
        try:
            original_result = original_predictor.predict_game_intelligent(**test_game)
            original_time = time.time() - start_time
            print(f"   ✅ 成功 - 耗時: {original_time:.3f}秒")
            print(f"   預測: {original_result['predicted_away_score']:.1f}-{original_result['predicted_home_score']:.1f}")
            print(f"   策略: {original_result['strategy']['name']}")
        except Exception as e:
            original_time = time.time() - start_time
            print(f"   ❌ 失敗: {e} - 耗時: {original_time:.3f}秒")
            original_result = None
        
        # 測試快速系統
        print(f"\n🚀 快速智能預測系統:")
        start_time = time.time()
        try:
            fast_result = fast_intelligent_predictor.predict_game_intelligent_fast(**test_game)
            fast_time = time.time() - start_time
            print(f"   ✅ 成功 - 耗時: {fast_time:.3f}秒")
            print(f"   預測: {fast_result['predicted_away_score']:.1f}-{fast_result['predicted_home_score']:.1f}")
            print(f"   策略: {fast_result['strategy']['name']}")
        except Exception as e:
            fast_time = time.time() - start_time
            print(f"   ❌ 失敗: {e} - 耗時: {fast_time:.3f}秒")
            fast_result = None
        
        # 比較結果
        if original_result and fast_result:
            print(f"\n📊 性能比較:")
            print(f"   原始系統耗時: {original_time:.3f}秒")
            print(f"   快速系統耗時: {fast_time:.3f}秒")
            
            if fast_time < original_time:
                speedup = (original_time - fast_time) / original_time * 100
                print(f"   🚀 速度提升: {speedup:.1f}% (快了 {original_time / fast_time:.1f}倍)")
            else:
                slowdown = (fast_time - original_time) / original_time * 100
                print(f"   🐌 速度下降: {slowdown:.1f}%")
            
            print(f"\n📈 預測結果比較:")
            print(f"   原始系統: {original_result['predicted_away_score']:.1f}-{original_result['predicted_home_score']:.1f} (總分: {original_result['total_runs']:.1f})")
            print(f"   快速系統: {fast_result['predicted_away_score']:.1f}-{fast_result['predicted_home_score']:.1f} (總分: {fast_result['total_runs']:.1f})")
            
            # 檢查預測一致性
            score_diff = abs(original_result['total_runs'] - fast_result['total_runs'])
            if score_diff < 1.0:
                print(f"   ✅ 預測結果一致: 總分差異 {score_diff:.1f}分")
            elif score_diff < 2.0:
                print(f"   ⚠️  預測結果略有差異: 總分差異 {score_diff:.1f}分")
            else:
                print(f"   ❌ 預測結果差異較大: 總分差異 {score_diff:.1f}分")

if __name__ == "__main__":
    test_fast_intelligent_prediction()
    test_batch_fast_prediction()
    test_cache_effectiveness()
    test_comparison_with_original()
