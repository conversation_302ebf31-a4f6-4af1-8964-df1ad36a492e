# MLB 預測分析系統

## 🎯 系統概述

這是一個完整的 MLB（美國職業棒球大聯盟）比賽預測分析系統，整合了數據獲取、機器學習預測、博彩分析和 Web 界面的全功能平台。系統提供準確的比賽結果預測、投手分析、博彩賠率追蹤和全面的數據管理功能。

## ✨ 主要功能

### 🤖 智能預測系統
- **機器學習預測**: 使用 XGBoost、Random Forest 等多種模型
- **投手對戰分析**: 詳細的投手統計和對戰記錄分析
- **博彩預測**: 勝負盤、讓分盤、大小分預測
- **信心度評估**: 基於數據質量和模型性能的預測信心度
- **多版本模型**: 支援不同版本的預測模型選擇

### 📊 數據管理
- **自動數據更新**: 每日自動獲取比賽數據和統計資料
- **多 API 整合**: 整合 MLB Stats API、ESPN API 等多個數據源
- **博彩賠率收集**: 從 SportsBookReview、Covers.com 等網站獲取真實賠率
- **Box Score 詳細數據**: 完整的比賽詳細統計和球員表現數據
- **歷史數據管理**: 支援多年歷史數據的下載和管理

### 🎛️ 管理功能
- **管理員儀表板**: 完整的系統監控和控制面板
- **數據狀態監控**: 實時監控數據完整性和系統健康狀況
- **手動操作**: 支援手動生成預測、重新訓練模型、更新數據
- **調度器管理**: 自動化任務的啟動、停止和監控
- **性能分析**: 預測準確率統計和模型性能追蹤

### 🌐 Web 界面
- **響應式設計**: 支援桌面和移動設備的現代化界面
- **比賽預覽**: 詳細的比賽分析和預測展示
- **球員統計**: 完整的球員和球隊統計資料
- **歷史記錄**: 預測歷史和準確率追蹤
- **即時更新**: 實時比賽狀態和結果更新

## 🏗️ 系統架構

```
mlb/
├── app.py                      # Flask 主應用程式
├── config.py                   # 系統配置
├── models/                     # 核心模型和服務
│   ├── database.py            # 數據庫模型定義
│   ├── ml_predictor.py        # 機器學習預測器
│   ├── automated_predictor.py # 自動化預測服務
│   ├── data_fetcher.py        # 數據獲取器
│   ├── detailed_data_fetcher.py # 詳細數據獲取
│   ├── prediction_service.py  # 預測服務
│   ├── reliable_odds_fetcher.py # 多 API 賠率獲取
│   └── ...
├── views/                      # Web 視圖控制器
│   ├── admin.py               # 管理員功能
│   ├── predictions.py         # 預測頁面
│   ├── games.py               # 比賽頁面
│   └── ...
├── templates/                  # HTML 模板
├── static/                     # 靜態資源
└── instance/                   # 數據庫文件
```

## 🚀 快速開始

### 1. 環境準備
```bash
# 進入項目目錄
cd training/mlb

# 激活虛擬環境
source .venv/bin/activate

# 安裝依賴（如需要）
pip install -r requirements.txt
```

### 2. 啟動系統
```bash
# 直接啟動 Flask 應用（數據庫已初始化）
python app.py
```

### 3. 訪問系統
- **主頁**: http://localhost:5500/
- **儀表板**: http://localhost:5500/dashboard/
- **預測頁面**: http://localhost:5500/predictions/
- **管理員面板**: http://localhost:5500/admin/
- **比賽數據**: http://localhost:5500/games/
- **球隊統計**: http://localhost:5500/teams/
- **智能預測**: http://localhost:5500/intelligent/
- **統一預測**: http://localhost:5500/unified/
- **快速預測**: http://localhost:5500/fast/
- **模擬系統**: http://localhost:5500/simulation/

### 4. 系統狀態
✅ **系統已就緒** - 包含完整的數據和訓練好的模型
- 📊 **15,202 場比賽數據** - 豐富的歷史數據
- 🏟️ **30 支 MLB 球隊** - 完整球隊資料
- 🎯 **564 筆預測記錄** - 已有預測歷史
- 🤖 **機器學習模型已訓練** - 可直接使用
- ⚡ **自動化調度器運行中** - 定時任務正常

## 📈 詳細功能說明

### 🎯 預測功能
#### 智能預測系統 (`/intelligent/`)
- **多模型預測**: XGBoost、Random Forest、Gradient Boosting
- **投手分析**: 詳細的投手統計和對戰記錄
- **打線分析**: 打擊率、上壘率、長打率分析
- **信心度評估**: 基於數據質量的預測可信度

#### 統一預測系統 (`/unified/`)
- **博彩預測**: 勝負盤、讓分盤、大小分預測
- **實時賠率**: 整合多家博彩公司賠率
- **歷史驗證**: 預測準確率追蹤和分析
- **風險評估**: 投注風險和回報分析

#### 快速預測 (`/fast/`)
- **即時預測**: 快速生成比賽預測
- **簡化界面**: 專注核心預測信息
- **批量處理**: 一次預測多場比賽

### 📊 數據分析
#### 比賽數據 (`/games/`)
- **比賽列表**: 完整的比賽行程和結果
- **詳細統計**: Box Score 和球員表現
- **歷史對戰**: 球隊交手記錄分析
- **比賽預覽**: 賽前分析和預測

#### 球隊統計 (`/teams/`)
- **球隊排名**: 勝率、得分、防守統計
- **球員名單**: 完整的球員資料和統計
- **主客場表現**: 主場優勢分析
- **賽季趨勢**: 表現變化和趨勢分析

#### 球員數據 (`/players/`)
- **球員統計**: 打擊、投球、守備統計
- **表現趨勢**: 近期表現和狀態分析
- **對戰記錄**: 投手對打者的歷史記錄
- **傷病狀況**: 球員健康狀態追蹤

### 🎛️ 管理功能 (`/admin/`)
#### 系統監控
- **系統狀態**: 調度器運行狀態、模型載入狀態
- **數據庫統計**: 比賽、球隊、預測記錄統計
- **模型性能**: 預測準確率和性能指標
- **API 狀態**: 各數據源的可用性檢查

#### 數據管理操作
- **每日數據更新** (`/data/daily-update`): 更新指定日期的比賽行程和 Box Score
- **全面數據更新** (`/data/comprehensive-update`): 批量更新過去7天到未來7天的數據
- **詳細數據獲取** (`/data/detailed`): 獲取指定日期的詳細比賽數據
- **行程刷新** (`/data/schedule-refresh`): 刷新比賽時間變更
- **Box Score 下載** (`/data/boxscore-today`): 下載當日已完成比賽的詳細數據

#### 預測管理操作
- **生成預測** (`/predictions/generate`): 手動為指定日期生成預測
- **模型重訓** (`/models/retrain`): 使用最新數據重新訓練機器學習模型
- **2025預測啟動器**: 新版預測系統的啟動和管理
- **投手公告檢查**: 檢查每日投手公告和預測時機

#### 調度器控制
- **啟動調度器** (`/scheduler/start`): 啟動自動化定時任務
- **停止調度器** (`/scheduler/stop`): 停止自動化定時任務
- **調度狀態監控**: 實時查看調度器運行狀態

#### 博彩數據管理
- **歷史賠率下載** (`/api/fetch_historical_odds`): 從多個網站下載歷史博彩賠率
- **賠率數據驗證**: 檢查賠率數據的一致性和完整性
- **多爬蟲支持**: 支持 SportsBookReview、Covers.com 等多個數據源

#### 全面數據功能
- **球隊數據獲取** (`/comprehensive-data/fetch-all`): 獲取所有球隊的全面數據
- **單隊數據更新** (`/comprehensive-data/fetch-team`): 更新指定球隊的數據
- **球員趨勢更新** (`/comprehensive-data/update-trends`): 更新球員表現趨勢
- **數據清理** (`/comprehensive-data/cleanup`): 清理過期的數據記錄

### 🔄 模擬系統 (`/simulation/`)
- **歷史回測**: 使用歷史數據驗證模型
- **策略測試**: 不同預測策略的效果比較
- **風險模擬**: 投注策略的風險評估
- **收益分析**: 長期投注收益預測

## 🔧 配置說明

### 自動化任務
- **06:00**: 每日數據更新
- **07:00**: 每日預測生成  
- **23:00**: 預測結果驗證
- **週日 02:00**: 模型重新訓練

### 預測特徵
- **球隊統計**: 勝率、得分、ERA、OPS 等
- **投手分析**: ERA、WHIP、對戰記錄
- **打線分析**: 打擊率、上壘率、長打率
- **對戰歷史**: 過去交手記錄和趨勢
- **主場優勢**: 主客場表現差異
- **近期狀態**: 最近表現和連勝/連敗

## 📊 數據來源

### 官方數據
- **MLB Stats API**: 官方比賽和統計數據
- **ESPN API**: 比賽狀態和基本統計
- **MLB.com**: Box Score 和詳細統計

### 博彩數據
- **SportsBookReview**: 歷史博彩賠率
- **Covers.com**: 即時賠率和分析
- **The Odds API**: 多家博彩公司賠率

## 🎯 預測類型

### 基本預測
- **勝負預測**: 比賽勝負結果
- **得分預測**: 雙方得分預測
- **信心度**: 預測可信度評估

### 博彩預測
- **讓分盤**: Run Line 預測
- **大小分**: Over/Under 總分預測
- **勝負盤**: Money Line 預測

## 💡 使用示例

### 查看今日比賽預測
1. 訪問 http://localhost:5500/predictions/
2. 查看當日所有比賽的預測結果
3. 點擊具體比賽查看詳細分析
4. 查看投手對戰和打線分析

### 使用管理員功能
1. 訪問 http://localhost:5500/admin/
2. 檢查系統狀態和數據完整性
3. 手動更新今日比賽數據：
   - 點擊「每日數據更新」
   - 選擇目標日期並執行
4. 生成預測：
   - 點擊「生成預測」
   - 選擇日期範圍
   - 查看預測結果

### 分析球隊表現
1. 訪問 http://localhost:5500/teams/
2. 選擇感興趣的球隊
3. 查看詳細統計和排名
4. 分析主客場表現差異
5. 查看球員名單和統計

### 博彩分析
1. 訪問 http://localhost:5500/unified/
2. 查看博彩預測和賠率
3. 分析讓分盤和大小分預測
4. 查看歷史準確率統計
5. 評估投注風險和回報

## 🛠️ 故障排除

### 常見問題
1. **系統啟動失敗**:
   - 確認虛擬環境已激活
   - 檢查 port 5500 是否被占用
   - 查看錯誤日誌確認問題

2. **數據獲取失敗**:
   - 檢查網絡連接
   - 驗證 API 密鑰有效性
   - 在管理員面板檢查 API 狀態

3. **預測生成失敗**:
   - 確認模型已訓練（系統已包含）
   - 檢查歷史數據完整性
   - 查看系統日誌確認錯誤

4. **調度器問題**:
   - 在管理員面板重啟調度器
   - 檢查定時任務設置
   - 查看調度器日誌

### 系統要求
✅ **當前系統已滿足所有要求**
- ✅ 15,202 場歷史比賽數據
- ✅ 完整的 30 支球隊數據
- ✅ 訓練好的機器學習模型
- ✅ 564 筆預測歷史記錄

## 📝 技術棧

- **後端**: Python, Flask, SQLAlchemy
- **機器學習**: scikit-learn, XGBoost, pandas
- **數據庫**: SQLite
- **前端**: Bootstrap, jQuery, Chart.js
- **調度**: APScheduler
- **數據獲取**: requests, BeautifulSoup

## 🔮 未來改進

- [ ] 深度學習模型整合
- [ ] 實時比賽狀態預測
- [ ] 移動端應用開發
- [ ] 更多博彩市場支援
- [ ] 社群功能和分享
- [ ] API 服務開放

---

**開發者**: Augment Agent  
**版本**: v2.0.0  
**最後更新**: 2025-07-16
