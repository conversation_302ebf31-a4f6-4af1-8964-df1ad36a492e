#!/usr/bin/env python3
"""
修復進度監控顯示問題
檢查並修復進度監控頁面沒有顯示已完成任務的問題
"""

import sys
sys.path.append('.')

import os
from datetime import datetime

def fix_progress_display():
    """修復進度顯示問題"""
    
    print("🔧 診斷進度監控顯示問題...")
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    try:
        from app import app
        with app.app_context():
            from progress_monitor import progress_monitor
            
            # 重新加載進度數據
            progress_monitor.load_progress()
            
            # 獲取所有任務
            all_tasks = progress_monitor.get_all_tasks()
            
            print(f"📊 當前任務狀態診斷:")
            print(f"   總任務數: {len(all_tasks)}")
            
            if not all_tasks:
                print("   ❌ 沒有找到任何任務記錄")
                print("   💡 這可能是進度文件未正確保存的原因")
                return False
            
            # 分析每個任務
            running_count = 0
            completed_count = 0
            failed_count = 0
            
            print(f"\\n📋 任務詳細分析:")
            for task_id, task in all_tasks.items():
                status = task.get('status', 'unknown')
                task_name = task.get('task_name', 'Unknown')
                progress = task.get('progress', 0)
                
                print(f"   📝 {task_name}")
                print(f"      └── 狀態: {status}, 進度: {progress:.1f}%")
                
                if status == 'running':
                    running_count += 1
                elif status == 'completed':
                    completed_count += 1
                elif status == 'failed':
                    failed_count += 1
            
            print(f"\\n📊 統計摘要:")
            print(f"   🔄 正在運行: {running_count}")
            print(f"   ✅ 已完成: {completed_count}")
            print(f"   ❌ 失敗: {failed_count}")
            
            # 測試API端點
            print(f"\\n🧪 測試進度監控API...")
            with app.test_client() as client:
                response = client.get('/admin/api/progress-status')
                print(f"   API響應狀態: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.get_json()
                    if data and data.get('success'):
                        api_total = len(data.get('all_tasks', {}))
                        api_running = data.get('running_count', 0)
                        print(f"   ✅ API返回數據正確")
                        print(f"   📊 API統計: 總數={api_total}, 運行中={api_running}")
                    else:
                        print(f"   ❌ API返回數據異常: {data}")
                        return False
                else:
                    print(f"   ❌ API請求失敗: {response.status_code}")
                    return False
            
            # 檢查進度監控文件
            progress_file = getattr(progress_monitor, 'progress_file', 'data/task_progress.json')
            print(f"\\n📁 檢查進度文件: {progress_file}")
            
            import os
            if os.path.exists(progress_file):
                file_size = os.path.getsize(progress_file)
                print(f"   ✅ 進度文件存在, 大小: {file_size} bytes")
                
                # 讀取文件內容
                try:
                    import json
                    with open(progress_file, 'r', encoding='utf-8') as f:
                        file_data = json.load(f)
                    print(f"   ✅ 文件內容有效, 包含 {len(file_data)} 個任務記錄")
                except Exception as e:
                    print(f"   ❌ 文件格式錯誤: {e}")
            else:
                print(f"   ❌ 進度文件不存在")
                return False
            
            # 診斷結果
            print(f"\\n🎯 診斷結果:")
            if completed_count > 0:
                print(f"   ✅ 系統正常 - 有 {completed_count} 個已完成任務")
                print(f"   💡 進度條應該顯示在「已完成的任務」區域")
                print(f"   🔄 請刷新進度監控頁面或點擊「刷新」按鈕")
                
                print(f"\\n🛠️ 如果仍然看不到:")
                print(f"   1. 清除瀏覽器緩存")
                print(f"   2. 按 Ctrl+F5 強制刷新頁面")
                print(f"   3. 點擊頁面上的「刷新」按鈕")
                print(f"   4. 啟用「自動刷新」功能")
                
                return True
            else:
                print(f"   ⚠️ 沒有已完成的任務記錄")
                print(f"   💡 建議運行一個新的數據下載任務來測試進度條")
                return False
                
    except Exception as e:
        print(f"❌ 診斷失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 進度監控顯示問題診斷工具")
    print("=" * 50)
    
    success = fix_progress_display()
    
    if success:
        print("\\n✅ 診斷完成 - 系統狀態正常")
        print("💡 如果您看不到進度條，請嘗試刷新頁面")
    else:
        print("\\n❌ 發現問題需要修復")
        print("💡 建議運行演示任務: python demo_progress_task.py")