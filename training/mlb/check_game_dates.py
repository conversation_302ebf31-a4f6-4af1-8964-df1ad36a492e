#!/usr/bin/env python3
"""
檢查比賽日期範圍，了解可以抓取賠率的日期
"""

import sys
import os
from datetime import date, datetime, timedelta

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game
from sqlalchemy import func, distinct

def check_game_dates():
    """檢查比賽日期範圍"""
    print("🗓️  檢查比賽日期範圍")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 基本統計
            total_games = Game.query.count()
            print(f"📊 總比賽數: {total_games:,}")
            
            if total_games == 0:
                print("❌ 沒有比賽記錄")
                return
            
            # 2. 日期範圍
            earliest_game = Game.query.order_by(Game.date.asc()).first()
            latest_game = Game.query.order_by(Game.date.desc()).first()
            
            print(f"\n📅 日期範圍:")
            print(f"   最早比賽: {earliest_game.date}")
            print(f"   最晚比賽: {latest_game.date}")
            
            # 計算天數差
            date_diff = (latest_game.date - earliest_game.date).days
            print(f"   跨越天數: {date_diff} 天")
            
            # 3. 比賽狀態統計
            print(f"\n📈 比賽狀態統計:")
            status_stats = db.session.query(
                Game.game_status,
                func.count(Game.game_id).label('count')
            ).group_by(Game.game_status).all()
            
            for status, count in status_stats:
                print(f"   {status}: {count:,} 場")
            
            # 4. 最近30天的比賽
            thirty_days_ago = date.today() - timedelta(days=30)
            recent_games = Game.query.filter(Game.date >= thirty_days_ago).count()
            print(f"\n🕒 最近30天比賽: {recent_games:,} 場")
            
            # 5. 每日比賽統計 (最近15天)
            print(f"\n📊 最近15天每日比賽統計:")
            daily_stats = db.session.query(
                Game.date,
                func.count(Game.game_id).label('game_count'),
                Game.game_status
            ).filter(Game.date >= date.today() - timedelta(days=15))\
            .group_by(Game.date, Game.game_status)\
            .order_by(Game.date.desc()).all()
            
            # 組織每日數據
            daily_data = {}
            for stat in daily_stats:
                game_date = stat.date
                if game_date not in daily_data:
                    daily_data[game_date] = {}
                daily_data[game_date][stat.game_status] = stat.game_count
            
            for game_date in sorted(daily_data.keys(), reverse=True):
                statuses = daily_data[game_date]
                total_day = sum(statuses.values())
                status_str = ", ".join([f"{status}: {count}" for status, count in statuses.items()])
                print(f"   {game_date}: {total_day} 場 ({status_str})")
            
            # 6. 今天和未來的比賽
            today = date.today()
            today_games = Game.query.filter_by(date=today).count()
            future_games = Game.query.filter(Game.date > today).count()
            
            print(f"\n🎯 今天和未來:")
            print(f"   今天比賽: {today_games} 場")
            print(f"   未來比賽: {future_games} 場")
            
            # 7. 可以抓取賠率的日期建議
            print(f"\n💡 賠率抓取建議:")
            
            # 最近完成的比賽日期
            completed_games = Game.query.filter(
                Game.game_status == 'completed',
                Game.date >= date.today() - timedelta(days=7)
            ).order_by(Game.date.desc()).limit(5).all()
            
            if completed_games:
                print(f"   最近完成的比賽日期 (可抓取歷史賠率):")
                for game in completed_games:
                    same_date_count = Game.query.filter_by(
                        date=game.date, 
                        game_status='completed'
                    ).count()
                    print(f"     {game.date}: {same_date_count} 場已完成")
            
            # 今天和明天的比賽 (可抓取即時賠率)
            tomorrow = today + timedelta(days=1)
            today_scheduled = Game.query.filter(
                Game.date == today,
                Game.game_status.in_(['scheduled', 'in_progress'])
            ).count()
            tomorrow_scheduled = Game.query.filter_by(
                date=tomorrow,
                game_status='scheduled'
            ).count()
            
            if today_scheduled > 0:
                print(f"   今天預定比賽: {today_scheduled} 場 (可抓取即時賠率)")
            if tomorrow_scheduled > 0:
                print(f"   明天預定比賽: {tomorrow_scheduled} 場 (可抓取即時賠率)")
            
            # 8. 推薦抓取日期
            print(f"\n🎯 推薦抓取日期:")
            
            # 找出最近有比賽的日期
            recent_dates = db.session.query(distinct(Game.date))\
                .filter(Game.date >= date.today() - timedelta(days=10))\
                .order_by(Game.date.desc()).limit(5).all()
            
            for game_date in recent_dates:
                game_date = game_date[0]
                game_count = Game.query.filter_by(date=game_date).count()
                completed_count = Game.query.filter_by(
                    date=game_date, 
                    game_status='completed'
                ).count()
                
                if game_count > 0:
                    status = "✅ 推薦" if completed_count > 0 else "⏳ 進行中"
                    print(f"   {game_date}: {game_count} 場比賽 ({completed_count} 場已完成) {status}")
            
        except Exception as e:
            print(f"❌ 檢查失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    check_game_dates()
