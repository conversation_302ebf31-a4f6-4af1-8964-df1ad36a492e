#!/usr/bin/env python3
"""
測試增強版博彩盤口獲取器
驗證多 API 系統整合功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from models.real_betting_odds_fetcher import RealBettingOddsFetcher

def print_separator(title: str):
    """打印分隔線"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_game_odds(games: list, max_games: int = 3):
    """打印比賽賠率信息"""
    if not games:
        print("❌ 沒有找到比賽數據")
        return
    
    print(f"✅ 找到 {len(games)} 場比賽:")
    print("-" * 40)
    
    for i, game in enumerate(games[:max_games]):
        print(f"\n{i+1}. {game.get('away_team', 'N/A')} @ {game.get('home_team', 'N/A')}")
        print(f"   狀態: {game.get('status', 'N/A')}")
        print(f"   時間: {game.get('game_time', 'N/A')}")
        print(f"   場地: {game.get('venue', 'N/A')}")
        
        odds = game.get('odds', {})
        if odds:
            # 勝負盤
            moneyline = odds.get('moneyline', {})
            if moneyline:
                print(f"   勝負盤: 客隊 {moneyline.get('away', 'N/A')} / 主隊 {moneyline.get('home', 'N/A')}")
                print(f"           提供商: {moneyline.get('bookmaker', 'N/A')}")
            
            # 讓分盤
            run_line = odds.get('run_line', {})
            if run_line:
                print(f"   讓分盤: 主隊 {run_line.get('home_line', 'N/A')} ({run_line.get('home_odds', 'N/A')})")
                print(f"           客隊 {run_line.get('away_line', 'N/A')} ({run_line.get('away_odds', 'N/A')})")
            
            # 大小分盤
            total = odds.get('total', {})
            if total:
                print(f"   大小分: {total.get('line', 'N/A')} 分")
                print(f"           大分 {total.get('over_odds', 'N/A')} / 小分 {total.get('under_odds', 'N/A')}")
        else:
            print("   📝 無賠率數據")
    
    if len(games) > max_games:
        print(f"\n... 還有 {len(games) - max_games} 場比賽")

def test_multi_api_status():
    """測試多 API 系統狀態"""
    print_separator("多 API 系統狀態檢查")
    
    fetcher = RealBettingOddsFetcher()
    
    print("🔍 檢查多 API 系統狀態...")
    status = fetcher.check_multi_api_status()
    
    print(f"📊 多 API 系統可用: {'✅' if status['multi_api_available'] else '❌'}")
    print(f"🎯 整體狀態: {status['overall_status']}")
    
    # 顯示多 API 狀態詳情
    multi_status = status.get('multi_api_status')
    if multi_status and isinstance(multi_status, dict):
        print(f"\n💰 付費 API 狀態:")
        print(f"   活躍端點: {multi_status.get('active_endpoints', 0)}/{multi_status.get('total_endpoints', 0)}")
        
        for endpoint in multi_status.get('endpoints', []):
            status_icon = "✅" if endpoint.get('is_active') else "❌"
            print(f"   {status_icon} {endpoint.get('name', 'N/A')}")
            if endpoint.get('last_error'):
                print(f"      錯誤: {endpoint.get('last_error', '')[:50]}...")
        
        print(f"\n🆓 免費 API 狀態:")
        for api in multi_status.get('free_apis', []):
            status_icon = "✅" if api.get('status') == '可用' else "❌"
            print(f"   {status_icon} {api.get('name', 'N/A')}: {api.get('status', 'N/A')}")
    
    # 顯示建議
    print(f"\n💡 建議:")
    for rec in status.get('recommendations', []):
        print(f"   • {rec}")

def test_betting_odds_fetching():
    """測試博彩盤口獲取"""
    print_separator("博彩盤口數據獲取測試")
    
    fetcher = RealBettingOddsFetcher()
    
    print("🔍 獲取今天的博彩盤口數據...")
    
    # 獲取今天的數據
    today_odds = fetcher.get_mlb_odds_today()
    
    if today_odds:
        summary = today_odds.get('summary', {})
        print(f"✅ 成功獲取數據")
        print(f"📅 日期: {today_odds.get('date', 'N/A')}")
        print(f"📊 總比賽數: {summary.get('total_games', 0)}")
        print(f"🎰 有賠率比賽: {summary.get('games_with_odds', 0)}")
        print(f"📡 數據源: {summary.get('data_source', 'N/A')}")
        
        if summary.get('is_free_api'):
            print("📝 注意: 數據來自免費 API，部分賠率為模擬數據")
            print(f"🔗 API 來源: {', '.join(summary.get('api_sources', []))}")
        
        games = today_odds.get('games', [])
        print_game_odds(games)
    else:
        print("❌ 獲取博彩盤口數據失敗")

def test_historical_odds():
    """測試歷史博彩盤口數據"""
    print_separator("歷史博彩盤口數據測試")
    
    fetcher = RealBettingOddsFetcher()
    
    # 測試昨天的數據
    yesterday = date.today() - timedelta(days=1)
    print(f"🔍 獲取 {yesterday} 的博彩盤口數據...")
    
    historical_odds = fetcher.get_mlb_odds_today(yesterday)
    
    if historical_odds:
        summary = historical_odds.get('summary', {})
        print(f"✅ 成功獲取歷史數據")
        print(f"📅 日期: {historical_odds.get('date', 'N/A')}")
        print(f"📊 總比賽數: {summary.get('total_games', 0)}")
        print(f"🎰 有賠率比賽: {summary.get('games_with_odds', 0)}")
        
        games = historical_odds.get('games', [])
        completed_games = [g for g in games if 'Final' in g.get('status', '')]
        print(f"🏁 已完成比賽: {len(completed_games)}")
        
        print_game_odds(games, max_games=2)
    else:
        print("❌ 獲取歷史博彩盤口數據失敗")

def test_specific_game_odds():
    """測試特定比賽賠率獲取"""
    print_separator("特定比賽賠率測試")
    
    fetcher = RealBettingOddsFetcher()
    
    # 先獲取今天的所有比賽
    today_odds = fetcher.get_mlb_odds_today()
    
    if today_odds and today_odds.get('games'):
        first_game = today_odds['games'][0]
        home_team = first_game.get('home_team', '')
        away_team = first_game.get('away_team', '')
        
        print(f"🔍 測試獲取特定比賽賠率: {away_team} @ {home_team}")
        
        # 通過球隊名稱獲取賠率
        game_odds = fetcher.get_game_odds_by_teams(home_team, away_team)
        
        if game_odds:
            print("✅ 成功獲取特定比賽賠率:")
            
            # 勝負盤
            moneyline = game_odds.get('moneyline', {})
            if moneyline:
                print(f"   勝負盤: {away_team} {moneyline.get('away', 'N/A')} / {home_team} {moneyline.get('home', 'N/A')}")
            
            # 讓分盤
            run_line = game_odds.get('run_line', {})
            if run_line:
                print(f"   讓分盤: {home_team} {run_line.get('home_line', 'N/A')} / {away_team} {run_line.get('away_line', 'N/A')}")
            
            # 大小分盤
            total = game_odds.get('total', {})
            if total:
                print(f"   大小分: {total.get('line', 'N/A')} 分")
        else:
            print("❌ 未找到特定比賽賠率")
    else:
        print("❌ 無法獲取今天的比賽數據進行測試")

def test_api_comparison():
    """測試 API 數據源比較"""
    print_separator("API 數據源比較")
    
    fetcher = RealBettingOddsFetcher()
    
    print("🔍 比較不同 API 數據源...")
    
    # 檢查原有 API 狀態
    original_status = fetcher.check_api_status()
    print(f"🔗 原有 API 狀態:")
    print(f"   配置: {'✅' if original_status.get('api_key_configured') else '❌'}")
    print(f"   可用: {'✅' if original_status.get('api_accessible') else '❌'}")
    print(f"   訊息: {original_status.get('message', 'N/A')}")
    
    if original_status.get('remaining_requests'):
        print(f"   剩餘請求: {original_status.get('remaining_requests')}")
    
    # 檢查多 API 系統
    multi_status = fetcher.check_multi_api_status()
    print(f"\n🔄 多 API 系統:")
    print(f"   狀態: {multi_status.get('overall_status', 'N/A')}")
    print(f"   可用: {'✅' if multi_status.get('multi_api_available') else '❌'}")
    
    # 獲取數據並比較
    odds_data = fetcher.get_mlb_odds_today()
    if odds_data:
        summary = odds_data.get('summary', {})
        print(f"\n📊 實際獲取結果:")
        print(f"   數據源: {summary.get('data_source', 'N/A')}")
        print(f"   比賽數: {summary.get('total_games', 0)}")
        print(f"   有賠率: {summary.get('games_with_odds', 0)}")
        
        if summary.get('is_free_api'):
            print(f"   免費 API: {', '.join(summary.get('api_sources', []))}")
        else:
            print(f"   付費 API: 可獲取真實博彩賠率")

def main():
    """主測試函數"""
    print_separator("增強版博彩盤口獲取器測試")
    
    print("🎯 測試目標:")
    print("1. 驗證多 API 系統整合")
    print("2. 測試博彩盤口數據獲取")
    print("3. 檢查歷史數據支持")
    print("4. 驗證特定比賽查詢")
    print("5. 比較不同數據源")
    
    # 執行各項測試
    test_multi_api_status()
    test_betting_odds_fetching()
    test_historical_odds()
    test_specific_game_odds()
    test_api_comparison()
    
    print_separator("測試完成")
    print("🎉 增強版博彩盤口獲取器測試完成！")
    
    print("\n🚀 系統優勢:")
    print("✅ 多 API 自動切換 - 確保數據可用性")
    print("✅ 智能賠率處理 - 付費/免費 API 自適應")
    print("✅ 歷史數據支持 - 支持任意日期查詢")
    print("✅ 統一數據格式 - 簡化上層應用集成")
    print("✅ 詳細狀態監控 - 便於運維管理")
    
    print("\n📈 應用價值:")
    print("- 為 MLB 預測系統提供可靠的博彩盤口數據")
    print("- 降低對單一 API 的依賴風險")
    print("- 支持免費和付費數據源的無縫切換")
    print("- 提供生產級的錯誤處理和恢復能力")

if __name__ == "__main__":
    main()
