#!/usr/bin/env python3
"""
調試預測數據顯示問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction
from models.unified_betting_predictor import UnifiedBettingPredictor

def debug_prediction_data():
    """調試預測數據"""
    app = create_app()
    
    with app.app_context():
        print("🔍 調試預測數據顯示問題...")
        
        # 檢查6月26日的數據
        target_date = date(2025, 6, 26)
        print(f"\n📅 檢查 {target_date} 的數據:")
        
        # 1. 檢查比賽數據
        games = Game.query.filter(Game.date == target_date).all()
        print(f"  比賽數量: {len(games)}")
        
        if games:
            for i, game in enumerate(games[:3]):  # 只顯示前3場
                print(f"  比賽 {i+1}: {game.away_team} @ {game.home_team}")
                print(f"    比分: {game.away_score} - {game.home_score}")
                print(f"    狀態: {game.game_status}")
        
        # 2. 檢查預測數據
        predictions = Prediction.query.join(Game).filter(
            Game.date == target_date
        ).all()
        print(f"\n  預測數量: {len(predictions)}")
        
        if predictions:
            for i, pred in enumerate(predictions[:3]):  # 只顯示前3個
                print(f"  預測 {i+1}: {pred.game_id}")
                print(f"    預測比分: {pred.predicted_away_score:.1f} - {pred.predicted_home_score:.1f}")
                print(f"    實際比分: {pred.actual_away_score} - {pred.actual_home_score}")
                print(f"    準確性: {pred.is_correct}")
                print(f"    信心度: {pred.confidence}")
        
        # 3. 測試統一預測器的歷史獲取
        print(f"\n🔧 測試統一預測器...")
        try:
            predictor = UnifiedBettingPredictor()
            history = predictor.get_prediction_history(days_back=30, include_results=True)
            
            print(f"  歷史獲取成功: {history.get('success', False)}")
            print(f"  總預測數: {history.get('total_predictions', 0)}")
            
            if history.get('predictions'):
                sample_pred = history['predictions'][0]
                print(f"  樣本預測:")
                print(f"    遊戲ID: {sample_pred.get('game_id')}")
                print(f"    預測比分: {sample_pred.get('predicted_away_score')} - {sample_pred.get('predicted_home_score')}")
                print(f"    實際比分: {sample_pred.get('actual_away_score')} - {sample_pred.get('actual_home_score')}")
                print(f"    準確性: {sample_pred.get('is_correct')}")
                
        except Exception as e:
            print(f"  ❌ 統一預測器測試失敗: {e}")
        
        # 4. 檢查最近的數據
        print(f"\n📊 檢查最近7天的數據統計:")
        recent_dates = [date.today() - timedelta(days=i) for i in range(1, 8)]
        
        for check_date in recent_dates:
            games_count = Game.query.filter(Game.date == check_date).count()
            games_with_results = Game.query.filter(
                Game.date == check_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).count()
            
            predictions_count = Prediction.query.join(Game).filter(
                Game.date == check_date
            ).count()
            
            predictions_with_accuracy = Prediction.query.join(Game).filter(
                Game.date == check_date,
                Prediction.is_correct.isnot(None)
            ).count()
            
            print(f"  {check_date}: {games_count} 場比賽, {games_with_results} 有結果, {predictions_count} 個預測, {predictions_with_accuracy} 有準確性")

def test_template_data():
    """測試模板數據格式"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🎨 測試模板數據格式...")
        
        try:
            predictor = UnifiedBettingPredictor()
            history = predictor.get_prediction_history(days_back=7, include_results=True)
            
            if history.get('success') and history.get('predictions'):
                print(f"  找到 {len(history['predictions'])} 個預測")
                
                # 檢查前幾個預測的數據格式
                for i, pred in enumerate(history['predictions'][:3]):
                    print(f"\n  預測 {i+1} 數據格式:")
                    print(f"    類型: {type(pred)}")
                    print(f"    鍵值: {list(pred.keys()) if isinstance(pred, dict) else 'Not a dict'}")
                    
                    if isinstance(pred, dict):
                        print(f"    actual_home_score: {pred.get('actual_home_score')} (類型: {type(pred.get('actual_home_score'))})")
                        print(f"    actual_away_score: {pred.get('actual_away_score')} (類型: {type(pred.get('actual_away_score'))})")
                        print(f"    is_correct: {pred.get('is_correct')} (類型: {type(pred.get('is_correct'))})")
            else:
                print(f"  ❌ 沒有找到預測數據或獲取失敗")
                print(f"  錯誤: {history.get('error', '未知錯誤')}")
                
        except Exception as e:
            print(f"  ❌ 模板數據測試失敗: {e}")

def fix_null_display_issue():
    """修復 null 顯示問題"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔧 檢查並修復 null 顯示問題...")
        
        # 查找有預測但沒有實際結果的記錄
        problematic_predictions = Prediction.query.filter(
            Prediction.actual_home_score.is_(None),
            Prediction.actual_away_score.is_(None)
        ).join(Game).filter(
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).all()
        
        print(f"  找到 {len(problematic_predictions)} 個有比賽結果但預測記錄中沒有實際結果的記錄")
        
        if problematic_predictions:
            fixed_count = 0
            for pred in problematic_predictions:
                game = Game.query.filter_by(game_id=pred.game_id).first()
                if game and game.home_score is not None and game.away_score is not None:
                    # 更新預測記錄中的實際結果
                    pred.actual_home_score = game.home_score
                    pred.actual_away_score = game.away_score
                    pred.actual_total_runs = game.home_score + game.away_score
                    
                    # 計算準確性
                    predicted_home_wins = pred.predicted_home_score > pred.predicted_away_score
                    actual_home_wins = game.home_score > game.away_score
                    pred.is_correct = (predicted_home_wins == actual_home_wins)
                    
                    # 計算得分差異
                    predicted_total = pred.predicted_home_score + pred.predicted_away_score
                    actual_total = game.home_score + game.away_score
                    pred.score_difference = abs(predicted_total - actual_total)
                    pred.total_runs_difference = abs(predicted_total - actual_total)
                    
                    fixed_count += 1
            
            try:
                db.session.commit()
                print(f"  ✅ 修復了 {fixed_count} 個預測記錄")
            except Exception as e:
                db.session.rollback()
                print(f"  ❌ 修復失敗: {e}")
        else:
            print(f"  ✅ 沒有發現需要修復的記錄")

if __name__ == "__main__":
    print("🚀 開始調試預測數據顯示問題...")
    
    debug_prediction_data()
    test_template_data()
    fix_null_display_issue()
    
    print("\n✅ 調試完成！")
