#!/usr/bin/env python3
"""
檢查賠率記錄的日期範圍
"""

import sys
import os
from datetime import date, datetime

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, BettingOdds
from sqlalchemy import func, distinct

def check_odds_records():
    """檢查賠率記錄的日期範圍和統計"""
    print("📊 檢查賠率記錄統計")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 基本統計
            total_odds = BettingOdds.query.count()
            spreads_count = BettingOdds.query.filter_by(market_type='spreads').count()
            totals_count = BettingOdds.query.filter_by(market_type='totals').count()
            
            print(f"📈 基本統計:")
            print(f"   總賠率記錄: {total_odds:,}")
            print(f"   讓分盤記錄: {spreads_count:,}")
            print(f"   大小分記錄: {totals_count:,}")
            
            if total_odds == 0:
                print("❌ 沒有賠率記錄")
                return
            
            # 2. 博彩商統計
            bookmakers = db.session.query(distinct(BettingOdds.bookmaker)).all()
            print(f"\n🏢 博彩商 ({len(bookmakers)} 個):")
            for bookmaker in bookmakers:
                count = BettingOdds.query.filter_by(bookmaker=bookmaker[0]).count()
                print(f"   - {bookmaker[0]}: {count:,} 筆記錄")
            
            # 3. 日期範圍統計 - 通過關聯的比賽日期
            print(f"\n📅 日期範圍統計:")
            
            # 查詢有賠率記錄的比賽日期
            games_with_odds = db.session.query(Game.date)\
                .join(BettingOdds, Game.game_id == BettingOdds.game_id)\
                .distinct().all()
            
            if games_with_odds:
                dates = [g[0] for g in games_with_odds]
                dates.sort()
                
                earliest_date = dates[0]
                latest_date = dates[-1]
                total_days = len(dates)
                
                print(f"   最早日期: {earliest_date}")
                print(f"   最晚日期: {latest_date}")
                print(f"   覆蓋天數: {total_days} 天")
                
                # 4. 每日統計
                print(f"\n📊 每日賠率記錄統計:")
                daily_stats = db.session.query(
                    Game.date,
                    func.count(BettingOdds.id).label('odds_count'),
                    func.count(distinct(BettingOdds.game_id)).label('games_count')
                ).join(BettingOdds, Game.game_id == BettingOdds.game_id)\
                .group_by(Game.date)\
                .order_by(Game.date.desc())\
                .limit(10).all()
                
                print(f"   最近 10 天的記錄:")
                for stat in daily_stats:
                    print(f"   {stat.date}: {stat.games_count} 場比賽, {stat.odds_count} 筆賠率")
            
            # 5. 創建時間統計
            print(f"\n🕒 記錄創建時間:")
            earliest_created = BettingOdds.query.order_by(BettingOdds.created_at.asc()).first()
            latest_created = BettingOdds.query.order_by(BettingOdds.created_at.desc()).first()
            
            if earliest_created and latest_created:
                print(f"   最早創建: {earliest_created.created_at}")
                print(f"   最晚創建: {latest_created.created_at}")
            
            # 6. 最近的記錄樣本
            print(f"\n📋 最近 5 筆記錄樣本:")
            recent_odds = db.session.query(BettingOdds, Game.date, Game.home_team, Game.away_team)\
                .join(Game, BettingOdds.game_id == Game.game_id)\
                .order_by(BettingOdds.created_at.desc())\
                .limit(5).all()
            
            for odds, game_date, home_team, away_team in recent_odds:
                print(f"   {game_date} | {away_team} @ {home_team}")
                print(f"     博彩商: {odds.bookmaker} | 市場: {odds.market_type}")
                if odds.market_type == 'spreads':
                    print(f"     讓分: 主隊 {odds.home_spread_point or 'N/A'} ({odds.home_spread_price or 'N/A'})")
                elif odds.market_type == 'totals':
                    print(f"     大小分: {odds.total_point or 'N/A'} (Over: {odds.over_price or 'N/A'}, Under: {odds.under_price or 'N/A'})")
                print()
            
            # 7. 數據完整性檢查
            print(f"🔍 數據完整性檢查:")
            
            # 檢查有比賽但沒有賠率的日期
            all_game_dates = db.session.query(distinct(Game.date)).all()
            all_game_dates = [d[0] for d in all_game_dates]
            
            odds_dates = [d[0] for d in games_with_odds]
            missing_odds_dates = set(all_game_dates) - set(odds_dates)
            
            print(f"   總比賽日期: {len(all_game_dates)} 天")
            print(f"   有賠率日期: {len(odds_dates)} 天")
            print(f"   缺少賠率日期: {len(missing_odds_dates)} 天")
            
            if missing_odds_dates:
                missing_sorted = sorted(list(missing_odds_dates))
                print(f"   缺少賠率的日期 (最近10天):")
                for missing_date in missing_sorted[-10:]:
                    game_count = Game.query.filter_by(date=missing_date).count()
                    print(f"     {missing_date}: {game_count} 場比賽")
            
        except Exception as e:
            print(f"❌ 檢查失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    check_odds_records()
