#!/usr/bin/env python3
"""
檢查BettingOdds表中的market_type值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, BettingOdds, Game

def check_market_types():
    """檢查market_type的值"""
    app = create_app()
    
    with app.app_context():
        print("🔍 檢查BettingOdds表中的market_type值...")
        
        # 查詢所有不同的market_type值
        market_types = db.session.query(BettingOdds.market_type).distinct().all()
        print(f"📊 找到的market_type值:")
        for mt in market_types:
            print(f"  - {mt[0]}")
        
        # 檢查2025-07-07的具體記錄
        test_date = date(2025, 7, 7)
        odds_records = db.session.query(BettingOdds, Game).join(
            Game, Game.game_id == BettingOdds.game_id
        ).filter(
            Game.date == test_date
        ).limit(3).all()
        
        print(f"\n📋 {test_date} 的前3筆記錄:")
        for odds, game in odds_records:
            print(f"  比賽: {game.away_team} @ {game.home_team}")
            print(f"    market_type: {odds.market_type}")
            print(f"    total_point: {odds.total_point}")
            print(f"    home_spread_point: {odds.home_spread_point}")
            print(f"    bookmaker: {odds.bookmaker}")
            print()

if __name__ == "__main__":
    check_market_types()
