#!/usr/bin/env python3
"""
檢查網頁數據是否正確
"""

import sqlite3
import os
import requests
import json

def check_database_data():
    """檢查數據庫數據"""
    print("🔍 檢查數據庫數據")
    print("=" * 40)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查2025-07-08的數據
        cursor.execute("""
            SELECT 
                g.away_team || '@' || g.home_team as matchup,
                p.predicted_total_runs,
                p.over_under_line,
                p.updated_at
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date = '2025-07-08'
            ORDER BY g.game_id
        """)
        
        records = cursor.fetchall()
        
        print("數據庫中的數據:")
        for matchup, total, line, updated_at in records:
            status = "✅" if (4 <= total <= 12 and 6 <= line <= 10) else "❌"
            print(f"  {status} {matchup}: 總分={total:.1f}, 盤口={line}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查數據庫失敗: {e}")

def check_web_api():
    """檢查網頁API數據"""
    print("\n🌐 檢查網頁API數據")
    print("=" * 40)
    
    try:
        # 嘗試訪問API端點
        url = "http://localhost:5500/api/predictions/over_under?date=2025-07-08"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("API返回的數據:")
            
            if 'predictions' in data:
                for pred in data['predictions'][:5]:  # 只顯示前5個
                    matchup = f"{pred.get('away_team', 'N/A')}@{pred.get('home_team', 'N/A')}"
                    total = pred.get('predicted_total_runs', 0)
                    line = pred.get('over_under_line', 0)
                    status = "✅" if (4 <= total <= 12 and 6 <= line <= 10) else "❌"
                    print(f"  {status} {matchup}: 總分={total}, 盤口={line}")
            else:
                print("  ⚠️  API響應中沒有predictions字段")
                print(f"  響應內容: {data}")
        else:
            print(f"  ❌ API請求失敗: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("  ❌ 無法連接到Flask應用 (http://localhost:5500)")
    except Exception as e:
        print(f"  ❌ 檢查API失敗: {e}")

def suggest_solutions():
    """建議解決方案"""
    print("\n💡 解決方案建議")
    print("=" * 40)
    print("如果網頁還是顯示舊數據，請嘗試:")
    print("1. 🔄 硬刷新瀏覽器 (Ctrl+F5 或 Cmd+Shift+R)")
    print("2. 🧹 清除瀏覽器緩存")
    print("3. 🔄 重啟Flask應用:")
    print("   - 在終端按 Ctrl+C 停止應用")
    print("   - 重新運行: python app.py")
    print("4. 🌐 嘗試無痕模式瀏覽器")
    print("5. 📱 嘗試不同的瀏覽器")

if __name__ == '__main__':
    check_database_data()
    check_web_api()
    suggest_solutions()
