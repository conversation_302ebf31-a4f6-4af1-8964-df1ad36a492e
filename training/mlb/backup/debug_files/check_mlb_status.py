#!/usr/bin/env python3
"""
檢查 MLB 比賽狀況和預測資料
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models.database import db
from models.game import Game
from models.prediction import Prediction
from datetime import datetime

def check_mlb_status():
    """檢查 MLB 比賽狀況"""
    with app.app_context():
        print('🏟️ MLB 比賽狀況檢查')
        print('=' * 60)

        # 檢查 07-01, 07-02, 07-03 的比賽
        dates_to_check = ['2025-07-01', '2025-07-02', '2025-07-03']

        for date_str in dates_to_check:
            print(f'\n📅 {date_str} 比賽狀況:')
            games = Game.query.filter(Game.game_date.like(f'{date_str}%')).all()
            
            if not games:
                print('   ❌ 沒有找到比賽記錄')
                continue
                
            status_counts = {}
            postponed_games = []
            completed_games = []
            
            for game in games:
                status = game.status_code or 'Unknown'
                status_counts[status] = status_counts.get(status, 0) + 1
                
                if status in ['P', 'PP', 'Postponed']:
                    postponed_games.append(f'{game.away_team} @ {game.home_team} (ID: {game.game_id})')
                elif status == 'F':
                    score = f'{game.away_score or 0}-{game.home_score or 0}'
                    completed_games.append(f'{game.away_team} @ {game.home_team} {score}')
            
            print(f'   📊 總比賽數: {len(games)}')
            for status, count in status_counts.items():
                status_name = {
                    'F': '已完成',
                    'S': '進行中', 
                    'P': '延期',
                    'PP': '延期',
                    'Postponed': '延期',
                    'Preview': '預覽',
                    'Pre-Game': '賽前',
                    'Scheduled': '已排程'
                }.get(status, status)
                print(f'   - {status_name} ({status}): {count} 場')
            
            if postponed_games:
                print(f'   🚫 延期比賽:')
                for game in postponed_games:
                    print(f'     • {game}')
            
            if completed_games and len(completed_games) <= 8:
                print(f'   ✅ 已完成比賽:')
                for game in completed_games:
                    print(f'     • {game}')

def check_0702_predictions():
    """檢查 07-02 的預測資料"""
    with app.app_context():
        print('\n' + '=' * 60)
        print('🔮 檢查 07-02 的預測資料:')

        # 檢查 07-02 的預測
        predictions_0702 = Prediction.query.filter(
            Prediction.prediction_date.like('2025-07-02%')
        ).all()

        print(f'📊 07-02 預測總數: {len(predictions_0702)}')

        if predictions_0702:
            model_versions = {}
            correct_predictions = 0
            total_with_results = 0
            
            for pred in predictions_0702:
                version = pred.model_version or 'Unknown'
                model_versions[version] = model_versions.get(version, 0) + 1
                
                # 檢查準確性
                if pred.is_correct is not None:
                    total_with_results += 1
                    if pred.is_correct:
                        correct_predictions += 1
            
            print('📈 預測模型版本分布:')
            for version, count in model_versions.items():
                print(f'   - {version}: {count} 場')
            
            if total_with_results > 0:
                accuracy = (correct_predictions / total_with_results) * 100
                print(f'\n🎯 預測準確率: {correct_predictions}/{total_with_results} = {accuracy:.1f}%')
            
            # 顯示前8個預測
            print('\n🎯 預測詳情:')
            for i, pred in enumerate(predictions_0702[:8]):
                home_score = pred.predicted_home_score or 0
                away_score = pred.predicted_away_score or 0
                confidence = (pred.confidence or 0) * 100
                
                # 獲取實際結果
                actual_result = ""
                if pred.actual_home_score is not None and pred.actual_away_score is not None:
                    actual_result = f" | 實際: {pred.actual_away_score}-{pred.actual_home_score}"
                    if pred.is_correct is not None:
                        result_icon = "✅" if pred.is_correct else "❌"
                        actual_result += f" {result_icon}"
                
                print(f'   {i+1}. 比賽 {pred.game_id}: 預測 {away_score:.1f}-{home_score:.1f} (信心度: {confidence:.1f}%){actual_result}')
        else:
            print('❌ 沒有找到 07-02 的預測資料')

def generate_0702_predictions():
    """生成 07-02 的預測"""
    with app.app_context():
        print('\n' + '=' * 60)
        print('🚀 嘗試生成 07-02 的預測...')
        
        try:
            from models.prediction_service import PredictionService
            service = PredictionService()
            
            # 檢查 07-02 是否有比賽
            games_0702 = Game.query.filter(Game.game_date.like('2025-07-02%')).all()
            print(f'📅 07-02 找到 {len(games_0702)} 場比賽')
            
            if games_0702:
                # 嘗試生成預測
                result = service.generate_daily_predictions('2025-07-02')
                print(f'🎯 預測生成結果: {result}')
            else:
                print('❌ 07-02 沒有比賽可以預測')
                
        except Exception as e:
            print(f'❌ 生成預測失敗: {e}')

if __name__ == "__main__":
    print("🔍 開始檢查 MLB 狀況...")
    
    # 檢查比賽狀況
    check_mlb_status()
    
    # 檢查 07-02 預測
    check_0702_predictions()
    
    # 如果沒有 07-02 預測，嘗試生成
    with app.app_context():
        predictions_count = Prediction.query.filter(
            Prediction.prediction_date.like('2025-07-02%')
        ).count()
        
        if predictions_count == 0:
            generate_0702_predictions()
    
    print('\n✅ 檢查完成！')
