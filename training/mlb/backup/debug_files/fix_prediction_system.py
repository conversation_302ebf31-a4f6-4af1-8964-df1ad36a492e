#!/usr/bin/env python3
"""
修復預測系統 - 解決球隊得分能力差異問題
"""

import sys
import os
from datetime import date, timedelta
from collections import defaultdict

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, TeamStats, Team
from models.ml_predictor import MLBPredictor

def fix_prediction_system():
    """修復預測系統"""
    print("🔧 修復預測系統")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        # 1. 檢查當前狀態
        print("📊 檢查當前狀態:")
        team_count = Team.query.count()
        stats_count = TeamStats.query.count()
        print(f"球隊數量: {team_count}")
        print(f"統計數量: {stats_count}")
        
        # 2. 如果沒有統計數據，重新生成
        if stats_count == 0:
            print("\n🔄 重新生成TeamStats數據...")
            generate_team_stats()
        
        # 3. 檢查球隊代碼匹配
        print("\n🏟️ 檢查球隊代碼:")
        test_teams = ['LAD', 'NYY', 'OAK', 'MIA', 'HOU']
        for team_code in test_teams:
            team = Team.query.filter_by(team_code=team_code).first()
            if team:
                stats = TeamStats.query.filter_by(team_id=team.team_id).first()
                if stats:
                    print(f"  {team_code}: ✅ ID:{team.team_id}, 得分:{stats.runs_scored:.2f}")
                else:
                    print(f"  {team_code}: ❌ ID:{team.team_id}, 無統計數據")
            else:
                print(f"  {team_code}: ❌ 找不到球隊")
        
        # 4. 測試預測系統
        print("\n🎯 測試預測系統:")
        try:
            predictor = MLBPredictor()
            
            # 測試不同球隊組合
            test_games = [
                ('LAD', 'OAK'),  # 強隊 vs 弱隊
                ('NYY', 'MIA'),  # 強隊 vs 弱隊
                ('HOU', 'COL'),  # 強隊 vs 弱隊
            ]
            
            predictions = []
            for home, away in test_games:
                try:
                    pred = predictor.predict_game(home, away, date.today())
                    predictions.append(pred)
                    print(f"  {away} @ {home}: {pred['predicted_away_score']:.1f} - {pred['predicted_home_score']:.1f}")
                except Exception as e:
                    print(f"  {away} @ {home}: ❌ 錯誤 - {e}")
            
            # 5. 分析預測差異
            if predictions:
                home_scores = [p['predicted_home_score'] for p in predictions]
                away_scores = [p['predicted_away_score'] for p in predictions]
                
                home_diff = max(home_scores) - min(home_scores)
                away_diff = max(away_scores) - min(away_scores)
                
                print(f"\n📈 預測差異分析:")
                print(f"主隊得分範圍: {min(home_scores):.1f} - {max(home_scores):.1f} (差異: {home_diff:.1f})")
                print(f"客隊得分範圍: {min(away_scores):.1f} - {max(away_scores):.1f} (差異: {away_diff:.1f})")
                
                if home_diff > 0.5 or away_diff > 0.5:
                    print("✅ 修復成功! 預測有合理差異")
                else:
                    print("❌ 修復失敗! 預測差異仍然太小")
                    print("需要進一步調查模型訓練問題")
            
        except Exception as e:
            print(f"❌ 預測系統測試失敗: {e}")

def generate_team_stats():
    """生成球隊統計數據"""
    print("📊 生成球隊統計數據...")
    
    # 獲取最近90天的比賽數據
    end_date = date.today()
    start_date = end_date - timedelta(days=90)
    
    games = Game.query.filter(
        Game.date >= start_date,
        Game.date < end_date,
        Game.home_score.isnot(None),
        Game.away_score.isnot(None),
        Game.game_status == 'completed'
    ).all()
    
    print(f"分析 {len(games)} 場比賽")
    
    # 初始化統計字典
    team_stats = defaultdict(lambda: {
        'games': 0,
        'wins': 0,
        'losses': 0,
        'home_games': 0,
        'home_wins': 0,
        'home_losses': 0,
        'away_games': 0,
        'away_wins': 0,
        'away_losses': 0,
        'runs_scored': 0,
        'runs_allowed': 0,
    })
    
    # 計算統計數據
    for game in games:
        home_team = game.home_team
        away_team = game.away_team
        home_score = game.home_score
        away_score = game.away_score
        
        # 主隊統計
        team_stats[home_team]['games'] += 1
        team_stats[home_team]['home_games'] += 1
        team_stats[home_team]['runs_scored'] += home_score
        team_stats[home_team]['runs_allowed'] += away_score
        
        if home_score > away_score:
            team_stats[home_team]['wins'] += 1
            team_stats[home_team]['home_wins'] += 1
        else:
            team_stats[home_team]['losses'] += 1
            team_stats[home_team]['home_losses'] += 1
        
        # 客隊統計
        team_stats[away_team]['games'] += 1
        team_stats[away_team]['away_games'] += 1
        team_stats[away_team]['runs_scored'] += away_score
        team_stats[away_team]['runs_allowed'] += home_score
        
        if away_score > home_score:
            team_stats[away_team]['wins'] += 1
            team_stats[away_team]['away_wins'] += 1
        else:
            team_stats[away_team]['losses'] += 1
            team_stats[away_team]['away_losses'] += 1
    
    # 保存到數據庫
    print("💾 保存統計數據...")
    
    # 清除現有數據
    TeamStats.query.delete()
    
    for team_code, stats in team_stats.items():
        # 獲取球隊ID
        team = Team.query.filter_by(team_code=team_code).first()
        if not team:
            print(f"⚠️ 找不到球隊: {team_code}")
            continue
        
        # 計算平均值
        games = stats['games']
        if games > 0:
            runs_per_game = stats['runs_scored'] / games
            runs_allowed_per_game = stats['runs_allowed'] / games
            win_pct = stats['wins'] / games
            
            # 創建TeamStats記錄
            team_stat = TeamStats(
                team_id=team.team_id,
                season=2025,
                games_played=games,
                wins=stats['wins'],
                losses=stats['losses'],
                win_percentage=win_pct,
                runs_scored=runs_per_game,
                runs_allowed=runs_allowed_per_game,
                batting_avg=0.250,  # 默認值
                era=runs_allowed_per_game * 9 / 9,  # 簡化ERA計算
                home_wins=stats['home_wins'],
                home_losses=stats['home_losses'],
                away_wins=stats['away_wins'],
                away_losses=stats['away_losses']
            )
            
            db.session.add(team_stat)
    
    db.session.commit()
    
    # 驗證結果
    final_count = TeamStats.query.count()
    print(f"✅ 生成完成! 總計 {final_count} 個球隊統計")

if __name__ == "__main__":
    fix_prediction_system()
