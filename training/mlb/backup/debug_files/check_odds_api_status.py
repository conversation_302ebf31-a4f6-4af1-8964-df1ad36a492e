#!/usr/bin/env python3
"""
檢查博彩API狀態
"""

import requests
import os

def check_odds_api():
    """檢查The Odds API狀態"""
    print("🔍 檢查The Odds API狀態")
    print("=" * 40)
    
    # 讀取API密鑰
    api_key_file = os.path.join(os.path.dirname(__file__), 'models', 'odds-api.txt')
    
    try:
        with open(api_key_file, 'r') as f:
            api_key = f.read().strip()
        
        print(f"API密鑰: {api_key[:8]}...{api_key[-4:]}")
        
        # 測試API連接
        url = "https://api.the-odds-api.com/v4/sports/baseball_mlb/odds"
        params = {
            'apiKey': api_key,
            'regions': 'us',
            'markets': 'totals',
            'oddsFormat': 'decimal',
            'dateFormat': 'iso'
        }
        
        print("📡 測試API連接...")
        response = requests.get(url, params=params, timeout=10)
        
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API正常，獲取到 {len(data)} 場比賽")
            
            # 顯示第一場比賽的數據
            if data:
                game = data[0]
                print(f"\n📊 示例比賽:")
                print(f"   主隊: {game.get('home_team')}")
                print(f"   客隊: {game.get('away_team')}")
                print(f"   開始時間: {game.get('commence_time')}")
                
                bookmakers = game.get('bookmakers', [])
                if bookmakers:
                    bookmaker = bookmakers[0]
                    print(f"   博彩商: {bookmaker.get('title')}")
                    
                    markets = bookmaker.get('markets', [])
                    for market in markets:
                        if market.get('key') == 'totals':
                            outcomes = market.get('outcomes', [])
                            for outcome in outcomes:
                                if outcome.get('name') == 'Over':
                                    print(f"   大小分: {outcome.get('point')}")
                                    print(f"   Over賠率: {outcome.get('price')}")
                                    break
        
        elif response.status_code == 401:
            print("❌ API密鑰無效或已過期")
            print("請檢查 models/odds-api.txt 中的密鑰")
            
        elif response.status_code == 429:
            print("⚠️  API請求次數已達上限")
            
        else:
            print(f"❌ API請求失敗: {response.status_code}")
            print(f"錯誤信息: {response.text}")
            
    except FileNotFoundError:
        print("❌ 找不到API密鑰文件: models/odds-api.txt")
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

def check_api_usage():
    """檢查API使用量"""
    print("\n📊 檢查API使用量")
    print("=" * 40)
    
    api_key_file = os.path.join(os.path.dirname(__file__), 'models', 'odds-api.txt')
    
    try:
        with open(api_key_file, 'r') as f:
            api_key = f.read().strip()
        
        # 檢查使用量
        url = f"https://api.the-odds-api.com/v4/sports?apiKey={api_key}"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            remaining = response.headers.get('x-requests-remaining', 'Unknown')
            used = response.headers.get('x-requests-used', 'Unknown')
            print(f"剩餘請求次數: {remaining}")
            print(f"已使用請求次數: {used}")
        else:
            print(f"無法獲取使用量信息: {response.status_code}")
            
    except Exception as e:
        print(f"檢查使用量失敗: {e}")

if __name__ == '__main__':
    check_odds_api()
    check_api_usage()
