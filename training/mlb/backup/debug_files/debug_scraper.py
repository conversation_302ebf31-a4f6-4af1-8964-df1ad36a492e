#!/usr/bin/env python3
"""
調試 SportsBookReview 網頁抓取器 - 檢查實際HTML結構
"""

import requests
from bs4 import BeautifulSoup
import logging
from datetime import date, timedelta

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_website_structure():
    """調試網站結構"""
    try:
        # 設置請求頭
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        session = requests.Session()
        session.headers.update(headers)
        
        # 測試總分盤頁面
        test_date = "2021-04-04"
        totals_url = f"https://www.sportsbookreview.com/betting-odds/mlb-baseball/totals/full-game/?date={test_date}"
        
        logger.info(f"正在獲取: {totals_url}")
        
        response = session.get(totals_url, timeout=30)
        response.raise_for_status()
        
        logger.info(f"響應狀態碼: {response.status_code}")
        logger.info(f"響應內容長度: {len(response.content)} bytes")
        
        # 解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 檢查頁面標題
        title = soup.find('title')
        if title:
            logger.info(f"頁面標題: {title.get_text(strip=True)}")
        
        # 查找所有表格
        tables = soup.find_all('table')
        logger.info(f"找到 {len(tables)} 個表格")
        
        # 查找所有行
        all_rows = soup.find_all('tr')
        logger.info(f"找到 {len(all_rows)} 個表格行")
        
        # 檢查是否有遊戲數據的跡象
        game_indicators = [
            'game-row', 'game', 'matchup', 'team', 'odds', 'bet365', 
            'fanduel', 'caesars', 'draftkings', 'betrivers'
        ]
        
        found_indicators = []
        for indicator in game_indicators:
            elements = soup.find_all(attrs={'class': lambda x: x and indicator.lower() in str(x).lower()})
            if elements:
                found_indicators.append(f"{indicator}: {len(elements)} 個元素")
        
        if found_indicators:
            logger.info("找到的遊戲相關元素:")
            for indicator in found_indicators:
                logger.info(f"  - {indicator}")
        else:
            logger.warning("沒有找到明顯的遊戲相關元素")
        
        # 查找包含MLB隊伍名稱的元素
        mlb_teams = ['ATL', 'PHI', 'TOR', 'NYY', 'BAL', 'BOS', 'STL', 'CIN', 
                     'CLE', 'DET', 'MIL', 'CHC', 'LAD', 'COL', 'HOU', 'OAK',
                     'ARI', 'SD', 'CHW', 'LAA', 'TB', 'MIN', 'WSH', 'NYM',
                     'MIA', 'SEA', 'TEX', 'SF', 'KC', 'PIT']
        
        team_mentions = []
        for team in mlb_teams:
            if team.lower() in response.text.lower():
                team_mentions.append(team)
        
        if team_mentions:
            logger.info(f"找到的MLB隊伍: {', '.join(team_mentions[:10])}")
            if len(team_mentions) > 10:
                logger.info(f"... 還有 {len(team_mentions) - 10} 個隊伍")
        else:
            logger.warning("沒有找到MLB隊伍名稱")
        
        # 保存HTML到文件以供檢查
        with open('debug_totals_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        logger.info("HTML內容已保存到 debug_totals_page.html")
        
        # 檢查是否有JavaScript重定向或動態加載
        if 'javascript' in response.text.lower() or 'redirect' in response.text.lower():
            logger.warning("頁面可能使用JavaScript動態加載內容")
        
        # 檢查是否有錯誤消息
        error_indicators = ['error', 'not found', '404', 'unavailable', 'maintenance']
        for indicator in error_indicators:
            if indicator.lower() in response.text.lower():
                logger.warning(f"可能的錯誤指示: {indicator}")
        
        # 顯示前500個字符的內容
        logger.info("頁面內容預覽:")
        logger.info("-" * 50)
        logger.info(response.text[:500])
        logger.info("-" * 50)
        
    except Exception as e:
        logger.error(f"調試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_different_dates():
    """測試不同日期"""
    today = date.today()
    test_dates = [
        "2021-04-04",
        "2024-07-04",
        (today - timedelta(days=3)).strftime('%Y-%m-%d')
    ]
    
    for test_date in test_dates:
        logger.info(f"\n測試日期: {test_date}")
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            url = f"https://www.sportsbookreview.com/betting-odds/mlb-baseball/totals/full-game/?date={test_date}"
            response = requests.get(url, headers=headers, timeout=15)
            
            logger.info(f"  狀態碼: {response.status_code}")
            logger.info(f"  內容長度: {len(response.content)} bytes")
            
            # 檢查是否有MLB相關內容
            if 'mlb' in response.text.lower() or 'baseball' in response.text.lower():
                logger.info("  ✅ 包含MLB/棒球相關內容")
            else:
                logger.warning("  ❌ 沒有MLB/棒球相關內容")
                
        except Exception as e:
            logger.error(f"  ❌ 請求失敗: {e}")

if __name__ == "__main__":
    print("🔍 開始調試 SportsBookReview 網站結構...")
    print("=" * 60)
    
    debug_website_structure()
    
    print("\n" + "=" * 60)
    print("🔍 測試不同日期...")
    
    test_different_dates()
    
    print("\n" + "=" * 60)
    print("🏁 調試完成")
