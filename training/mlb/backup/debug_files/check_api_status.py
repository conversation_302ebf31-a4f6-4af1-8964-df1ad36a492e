#!/usr/bin/env python3
"""
檢查 The Odds API 狀態和配額
"""

import sys
import os
from datetime import datetime

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.odds_data_fetcher import OddsDataFetcher

def check_api_status():
    """檢查API狀態"""
    print("🔍 檢查 The Odds API 狀態")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        fetcher = OddsDataFetcher(app)
        
        # 檢查API狀態
        status = fetcher.check_api_status()
        
        print(f"📊 API狀態報告:")
        print(f"   API密鑰配置: {'✅' if status['api_key_configured'] else '❌'}")
        print(f"   API可訪問: {'✅' if status['api_accessible'] else '❌'}")
        print(f"   剩餘請求次數: {status.get('remaining_requests', 'N/A')}")
        print(f"   檢查時間: {status['last_check']}")
        print(f"   狀態信息: {status.get('message', 'N/A')}")
        
        if not status['api_accessible']:
            print(f"\n⚠️  API當前不可用")
            print(f"   原因: {status.get('message', '未知')}")
            
            if '401' in str(status.get('message', '')):
                print(f"\n💡 解決方案:")
                print(f"   1. 檢查API密鑰是否正確")
                print(f"   2. 等待配額重置 (通常每天重置)")
                print(f"   3. 升級到付費計劃獲得更多配額")
                
                print(f"\n📅 The Odds API 配額信息:")
                print(f"   免費計劃: 500次請求/月")
                print(f"   配額重置: 每天 UTC 00:00")
                print(f"   當前UTC時間: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print(f"\n✅ API正常運行")
            remaining = status.get('remaining_requests')
            if remaining:
                try:
                    remaining_int = int(remaining)
                    if remaining_int < 10:
                        print(f"   ⚠️  剩餘請求次數較少: {remaining}")
                    else:
                        print(f"   ✅ 剩餘請求次數充足: {remaining}")
                except:
                    print(f"   剩餘請求次數: {remaining}")

def suggest_next_steps():
    """建議下一步操作"""
    print(f"\n🎯 建議的下一步操作:")
    print(f"   1. 如果API可用且有配額:")
    print(f"      python fetch_historical_odds.py")
    print(f"   ")
    print(f"   2. 如果API配額用完:")
    print(f"      - 等待明天配額重置")
    print(f"      - 或考慮升級API計劃")
    print(f"   ")
    print(f"   3. 測試球隊映射修復:")
    print(f"      python test_team_mapping.py")
    print(f"   ")
    print(f"   4. 檢查現有數據:")
    print(f"      - 查看數據庫中已有的賠率記錄")
    print(f"      - 分析預測系統準確率")

if __name__ == "__main__":
    check_api_status()
    suggest_next_steps()
