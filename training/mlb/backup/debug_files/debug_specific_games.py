#!/usr/bin/env python3
"""
調試特定比賽的預測問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game, Prediction, BoxScore
from models.enhanced_feature_engineer import EnhancedFeatureEngineer
from models.daily_lineup_fetcher import DailyLineupFetcher
import json

def debug_nymbal_games():
    """調試NYM @ BAL重賽問題"""
    app = create_app()
    
    with app.app_context():
        print("🔍 調試 NYM @ BAL 重賽問題")
        print("=" * 60)
        
        # 查找兩場比賽
        games = Game.query.filter(
            Game.game_id.in_(['777165', '777181'])
        ).order_by(Game.game_id).all()
        
        if len(games) != 2:
            print(f"❌ 找不到兩場比賽，只找到 {len(games)} 場")
            return
        
        feature_engineer = EnhancedFeatureEngineer()
        
        for i, game in enumerate(games, 1):
            print(f"\n🎲 比賽 {i}: {game.away_team} @ {game.home_team}")
            print(f"  Game ID: {game.game_id}")
            print(f"  日期: {game.date}")
            print(f"  實際比分: {game.away_score} - {game.home_score}")
            
            # 檢查預測
            prediction = Prediction.query.filter_by(game_id=game.game_id).order_by(Prediction.created_at.desc()).first()
            if prediction:
                print(f"  預測比分: {prediction.predicted_away_score:.1f} - {prediction.predicted_home_score:.1f}")
                print(f"  信心度: {prediction.confidence:.1%}")
            
            # 測試特徵工程
            try:
                print(f"\n  🔧 測試特徵工程 (game_id={game.game_id}):")
                features = feature_engineer.extract_comprehensive_features(
                    home_team=game.home_team,
                    away_team=game.away_team,
                    game_date=game.date,
                    game_id=game.game_id  # 傳遞game_id
                )
                
                # 檢查投手信息
                home_pitcher = features.get('home_probable_starter')
                away_pitcher = features.get('away_probable_starter')
                
                print(f"    主隊投手: {home_pitcher or '未知'}")
                print(f"    客隊投手: {away_pitcher or '未知'}")
                
                # 檢查投手質量特徵
                pitcher_features = {k: v for k, v in features.items() if 'pitcher' in k.lower()}
                print(f"    投手特徵數量: {len(pitcher_features)}")
                
                if pitcher_features:
                    for key, value in list(pitcher_features.items())[:3]:
                        print(f"      {key}: {value}")
                
            except Exception as e:
                print(f"    ❌ 特徵工程錯誤: {e}")

def debug_tb_bos_game():
    """調試TB @ BOS極端預測問題"""
    app = create_app()
    
    with app.app_context():
        print("\n🔍 調試 TB @ BOS 極端預測問題")
        print("=" * 60)
        
        # 查找比賽
        game = Game.query.filter_by(game_id='777170').first()
        
        if not game:
            print("❌ 找不到TB @ BOS比賽")
            return
        
        print(f"🎲 比賽: {game.away_team} @ {game.home_team}")
        print(f"  Game ID: {game.game_id}")
        print(f"  日期: {game.date}")
        print(f"  實際比分: {game.away_score} - {game.home_score}")
        
        # 檢查預測
        prediction = Prediction.query.filter_by(game_id=game.game_id).order_by(Prediction.created_at.desc()).first()
        if prediction:
            total_predicted = prediction.predicted_away_score + prediction.predicted_home_score
            print(f"  預測比分: {prediction.predicted_away_score:.1f} - {prediction.predicted_home_score:.1f}")
            print(f"  預測總分: {total_predicted:.1f}")
            print(f"  信心度: {prediction.confidence:.1%}")
            
            if total_predicted > 15:
                print(f"  ⚠️  極端高分預測！")
        
        # 測試特徵工程
        feature_engineer = EnhancedFeatureEngineer()
        
        try:
            print(f"\n  🔧 測試特徵工程:")
            features = feature_engineer.extract_comprehensive_features(
                home_team=game.home_team,
                away_team=game.away_team,
                game_date=game.date,
                game_id=game.game_id
            )
            
            # 檢查投手信息
            home_pitcher = features.get('home_probable_starter')
            away_pitcher = features.get('away_probable_starter')
            
            print(f"    主隊投手: {home_pitcher or '未知'}")
            print(f"    客隊投手: {away_pitcher or '未知'}")
            
            # 檢查得分相關特徵
            scoring_features = {k: v for k, v in features.items() if any(word in k.lower() for word in ['score', 'runs', 'era', 'avg'])}
            print(f"    得分相關特徵數量: {len(scoring_features)}")
            
            if scoring_features:
                print(f"    關鍵得分特徵:")
                for key, value in list(scoring_features.items())[:5]:
                    print(f"      {key}: {value}")
            
            # 檢查是否有異常值
            extreme_values = {k: v for k, v in features.items() if isinstance(v, (int, float)) and abs(v) > 10}
            if extreme_values:
                print(f"    ⚠️  發現極端值:")
                for key, value in extreme_values.items():
                    print(f"      {key}: {value}")
                    
        except Exception as e:
            print(f"    ❌ 特徵工程錯誤: {e}")

def check_pitcher_cache():
    """檢查投手緩存狀況"""
    app = create_app()
    
    with app.app_context():
        print("\n🔍 檢查投手緩存狀況")
        print("=" * 60)
        
        feature_engineer = EnhancedFeatureEngineer()
        
        # 檢查緩存內容
        if hasattr(feature_engineer, '_pitcher_cache'):
            cache = feature_engineer._pitcher_cache
            print(f"緩存條目數量: {len(cache)}")
            
            # 顯示NYM @ BAL相關的緩存
            nymbal_cache = {k: v for k, v in cache.items() if '777165' in k or '777181' in k}
            if nymbal_cache:
                print(f"\nNYM @ BAL 相關緩存:")
                for key, value in nymbal_cache.items():
                    print(f"  {key}: {value}")
            else:
                print(f"\n❌ 沒有找到NYM @ BAL相關緩存")
        else:
            print("❌ 特徵工程器沒有投手緩存")

def test_fixed_predictions():
    """測試修復後的預測系統"""
    app = create_app()

    with app.app_context():
        print("\n🔧 測試修復後的預測系統")
        print("=" * 60)

        from models.improved_predictor import ImprovedMLBPredictor

        predictor = ImprovedMLBPredictor()

        # 測試NYM @ BAL預測
        print("\n🎯 測試 NYM @ BAL 預測:")
        try:
            # 模擬兩場不同的比賽
            result1 = predictor.predict_game('BAL', 'NYM', date(2025, 7, 10))
            print(f"  預測1: {result1['predicted_away_score']:.1f} - {result1['predicted_home_score']:.1f}")
            print(f"  總分: {result1['total_runs']:.1f}, 信心度: {result1['confidence']:.1%}")

        except Exception as e:
            print(f"  ❌ 預測失敗: {e}")

        # 測試TB @ BOS預測
        print("\n🎯 測試 TB @ BOS 預測:")
        try:
            result2 = predictor.predict_game('BOS', 'TB', date(2025, 7, 10))
            print(f"  預測: {result2['predicted_away_score']:.1f} - {result2['predicted_home_score']:.1f}")
            print(f"  總分: {result2['total_runs']:.1f}, 信心度: {result2['confidence']:.1%}")

            if result2['total_runs'] > 15:
                print(f"  ⚠️  仍然是極端預測！")
            else:
                print(f"  ✅ 預測結果合理")

        except Exception as e:
            print(f"  ❌ 預測失敗: {e}")

if __name__ == "__main__":
    debug_nymbal_games()
    debug_tb_bos_game()
    check_pitcher_cache()
    test_fixed_predictions()
