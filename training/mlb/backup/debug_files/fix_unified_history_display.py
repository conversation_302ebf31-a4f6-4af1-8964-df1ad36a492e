#!/usr/bin/env python3
"""
修復unified/history頁面顯示問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from datetime import datetime, date, timed<PERSON><PERSON>

def check_unified_history_data():
    """檢查unified/history頁面的數據"""
    print("🔍 檢查unified/history頁面數據")
    print("=" * 60)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查最近幾天的預測記錄
        print("1️⃣  檢查預測記錄:")
        cursor.execute("""
            SELECT 
                g.date,
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN p.is_correct IS NOT NULL THEN 1 END) as with_results,
                COUNT(CASE WHEN p.is_correct = 1 THEN 1 END) as correct_predictions,
                AVG(p.over_under_line) as avg_line
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-07-08' AND g.date <= '2025-07-10'
            AND p.model_version = 'unified_v1.0'
            GROUP BY g.date
            ORDER BY g.date DESC
        """)
        
        results = cursor.fetchall()
        
        print("日期       | 總預測 | 有結果 | 正確數 | 平均盤口")
        print("-" * 50)
        
        for date_str, total, with_results, correct, avg_line in results:
            accuracy = (correct / with_results * 100) if with_results > 0 else 0
            print(f"{date_str} | {total:6d} | {with_results:6d} | {correct:6d} | {avg_line:8.1f}")
        
        print()
        
        # 檢查具體的預測記錄
        print("2️⃣  檢查具體預測記錄 (2025-07-09):")
        cursor.execute("""
            SELECT 
                g.away_team || '@' || g.home_team as matchup,
                p.predicted_total_runs,
                p.over_under_line,
                g.home_score,
                g.away_score,
                (g.home_score + g.away_score) as actual_total,
                p.is_correct,
                p.actual_total_runs,
                p.over_under_confidence
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date = '2025-07-09'
            AND p.model_version = 'unified_v1.0'
            ORDER BY g.game_id
        """)
        
        records = cursor.fetchall()
        
        print("比賽對戰        | 預測總分 | 盤口 | 實際比分 | 實際總分 | 正確性 | 信心度")
        print("-" * 80)
        
        for matchup, pred_total, line, home_score, away_score, actual_total, is_correct, stored_actual, confidence in records:
            actual_str = f"{away_score}-{home_score}" if home_score is not None else "未完成"
            correct_str = "✅" if is_correct == 1 else "❌" if is_correct == 0 else "待確認"
            confidence_str = f"{confidence:.1f}%" if confidence else "N/A"
            
            print(f"{matchup:15s} | {pred_total:8.1f} | {line:4.1f} | {actual_str:8s} | {actual_total:8.1f} | {correct_str:6s} | {confidence_str}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()

def update_prediction_results():
    """更新預測結果"""
    print("\n🔄 更新預測結果")
    print("=" * 60)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 更新已完成比賽的預測結果
        cursor.execute("""
            UPDATE predictions 
            SET 
                actual_total_runs = (
                    SELECT g.home_score + g.away_score 
                    FROM games g 
                    WHERE g.game_id = predictions.game_id
                ),
                is_correct = CASE 
                    WHEN (
                        SELECT g.home_score + g.away_score 
                        FROM games g 
                        WHERE g.game_id = predictions.game_id
                    ) > predictions.over_under_line THEN 
                        CASE WHEN predictions.over_probability > 0.5 THEN 1 ELSE 0 END
                    ELSE 
                        CASE WHEN predictions.under_probability > 0.5 THEN 1 ELSE 0 END
                END,
                updated_at = ?
            WHERE game_id IN (
                SELECT g.game_id 
                FROM games g 
                WHERE g.date >= '2025-07-08' AND g.date <= '2025-07-10'
                AND g.home_score IS NOT NULL 
                AND g.away_score IS NOT NULL
                AND g.game_status = 'completed'
            )
            AND model_version = 'unified_v1.0'
        """, (datetime.now().isoformat(),))
        
        updated_count = cursor.rowcount
        print(f"✅ 更新了 {updated_count} 個預測結果")
        
        # 檢查更新後的統計
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN is_correct = 1 THEN 1 END) as correct,
                COUNT(CASE WHEN is_correct IS NOT NULL THEN 1 END) as with_results
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-07-08' AND g.date <= '2025-07-10'
            AND p.model_version = 'unified_v1.0'
        """)
        
        total, correct, with_results = cursor.fetchone()
        accuracy = (correct / with_results * 100) if with_results > 0 else 0
        
        print(f"📊 統計結果: {correct}/{with_results} 正確 ({accuracy:.1f}%)")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"❌ 更新失敗: {e}")
        import traceback
        traceback.print_exc()

def check_model_version_consistency():
    """檢查模型版本一致性"""
    print("\n🔍 檢查模型版本一致性")
    print("=" * 60)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查不同模型版本的記錄數
        cursor.execute("""
            SELECT 
                p.model_version,
                COUNT(*) as count,
                MIN(g.date) as earliest_date,
                MAX(g.date) as latest_date
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-07-08'
            GROUP BY p.model_version
            ORDER BY count DESC
        """)
        
        results = cursor.fetchall()
        
        print("模型版本        | 記錄數 | 最早日期   | 最晚日期")
        print("-" * 50)
        
        for version, count, earliest, latest in results:
            print(f"{version:15s} | {count:6d} | {earliest} | {latest}")
        
        # 如果有多個版本，統一為unified_v1.0
        if len(results) > 1:
            print(f"\n⚠️  發現多個模型版本，統一為 unified_v1.0...")
            
            cursor.execute("""
                UPDATE predictions 
                SET model_version = 'unified_v1.0',
                    updated_at = ?
                WHERE game_id IN (
                    SELECT g.game_id 
                    FROM games g 
                    WHERE g.date >= '2025-07-08'
                )
            """, (datetime.now().isoformat(),))
            
            unified_count = cursor.rowcount
            print(f"✅ 統一了 {unified_count} 個記錄的模型版本")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

def fix_missing_probabilities():
    """修復缺失的概率數據"""
    print("\n🔧 修復缺失的概率數據")
    print("=" * 60)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 為缺失概率的記錄設置默認值
        cursor.execute("""
            UPDATE predictions 
            SET 
                over_probability = CASE 
                    WHEN predicted_total_runs > over_under_line THEN 0.65
                    ELSE 0.35
                END,
                under_probability = CASE 
                    WHEN predicted_total_runs > over_under_line THEN 0.35
                    ELSE 0.65
                END,
                over_under_confidence = CASE 
                    WHEN ABS(predicted_total_runs - over_under_line) > 1.0 THEN 75.0
                    WHEN ABS(predicted_total_runs - over_under_line) > 0.5 THEN 65.0
                    ELSE 55.0
                END,
                updated_at = ?
            WHERE (over_probability IS NULL OR under_probability IS NULL OR over_under_confidence IS NULL)
            AND game_id IN (
                SELECT g.game_id 
                FROM games g 
                WHERE g.date >= '2025-07-08'
            )
        """, (datetime.now().isoformat(),))
        
        fixed_count = cursor.rowcount
        print(f"✅ 修復了 {fixed_count} 個記錄的概率數據")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")

if __name__ == '__main__':
    check_unified_history_data()
    check_model_version_consistency()
    fix_missing_probabilities()
    update_prediction_results()
    
    print("\n" + "=" * 60)
    print("🔄 請刷新 unified/history 頁面查看修復效果")
    print("=" * 60)
