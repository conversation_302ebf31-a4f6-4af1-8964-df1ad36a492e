#!/usr/bin/env python3
"""
簡單檢查 MLB 狀況和生成 07-02 預測
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app

def check_games_and_predictions():
    """檢查比賽和預測狀況"""
    with app.app_context():
        from models.database import db
        from models.game import Game
        from models.prediction import Prediction
        from datetime import datetime
        
        print('🏟️ MLB 比賽狀況檢查')
        print('=' * 60)

        # 檢查最近幾天的比賽
        dates_to_check = ['2025-07-01', '2025-07-02', '2025-07-03']

        for date_str in dates_to_check:
            print(f'\n📅 {date_str} 比賽狀況:')
            
            # 查詢比賽
            games = Game.query.filter(Game.game_date.like(f'{date_str}%')).all()
            
            if not games:
                print('   ❌ 沒有找到比賽記錄')
                continue
                
            # 統計比賽狀態
            status_counts = {}
            postponed_games = []
            
            for game in games:
                status = game.status_code or 'Unknown'
                status_counts[status] = status_counts.get(status, 0) + 1
                
                if status in ['P', 'PP', 'Postponed']:
                    postponed_games.append(f'{game.away_team} @ {game.home_team}')
            
            print(f'   📊 總比賽數: {len(games)}')
            for status, count in status_counts.items():
                status_name = {
                    'F': '已完成',
                    'S': '進行中', 
                    'P': '延期',
                    'PP': '延期',
                    'Postponed': '延期',
                    'Preview': '預覽',
                    'Pre-Game': '賽前',
                    'Scheduled': '已排程'
                }.get(status, status)
                print(f'   - {status_name}: {count} 場')
            
            if postponed_games:
                print(f'   🚫 延期比賽: {", ".join(postponed_games)}')

        # 檢查 07-02 預測
        print('\n' + '=' * 60)
        print('🔮 檢查 07-02 預測狀況:')
        
        predictions_0702 = Prediction.query.filter(
            Prediction.prediction_date.like('2025-07-02%')
        ).all()
        
        print(f'📊 07-02 預測總數: {len(predictions_0702)}')
        
        if predictions_0702:
            # 統計模型版本
            model_versions = {}
            for pred in predictions_0702:
                version = pred.model_version or 'Unknown'
                model_versions[version] = model_versions.get(version, 0) + 1
            
            print('📈 預測模型版本:')
            for version, count in model_versions.items():
                print(f'   - {version}: {count} 場')
            
            # 顯示前5個預測
            print('\n🎯 預測範例:')
            for i, pred in enumerate(predictions_0702[:5]):
                home_score = pred.predicted_home_score or 0
                away_score = pred.predicted_away_score or 0
                confidence = (pred.confidence or 0) * 100
                print(f'   {i+1}. {pred.game_id}: {away_score:.1f}-{home_score:.1f} (信心度: {confidence:.1f}%)')
        else:
            print('❌ 沒有 07-02 預測，嘗試生成...')
            
            # 嘗試生成預測
            try:
                from models.unified_betting_predictor import UnifiedBettingPredictor
                predictor = UnifiedBettingPredictor()
                
                # 檢查 07-02 比賽
                games_0702 = Game.query.filter(Game.game_date.like('2025-07-02%')).all()
                print(f'📅 07-02 找到 {len(games_0702)} 場比賽')
                
                if games_0702:
                    # 生成預測
                    result = predictor.generate_predictions_for_date('2025-07-02')
                    print(f'🎯 預測生成結果: {result.get("message", "完成")}')
                    print(f'📊 成功生成: {result.get("successful_predictions", 0)} 場預測')
                else:
                    print('❌ 07-02 沒有比賽可預測')
                    
            except Exception as e:
                print(f'❌ 生成預測失敗: {e}')

if __name__ == "__main__":
    print("🔍 開始檢查...")
    check_games_and_predictions()
    print('\n✅ 檢查完成！')
