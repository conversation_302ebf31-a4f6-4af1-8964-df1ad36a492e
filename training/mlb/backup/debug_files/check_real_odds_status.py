#!/usr/bin/env python3
"""
檢查真實博彩盤口數據狀態
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from models.database import db, Game, BettingOdds
from datetime import date

def check_real_odds_status():
    """檢查真實博彩盤口數據狀態"""
    print("🔍 檢查真實博彩盤口數據狀態")
    print("=" * 60)
    
    app = create_app()
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        # 1. 檢查API狀態
        print("1. 📡 API狀態檢查:")
        api_status = fetcher.check_api_status()
        print(f"   API密鑰配置: {'✅' if api_status['api_key_configured'] else '❌'}")
        print(f"   API可訪問: {'✅' if api_status['api_accessible'] else '❌'}")
        print(f"   狀態信息: {api_status['message']}")
        
        if api_status.get('remaining_requests'):
            print(f"   剩餘請求: {api_status['remaining_requests']}")
        
        # 2. 檢查數據庫中的真實盤口數據
        print(f"\n2. 🗄️ 數據庫盤口數據檢查:")
        today = date.today()
        
        # 檢查今天的比賽
        todays_games = Game.query.filter_by(date=today).all()
        print(f"   今天的比賽: {len(todays_games)} 場")
        
        # 檢查今天的盤口數據
        todays_odds = db.session.query(BettingOdds).join(Game).filter(
            Game.date == today
        ).all()
        print(f"   今天的盤口記錄: {len(todays_odds)} 條")
        
        # 分析盤口數據類型
        totals_count = len([o for o in todays_odds if o.market_type == 'totals'])
        spreads_count = len([o for o in todays_odds if o.market_type == 'spreads'])
        
        print(f"   大小分盤口: {totals_count} 條")
        print(f"   讓分盤口: {spreads_count} 條")
        
        # 3. 測試獲取今日盤口
        print(f"\n3. 🎯 測試獲取今日盤口:")
        try:
            odds_data = fetcher.get_mlb_odds_today(today)
            
            total_games = odds_data.get('summary', {}).get('total_games', 0)
            games_with_odds = odds_data.get('summary', {}).get('games_with_odds', 0)
            is_free_api = odds_data.get('summary', {}).get('is_free_api', False)
            data_source = odds_data.get('summary', {}).get('data_source', 'unknown')
            
            print(f"   獲取比賽數: {total_games}")
            print(f"   有盤口比賽: {games_with_odds}")
            print(f"   數據源: {data_source}")
            print(f"   是否免費API: {'是' if is_free_api else '否'}")
            
            if is_free_api:
                print("   ⚠️ 警告: 當前使用免費API，不提供真實博彩盤口")
            
            # 檢查具體比賽的盤口
            if odds_data.get('games'):
                print(f"\n   📊 前3場比賽盤口詳情:")
                for i, game in enumerate(odds_data['games'][:3]):
                    home_team = game.get('home_team', 'Unknown')
                    away_team = game.get('away_team', 'Unknown')
                    odds = game.get('odds', {})
                    
                    print(f"   比賽 {i+1}: {away_team} @ {home_team}")
                    
                    # 檢查大小分
                    total_odds = odds.get('total', {})
                    if total_odds.get('line'):
                        print(f"     大小分線: {total_odds['line']} (真實盤口)")
                    else:
                        print(f"     大小分線: 無真實數據")
                    
                    # 檢查讓分盤
                    run_line = odds.get('run_line', {})
                    if run_line.get('home_line') is not None:
                        print(f"     讓分盤: {run_line['home_line']} (真實盤口)")
                    else:
                        print(f"     讓分盤: 無真實數據")
                        
        except Exception as e:
            print(f"   ❌ 獲取失敗: {e}")
        
        # 4. 測試特定比賽盤口獲取
        print(f"\n4. 🏟️ 測試特定比賽盤口獲取:")
        if todays_games:
            test_game = todays_games[0]
            print(f"   測試比賽: {test_game.away_team} @ {test_game.home_team}")
            
            try:
                game_odds = fetcher.get_game_odds_by_teams(
                    test_game.home_team, 
                    test_game.away_team, 
                    today
                )
                
                if game_odds:
                    print(f"   ✅ 成功獲取盤口數據")
                    
                    # 檢查大小分
                    if 'totals' in game_odds:
                        total_point = game_odds['totals'].get('total_point')
                        print(f"   大小分線: {total_point} (來源: 數據庫)")
                    elif 'total' in game_odds:
                        total_line = game_odds['total'].get('line')
                        print(f"   大小分線: {total_line} (來源: API)")
                    else:
                        print(f"   大小分線: 無數據")
                    
                    # 檢查讓分盤
                    if 'run_line' in game_odds:
                        home_line = game_odds['run_line'].get('home_line')
                        print(f"   讓分盤: {home_line} (來源: 數據庫/API)")
                    else:
                        print(f"   讓分盤: 無數據")
                        
                else:
                    print(f"   ❌ 無法獲取盤口數據")
                    
            except Exception as e:
                print(f"   ❌ 獲取失敗: {e}")
        
        # 5. 總結和建議
        print(f"\n5. 📋 總結和建議:")
        
        if not api_status['api_key_configured']:
            print("   ❌ 問題: 未配置博彩API密鑰")
            print("   💡 建議: 在 models/odds-api.txt 中添加有效的API密鑰")
        
        if not api_status['api_accessible']:
            print("   ❌ 問題: API無法訪問")
            print("   💡 建議: 檢查API密鑰是否有效，或網絡連接")
        
        if totals_count == 0 and spreads_count == 0:
            print("   ❌ 問題: 數據庫中沒有真實盤口數據")
            print("   💡 建議: 運行歷史盤口數據下載")
        
        if is_free_api:
            print("   ⚠️ 問題: 當前使用免費API，不提供博彩盤口")
            print("   💡 建議: 配置付費API或使用網頁抓取器")
        
        print(f"\n🎯 結論:")
        if api_status['api_accessible'] and totals_count > 0:
            print("   ✅ 系統可以獲取真實博彩盤口數據")
        else:
            print("   ❌ 系統目前無法獲取真實博彩盤口數據")
            print("   📝 Web界面顯示的大小分數據可能是模擬生成的")

def main():
    check_real_odds_status()

if __name__ == "__main__":
    main()
