#!/usr/bin/env python3
"""
修復缺失分數的數據質量問題
使用MLB官方API和多個數據源補充缺失的比賽分數
"""

import sqlite3
import requests
import time
import json
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MissingScoresFixer:
    """缺失分數修復器"""
    
    def __init__(self, db_path='instance/mlb_data.db'):
        self.db_path = db_path
        self.fixed_count = 0
        self.failed_count = 0
        
    def fix_all_missing_scores(self):
        """修復所有缺失的分數"""
        print("🔧 開始修復缺失分數數據")
        print("=" * 60)
        
        # 獲取缺失分數的比賽
        missing_games = self._get_missing_score_games()
        total_missing = len(missing_games)
        
        if total_missing == 0:
            print("✅ 沒有發現缺失分數的比賽")
            return
        
        print(f"📊 發現 {total_missing} 場比賽缺失分數")
        print(f"🎯 開始修復...")
        
        for i, game in enumerate(missing_games, 1):
            game_id, game_date, home_team, away_team = game
            
            print(f"\\n[{i}/{total_missing}] 修復: {game_date} {away_team} @ {home_team}")
            
            # 嘗試多種方法獲取分數
            score_data = self._fetch_game_score(game_date, home_team, away_team)
            
            if score_data:
                # 更新數據庫
                if self._update_game_score(game_id, score_data):
                    self.fixed_count += 1
                    print(f"✅ 修復成功: {score_data['away_score']}-{score_data['home_score']}")
                else:
                    self.failed_count += 1
                    print("❌ 數據庫更新失敗")
            else:
                self.failed_count += 1
                print("❌ 無法獲取分數數據")
            
            # 添加延遲避免API限制
            time.sleep(1)
        
        # 總結
        print(f"\\n🎉 修復完成!")
        print(f"✅ 成功修復: {self.fixed_count} 場")
        print(f"❌ 修復失敗: {self.failed_count} 場")
        print(f"📈 修復率: {self.fixed_count/total_missing*100:.1f}%")
        
        if self.fixed_count > 0:
            self._verify_fixes()
    
    def _get_missing_score_games(self) -> List[tuple]:
        """獲取缺失分數的比賽"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT game_id, date, home_team, away_team
            FROM games 
            WHERE game_status = "completed" 
            AND (home_score IS NULL OR away_score IS NULL)
            ORDER BY date DESC
            LIMIT 100
        ''')
        
        games = cursor.fetchall()
        conn.close()
        return games
    
    def _fetch_game_score(self, game_date: str, home_team: str, away_team: str) -> Optional[Dict]:
        """獲取比賽分數 - 多種數據源"""
        
        # 方法1: 使用MLB Stats API
        try:
            score_data = self._fetch_from_mlb_stats_api(game_date, home_team, away_team)
            if score_data:
                return score_data
        except Exception as e:
            logger.debug(f"MLB Stats API失敗: {e}")
        
        # 方法2: 使用ESPN API
        try:
            score_data = self._fetch_from_espn_api(game_date, home_team, away_team)
            if score_data:
                return score_data
        except Exception as e:
            logger.debug(f"ESPN API失敗: {e}")
        
        # 方法3: 使用現有的爬蟲數據
        try:
            score_data = self._fetch_from_local_data(game_date, home_team, away_team)
            if score_data:
                return score_data
        except Exception as e:
            logger.debug(f"本地數據查詢失敗: {e}")
        
        return None
    
    def _fetch_from_mlb_stats_api(self, game_date: str, home_team: str, away_team: str) -> Optional[Dict]:
        """從MLB Stats API獲取分數"""
        try:
            # 將日期轉換為MLB API格式
            date_obj = datetime.strptime(game_date, '%Y-%m-%d')
            api_date = date_obj.strftime('%Y-%m-%d')
            
            # MLB Stats API endpoint
            url = f"https://statsapi.mlb.com/api/v1/schedule"
            params = {
                'date': api_date,
                'sportId': 1,
                'hydrate': 'game(content(editorial(recap))),linescore,team'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # 查找匹配的比賽
            for game_date_info in data.get('dates', []):
                for game in game_date_info.get('games', []):
                    teams = game.get('teams', {})
                    home = teams.get('home', {}).get('team', {}).get('abbreviation', '')
                    away = teams.get('away', {}).get('team', {}).get('abbreviation', '')
                    
                    # 檢查球隊名稱是否匹配
                    if self._teams_match(home, home_team) and self._teams_match(away, away_team):
                        # 檢查比賽是否完成
                        status = game.get('status', {}).get('detailedState', '')
                        if 'Final' in status:
                            home_score = teams.get('home', {}).get('score')
                            away_score = teams.get('away', {}).get('score')
                            
                            if home_score is not None and away_score is not None:
                                return {
                                    'home_score': int(home_score),
                                    'away_score': int(away_score),
                                    'source': 'mlb_stats_api'
                                }
            
            return None
            
        except Exception as e:
            logger.debug(f"MLB Stats API錯誤: {e}")
            return None
    
    def _fetch_from_espn_api(self, game_date: str, home_team: str, away_team: str) -> Optional[Dict]:
        """從ESPN API獲取分數"""
        try:
            # ESPN API endpoint
            date_obj = datetime.strptime(game_date, '%Y-%m-%d')
            espn_date = date_obj.strftime('%Y%m%d')
            
            url = f"https://site.api.espn.com/apis/site/v2/sports/baseball/mlb/scoreboard"
            params = {'dates': espn_date}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # 查找匹配的比賽
            for event in data.get('events', []):
                competitions = event.get('competitions', [])
                for competition in competitions:
                    competitors = competition.get('competitors', [])
                    
                    if len(competitors) == 2:
                        home_comp = next((c for c in competitors if c.get('homeAway') == 'home'), None)
                        away_comp = next((c for c in competitors if c.get('homeAway') == 'away'), None)
                        
                        if home_comp and away_comp:
                            home_abbr = home_comp.get('team', {}).get('abbreviation', '')
                            away_abbr = away_comp.get('team', {}).get('abbreviation', '')
                            
                            if self._teams_match(home_abbr, home_team) and self._teams_match(away_abbr, away_team):
                                # 檢查比賽狀態
                                status = competition.get('status', {}).get('type', {}).get('name', '')
                                if status == 'STATUS_FINAL':
                                    home_score = home_comp.get('score')
                                    away_score = away_comp.get('score')
                                    
                                    if home_score is not None and away_score is not None:
                                        return {
                                            'home_score': int(home_score),
                                            'away_score': int(away_score),
                                            'source': 'espn_api'
                                        }
            
            return None
            
        except Exception as e:
            logger.debug(f"ESPN API錯誤: {e}")
            return None
    
    def _fetch_from_local_data(self, game_date: str, home_team: str, away_team: str) -> Optional[Dict]:
        """從本地其他數據源獲取分數"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查詢是否有其他相似的記錄
            cursor.execute('''
                SELECT home_score, away_score 
                FROM games 
                WHERE date = ? 
                AND (home_team LIKE ? OR home_team = ?)
                AND (away_team LIKE ? OR away_team = ?)
                AND home_score IS NOT NULL 
                AND away_score IS NOT NULL
                LIMIT 1
            ''', (game_date, f'%{home_team}%', home_team, f'%{away_team}%', away_team))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'home_score': result[0],
                    'away_score': result[1],
                    'source': 'local_data'
                }
            
            return None
            
        except Exception as e:
            logger.debug(f"本地數據查詢錯誤: {e}")
            return None
    
    def _teams_match(self, api_team: str, db_team: str) -> bool:
        """檢查球隊名稱是否匹配"""
        if not api_team or not db_team:
            return False
        
        # 標準化比較
        api_team = api_team.upper().strip()
        db_team = db_team.upper().strip()
        
        # 直接匹配
        if api_team == db_team:
            return True
        
        # 球隊名稱映射
        team_mappings = {
            'LAA': ['LA ANGELS', 'ANGELS'],
            'LAD': ['LA DODGERS', 'DODGERS'],
            'NYY': ['NY YANKEES', 'YANKEES'],
            'NYM': ['NY METS', 'METS'],
            'CWS': ['CHI WHITE SOX', 'WHITE SOX'],
            'CHC': ['CHI CUBS', 'CUBS'],
            'TB': ['TAMPA BAY', 'RAYS'],
            'SF': ['SAN FRANCISCO', 'GIANTS'],
            'SD': ['SAN DIEGO', 'PADRES'],
            'WSH': ['WASHINGTON', 'NATIONALS'],
            'AZ': ['ARIZONA', 'DIAMONDBACKS']
        }
        
        # 檢查映射
        for abbr, full_names in team_mappings.items():
            if (api_team == abbr and db_team in full_names) or (db_team == abbr and api_team in full_names):
                return True
            if api_team in full_names and db_team in full_names:
                return True
        
        return False
    
    def _update_game_score(self, game_id: int, score_data: Dict) -> bool:
        """更新數據庫中的比賽分數"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE games 
                SET home_score = ?, away_score = ?
                WHERE game_id = ?
            ''', (score_data['home_score'], score_data['away_score'], game_id))
            
            conn.commit()
            success = cursor.rowcount > 0
            conn.close()
            
            return success
            
        except Exception as e:
            logger.error(f"更新數據庫失敗: {e}")
            return False
    
    def _verify_fixes(self):
        """驗證修復結果"""
        print(f"\\n🔍 驗證修復結果...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 重新檢查缺失分數的比例
        cursor.execute('''
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN home_score IS NULL OR away_score IS NULL THEN 1 ELSE 0 END) as missing
            FROM games 
            WHERE game_status = "completed"
        ''')
        
        result = cursor.fetchone()
        total, missing = result
        missing_percent = missing / total * 100 if total > 0 else 0
        
        print(f"📊 修復後統計:")
        print(f"   總完成比賽: {total}")
        print(f"   仍缺失分數: {missing} ({missing_percent:.1f}%)")
        print(f"   改進幅度: {12.2 - missing_percent:.1f}%")
        
        conn.close()

def main():
    """主函數"""
    fixer = MissingScoresFixer()
    fixer.fix_all_missing_scores()

if __name__ == "__main__":
    main()