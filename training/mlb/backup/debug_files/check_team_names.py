#!/usr/bin/env python3
"""
檢查數據庫中的球隊名稱格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, timedelta
from models.database import db, Game
from app import create_app
from sqlalchemy import distinct

def check_team_names():
    """檢查數據庫中的球隊名稱"""
    print("=== 檢查數據庫中的球隊名稱格式 ===")
    
    # 創建Flask應用上下文
    app = create_app()
    
    with app.app_context():
        # 檢查2024年7月的比賽
        start_date = date(2024, 7, 1)
        end_date = date(2024, 7, 31)
        
        print(f"檢查日期範圍: {start_date} 到 {end_date}")
        
        # 查詢該時間範圍內的比賽
        games = Game.query.filter(
            Game.date >= start_date,
            Game.date <= end_date
        ).limit(50).all()
        
        print(f"找到 {len(games)} 場比賽")
        
        if games:
            print("\n=== 前10場比賽的球隊名稱格式 ===")
            for i, game in enumerate(games[:10]):
                print(f"{i+1}. {game.date}: {game.away_team} @ {game.home_team}")
        
        # 獲取所有不同的客隊名稱
        away_teams = db.session.query(distinct(Game.away_team)).filter(
            Game.date >= start_date,
            Game.date <= end_date
        ).all()
        
        print(f"\n=== 所有客隊名稱 ({len(away_teams)} 個) ===")
        for team in sorted([t[0] for t in away_teams]):
            print(f"  {team}")
        
        # 獲取所有不同的主隊名稱
        home_teams = db.session.query(distinct(Game.home_team)).filter(
            Game.date >= start_date,
            Game.date <= end_date
        ).all()
        
        print(f"\n=== 所有主隊名稱 ({len(home_teams)} 個) ===")
        for team in sorted([t[0] for t in home_teams]):
            print(f"  {team}")
        
        # 檢查特定日期的比賽
        target_date = date(2024, 7, 7)
        games_on_date = Game.query.filter(Game.date == target_date).all()
        
        print(f"\n=== {target_date} 的比賽 ({len(games_on_date)} 場) ===")
        for game in games_on_date:
            print(f"  {game.away_team} @ {game.home_team}")

if __name__ == "__main__":
    check_team_names()
