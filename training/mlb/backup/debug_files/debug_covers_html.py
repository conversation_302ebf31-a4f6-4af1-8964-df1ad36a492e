#!/usr/bin/env python3
"""
調試covers.com HTML結構
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.covers_scraper import CoversMLBScraper
from datetime import date
import requests
from bs4 import BeautifulSoup

def debug_covers_html():
    """調試covers.com的HTML結構"""
    print("=== 調試 Covers.com HTML 結構 ===")
    
    scraper = CoversMLBScraper()
    
    # 測試日期：2024年7月7日
    test_date = date(2024, 7, 7)
    date_str = test_date.strftime('%Y-%m-%d')
    url = f"https://www.covers.com/sports/mlb/matchups?selectedDate={date_str}"
    
    print(f"測試URL: {url}")
    
    try:
        # 發送請求
        response = scraper.session.get(url, timeout=30)
        response.raise_for_status()
        
        print(f"響應狀態碼: {response.status_code}")
        print(f"響應內容長度: {len(response.content)}")
        
        # 解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 獲取文本內容
        text_content = soup.get_text()
        
        # 保存完整文本到文件以供分析
        with open('covers_debug_text.txt', 'w', encoding='utf-8') as f:
            f.write(text_content)
        
        print("完整文本已保存到 covers_debug_text.txt")
        
        # 查找包含 "Final" 的行
        lines = text_content.split('\n')
        final_lines = []
        
        for i, line in enumerate(lines):
            if 'Final' in line:
                final_lines.append((i, line.strip()))
                
        print(f"\n找到 {len(final_lines)} 行包含 'Final':")
        for line_num, line in final_lines[:10]:  # 只顯示前10行
            print(f"  行 {line_num}: {line}")
            
        # 查找包含 "@" 的行
        at_lines = []
        for i, line in enumerate(lines):
            if '@' in line and line.strip():
                at_lines.append((i, line.strip()))
                
        print(f"\n找到 {len(at_lines)} 行包含 '@':")
        for line_num, line in at_lines[:10]:  # 只顯示前10行
            print(f"  行 {line_num}: {line}")
            
        # 查找包含球隊名稱的行
        team_lines = []
        team_names = ['Yankees', 'Dodgers', 'Red Sox', 'Cubs', 'Angels']
        
        for i, line in enumerate(lines):
            for team in team_names:
                if team.lower() in line.lower():
                    team_lines.append((i, line.strip()))
                    break
                    
        print(f"\n找到 {len(team_lines)} 行包含球隊名稱:")
        for line_num, line in team_lines[:10]:  # 只顯示前10行
            print(f"  行 {line_num}: {line}")
            
        # 查找包含分數的行
        score_lines = []
        import re
        
        for i, line in enumerate(lines):
            # 查找數字模式
            if re.search(r'\d+', line) and line.strip():
                score_lines.append((i, line.strip()))
                
        print(f"\n找到 {len(score_lines)} 行包含數字:")
        for line_num, line in score_lines[:20]:  # 只顯示前20行
            print(f"  行 {line_num}: {line}")
            
    except Exception as e:
        print(f"調試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_covers_html()
