#!/usr/bin/env python3
"""
智能預測系統演示
展示如何根據投手質量自動選擇預測策略
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game
from models.intelligent_predictor import IntelligentPredictor

def demo_intelligent_prediction():
    """演示智能預測系統的核心功能"""
    print("🧠 智能預測系統演示")
    print("=" * 80)
    print("這個系統會自動:")
    print("1. 分析投手質量 (王牌/優秀/普通/弱勢)")
    print("2. 判斷對戰類型 (王牌對決/打擊戰/強弱對戰/普通對戰)")
    print("3. 自動選擇預測策略")
    print("4. 調整預測結果")
    print()
    
    app = create_app()
    
    with app.app_context():
        predictor = IntelligentPredictor()
        
        # 演示不同的投手組合場景
        demo_scenarios = [
            {
                'title': '🔥 王牌對決場景',
                'description': '兩位王牌投手對決 → 應該預測低分比賽',
                'pitcher_analysis': {
                    'home_pitcher': {'name': '<PERSON><PERSON><PERSON> Cole', 'era': 2.20, 'quality': 85, 'strength': '王牌'},
                    'away_pitcher': {'name': 'Shane Bieber', 'era': 2.50, 'quality': 82, 'strength': '王牌'},
                    'matchup_type': '王牌對決',
                    'average_era': 2.35,
                    'average_quality': 83.5
                }
            },
            {
                'title': '💥 打擊戰場景',
                'description': '兩位弱勢投手 → 應該預測高分比賽',
                'pitcher_analysis': {
                    'home_pitcher': {'name': 'Weak Pitcher A', 'era': 5.50, 'quality': 30, 'strength': '弱勢'},
                    'away_pitcher': {'name': 'Weak Pitcher B', 'era': 6.00, 'quality': 25, 'strength': '弱勢'},
                    'matchup_type': '打擊戰',
                    'average_era': 5.75,
                    'average_quality': 27.5
                }
            },
            {
                'title': '⚖️ 強弱對戰場景',
                'description': '一強一弱投手 → 應該預測不平衡比分',
                'pitcher_analysis': {
                    'home_pitcher': {'name': 'Good Pitcher', 'era': 2.80, 'quality': 78, 'strength': '優秀'},
                    'away_pitcher': {'name': 'Poor Pitcher', 'era': 5.20, 'quality': 35, 'strength': '弱勢'},
                    'matchup_type': '強弱對戰',
                    'average_era': 4.00,
                    'average_quality': 56.5
                }
            },
            {
                'title': '📊 普通對戰場景 (CHC @ NYY 實例)',
                'description': '普通投手對戰 → 使用標準預測',
                'pitcher_analysis': {
                    'home_pitcher': {'name': 'Will Warren', 'era': 10.32, 'quality': 22, 'strength': '弱勢'},
                    'away_pitcher': {'name': 'Shota Imanaga', 'era': 4.50, 'quality': 64, 'strength': '普通'},
                    'matchup_type': '普通對戰',
                    'average_era': 7.41,
                    'average_quality': 43.0
                }
            }
        ]
        
        for i, scenario in enumerate(demo_scenarios, 1):
            print(f"\n{scenario['title']}")
            print("-" * 60)
            print(f"📝 {scenario['description']}")
            
            # 分析投手
            pitcher_analysis = scenario['pitcher_analysis']
            print(f"\n📊 投手分析:")
            print(f"   主隊: {pitcher_analysis['home_pitcher']['name']} "
                  f"(ERA: {pitcher_analysis['home_pitcher']['era']:.2f}, "
                  f"等級: {pitcher_analysis['home_pitcher']['strength']})")
            print(f"   客隊: {pitcher_analysis['away_pitcher']['name']} "
                  f"(ERA: {pitcher_analysis['away_pitcher']['era']:.2f}, "
                  f"等級: {pitcher_analysis['away_pitcher']['strength']})")
            print(f"   對戰類型: {pitcher_analysis['matchup_type']}")
            
            # 確定策略
            strategy = predictor._determine_prediction_strategy(pitcher_analysis)
            print(f"\n🧠 智能策略選擇:")
            print(f"   策略名稱: {strategy['name']}")
            print(f"   策略類型: {strategy['type']}")
            print(f"   目標總分: {strategy['target_total']}分")
            print(f"   策略說明: {strategy['description']}")
            
            # 模擬預測調整
            base_prediction = {
                'predicted_home_score': 5.0,
                'predicted_away_score': 4.5,
                'total_runs': 9.5,
                'confidence': 0.6,
                'home_win_probability': 0.52
            }
            
            adjusted_prediction = predictor._adjust_prediction_by_strategy(
                base_prediction, strategy
            )
            
            print(f"\n📈 預測結果:")
            print(f"   基礎預測: {base_prediction['predicted_away_score']:.1f} - {base_prediction['predicted_home_score']:.1f} (總分: {base_prediction['total_runs']:.1f})")
            print(f"   智能調整: {adjusted_prediction['predicted_away_score']:.1f} - {adjusted_prediction['predicted_home_score']:.1f} (總分: {adjusted_prediction['total_runs']:.1f})")
            print(f"   信心度: {adjusted_prediction['confidence']:.1%}")
            
            # 策略合理性檢查
            analyze_strategy_effectiveness(pitcher_analysis, strategy, adjusted_prediction)

def analyze_strategy_effectiveness(pitcher_analysis: dict, strategy: dict, prediction: dict):
    """分析策略有效性"""
    print(f"\n🔍 策略有效性分析:")
    
    matchup_type = pitcher_analysis['matchup_type']
    predicted_total = prediction['total_runs']
    
    if matchup_type == "王牌對決":
        if predicted_total <= 8.0:
            print(f"   ✅ 策略有效: 王牌對決成功預測低分 ({predicted_total:.1f}分 ≤ 8.0分)")
        else:
            print(f"   ❌ 策略失效: 王牌對決但預測高分 ({predicted_total:.1f}分 > 8.0分)")
            
    elif matchup_type == "打擊戰":
        if predicted_total >= 11.0:
            print(f"   ✅ 策略有效: 打擊戰成功預測高分 ({predicted_total:.1f}分 ≥ 11.0分)")
        else:
            print(f"   ❌ 策略失效: 打擊戰但預測低分 ({predicted_total:.1f}分 < 11.0分)")
            
    elif matchup_type == "強弱對戰":
        if 8.0 <= predicted_total <= 11.0:
            print(f"   ✅ 策略有效: 強弱對戰預測中等分數 ({predicted_total:.1f}分)")
        else:
            print(f"   ⚠️  策略調整: 強弱對戰預測極端分數 ({predicted_total:.1f}分)")
            
    else:  # 普通對戰
        if 8.0 <= predicted_total <= 11.0:
            print(f"   ✅ 策略有效: 普通對戰預測標準分數 ({predicted_total:.1f}分)")
        else:
            print(f"   ⚠️  策略調整: 普通對戰預測非標準分數 ({predicted_total:.1f}分)")

def demo_real_game_prediction():
    """演示真實比賽的智能預測"""
    print(f"\n\n🎯 真實比賽智能預測演示")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        predictor = IntelligentPredictor()
        
        # 查找 CHC @ NYY 比賽
        game = Game.query.filter_by(game_id='777122').first()
        
        if game:
            print(f"📅 比賽: {game.away_team} @ {game.home_team} ({game.date})")
            if game.away_score is not None and game.home_score is not None:
                print(f"🏆 實際比分: {game.away_score} - {game.home_score} (總分: {game.away_score + game.home_score})")
            else:
                print(f"⏳ 比賽尚未完成或數據未更新")
            
            try:
                # 執行智能預測
                result = predictor.predict_game_intelligent(
                    home_team=game.home_team,
                    away_team=game.away_team,
                    game_date=game.date,
                    game_id='777122'
                )
                
                print(f"\n{result['explanation']}")
                
                # 如果有實際結果，比較準確性
                if game.away_score is not None and game.home_score is not None:
                    actual_total = game.away_score + game.home_score
                    predicted_total = result['total_runs']
                    diff = abs(predicted_total - actual_total)
                    
                    print(f"\n🎯 預測準確性:")
                    print(f"   實際總分: {actual_total}分")
                    print(f"   預測總分: {predicted_total:.1f}分")
                    print(f"   差異: {diff:.1f}分")
                    
                    if diff <= 2.0:
                        print(f"   ✅ 預測非常準確!")
                    elif diff <= 4.0:
                        print(f"   ⚠️  預測尚可")
                    else:
                        print(f"   ❌ 預測偏差較大")
                
            except Exception as e:
                print(f"❌ 智能預測失敗: {e}")
        else:
            print(f"❌ 找不到比賽 ID: 777122")

if __name__ == "__main__":
    demo_intelligent_prediction()
    demo_real_game_prediction()
