#!/usr/bin/env python3
"""
調試球隊查找問題
"""

import sqlite3

def debug_team_lookup():
    """調試球隊查找"""
    print("🔍 調試球隊查找問題")
    print("=" * 40)
    
    # 直接使用SQLite查詢
    conn = sqlite3.connect('instance/mlb_data.db')
    cursor = conn.cursor()
    
    # 1. 檢查teams表
    print("📊 Teams表數據:")
    cursor.execute("SELECT team_id, team_code, team_name FROM teams ORDER BY team_code LIMIT 10;")
    teams = cursor.fetchall()
    for team in teams:
        print(f"  {team[1]} (ID:{team[0]}) - {team[2]}")
    
    # 2. 檢查team_stats表
    print(f"\n📈 TeamStats表數據:")
    cursor.execute("SELECT COUNT(*) FROM team_stats;")
    count = cursor.fetchone()[0]
    print(f"總記錄數: {count}")
    
    if count > 0:
        cursor.execute("SELECT team_id, runs_scored, era, wins, losses FROM team_stats LIMIT 5;")
        stats = cursor.fetchall()
        for stat in stats:
            print(f"  team_id:{stat[0]} - 得分:{stat[1]:.2f}, ERA:{stat[2]:.2f}, 勝:{stat[3]}, 負:{stat[4]}")
    
    # 3. 檢查特定球隊的統計
    test_teams = ['LAD', 'NYY', 'OAK', 'MIA', 'HOU']
    print(f"\n🏟️ 測試球隊統計:")
    for team_code in test_teams:
        cursor.execute("""
            SELECT t.team_id, t.team_code, t.team_name, 
                   ts.runs_scored, ts.era, ts.wins, ts.losses
            FROM teams t 
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id 
            WHERE t.team_code = ?
        """, (team_code,))
        
        result = cursor.fetchone()
        if result:
            team_id, code, name, runs, era, wins, losses = result
            if runs is not None:
                print(f"  {code}: ✅ ID:{team_id}, 得分:{runs:.2f}, ERA:{era:.2f}")
            else:
                print(f"  {code}: ❌ ID:{team_id}, 無統計數據")
        else:
            print(f"  {code}: ❌ 找不到球隊")
    
    # 4. 檢查最近的預測
    print(f"\n🎯 最近預測:")
    cursor.execute("""
        SELECT p.predicted_away_score, p.predicted_home_score, 
               g.away_team, g.home_team, g.date
        FROM predictions p 
        JOIN games g ON p.game_id = g.game_id 
        ORDER BY g.date DESC 
        LIMIT 5;
    """)
    predictions = cursor.fetchall()
    for pred in predictions:
        print(f"  {pred[2]} @ {pred[3]}: {pred[0]} - {pred[1]} ({pred[4]})")
    
    conn.close()

if __name__ == "__main__":
    debug_team_lookup()
