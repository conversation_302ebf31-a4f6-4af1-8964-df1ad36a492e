#!/usr/bin/env python3
"""
檢查2025賽季比賽數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime
from models.database import db, Game, BettingOdds
from app import create_app
from sqlalchemy import func, and_

def check_2025_games():
    """檢查2025賽季比賽數據"""
    print("=== 檢查2025賽季比賽數據 ===")
    
    app = create_app()
    
    with app.app_context():
        # 查詢2025年的所有比賽
        games_2025 = Game.query.filter(
            Game.date >= date(2025, 1, 1),
            Game.date < date(2026, 1, 1)
        ).order_by(Game.date).all()
        
        print(f"2025賽季總比賽數: {len(games_2025)}")
        
        if games_2025:
            # 按月份統計
            monthly_stats = {}
            for game in games_2025:
                month_key = f"{game.date.year}-{game.date.month:02d}"
                if month_key not in monthly_stats:
                    monthly_stats[month_key] = 0
                monthly_stats[month_key] += 1
            
            print("\n=== 按月份統計 ===")
            for month, count in sorted(monthly_stats.items()):
                print(f"  {month}: {count} 場比賽")
            
            # 檢查日期範圍
            earliest_game = min(games_2025, key=lambda x: x.date)
            latest_game = max(games_2025, key=lambda x: x.date)
            
            print(f"\n=== 日期範圍 ===")
            print(f"最早比賽: {earliest_game.date} ({earliest_game.away_team} @ {earliest_game.home_team})")
            print(f"最晚比賽: {latest_game.date} ({latest_game.away_team} @ {latest_game.home_team})")
            
            # 檢查已有賠率數據
            games_with_odds = db.session.query(Game).join(BettingOdds).filter(
                Game.date >= date(2025, 1, 1),
                Game.date < date(2026, 1, 1),
                BettingOdds.bookmaker == 'covers.com'
            ).distinct().all()
            
            print(f"\n=== 賠率數據統計 ===")
            print(f"已有賠率數據的比賽: {len(games_with_odds)}")
            print(f"缺少賠率數據的比賽: {len(games_2025) - len(games_with_odds)}")
            
            # 顯示前10場比賽
            print(f"\n=== 前10場比賽 ===")
            for i, game in enumerate(games_2025[:10]):
                has_odds = any(g.game_id == game.game_id for g in games_with_odds)
                odds_status = "✅" if has_odds else "❌"
                print(f"  {i+1}. {game.date}: {game.away_team} @ {game.home_team} {odds_status}")
            
            # 檢查已完成的比賽（有比分的）
            completed_games = [g for g in games_2025 if g.away_score is not None and g.home_score is not None]
            print(f"\n=== 比賽狀態 ===")
            print(f"已完成比賽: {len(completed_games)}")
            print(f"未完成比賽: {len(games_2025) - len(completed_games)}")
            
            if completed_games:
                print(f"\n=== 最近完成的5場比賽 ===")
                recent_completed = sorted(completed_games, key=lambda x: x.date, reverse=True)[:5]
                for game in recent_completed:
                    has_odds = any(g.game_id == game.game_id for g in games_with_odds)
                    odds_status = "✅" if has_odds else "❌"
                    print(f"  {game.date}: {game.away_team} {game.away_score} - {game.home_score} {game.home_team} {odds_status}")
        
        else:
            print("未找到2025賽季比賽數據")

if __name__ == "__main__":
    check_2025_games()
