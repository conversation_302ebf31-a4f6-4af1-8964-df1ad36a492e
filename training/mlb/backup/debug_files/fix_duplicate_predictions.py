#!/usr/bin/env python3
"""
修復重複預測和數據保存問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction
from datetime import date
from sqlalchemy import text, func

def check_duplicate_games():
    """檢查重複的比賽記錄"""
    print("🔍 檢查重複的比賽記錄...")
    
    app = create_app()
    with app.app_context():
        # 查找重複的比賽 (相同日期、相同隊伍)
        query = text("""
            SELECT home_team, away_team, date, COUNT(*) as count, 
                   GROUP_CONCAT(game_id) as game_ids
            FROM games 
            WHERE date >= '2025-07-10'
            GROUP BY home_team, away_team, date 
            HAVING COUNT(*) > 1
            ORDER BY date DESC, count DESC
        """)
        
        results = db.session.execute(query).fetchall()
        
        print(f"📊 找到 {len(results)} 組重複比賽:")
        print("-" * 80)
        print("比賽                    日期        重複數  Game IDs")
        print("-" * 80)
        
        for row in results:
            home_team, away_team, game_date, count, game_ids = row
            print(f"{away_team:3} @ {home_team:3}           {game_date}     {count:2}      {game_ids}")
        
        return results

def check_duplicate_predictions():
    """檢查重複的預測記錄"""
    print(f"\n🎯 檢查重複的預測記錄...")
    
    app = create_app()
    with app.app_context():
        # 查找重複的預測 (相同game_id的多個預測)
        query = text("""
            SELECT p.game_id, COUNT(*) as count,
                   GROUP_CONCAT(p.id) as prediction_ids,
                   GROUP_CONCAT(p.predicted_home_score) as home_scores,
                   GROUP_CONCAT(p.predicted_away_score) as away_scores,
                   g.home_team, g.away_team, g.date
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-07-10'
            GROUP BY p.game_id 
            HAVING COUNT(*) > 1
            ORDER BY g.date DESC, count DESC
        """)
        
        results = db.session.execute(query).fetchall()
        
        print(f"📊 找到 {len(results)} 個game_id有重複預測:")
        print("-" * 100)
        print("Game ID   比賽                日期        重複數  預測IDs        預測分數")
        print("-" * 100)
        
        for row in results:
            game_id, count, pred_ids, home_scores, away_scores, home_team, away_team, game_date = row
            print(f"{game_id:8}  {away_team:3} @ {home_team:3}     {game_date}     {count:2}      {pred_ids:15} {home_scores}-{away_scores}")
        
        return results

def check_prediction_consistency():
    """檢查預測一致性問題"""
    print(f"\n🔧 檢查預測一致性...")
    
    app = create_app()
    with app.app_context():
        # 查找相同隊伍、相同日期但預測分數相同的異常情況
        query = text("""
            SELECT p1.game_id as game_id1, p2.game_id as game_id2,
                   p1.predicted_home_score, p1.predicted_away_score,
                   g1.home_team, g1.away_team, g1.date,
                   g2.home_team as home_team2, g2.away_team as away_team2
            FROM predictions p1
            JOIN predictions p2 ON p1.id != p2.id 
                AND p1.predicted_home_score = p2.predicted_home_score 
                AND p1.predicted_away_score = p2.predicted_away_score
            JOIN games g1 ON p1.game_id = g1.game_id
            JOIN games g2 ON p2.game_id = g2.game_id
            WHERE g1.date >= '2025-07-10' 
            AND g1.date = g2.date
            AND g1.home_team = g2.home_team 
            AND g1.away_team = g2.away_team
            ORDER BY g1.date DESC
        """)
        
        results = db.session.execute(query).fetchall()
        
        print(f"📊 找到 {len(results)} 對相同預測分數的記錄:")
        if results:
            print("-" * 100)
            print("Game ID 1  Game ID 2  比賽                預測分數    日期")
            print("-" * 100)
            
            for row in results:
                game_id1, game_id2, pred_home, pred_away, home_team, away_team, game_date, home_team2, away_team2 = row
                print(f"{game_id1:9}  {game_id2:9}  {away_team:3} @ {home_team:3}     {pred_away:.1f}-{pred_home:.1f}     {game_date}")
        
        return results

def clean_duplicate_predictions():
    """清理重複的預測記錄"""
    print(f"\n🧹 清理重複的預測記錄...")
    
    app = create_app()
    with app.app_context():
        # 對於每個game_id，只保留最新的預測記錄
        query = text("""
            SELECT p.game_id, COUNT(*) as count
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-07-10'
            GROUP BY p.game_id
            HAVING COUNT(*) > 1
        """)
        
        duplicate_games = db.session.execute(query).fetchall()
        
        cleaned_count = 0
        for game_id, count in duplicate_games:
            # 獲取該game_id的所有預測，按ID排序
            predictions = Prediction.query.filter_by(game_id=game_id).order_by(Prediction.id.desc()).all()
            
            if len(predictions) > 1:
                # 保留最新的(ID最大的)，刪除其他的
                keep_prediction = predictions[0]
                delete_predictions = predictions[1:]
                
                print(f"🗑️  Game {game_id}: 保留預測ID {keep_prediction.id}, 刪除 {len(delete_predictions)} 個重複記錄")
                
                for pred in delete_predictions:
                    db.session.delete(pred)
                    cleaned_count += 1
        
        if cleaned_count > 0:
            try:
                db.session.commit()
                print(f"✅ 成功清理 {cleaned_count} 個重複預測記錄")
            except Exception as e:
                db.session.rollback()
                print(f"❌ 清理失敗: {e}")
        else:
            print("✅ 沒有需要清理的重複記錄")

def check_missing_predictions():
    """檢查缺失的預測記錄"""
    print(f"\n🔍 檢查缺失的預測記錄...")
    
    app = create_app()
    with app.app_context():
        # 查找有比賽但沒有預測的記錄
        query = text("""
            SELECT g.game_id, g.home_team, g.away_team, g.date,
                   g.home_score, g.away_score
            FROM games g
            LEFT JOIN predictions p ON g.game_id = p.game_id
            WHERE g.date >= '2025-07-10'
            AND p.game_id IS NULL
            ORDER BY g.date DESC
        """)
        
        results = db.session.execute(query).fetchall()
        
        print(f"📊 找到 {len(results)} 場比賽沒有預測記錄:")
        if results:
            print("-" * 80)
            print("Game ID   比賽                日期        實際比分")
            print("-" * 80)
            
            for row in results:
                game_id, home_team, away_team, game_date, home_score, away_score = row
                actual_score = f"{away_score}-{home_score}" if home_score is not None else "未完成"
                print(f"{game_id:8}  {away_team:3} @ {home_team:3}     {game_date}     {actual_score}")
        
        return results

def main():
    """主要修復流程"""
    print("🔧 重複預測和數據問題修復工具")
    print("=" * 60)
    
    # 1. 檢查重複比賽
    duplicate_games = check_duplicate_games()
    
    # 2. 檢查重複預測
    duplicate_predictions = check_duplicate_predictions()
    
    # 3. 檢查預測一致性
    consistency_issues = check_prediction_consistency()
    
    # 4. 檢查缺失預測
    missing_predictions = check_missing_predictions()
    
    # 5. 清理重複預測
    clean_duplicate_predictions()
    
    print(f"\n📋 修復總結:")
    print(f"  重複比賽組數: {len(duplicate_games) if duplicate_games else 0}")
    print(f"  重複預測game_id數: {len(duplicate_predictions) if duplicate_predictions else 0}")
    print(f"  一致性問題數: {len(consistency_issues) if consistency_issues else 0}")
    print(f"  缺失預測比賽數: {len(missing_predictions) if missing_predictions else 0}")
    
    print(f"\n🚀 建議:")
    print("1. 檢查預測生成邏輯，避免重複創建預測")
    print("2. 確保每個game_id只有一個預測記錄")
    print("3. 檢查比賽數據導入是否有重複")

if __name__ == "__main__":
    main()
