#!/usr/bin/env python3
"""
調試預測錯誤
"""

from datetime import date
from app import create_app
from models.ml_predictor import MLBPredictor
from models.database import Game
import pandas as pd

def debug_prediction_error():
    """調試預測錯誤"""
    print("🔍 調試預測錯誤...")
    
    app = create_app()
    with app.app_context():
        try:
            # 獲取6月25日的比賽
            target_date = date(2025, 6, 25)
            games = Game.query.filter(Game.date == target_date).all()
            
            print(f"📅 找到 {len(games)} 場比賽")
            
            if not games:
                print("❌ 沒有找到比賽數據")
                return
            
            # 測試第一場比賽
            game = games[0]
            print(f"🏟️ 測試比賽: {game.away_team} @ {game.home_team}")
            
            # 創建預測器
            predictor = MLBPredictor()
            
            # 嘗試載入模型
            try:
                predictor.load_models('models/saved')
                print("✅ 模型載入成功")
            except Exception as e:
                print(f"❌ 模型載入失敗: {e}")
                return
            
            # 檢查特徵列
            print(f"📊 特徵列數量: {len(predictor.feature_columns)}")
            print(f"📊 前10個特徵: {predictor.feature_columns[:10]}")
            
            # 創建測試數據
            game_data = pd.DataFrame([{
                'game_id': game.game_id,
                'date': game.date,
                'home_team': game.home_team,
                'away_team': game.away_team,
                'home_score': None,
                'away_score': None
            }])
            
            print("🔧 開始特徵提取...")
            
            # 測試特徵提取
            features_df = predictor.extract_features(game_data)
            
            if features_df.empty:
                print("❌ 特徵提取返回空DataFrame")
                return
            
            print(f"✅ 特徵提取成功: {len(features_df)} 行, {len(features_df.columns)} 列")
            print(f"📊 特徵列: {list(features_df.columns)}")
            
            # 檢查特徵數據
            print("\n📋 特徵數據樣本:")
            for col in features_df.columns[:10]:
                value = features_df[col].iloc[0]
                print(f"   {col}: {value}")
            
            # 檢查是否有缺失的特徵列
            missing_features = set(predictor.feature_columns) - set(features_df.columns)
            if missing_features:
                print(f"⚠️ 缺失特徵列: {missing_features}")
            
            # 準備預測數據
            try:
                X = features_df[predictor.feature_columns]
                print(f"✅ 特徵數據準備成功: {X.shape}")
                
                # 檢查數據類型
                print(f"📊 數據類型: {X.dtypes.unique()}")
                
                # 檢查是否有NaN值
                nan_count = X.isnull().sum().sum()
                if nan_count > 0:
                    print(f"⚠️ 發現 {nan_count} 個NaN值")
                    print("NaN值分布:")
                    for col in X.columns:
                        if X[col].isnull().any():
                            print(f"   {col}: {X[col].isnull().sum()}")
                
                # 嘗試標準化
                try:
                    X_scaled = predictor.scalers['features'].transform(X)
                    print(f"✅ 數據標準化成功: {X_scaled.shape}")
                    
                    # 檢查標準化後的數據
                    if X_scaled.size == 0:
                        print("❌ 標準化後數據為空")
                    else:
                        print(f"📊 標準化後數據範圍: {X_scaled.min():.3f} ~ {X_scaled.max():.3f}")
                        
                        # 嘗試預測
                        try:
                            home_score_pred = predictor.models['home_score'].predict(X_scaled)
                            print(f"✅ 主隊得分預測成功: {home_score_pred[0]:.2f}")
                            
                            away_score_pred = predictor.models['away_score'].predict(X_scaled)
                            print(f"✅ 客隊得分預測成功: {away_score_pred[0]:.2f}")
                            
                            win_prob = predictor.models['win_probability'].predict(X_scaled)
                            print(f"✅ 勝率預測成功: {win_prob[0]:.3f}")
                            
                        except Exception as e:
                            print(f"❌ 模型預測失敗: {e}")
                            print(f"   X_scaled shape: {X_scaled.shape}")
                            print(f"   X_scaled type: {type(X_scaled)}")
                            
                except Exception as e:
                    print(f"❌ 數據標準化失敗: {e}")
                    
            except Exception as e:
                print(f"❌ 特徵數據準備失敗: {e}")
                print(f"   需要的特徵: {len(predictor.feature_columns)}")
                print(f"   實際特徵: {len(features_df.columns)}")
                
        except Exception as e:
            print(f"❌ 調試失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_prediction_error()
