#!/usr/bin/env python3
"""
修復預測偏差問題
解決客隊得分低估-1.55和主隊得分高估+0.38的系統性偏差
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import pickle
import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PredictionBiasFixer:
    """預測偏差修復器"""
    
    def __init__(self, db_path='instance/mlb_data.db'):
        self.db_path = db_path
        self.bias_corrections = {}
        
    def analyze_current_bias(self) -> Dict:
        """分析當前預測偏差"""
        print("🔍 分析當前預測偏差")
        print("=" * 60)
        
        conn = sqlite3.connect(self.db_path)
        
        # 獲取最近的預測數據
        query = """
        SELECT 
            p.predicted_home_score,
            p.predicted_away_score,
            g.home_score,
            g.away_score,
            p.confidence,
            g.date
        FROM predictions p
        JOIN games g ON p.game_id = g.game_id
        WHERE g.game_status = 'completed' 
        AND g.home_score IS NOT NULL 
        AND g.away_score IS NOT NULL
        AND p.predicted_home_score IS NOT NULL
        AND p.predicted_away_score IS NOT NULL
        ORDER BY g.date DESC
        LIMIT 200
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) == 0:
            print("❌ 沒有找到預測數據")
            return {}
        
        # 計算偏差
        home_bias = (df['predicted_home_score'] - df['home_score']).mean()
        away_bias = (df['predicted_away_score'] - df['away_score']).mean()
        
        home_std = (df['predicted_home_score'] - df['home_score']).std()
        away_std = (df['predicted_away_score'] - df['away_score']).std()
        
        # 分析偏差的分佈
        home_errors = df['predicted_home_score'] - df['home_score']
        away_errors = df['predicted_away_score'] - df['away_score']
        
        print(f"📊 預測偏差分析 (基於{len(df)}場比賽):")
        print(f"   主隊得分偏差: {home_bias:+.2f} ± {home_std:.2f}")
        print(f"   客隊得分偏差: {away_bias:+.2f} ± {away_std:.2f}")
        print(f"   主隊誤差範圍: {home_errors.min():.2f} 到 {home_errors.max():.2f}")
        print(f"   客隊誤差範圍: {away_errors.min():.2f} 到 {away_errors.max():.2f}")
        
        # 檢查偏差的一致性
        positive_home_bias = (home_errors > 0).mean()
        positive_away_bias = (away_errors > 0).mean()
        
        print(f"   主隊高估比例: {positive_home_bias:.1%}")
        print(f"   客隊高估比例: {positive_away_bias:.1%}")
        
        # 分析置信度與偏差的關係
        high_conf = df[df['confidence'] > 0.6]
        if len(high_conf) > 0:
            high_conf_home_bias = (high_conf['predicted_home_score'] - high_conf['home_score']).mean()
            high_conf_away_bias = (high_conf['predicted_away_score'] - high_conf['away_score']).mean()
            print(f"   高置信度主隊偏差: {high_conf_home_bias:+.2f}")
            print(f"   高置信度客隊偏差: {high_conf_away_bias:+.2f}")
        
        bias_data = {
            'home_bias': home_bias,
            'away_bias': away_bias,
            'home_std': home_std,
            'away_std': away_std,
            'sample_size': len(df),
            'positive_home_bias_rate': positive_home_bias,
            'positive_away_bias_rate': positive_away_bias
        }
        
        return bias_data
    
    def calculate_bias_corrections(self, bias_data: Dict) -> Dict:
        """計算偏差修正值"""
        print(f"\n🔧 計算偏差修正值")
        print("-" * 40)
        
        home_bias = bias_data.get('home_bias', 0)
        away_bias = bias_data.get('away_bias', 0)
        
        # 計算修正值 (使用保守的修正策略)
        home_correction = -home_bias * 0.8  # 修正80%的偏差
        away_correction = -away_bias * 0.8
        
        # 設置合理的修正範圍限制
        home_correction = max(-2.0, min(2.0, home_correction))
        away_correction = max(-2.0, min(2.0, away_correction))
        
        corrections = {
            'home_correction': home_correction,
            'away_correction': away_correction,
            'original_home_bias': home_bias,
            'original_away_bias': away_bias,
            'correction_factor': 0.8
        }
        
        print(f"原始偏差:")
        print(f"  主隊: {home_bias:+.2f}")
        print(f"  客隊: {away_bias:+.2f}")
        print(f"修正值:")
        print(f"  主隊修正: {home_correction:+.2f}")
        print(f"  客隊修正: {away_correction:+.2f}")
        
        return corrections
    
    def apply_bias_corrections(self, corrections: Dict) -> bool:
        """應用偏差修正到未來預測"""
        print(f"\n💾 保存偏差修正配置")
        print("-" * 40)
        
        try:
            # 保存修正配置到文件
            config_path = 'models/bias_corrections.json'
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            import json
            with open(config_path, 'w') as f:
                json.dump(corrections, f, indent=2)
            
            print(f"✅ 偏差修正配置已保存到 {config_path}")
            
            # 創建修正函數文件
            correction_code = f"""
# 預測偏差修正函數
# 自動生成於 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

def apply_bias_correction(home_score, away_score):
    \"\"\"應用偏差修正到預測分數\"\"\"
    corrected_home = home_score + {corrections['home_correction']:.3f}
    corrected_away = away_score + {corrections['away_correction']:.3f}
    
    # 確保分數在合理範圍內
    corrected_home = max(0, min(20, corrected_home))
    corrected_away = max(0, min(20, corrected_away))
    
    return corrected_home, corrected_away

def get_bias_info():
    \"\"\"獲取偏差修正信息\"\"\"
    return {{
        'home_correction': {corrections['home_correction']:.3f},
        'away_correction': {corrections['away_correction']:.3f},
        'original_home_bias': {corrections['original_home_bias']:.3f},
        'original_away_bias': {corrections['original_away_bias']:.3f},
        'applied_date': '{datetime.now().strftime('%Y-%m-%d')}'
    }}
"""
            
            with open('models/bias_correction.py', 'w') as f:
                f.write(correction_code)
            
            print(f"✅ 修正函數已創建: models/bias_correction.py")
            
            return True
            
        except Exception as e:
            logger.error(f"保存偏差修正配置失敗: {e}")
            return False
    
    def test_corrections(self, corrections: Dict) -> None:
        """測試修正效果"""
        print(f"\n🧪 測試偏差修正效果")
        print("-" * 40)
        
        # 模擬一些典型的預測分數
        test_cases = [
            (5.2, 4.8, "低分比賽"),
            (7.5, 6.2, "中分比賽"),
            (9.1, 8.7, "高分比賽"),
            (3.5, 2.8, "極低分比賽"),
            (12.2, 11.5, "極高分比賽")
        ]
        
        print(f"測試案例:")
        print(f"{'原始預測':<15} {'修正後':<15} {'類型':<10}")
        print("-" * 45)
        
        for home_orig, away_orig, case_type in test_cases:
            home_corrected = home_orig + corrections['home_correction']
            away_corrected = away_orig + corrections['away_correction']
            
            # 確保在合理範圍內
            home_corrected = max(0, min(20, home_corrected))
            away_corrected = max(0, min(20, away_corrected))
            
            print(f"{home_orig:.1f}-{away_orig:.1f}      {home_corrected:.1f}-{away_corrected:.1f}      {case_type}")
    
    def update_existing_predictions(self, corrections: Dict) -> int:
        """更新現有未完成比賽的預測"""
        print(f"\n🔄 更新現有未完成比賽的預測")
        print("-" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查找未完成的比賽預測
            cursor.execute("""
                SELECT p.prediction_id, p.predicted_home_score, p.predicted_away_score
                FROM predictions p
                JOIN games g ON p.game_id = g.game_id
                WHERE g.game_status != 'completed'
                AND p.predicted_home_score IS NOT NULL
                AND p.predicted_away_score IS NOT NULL
            """)
            
            pending_predictions = cursor.fetchall()
            
            if not pending_predictions:
                print("📋 沒有找到待更新的預測")
                return 0
            
            updated_count = 0
            
            for pred_id, home_score, away_score in pending_predictions:
                # 應用修正
                corrected_home = home_score + corrections['home_correction']
                corrected_away = away_score + corrections['away_correction']
                
                # 確保在合理範圍內
                corrected_home = max(0, min(20, corrected_home))
                corrected_away = max(0, min(20, corrected_away))
                
                # 更新數據庫
                cursor.execute("""
                    UPDATE predictions 
                    SET predicted_home_score = ?, 
                        predicted_away_score = ?,
                        updated_at = ?
                    WHERE prediction_id = ?
                """, (corrected_home, corrected_away, datetime.now(), pred_id))
                
                updated_count += 1
            
            conn.commit()
            conn.close()
            
            print(f"✅ 已更新 {updated_count} 個預測")
            return updated_count
            
        except Exception as e:
            logger.error(f"更新預測失敗: {e}")
            return 0
    
    def fix_all_bias_issues(self):
        """修復所有偏差問題"""
        print("🎯 開始修復預測偏差問題")
        print("=" * 60)
        
        # 步驟1: 分析當前偏差
        bias_data = self.analyze_current_bias()
        
        if not bias_data:
            print("❌ 無法分析偏差，退出")
            return
        
        # 檢查是否需要修正
        home_bias = abs(bias_data.get('home_bias', 0))
        away_bias = abs(bias_data.get('away_bias', 0))
        
        if home_bias < 0.2 and away_bias < 0.2:
            print("✅ 預測偏差在可接受範圍內，無需修正")
            return
        
        # 步驟2: 計算修正值
        corrections = self.calculate_bias_corrections(bias_data)
        
        # 步驟3: 測試修正效果
        self.test_corrections(corrections)
        
        # 步驟4: 應用修正
        if self.apply_bias_corrections(corrections):
            # 步驟5: 更新現有預測
            updated_count = self.update_existing_predictions(corrections)
            
            # 總結
            print(f"\n🎉 偏差修正完成!")
            print(f"✅ 主隊偏差修正: {corrections['original_home_bias']:+.2f} → {corrections['home_correction']:+.2f}")
            print(f"✅ 客隊偏差修正: {corrections['original_away_bias']:+.2f} → {corrections['away_correction']:+.2f}")
            print(f"✅ 已更新預測: {updated_count} 個")
            print(f"📁 配置文件: models/bias_corrections.json")
            print(f"📁 修正函數: models/bias_correction.py")
            
            # 建議下一步
            print(f"\n💡 下一步建議:")
            print(f"1. 修改預測模型代碼，整合 bias_correction.py")
            print(f"2. 重新訓練模型時應用這些修正")
            print(f"3. 監控修正後的預測效果")
            
        else:
            print("❌ 偏差修正應用失敗")

def main():
    """主函數"""
    fixer = PredictionBiasFixer()
    fixer.fix_all_bias_issues()

if __name__ == "__main__":
    main()