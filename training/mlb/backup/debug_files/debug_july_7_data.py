#!/usr/bin/env python3
"""
調試2025-07-07的數據問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, BettingOdds, Game
from models.covers_scraper import CoversMLBScraper

def debug_july_7_data():
    """調試2025-07-07的數據"""
    app = create_app()
    
    with app.app_context():
        test_date = date(2025, 7, 7)
        
        print(f"🔍 調試 {test_date} 的數據問題...")
        
        # 1. 檢查數據庫中的比賽
        games = Game.query.filter_by(date=test_date).all()
        print(f"📊 數據庫中有 {len(games)} 場比賽:")
        for game in games:
            print(f"  - {game.away_team} @ {game.home_team} (ID: {game.game_id})")
        
        # 2. 檢查現有盤口數據
        existing_odds = BettingOdds.query.join(Game).filter(Game.date == test_date).all()
        print(f"\n📈 現有盤口記錄: {len(existing_odds)}")
        
        # 3. 重新抓取covers.com數據
        print(f"\n🌐 重新抓取covers.com數據...")
        scraper = CoversMLBScraper()
        result = scraper.fetch_mlb_games_for_date(test_date)
        
        if result.get('success'):
            scraped_games = result.get('games', [])
            print(f"✅ 成功抓取到 {len(scraped_games)} 場比賽")
            
            print(f"\n📋 抓取到的比賽詳情:")
            for i, game in enumerate(scraped_games):
                print(f"\n  比賽 {i+1}:")
                print(f"    客隊: {game.get('away_team')}")
                print(f"    主隊: {game.get('home_team')}")
                print(f"    狀態: {game.get('status')}")
                
                odds = game.get('odds', {})
                print(f"    盤口數據:")
                print(f"      總分線: {odds.get('total_line')}")
                print(f"      讓分線: {odds.get('spread_line')}")
                print(f"      客隊勝負盤: {odds.get('moneyline_away')}")
                print(f"      主隊勝負盤: {odds.get('moneyline_home')}")
                
                # 檢查是否有任何盤口數據
                has_any_odds = any([
                    odds.get('total_line'),
                    odds.get('spread_line'),
                    odds.get('moneyline_away'),
                    odds.get('moneyline_home')
                ])
                print(f"    有盤口數據: {'✅' if has_any_odds else '❌'}")
        else:
            print(f"❌ 抓取失敗: {result.get('error')}")
        
        # 4. 比較其他日期的數據
        print(f"\n🔄 比較其他日期的數據:")
        for check_date in [date(2025, 7, 6), date(2025, 7, 8)]:
            games_count = Game.query.filter_by(date=check_date).count()
            odds_count = BettingOdds.query.join(Game).filter(Game.date == check_date).count()
            print(f"  {check_date}: {games_count} 場比賽, {odds_count} 筆盤口")

if __name__ == "__main__":
    debug_july_7_data()
