#!/usr/bin/env python3
"""
修復博彩盤口問題和補賽處理
1. 檢查並修復Unknown盤口
2. 識別和處理補賽
3. 更新實際結果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction, BettingOdds
from models.real_betting_odds_fetcher import RealBettingOddsFet<PERSON>

def diagnose_betting_odds_issues():
    """診斷博彩盤口問題"""
    app = create_app()
    
    with app.app_context():
        print("🔍 診斷博彩盤口問題...")
        
        # 檢查最近7天的數據
        recent_dates = [date.today() - timedelta(days=i) for i in range(7)]
        
        total_games = 0
        games_with_odds = 0
        games_with_unknown = 0
        games_missing_results = 0
        potential_makeup_games = {}
        
        for check_date in recent_dates:
            games = Game.query.filter(Game.date == check_date).all()
            if not games:
                continue
                
            print(f"\n📅 {check_date} ({len(games)} 場比賽):")
            total_games += len(games)
            
            # 檢查是否有異常多的比賽（可能包含補賽）
            if len(games) > 20:  # 正常一天約15場比賽
                potential_makeup_games[check_date] = len(games)
                print(f"  ⚠️  可能包含補賽 ({len(games)} 場)")
            
            date_with_odds = 0
            date_unknown = 0
            date_missing_results = 0
            
            for game in games:
                # 檢查博彩盤口
                odds = BettingOdds.query.filter_by(
                    game_id=game.game_id, 
                    market_type='totals'
                ).first()
                
                if odds and odds.total_point:
                    date_with_odds += 1
                else:
                    date_unknown += 1
                
                # 檢查實際結果
                if game.game_status == 'completed' and (game.home_score is None or game.away_score is None):
                    date_missing_results += 1
            
            games_with_odds += date_with_odds
            games_with_unknown += date_unknown
            games_missing_results += date_missing_results
            
            print(f"  📊 有盤口: {date_with_odds}, Unknown: {date_unknown}, 缺結果: {date_missing_results}")
        
        print(f"\n📊 總體統計:")
        print(f"  - 總比賽數: {total_games}")
        print(f"  - 有盤口數據: {games_with_odds} ({games_with_odds/total_games*100:.1f}%)")
        print(f"  - Unknown盤口: {games_with_unknown} ({games_with_unknown/total_games*100:.1f}%)")
        print(f"  - 缺失結果: {games_missing_results}")
        
        if potential_makeup_games:
            print(f"\n⚠️  可能的補賽日期:")
            for makeup_date, game_count in potential_makeup_games.items():
                print(f"  - {makeup_date}: {game_count} 場比賽")
        
        return {
            'total_games': total_games,
            'games_with_odds': games_with_odds,
            'games_with_unknown': games_with_unknown,
            'games_missing_results': games_missing_results,
            'potential_makeup_games': potential_makeup_games
        }

def fix_unknown_betting_odds():
    """修復Unknown博彩盤口"""
    app = create_app()
    
    with app.app_context():
        print("\n🔧 修復Unknown博彩盤口...")
        
        # 獲取最近7天沒有盤口的比賽
        recent_dates = [date.today() - timedelta(days=i) for i in range(7)]
        
        fixed_count = 0
        fetcher = RealBettingOddsFetcher()
        
        for check_date in recent_dates:
            games = Game.query.filter(Game.date == check_date).all()
            
            for game in games:
                # 檢查是否缺少大小分盤口
                existing_odds = BettingOdds.query.filter_by(
                    game_id=game.game_id,
                    market_type='totals'
                ).first()
                
                if not existing_odds or not existing_odds.total_point:
                    print(f"  🔧 修復 {game.away_team} @ {game.home_team} ({check_date})")
                    
                    # 嘗試從數據庫獲取博彩盤口
                    try:
                        # 直接查詢數據庫中的博彩盤口
                        existing_totals = BettingOdds.query.filter_by(
                            game_id=game.game_id,
                            market_type='totals'
                        ).first()

                        odds_data = None
                        if existing_totals and existing_totals.total_point:
                            odds_data = {
                                'totals': {
                                    'total_point': existing_totals.total_point,
                                    'over_odds': existing_totals.over_price or -110,
                                    'under_odds': existing_totals.under_price or -110,
                                    'bookmaker': existing_totals.bookmaker
                                }
                            }
                        
                        if odds_data and odds_data.get('totals'):
                            totals = odds_data['totals']
                            
                            if existing_odds:
                                # 更新現有記錄
                                existing_odds.total_point = totals['total_point']
                                existing_odds.over_price = totals.get('over_odds', -110)
                                existing_odds.under_price = totals.get('under_odds', -110)
                                existing_odds.bookmaker = totals.get('bookmaker', 'bet365')
                                existing_odds.updated_at = datetime.now()
                            else:
                                # 創建新記錄
                                new_odds = BettingOdds(
                                    game_id=game.game_id,
                                    bookmaker=totals.get('bookmaker', 'bet365'),
                                    market_type='totals',
                                    total_point=totals['total_point'],
                                    over_price=totals.get('over_odds', -110),
                                    under_price=totals.get('under_odds', -110),
                                    is_real=True,
                                    data_source='database',
                                    odds_time=datetime.now(),
                                    created_at=datetime.now(),
                                    updated_at=datetime.now()
                                )
                                db.session.add(new_odds)
                            
                            fixed_count += 1
                            print(f"    ✅ 盤口: {totals['total_point']}")
                        else:
                            # 使用合理的默認值
                            default_total = 8.5  # MLB平均總分
                            
                            if existing_odds:
                                existing_odds.total_point = default_total
                                existing_odds.bookmaker = 'estimated'
                                existing_odds.updated_at = datetime.now()
                            else:
                                new_odds = BettingOdds(
                                    game_id=game.game_id,
                                    bookmaker='estimated',
                                    market_type='totals',
                                    total_point=default_total,
                                    over_price=-110,
                                    under_price=-110,
                                    is_real=False,
                                    data_source='estimated',
                                    odds_time=datetime.now(),
                                    created_at=datetime.now(),
                                    updated_at=datetime.now()
                                )
                                db.session.add(new_odds)
                            
                            print(f"    ⚠️  使用估計盤口: {default_total}")
                    
                    except Exception as e:
                        print(f"    ❌ 修復失敗: {e}")
        
        try:
            db.session.commit()
            print(f"\n✅ 修復完成，共修復 {fixed_count} 個盤口")
        except Exception as e:
            db.session.rollback()
            print(f"❌ 修復失敗: {e}")

def update_game_results():
    """更新比賽實際結果"""
    app = create_app()
    
    with app.app_context():
        print("\n🔄 更新比賽實際結果...")
        
        # 查找已完成但缺少結果的比賽
        completed_games = Game.query.filter(
            Game.game_status == 'completed',
            db.or_(Game.home_score.is_(None), Game.away_score.is_(None))
        ).all()
        
        print(f"找到 {len(completed_games)} 場需要更新結果的比賽")
        
        updated_count = 0
        for game in completed_games:
            # 這裡可以添加從MLB API獲取結果的邏輯
            # 暫時跳過，因為需要實際的API調用
            print(f"  ⚠️  {game.away_team} @ {game.home_team} ({game.date}) - 需要手動更新")
        
        print(f"✅ 結果更新完成，共更新 {updated_count} 場比賽")

def identify_makeup_games():
    """識別補賽並標記"""
    app = create_app()
    
    with app.app_context():
        print("\n🔍 識別補賽...")
        
        # 檢查最近30天的比賽分布
        recent_dates = [date.today() - timedelta(days=i) for i in range(30)]
        
        makeup_candidates = []
        
        for check_date in recent_dates:
            games = Game.query.filter(Game.date == check_date).all()
            
            # 如果一天超過20場比賽，可能包含補賽
            if len(games) > 20:
                # 檢查是否有重複的球隊
                teams_playing = []
                for game in games:
                    teams_playing.extend([game.home_team, game.away_team])
                
                # 如果有球隊一天打多場，很可能是補賽
                from collections import Counter
                team_counts = Counter(teams_playing)
                teams_multiple_games = [team for team, count in team_counts.items() if count > 2]
                
                if teams_multiple_games:
                    makeup_candidates.append({
                        'date': check_date,
                        'total_games': len(games),
                        'teams_multiple_games': teams_multiple_games
                    })
        
        if makeup_candidates:
            print("🚨 發現可能的補賽:")
            for candidate in makeup_candidates:
                print(f"  📅 {candidate['date']}: {candidate['total_games']} 場比賽")
                print(f"    多場比賽球隊: {', '.join(candidate['teams_multiple_games'])}")
        else:
            print("✅ 沒有發現明顯的補賽")
        
        return makeup_candidates

if __name__ == "__main__":
    print("🚀 開始修復博彩盤口問題...")
    
    # 步驟1: 診斷問題
    diagnosis = diagnose_betting_odds_issues()
    
    # 步驟2: 修復Unknown盤口
    fix_unknown_betting_odds()
    
    # 步驟3: 識別補賽
    makeup_games = identify_makeup_games()
    
    # 步驟4: 更新比賽結果
    update_game_results()
    
    print("\n✅ 修復完成！")
    print("\n📋 建議:")
    print("1. 定期運行此腳本以保持數據更新")
    print("2. 對於補賽，考慮在預測時排除或特別標記")
    print("3. 監控博彩盤口數據源的可用性")
