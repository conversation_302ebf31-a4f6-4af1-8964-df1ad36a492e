#!/usr/bin/env python3
"""
修復預測問題：
1. 檢查異常高的預測分數
2. 獲取缺失的真實盤口數據
3. 重新計算合理的預測
"""

import sys
from datetime import date, datetime, timedelta
from pathlib import Path

# 添加項目根目錄到Python路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app
from models.database import db, Prediction, Game, BettingOdds

def check_abnormal_predictions(target_date: date):
    """檢查異常的預測分數"""
    print(f"🔍 檢查 {target_date} 的異常預測...")
    
    app = create_app()
    with app.app_context():
        predictions = Prediction.query.join(Game).filter(
            Game.date >= target_date,
            Game.date < target_date + timedelta(days=1)
        ).all()
        
        abnormal_predictions = []
        
        for pred in predictions:
            game = Game.query.filter_by(game_id=pred.game_id).first()
            if not game:
                continue
            
            total_predicted = pred.predicted_total_runs or 0
            home_score = pred.predicted_home_score or 0
            away_score = pred.predicted_away_score or 0
            
            # 檢查異常條件
            is_abnormal = False
            reasons = []
            
            if total_predicted > 15:
                is_abnormal = True
                reasons.append(f"總分過高: {total_predicted}")
            
            if home_score > 12 or away_score > 12:
                is_abnormal = True
                reasons.append(f"單隊得分過高: {home_score}-{away_score}")
            
            if total_predicted < 4:
                is_abnormal = True
                reasons.append(f"總分過低: {total_predicted}")
            
            if is_abnormal:
                abnormal_predictions.append({
                    'game': f"{game.away_team}@{game.home_team}",
                    'game_id': game.game_id,
                    'predicted_score': f"{away_score}-{home_score}",
                    'total': total_predicted,
                    'reasons': reasons,
                    'prediction': pred
                })
        
        if abnormal_predictions:
            print(f"⚠️  發現 {len(abnormal_predictions)} 個異常預測:")
            for item in abnormal_predictions:
                print(f"  {item['game']}: {item['predicted_score']} (總分: {item['total']})")
                for reason in item['reasons']:
                    print(f"    - {reason}")
        else:
            print("✅ 沒有發現異常預測")
        
        return abnormal_predictions

def add_missing_betting_odds(game_id: str, total_line: float, bookmaker: str = "manual"):
    """手動添加缺失的博彩盤口"""
    app = create_app()
    with app.app_context():
        # 檢查是否已存在
        existing_odds = BettingOdds.query.filter_by(
            game_id=game_id,
            market_type='totals',
            bookmaker=bookmaker
        ).first()
        
        if existing_odds:
            print(f"  更新現有盤口: {existing_odds.total_point} -> {total_line}")
            existing_odds.total_point = total_line
            existing_odds.updated_at = datetime.now()
        else:
            print(f"  添加新盤口: {total_line}")
            new_odds = BettingOdds(
                game_id=game_id,
                bookmaker=bookmaker,
                market_type='totals',
                total_point=total_line,
                odds_time=datetime.now(),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db.session.add(new_odds)
        
        db.session.commit()

def fix_abnormal_prediction(prediction: Prediction, new_total_line: float):
    """修復異常預測"""
    app = create_app()
    with app.app_context():
        # 重新計算合理的預測分數
        # 基於真實盤口調整預測
        if new_total_line > 0:
            # 按比例調整預測分數
            current_total = prediction.predicted_total_runs or 0
            if current_total > 0:
                scale_factor = new_total_line / current_total * 1.1  # 稍微高於盤口
                
                new_home_score = (prediction.predicted_home_score or 0) * scale_factor
                new_away_score = (prediction.predicted_away_score or 0) * scale_factor
                
                # 確保分數合理
                new_home_score = max(2.0, min(10.0, new_home_score))
                new_away_score = max(2.0, min(10.0, new_away_score))
                
                prediction.predicted_home_score = round(new_home_score, 1)
                prediction.predicted_away_score = round(new_away_score, 1)
                prediction.predicted_total_runs = round(new_home_score + new_away_score, 1)
            else:
                # 如果沒有原始預測，使用默認分配
                prediction.predicted_home_score = round(new_total_line * 0.52, 1)  # 主場稍微優勢
                prediction.predicted_away_score = round(new_total_line * 0.48, 1)
                prediction.predicted_total_runs = round(new_total_line * 1.05, 1)  # 稍高於盤口
        
        # 更新盤口
        prediction.over_under_line = new_total_line
        
        # 重新計算大小分概率
        predicted_total = prediction.predicted_total_runs
        if predicted_total > new_total_line:
            prediction.over_probability = 0.6
            prediction.under_probability = 0.4
        else:
            prediction.over_probability = 0.4
            prediction.under_probability = 0.6
        
        prediction.over_under_confidence = 0.7
        prediction.updated_at = datetime.now()
        
        db.session.commit()

def manual_fix_stl_pit():
    """手動修復 STL@PIT 的問題"""
    print("🔧 手動修復 STL@PIT 問題...")
    
    app = create_app()
    with app.app_context():
        # 查找 STL@PIT 比賽
        game = Game.query.filter(
            Game.date >= date(2025, 7, 1),
            Game.date < date(2025, 7, 2),
            Game.away_team == 'STL',
            Game.home_team == 'PIT'
        ).first()
        
        if not game:
            print("❌ 找不到 STL@PIT 比賽")
            return
        
        print(f"找到比賽: {game.game_id}")
        
        # 添加真實盤口 (bet365: 7.0)
        add_missing_betting_odds(game.game_id, 7.0, "bet365")
        
        # 修復預測
        prediction = Prediction.query.filter_by(game_id=game.game_id).first()
        if prediction:
            print(f"修復前: {prediction.predicted_away_score}-{prediction.predicted_home_score} (總分: {prediction.predicted_total_runs})")
            fix_abnormal_prediction(prediction, 7.0)
            print(f"修復後: {prediction.predicted_away_score}-{prediction.predicted_home_score} (總分: {prediction.predicted_total_runs})")
        else:
            print("❌ 找不到預測記錄")

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='修復預測問題')
    parser.add_argument('--date', type=str, help='目標日期 (YYYY-MM-DD)', default='2025-07-01')
    parser.add_argument('--check', action='store_true', help='只檢查異常預測')
    parser.add_argument('--fix-stl-pit', action='store_true', help='手動修復STL@PIT問題')
    
    args = parser.parse_args()
    
    if args.fix_stl_pit:
        manual_fix_stl_pit()
    else:
        try:
            target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
        except ValueError:
            print("❌ 日期格式錯誤，請使用 YYYY-MM-DD 格式")
            exit(1)
        
        abnormal_predictions = check_abnormal_predictions(target_date)
        
        if not args.check and abnormal_predictions:
            print("\n🔧 是否要修復這些異常預測？(y/n): ", end="")
            response = input().strip().lower()
            if response == 'y':
                for item in abnormal_predictions:
                    print(f"修復 {item['game']}...")
                    # 這裡可以添加自動修復邏輯
                    # 或者提示用戶手動輸入正確的盤口值
