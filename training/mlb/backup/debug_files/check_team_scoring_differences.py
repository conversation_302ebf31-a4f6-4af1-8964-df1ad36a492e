#!/usr/bin/env python3
"""
檢查球隊得分能力差異和預測問題
"""

from app import create_app
from models.database import Prediction, Game, TeamStats, db
from datetime import date, timedelta
import pandas as pd

def check_team_scoring_differences():
    """檢查球隊得分能力差異"""
    app = create_app()
    with app.app_context():
        print("🔍 檢查球隊得分能力差異")
        print("=" * 60)
        
        # 1. 檢查最近的預測結果
        print("\n📊 最近10個預測結果:")
        recent_predictions = Prediction.query.join(Game).order_by(Game.date.desc()).limit(10).all()
        
        prediction_scores = []
        for pred in recent_predictions:
            game = Game.query.filter_by(game_id=pred.game_id).first()
            if game:
                away_score = pred.predicted_away_score
                home_score = pred.predicted_home_score
                prediction_scores.append((away_score, home_score))
                print(f"{game.away_team} @ {game.home_team}")
                print(f"  預測: {away_score:.1f} - {home_score:.1f}")
                print(f"  日期: {game.date}")
                print()
        
        # 2. 分析預測分數的變異性
        if prediction_scores:
            away_scores = [s[0] for s in prediction_scores]
            home_scores = [s[1] for s in prediction_scores]
            
            print(f"\n📈 預測分數統計:")
            print(f"客隊得分範圍: {min(away_scores):.1f} - {max(away_scores):.1f}")
            print(f"主隊得分範圍: {min(home_scores):.1f} - {max(home_scores):.1f}")
            print(f"客隊平均: {sum(away_scores)/len(away_scores):.1f}")
            print(f"主隊平均: {sum(home_scores)/len(home_scores):.1f}")
            
            # 檢查是否所有預測都相同
            unique_away = len(set(f"{s:.1f}" for s in away_scores))
            unique_home = len(set(f"{s:.1f}" for s in home_scores))
            
            if unique_away == 1 and unique_home == 1:
                print("❌ 問題發現: 所有預測分數都相同!")
            elif unique_away <= 2 and unique_home <= 2:
                print("⚠️  警告: 預測分數變異性很低")
            else:
                print("✅ 預測分數有合理變異性")
        
        # 3. 檢查球隊統計差異
        print(f"\n🏟️ 球隊統計差異:")
        teams = TeamStats.query.limit(10).all()
        
        if teams:
            print("球隊得分能力對比:")
            team_stats = []
            for team in teams:
                team_stats.append({
                    'team': team.team_id,
                    'runs_per_game': team.runs_per_game,
                    'era': team.era,
                    'win_pct': team.win_percentage
                })
                print(f"{team.team_id}: 平均得分={team.runs_per_game:.2f}, ERA={team.era:.2f}, 勝率={team.win_percentage:.3f}")
            
            # 分析統計差異
            runs_list = [t['runs_per_game'] for t in team_stats]
            era_list = [t['era'] for t in team_stats]
            
            print(f"\n📊 球隊統計變異性:")
            print(f"得分範圍: {min(runs_list):.2f} - {max(runs_list):.2f}")
            print(f"ERA範圍: {min(era_list):.2f} - {max(era_list):.2f}")
            
            if max(runs_list) - min(runs_list) < 1.0:
                print("❌ 問題: 球隊得分能力差異太小")
            else:
                print("✅ 球隊統計有合理差異")
        else:
            print("❌ 沒有找到球隊統計數據")
        
        # 4. 檢查模型特徵提取
        print(f"\n🤖 測試模型特徵提取:")
        try:
            from models.ml_predictor import MLBPredictor
            predictor = MLBPredictor()
            
            # 測試兩個不同實力的球隊
            test_games = [
                {'home_team': 'LAD', 'away_team': 'OAK', 'date': date.today()},  # 強隊 vs 弱隊
                {'home_team': 'NYY', 'away_team': 'HOU', 'date': date.today()}   # 強隊 vs 強隊
            ]
            
            for i, game_data in enumerate(test_games, 1):
                print(f"\n測試比賽 {i}: {game_data['away_team']} @ {game_data['home_team']}")
                
                # 創建測試用的 pandas Series
                game_series = pd.Series(game_data)
                features = predictor.extract_features(game_series)
                
                if features is not None:
                    print(f"  特徵數量: {len(features)}")
                    
                    # 檢查關鍵特徵
                    key_features = ['home_runs_per_game', 'away_runs_per_game', 'home_era', 'away_era']
                    for feature in key_features:
                        if feature in features:
                            print(f"  {feature}: {features[feature]:.3f}")
                    
                    # 進行預測
                    prediction = predictor.predict_game(
                        game_data['home_team'], 
                        game_data['away_team'], 
                        game_data['date']
                    )
                    
                    if prediction:
                        print(f"  預測結果: {prediction['away_score']:.1f} - {prediction['home_score']:.1f}")
                else:
                    print("  ❌ 特徵提取失敗")
                    
        except Exception as e:
            print(f"❌ 模型測試失敗: {e}")
        
        # 5. 診斷建議
        print(f"\n💡 診斷建議:")
        print("1. 檢查 TeamStats 數據是否正確計算")
        print("2. 驗證特徵提取是否使用了正確的球隊統計")
        print("3. 檢查模型是否需要重新訓練")
        print("4. 確認特徵標準化是否正確")

if __name__ == "__main__":
    check_team_scoring_differences()
