#!/usr/bin/env python3
"""
調試covers.com數據提取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from models.covers_scraper import CoversMLBScraper

def debug_covers_extraction():
    """調試covers.com數據提取"""
    print("=== 調試covers.com數據提取 ===")
    
    scraper = CoversMLBScraper()
    target_date = date(2024, 7, 7)
    
    result = scraper.fetch_mlb_games_for_date(target_date)
    
    if result['success']:
        games = result['games']
        print(f"抓取到 {len(games)} 場比賽")
        
        print(f"\n=== 詳細數據檢查 ===")
        for i, game in enumerate(games[:5]):  # 只檢查前5場
            print(f"\n比賽 {i+1}: {game['away_team']} @ {game['home_team']}")
            print(f"  狀態: {game.get('status', 'N/A')}")
            print(f"  客隊得分: {game.get('away_score', 'N/A')}")
            print(f"  主隊得分: {game.get('home_score', 'N/A')}")
            print(f"  賠率數據: {game['odds']}")
            
            # 檢查賠率數據的合理性
            odds = game['odds']
            if odds.get('total_line'):
                total = float(odds['total_line'])
                if total < 5.0:
                    print(f"  ⚠️  異常低的大小分: {total}")
                elif total > 15.0:
                    print(f"  ⚠️  異常高的大小分: {total}")
                else:
                    print(f"  ✅ 正常大小分: {total}")
    else:
        print(f"抓取失敗: {result.get('error')}")

if __name__ == "__main__":
    debug_covers_extraction()
