#!/usr/bin/env python3
"""
修復不完整的比賽得分
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction

def fix_incomplete_scores():
    """修復不完整的比賽得分"""
    app = create_app()
    
    # 6月26日的完整比賽結果（從可靠來源獲取）
    complete_results = {
        '2025-06-26': {
            'ATH@DET': {'away_score': 4, 'home_score': 8},
            'TOR@CLE': {'away_score': 6, 'home_score': 5},
            'SEA@MIN': {'away_score': 1, 'home_score': 10},
            'TB@KC': {'away_score': 4, 'home_score': 7},
            'PHI@HOU': {'away_score': 8, 'home_score': 5},
            'CHC@STL': {'away_score': 9, 'home_score': 5},
            'COL@CIN': {'away_score': 6, 'home_score': 4},
            'AZ@LAA': {'away_score': 3, 'home_score': 12},
            'LAD@SF': {'away_score': 8, 'home_score': 6}
        }
    }
    
    with app.app_context():
        print("🔧 修復不完整的比賽得分...")
        
        updated_count = 0
        
        for date_str, games in complete_results.items():
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            print(f"\n📅 修復 {target_date} 的比賽結果:")
            
            for game_key, result in games.items():
                away_team, home_team = game_key.split('@')
                
                # 查找對應的比賽
                game = Game.query.filter(
                    Game.date == target_date,
                    Game.away_team == away_team,
                    Game.home_team == home_team
                ).first()
                
                if game:
                    # 檢查是否需要更新
                    needs_update = (
                        game.away_score is None or 
                        game.home_score is None or
                        game.away_score != result['away_score'] or
                        game.home_score != result['home_score']
                    )
                    
                    if needs_update:
                        old_score = f"{game.away_score} - {game.home_score}"
                        
                        # 更新結果
                        game.away_score = result['away_score']
                        game.home_score = result['home_score']
                        game.game_status = 'completed'
                        
                        new_score = f"{result['away_score']} - {result['home_score']}"
                        print(f"    ✅ 更新 {away_team} @ {home_team}: {old_score} → {new_score}")
                        updated_count += 1
                    else:
                        print(f"    ✓ {away_team} @ {home_team}: 已是正確結果")
                else:
                    print(f"    ❌ 未找到比賽: {away_team} @ {home_team}")
        
        try:
            db.session.commit()
            print(f"\n✅ 修復完成，共更新 {updated_count} 場比賽結果")
            
            # 更新相關的預測準確性
            update_prediction_accuracy_for_fixed_games()
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 修復失敗: {e}")

def update_prediction_accuracy_for_fixed_games():
    """為修復的比賽更新預測準確性"""
    print(f"\n🎯 更新修復比賽的預測準確性...")
    
    # 獲取6月26日的所有預測
    target_date = date(2025, 6, 26)
    predictions = Prediction.query.join(Game).filter(
        Game.date == target_date
    ).all()
    
    print(f"  📊 找到 {len(predictions)} 個預測需要更新")
    
    updated_count = 0
    
    for prediction in predictions:
        game = Game.query.filter_by(game_id=prediction.game_id).first()
        
        if game and game.home_score is not None and game.away_score is not None:
            # 更新實際結果
            prediction.actual_home_score = game.home_score
            prediction.actual_away_score = game.away_score
            prediction.actual_total_runs = game.home_score + game.away_score
            
            # 計算勝負預測準確性
            predicted_home_wins = prediction.predicted_home_score > prediction.predicted_away_score
            actual_home_wins = game.home_score > game.away_score
            prediction.is_correct = (predicted_home_wins == actual_home_wins)
            
            # 計算得分差異
            predicted_total = prediction.predicted_home_score + prediction.predicted_away_score
            actual_total = game.home_score + game.away_score
            prediction.score_difference = abs(predicted_total - actual_total)
            prediction.total_runs_difference = abs(predicted_total - actual_total)
            
            updated_count += 1
    
    try:
        db.session.commit()
        print(f"  ✅ 更新完成，共更新 {updated_count} 個預測的準確性")
    except Exception as e:
        db.session.rollback()
        print(f"  ❌ 更新失敗: {e}")

def verify_fix():
    """驗證修復結果"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔍 驗證修復結果...")
        
        target_date = date(2025, 6, 26)
        
        # 檢查比賽結果
        games = Game.query.filter(Game.date == target_date).all()
        incomplete_games = [g for g in games if g.home_score is None or g.away_score is None]
        
        print(f"  比賽總數: {len(games)}")
        print(f"  不完整結果: {len(incomplete_games)}")
        
        if incomplete_games:
            print("  仍有不完整的比賽:")
            for game in incomplete_games:
                print(f"    {game.away_team} @ {game.home_team}: {game.away_score} - {game.home_score}")
        
        # 檢查預測準確性
        predictions = Prediction.query.join(Game).filter(
            Game.date == target_date
        ).all()
        
        predictions_without_accuracy = [p for p in predictions if p.is_correct is None]
        
        print(f"  預測總數: {len(predictions)}")
        print(f"  缺少準確性: {len(predictions_without_accuracy)}")
        
        # 計算準確率
        correct_predictions = [p for p in predictions if p.is_correct is True]
        if predictions:
            accuracy_rate = len(correct_predictions) / len(predictions) * 100
            print(f"  準確率: {accuracy_rate:.1f}% ({len(correct_predictions)}/{len(predictions)})")

if __name__ == "__main__":
    print("🚀 開始修復不完整的比賽得分...")
    
    fix_incomplete_scores()
    verify_fix()
    
    print("\n✅ 修復完成！請刷新瀏覽器頁面查看效果。")
