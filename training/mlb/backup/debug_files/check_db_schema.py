#!/usr/bin/env python3
"""
檢查數據庫架構和缺失的列
"""

import sqlite3
import os
from flask import Flask
from models.database import db, Prediction

def check_database_schema():
    """檢查數據庫架構"""
    
    # 檢查所有可能的數據庫文件
    db_files = [
        'mlb_data.db',
        'instance/mlb_data.db',
        'instance/mlb_predictions.db'
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"\n=== 檢查數據庫: {db_file} ===")
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 檢查predictions表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='predictions';")
                table_exists = cursor.fetchone()
                
                if table_exists:
                    print("✅ predictions表存在")
                    
                    # 獲取表結構
                    cursor.execute("PRAGMA table_info(predictions);")
                    columns = cursor.fetchall()
                    
                    print(f"當前列數: {len(columns)}")
                    print("現有列:")
                    existing_columns = []
                    for col in columns:
                        col_name = col[1]
                        col_type = col[2]
                        existing_columns.append(col_name)
                        print(f"  - {col_name} ({col_type})")
                    
                    # 檢查缺失的列
                    required_columns = [
                        'predicted_total_runs',
                        'over_under_line', 
                        'over_probability',
                        'under_probability',
                        'over_under_confidence',
                        'pitcher_vs_batter_analysis',
                        'starting_pitcher_home',
                        'starting_pitcher_away',
                        'pitcher_matchup_advantage',
                        'features_used',
                        'actual_total_runs',
                        'over_under_correct',
                        'total_runs_difference'
                    ]
                    
                    missing_columns = []
                    for col in required_columns:
                        if col not in existing_columns:
                            missing_columns.append(col)
                    
                    if missing_columns:
                        print(f"\n❌ 缺失的列 ({len(missing_columns)}):")
                        for col in missing_columns:
                            print(f"  - {col}")
                    else:
                        print("\n✅ 所有必需的列都存在")
                        
                else:
                    print("❌ predictions表不存在")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ 檢查數據庫時出錯: {e}")
        else:
            print(f"❌ 數據庫文件不存在: {db_file}")

def create_migration_sql():
    """生成遷移SQL"""
    
    migration_sql = """
-- 添加缺失的列到predictions表
ALTER TABLE predictions ADD COLUMN predicted_total_runs REAL;
ALTER TABLE predictions ADD COLUMN over_under_line REAL;
ALTER TABLE predictions ADD COLUMN over_probability REAL DEFAULT 0.0;
ALTER TABLE predictions ADD COLUMN under_probability REAL DEFAULT 0.0;
ALTER TABLE predictions ADD COLUMN over_under_confidence REAL DEFAULT 0.0;
ALTER TABLE predictions ADD COLUMN pitcher_vs_batter_analysis TEXT;
ALTER TABLE predictions ADD COLUMN starting_pitcher_home VARCHAR(100);
ALTER TABLE predictions ADD COLUMN starting_pitcher_away VARCHAR(100);
ALTER TABLE predictions ADD COLUMN pitcher_matchup_advantage VARCHAR(20);
ALTER TABLE predictions ADD COLUMN features_used TEXT;
ALTER TABLE predictions ADD COLUMN actual_total_runs INTEGER;
ALTER TABLE predictions ADD COLUMN over_under_correct BOOLEAN;
ALTER TABLE predictions ADD COLUMN total_runs_difference REAL;
"""
    
    return migration_sql

def apply_migration():
    """應用數據庫遷移"""
    
    # 檢查主數據庫文件
    db_files = ['mlb_data.db', 'instance/mlb_data.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"\n=== 應用遷移到: {db_file} ===")
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 檢查predictions表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='predictions';")
                if not cursor.fetchone():
                    print("❌ predictions表不存在，跳過遷移")
                    conn.close()
                    continue
                
                # 獲取現有列
                cursor.execute("PRAGMA table_info(predictions);")
                existing_columns = [col[1] for col in cursor.fetchall()]
                
                # 需要添加的列
                columns_to_add = [
                    ('predicted_total_runs', 'REAL'),
                    ('over_under_line', 'REAL'),
                    ('over_probability', 'REAL DEFAULT 0.0'),
                    ('under_probability', 'REAL DEFAULT 0.0'),
                    ('over_under_confidence', 'REAL DEFAULT 0.0'),
                    ('pitcher_vs_batter_analysis', 'TEXT'),
                    ('starting_pitcher_home', 'VARCHAR(100)'),
                    ('starting_pitcher_away', 'VARCHAR(100)'),
                    ('pitcher_matchup_advantage', 'VARCHAR(20)'),
                    ('features_used', 'TEXT'),
                    ('actual_total_runs', 'INTEGER'),
                    ('over_under_correct', 'BOOLEAN'),
                    ('total_runs_difference', 'REAL')
                ]
                
                added_count = 0
                for col_name, col_type in columns_to_add:
                    if col_name not in existing_columns:
                        try:
                            sql = f"ALTER TABLE predictions ADD COLUMN {col_name} {col_type};"
                            cursor.execute(sql)
                            print(f"✅ 添加列: {col_name}")
                            added_count += 1
                        except Exception as e:
                            print(f"❌ 添加列 {col_name} 失敗: {e}")
                    else:
                        print(f"⏭️  列已存在: {col_name}")
                
                conn.commit()
                conn.close()
                
                print(f"\n✅ 遷移完成，添加了 {added_count} 個列")
                
            except Exception as e:
                print(f"❌ 遷移失敗: {e}")

if __name__ == '__main__':
    print("🔍 檢查數據庫架構...")
    check_database_schema()
    
    print("\n" + "="*50)
    response = input("是否要應用數據庫遷移? (y/N): ")
    
    if response.lower() == 'y':
        apply_migration()
        print("\n🔍 遷移後重新檢查...")
        check_database_schema()
    else:
        print("遷移已取消")
