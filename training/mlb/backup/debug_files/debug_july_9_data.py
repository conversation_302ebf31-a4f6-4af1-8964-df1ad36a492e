#!/usr/bin/env python3
"""
調試 2025-07-09 的數據問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game, BettingOdds, Prediction

def debug_july_9_data():
    """調試 2025-07-09 的數據問題"""
    app = create_app()
    
    with app.app_context():
        test_date = date(2025, 7, 9)
        
        print(f"🔍 調試 {test_date} 的數據問題...")
        
        # 1. 檢查比賽數據
        games = Game.query.filter(Game.date == test_date).all()
        print(f"📊 數據庫中有 {len(games)} 場比賽:")
        for game in games:
            print(f"  - {game.away_team} @ {game.home_team} (ID: {game.game_id})")
            print(f"    狀態: {game.game_status}")
            print(f"    比分: {game.away_score} - {game.home_score}")
        
        # 2. 檢查盤口數據
        odds_count = BettingOdds.query.join(Game).filter(Game.date == test_date).count()
        print(f"📈 現有盤口記錄: {odds_count}")
        
        # 3. 檢查預測數據
        predictions = Prediction.query.join(Game).filter(Game.date == test_date).all()
        print(f"🎯 預測記錄: {len(predictions)}")
        
        # 4. 檢查具體的問題比賽
        problem_games = ["777172", "777175", "777163", "777184", "777179", "777183", "777204"]
        
        print(f"\n🔍 檢查問題比賽:")
        for game_id in problem_games:
            game = Game.query.filter_by(game_id=game_id).first()
            if game:
                print(f"\n比賽 {game_id}: {game.away_team} @ {game.home_team}")
                print(f"  日期: {game.date}")
                print(f"  狀態: {game.game_status}")
                print(f"  比分: {game.away_score} - {game.home_score}")
                
                # 檢查盤口數據
                odds = BettingOdds.query.filter_by(game_id=game_id).all()
                print(f"  盤口記錄: {len(odds)}")
                for odd in odds:
                    print(f"    - {odd.bookmaker} {odd.market_type}: 總分線={odd.total_point}")
                
                # 檢查預測數據
                pred = Prediction.query.filter_by(game_id=game_id).first()
                if pred:
                    print(f"  預測: {pred.predicted_away_score} - {pred.predicted_home_score}")
                    print(f"  實際結果: {pred.actual_away_score} - {pred.actual_home_score}")
                    print(f"  準確性: {pred.is_correct}")
                else:
                    print(f"  ❌ 沒有預測記錄")
            else:
                print(f"❌ 找不到比賽 {game_id}")

if __name__ == "__main__":
    debug_july_9_data()
