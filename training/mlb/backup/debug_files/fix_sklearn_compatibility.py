#!/usr/bin/env python3
"""
修復sklearn版本兼容性問題
重新保存模型文件以兼容當前sklearn版本，並整合偏差修正
"""

import os
import pickle
import joblib
import numpy as np
import pandas as pd
from datetime import datetime
import logging
from typing import Dict, List, Optional, Tuple
import sys

# 添加路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SklearnCompatibilityFixer:
    """sklearn版本兼容性修復器"""
    
    def __init__(self):
        self.current_sklearn_version = None
        self.model_paths = []
        self.backup_dir = "models/backup"
        self.fixed_models = {}
        
    def check_sklearn_version(self) -> str:
        """檢查當前sklearn版本"""
        try:
            import sklearn
            self.current_sklearn_version = sklearn.__version__
            print(f"📦 當前sklearn版本: {self.current_sklearn_version}")
            return self.current_sklearn_version
        except ImportError:
            print("❌ sklearn未安裝")
            return None
    
    def find_model_files(self) -> List[str]:
        """查找所有模型文件"""
        print(f"\n🔍 查找模型文件...")
        
        model_extensions = ['.pkl', '.joblib', '.pickle']
        model_files = []
        
        # 搜索models目錄
        for root, dirs, files in os.walk('models'):
            for file in files:
                if any(file.endswith(ext) for ext in model_extensions):
                    full_path = os.path.join(root, file)
                    model_files.append(full_path)
        
        self.model_paths = model_files
        print(f"📁 找到 {len(model_files)} 個模型文件:")
        for path in model_files:
            print(f"   {path}")
        
        return model_files
    
    def create_backup(self) -> bool:
        """創建模型文件備份"""
        print(f"\n💾 創建模型文件備份...")
        
        try:
            os.makedirs(self.backup_dir, exist_ok=True)
            
            for model_path in self.model_paths:
                # 創建備份路徑
                rel_path = os.path.relpath(model_path, 'models')
                backup_path = os.path.join(self.backup_dir, rel_path)
                backup_dir = os.path.dirname(backup_path)
                os.makedirs(backup_dir, exist_ok=True)
                
                # 複製文件
                import shutil
                shutil.copy2(model_path, backup_path)
                print(f"   ✅ 備份: {model_path} → {backup_path}")
            
            # 創建備份信息文件
            backup_info = {
                'created_at': datetime.now().isoformat(),
                'sklearn_version': self.current_sklearn_version,
                'backed_up_files': self.model_paths
            }
            
            import json
            with open(os.path.join(self.backup_dir, 'backup_info.json'), 'w') as f:
                json.dump(backup_info, f, indent=2)
            
            print(f"✅ 備份完成，共 {len(self.model_paths)} 個文件")
            return True
            
        except Exception as e:
            logger.error(f"備份失敗: {e}")
            return False
    
    def test_model_loading(self, model_path: str) -> Dict:
        """測試模型載入"""
        result = {
            'path': model_path,
            'loadable': False,
            'error': None,
            'model_type': None,
            'sklearn_version': None
        }
        
        try:
            # 嘗試用joblib載入
            try:
                model = joblib.load(model_path)
                result['loadable'] = True
                result['loader'] = 'joblib'
            except:
                # 嘗試用pickle載入
                with open(model_path, 'rb') as f:
                    model = pickle.load(f)
                result['loadable'] = True
                result['loader'] = 'pickle'
            
            # 獲取模型信息
            result['model_type'] = type(model).__name__
            
            # 檢查是否有sklearn版本信息
            if hasattr(model, '_sklearn_version'):
                result['sklearn_version'] = model._sklearn_version
                
        except Exception as e:
            result['error'] = str(e)
            
        return result
    
    def fix_model_compatibility(self, model_path: str) -> bool:
        """修復單個模型的兼容性"""
        print(f"\n🔧 修復模型: {model_path}")
        
        try:
            # 測試原始模型
            test_result = self.test_model_loading(model_path)
            
            if test_result['loadable']:
                print(f"   ✅ 模型可以載入")
                # 重新保存以確保兼容性
                if test_result['loader'] == 'joblib':
                    model = joblib.load(model_path)
                else:
                    with open(model_path, 'rb') as f:
                        model = pickle.load(f)
                
                # 檢查是否為預測模型，如果是則整合偏差修正
                model_name = os.path.basename(model_path)
                if 'score' in model_name.lower():
                    print(f"   🎯 檢測到分數預測模型，準備整合偏差修正...")
                    model = self._integrate_bias_correction(model, model_name)
                
                # 重新保存
                joblib.dump(model, model_path)
                print(f"   ✅ 重新保存完成")
                
                self.fixed_models[model_path] = {
                    'status': 'fixed',
                    'original_version': test_result.get('sklearn_version'),
                    'current_version': self.current_sklearn_version
                }
                
                return True
            else:
                print(f"   ❌ 模型無法載入: {test_result['error']}")
                self.fixed_models[model_path] = {
                    'status': 'failed',
                    'error': test_result['error']
                }
                return False
                
        except Exception as e:
            logger.error(f"修復模型 {model_path} 失敗: {e}")
            self.fixed_models[model_path] = {
                'status': 'failed',
                'error': str(e)
            }
            return False
    
    def _integrate_bias_correction(self, model, model_name: str):
        """整合偏差修正到模型中"""
        try:
            # 載入偏差修正配置
            from models.bias_correction import apply_bias_correction, get_bias_info
            
            # 創建包裝器類
            class BiascorrectedModel:
                def __init__(self, original_model, model_name):
                    self.original_model = original_model
                    self.model_name = model_name
                    self.bias_info = get_bias_info()
                    
                def predict(self, X):
                    # 原始預測
                    predictions = self.original_model.predict(X)
                    
                    # 應用偏差修正
                    if 'home' in self.model_name.lower():
                        # 主隊分數模型
                        corrected_predictions = predictions + self.bias_info['home_correction']
                    elif 'away' in self.model_name.lower():
                        # 客隊分數模型
                        corrected_predictions = predictions + self.bias_info['away_correction']
                    else:
                        # 其他模型不應用修正
                        corrected_predictions = predictions
                    
                    # 確保分數在合理範圍內
                    corrected_predictions = np.clip(corrected_predictions, 0, 20)
                    
                    return corrected_predictions
                    
                def __getattr__(self, name):
                    # 委託其他屬性到原始模型
                    return getattr(self.original_model, name)
            
            wrapped_model = BiasCorrectiveModel(model, model_name)
            print(f"   ✅ 已整合偏差修正")
            return wrapped_model
            
        except Exception as e:
            logger.warning(f"偏差修正整合失敗: {e}，使用原始模型")
            return model
    
    def test_all_models(self) -> Dict:
        """測試所有模型的載入狀況"""
        print(f"\n🧪 測試所有模型載入狀況...")
        
        results = {}
        loadable_count = 0
        
        for model_path in self.model_paths:
            result = self.test_model_loading(model_path)
            results[model_path] = result
            
            if result['loadable']:
                loadable_count += 1
                print(f"   ✅ {model_path} - {result['model_type']}")
            else:
                print(f"   ❌ {model_path} - {result['error']}")
        
        print(f"\n📊 測試結果: {loadable_count}/{len(self.model_paths)} 個模型可載入")
        
        return results
    
    def create_model_info(self) -> None:
        """創建模型信息文件"""
        print(f"\n📝 創建模型信息文件...")
        
        model_info = {
            'updated_at': datetime.now().isoformat(),
            'sklearn_version': self.current_sklearn_version,
            'models': {}
        }
        
        for model_path in self.model_paths:
            try:
                test_result = self.test_model_loading(model_path)
                model_info['models'][model_path] = {
                    'loadable': test_result['loadable'],
                    'model_type': test_result.get('model_type'),
                    'file_size': os.path.getsize(model_path),
                    'modified_time': datetime.fromtimestamp(os.path.getmtime(model_path)).isoformat()
                }
            except:
                model_info['models'][model_path] = {'status': 'error'}
        
        # 保存信息文件
        import json
        with open('models/model_info.json', 'w') as f:
            json.dump(model_info, f, indent=2)
        
        print(f"✅ 模型信息已保存到 models/model_info.json")
    
    def fix_all_sklearn_issues(self):
        """修復所有sklearn兼容性問題"""
        print("🔧 開始修復sklearn版本兼容性問題")
        print("=" * 60)
        
        # 步驟1: 檢查sklearn版本
        if not self.check_sklearn_version():
            print("❌ 無法檢查sklearn版本，退出")
            return
        
        # 步驟2: 查找模型文件
        if not self.find_model_files():
            print("❌ 沒有找到模型文件")
            return
        
        # 步驟3: 測試當前狀況
        print(f"\n📋 測試當前模型載入狀況...")
        test_results = self.test_all_models()
        
        # 統計不可載入的模型
        failed_models = [path for path, result in test_results.items() if not result['loadable']]
        
        if not failed_models:
            print("✅ 所有模型都可以正常載入，但仍建議重新保存以確保兼容性")
        else:
            print(f"⚠️  {len(failed_models)} 個模型無法載入，需要修復")
        
        # 步驟4: 創建備份
        if not self.create_backup():
            print("❌ 備份失敗，為安全起見停止修復")
            return
        
        # 步驟5: 修復所有模型
        print(f"\n🔧 開始修復模型...")
        fixed_count = 0
        
        for model_path in self.model_paths:
            if self.fix_model_compatibility(model_path):
                fixed_count += 1
        
        # 步驟6: 最終測試
        print(f"\n🧪 最終測試...")
        final_results = self.test_all_models()
        final_loadable = sum(1 for result in final_results.values() if result['loadable'])
        
        # 步驟7: 創建模型信息
        self.create_model_info()
        
        # 總結
        print(f"\n🎉 sklearn兼容性修復完成!")
        print(f"✅ 處理模型: {len(self.model_paths)} 個")
        print(f"✅ 修復成功: {fixed_count} 個")
        print(f"✅ 最終可載入: {final_loadable}/{len(self.model_paths)} 個")
        print(f"📁 備份位置: {self.backup_dir}")
        print(f"📁 模型信息: models/model_info.json")
        
        if final_loadable == len(self.model_paths):
            print(f"🎯 所有模型已兼容sklearn {self.current_sklearn_version}")
        else:
            print(f"⚠️  仍有 {len(self.model_paths) - final_loadable} 個模型存在問題")
            
        # 建議下一步
        print(f"\n💡 下一步建議:")
        print(f"1. 測試修復後的模型在預測中的表現")
        print(f"2. 如果模型表現良好，可以刪除備份文件")
        print(f"3. 重新訓練模型以獲得最佳性能")

def main():
    """主函數"""
    fixer = SklearnCompatibilityFixer()
    fixer.fix_all_sklearn_issues()

if __name__ == "__main__":
    main()