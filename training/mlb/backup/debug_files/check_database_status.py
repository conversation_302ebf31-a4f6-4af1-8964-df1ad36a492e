#!/usr/bin/env python3
"""
檢查數據庫狀態和數據完整性
"""

from app import create_app
from models.database import db, Game, Team, Player, PlayerStats, BoxScore, PlayerGameStats, Prediction
from datetime import date, timedelta

def check_basic_counts():
    """檢查基本數據表計數"""
    print("📊 基本數據表統計:")
    print("-" * 40)
    
    tables = [
        ("比賽", Game),
        ("球隊", Team),
        ("球員", Player),
        ("球員統計", PlayerStats),
        ("Box Scores", BoxScore),
        ("球員比賽統計", PlayerGameStats),
        ("預測", Prediction)
    ]
    
    for name, model in tables:
        count = model.query.count()
        print(f"{name:12}: {count:,}")

def check_data_by_year():
    """按年份檢查數據分布"""
    print("\n📅 數據按年份分布:")
    print("-" * 40)
    
    # 檢查比賽按年份分布
    games_by_year = db.session.query(
        db.func.strftime('%Y', Game.date).label('year'),
        db.func.count(Game.game_id).label('count')
    ).group_by(db.func.strftime('%Y', Game.date)).order_by('year').all()
    
    print("比賽按年份:")
    for year, count in games_by_year:
        print(f"  {year}年: {count:,} 場")
    
    # 檢查球員統計按年份分布
    stats_by_year = db.session.query(
        PlayerStats.season,
        db.func.count(PlayerStats.player_id).label('count')
    ).group_by(PlayerStats.season).order_by(PlayerStats.season).all()
    
    print("\n球員統計按年份:")
    for year, count in stats_by_year:
        print(f"  {year}年: {count:,} 條")
    
    # 檢查Box Score按年份分布
    boxscore_by_year = db.session.query(
        db.func.strftime('%Y', Game.date).label('year'),
        db.func.count(BoxScore.box_score_id).label('count')
    ).join(Game, BoxScore.game_id == Game.game_id)\
     .group_by(db.func.strftime('%Y', Game.date))\
     .order_by('year').all()
    
    print("\nBox Score按年份:")
    for year, count in boxscore_by_year:
        print(f"  {year}年: {count:,} 場")

def check_data_completeness():
    """檢查數據完整性"""
    print("\n🔍 數據完整性檢查:")
    print("-" * 40)
    
    # 檢查最近三年的Box Score完整性
    current_year = date.today().year
    for year in [current_year-2, current_year-1, current_year]:
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        
        total_completed_games = Game.query.filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.game_status == 'completed'
        ).count()
        
        games_with_boxscore = db.session.query(Game).filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.game_status == 'completed',
            Game.game_id.in_(
                db.session.query(BoxScore.game_id).distinct()
            )
        ).count()
        
        if total_completed_games > 0:
            percentage = (games_with_boxscore / total_completed_games) * 100
            print(f"{year}年 Box Score完整性: {games_with_boxscore}/{total_completed_games} ({percentage:.1f}%)")
        else:
            print(f"{year}年: 沒有已完成的比賽")

def check_recent_data():
    """檢查最近的數據"""
    print("\n📈 最近數據檢查:")
    print("-" * 40)
    
    # 檢查最近7天的比賽
    end_date = date.today()
    start_date = end_date - timedelta(days=7)
    
    recent_games = Game.query.filter(
        Game.date >= start_date,
        Game.date <= end_date
    ).count()
    
    print(f"最近7天比賽: {recent_games} 場")
    
    # 檢查最近的預測
    recent_predictions = Prediction.query.filter(
        Prediction.prediction_date >= start_date
    ).count()
    
    print(f"最近7天預測: {recent_predictions} 個")
    
    # 檢查最新的球員統計
    latest_stats = PlayerStats.query.order_by(PlayerStats.season.desc()).first()
    if latest_stats:
        print(f"最新球員統計: {latest_stats.season}年")
    
    # 檢查最新的Box Score
    latest_boxscore = db.session.query(BoxScore, Game).join(
        Game, BoxScore.game_id == Game.game_id
    ).order_by(Game.date.desc()).first()
    
    if latest_boxscore:
        print(f"最新Box Score: {latest_boxscore.Game.date}")

def check_data_quality():
    """檢查數據質量"""
    print("\n✅ 數據質量檢查:")
    print("-" * 40)
    
    # 檢查空值
    games_without_scores = Game.query.filter(
        Game.game_status == 'completed',
        db.or_(Game.home_score.is_(None), Game.away_score.is_(None))
    ).count()
    
    print(f"已完成但缺少分數的比賽: {games_without_scores}")
    
    # 檢查重複數據
    duplicate_games = db.session.query(
        Game.game_id,
        db.func.count(Game.id).label('count')
    ).group_by(Game.game_id).having(db.func.count(Game.id) > 1).count()
    
    print(f"重複的比賽記錄: {duplicate_games}")
    
    # 檢查球員統計的完整性
    players_without_stats = db.session.query(Player).filter(
        ~Player.player_id.in_(
            db.session.query(PlayerStats.player_id).distinct()
        )
    ).count()
    
    print(f"沒有統計數據的球員: {players_without_stats}")

def main():
    """主檢查函數"""
    app = create_app()
    
    with app.app_context():
        print("🔍 MLB數據庫狀態檢查")
        print("=" * 50)
        
        try:
            check_basic_counts()
            check_data_by_year()
            check_data_completeness()
            check_recent_data()
            check_data_quality()
            
            print("\n" + "=" * 50)
            print("✅ 數據庫狀態檢查完成")
            
        except Exception as e:
            print(f"❌ 檢查過程中發生錯誤: {e}")
            return False
    
    return True

if __name__ == '__main__':
    main()
