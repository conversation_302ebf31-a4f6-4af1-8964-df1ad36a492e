#!/usr/bin/env python3
"""
數據庫遷移腳本 - 添加缺失的列
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db
from sqlalchemy import text
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_column_exists(table_name, column_name):
    """檢查列是否存在"""
    try:
        result = db.session.execute(text(f"PRAGMA table_info({table_name})"))
        columns = [row[1] for row in result.fetchall()]
        return column_name in columns
    except Exception as e:
        logger.error(f"檢查列 {table_name}.{column_name} 失敗: {e}")
        return False

def add_column_if_not_exists(table_name, column_name, column_definition):
    """如果列不存在則添加"""
    if not check_column_exists(table_name, column_name):
        try:
            sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition}"
            db.session.execute(text(sql))
            db.session.commit()
            logger.info(f"✅ 添加列: {table_name}.{column_name}")
            return True
        except Exception as e:
            logger.error(f"❌ 添加列 {table_name}.{column_name} 失敗: {e}")
            db.session.rollback()
            return False
    else:
        logger.info(f"⏭️ 列已存在: {table_name}.{column_name}")
        return True

def migrate_predictions_table():
    """遷移predictions表"""
    logger.info("開始遷移predictions表...")
    
    # 需要添加的列
    columns_to_add = [
        # 大小分預測相關
        ("predicted_total_runs", "REAL"),
        ("over_under_line", "REAL"),
        ("over_probability", "REAL DEFAULT 0.0"),
        ("under_probability", "REAL DEFAULT 0.0"),
        ("over_under_confidence", "REAL DEFAULT 0.0"),
        
        # 投手對打者分析相關
        ("pitcher_vs_batter_analysis", "TEXT"),
        ("starting_pitcher_home", "VARCHAR(100)"),
        ("starting_pitcher_away", "VARCHAR(100)"),
        ("pitcher_matchup_advantage", "VARCHAR(20)"),
        
        # 預測特徵
        ("features_used", "TEXT"),
        
        # 大小分結果驗證
        ("actual_total_runs", "INTEGER"),
        ("over_under_correct", "BOOLEAN"),
        ("total_runs_difference", "REAL"),
    ]
    
    success_count = 0
    for column_name, column_definition in columns_to_add:
        if add_column_if_not_exists("predictions", column_name, column_definition):
            success_count += 1
    
    logger.info(f"predictions表遷移完成: {success_count}/{len(columns_to_add)} 列成功")
    return success_count == len(columns_to_add)

def migrate_database():
    """執行數據庫遷移"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("🔄 開始數據庫遷移...")
            
            # 檢查數據庫連接
            db.session.execute(text('SELECT 1'))
            logger.info("✅ 數據庫連接正常")
            
            # 遷移predictions表
            if migrate_predictions_table():
                logger.info("✅ predictions表遷移成功")
            else:
                logger.error("❌ predictions表遷移失敗")
                return False
            
            # 確保所有表都存在
            logger.info("確保所有表都存在...")
            db.create_all()
            logger.info("✅ 數據庫表檢查完成")
            
            logger.info("🎉 數據庫遷移完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 數據庫遷移失敗: {e}")
            import traceback
            traceback.print_exc()
            return False

def verify_migration():
    """驗證遷移結果"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("🔍 驗證遷移結果...")
            
            # 檢查predictions表結構
            result = db.session.execute(text("PRAGMA table_info(predictions)"))
            columns = [row[1] for row in result.fetchall()]
            
            required_columns = [
                'predicted_total_runs', 'over_under_line', 'over_probability',
                'under_probability', 'over_under_confidence', 'pitcher_vs_batter_analysis',
                'starting_pitcher_home', 'starting_pitcher_away', 'pitcher_matchup_advantage',
                'features_used', 'actual_total_runs', 'over_under_correct', 'total_runs_difference'
            ]
            
            missing_columns = []
            for col in required_columns:
                if col not in columns:
                    missing_columns.append(col)
            
            if missing_columns:
                logger.error(f"❌ 缺失列: {missing_columns}")
                return False
            else:
                logger.info("✅ 所有必需列都存在")
                return True
                
        except Exception as e:
            logger.error(f"❌ 驗證失敗: {e}")
            return False

if __name__ == "__main__":
    print("🏗️ MLB數據庫遷移工具")
    print("=" * 50)
    
    # 執行遷移
    if migrate_database():
        print("\n🔍 驗證遷移結果...")
        if verify_migration():
            print("\n🎉 數據庫遷移成功完成！")
            sys.exit(0)
        else:
            print("\n❌ 遷移驗證失敗！")
            sys.exit(1)
    else:
        print("\n❌ 數據庫遷移失敗！")
        sys.exit(1)
