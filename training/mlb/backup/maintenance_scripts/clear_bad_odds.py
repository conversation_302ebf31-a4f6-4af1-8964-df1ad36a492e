#!/usr/bin/env python3
"""
清除錯誤的賠率數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import db, BettingOdds
from app import create_app

def clear_bad_odds():
    """清除錯誤的賠率數據"""
    print("=== 清除錯誤的賠率數據 ===")
    
    app = create_app()
    
    with app.app_context():
        # 刪除所有covers.com的賠率記錄
        covers_odds = BettingOdds.query.filter_by(bookmaker='covers.com').all()
        
        print(f"找到 {len(covers_odds)} 條covers.com賠率記錄")
        
        for odds in covers_odds:
            db.session.delete(odds)
        
        db.session.commit()
        print("✅ 已清除所有covers.com賠率記錄")
        
        # 驗證清除結果
        remaining_odds = BettingOdds.query.filter_by(bookmaker='covers.com').count()
        print(f"剩餘covers.com記錄: {remaining_odds}")

if __name__ == "__main__":
    clear_bad_odds()
