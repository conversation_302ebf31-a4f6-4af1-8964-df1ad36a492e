#!/usr/bin/env python3
"""
清理所有模擬/估算的博彩數據
移除所有來源為 estimated, Simulated, realistic_estimate 的數據
"""

import sqlite3
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clean_estimated_betting_data():
    """清理所有模擬/估算的博彩數據"""
    
    print("🧹 開始清理所有模擬/估算的博彩數據...")
    print("=" * 60)
    
    # 連接數據庫
    conn = sqlite3.connect('instance/mlb_data.db')
    cursor = conn.cursor()
    
    try:
        # 1. 先查看要刪除的數據統計
        cursor.execute("""
            SELECT 
                COUNT(*) as count,
                market_type,
                bookmaker
            FROM betting_odds 
            WHERE bookmaker IN ('estimated', 'Simulated', 'realistic_estimate')
            GROUP BY market_type, bookmaker
            ORDER BY market_type, bookmaker
        """)
        
        stats_before = cursor.fetchall()
        
        print("📊 清理前的模擬數據統計：")
        print("-" * 50)
        total_to_delete = 0
        for count, market_type, bookmaker in stats_before:
            print(f"{market_type:^8} | {bookmaker:^18} | {count:^5}")
            total_to_delete += count
        
        print(f"\n🎯 總計要刪除: {total_to_delete} 條模擬數據")
        
        if total_to_delete == 0:
            print("✅ 沒有找到需要清理的模擬數據")
            return
        
        # 2. 刪除所有模擬數據
        cursor.execute("""
            DELETE FROM betting_odds 
            WHERE bookmaker IN ('estimated', 'Simulated', 'realistic_estimate')
        """)
        
        deleted_count = cursor.rowcount
        print(f"🗑️  成功刪除 {deleted_count} 條模擬數據")
        
        # 3. 驗證清理結果
        cursor.execute("""
            SELECT COUNT(*) 
            FROM betting_odds 
            WHERE bookmaker IN ('estimated', 'Simulated', 'realistic_estimate')
        """)
        
        remaining_count = cursor.fetchone()[0]
        
        if remaining_count == 0:
            print("✅ 所有模擬數據已成功清理")
        else:
            print(f"⚠️  仍有 {remaining_count} 條模擬數據未清理")
        
        # 4. 查看清理後的數據源分布
        cursor.execute("""
            SELECT 
                COUNT(*) as count,
                market_type,
                bookmaker
            FROM betting_odds 
            GROUP BY market_type, bookmaker
            ORDER BY market_type, bookmaker
        """)
        
        stats_after = cursor.fetchall()
        
        print(f"\n📈 清理後的數據源分布：")
        print("-" * 50)
        print("市場類型 | 數據源 | 數量")
        print("-" * 50)
        for count, market_type, bookmaker in stats_after:
            print(f"{market_type:^8} | {bookmaker:^15} | {count:^5}")
        
        # 5. 提交更改
        conn.commit()
        print(f"\n💾 數據庫更改已提交")
        
    except Exception as e:
        conn.rollback()
        logger.error(f"清理模擬數據失敗: {e}")
        print(f"❌ 操作失敗: {e}")
        
    finally:
        conn.close()

def check_games_without_odds():
    """檢查沒有博彩數據的比賽"""
    
    print("\n🔍 檢查沒有博彩數據的比賽...")
    print("=" * 60)
    
    conn = sqlite3.connect('instance/mlb_data.db')
    cursor = conn.cursor()
    
    try:
        # 查找沒有任何博彩數據的比賽
        cursor.execute("""
            SELECT 
                g.date,
                g.away_team || '@' || g.home_team as matchup,
                g.game_id,
                COUNT(bo.game_id) as odds_count
            FROM games g
            LEFT JOIN betting_odds bo ON g.game_id = bo.game_id
            WHERE g.date >= '2025-07-01'  -- 只檢查最近的比賽
            GROUP BY g.game_id, g.date, g.away_team, g.home_team
            HAVING COUNT(bo.game_id) = 0
            ORDER BY g.date DESC, g.away_team
        """)
        
        games_without_odds = cursor.fetchall()
        
        if games_without_odds:
            print(f"⚠️  發現 {len(games_without_odds)} 場比賽沒有博彩數據：")
            print("-" * 60)
            print("日期 | 比賽對戰 | 比賽ID")
            print("-" * 60)
            for date, matchup, game_id, odds_count in games_without_odds:
                print(f"{date} | {matchup:^12} | {game_id}")
        else:
            print("✅ 所有比賽都有博彩數據")
        
        # 查找只有部分博彩數據的比賽
        cursor.execute("""
            SELECT 
                g.date,
                g.away_team || '@' || g.home_team as matchup,
                g.game_id,
                COUNT(CASE WHEN bo.market_type = 'totals' THEN 1 END) as totals_count,
                COUNT(CASE WHEN bo.market_type = 'spreads' THEN 1 END) as spreads_count,
                COUNT(CASE WHEN bo.market_type = 'h2h' THEN 1 END) as h2h_count
            FROM games g
            LEFT JOIN betting_odds bo ON g.game_id = bo.game_id
            WHERE g.date >= '2025-07-01'
            GROUP BY g.game_id, g.date, g.away_team, g.home_team
            HAVING COUNT(bo.game_id) > 0 AND (
                COUNT(CASE WHEN bo.market_type = 'totals' THEN 1 END) = 0 OR
                COUNT(CASE WHEN bo.market_type = 'spreads' THEN 1 END) = 0
            )
            ORDER BY g.date DESC, g.away_team
        """)
        
        incomplete_games = cursor.fetchall()
        
        if incomplete_games:
            print(f"\n⚠️  發現 {len(incomplete_games)} 場比賽博彩數據不完整：")
            print("-" * 80)
            print("日期 | 比賽對戰 | 大小分 | 讓分盤 | 勝負盤")
            print("-" * 80)
            for date, matchup, game_id, totals, spreads, h2h in incomplete_games:
                totals_status = "✅" if totals > 0 else "❌"
                spreads_status = "✅" if spreads > 0 else "❌"
                h2h_status = "✅" if h2h > 0 else "❌"
                print(f"{date} | {matchup:^12} | {totals_status:^6} | {spreads_status:^6} | {h2h_status:^6}")
        else:
            print("\n✅ 所有有博彩數據的比賽都是完整的")
        
    except Exception as e:
        logger.error(f"檢查比賽數據失敗: {e}")
        print(f"❌ 檢查失敗: {e}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    print("🧹 MLB博彩數據清理工具")
    print("=" * 60)
    
    # 1. 清理所有模擬數據
    clean_estimated_betting_data()
    
    # 2. 檢查清理後的狀況
    check_games_without_odds()
    
    print("\n🎉 博彩數據清理完成！")
    print("\n💡 提醒：")
    print("   - 所有模擬/估算數據已清理")
    print("   - 請確保真實博彩數據獲取功能正常")
    print("   - 建議重新獲取最新的真實博彩數據")
