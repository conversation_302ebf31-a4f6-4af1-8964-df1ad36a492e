#!/usr/bin/env python3
"""
最終驗證所有預測數據
"""

import sqlite3
import os
from datetime import datetime, date

def final_verification():
    """最終驗證所有預測數據"""
    print("🔍 最終驗證所有預測數據")
    print("=" * 60)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    if not os.path.exists(db_path):
        print(f"❌ 數據庫文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 檢查總體統計
        print("1. 📊 總體統計:")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN p.predicted_total_runs BETWEEN 4 AND 12 
                           AND p.over_under_line > 0 THEN 1 END) as normal_predictions,
                COUNT(CASE WHEN p.predicted_total_runs > 12 
                           OR p.over_under_line = 0 
                           OR p.over_under_line IS NULL THEN 1 END) as abnormal_predictions
            FROM games g
            JOIN predictions p ON g.game_id = p.game_id
            WHERE g.date >= '2025-07-01'
        """)
        
        total, normal, abnormal = cursor.fetchone()
        print(f"   總預測數: {total}")
        print(f"   ✅ 正常預測: {normal} ({normal/total*100:.1f}%)")
        print(f"   ❌ 異常預測: {abnormal} ({abnormal/total*100:.1f}%)")
        
        # 2. 按日期檢查
        print("\n2. 📅 按日期統計:")
        cursor.execute("""
            SELECT 
                g.date,
                COUNT(*) as total,
                COUNT(CASE WHEN p.predicted_total_runs BETWEEN 4 AND 12 
                           AND p.over_under_line > 0 THEN 1 END) as normal,
                AVG(p.predicted_total_runs) as avg_total,
                AVG(p.over_under_line) as avg_line
            FROM games g
            JOIN predictions p ON g.game_id = p.game_id
            WHERE g.date >= '2025-07-01'
            GROUP BY g.date
            ORDER BY g.date
        """)
        
        dates = cursor.fetchall()
        for date_str, total, normal, avg_total, avg_line in dates:
            status = "✅" if normal == total else "⚠️"
            print(f"   {status} {date_str}: {normal}/{total} 正常 (平均總分: {avg_total:.1f}, 平均盤口: {avg_line:.1f})")
        
        # 3. 檢查特定問題案例
        print("\n3. 🔍 檢查之前的問題案例:")
        problem_cases = [
            ('2025-07-01', 'STL', 'PIT'),
            ('2025-07-04', 'BOS', 'WSH'),
            ('2025-07-04', 'STL', 'CHC'),
            ('2025-07-04', 'PIT', 'SEA'),
        ]
        
        for date_str, away, home in problem_cases:
            cursor.execute("""
                SELECT 
                    g.away_team || '@' || g.home_team as matchup,
                    p.predicted_total_runs,
                    p.over_under_line,
                    p.predicted_away_score,
                    p.predicted_home_score
                FROM games g
                JOIN predictions p ON g.game_id = p.game_id
                WHERE g.date = ? AND g.away_team = ? AND g.home_team = ?
                LIMIT 1
            """, (date_str, away, home))
            
            result = cursor.fetchone()
            if result:
                matchup, total, line, away_score, home_score = result
                if total and 4 <= total <= 12 and line and line > 0:
                    status = "✅"
                else:
                    status = "❌"
                print(f"   {status} {matchup}: {away_score}-{home_score} (總分: {total}, 盤口: {line})")
            else:
                print(f"   ⚠️  {away}@{home} ({date_str}): 未找到數據")
        
        # 4. 檢查盤口分佈
        print("\n4. 📈 盤口分佈:")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN p.over_under_line < 7 THEN '< 7.0'
                    WHEN p.over_under_line < 8 THEN '7.0-7.9'
                    WHEN p.over_under_line < 9 THEN '8.0-8.9'
                    WHEN p.over_under_line < 10 THEN '9.0-9.9'
                    ELSE '>= 10.0'
                END as line_range,
                COUNT(*) as count
            FROM games g
            JOIN predictions p ON g.game_id = p.game_id
            WHERE g.date >= '2025-07-01' AND p.over_under_line > 0
            GROUP BY line_range
            ORDER BY MIN(p.over_under_line)
        """)
        
        line_distribution = cursor.fetchall()
        for line_range, count in line_distribution:
            print(f"   {line_range}: {count} 場比賽")
        
        # 5. 檢查大小分推薦分佈
        print("\n5. 🎯 大小分推薦分佈:")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN p.predicted_total_runs > p.over_under_line THEN '大分'
                    WHEN p.predicted_total_runs < p.over_under_line THEN '小分'
                    ELSE '平手'
                END as recommendation,
                COUNT(*) as count
            FROM games g
            JOIN predictions p ON g.game_id = p.game_id
            WHERE g.date >= '2025-07-01' 
            AND p.over_under_line > 0 
            AND p.predicted_total_runs IS NOT NULL
            GROUP BY recommendation
        """)
        
        recommendations = cursor.fetchall()
        total_recs = sum(count for _, count in recommendations)
        for rec, count in recommendations:
            percentage = count / total_recs * 100 if total_recs > 0 else 0
            print(f"   {rec}: {count} 場 ({percentage:.1f}%)")
        
        # 6. 最新更新時間
        print("\n6. ⏰ 最新更新時間:")
        cursor.execute("""
            SELECT MAX(p.updated_at) as latest_update
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-07-01'
        """)
        
        latest_update = cursor.fetchone()[0]
        if latest_update:
            print(f"   最後更新: {latest_update}")
        
        conn.close()
        
        # 7. 總結
        print("\n" + "=" * 60)
        if abnormal == 0:
            print("🎉 恭喜！所有預測數據已完全修復！")
            print("✅ 所有預測總分都在合理範圍內 (4-12分)")
            print("✅ 所有比賽都有有效的盤口數據")
            print("✅ 網頁界面應該顯示正確的預測結果")
        else:
            print(f"⚠️  還有 {abnormal} 個異常預測需要處理")
        
        print(f"\n🌐 請訪問: http://localhost:5500/predictions/over_under")
        print("📊 查看修復後的預測結果")
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")

if __name__ == '__main__':
    final_verification()
