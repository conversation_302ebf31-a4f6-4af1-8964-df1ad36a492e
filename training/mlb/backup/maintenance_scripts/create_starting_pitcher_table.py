#!/usr/bin/env python3
"""
創建先發投手記錄表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db
from models.starting_pitcher_tracker import Starting<PERSON><PERSON><PERSON>Record

def create_starting_pitcher_table():
    """創建先發投手記錄表"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🏗️ 創建先發投手記錄表...")
            
            # 創建表
            db.create_all()
            
            print("✅ 先發投手記錄表創建成功！")
            
            # 檢查表結構
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            
            if 'starting_pitcher_records' in inspector.get_table_names():
                columns = inspector.get_columns('starting_pitcher_records')
                print(f"\n📋 表結構 (共 {len(columns)} 個欄位):")
                for col in columns:
                    print(f"  - {col['name']} ({col['type']})")
            else:
                print("❌ 表創建失敗")
                
        except Exception as e:
            print(f"❌ 創建表失敗: {e}")

if __name__ == "__main__":
    create_starting_pitcher_table()
