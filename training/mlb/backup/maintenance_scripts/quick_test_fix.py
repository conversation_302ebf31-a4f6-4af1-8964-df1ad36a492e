#!/usr/bin/env python3
"""
快速測試修復 - 驗證球隊得分差異
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.ml_predictor import MLBPredictor
from datetime import date

def quick_test():
    """快速測試預測差異"""
    app = create_app()
    with app.app_context():
        print("🔧 快速測試預測修復")
        print("=" * 40)
        
        try:
            predictor = MLBPredictor()
            
            # 測試幾個不同實力的球隊對戰
            test_games = [
                ('LAD', 'OAK'),  # 強隊 vs 弱隊
                ('NYY', 'MIA'),  # 強隊 vs 弱隊  
                ('HOU', 'COL'),  # 強隊 vs 弱隊
                ('OAK', 'LAD'),  # 弱隊 vs 強隊
                ('MIA', 'NYY'),  # 弱隊 vs 強隊
            ]
            
            print("測試預測結果:")
            results = []
            
            for home_team, away_team in test_games:
                try:
                    prediction = predictor.predict_game(home_team, away_team, date(2025, 6, 29))
                    
                    home_score = prediction['predicted_home_score']
                    away_score = prediction['predicted_away_score']
                    total = home_score + away_score
                    
                    results.append({
                        'matchup': f"{away_team} @ {home_team}",
                        'home_score': home_score,
                        'away_score': away_score,
                        'total': total
                    })
                    
                    print(f"  {away_team} @ {home_team}: {away_score:.1f} - {home_score:.1f} (總分: {total:.1f})")
                    
                except Exception as e:
                    print(f"  {away_team} @ {home_team}: ❌ 預測失敗 - {e}")
            
            # 分析結果
            if len(results) >= 3:
                print(f"\n📊 結果分析:")
                home_scores = [r['home_score'] for r in results]
                away_scores = [r['away_score'] for r in results]
                totals = [r['total'] for r in results]
                
                print(f"主隊得分範圍: {min(home_scores):.1f} - {max(home_scores):.1f}")
                print(f"客隊得分範圍: {min(away_scores):.1f} - {max(away_scores):.1f}")
                print(f"總分範圍: {min(totals):.1f} - {max(totals):.1f}")
                
                # 檢查差異
                home_diff = max(home_scores) - min(home_scores)
                away_diff = max(away_scores) - min(away_scores)
                total_diff = max(totals) - min(totals)
                
                print(f"\n差異分析:")
                print(f"主隊得分差異: {home_diff:.1f}")
                print(f"客隊得分差異: {away_diff:.1f}")
                print(f"總分差異: {total_diff:.1f}")
                
                if home_diff > 0.5 or away_diff > 0.5 or total_diff > 1.0:
                    print("✅ 修復成功! 預測有合理差異")
                else:
                    print("❌ 修復失敗! 預測差異仍然太小")
                    
                    # 檢查是否所有預測都相同
                    unique_home = len(set(f"{s:.1f}" for s in home_scores))
                    unique_away = len(set(f"{s:.1f}" for s in away_scores))
                    
                    print(f"主隊得分唯一值數量: {unique_home}")
                    print(f"客隊得分唯一值數量: {unique_away}")
                    
                    if unique_home == 1 and unique_away == 1:
                        print("⚠️  所有預測都完全相同!")
            
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    quick_test()
