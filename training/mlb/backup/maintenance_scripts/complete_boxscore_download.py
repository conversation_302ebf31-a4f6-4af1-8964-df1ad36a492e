#!/usr/bin/env python3
"""
完整下載所有缺失的Box Score數據
這是分析的核心數據來源，必須確保完整性
"""

import logging
from datetime import date, timedelta
from app import create_app
from models.detailed_data_fetcher import DetailedDataFetcher
from models.database import db, Game, BoxScore

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_missing_boxscores_by_year(year):
    """獲取指定年份缺失Box Score的比賽"""
    start_date = date(year, 1, 1)
    end_date = date(year, 12, 31)
    
    # 查找該年份沒有Box Score的已完成比賽
    missing_games = db.session.query(Game).filter(
        Game.date >= start_date,
        Game.date <= end_date,
        Game.game_status == 'completed',
        ~Game.game_id.in_(
            db.session.query(BoxScore.game_id).distinct()
        )
    ).order_by(Game.date.desc()).all()
    
    return missing_games

def download_year_boxscores(year, max_games=None):
    """下載指定年份的Box Score"""
    logger.info(f"🏟️ 開始下載 {year} 年的Box Score數據...")
    
    missing_games = get_missing_boxscores_by_year(year)
    
    if not missing_games:
        logger.info(f"✅ {year} 年的Box Score已經完整")
        return 0
    
    total_missing = len(missing_games)
    if max_games:
        missing_games = missing_games[:max_games]
        logger.info(f"📊 {year} 年缺失 {total_missing} 場，本次下載 {len(missing_games)} 場")
    else:
        logger.info(f"📊 {year} 年缺失 {total_missing} 場，準備全部下載")
    
    fetcher = DetailedDataFetcher()
    success_count = 0
    
    for i, game in enumerate(missing_games, 1):
        try:
            logger.info(f"[{i}/{len(missing_games)}] {year} - {game.away_team} @ {game.home_team} ({game.date})")
            
            success = fetcher.fetch_game_boxscore(game.game_id)
            if success:
                success_count += 1
                logger.info(f"  ✓ 成功下載")
            else:
                logger.warning(f"  ✗ 下載失敗")
            
            # 每5場比賽後暫停，避免請求過於頻繁
            if i % 5 == 0:
                import time
                time.sleep(1)
                
                # 每50場比賽提交一次數據庫
                if i % 50 == 0:
                    try:
                        db.session.commit()
                        logger.info(f"  💾 已保存前 {i} 場比賽的數據")
                    except Exception as e:
                        logger.error(f"  ❌ 保存數據失敗: {e}")
                        db.session.rollback()
                
        except Exception as e:
            logger.error(f"下載比賽 {game.game_id} 失敗: {e}")
    
    # 最終提交
    try:
        db.session.commit()
        logger.info(f"✅ {year} 年Box Score下載完成: {success_count}/{len(missing_games)} 成功")
    except Exception as e:
        logger.error(f"❌ 最終保存失敗: {e}")
        db.session.rollback()
    
    return success_count

def print_current_status():
    """打印當前Box Score狀態"""
    logger.info("📈 當前Box Score狀態:")
    
    years = [2023, 2024, 2025]
    for year in years:
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        
        total_completed = Game.query.filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.game_status == 'completed'
        ).count()
        
        with_boxscore = db.session.query(Game).filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.game_status == 'completed',
            Game.game_id.in_(
                db.session.query(BoxScore.game_id).distinct()
            )
        ).count()
        
        if total_completed > 0:
            percentage = (with_boxscore / total_completed) * 100
            missing = total_completed - with_boxscore
            logger.info(f"  {year}年: {with_boxscore}/{total_completed} ({percentage:.1f}%) - 缺失 {missing} 場")
        else:
            logger.info(f"  {year}年: 沒有已完成的比賽")

def main():
    """主函數 - 完整下載所有缺失的Box Score"""
    app = create_app()
    
    with app.app_context():
        logger.info("🚀 開始完整Box Score數據下載...")
        logger.info("=" * 60)
        
        # 打印初始狀態
        print_current_status()
        
        logger.info("\n" + "=" * 60)
        logger.info("開始下載缺失的Box Score數據...")
        
        total_downloaded = 0
        
        # 按優先級下載：2024年 -> 2023年 -> 2025年補齊
        download_order = [
            (2024, "2024年數據（優先）"),
            (2023, "2023年數據（完整）"), 
            (2025, "2025年補齊")
        ]
        
        for year, description in download_order:
            try:
                logger.info(f"\n📅 處理 {description}")
                logger.info("-" * 40)
                
                downloaded = download_year_boxscores(year)
                total_downloaded += downloaded
                
                logger.info(f"✅ {description} 完成，下載了 {downloaded} 場比賽")
                
                # 每年之間暫停一下
                import time
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ 下載 {year} 年數據失敗: {e}")
        
        logger.info("\n" + "=" * 60)
        logger.info(f"🎉 完整Box Score下載完成！")
        logger.info(f"總共下載了 {total_downloaded} 場比賽的Box Score")
        
        # 打印最終狀態
        logger.info("\n📊 最終Box Score狀態:")
        print_current_status()
        
        # 檢查總數
        total_boxscores = BoxScore.query.count()
        logger.info(f"\n💾 數據庫中總Box Score數量: {total_boxscores:,}")

if __name__ == '__main__':
    main()
