#!/usr/bin/env python3
"""
更新今日比賽數據的腳本
"""

from app import create_app
from models.data_fetcher import MLBDataFetcher
from models.detailed_data_fetcher import DetailedDataFetcher
from datetime import date, timedelta
import logging
import sys

def update_date_data(target_date):
    """更新指定日期的數據"""
    app = create_app()
    with app.app_context():
        print(f'🔄 開始更新 {target_date} 的數據...')
        
        try:
            # 1. 更新比賽狀態和比分
            print('步驟1: 更新比賽狀態和比分...')
            game_fetcher = MLBDataFetcher()
            game_fetcher.update_games_for_date(target_date)
            print('✅ 比賽狀態和比分更新完成')
            
            # 2. 下載Box Score
            print('步驟2: 下載Box Score...')
            detailed_fetcher = DetailedDataFetcher()
            boxscore_result = detailed_fetcher.fetch_completed_games_boxscores(target_date)
            
            if boxscore_result:
                print('✅ Box Score下載完成')
            else:
                print('⚠️ Box Score下載可能有問題')
            
            print(f'✅ {target_date} 數據更新完成！')
            return True
            
        except Exception as e:
            print(f'❌ 更新失敗: {e}')
            return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # 默認更新今天和昨天的數據
    today = date.today()
    yesterday = today - timedelta(days=1)
    
    print("🏀 MLB數據更新工具")
    print("=" * 50)
    
    # 更新昨天的數據
    print(f"\n📅 更新 {yesterday} 的數據...")
    update_date_data(yesterday)
    
    # 更新今天的數據
    print(f"\n📅 更新 {today} 的數據...")
    update_date_data(today)
    
    print("\n🎉 所有數據更新完成！")
