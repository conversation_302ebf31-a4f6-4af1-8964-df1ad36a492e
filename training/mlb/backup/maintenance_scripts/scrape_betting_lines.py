#!/usr/bin/env python3
"""
爬取博彩盤口數據
"""

import requests
import json
import time
from datetime import datetime, date
from bs4 import BeautifulSoup
import re
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, BettingOdds

class BettingLinesScraper:
    """博彩盤口爬蟲"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def scrape_sportsbookreview(self, game_date):
        """爬取 sportsbookreview.com 的盤口數據"""
        print(f"🕷️ 爬取 {game_date} 的盤口數據...")
        
        # 格式化日期
        if isinstance(game_date, str):
            date_obj = datetime.strptime(game_date, '%Y-%m-%d').date()
        else:
            date_obj = game_date
            
        date_str = date_obj.strftime('%Y-%m-%d')
        
        # sportsbookreview URL (大小分)
        url = f"https://www.sportsbookreview.com/betting-odds/mlb-baseball/totals/full-game/?date={date_str}"
        
        try:
            print(f"  請求 URL: {url}")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找比賽數據
            games_data = self.parse_sportsbookreview_data(soup, date_obj)
            
            return games_data
            
        except Exception as e:
            print(f"  ❌ 爬取失敗: {e}")
            return []
    
    def parse_sportsbookreview_data(self, soup, game_date):
        """解析 sportsbookreview 數據"""
        games_data = []
        
        try:
            # 查找比賽表格或數據容器
            # 這個需要根據實際網站結構調整
            game_rows = soup.find_all('tr', class_=re.compile(r'game|row'))
            
            for row in game_rows:
                try:
                    # 提取隊伍名稱
                    teams = row.find_all('span', class_=re.compile(r'team|name'))
                    if len(teams) >= 2:
                        away_team = self.normalize_team_name(teams[0].get_text().strip())
                        home_team = self.normalize_team_name(teams[1].get_text().strip())
                        
                        # 提取大小分盤口
                        total_cells = row.find_all('td', class_=re.compile(r'total|over|under'))
                        for cell in total_cells:
                            total_text = cell.get_text().strip()
                            total_match = re.search(r'(\d+\.?\d*)', total_text)
                            if total_match:
                                total_line = float(total_match.group(1))
                                
                                game_data = {
                                    'date': game_date,
                                    'away_team': away_team,
                                    'home_team': home_team,
                                    'total_point': total_line,
                                    'bookmaker': 'sportsbookreview',
                                    'source_url': 'sportsbookreview.com'
                                }
                                
                                games_data.append(game_data)
                                break
                                
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"  ❌ 解析數據失敗: {e}")
            
        return games_data
    
    def scrape_odds_api(self, game_date):
        """使用 Odds API 獲取盤口數據"""
        print(f"🔌 使用 Odds API 獲取 {game_date} 的盤口數據...")
        
        # 讀取 API key
        try:
            with open('models/odds-api.txt', 'r') as f:
                api_key = f.read().strip()
        except:
            print("  ❌ 無法讀取 Odds API key")
            return []
        
        # 格式化日期
        if isinstance(game_date, str):
            date_obj = datetime.strptime(game_date, '%Y-%m-%d').date()
        else:
            date_obj = game_date
            
        # Odds API URL
        url = "https://api.the-odds-api.com/v4/sports/baseball_mlb/odds"
        
        params = {
            'apiKey': api_key,
            'regions': 'us',
            'markets': 'totals',
            'oddsFormat': 'decimal',
            'dateFormat': 'iso'
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            games_data = self.parse_odds_api_data(data, date_obj)
            
            return games_data
            
        except Exception as e:
            print(f"  ❌ Odds API 請求失敗: {e}")
            return []
    
    def parse_odds_api_data(self, data, target_date):
        """解析 Odds API 數據"""
        games_data = []
        
        for game in data:
            try:
                # 檢查比賽日期
                game_time = datetime.fromisoformat(game['commence_time'].replace('Z', '+00:00'))
                game_date = game_time.date()
                
                if game_date != target_date:
                    continue
                
                # 提取隊伍名稱
                teams = game['teams']
                if len(teams) != 2:
                    continue
                    
                away_team = self.normalize_team_name(teams[0])
                home_team = self.normalize_team_name(teams[1])
                
                # 提取大小分盤口
                for bookmaker in game.get('bookmakers', []):
                    for market in bookmaker.get('markets', []):
                        if market['key'] == 'totals':
                            for outcome in market['outcomes']:
                                if outcome['name'] == 'Over':
                                    total_line = outcome['point']
                                    
                                    game_data = {
                                        'date': game_date,
                                        'away_team': away_team,
                                        'home_team': home_team,
                                        'total_point': total_line,
                                        'over_price': outcome['price'],
                                        'bookmaker': bookmaker['title'],
                                        'source_url': 'the-odds-api.com'
                                    }
                                    
                                    games_data.append(game_data)
                                    break
                            break
                    break
                    
            except Exception as e:
                continue
                
        return games_data
    
    def normalize_team_name(self, team_name):
        """標準化隊伍名稱"""
        # MLB 隊伍名稱對應表
        team_mapping = {
            'Colorado Rockies': 'COL', 'Rockies': 'COL',
            'Cincinnati Reds': 'CIN', 'Reds': 'CIN',
            'Los Angeles Dodgers': 'LAD', 'Dodgers': 'LAD',
            'San Francisco Giants': 'SF', 'Giants': 'SF',
            'Toronto Blue Jays': 'TOR', 'Blue Jays': 'TOR',
            'Oakland Athletics': 'ATH', 'Athletics': 'ATH',
            'Philadelphia Phillies': 'PHI', 'Phillies': 'PHI',
            'San Diego Padres': 'SD', 'Padres': 'SD',
            'Arizona Diamondbacks': 'AZ', 'Diamondbacks': 'AZ',
            'Los Angeles Angels': 'LAA', 'Angels': 'LAA',
            'Atlanta Braves': 'ATL', 'Braves': 'ATL',
            'St. Louis Cardinals': 'STL', 'Cardinals': 'STL',
            'Washington Nationals': 'WSH', 'Nationals': 'WSH',
            'Milwaukee Brewers': 'MIL', 'Brewers': 'MIL'
        }
        
        # 直接匹配
        if team_name in team_mapping:
            return team_mapping[team_name]
            
        # 部分匹配
        for full_name, abbr in team_mapping.items():
            if team_name.lower() in full_name.lower() or full_name.lower() in team_name.lower():
                return abbr
                
        # 如果已經是縮寫，直接返回
        if len(team_name) <= 3 and team_name.isupper():
            return team_name
            
        return team_name
    
    def save_to_database(self, games_data):
        """保存盤口數據到數據庫"""
        app = create_app()
        
        with app.app_context():
            saved_count = 0
            
            for game_data in games_data:
                try:
                    # 查找對應的比賽
                    game = Game.query.filter(
                        Game.date == game_data['date'],
                        Game.away_team == game_data['away_team'],
                        Game.home_team == game_data['home_team']
                    ).first()
                    
                    if not game:
                        print(f"  ⚠️ 未找到比賽: {game_data['away_team']} @ {game_data['home_team']}")
                        continue
                    
                    # 檢查是否已存在盤口數據
                    existing_odds = BettingOdds.query.filter_by(
                        game_id=game.game_id,
                        bookmaker=game_data['bookmaker']
                    ).first()
                    
                    if existing_odds:
                        # 更新現有數據
                        existing_odds.total_point = game_data['total_point']
                        existing_odds.over_price = game_data.get('over_price')
                        existing_odds.under_price = game_data.get('under_price')
                        existing_odds.updated_at = datetime.now()
                    else:
                        # 創建新記錄
                        new_odds = BettingOdds(
                            game_id=game.game_id,
                            bookmaker=game_data['bookmaker'],
                            total_point=game_data['total_point'],
                            over_price=game_data.get('over_price'),
                            under_price=game_data.get('under_price'),
                            created_at=datetime.now(),
                            updated_at=datetime.now()
                        )
                        db.session.add(new_odds)
                    
                    saved_count += 1
                    
                except Exception as e:
                    print(f"  ❌ 保存失敗: {e}")
                    continue
            
            try:
                db.session.commit()
                print(f"  ✅ 成功保存 {saved_count} 條盤口數據")
            except Exception as e:
                db.session.rollback()
                print(f"  ❌ 數據庫提交失敗: {e}")

def get_betting_line_for_game(away_team, home_team, game_date):
    """獲取特定比賽的盤口"""
    scraper = BettingLinesScraper()
    
    print(f"🔍 查找 {game_date} {away_team} @ {home_team} 的盤口...")
    
    # 嘗試多個數據源
    all_games_data = []
    
    # 1. 嘗試 Odds API
    odds_api_data = scraper.scrape_odds_api(game_date)
    all_games_data.extend(odds_api_data)
    
    # 2. 嘗試 sportsbookreview (如果 API 失敗)
    if not odds_api_data:
        sbr_data = scraper.scrape_sportsbookreview(game_date)
        all_games_data.extend(sbr_data)
    
    # 查找目標比賽
    for game_data in all_games_data:
        if (game_data['away_team'] == away_team and 
            game_data['home_team'] == home_team):
            print(f"  ✅ 找到盤口: {game_data['total_point']}")
            return game_data['total_point']
    
    print(f"  ❌ 未找到該比賽的盤口數據")
    return None

if __name__ == "__main__":
    # 測試爬取 COL @ CIN 的盤口
    game_date = "2025-07-12"  # 或實際比賽日期
    betting_line = get_betting_line_for_game("COL", "CIN", game_date)
    
    if betting_line:
        print(f"\n🎯 COL @ CIN 的大小分盤口: {betting_line}")
        
        # 分析預測準確性
        predicted_total = 9.0
        actual_total = 11.0
        
        print(f"\n📊 大小分分析:")
        print(f"  預測總分: {predicted_total}")
        print(f"  實際總分: {actual_total}")
        print(f"  盤口: {betting_line}")
        
        predicted_over = predicted_total > betting_line
        actual_over = actual_total > betting_line
        
        print(f"  預測: {'大分' if predicted_over else '小分'}")
        print(f"  實際: {'大分' if actual_over else '小分'}")
        print(f"  結果: {'✅ 正確' if predicted_over == actual_over else '❌ 錯誤'}")
    else:
        print(f"\n❌ 無法獲取盤口數據，請檢查網絡連接或 API 配置")
