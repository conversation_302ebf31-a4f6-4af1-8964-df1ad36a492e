#!/usr/bin/env python3
"""
探索MLB傷兵報告數據源
"""

import requests
import json
from datetime import date, timedelta

def explore_mlb_injury_sources():
    """探索MLB官方和第三方傷兵數據源"""
    print("=" * 80)
    print("🏥 探索MLB傷兵報告數據源")
    print("=" * 80)
    
    base_url = "https://statsapi.mlb.com/api/v1"
    
    # 測試MLB官方API的傷兵相關端點
    injury_endpoints = [
        {
            'name': '球員狀態API',
            'url': f"{base_url}/people",
            'params': {'hydrate': 'currentTeam,stats,transactions'},
            'description': '檢查球員基本信息中是否包含傷兵狀態'
        },
        {
            'name': '球隊名單API',
            'url': f"{base_url}/teams/111/roster",
            'params': {'rosterType': 'active'},
            'description': '檢查球隊名單中的球員狀態信息'
        },
        {
            'name': '球隊名單API - 傷兵名單',
            'url': f"{base_url}/teams/111/roster",
            'params': {'rosterType': 'depthChart'},
            'description': '檢查是否有傷兵名單相關信息'
        },
        {
            'name': '交易/異動API',
            'url': f"{base_url}/transactions",
            'params': {'startDate': (date.today() - timedelta(days=7)).strftime('%Y-%m-%d'),
                      'endDate': date.today().strftime('%Y-%m-%d')},
            'description': '檢查球員異動記錄中的傷兵相關信息'
        }
    ]
    
    # 測試知名球員ID
    test_player_ids = [
        592789,  # Jacob deGrom
        545361,  # Mike Trout
        660271,  # Shane Bieber
        608369,  # Ronald Acuña Jr.
        596059   # Mookie Betts
    ]
    
    for endpoint in injury_endpoints:
        print(f"\n📡 測試: {endpoint['name']}")
        print(f"描述: {endpoint['description']}")
        print("-" * 50)
        
        try:
            response = requests.get(endpoint['url'], params=endpoint['params'], timeout=30)
            print(f"狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"響應大小: {len(response.text)} 字符")
                
                # 分析響應結構，尋找傷兵相關字段
                injury_keywords = ['injury', 'injured', 'disabled', 'il', 'status', 'roster', 'transaction']
                found_keywords = []
                
                def search_keywords(obj, path=""):
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            current_path = f"{path}.{key}" if path else key
                            if any(keyword in key.lower() for keyword in injury_keywords):
                                found_keywords.append(f"{current_path}: {value}")
                            search_keywords(value, current_path)
                    elif isinstance(obj, list) and obj:
                        for i, item in enumerate(obj[:3]):  # 只檢查前3個項目
                            search_keywords(item, f"{path}[{i}]")
                
                search_keywords(data)
                
                if found_keywords:
                    print("🔍 發現相關字段:")
                    for keyword in found_keywords[:10]:  # 只顯示前10個
                        print(f"  {keyword}")
                else:
                    print("❌ 未發現傷兵相關字段")
                    
                # 特別檢查roster數據結構
                if 'roster' in data:
                    roster = data['roster']
                    print(f"\n📋 名單信息: {len(roster)} 名球員")
                    if roster:
                        sample_player = roster[0]
                        print("樣本球員字段:")
                        for key in sample_player.keys():
                            print(f"  - {key}")
                
                # 特別檢查transactions數據結構
                if 'transactions' in data:
                    transactions = data['transactions']
                    print(f"\n📝 異動記錄: {len(transactions)} 筆")
                    if transactions:
                        sample_transaction = transactions[0]
                        print("樣本異動字段:")
                        for key in sample_transaction.keys():
                            print(f"  - {key}")
                        
                        # 檢查異動類型
                        if 'typeCode' in sample_transaction:
                            print(f"異動類型: {sample_transaction['typeCode']}")
                        if 'description' in sample_transaction:
                            print(f"描述: {sample_transaction['description']}")
                            
            else:
                print(f"❌ 請求失敗: {response.status_code}")
                if response.text:
                    print(f"錯誤信息: {response.text[:200]}")
                    
        except Exception as e:
            print(f"❌ 錯誤: {e}")
    
    # 測試特定球員的詳細信息
    print(f"\n📡 測試: 特定球員詳細信息")
    print("-" * 50)
    
    for player_id in test_player_ids[:3]:  # 只測試前3個球員
        try:
            player_url = f"{base_url}/people/{player_id}"
            params = {
                'hydrate': 'currentTeam,stats,transactions,awards,education,draft'
            }
            
            response = requests.get(player_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'people' in data and data['people']:
                    player = data['people'][0]
                    name = player.get('fullName', 'Unknown')
                    print(f"\n球員: {name} (ID: {player_id})")
                    
                    # 檢查球員狀態相關字段
                    status_fields = ['status', 'rosterStatus', 'currentTeam', 'active']
                    for field in status_fields:
                        if field in player:
                            print(f"  {field}: {player[field]}")
                    
                    # 檢查是否有交易/異動記錄
                    if 'transactions' in player:
                        transactions = player['transactions']
                        print(f"  異動記錄: {len(transactions)} 筆")
                        
                        # 檢查最近的異動
                        for transaction in transactions[:3]:
                            if 'date' in transaction and 'description' in transaction:
                                print(f"    {transaction['date']}: {transaction['description']}")
            else:
                print(f"❌ 球員 {player_id} 請求失敗: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 球員 {player_id} 錯誤: {e}")

def explore_third_party_sources():
    """探索第三方傷兵數據源"""
    print(f"\n📡 探索第三方傷兵數據源")
    print("-" * 50)
    
    # 一些可能的第三方數據源（僅測試可訪問性）
    third_party_sources = [
        {
            'name': 'ESPN MLB',
            'url': 'https://www.espn.com/mlb/injuries',
            'description': 'ESPN MLB傷兵報告頁面'
        },
        {
            'name': 'MLB.com Injuries',
            'url': 'https://www.mlb.com/news/topic/injuries',
            'description': 'MLB官網傷兵新聞'
        },
        {
            'name': 'Rotoworld',
            'url': 'https://www.nbcsports.com/fantasy/baseball/news',
            'description': 'Rotoworld棒球新聞（包含傷兵信息）'
        }
    ]
    
    for source in third_party_sources:
        try:
            print(f"\n測試: {source['name']}")
            print(f"URL: {source['url']}")
            
            # 只測試連接性，不解析內容
            response = requests.head(source['url'], timeout=10)
            print(f"狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 可訪問")
            else:
                print("❌ 無法訪問")
                
        except Exception as e:
            print(f"❌ 錯誤: {e}")

def analyze_roster_status_codes():
    """分析MLB API中的球員狀態代碼"""
    print(f"\n📊 分析球員狀態代碼")
    print("-" * 50)
    
    base_url = "https://statsapi.mlb.com/api/v1"
    
    # 獲取多個球隊的名單來分析狀態代碼
    team_ids = [111, 112, 113, 114, 115]  # 前5個球隊
    
    all_status_codes = set()
    roster_types = ['active', 'fullRoster', 'depthChart', '40Man']
    
    for team_id in team_ids:
        for roster_type in roster_types:
            try:
                url = f"{base_url}/teams/{team_id}/roster"
                params = {'rosterType': roster_type}
                
                response = requests.get(url, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if 'roster' in data:
                        for player in data['roster']:
                            # 收集所有狀態相關字段
                            if 'status' in player:
                                status = player['status']
                                if isinstance(status, dict):
                                    if 'code' in status:
                                        all_status_codes.add(f"status.code: {status['code']}")
                                    if 'description' in status:
                                        all_status_codes.add(f"status.description: {status['description']}")
                                else:
                                    all_status_codes.add(f"status: {status}")
                            
                            # 檢查其他可能的狀態字段
                            for field in ['rosterStatus', 'injuryStatus', 'active']:
                                if field in player:
                                    all_status_codes.add(f"{field}: {player[field]}")
                    
                    print(f"✅ 球隊 {team_id} - {roster_type}: {len(data.get('roster', []))} 球員")
                else:
                    print(f"❌ 球隊 {team_id} - {roster_type}: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 球隊 {team_id} - {roster_type} 錯誤: {e}")
    
    print(f"\n🔍 發現的狀態代碼:")
    for status_code in sorted(all_status_codes):
        print(f"  {status_code}")

def main():
    """主函數"""
    explore_mlb_injury_sources()
    explore_third_party_sources()
    analyze_roster_status_codes()

if __name__ == "__main__":
    main()
