#!/usr/bin/env python3
"""
清除指定日期的盤口數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, BettingOdds, Game

def clear_odds_data():
    """清除2025-07-11的盤口數據"""
    app = create_app()
    
    with app.app_context():
        # 刪除2025-07-11的盤口數據
        test_date = date(2025, 7, 11)
        games = Game.query.filter_by(date=test_date).all()
        game_ids = [g.game_id for g in games]
        
        print(f"找到 {len(games)} 場比賽")
        print(f"遊戲ID: {game_ids[:5]}...")  # 只顯示前5個
        
        deleted = BettingOdds.query.filter(BettingOdds.game_id.in_(game_ids)).delete(synchronize_session=False)
        db.session.commit()
        
        print(f'已刪除 {deleted} 筆盤口記錄')

if __name__ == "__main__":
    clear_odds_data()
