#!/usr/bin/env python3
"""
創建賠率數據表
"""

import sys
import os

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, BettingOdds

def create_odds_table():
    """創建賠率數據表"""
    print("🗄️  創建賠率數據表")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 創建表
            db.create_all()
            
            print("✅ 賠率數據表創建成功")
            
            # 檢查表是否存在
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            if 'betting_odds' in tables:
                print("✅ betting_odds 表已存在")
                
                # 顯示表結構
                columns = inspector.get_columns('betting_odds')
                print(f"\n📋 表結構 (共 {len(columns)} 個欄位):")
                for col in columns:
                    print(f"   - {col['name']}: {col['type']}")
            else:
                print("❌ betting_odds 表創建失敗")
            
        except Exception as e:
            print(f"❌ 創建表失敗: {e}")

if __name__ == "__main__":
    create_odds_table()
