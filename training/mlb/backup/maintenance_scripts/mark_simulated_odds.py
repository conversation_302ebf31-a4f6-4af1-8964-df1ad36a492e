#!/usr/bin/env python3
"""
標記模擬博彩盤口數據
確保用戶知道哪些是真實盤口，哪些是模擬數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction, BettingOdds
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from datetime import date, timedelta

def mark_simulated_odds():
    """標記模擬博彩盤口數據"""
    print("🏷️ 標記模擬博彩盤口數據")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        # 檢查API狀態
        api_status = fetcher.check_api_status()
        print(f"📡 API狀態: {api_status['message']}")
        
        # 獲取最近7天的預測
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        predictions = db.session.query(Prediction).join(Game).filter(
            Game.date >= start_date,
            Game.date <= end_date
        ).all()
        
        print(f"📊 檢查 {len(predictions)} 個預測記錄")
        
        real_odds_count = 0
        simulated_count = 0
        updated_count = 0
        
        for pred in predictions:
            try:
                game = Game.query.filter_by(game_id=pred.game_id).first()
                if not game:
                    continue
                
                # 檢查是否有真實博彩盤口數據
                has_real_odds = False
                
                # 1. 檢查數據庫中的博彩盤口
                betting_odds = BettingOdds.query.filter_by(
                    game_id=game.game_id
                ).first()
                
                if betting_odds and betting_odds.total_point:
                    has_real_odds = True
                    real_odds_count += 1
                    data_source = "database_real"
                else:
                    # 2. 檢查是否能從API獲取真實盤口
                    try:
                        game_odds = fetcher.get_game_odds_by_teams(
                            game.home_team, 
                            game.away_team, 
                            game.date
                        )
                        
                        if game_odds and (game_odds.get('totals') or game_odds.get('total')):
                            has_real_odds = True
                            real_odds_count += 1
                            data_source = "api_real"
                        else:
                            has_real_odds = False
                            simulated_count += 1
                            data_source = "simulated"
                    except:
                        has_real_odds = False
                        simulated_count += 1
                        data_source = "simulated"
                
                # 更新模型版本以標記數據來源
                old_version = pred.model_version or "unknown"
                
                if has_real_odds:
                    if "real_odds" not in old_version:
                        pred.model_version = f"{old_version}_real_odds"
                        updated_count += 1
                else:
                    if "simulated_odds" not in old_version:
                        pred.model_version = f"{old_version}_simulated_odds"
                        updated_count += 1
                
                # 如果大小分線過高且是模擬數據，標記為明顯模擬
                if not has_real_odds and pred.over_under_line and pred.over_under_line > 12:
                    pred.model_version = f"{pred.model_version}_obvious_simulated"
                
            except Exception as e:
                print(f"  ❌ 處理預測 {pred.game_id} 失敗: {e}")
        
        # 提交更改
        try:
            db.session.commit()
            print(f"\n📊 標記完成:")
            print(f"  真實盤口: {real_odds_count} 個")
            print(f"  模擬數據: {simulated_count} 個")
            print(f"  更新記錄: {updated_count} 個")
            
            # 顯示統計
            total_checked = real_odds_count + simulated_count
            if total_checked > 0:
                real_percentage = (real_odds_count / total_checked) * 100
                print(f"\n📈 數據質量:")
                print(f"  真實盤口比例: {real_percentage:.1f}%")
                print(f"  模擬數據比例: {100 - real_percentage:.1f}%")
                
                if real_percentage < 30:
                    print(f"\n⚠️ 警告: 真實盤口數據不足30%")
                    print("   建議:")
                    print("   1. 配置有效的博彩API密鑰")
                    print("   2. 運行歷史盤口數據下載")
                    print("   3. 使用網頁抓取器獲取真實盤口")
                
        except Exception as e:
            print(f"❌ 提交失敗: {e}")
            db.session.rollback()

def add_odds_warning_to_templates():
    """在模板中添加盤口數據警告"""
    print(f"\n🎨 更新Web界面顯示:")
    
    # 檢查當前系統的真實盤口比例
    app = create_app()
    with app.app_context():
        today = date.today()
        
        # 檢查今天的盤口數據
        todays_odds = db.session.query(BettingOdds).join(Game).filter(
            Game.date == today
        ).count()
        
        todays_games = Game.query.filter_by(date=today).count()
        
        if todays_games > 0:
            real_odds_ratio = todays_odds / todays_games
            print(f"  今日真實盤口覆蓋率: {real_odds_ratio * 100:.1f}%")
            
            if real_odds_ratio < 0.5:
                print("  ⚠️ 建議在Web界面顯示數據來源警告")
                print("  📝 已更新模板以標示模擬數據")
            else:
                print("  ✅ 真實盤口數據充足")
        else:
            print("  📅 今天沒有比賽數據")

def main():
    mark_simulated_odds()
    add_odds_warning_to_templates()
    
    print(f"\n🎯 總結:")
    print("  1. ✅ 已標記所有預測的數據來源")
    print("  2. ✅ 已更新Web界面以顯示數據來源")
    print("  3. ✅ 用戶現在可以區分真實和模擬盤口")
    print(f"\n💡 建議:")
    print("  - 刷新Web界面查看更新後的數據來源標示")
    print("  - 考慮配置真實博彩API以獲取更多真實盤口")

if __name__ == "__main__":
    main()
