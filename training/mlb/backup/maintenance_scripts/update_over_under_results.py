#!/usr/bin/env python3
"""
更新大小分預測結果
"""

import sys
from datetime import date, datetime, timedelta
from pathlib import Path

# 添加項目根目錄到Python路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app
from models.database import db, Prediction, Game

def update_over_under_results(target_date: date):
    """更新指定日期的大小分預測結果"""
    print(f"🔄 更新 {target_date} 的大小分預測結果...")
    
    app = create_app()
    with app.app_context():
        # 獲取該日期的預測記錄
        predictions = Prediction.query.join(Game).filter(
            Game.date >= target_date,
            Game.date < target_date + timedelta(days=1)
        ).all()
        
        if not predictions:
            print(f"❌ 沒有找到 {target_date} 的預測記錄")
            return
        
        print(f"📊 找到 {len(predictions)} 個預測記錄")
        updated_count = 0
        
        for prediction in predictions:
            # 獲取比賽信息
            game = Game.query.filter_by(game_id=prediction.game_id).first()
            if not game:
                continue
            
            print(f"處理比賽: {game.away_team}@{game.home_team}")
            
            # 檢查是否已有大小分數據
            if prediction.over_under_line and prediction.over_under_line > 0:
                print(f"  已有大小分盤口: {prediction.over_under_line}")
                
                # 如果比賽已結束，更新大小分結果
                if game.game_status in ['Final', 'completed'] and game.home_score is not None and game.away_score is not None:
                    actual_total = game.home_score + game.away_score
                    predicted_total = prediction.predicted_home_score + prediction.predicted_away_score
                    
                    # 更新實際總分
                    prediction.actual_total_runs = actual_total
                    
                    # 計算大小分預測是否正確
                    predicted_over = predicted_total > prediction.over_under_line
                    actual_over = actual_total > prediction.over_under_line
                    prediction.over_under_correct = predicted_over == actual_over
                    
                    # 計算總分差異
                    prediction.total_runs_difference = abs(predicted_total - actual_total)
                    
                    prediction.updated_at = datetime.now()
                    updated_count += 1
                    
                    print(f"  更新結果: 預測總分={predicted_total:.1f}, 實際總分={actual_total}, 盤口={prediction.over_under_line}, 正確={prediction.over_under_correct}")
                else:
                    print(f"  比賽未結束，狀態: {game.game_status}")
            else:
                print(f"  缺少大小分盤口數據")
                
                # 嘗試設置模擬盤口
                if prediction.predicted_home_score and prediction.predicted_away_score:
                    predicted_total = prediction.predicted_home_score + prediction.predicted_away_score
                    # 使用預測總分作為模擬盤口（通常盤口會略低於預測）
                    simulated_line = round(predicted_total - 0.5, 1)
                    
                    prediction.predicted_total_runs = predicted_total
                    prediction.over_under_line = simulated_line
                    prediction.over_probability = 0.55  # 略偏向大分
                    prediction.under_probability = 0.45
                    prediction.over_under_confidence = 0.6  # 中等信心度
                    
                    print(f"  設置模擬盤口: 預測總分={predicted_total:.1f}, 盤口={simulated_line}")
                    
                    # 如果比賽已結束，計算結果
                    if game.game_status in ['Final', 'completed'] and game.home_score is not None and game.away_score is not None:
                        actual_total = game.home_score + game.away_score
                        prediction.actual_total_runs = actual_total
                        
                        predicted_over = predicted_total > simulated_line
                        actual_over = actual_total > simulated_line
                        prediction.over_under_correct = predicted_over == actual_over
                        prediction.total_runs_difference = abs(predicted_total - actual_total)
                        
                        print(f"  計算結果: 實際總分={actual_total}, 正確={prediction.over_under_correct}")
                    
                    prediction.updated_at = datetime.now()
                    updated_count += 1
        
        # 提交更改
        db.session.commit()
        print(f"✅ 成功更新 {updated_count} 個預測記錄")

if __name__ == '__main__':
    target_date = date(2025, 7, 5)
    update_over_under_results(target_date)
