#!/usr/bin/env python3
"""
MLB預測結果驗證更新器
自動收集最新比賽結果並更新預測準確率統計
"""

import sqlite3
import requests
import json
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional

class PredictionValidationUpdater:
    """預測結果驗證更新器"""
    
    def __init__(self, db_path='instance/mlb_data.db'):
        self.db_path = db_path
        self.validation_stats = {}
        
    def get_pending_predictions(self) -> List[Dict]:
        """獲取待驗證的預測記錄"""
        print("🔍 搜尋待驗證的預測記錄...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 搜尋有預測但缺少實際結果的記錄
            cursor.execute('''
                SELECT p.id, p.game_id, p.predicted_home_score, p.predicted_away_score,
                       p.home_win_probability, p.confidence, p.model_version, p.created_at,
                       g.date, g.home_team, g.away_team, g.home_score, g.away_score, g.status
                FROM predictions p
                JOIN games g ON p.game_id = g.game_id
                WHERE (p.actual_home_score IS NULL OR p.actual_away_score IS NULL)
                AND g.home_score IS NOT NULL
                AND g.away_score IS NOT NULL
                AND g.status = 'Final'
                ORDER BY g.date DESC
                LIMIT 50
            ''')
            
            pending = cursor.fetchall()
            conn.close()
            
            predictions = []
            for row in pending:
                pred = {
                    'pred_id': row[0],
                    'game_id': row[1],
                    'predicted_home': row[2],
                    'predicted_away': row[3],
                    'home_win_prob': row[4],
                    'confidence': row[5],
                    'model_version': row[6],
                    'created_at': row[7],
                    'game_date': row[8],
                    'home_team': row[9],
                    'away_team': row[10],
                    'actual_home': row[11],
                    'actual_away': row[12],
                    'status': row[13]
                }
                predictions.append(pred)
            
            print(f"找到 {len(predictions)} 筆待驗證預測")
            return predictions
            
        except Exception as e:
            print(f"❌ 搜尋錯誤: {e}")
            return []
    
    def validate_prediction(self, prediction: Dict) -> Dict:
        """驗證單個預測的準確性"""
        pred_home = float(prediction['predicted_home'])
        pred_away = float(prediction['predicted_away'])
        actual_home = int(prediction['actual_home'])
        actual_away = int(prediction['actual_away'])
        
        # 判斷勝負預測是否正確
        predicted_winner = 'home' if pred_home > pred_away else 'away'
        actual_winner = 'home' if actual_home > actual_away else 'away'
        is_correct = predicted_winner == actual_winner
        
        # 計算得分差異
        home_score_diff = abs(pred_home - actual_home)
        away_score_diff = abs(pred_away - actual_away)
        total_score_diff = abs((pred_home + pred_away) - (actual_home + actual_away))
        
        # 計算Over/Under預測
        predicted_total = pred_home + pred_away
        actual_total = actual_home + actual_away
        ou_line = 9.0  # 標準Over/Under線
        predicted_ou = 'over' if predicted_total > ou_line else 'under'
        actual_ou = 'over' if actual_total > ou_line else 'under'
        ou_correct = predicted_ou == actual_ou
        
        return {
            'is_correct': is_correct,
            'home_score_diff': home_score_diff,
            'away_score_diff': away_score_diff,
            'total_score_diff': total_score_diff,
            'actual_home_score': actual_home,
            'actual_away_score': actual_away,
            'actual_total_runs': actual_total,
            'over_under_correct': ou_correct,
            'total_runs_difference': total_score_diff
        }
    
    def update_prediction_record(self, pred_id: int, validation: Dict) -> bool:
        """更新預測記錄的驗證結果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE predictions 
                SET is_correct = ?,
                    actual_home_score = ?,
                    actual_away_score = ?,
                    score_difference = ?,
                    actual_total_runs = ?,
                    over_under_correct = ?,
                    total_runs_difference = ?,
                    updated_at = ?
                WHERE id = ?
            ''', (
                validation['is_correct'],
                validation['actual_home_score'],
                validation['actual_away_score'],
                validation['total_score_diff'],
                validation['actual_total_runs'],
                validation['over_under_correct'],
                validation['total_runs_difference'],
                datetime.now().isoformat(),
                pred_id
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 更新錯誤 (ID: {pred_id}): {e}")
            return False
    
    def process_all_validations(self) -> Dict:
        """處理所有待驗證的預測"""
        print("⚙️  開始處理預測驗證...")
        
        pending_predictions = self.get_pending_predictions()
        
        if not pending_predictions:
            print("✅ 沒有待驗證的預測記錄")
            return {'updated': 0, 'errors': 0}
        
        updated_count = 0
        error_count = 0
        
        for prediction in pending_predictions:
            try:
                # 驗證預測
                validation = self.validate_prediction(prediction)
                
                # 更新記錄
                success = self.update_prediction_record(prediction['pred_id'], validation)
                
                if success:
                    updated_count += 1
                    result = "✅" if validation['is_correct'] else "❌"
                    print(f"  {result} {prediction['home_team']} vs {prediction['away_team']} "
                          f"({prediction['actual_home']}-{prediction['actual_away']}) "
                          f"預測: {prediction['predicted_home']:.1f}-{prediction['predicted_away']:.1f}")
                else:
                    error_count += 1
                    
            except Exception as e:
                print(f"❌ 處理錯誤: {e}")
                error_count += 1
        
        print(f"\n📊 驗證完成: {updated_count} 筆更新, {error_count} 筆錯誤")
        return {'updated': updated_count, 'errors': error_count}
    
    def generate_accuracy_report(self) -> Dict:
        """生成準確率報告"""
        print("\\n📈 生成準確率報告...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 總體準確率
            cursor.execute('SELECT COUNT(*) FROM predictions WHERE is_correct IS NOT NULL')
            total_verified = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM predictions WHERE is_correct = 1')
            total_correct = cursor.fetchone()[0]
            
            overall_accuracy = (total_correct / total_verified * 100) if total_verified > 0 else 0
            
            # 按模型版本分析
            cursor.execute('''
                SELECT model_version, 
                       COUNT(*) as total,
                       SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct
                FROM predictions 
                WHERE is_correct IS NOT NULL 
                GROUP BY model_version
                ORDER BY total DESC
            ''')
            model_stats = cursor.fetchall()
            
            # Over/Under準確率
            cursor.execute('SELECT COUNT(*) FROM predictions WHERE over_under_correct IS NOT NULL')
            ou_total = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM predictions WHERE over_under_correct = 1')
            ou_correct = cursor.fetchone()[0]
            
            ou_accuracy = (ou_correct / ou_total * 100) if ou_total > 0 else 0
            
            # 平均得分誤差
            cursor.execute('''
                SELECT 
                    AVG(ABS(predicted_home_score - actual_home_score)) as home_mae,
                    AVG(ABS(predicted_away_score - actual_away_score)) as away_mae,
                    AVG(ABS(total_runs_difference)) as total_mae
                FROM predictions 
                WHERE actual_home_score IS NOT NULL
            ''')
            mae_stats = cursor.fetchone()
            
            conn.close()
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'overall': {
                    'accuracy': overall_accuracy,
                    'correct': total_correct,
                    'total': total_verified
                },
                'over_under': {
                    'accuracy': ou_accuracy,
                    'correct': ou_correct,
                    'total': ou_total
                },
                'mae': {
                    'home': mae_stats[0] if mae_stats[0] else 0,
                    'away': mae_stats[1] if mae_stats[1] else 0,
                    'total': mae_stats[2] if mae_stats[2] else 0
                },
                'by_model': {}
            }
            
            for version, total, correct in model_stats:
                accuracy = (correct / total * 100) if total > 0 else 0
                report['by_model'][version] = {
                    'accuracy': accuracy,
                    'correct': correct,
                    'total': total
                }
            
            # 顯示報告
            print(f"總體準確率: {overall_accuracy:.1f}% ({total_correct}/{total_verified})")
            print(f"Over/Under準確率: {ou_accuracy:.1f}% ({ou_correct}/{ou_total})")
            print(f"平均得分誤差: 主隊 {mae_stats[0]:.2f}, 客隊 {mae_stats[1]:.2f}, 總分 {mae_stats[2]:.2f}")
            
            print("\\n各模型準確率:")
            for version, stats in report['by_model'].items():
                print(f"  {version}: {stats['accuracy']:.1f}% ({stats['correct']}/{stats['total']})")
            
            return report
            
        except Exception as e:
            print(f"❌ 報告生成錯誤: {e}")
            return {}
    
    def save_validation_report(self, report: Dict):
        """保存驗證報告"""
        filename = f"prediction_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"\\n📄 驗證報告已保存: {filename}")
            
        except Exception as e:
            print(f"❌ 保存報告錯誤: {e}")

def main():
    """主執行函數"""
    print("🚀 MLB預測結果驗證更新器")
    print("=" * 60)
    
    updater = PredictionValidationUpdater()
    
    # 1. 處理待驗證預測
    validation_results = updater.process_all_validations()
    
    # 2. 生成準確率報告
    accuracy_report = updater.generate_accuracy_report()
    
    # 3. 保存報告
    if accuracy_report:
        combined_report = {
            'validation_results': validation_results,
            'accuracy_report': accuracy_report
        }
        updater.save_validation_report(combined_report)
    
    print(f"\\n✅ 預測驗證更新完成!")
    
    if validation_results['updated'] > 0:
        print(f"🎯 已更新 {validation_results['updated']} 筆預測驗證")
        print(f"📊 當前總體準確率: {accuracy_report.get('overall', {}).get('accuracy', 0):.1f}%")
    else:
        print("ℹ️  無新的預測需要驗證")

if __name__ == "__main__":
    main()