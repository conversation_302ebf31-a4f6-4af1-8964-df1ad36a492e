#!/usr/bin/env python3
"""
更新預測系統使用真實博彩盤口
"""

import sys
from datetime import date, datetime, timedelta
from pathlib import Path

# 添加項目根目錄到Python路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app
from models.database import db, Prediction, Game, BettingOdds

def update_predictions_with_real_odds(target_date: date):
    """更新指定日期的預測，使用真實博彩盤口"""
    print(f"🔄 更新 {target_date} 的預測，使用真實博彩盤口...")
    
    app = create_app()
    with app.app_context():
        # 獲取該日期的預測記錄
        predictions = Prediction.query.join(Game).filter(
            Game.date >= target_date,
            Game.date < target_date + timedelta(days=1)
        ).all()
        
        if not predictions:
            print(f"❌ 沒有找到 {target_date} 的預測記錄")
            return
        
        print(f"📊 找到 {len(predictions)} 個預測記錄")
        updated_count = 0
        real_odds_count = 0
        
        for prediction in predictions:
            # 獲取比賽信息
            game = Game.query.filter_by(game_id=prediction.game_id).first()
            if not game:
                continue
            
            print(f"處理比賽: {game.away_team}@{game.home_team}")
            
            # 查找真實博彩盤口
            real_odds = BettingOdds.query.filter_by(
                game_id=prediction.game_id,
                market_type='totals'
            ).first()
            
            if real_odds and real_odds.total_point:
                # 使用真實盤口
                real_total_line = real_odds.total_point
                prediction.over_under_line = real_total_line
                
                # 重新計算預測總分（如果需要）
                if not prediction.predicted_total_runs:
                    prediction.predicted_total_runs = prediction.predicted_home_score + prediction.predicted_away_score
                
                # 重新計算大小分概率
                predicted_total = prediction.predicted_total_runs
                if predicted_total > real_total_line:
                    prediction.over_probability = 0.6  # 偏向大分
                    prediction.under_probability = 0.4
                else:
                    prediction.over_probability = 0.4  # 偏向小分
                    prediction.under_probability = 0.6
                
                prediction.over_under_confidence = 0.7  # 使用真實盤口的信心度較高
                
                print(f"  ✅ 使用真實盤口: {real_total_line} (來源: {real_odds.bookmaker})")
                real_odds_count += 1
                
                # 如果比賽已結束，重新計算大小分結果
                if game.game_status in ['Final', 'completed'] and game.home_score is not None and game.away_score is not None:
                    actual_total = game.home_score + game.away_score
                    prediction.actual_total_runs = actual_total
                    
                    # 計算大小分預測是否正確
                    predicted_over = predicted_total > real_total_line
                    actual_over = actual_total > real_total_line
                    prediction.over_under_correct = predicted_over == actual_over
                    
                    # 計算總分差異
                    prediction.total_runs_difference = abs(predicted_total - actual_total)
                    
                    print(f"    結果: 預測總分={predicted_total:.1f}, 實際總分={actual_total}, 盤口={real_total_line}, 正確={prediction.over_under_correct}")
                
                prediction.updated_at = datetime.now()
                updated_count += 1
                
            else:
                print(f"  ⚠️  沒有找到真實盤口，保持現有設置")
        
        # 提交更改
        db.session.commit()
        print(f"✅ 成功更新 {updated_count} 個預測記錄")
        print(f"📈 其中 {real_odds_count} 個使用了真實博彩盤口")

def check_betting_odds_availability(target_date: date):
    """檢查指定日期的博彩盤口數據可用性"""
    print(f"🔍 檢查 {target_date} 的博彩盤口數據...")
    
    app = create_app()
    with app.app_context():
        # 獲取該日期的比賽
        games = Game.query.filter(
            Game.date >= target_date,
            Game.date < target_date + timedelta(days=1)
        ).all()
        
        if not games:
            print(f"❌ 沒有找到 {target_date} 的比賽記錄")
            return
        
        print(f"📊 找到 {len(games)} 場比賽")
        
        total_odds_count = 0
        for game in games:
            # 查找博彩盤口
            odds_records = BettingOdds.query.filter_by(game_id=game.game_id).all()
            
            if odds_records:
                totals_odds = [o for o in odds_records if o.market_type == 'totals']
                spreads_odds = [o for o in odds_records if o.market_type == 'spreads']
                
                print(f"  {game.away_team}@{game.home_team}:")
                if totals_odds:
                    for odds in totals_odds:
                        print(f"    大小分: {odds.total_point} ({odds.bookmaker})")
                        total_odds_count += 1
                if spreads_odds:
                    for odds in spreads_odds:
                        print(f"    讓分: {odds.home_spread_point} ({odds.bookmaker})")
                if not totals_odds and not spreads_odds:
                    print(f"    ❌ 沒有盤口數據")
            else:
                print(f"  {game.away_team}@{game.home_team}: ❌ 沒有盤口數據")
        
        print(f"📈 總共找到 {total_odds_count} 個大小分盤口記錄")

def fetch_missing_odds(target_date: date):
    """為缺少盤口數據的比賽嘗試獲取真實盤口"""
    print(f"🔍 嘗試獲取 {target_date} 缺少的盤口數據...")

    app = create_app()
    with app.app_context():
        # 獲取該日期沒有盤口數據的比賽
        games_without_odds = db.session.query(Game).filter(
            Game.date >= target_date,
            Game.date < target_date + timedelta(days=1)
        ).filter(
            ~Game.game_id.in_(
                db.session.query(BettingOdds.game_id).filter(
                    BettingOdds.market_type == 'totals'
                )
            )
        ).all()

        if not games_without_odds:
            print("✅ 所有比賽都已有盤口數據")
            return

        print(f"📊 找到 {len(games_without_odds)} 場比賽缺少盤口數據")

        # 這裡可以添加調用API獲取盤口數據的邏輯
        # 例如調用 covers_scraper 或其他盤口數據源
        for game in games_without_odds:
            print(f"  {game.away_team}@{game.home_team}: 需要獲取盤口數據")

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='更新預測系統使用真實博彩盤口')
    parser.add_argument('--date', type=str, help='目標日期 (YYYY-MM-DD)', default='2025-06-30')
    parser.add_argument('--check', action='store_true', help='只檢查盤口數據可用性')
    parser.add_argument('--fetch', action='store_true', help='嘗試獲取缺少的盤口數據')

    args = parser.parse_args()

    try:
        target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
    except ValueError:
        print("❌ 日期格式錯誤，請使用 YYYY-MM-DD 格式")
        exit(1)

    if args.check:
        check_betting_odds_availability(target_date)
    elif args.fetch:
        fetch_missing_odds(target_date)
    else:
        check_betting_odds_availability(target_date)
        print()
        update_predictions_with_real_odds(target_date)
        print()
        print("💡 提示: 使用 --fetch 參數可以嘗試獲取缺少的盤口數據")
