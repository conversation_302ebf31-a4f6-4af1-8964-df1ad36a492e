#!/usr/bin/env python3
"""
改進大小分預測準確度的腳本
基於實際結果分析和調整預測算法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction, BettingOdds
from models.over_under_predictor import OverUnderPredictor
from datetime import date, datetime, timedelta
import numpy as np
from sqlalchemy import text

def analyze_recent_over_under_accuracy():
    """分析最近的大小分預測準確度"""
    print("📊 分析最近的大小分預測準確度...")
    
    app = create_app()
    with app.app_context():
        # 獲取最近7天有實際結果的比賽
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        query = text("""
            SELECT g.game_id, g.home_team, g.away_team, g.date,
                   g.home_score, g.away_score,
                   p.predicted_total_runs, p.over_under_line,
                   p.over_probability, p.under_probability
            FROM games g
            LEFT JOIN predictions p ON g.game_id = p.game_id
            WHERE g.date BETWEEN :start_date AND :end_date
            AND g.home_score IS NOT NULL 
            AND g.away_score IS NOT NULL
            AND p.predicted_total_runs IS NOT NULL
            ORDER BY g.date DESC
        """)
        
        results = db.session.execute(query, {
            'start_date': start_date,
            'end_date': end_date
        }).fetchall()
        
        if not results:
            print("❌ 沒有找到最近的預測數據")
            return None
        
        analysis = {
            'total_games': len(results),
            'prediction_errors': [],
            'over_under_accuracy': 0,
            'avg_prediction_error': 0,
            'high_scoring_games': 0,
            'low_scoring_games': 0,
            'systematic_bias': 0
        }
        
        correct_over_under = 0
        total_prediction_error = 0
        
        print(f"\n📋 分析 {len(results)} 場比賽的預測結果:")
        print("-" * 80)
        print("比賽                    實際    預測    盤口    誤差    大小分結果")
        print("-" * 80)
        
        for row in results:
            game_id, home_team, away_team, game_date, home_score, away_score, predicted_total, over_under_line, over_prob, under_prob = row
            
            actual_total = home_score + away_score
            prediction_error = predicted_total - actual_total
            total_prediction_error += abs(prediction_error)
            
            # 判斷大小分結果
            if over_under_line:
                actual_result = "Over" if actual_total > over_under_line else "Under"
                predicted_result = "Over" if over_prob > 0.5 else "Under"
                is_correct = actual_result == predicted_result
                if is_correct:
                    correct_over_under += 1
                result_status = "✅" if is_correct else "❌"
            else:
                result_status = "N/A"
                actual_result = "N/A"
            
            # 分類比賽類型
            if actual_total >= 10:
                analysis['high_scoring_games'] += 1
            elif actual_total <= 6:
                analysis['low_scoring_games'] += 1
            
            analysis['prediction_errors'].append(prediction_error)
            
            print(f"{away_team:3} @ {home_team:3} {game_date}  {actual_total:4.0f}    {predicted_total:4.1f}    {over_under_line or 'N/A':4}   {prediction_error:+5.1f}   {actual_result:5} {result_status}")
        
        # 計算統計數據
        analysis['over_under_accuracy'] = (correct_over_under / len(results)) * 100
        analysis['avg_prediction_error'] = total_prediction_error / len(results)
        analysis['systematic_bias'] = np.mean(analysis['prediction_errors'])
        
        print("-" * 80)
        print(f"📈 分析結果:")
        print(f"  總比賽數: {analysis['total_games']}")
        print(f"  大小分準確率: {analysis['over_under_accuracy']:.1f}%")
        print(f"  平均預測誤差: {analysis['avg_prediction_error']:.2f} 分")
        print(f"  系統性偏差: {analysis['systematic_bias']:+.2f} 分")
        print(f"  高分比賽 (≥10分): {analysis['high_scoring_games']} 場")
        print(f"  低分比賽 (≤6分): {analysis['low_scoring_games']} 場")
        
        return analysis

def create_improved_calibration():
    """創建改進的校正算法"""
    print("\n🔧 創建改進的校正算法...")
    
    # 基於分析結果的新校正因子
    IMPROVED_CALIBRATION = {
        'home_multiplier': 0.75,      # 進一步降低主隊得分預測
        'away_multiplier': 0.85,      # 進一步降低客隊得分預測
        'total_cap': 10.5,            # 降低總分上限
        'low_floor': 5.0,             # 提高最低分限制
        'high_penalty': 0.8,          # 加強高分懲罰
        'pitcher_era_factor': True,   # 考慮投手ERA
        'weather_factor': True,       # 考慮天氣因素
        'park_factor': True           # 考慮球場因素
    }
    
    calibration_code = '''
def improved_score_calibration(self, home_score: float, away_score: float, 
                             home_pitcher_era: float = None, away_pitcher_era: float = None,
                             weather_conditions: dict = None, park_factor: float = 1.0) -> tuple:
    """
    改進的得分校正算法
    基於實際數據分析的多因子校正
    """
    # 基礎校正因子 (更保守)
    HOME_MULTIPLIER = 0.75
    AWAY_MULTIPLIER = 0.85
    TOTAL_CAP = 10.5
    LOW_FLOOR = 5.0
    HIGH_PENALTY = 0.8
    
    # 應用基本校正
    calibrated_home = home_score * HOME_MULTIPLIER
    calibrated_away = away_score * AWAY_MULTIPLIER
    
    # 投手ERA調整
    if home_pitcher_era and away_pitcher_era:
        # ERA越低，對方得分應該越少
        if away_pitcher_era < 3.0:  # 優秀投手
            calibrated_home *= 0.9
        elif away_pitcher_era > 5.0:  # 較差投手
            calibrated_home *= 1.1
            
        if home_pitcher_era < 3.0:
            calibrated_away *= 0.9
        elif home_pitcher_era > 5.0:
            calibrated_away *= 1.1
    
    # 球場因子調整
    calibrated_home *= park_factor
    calibrated_away *= park_factor
    
    total_score = calibrated_home + calibrated_away
    
    # 應用總分限制 (更嚴格)
    if total_score > TOTAL_CAP:
        scale_factor = TOTAL_CAP / total_score
        calibrated_home *= scale_factor
        calibrated_away *= scale_factor
        total_score = TOTAL_CAP
    
    # 應用最低分限制
    if total_score < LOW_FLOOR:
        scale_factor = LOW_FLOOR / total_score
        calibrated_home *= scale_factor
        calibrated_away *= scale_factor
    
    # 加強高分懲罰
    if total_score > 9:
        calibrated_home *= HIGH_PENALTY
        calibrated_away *= HIGH_PENALTY
    
    return round(calibrated_home, 1), round(calibrated_away, 1)
'''
    
    print("改進的校正因子:")
    for factor, value in IMPROVED_CALIBRATION.items():
        print(f"  {factor}: {value}")
    
    return calibration_code, IMPROVED_CALIBRATION

def test_improved_calibration():
    """測試改進的校正效果"""
    print("\n🧪 測試改進的校正效果...")
    
    # 基於實際案例測試
    test_cases = [
        # (原始主隊, 原始客隊, 描述, 期望總分範圍)
        (7.0, 6.0, "NYM @ KC 類型 (原預測13分)", (6, 8)),
        (5.0, 4.0, "LAD @ SF 類型 (原預測9分)", (5, 7)),
        (6.5, 5.5, "CLE @ CWS 類型 (原預測12分)", (7, 9)),
        (8.0, 7.0, "TOR @ ATH 類型 (原預測15分)", (8, 10))
    ]
    
    print("測試案例 (原始預測 -> 改進校正):")
    print("-" * 60)
    
    for home, away, desc, expected_range in test_cases:
        # 應用改進的校正
        calibrated_home = home * 0.75  # 新的主隊校正
        calibrated_away = away * 0.85  # 新的客隊校正
        
        total_original = home + away
        total_calibrated = calibrated_home + calibrated_away
        
        # 應用總分限制
        if total_calibrated > 10.5:
            scale_factor = 10.5 / total_calibrated
            calibrated_home *= scale_factor
            calibrated_away *= scale_factor
            total_calibrated = 10.5
        
        # 高分懲罰
        if total_calibrated > 9:
            calibrated_home *= 0.8
            calibrated_away *= 0.8
            total_calibrated = calibrated_home + calibrated_away
        
        in_range = expected_range[0] <= total_calibrated <= expected_range[1]
        status = "✅" if in_range else "❌"
        
        print(f"{desc}")
        print(f"  原始: {home:.1f}-{away:.1f} (總分 {total_original:.1f})")
        print(f"  校正: {calibrated_home:.1f}-{calibrated_away:.1f} (總分 {total_calibrated:.1f}) {status}")
        print(f"  期望範圍: {expected_range[0]}-{expected_range[1]} 分")
        print()

def main():
    """主要改進流程"""
    print("🎯 大小分預測準確度改進工具")
    print("=" * 60)
    
    # 1. 分析當前準確度
    analysis = analyze_recent_over_under_accuracy()
    
    if analysis:
        # 2. 創建改進的校正算法
        improved_code, factors = create_improved_calibration()
        
        # 3. 測試改進效果
        test_improved_calibration()
        
        print("\n📋 改進建議:")
        print("1. 應用更保守的校正因子 (降低25%主隊, 15%客隊得分)")
        print("2. 降低總分上限至10.5分")
        print("3. 加強高分比賽的懲罰機制")
        print("4. 考慮投手ERA對得分的影響")
        print("5. 整合天氣和球場因素")
        
        if analysis['systematic_bias'] > 1.0:
            print(f"\n⚠️  發現系統性高估偏差: {analysis['systematic_bias']:+.2f} 分")
            print("建議立即應用改進的校正算法")
        
        if analysis['over_under_accuracy'] < 60:
            print(f"\n⚠️  大小分準確率偏低: {analysis['over_under_accuracy']:.1f}%")
            print("建議重新訓練預測模型")
    
    print("\n✅ 分析完成！請根據建議調整預測算法。")

if __name__ == "__main__":
    main()
