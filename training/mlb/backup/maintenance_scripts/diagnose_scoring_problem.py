#!/usr/bin/env python3
"""
診斷球隊得分能力問題
"""

import sqlite3
import os

def diagnose_scoring_problem():
    """診斷得分問題"""
    print("🔍 診斷球隊得分能力問題")
    print("=" * 50)
    
    # 檢查數據庫
    db_path = "instance/mlb_data.db"
    if not os.path.exists(db_path):
        print(f"❌ 數據庫不存在: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 1. 檢查最近預測
    print("\n📊 最近10個預測:")
    cursor.execute("""
        SELECT p.predicted_away_score, p.predicted_home_score, 
               g.away_team, g.home_team, g.date
        FROM predictions p
        JOIN games g ON p.game_id = g.game_id
        ORDER BY g.date DESC
        LIMIT 10
    """)
    
    predictions = cursor.fetchall()
    if predictions:
        away_scores = []
        home_scores = []
        
        for pred in predictions:
            away_score, home_score, away_team, home_team, date = pred
            away_scores.append(away_score)
            home_scores.append(home_score)
            print(f"{away_team} @ {home_team}: {away_score:.1f} - {home_score:.1f} ({date})")
        
        # 分析變異性
        print(f"\n📈 預測分數分析:")
        print(f"客隊得分: {min(away_scores):.1f} - {max(away_scores):.1f}")
        print(f"主隊得分: {min(home_scores):.1f} - {max(home_scores):.1f}")
        
        unique_away = len(set(f"{s:.1f}" for s in away_scores))
        unique_home = len(set(f"{s:.1f}" for s in home_scores))
        
        print(f"客隊得分種類: {unique_away}")
        print(f"主隊得分種類: {unique_home}")
        
        if unique_away == 1 and unique_home == 1:
            print("❌ 問題確認: 所有預測分數都相同!")
        elif unique_away <= 2 and unique_home <= 2:
            print("⚠️  問題: 預測分數變異性太低")
        else:
            print("✅ 預測分數有變異性")
    
    # 2. 檢查球隊統計
    print(f"\n🏟️ 球隊統計分析:")
    cursor.execute("""
        SELECT team_id, runs_per_game, era, win_percentage
        FROM team_stats
        ORDER BY runs_per_game DESC
        LIMIT 10
    """)
    
    team_stats = cursor.fetchall()
    if team_stats:
        print("得分能力排名 (前10名):")
        runs_list = []
        
        for i, (team, rpg, era, win_pct) in enumerate(team_stats, 1):
            runs_list.append(rpg)
            print(f"{i:2d}. {team}: {rpg:.2f}分/場, ERA={era:.2f}, 勝率={win_pct:.3f}")
        
        print(f"\n📊 球隊統計變異:")
        print(f"得分範圍: {min(runs_list):.2f} - {max(runs_list):.2f}")
        print(f"得分差距: {max(runs_list) - min(runs_list):.2f}")
        
        if max(runs_list) - min(runs_list) < 1.0:
            print("❌ 問題: 球隊得分差異太小")
        else:
            print("✅ 球隊統計有合理差異")
    
    # 3. 檢查最弱和最強球隊
    print(f"\n🔍 最強 vs 最弱球隊對比:")
    
    cursor.execute("""
        SELECT team_id, runs_per_game, era
        FROM team_stats
        ORDER BY runs_per_game DESC
        LIMIT 1
    """)
    strongest = cursor.fetchone()
    
    cursor.execute("""
        SELECT team_id, runs_per_game, era
        FROM team_stats
        ORDER BY runs_per_game ASC
        LIMIT 1
    """)
    weakest = cursor.fetchone()
    
    if strongest and weakest:
        print(f"最強: {strongest[0]} - {strongest[1]:.2f}分/場, ERA={strongest[2]:.2f}")
        print(f"最弱: {weakest[0]} - {weakest[1]:.2f}分/場, ERA={weakest[2]:.2f}")
        print(f"得分差距: {strongest[1] - weakest[1]:.2f}分/場")
        
        if strongest[1] - weakest[1] < 1.0:
            print("❌ 嚴重問題: 最強和最弱球隊得分差距小於1分!")
        else:
            print("✅ 球隊實力有明顯差異")
    
    # 4. 檢查特定球隊對戰預測
    print(f"\n⚾ 檢查特定對戰預測:")
    
    # 查找最近的強隊 vs 弱隊比賽
    if strongest and weakest:
        cursor.execute("""
            SELECT p.predicted_away_score, p.predicted_home_score,
                   g.away_team, g.home_team, g.date
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE (g.home_team = ? AND g.away_team = ?) 
               OR (g.home_team = ? AND g.away_team = ?)
            ORDER BY g.date DESC
            LIMIT 3
        """, (strongest[0], weakest[0], weakest[0], strongest[0]))
        
        matchups = cursor.fetchall()
        if matchups:
            print(f"最近的 {strongest[0]} vs {weakest[0]} 對戰預測:")
            for match in matchups:
                away_score, home_score, away_team, home_team, date = match
                print(f"  {away_team} @ {home_team}: {away_score:.1f} - {home_score:.1f} ({date})")
        else:
            print(f"沒有找到 {strongest[0]} vs {weakest[0]} 的對戰記錄")
    
    conn.close()
    
    # 5. 診斷建議
    print(f"\n💡 問題診斷和建議:")
    print("1. 如果所有預測分數都相同，可能是:")
    print("   - 模型訓練數據有問題")
    print("   - 特徵提取沒有反映球隊差異")
    print("   - 模型過度簡化或正則化過強")
    print("2. 如果球隊統計差異太小，可能是:")
    print("   - TeamStats 計算方法有問題")
    print("   - 數據時間範圍設置不當")
    print("   - 統計指標選擇不合適")
    print("3. 建議解決方案:")
    print("   - 重新檢查 TeamStats 生成邏輯")
    print("   - 重新訓練模型使用更多樣化的特徵")
    print("   - 增加球隊實力差異的權重")

if __name__ == "__main__":
    diagnose_scoring_problem()
