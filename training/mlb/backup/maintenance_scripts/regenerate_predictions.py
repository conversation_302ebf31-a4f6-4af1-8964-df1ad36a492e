#!/usr/bin/env python3
"""
重新生成預測 - 使用修復後的方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction
from models.prediction_service import PredictionService
from datetime import date, datetime
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def regenerate_predictions():
    """重新生成預測"""
    app = create_app()
    with app.app_context():
        print("🔄 重新生成預測 - 使用修復後的方法")
        print("=" * 50)
        
        # 選擇要重新生成的日期
        target_date = date(2025, 6, 29)
        print(f"目標日期: {target_date}")
        
        # 查找該日期的比賽
        games = Game.query.filter_by(date=target_date).all()
        print(f"找到 {len(games)} 場比賽")
        
        if not games:
            print("❌ 沒有找到比賽")
            return
        
        # 初始化預測服務
        prediction_service = PredictionService()
        
        # 刪除現有預測
        existing_predictions = Prediction.query.join(Game).filter(Game.date == target_date).all()
        print(f"刪除 {len(existing_predictions)} 個現有預測")
        
        for pred in existing_predictions:
            db.session.delete(pred)
        db.session.commit()
        
        # 重新生成預測
        print(f"\n🎯 重新生成預測:")
        success_count = 0
        
        for i, game in enumerate(games, 1):
            try:
                print(f"  {i:2d}. {game.away_team} @ {game.home_team}", end=" ... ")
                
                # 生成預測
                result = prediction_service.generate_prediction(game.game_id)
                
                if result and result.get('created'):
                    prediction = result.get('prediction')
                    if prediction:
                        home_score = prediction.predicted_home_score
                        away_score = prediction.predicted_away_score
                        total = home_score + away_score
                        print(f"✅ {away_score:.1f} - {home_score:.1f} (總分: {total:.1f})")
                        success_count += 1
                    else:
                        print("❌ 預測對象為空")
                else:
                    print("❌ 生成失敗")
                    
            except Exception as e:
                print(f"❌ 錯誤: {e}")
        
        print(f"\n📊 結果統計:")
        print(f"成功生成: {success_count}/{len(games)}")
        print(f"成功率: {success_count/len(games)*100:.1f}%")
        
        # 檢查預測差異
        if success_count >= 3:
            print(f"\n🔍 檢查預測差異:")
            new_predictions = Prediction.query.join(Game).filter(Game.date == target_date).all()
            
            home_scores = [p.predicted_home_score for p in new_predictions]
            away_scores = [p.predicted_away_score for p in new_predictions]
            totals = [p.predicted_home_score + p.predicted_away_score for p in new_predictions]
            
            print(f"主隊得分範圍: {min(home_scores):.1f} - {max(home_scores):.1f}")
            print(f"客隊得分範圍: {min(away_scores):.1f} - {max(away_scores):.1f}")
            print(f"總分範圍: {min(totals):.1f} - {max(totals):.1f}")
            
            # 檢查差異
            home_diff = max(home_scores) - min(home_scores)
            away_diff = max(away_scores) - min(away_scores)
            total_diff = max(totals) - min(totals)
            
            print(f"\n差異分析:")
            print(f"主隊得分差異: {home_diff:.1f}")
            print(f"客隊得分差異: {away_diff:.1f}")
            print(f"總分差異: {total_diff:.1f}")
            
            if home_diff > 0.5 or away_diff > 0.5 or total_diff > 1.0:
                print("✅ 修復成功! 預測有合理差異")
            else:
                print("❌ 修復失敗! 預測差異仍然太小")
                
                # 檢查唯一值
                unique_home = len(set(f"{s:.1f}" for s in home_scores))
                unique_away = len(set(f"{s:.1f}" for s in away_scores))
                
                print(f"主隊得分唯一值數量: {unique_home}")
                print(f"客隊得分唯一值數量: {unique_away}")
                
                if unique_home == 1 and unique_away == 1:
                    print("⚠️  所有預測都完全相同!")
                    
                    # 顯示一些具體的預測
                    print(f"\n具體預測示例:")
                    for i, pred in enumerate(new_predictions[:5]):
                        game = Game.query.filter_by(game_id=pred.game_id).first()
                        print(f"  {game.away_team} @ {game.home_team}: {pred.predicted_away_score:.1f} - {pred.predicted_home_score:.1f}")
        
        print(f"\n✅ 重新生成完成!")

if __name__ == "__main__":
    regenerate_predictions()
