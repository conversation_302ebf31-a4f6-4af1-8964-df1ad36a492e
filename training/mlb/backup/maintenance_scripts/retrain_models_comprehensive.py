#!/usr/bin/env python3
"""
全面重新訓練MLB預測模型
解決模型訓練不充分的問題
"""

import sys
import os
from datetime import date, timedelta
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, r2_score
import joblib
import json

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import Game, TeamStats
from models.enhanced_feature_engineer import EnhancedFeatureEngineer

def collect_training_data():
    """收集訓練數據"""
    print("📊 收集訓練數據...")
    
    # 獲取最近3個月的已完成比賽
    end_date = date.today()
    start_date = end_date - timedelta(days=90)
    
    games = Game.query.filter(
        Game.date >= start_date,
        Game.date < end_date,
        Game.home_score.isnot(None),
        Game.away_score.isnot(None),
        Game.game_status == 'completed'
    ).all()
    
    print(f"找到 {len(games)} 場已完成比賽")
    return games

def extract_features_for_training(games):
    """為訓練提取特徵"""
    print("🔧 提取特徵...")
    
    feature_engineer = EnhancedFeatureEngineer()
    
    features_list = []
    targets_list = []
    
    for i, game in enumerate(games):
        if i % 100 == 0:
            print(f"處理進度: {i}/{len(games)}")
        
        try:
            # 提取特徵
            features = feature_engineer.extract_comprehensive_features(
                game.home_team, game.away_team, game.date
            )
            
            if features:
                # 轉換為數值特徵
                feature_vector = []
                feature_names = []
                
                for key, value in features.items():
                    if isinstance(value, (int, float)) and not np.isnan(value):
                        feature_vector.append(value)
                        feature_names.append(key)
                
                if len(feature_vector) > 10:  # 確保有足夠的特徵
                    features_list.append(feature_vector)
                    targets_list.append({
                        'home_score': game.home_score,
                        'away_score': game.away_score,
                        'home_win': 1 if game.home_score > game.away_score else 0
                    })
        
        except Exception as e:
            print(f"處理比賽 {game.game_id} 時出錯: {e}")
            continue
    
    print(f"成功提取 {len(features_list)} 個訓練樣本")
    return np.array(features_list), targets_list, feature_names

def train_enhanced_models(X, y_list, feature_names):
    """訓練增強模型"""
    print("🎯 開始訓練模型...")
    
    # 準備目標變量
    y_home_score = np.array([y['home_score'] for y in y_list])
    y_away_score = np.array([y['away_score'] for y in y_list])
    y_home_win = np.array([y['home_win'] for y in y_list])
    
    # 分割訓練和測試數據
    X_train, X_test, y_home_train, y_home_test = train_test_split(
        X, y_home_score, test_size=0.2, random_state=42
    )
    _, _, y_away_train, y_away_test = train_test_split(
        X, y_away_score, test_size=0.2, random_state=42
    )
    _, _, y_win_train, y_win_test = train_test_split(
        X, y_home_win, test_size=0.2, random_state=42
    )
    
    # 特徵縮放
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    models = {}
    performance = {}
    
    # 1. 主隊得分模型
    print("訓練主隊得分模型...")
    home_score_model = GradientBoostingRegressor(
        n_estimators=200,
        learning_rate=0.1,
        max_depth=6,
        random_state=42
    )
    home_score_model.fit(X_train_scaled, y_home_train)
    
    # 評估
    y_home_pred = home_score_model.predict(X_test_scaled)
    home_mae = mean_absolute_error(y_home_test, y_home_pred)
    home_r2 = r2_score(y_home_test, y_home_pred)
    
    models['home_score'] = home_score_model
    performance['home_score'] = {'mae': home_mae, 'r2': home_r2}
    print(f"主隊得分模型 - MAE: {home_mae:.3f}, R²: {home_r2:.3f}")
    
    # 2. 客隊得分模型
    print("訓練客隊得分模型...")
    away_score_model = GradientBoostingRegressor(
        n_estimators=200,
        learning_rate=0.1,
        max_depth=6,
        random_state=42
    )
    away_score_model.fit(X_train_scaled, y_away_train)
    
    # 評估
    y_away_pred = away_score_model.predict(X_test_scaled)
    away_mae = mean_absolute_error(y_away_test, y_away_pred)
    away_r2 = r2_score(y_away_test, y_away_pred)
    
    models['away_score'] = away_score_model
    performance['away_score'] = {'mae': away_mae, 'r2': away_r2}
    print(f"客隊得分模型 - MAE: {away_mae:.3f}, R²: {away_r2:.3f}")
    
    # 3. 勝負預測模型
    print("訓練勝負預測模型...")
    win_model = RandomForestRegressor(
        n_estimators=150,
        max_depth=8,
        random_state=42
    )
    win_model.fit(X_train_scaled, y_win_train)
    
    # 評估
    y_win_pred = win_model.predict(X_test_scaled)
    win_mae = mean_absolute_error(y_win_test, y_win_pred)
    win_r2 = r2_score(y_win_test, y_win_pred)
    
    models['win_probability'] = win_model
    performance['win_probability'] = {'mae': win_mae, 'r2': win_r2}
    print(f"勝負預測模型 - MAE: {win_mae:.3f}, R²: {win_r2:.3f}")
    
    return models, scaler, performance, feature_names

def save_models(models, scaler, performance, feature_names):
    """保存模型"""
    print("💾 保存模型...")
    
    model_dir = 'models/saved'
    os.makedirs(model_dir, exist_ok=True)
    
    # 保存模型
    for name, model in models.items():
        filename = f'{name}_model.joblib'
        joblib.dump(model, os.path.join(model_dir, filename))
        print(f"保存 {filename}")
    
    # 保存縮放器
    joblib.dump(scaler, os.path.join(model_dir, 'feature_scaler.joblib'))
    print("保存 feature_scaler.joblib")
    
    # 保存特徵列名
    with open(os.path.join(model_dir, 'feature_columns.json'), 'w') as f:
        json.dump(feature_names, f)
    print("保存 feature_columns.json")
    
    # 保存性能指標
    with open(os.path.join(model_dir, 'model_performance.json'), 'w') as f:
        json.dump(performance, f, indent=2)
    print("保存 model_performance.json")

def test_model_loading():
    """測試模型載入"""
    print("🧪 測試模型載入...")
    
    try:
        from models.ml_predictor import MLBPredictor
        predictor = MLBPredictor()
        
        if predictor.models and all(predictor.models.values()):
            print("✅ 模型載入成功！")
            return True
        else:
            print("❌ 模型載入失敗")
            return False
    except Exception as e:
        print(f"❌ 模型載入錯誤: {e}")
        return False

def main():
    """主函數"""
    print("🚀 開始全面重新訓練MLB預測模型")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 收集訓練數據
            games = collect_training_data()
            
            if len(games) < 100:
                print("❌ 訓練數據不足，需要至少100場比賽")
                return
            
            # 2. 提取特徵
            X, y_list, feature_names = extract_features_for_training(games)
            
            if len(X) < 50:
                print("❌ 有效特徵數據不足")
                return
            
            print(f"特徵維度: {X.shape}")
            print(f"特徵數量: {len(feature_names)}")
            
            # 3. 訓練模型
            models, scaler, performance, feature_names = train_enhanced_models(
                X, y_list, feature_names
            )
            
            # 4. 保存模型
            save_models(models, scaler, performance, feature_names)
            
            # 5. 測試載入
            success = test_model_loading()
            
            if success:
                print("\n🎉 模型重新訓練完成！")
                print("📈 性能指標:")
                for model_name, perf in performance.items():
                    print(f"  {model_name}: MAE={perf['mae']:.3f}, R²={perf['r2']:.3f}")
                
                print("\n💡 建議:")
                print("1. 重啟應用程序以載入新模型")
                print("2. 測試新模型的預測效果")
                print("3. 監控預測準確度")
            else:
                print("\n❌ 模型訓練完成但載入失敗，請檢查模型文件")
        
        except Exception as e:
            print(f"\n❌ 訓練過程中出錯: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
