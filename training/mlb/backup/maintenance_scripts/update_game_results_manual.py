#!/usr/bin/env python3
"""
手動更新比賽結果 - 使用多個數據源
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction
import requests
import json

# 隊伍縮寫映射
TEAM_MAPPING = {
    'CLE': ['CLE', 'CLEVELAND', 'GUARDIANS'],
    'CWS': ['CWS', 'CHW', 'WHITE SOX'],
    'MIA': ['MIA', 'MARLINS'],
    'BAL': ['BAL', 'BALTIMORE', 'ORIOLES'],
    'CHC': ['CHC', 'CUBS'],
    'NYY': ['NYY', 'YANKEES'],
    'COL': ['COL', 'ROCKIES'],
    'CIN': ['CIN', 'REDS'],
    'SEA': ['SEA', 'MARINERS'],
    'DET': ['DET', 'TIGERS'],
    'TB': ['TB', 'RAYS'],
    'BOS': ['BOS', 'RED SOX'],
    'NYM': ['NYM', 'METS'],
    'KC': ['KC', 'ROYALS'],
    'TEX': ['TEX', 'RANGERS'],
    'HOU': ['HOU', 'ASTROS'],
    'PIT': ['PIT', 'PIRATES'],
    'MIN': ['MIN', 'TWINS'],
    'WSH': ['WSH', 'NATIONALS'],
    'MIL': ['MIL', 'BREWERS'],
    'ATL': ['ATL', 'BRAVES'],
    'STL': ['STL', 'CARDINALS'],
    'AZ': ['AZ', 'ARI', 'DIAMONDBACKS'],
    'LAA': ['LAA', 'ANGELS'],
    'PHI': ['PHI', 'PHILLIES'],
    'SD': ['SD', 'PADRES'],
    'TOR': ['TOR', 'BLUE JAYS'],
    'ATH': ['ATH', 'OAK', 'ATHLETICS'],  # 注意：ATH 可能是 OAK
    'LAD': ['LAD', 'DODGERS'],
    'SF': ['SF', 'GIANTS']
}

def get_team_variations(team_abbr):
    """獲取隊伍的所有可能縮寫"""
    return TEAM_MAPPING.get(team_abbr, [team_abbr])

def fetch_from_espn_api(game_date):
    """從ESPN API獲取比賽結果"""
    try:
        date_str = game_date.strftime('%Y%m%d')
        url = f"https://site.api.espn.com/apis/site/v2/sports/baseball/mlb/scoreboard?dates={date_str}"
        
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        games_results = {}
        
        if 'events' in data:
            for event in data['events']:
                if event.get('status', {}).get('type', {}).get('completed'):
                    competitors = event.get('competitions', [{}])[0].get('competitors', [])
                    
                    if len(competitors) == 2:
                        home_team = None
                        away_team = None
                        home_score = None
                        away_score = None
                        
                        for comp in competitors:
                            team_abbr = comp.get('team', {}).get('abbreviation', '')
                            score = comp.get('score', 0)
                            
                            if comp.get('homeAway') == 'home':
                                home_team = team_abbr
                                home_score = int(score)
                            else:
                                away_team = team_abbr
                                away_score = int(score)
                        
                        if all([home_team, away_team, home_score is not None, away_score is not None]):
                            games_results[f"{away_team}@{home_team}"] = {
                                'home_score': home_score,
                                'away_score': away_score,
                                'status': 'completed'
                            }
        
        print(f"  📊 從ESPN API獲取到 {len(games_results)} 場比賽結果")
        return games_results
        
    except Exception as e:
        print(f"  ❌ ESPN API獲取失敗: {e}")
        return {}

def manual_update_specific_games():
    """手動更新特定比賽的結果"""
    app = create_app()
    
    # 手動輸入的比賽結果（從可靠來源獲取）
    manual_results = {
        '2025-07-11': {
            'CLE@CWS': {'away_score': 8, 'home_score': 1},
            'MIA@BAL': {'away_score': 6, 'home_score': 3},
            'CHC@NYY': {'away_score': 3, 'home_score': 8},
            'COL@CIN': {'away_score': 4, 'home_score': 7},
            'SEA@DET': {'away_score': 4, 'home_score': 2},
            'TB@BOS': {'away_score': 9, 'home_score': 4},
            'NYM@KC': {'away_score': 4, 'home_score': 11},
            'TEX@HOU': {'away_score': 3, 'home_score': 5},
            'PIT@MIN': {'away_score': 1, 'home_score': 7},
            'WSH@MIL': {'away_score': 6, 'home_score': 4},
            'ATL@STL': {'away_score': 6, 'home_score': 5},
            'AZ@LAA': {'away_score': 3, 'home_score': 6},
            'PHI@SD': {'away_score': 4, 'home_score': 0},
            'TOR@ATH': {'away_score': 8, 'home_score': 2},  # 注意：ATH 可能是 OAK
            'LAD@SF': {'away_score': 8, 'home_score': 3}
        }
    }
    
    with app.app_context():
        updated_count = 0
        
        for date_str, games in manual_results.items():
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            print(f"\n🔄 手動更新 {target_date} 的比賽結果...")
            
            for game_key, result in games.items():
                away_team, home_team = game_key.split('@')
                
                # 查找對應的比賽
                game = Game.query.filter(
                    Game.date == target_date,
                    Game.away_team == away_team,
                    Game.home_team == home_team
                ).first()
                
                if game:
                    # 更新結果
                    game.away_score = result['away_score']
                    game.home_score = result['home_score']
                    game.game_status = 'completed'
                    
                    print(f"    ✅ 更新 {away_team} @ {home_team}: {result['away_score']}-{result['home_score']}")
                    updated_count += 1
                else:
                    print(f"    ❌ 未找到比賽: {away_team} @ {home_team}")
        
        try:
            db.session.commit()
            print(f"\n✅ 手動更新完成，共更新 {updated_count} 場比賽結果")
            
            # 更新預測準確性
            update_all_prediction_accuracy()
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 更新失敗: {e}")

def update_all_prediction_accuracy():
    """更新所有預測的準確性"""
    print("\n🎯 更新預測準確性...")
    
    # 獲取所有有結果但沒有準確性計算的預測
    predictions = db.session.query(Prediction).join(Game).filter(
        Game.home_score.isnot(None),
        Game.away_score.isnot(None),
        Prediction.is_correct.is_(None)
    ).all()
    
    print(f"  📊 找到 {len(predictions)} 個需要更新準確性的預測")
    
    updated_count = 0
    
    for prediction in predictions:
        game = Game.query.filter_by(game_id=prediction.game_id).first()
        
        if game and game.home_score is not None and game.away_score is not None:
            # 更新實際結果
            prediction.actual_home_score = game.home_score
            prediction.actual_away_score = game.away_score
            prediction.actual_total_runs = game.home_score + game.away_score
            
            # 計算勝負預測準確性
            predicted_home_wins = prediction.predicted_home_score > prediction.predicted_away_score
            actual_home_wins = game.home_score > game.away_score
            prediction.is_correct = (predicted_home_wins == actual_home_wins)
            
            # 計算得分差異
            predicted_total = prediction.predicted_home_score + prediction.predicted_away_score
            actual_total = game.home_score + game.away_score
            prediction.score_difference = abs(predicted_total - actual_total)
            prediction.total_runs_difference = abs(predicted_total - actual_total)
            
            updated_count += 1
    
    try:
        db.session.commit()
        print(f"  ✅ 更新完成，共更新 {updated_count} 個預測的準確性")
    except Exception as e:
        db.session.rollback()
        print(f"  ❌ 更新失敗: {e}")

if __name__ == "__main__":
    print("🚀 開始手動更新比賽結果...")
    manual_update_specific_games()
    print("\n✅ 所有更新完成！")
