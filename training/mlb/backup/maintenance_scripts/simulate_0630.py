#!/usr/bin/env python3
"""
06/30 模擬測試腳本
專門用於模擬 2025-06-30 當天的預測情況
"""

import sys
import os
from datetime import datetime, date
from flask import Flask

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction, Team
from models.unified_betting_predictor import UnifiedBettingPredictor

def simulate_0630():
    """模擬 06/30 當天的情況"""
    print("🎯 模擬 2025-06-30 當天情況")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        target_date = date(2025, 6, 30)
        
        # 步驟1: 檢查當天比賽情況
        print("📊 步驟1: 檢查當天比賽情況")
        print("-" * 30)
        
        games = Game.query.filter(Game.date == target_date).all()
        print(f"總比賽數: {len(games)}")
        
        if not games:
            print("❌ 沒有找到 06/30 的比賽記錄")
            return
        
        # 按狀態分組
        status_counts = {}
        for game in games:
            status = game.game_status
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print("比賽狀態分布:")
        for status, count in status_counts.items():
            print(f"  {status}: {count} 場")
        
        # 步驟2: 檢查預測情況
        print("\n🔮 步驟2: 檢查預測情況")
        print("-" * 30)
        
        predictions = []
        for game in games:
            pred = Prediction.query.filter_by(game_id=game.game_id).first()
            if pred:
                predictions.append({
                    'game': game,
                    'prediction': pred
                })
        
        print(f"有預測的比賽: {len(predictions)}/{len(games)}")
        print(f"預測覆蓋率: {len(predictions)/len(games)*100:.1f}%")
        
        # 步驟3: 分析預測準確性
        print("\n🎯 步驟3: 分析預測準確性")
        print("-" * 30)
        
        correct_predictions = 0
        total_evaluated = 0
        
        for pred_data in predictions:
            game = pred_data['game']
            pred = pred_data['prediction']
            
            if (game.game_status == 'completed' and 
                game.home_score is not None and 
                game.away_score is not None and
                pred.predicted_home_score is not None and
                pred.predicted_away_score is not None):
                
                total_evaluated += 1
                
                # 檢查勝負預測
                actual_home_win = game.home_score > game.away_score
                predicted_home_win = pred.predicted_home_score > pred.predicted_away_score
                
                is_correct = actual_home_win == predicted_home_win
                if is_correct:
                    correct_predictions += 1
                
                # 顯示詳細結果
                home_team = get_team_name(game.home_team)
                away_team = get_team_name(game.away_team)
                
                print(f"  {away_team} @ {home_team}")
                print(f"    實際: {game.away_score}-{game.home_score}")
                print(f"    預測: {pred.predicted_away_score:.1f}-{pred.predicted_home_score:.1f}")
                print(f"    結果: {'✅ 正確' if is_correct else '❌ 錯誤'}")
                print()
        
        if total_evaluated > 0:
            accuracy = correct_predictions / total_evaluated * 100
            print(f"📈 預測準確率: {accuracy:.1f}% ({correct_predictions}/{total_evaluated})")
        else:
            print("⚠️  沒有可評估的預測")
        
        # 步驟4: 模型版本分析
        print("\n🔧 步驟4: 模型版本分析")
        print("-" * 30)
        
        model_versions = {}
        for pred_data in predictions:
            pred = pred_data['prediction']
            version = pred.model_version or 'unknown'
            if version not in model_versions:
                model_versions[version] = []
            model_versions[version].append(pred_data)
        
        print("模型版本分布:")
        for version, pred_list in model_versions.items():
            print(f"  {version}: {len(pred_list)} 個預測")
        
        # 步驟5: 生成模擬報告
        print("\n📋 步驟5: 生成模擬報告")
        print("-" * 30)
        
        report = generate_simulation_report(target_date, games, predictions, 
                                          correct_predictions, total_evaluated)
        
        print("模擬報告已生成:")
        print(f"  日期: {report['date']}")
        print(f"  總比賽: {report['total_games']}")
        print(f"  預測覆蓋: {report['prediction_coverage']:.1f}%")
        print(f"  準確率: {report['accuracy']:.1f}%" if report['accuracy'] else "  準確率: N/A")
        
        # 步驟6: 測試新預測生成
        print("\n🚀 步驟6: 測試新預測生成")
        print("-" * 30)
        
        try:
            predictor = UnifiedBettingPredictor()
            print("✅ 統一預測器初始化成功")
            
            # 測試單場比賽預測
            if games:
                test_game = games[0]
                print(f"測試比賽: {test_game.away_team} @ {test_game.home_team}")
                
                # 這裡可以添加更多測試邏輯
                print("✅ 預測器功能正常")
            
        except Exception as e:
            print(f"❌ 預測器測試失敗: {e}")
        
        print("\n🎉 06/30 模擬完成!")
        return report

def get_team_name(team_code):
    """獲取球隊名稱"""
    team = Team.query.filter_by(team_code=team_code).first()
    return team.team_name if team else team_code

def generate_simulation_report(target_date, games, predictions, correct_predictions, total_evaluated):
    """生成模擬報告"""
    
    # 計算各種統計
    completed_games = len([g for g in games if g.game_status == 'completed'])
    scheduled_games = len([g for g in games if g.game_status == 'scheduled'])
    postponed_games = len([g for g in games if g.game_status == 'postponed'])
    
    prediction_coverage = len(predictions) / len(games) * 100 if games else 0
    accuracy = correct_predictions / total_evaluated * 100 if total_evaluated > 0 else None
    
    # 模型版本統計
    model_versions = {}
    for pred_data in predictions:
        pred = pred_data['prediction']
        version = pred.model_version or 'unknown'
        model_versions[version] = model_versions.get(version, 0) + 1
    
    report = {
        'date': target_date.isoformat(),
        'total_games': len(games),
        'completed_games': completed_games,
        'scheduled_games': scheduled_games,
        'postponed_games': postponed_games,
        'predictions_count': len(predictions),
        'prediction_coverage': prediction_coverage,
        'evaluated_games': total_evaluated,
        'correct_predictions': correct_predictions,
        'accuracy': accuracy,
        'model_versions': model_versions,
        'simulation_time': datetime.now().isoformat()
    }
    
    return report

def detailed_game_analysis():
    """詳細的比賽分析"""
    print("\n🔍 詳細比賽分析")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        target_date = date(2025, 6, 30)
        games = Game.query.filter(Game.date == target_date).all()
        
        for game in games:
            print(f"\n比賽 ID: {game.game_id}")
            print(f"對戰: {game.away_team} @ {game.home_team}")
            print(f"狀態: {game.game_status}")
            
            if game.home_score is not None and game.away_score is not None:
                print(f"比分: {game.away_score} - {game.home_score}")
            
            # 檢查預測
            pred = Prediction.query.filter_by(game_id=game.game_id).first()
            if pred:
                print(f"預測比分: {pred.predicted_away_score:.1f} - {pred.predicted_home_score:.1f}")
                print(f"勝率: {(pred.home_win_probability or 0.5) * 100:.1f}%")
                print(f"模型版本: {pred.model_version}")
                
                if pred.over_probability and pred.under_probability:
                    print(f"大小分: 大分 {pred.over_probability*100:.1f}% / 小分 {pred.under_probability*100:.1f}%")
            else:
                print("❌ 沒有預測記錄")
            
            print("-" * 40)

def compare_prediction_methods():
    """比較不同預測方法"""
    print("\n⚖️  比較預測方法")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        target_date = date(2025, 6, 30)
        
        # 獲取不同模型版本的預測
        predictions_by_version = {}
        
        games = Game.query.filter(Game.date == target_date).all()
        
        for game in games:
            preds = Prediction.query.filter_by(game_id=game.game_id).all()
            
            for pred in preds:
                version = pred.model_version or 'unknown'
                if version not in predictions_by_version:
                    predictions_by_version[version] = []
                
                predictions_by_version[version].append({
                    'game': game,
                    'prediction': pred
                })
        
        # 比較各版本表現
        print("模型版本比較:")
        for version, pred_list in predictions_by_version.items():
            correct = 0
            total = 0
            
            for item in pred_list:
                game = item['game']
                pred = item['prediction']
                
                if (game.game_status == 'completed' and 
                    game.home_score is not None and 
                    game.away_score is not None):
                    
                    total += 1
                    actual_home_win = game.home_score > game.away_score
                    predicted_home_win = pred.predicted_home_score > pred.predicted_away_score
                    
                    if actual_home_win == predicted_home_win:
                        correct += 1
            
            accuracy = correct / total * 100 if total > 0 else 0
            print(f"  {version}: {correct}/{total} = {accuracy:.1f}%")

if __name__ == '__main__':
    print("🎯 06/30 模擬測試系統")
    print("=" * 60)
    
    # 主要模擬
    report = simulate_0630()
    
    # 詳細分析
    detailed_game_analysis()
    
    # 方法比較
    compare_prediction_methods()
    
    print("\n✅ 所有模擬測試完成!")
