#!/usr/bin/env python3
"""
手動更新6月24日數據的腳本
"""

from app import create_app
from models.data_fetcher import MLBDataFetcher
from models.detailed_data_fetcher import DetailedDataFetcher
from models.database import Game, db
from datetime import date
import logging

def main():
    logging.basicConfig(level=logging.INFO)
    
    app = create_app()
    with app.app_context():
        target_date = date(2025, 6, 24)
        
        print(f'🔄 手動更新 {target_date} 的數據...')
        
        # 檢查當前狀態
        games_count = Game.query.filter(Game.date == target_date).count()
        completed_games = Game.query.filter(
            Game.date == target_date, 
            Game.game_status == 'completed'
        ).count()
        
        print(f'📊 當前狀態: {games_count} 場比賽, {completed_games} 場已完成')
        
        try:
            # 1. 更新比賽狀態和比分
            print('步驟1: 更新比賽狀態和比分...')
            game_fetcher = MLBDataFetcher()
            game_fetcher.update_games_for_date(target_date)
            
            # 檢查更新後狀態
            completed_after = Game.query.filter(
                Game.date == target_date, 
                Game.game_status == 'completed'
            ).count()
            print(f'✅ 更新後: {completed_after} 場已完成')
            
            # 2. 下載Box Score
            if completed_after > 0:
                print('步驟2: 下載Box Score...')
                detailed_fetcher = DetailedDataFetcher()
                boxscore_result = detailed_fetcher.fetch_completed_games_boxscores(target_date)
                
                if boxscore_result:
                    print('✅ Box Score下載完成')
                else:
                    print('⚠️ Box Score下載可能有問題')
            else:
                print('⚠️ 沒有已完成的比賽，跳過Box Score下載')
            
            print(f'✅ {target_date} 數據更新完成！')
            
        except Exception as e:
            print(f'❌ 更新失敗: {e}')
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
