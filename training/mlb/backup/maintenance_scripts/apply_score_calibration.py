#!/usr/bin/env python3
"""
立即應用得分校正因子
修復預測得分高估2.45分的問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.unified_betting_predictor import UnifiedBettingPredictor
from models.prediction_service import PredictionService
import sqlite3

def create_calibrated_prediction_service():
    """創建校正後的預測服務"""
    print("🔧 創建校正後的預測服務...")
    
    # 校正因子（基於分析結果）
    CALIBRATION_FACTORS = {
        'home_score_multiplier': 0.80,  # 降低主隊預測得分20%
        'away_score_multiplier': 0.95,  # 降低客隊預測得分5%
        'total_score_cap': 11.5,        # 總得分上限
        'low_score_floor': 4.0,         # 總得分下限
        'high_score_penalty': 0.9       # 高分預測懲罰因子
    }
    
    calibration_code = f'''
def apply_score_calibration(self, home_score, away_score):
    """應用得分校正因子"""
    # 校正因子
    HOME_MULTIPLIER = {CALIBRATION_FACTORS['home_score_multiplier']}
    AWAY_MULTIPLIER = {CALIBRATION_FACTORS['away_score_multiplier']}
    TOTAL_CAP = {CALIBRATION_FACTORS['total_score_cap']}
    LOW_FLOOR = {CALIBRATION_FACTORS['low_score_floor']}
    HIGH_PENALTY = {CALIBRATION_FACTORS['high_score_penalty']}
    
    # 應用基本校正
    calibrated_home = home_score * HOME_MULTIPLIER
    calibrated_away = away_score * AWAY_MULTIPLIER
    
    total_score = calibrated_home + calibrated_away
    
    # 應用總分限制
    if total_score > TOTAL_CAP:
        # 按比例降低
        scale_factor = TOTAL_CAP / total_score
        calibrated_home *= scale_factor
        calibrated_away *= scale_factor
        total_score = TOTAL_CAP
    
    # 應用最低分限制
    if total_score < LOW_FLOOR:
        # 按比例提高
        scale_factor = LOW_FLOOR / total_score
        calibrated_home *= scale_factor
        calibrated_away *= scale_factor
    
    # 高分懲罰
    if total_score > 10:
        calibrated_home *= HIGH_PENALTY
        calibrated_away *= HIGH_PENALTY
    
    return round(calibrated_home, 1), round(calibrated_away, 1)
'''
    
    print("校正因子:")
    for factor, value in CALIBRATION_FACTORS.items():
        print(f"  {factor}: {value}")
    
    return calibration_code, CALIBRATION_FACTORS

def patch_unified_betting_predictor():
    """修補統一博彩預測器"""
    print("\n🎯 修補統一博彩預測器...")
    
    # 讀取當前文件
    predictor_file = 'models/unified_betting_predictor.py'
    
    try:
        with open(predictor_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否已經有校正函數
        if 'apply_score_calibration' in content:
            print("✅ 校正函數已存在")
            return True
        
        # 添加校正函數
        calibration_code, factors = create_calibrated_prediction_service()
        
        # 在類定義後添加校正方法
        class_pattern = 'class UnifiedBettingPredictor:'
        if class_pattern in content:
            # 找到合適的位置插入校正方法
            lines = content.split('\n')
            insert_index = -1
            
            for i, line in enumerate(lines):
                if 'def __init__' in line and 'UnifiedBettingPredictor' in lines[max(0, i-10):i]:
                    # 找到__init__方法後的位置
                    for j in range(i, len(lines)):
                        if lines[j].strip() == '' and j < len(lines) - 1:
                            if not lines[j+1].startswith('    '):  # 不是縮進的行
                                insert_index = j
                                break
                    break
            
            if insert_index > 0:
                # 插入校正方法
                calibration_lines = calibration_code.strip().split('\n')
                # 添加適當的縮進
                indented_lines = ['    ' + line if line.strip() else line for line in calibration_lines]
                
                lines[insert_index:insert_index] = [''] + indented_lines + ['']
                
                # 寫回文件
                with open(predictor_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                
                print(f"✅ 已添加校正方法到 {predictor_file}")
                return True
        
        print("❌ 無法找到合適的插入位置")
        return False
        
    except Exception as e:
        print(f"❌ 修補失敗: {e}")
        return False

def patch_prediction_calls():
    """修補預測調用以使用校正"""
    print("\n🔄 修補預測調用...")
    
    predictor_file = 'models/unified_betting_predictor.py'
    
    try:
        with open(predictor_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找預測分數的賦值
        patterns_to_replace = [
            ('predicted_home_score = ', 'predicted_home_score_raw = '),
            ('predicted_away_score = ', 'predicted_away_score_raw = ')
        ]
        
        modified = False
        for old_pattern, new_pattern in patterns_to_replace:
            if old_pattern in content and new_pattern not in content:
                content = content.replace(old_pattern, new_pattern)
                modified = True
        
        # 添加校正調用
        if modified and 'apply_score_calibration' in content:
            # 在預測結果創建前添加校正
            calibration_call = '''
        # 應用得分校正
        if hasattr(self, 'apply_score_calibration'):
            predicted_home_score, predicted_away_score = self.apply_score_calibration(
                predicted_home_score_raw, predicted_away_score_raw
            )
        else:
            predicted_home_score = predicted_home_score_raw
            predicted_away_score = predicted_away_score_raw
'''
            
            # 查找合適的插入位置
            if 'predicted_total_runs =' in content:
                content = content.replace(
                    'predicted_total_runs =',
                    calibration_call + '\n        predicted_total_runs ='
                )
                
                with open(predictor_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ 已添加校正調用")
                return True
        
        print("⚠️  未找到需要修改的預測調用")
        return False
        
    except Exception as e:
        print(f"❌ 修補預測調用失敗: {e}")
        return False

def test_calibration():
    """測試校正效果"""
    print("\n🧪 測試校正效果...")
    
    # 模擬測試數據
    test_cases = [
        (7.0, 5.0, "正常得分"),
        (9.0, 8.0, "高分比賽"),
        (3.0, 2.0, "低分比賽"),
        (12.0, 10.0, "極高分比賽")
    ]
    
    # 校正因子
    HOME_MULTIPLIER = 0.80
    AWAY_MULTIPLIER = 0.95
    TOTAL_CAP = 11.5
    LOW_FLOOR = 4.0
    HIGH_PENALTY = 0.9
    
    print("校正效果測試:")
    print("原始得分 -> 校正後得分 (類型)")
    print("-" * 40)
    
    for home, away, desc in test_cases:
        # 應用校正
        calibrated_home = home * HOME_MULTIPLIER
        calibrated_away = away * AWAY_MULTIPLIER
        total_score = calibrated_home + calibrated_away
        
        # 應用限制
        if total_score > TOTAL_CAP:
            scale_factor = TOTAL_CAP / total_score
            calibrated_home *= scale_factor
            calibrated_away *= scale_factor
            total_score = TOTAL_CAP
        
        if total_score < LOW_FLOOR:
            scale_factor = LOW_FLOOR / total_score
            calibrated_home *= scale_factor
            calibrated_away *= scale_factor
        
        if total_score > 10:
            calibrated_home *= HIGH_PENALTY
            calibrated_away *= HIGH_PENALTY
        
        original_total = home + away
        final_total = calibrated_home + calibrated_away
        
        print(f"{home:.1f}-{away:.1f} ({original_total:.1f}) -> {calibrated_home:.1f}-{calibrated_away:.1f} ({final_total:.1f}) [{desc}]")

def main():
    """主要執行流程"""
    print("🎯 應用MLB預測得分校正")
    print("=" * 50)
    print("目標: 修復預測得分高估2.45分的問題")
    print("=" * 50)
    
    # 1. 創建校正服務
    calibration_code, factors = create_calibrated_prediction_service()
    
    # 2. 修補預測器
    patch_success = patch_unified_betting_predictor()
    
    # 3. 修補預測調用
    if patch_success:
        call_success = patch_prediction_calls()
    else:
        call_success = False
    
    # 4. 測試校正效果
    test_calibration()
    
    # 總結
    print(f"\n📋 校正應用結果:")
    print("=" * 50)
    
    if patch_success:
        print("✅ 校正方法已添加到預測器")
    else:
        print("❌ 校正方法添加失敗")
    
    if call_success:
        print("✅ 預測調用已修改為使用校正")
    else:
        print("❌ 預測調用修改失敗")
    
    print(f"\n🎯 預期改進效果:")
    print("  - 總得分偏差: 2.45分 → 0.5分以內")
    print("  - 主隊得分偏差: 2.42分 → 0.3分以內")
    print("  - 極端高分預測: 大幅減少")
    print("  - 預測準確率: 72.9% → 76%+")
    
    if patch_success and call_success:
        print(f"\n✅ 校正已成功應用！")
        print("請重新啟動預測系統以使用新的校正功能")
    else:
        print(f"\n⚠️  校正應用不完整，請手動檢查修改")

if __name__ == "__main__":
    main()
