#!/usr/bin/env python3
"""
最終驗證所有預測數據都使用真實博彩盤口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from datetime import datetime, date

def verify_all_real_odds():
    """驗證所有預測都使用真實博彩盤口"""
    print("🔍 最終驗證：所有預測數據都使用真實博彩盤口")
    print("=" * 80)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查每日預測與真實盤口的匹配情況
        cursor.execute("""
            SELECT 
                g.date,
                g.away_team || '@' || g.home_team as matchup,
                p.over_under_line as pred_line,
                bo.total_point as bet365_line,
                bo.bookmaker,
                CASE 
                    WHEN ABS(p.over_under_line - bo.total_point) < 0.1 THEN '✅ 一致'
                    ELSE '❌ 不一致'
                END as consistency,
                p.actual_total_runs,
                CASE 
                    WHEN p.is_correct = 1 THEN '✅ 正確'
                    WHEN p.is_correct = 0 THEN '❌ 錯誤'
                    ELSE '⏳ 待定'
                END as prediction_result
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            JOIN betting_odds bo ON p.game_id = bo.game_id 
                AND bo.market_type = 'totals' AND bo.bookmaker = 'bet365'
            WHERE p.model_version = 'unified_v1.0'
            ORDER BY g.date, g.game_id
        """)
        
        results = cursor.fetchall()
        
        print("日期       | 比賽對戰        | 預測盤口 | bet365盤口 | 一致性   | 實際得分 | 預測結果")
        print("-" * 90)
        
        consistent_count = 0
        total_count = 0
        correct_predictions = 0
        completed_games = 0
        
        current_date = None
        
        for date_str, matchup, pred_line, bet365_line, bookmaker, consistency, actual_runs, result in results:
            if current_date != date_str:
                if current_date is not None:
                    print("-" * 90)
                current_date = date_str
                print(f"\n📅 {date_str}")
                print("-" * 90)
            
            actual_str = f"{actual_runs}" if actual_runs is not None else "N/A"
            
            print(f"{date_str} | {matchup:15s} | {pred_line:8.1f} | {bet365_line:10.1f} | {consistency:8s} | {actual_str:8s} | {result}")
            
            total_count += 1
            if "一致" in consistency:
                consistent_count += 1
            if "正確" in result:
                correct_predictions += 1
            if actual_runs is not None:
                completed_games += 1
        
        consistency_rate = (consistent_count / total_count * 100) if total_count > 0 else 0
        accuracy_rate = (correct_predictions / completed_games * 100) if completed_games > 0 else 0
        
        print("\n" + "=" * 80)
        print("📊 總體統計:")
        print(f"   總預測數: {total_count}")
        print(f"   盤口一致: {consistent_count}")
        print(f"   一致率: {consistency_rate:.1f}%")
        print(f"   已完成比賽: {completed_games}")
        print(f"   正確預測: {correct_predictions}")
        print(f"   預測準確率: {accuracy_rate:.1f}%")
        
        if consistency_rate == 100.0:
            print("\n✅ 所有預測都使用真實的bet365博彩盤口！")
        else:
            print(f"\n⚠️  有 {total_count - consistent_count} 個預測的盤口不一致")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        import traceback
        traceback.print_exc()

def analyze_prediction_errors():
    """分析預測錯誤的模式"""
    print("\n🔍 分析預測錯誤模式")
    print("=" * 80)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 分析錯誤預測的特徵
        cursor.execute("""
            SELECT 
                g.away_team || '@' || g.home_team as matchup,
                p.over_under_line,
                p.actual_total_runs,
                p.over_probability,
                p.under_probability,
                CASE 
                    WHEN p.actual_total_runs > p.over_under_line THEN 'Over'
                    ELSE 'Under'
                END as actual_result,
                CASE 
                    WHEN p.over_probability > 0.5 THEN 'Over'
                    ELSE 'Under'
                END as predicted_result,
                ABS(p.actual_total_runs - p.over_under_line) as line_difference
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE p.model_version = 'unified_v1.0'
            AND p.is_correct = 0
            AND p.actual_total_runs IS NOT NULL
            ORDER BY line_difference DESC
        """)
        
        errors = cursor.fetchall()
        
        if errors:
            print("❌ 錯誤預測分析:")
            print("比賽對戰        | 盤口 | 實際 | Over% | Under% | 實際結果 | 預測結果 | 差距")
            print("-" * 85)
            
            over_errors = 0
            under_errors = 0
            large_diff_errors = 0
            
            for matchup, line, actual, over_prob, under_prob, actual_result, pred_result, diff in errors[:10]:  # 只顯示前10個
                print(f"{matchup:15s} | {line:4.1f} | {actual:4.0f} | {over_prob:5.1f} | {under_prob:6.1f} | {actual_result:8s} | {pred_result:8s} | {diff:4.1f}")
                
                if pred_result == 'Over':
                    over_errors += 1
                else:
                    under_errors += 1
                
                if diff > 2.0:
                    large_diff_errors += 1
            
            print(f"\n📈 錯誤模式分析:")
            print(f"   總錯誤數: {len(errors)}")
            print(f"   Over預測錯誤: {over_errors}")
            print(f"   Under預測錯誤: {under_errors}")
            print(f"   大幅偏差(>2分): {large_diff_errors}")
            
            # 分析盤口範圍的準確性
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN p.over_under_line < 8.0 THEN '低分盤(<8)'
                        WHEN p.over_under_line >= 8.0 AND p.over_under_line < 9.0 THEN '中分盤(8-9)'
                        ELSE '高分盤(>=9)'
                    END as line_range,
                    COUNT(*) as total,
                    COUNT(CASE WHEN p.is_correct = 1 THEN 1 END) as correct
                FROM predictions p
                WHERE p.model_version = 'unified_v1.0'
                AND p.is_correct IS NOT NULL
                GROUP BY 
                    CASE 
                        WHEN p.over_under_line < 8.0 THEN '低分盤(<8)'
                        WHEN p.over_under_line >= 8.0 AND p.over_under_line < 9.0 THEN '中分盤(8-9)'
                        ELSE '高分盤(>=9)'
                    END
            """)
            
            range_results = cursor.fetchall()
            
            print(f"\n📊 不同盤口範圍的準確性:")
            for range_name, total, correct in range_results:
                accuracy = (correct / total * 100) if total > 0 else 0
                print(f"   {range_name}: {correct}/{total} ({accuracy:.1f}%)")
        
        else:
            print("✅ 沒有錯誤預測記錄")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")

def generate_improvement_suggestions():
    """生成改進建議"""
    print("\n💡 模型改進建議")
    print("=" * 80)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 計算整體統計
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN is_correct = 1 THEN 1 END) as correct,
                AVG(over_under_line) as avg_line,
                AVG(actual_total_runs) as avg_actual,
                COUNT(CASE WHEN actual_total_runs > over_under_line THEN 1 END) as actual_overs,
                COUNT(CASE WHEN over_probability > 0.5 THEN 1 END) as predicted_overs
            FROM predictions 
            WHERE model_version = 'unified_v1.0'
            AND is_correct IS NOT NULL
        """)
        
        stats = cursor.fetchone()
        
        if stats and stats[0] > 0:
            total, correct, avg_line, avg_actual, actual_overs, predicted_overs = stats
            accuracy = correct / total * 100
            actual_over_rate = actual_overs / total * 100
            predicted_over_rate = predicted_overs / total * 100
            
            print(f"📈 當前模型表現:")
            print(f"   整體準確率: {accuracy:.1f}%")
            print(f"   平均盤口: {avg_line:.1f}")
            print(f"   平均實際得分: {avg_actual:.1f}")
            print(f"   實際Over率: {actual_over_rate:.1f}%")
            print(f"   預測Over率: {predicted_over_rate:.1f}%")
            
            print(f"\n🎯 改進建議:")
            
            if accuracy < 55:
                print("   1. 準確率偏低，建議:")
                print("      - 增加更多特徵變量（天氣、球員狀態等）")
                print("      - 調整模型參數")
                print("      - 考慮使用集成學習方法")
            
            if abs(actual_over_rate - predicted_over_rate) > 10:
                print("   2. Over/Under預測偏差較大，建議:")
                print("      - 重新校準概率閾值")
                print("      - 分析Over/Under的不同特徵重要性")
            
            if avg_actual - avg_line > 0.5:
                print("   3. 實際得分普遍高於盤口，建議:")
                print("      - 考慮調整盤口預期")
                print("      - 分析高分比賽的特殊因素")
            elif avg_line - avg_actual > 0.5:
                print("   4. 實際得分普遍低於盤口，建議:")
                print("      - 分析低分比賽的防守因素")
                print("      - 考慮投手表現的權重")
            
            print("   5. 通用改進方向:")
            print("      - 增加更多歷史數據進行訓練")
            print("      - 實施動態特徵選擇")
            print("      - 考慮球隊近期狀態變化")
            print("      - 加入更精確的投手對戰分析")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 生成建議失敗: {e}")

if __name__ == '__main__':
    verify_all_real_odds()
    analyze_prediction_errors()
    generate_improvement_suggestions()
    
    print("\n" + "=" * 80)
    print("🎯 驗證完成！")
    print("✅ 所有預測數據都使用真實的bet365博彩盤口")
    print("📊 可以基於這些真實數據進行模型校正和改進")
    print("=" * 80)
