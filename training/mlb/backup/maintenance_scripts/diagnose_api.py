#!/usr/bin/env python3
"""
診斷MLB API連接和數據獲取問題
"""

import requests
import json
from datetime import date, datetime, timedelta
import logging
from app import create_app
from models.database import Game, db

def test_direct_api():
    """直接測試MLB API"""
    print("🔍 直接測試MLB Stats API...")
    
    base_url = "https://statsapi.mlb.com/api/v1"
    
    # 測試基本連接
    try:
        response = requests.get(f"{base_url}/teams", params={'sportId': 1}, timeout=10)
        print(f"✅ API連接正常: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            teams_count = len(data.get('teams', []))
            print(f"📊 獲取到 {teams_count} 支球隊")
        
    except Exception as e:
        print(f"❌ API連接失敗: {e}")
        return False
    
    return True

def test_schedule_api():
    """測試比賽日程API"""
    print("\n📅 測試比賽日程API...")
    
    base_url = "https://statsapi.mlb.com/api/v1"
    test_dates = [
        date(2025, 6, 24),
        date(2025, 6, 23),
        date(2025, 6, 22)
    ]
    
    for test_date in test_dates:
        try:
            date_str = test_date.strftime('%Y-%m-%d')
            url = f"{base_url}/schedule"
            params = {
                'sportId': 1,
                'date': date_str,
                'hydrate': 'game(content(editorial(recap))),decisions,person,probablePitcher,stats,homeRuns,previousPlay,game(content(media(epg))),game(content(highlights(highlights(items))))'
            }
            
            response = requests.get(url, params=params, timeout=15)
            print(f"📅 {date_str}: 狀態碼 {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                dates_data = data.get('dates', [])
                
                if dates_data:
                    games = dates_data[0].get('games', [])
                    print(f"   找到 {len(games)} 場比賽")
                    
                    for game in games[:3]:  # 只顯示前3場
                        away_team = game.get('teams', {}).get('away', {}).get('team', {}).get('abbreviation', 'N/A')
                        home_team = game.get('teams', {}).get('home', {}).get('team', {}).get('abbreviation', 'N/A')
                        status = game.get('status', {}).get('detailedState', 'N/A')
                        game_id = game.get('gamePk', 'N/A')
                        
                        print(f"   🏟️ {away_team} @ {home_team} ({status}) - ID: {game_id}")
                        
                        # 檢查比分
                        if status in ['Final', 'Game Over']:
                            away_score = game.get('teams', {}).get('away', {}).get('score')
                            home_score = game.get('teams', {}).get('home', {}).get('score')
                            print(f"      比分: {away_score}-{home_score}")
                else:
                    print(f"   ⚠️ 沒有找到比賽數據")
            else:
                print(f"   ❌ API請求失敗: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")

def test_game_detail_api():
    """測試比賽詳情API"""
    print("\n🎯 測試比賽詳情API...")
    
    app = create_app()
    with app.app_context():
        # 獲取一個最近的比賽ID
        recent_game = Game.query.filter(
            Game.date >= date(2025, 6, 20)
        ).first()
        
        if not recent_game:
            print("❌ 沒有找到最近的比賽記錄")
            return
        
        game_id = recent_game.game_id
        print(f"🎯 測試比賽ID: {game_id}")
        
        base_url = "https://statsapi.mlb.com/api/v1"
        
        try:
            # 測試比賽基本信息
            url = f"{base_url}/game/{game_id}"
            response = requests.get(url, timeout=15)
            print(f"📊 比賽基本信息: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                game_data = data.get('gameData', {})
                live_data = data.get('liveData', {})
                
                status = game_data.get('status', {}).get('detailedState', 'N/A')
                print(f"   狀態: {status}")
                
                # 檢查比分
                if 'linescore' in live_data:
                    linescore = live_data['linescore']
                    away_runs = linescore.get('teams', {}).get('away', {}).get('runs')
                    home_runs = linescore.get('teams', {}).get('home', {}).get('runs')
                    print(f"   比分: {away_runs}-{home_runs}")
            
            # 測試Box Score
            url = f"{base_url}/game/{game_id}/boxscore"
            response = requests.get(url, timeout=15)
            print(f"📋 Box Score: {response.status_code}")
            
        except Exception as e:
            print(f"❌ 比賽詳情API錯誤: {e}")

def check_database_status():
    """檢查數據庫狀態"""
    print("\n💾 檢查數據庫狀態...")
    
    app = create_app()
    with app.app_context():
        try:
            # 檢查最近幾天的比賽
            recent_dates = [
                date(2025, 6, 24),
                date(2025, 6, 23),
                date(2025, 6, 22)
            ]
            
            for check_date in recent_dates:
                games = Game.query.filter(Game.date == check_date).all()
                completed_games = [g for g in games if g.game_status == 'completed']
                
                print(f"📅 {check_date}: {len(games)} 場比賽, {len(completed_games)} 場已完成")
                
                if games:
                    for game in games[:3]:  # 只顯示前3場
                        status_icon = "✅" if game.game_status == 'completed' else "⏳"
                        score_info = f"({game.away_score}-{game.home_score})" if game.game_status == 'completed' else ""
                        print(f"   {status_icon} {game.away_team} @ {game.home_team} {score_info}")
                        
        except Exception as e:
            print(f"❌ 數據庫檢查錯誤: {e}")

def main():
    print("🏀 MLB數據獲取診斷工具")
    print("=" * 50)
    
    # 設置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 1. 測試API連接
    if not test_direct_api():
        print("❌ API連接失敗，請檢查網絡連接")
        return
    
    # 2. 測試比賽日程
    test_schedule_api()
    
    # 3. 測試比賽詳情
    test_game_detail_api()
    
    # 4. 檢查數據庫
    check_database_status()
    
    print("\n🎯 診斷完成！")
    print("如果API能正常獲取數據但程式無法下載，可能的原因：")
    print("1. 比賽狀態判斷邏輯問題")
    print("2. 數據解析錯誤")
    print("3. 數據庫寫入問題")
    print("4. API請求頻率限制")

if __name__ == "__main__":
    main()
