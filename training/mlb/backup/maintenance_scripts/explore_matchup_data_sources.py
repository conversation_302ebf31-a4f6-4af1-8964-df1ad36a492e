#!/usr/bin/env python3
"""
探索投手對打者對戰數據來源
"""

import requests
import json
from datetime import date, timed<PERSON>ta

def explore_matchup_data_sources():
    """探索不同的對戰數據來源"""
    print("=" * 80)
    print("🔍 探索投手對打者對戰數據來源")
    print("=" * 80)
    
    base_url = "https://statsapi.mlb.com/api/v1"
    
    # 測試不同的API端點
    test_endpoints = [
        {
            'name': '球員統計 - 對戰分割',
            'url_template': f"{base_url}/people/{{player_id}}/stats",
            'params': {
                'stats': 'vsPlayer',
                'season': 2024
            }
        },
        {
            'name': '球員統計 - 對球隊分割',
            'url_template': f"{base_url}/people/{{player_id}}/stats",
            'params': {
                'stats': 'vsTeam',
                'season': 2024
            }
        },
        {
            'name': '球員統計 - 情境分割',
            'url_template': f"{base_url}/people/{{player_id}}/stats",
            'params': {
                'stats': 'situational',
                'season': 2024
            }
        },
        {
            'name': '比賽日誌',
            'url_template': f"{base_url}/people/{{player_id}}/stats",
            'params': {
                'stats': 'gameLog',
                'season': 2024
            }
        }
    ]
    
    # 使用知名球員ID進行測試
    test_players = [
        {'id': 592789, 'name': 'Jacob deGrom', 'position': 'Pitcher'},
        {'id': 545361, 'name': 'Mike Trout', 'position': 'Batter'},
        {'id': 660271, 'name': 'Shane Bieber', 'position': 'Pitcher'}
    ]
    
    for endpoint in test_endpoints:
        print(f"\n📡 測試: {endpoint['name']}")
        print("-" * 50)
        
        for player in test_players:
            try:
                url = endpoint['url_template'].format(player_id=player['id'])
                response = requests.get(url, params=endpoint['params'], timeout=30)
                
                print(f"\n{player['name']} ({player['position']}):")
                print(f"  狀態碼: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"  響應大小: {len(response.text)} 字符")
                    
                    if 'stats' in data:
                        stats = data['stats']
                        print(f"  統計組數: {len(stats)}")
                        
                        for i, stat_group in enumerate(stats):
                            group_type = stat_group.get('type', {}).get('displayName', 'Unknown')
                            print(f"    組 {i+1}: {group_type}")
                            
                            if 'splits' in stat_group:
                                splits = stat_group['splits']
                                print(f"      分割數: {len(splits)}")
                                
                                # 顯示前幾個分割的詳細信息
                                for j, split in enumerate(splits[:3]):
                                    if 'player' in split:
                                        opponent = split['player'].get('fullName', 'Unknown')
                                        print(f"        分割 {j+1}: vs {opponent}")
                                    elif 'team' in split:
                                        opponent = split['team'].get('name', 'Unknown')
                                        print(f"        分割 {j+1}: vs {opponent}")
                                    elif 'season' in split:
                                        season = split['season']
                                        print(f"        分割 {j+1}: {season}賽季")
                                    
                                    if 'stat' in split:
                                        stat_data = split['stat']
                                        # 顯示一些關鍵統計
                                        key_stats = []
                                        if 'atBats' in stat_data:
                                            key_stats.append(f"AB:{stat_data['atBats']}")
                                        if 'hits' in stat_data:
                                            key_stats.append(f"H:{stat_data['hits']}")
                                        if 'inningsPitched' in stat_data:
                                            key_stats.append(f"IP:{stat_data['inningsPitched']}")
                                        if 'era' in stat_data:
                                            key_stats.append(f"ERA:{stat_data['era']}")
                                        
                                        if key_stats:
                                            print(f"          統計: {', '.join(key_stats)}")
                else:
                    print(f"  ❌ 請求失敗: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 錯誤: {e}")
    
    # 測試特定比賽的詳細數據
    print(f"\n📡 測試: 特定比賽的打席數據")
    print("-" * 50)
    
    try:
        # 獲取最近的比賽
        yesterday = date.today() - timedelta(days=1)
        date_str = yesterday.strftime('%Y-%m-%d')
        
        schedule_url = f"{base_url}/schedule"
        schedule_params = {'sportId': 1, 'date': date_str}
        schedule_response = requests.get(schedule_url, params=schedule_params, timeout=30)
        
        if schedule_response.status_code == 200:
            schedule_data = schedule_response.json()
            
            if schedule_data['dates'] and schedule_data['dates'][0]['games']:
                game_id = schedule_data['dates'][0]['games'][0]['gamePk']
                
                # 獲取比賽的play-by-play數據
                playbyplay_url = f"{base_url}/game/{game_id}/playByPlay"
                playbyplay_response = requests.get(playbyplay_url, timeout=30)
                
                print(f"比賽ID: {game_id}")
                print(f"Play-by-Play狀態碼: {playbyplay_response.status_code}")
                
                if playbyplay_response.status_code == 200:
                    playbyplay_data = playbyplay_response.json()
                    print(f"響應大小: {len(playbyplay_response.text)} 字符")
                    
                    if 'allPlays' in playbyplay_data:
                        plays = playbyplay_data['allPlays']
                        print(f"總打席數: {len(plays)}")
                        
                        # 分析前幾個打席
                        for i, play in enumerate(plays[:5]):
                            if 'matchup' in play:
                                matchup = play['matchup']
                                batter = matchup.get('batter', {}).get('fullName', 'Unknown')
                                pitcher = matchup.get('pitcher', {}).get('fullName', 'Unknown')
                                
                                print(f"  打席 {i+1}: {batter} vs {pitcher}")
                                
                                if 'result' in play:
                                    result = play['result']
                                    event = result.get('event', 'Unknown')
                                    description = result.get('description', '')
                                    print(f"    結果: {event} - {description}")
                
    except Exception as e:
        print(f"❌ 比賽數據測試失敗: {e}")

def main():
    """主函數"""
    explore_matchup_data_sources()

if __name__ == "__main__":
    main()
