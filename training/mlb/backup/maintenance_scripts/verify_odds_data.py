#!/usr/bin/env python3
"""
驗證下載的賠率數據質量
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from models.database import db, BettingOdds, Game
from app import create_app
from sqlalchemy import func

def verify_odds_data():
    """驗證賠率數據質量"""
    print("=== 驗證賠率數據質量 ===")
    
    app = create_app()
    
    with app.app_context():
        # 統計總記錄數
        total_odds = BettingOdds.query.count()
        print(f"總賠率記錄數: {total_odds}")
        
        # 按市場類型統計
        totals_count = BettingOdds.query.filter_by(market_type='totals').count()
        spreads_count = BettingOdds.query.filter_by(market_type='spreads').count()
        
        print(f"大小分記錄: {totals_count}")
        print(f"讓分記錄: {spreads_count}")
        
        # 檢查大小分數據範圍
        print(f"\n=== 大小分數據分析 ===")
        totals_records = BettingOdds.query.filter_by(market_type='totals').all()
        
        if totals_records:
            total_points = [r.total_point for r in totals_records if r.total_point is not None]
            if total_points:
                print(f"大小分範圍: {min(total_points)} - {max(total_points)}")
                print(f"平均大小分: {sum(total_points)/len(total_points):.2f}")
                
                # 顯示一些具體例子
                print(f"\n=== 前10個大小分記錄 ===")
                for i, record in enumerate(totals_records[:10]):
                    # 查找對應的比賽
                    game = Game.query.filter_by(game_id=record.game_id).first()
                    if game:
                        print(f"{i+1}. {game.date}: {game.away_team} @ {game.home_team} - 大小分: {record.total_point}")
                    else:
                        print(f"{i+1}. Game ID {record.game_id} - 大小分: {record.total_point}")
        
        # 檢查異常數據
        print(f"\n=== 異常數據檢查 ===")
        
        # 檢查過低的大小分 (< 5.0)
        low_totals = BettingOdds.query.filter(
            BettingOdds.market_type == 'totals',
            BettingOdds.total_point < 5.0
        ).all()
        
        print(f"過低大小分記錄 (< 5.0): {len(low_totals)}")
        for record in low_totals[:5]:  # 只顯示前5個
            game = Game.query.filter_by(game_id=record.game_id).first()
            if game:
                print(f"  {game.date}: {game.away_team} @ {game.home_team} - 大小分: {record.total_point}")
        
        # 檢查過高的大小分 (> 15.0)
        high_totals = BettingOdds.query.filter(
            BettingOdds.market_type == 'totals',
            BettingOdds.total_point > 15.0
        ).all()
        
        print(f"過高大小分記錄 (> 15.0): {len(high_totals)}")
        for record in high_totals[:5]:  # 只顯示前5個
            game = Game.query.filter_by(game_id=record.game_id).first()
            if game:
                print(f"  {game.date}: {game.away_team} @ {game.home_team} - 大小分: {record.total_point}")
        
        # 檢查正常範圍的大小分 (7.0 - 12.0)
        normal_totals = BettingOdds.query.filter(
            BettingOdds.market_type == 'totals',
            BettingOdds.total_point >= 7.0,
            BettingOdds.total_point <= 12.0
        ).count()
        
        print(f"正常範圍大小分記錄 (7.0-12.0): {normal_totals}")
        
        # 按日期統計
        print(f"\n=== 按日期統計 ===")
        date_stats = db.session.query(
            Game.date,
            func.count(BettingOdds.id).label('odds_count')
        ).join(
            BettingOdds, Game.game_id == BettingOdds.game_id
        ).group_by(Game.date).order_by(Game.date.desc()).limit(10).all()
        
        for date_stat in date_stats:
            print(f"  {date_stat.date}: {date_stat.odds_count} 條賠率記錄")

if __name__ == "__main__":
    verify_odds_data()
