#!/usr/bin/env python3
"""
簡化版智能預測系統
解決模型特徵維度不匹配和數據庫問題
"""

import logging
from datetime import date
from typing import Dict, Tuple, Optional
import numpy as np

logger = logging.getLogger(__name__)

class SimpleIntelligentPredictor:
    """簡化版智能預測器 - 避免複雜模型依賴"""
    
    def __init__(self):
        self.pitcher_stats_cache = {}
        
    def predict_game_intelligent(self, home_team: str, away_team: str, 
                               game_date: date, game_id: str = None) -> Dict:
        """
        智能預測比賽 - 簡化版本
        
        Args:
            home_team: 主隊代碼
            away_team: 客隊代碼  
            game_date: 比賽日期
            game_id: 比賽ID
            
        Returns:
            包含預測結果和策略說明的字典
        """
        try:
            logger.info(f"🧠 開始簡化智能預測: {away_team} @ {home_team} ({game_date})")
            
            # 1. 分析投手質量 (簡化版)
            pitcher_analysis = self._analyze_pitcher_quality_simple(
                home_team, away_team, game_date, game_id
            )
            
            # 2. 根據投手質量選擇預測策略
            strategy = self._determine_prediction_strategy(pitcher_analysis)
            
            # 3. 執行簡化預測
            prediction = self._execute_simple_prediction(
                home_team, away_team, game_date, strategy
            )
            
            # 4. 組合結果
            result = {
                **prediction,
                'pitcher_analysis': pitcher_analysis,
                'strategy': strategy,
                'explanation': self._generate_explanation(pitcher_analysis, strategy, prediction)
            }
            
            logger.info(f"✅ 簡化智能預測完成: {strategy['name']} - {result['predicted_away_score']:.1f}-{result['predicted_home_score']:.1f}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 簡化智能預測失敗: {e}")
            # 返回保守預測
            return self._get_fallback_prediction(home_team, away_team)

    def _analyze_pitcher_from_boxscore(self, game_id: str, home_team: str, away_team: str) -> Dict:
        """使用BoxScore分析投手質量"""
        try:
            from models.boxscore_pitcher_extractor import boxscore_pitcher_extractor

            # 使用BoxScore投手提取器分析投手對戰
            analysis = boxscore_pitcher_extractor.analyze_pitcher_matchup(game_id, home_team, away_team)

            if analysis and analysis.get('home_pitcher') and analysis.get('away_pitcher'):
                logger.info(f"✅ 使用BoxScore分析投手: {analysis['home_pitcher']['name']} vs {analysis['away_pitcher']['name']}")
                return analysis

            return None

        except Exception as e:
            logger.warning(f"⚠️ BoxScore投手分析失敗: {e}")
            return None

    def _analyze_pitcher_quality_simple(self, home_team: str, away_team: str,
                                      game_date: date, game_id: str = None) -> Dict:
        """簡化版投手質量分析"""
        try:
            # 優先使用BoxScore投手提取器
            if game_id:
                analysis = self._analyze_pitcher_from_boxscore(game_id, home_team, away_team)
                if analysis:
                    return analysis

            # 如果BoxScore方法失敗，使用原來的方法
            home_pitcher_info = self._get_pitcher_info_simple(home_team, game_date, game_id)
            away_pitcher_info = self._get_pitcher_info_simple(away_team, game_date, game_id, is_home=False)
            
            # 投手質量分類
            def classify_pitcher_strength(era: float, quality: float) -> str:
                if era <= 2.50 and quality >= 80:
                    return "王牌"
                elif era <= 3.50 and quality >= 70:
                    return "優秀"
                elif era <= 4.50 and quality >= 50:
                    return "普通"
                else:
                    return "弱勢"
            
            home_strength = classify_pitcher_strength(home_pitcher_info['era'], home_pitcher_info['quality'])
            away_strength = classify_pitcher_strength(away_pitcher_info['era'], away_pitcher_info['quality'])
            
            # 對戰類型分析
            matchup_type = self._determine_matchup_type(home_strength, away_strength)
            
            analysis = {
                'home_pitcher': {
                    'name': home_pitcher_info['name'],
                    'era': home_pitcher_info['era'],
                    'quality': home_pitcher_info['quality'],
                    'strength': home_strength
                },
                'away_pitcher': {
                    'name': away_pitcher_info['name'],
                    'era': away_pitcher_info['era'],
                    'quality': away_pitcher_info['quality'],
                    'strength': away_strength
                },
                'matchup_type': matchup_type,
                'average_era': (home_pitcher_info['era'] + away_pitcher_info['era']) / 2,
                'average_quality': (home_pitcher_info['quality'] + away_pitcher_info['quality']) / 2
            }
            
            logger.info(f"📊 簡化投手分析: {home_pitcher_info['name']}({home_strength}) vs {away_pitcher_info['name']}({away_strength}) - {matchup_type}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 簡化投手質量分析失敗: {e}")
            # 返回默認分析
            return self._get_default_pitcher_analysis()
    
    def _get_pitcher_info_simple(self, team: str, game_date: date, game_id: str = None, is_home: bool = True) -> Dict:
        """簡化版獲取投手信息"""
        try:
            from models.database import db, Game, PlayerStats, BoxScore, PlayerGameStats, Player, Team

            # 方法1: 嘗試從PlayerGameStats獲取該比賽的第一個投手（先發投手）
            if game_id:
                pitcher_info = self._get_starting_pitcher_from_game_stats(game_id, team, is_home)
                if pitcher_info:
                    return pitcher_info

            # 方法2: 嘗試從最近比賽找該球隊的常用投手
            recent_pitcher = self._get_recent_team_pitcher(team, is_home)
            if recent_pitcher:
                return recent_pitcher

            # 方法3: 如果找不到具體投手，使用球隊平均
            team_era = self._get_team_average_era(team)
            return {
                'name': f'{team} 投手',
                'era': team_era,
                'quality': self._era_to_quality(team_era)
            }

        except Exception as e:
            logger.warning(f"⚠️ 獲取投手信息失敗: {e}")
            return {
                'name': f'{team} 投手',
                'era': 4.50,
                'quality': 50.0
            }
    
    def _get_starting_pitcher_from_game_stats(self, game_id: str, team: str, is_home: bool) -> Dict:
        """從PlayerGameStats獲取該比賽的先發投手（第一個投手）"""
        try:
            from models.database import db, PlayerGameStats, Player, Team

            # 查找該球隊在該比賽中的投手
            team_obj = Team.query.filter_by(team_code=team).first()
            if not team_obj:
                logger.warning(f"⚠️ 找不到球隊: {team}")
                return None

            # 查找該比賽中該球隊的投手，按投球局數排序（先發投手通常投球局數最多）
            pitchers = db.session.query(PlayerGameStats, Player).join(Player).filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.team_id == team_obj.team_id,
                PlayerGameStats.position == 'P',
                PlayerGameStats.innings_pitched > 0
            ).order_by(PlayerGameStats.innings_pitched.desc()).all()

            if pitchers:
                # 取第一個投手（投球局數最多的，通常是先發投手）
                pitcher_stats, pitcher_player = pitchers[0]

                # 計算該場比賽的ERA
                if pitcher_stats.innings_pitched > 0:
                    game_era = (pitcher_stats.earned_runs * 9.0) / pitcher_stats.innings_pitched
                else:
                    game_era = 0.0

                # 嘗試獲取該投手的季度統計
                season_stats = self._get_pitcher_stats_from_db(pitcher_player.player_name)
                if season_stats:
                    era = season_stats.get('era', game_era)
                    quality = self._calculate_pitcher_quality(season_stats)
                else:
                    era = game_era if game_era > 0 else 4.50
                    quality = self._era_to_quality(era)

                logger.info(f"✅ 從比賽統計找到 {team} 先發投手: {pitcher_player.full_name} (ERA: {era:.2f}, 投球局數: {pitcher_stats.innings_pitched})")

                return {
                    'name': pitcher_player.full_name,
                    'era': era,
                    'quality': quality
                }

            logger.warning(f"⚠️ 在比賽 {game_id} 中找不到 {team} 的投手統計")
            return None

        except Exception as e:
            logger.warning(f"⚠️ 從比賽統計獲取先發投手失敗: {e}")
            return None

    def _get_pitcher_stats_from_db(self, pitcher_name: str) -> Dict:
        """從數據庫獲取投手統計"""
        try:
            from models.database import PlayerStats, Player

            # 先嘗試通過Player表查找球員ID
            player = Player.query.filter(
                Player.full_name.ilike(f'%{pitcher_name}%')
            ).first()

            if player:
                # 使用球員ID查找統計
                pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.player_id == player.player_id,
                    PlayerStats.position == 'P'
                ).order_by(PlayerStats.season.desc()).first()
            else:
                # 如果找不到球員，嘗試直接通過名字模糊匹配
                pitcher_stats = PlayerStats.query.filter(
                    PlayerStats.player_name.ilike(f'%{pitcher_name}%') if hasattr(PlayerStats, 'player_name') else True,
                    PlayerStats.position == 'P'
                ).order_by(PlayerStats.season.desc()).first()

            if pitcher_stats:
                return {
                    'era': float(pitcher_stats.era or 4.50),
                    'whip': float(pitcher_stats.whip or 1.30),
                    'strikeouts': int(pitcher_stats.strikeouts or 0),
                    'innings_pitched': float(pitcher_stats.innings_pitched or 0)
                }

            return None

        except Exception as e:
            logger.warning(f"⚠️ 從數據庫獲取投手統計失敗: {e}")
            return None
    
    def _calculate_pitcher_quality(self, stats: Dict) -> float:
        """計算投手質量分數"""
        try:
            era = stats.get('era', 4.50)
            whip = stats.get('whip', 1.30)
            
            # 簡化的質量計算
            era_score = max(0, 100 - (era - 2.0) * 20)  # ERA 2.0 = 100分，每增加1.0減20分
            whip_score = max(0, 100 - (whip - 1.0) * 50)  # WHIP 1.0 = 100分，每增加0.1減5分
            
            quality = (era_score + whip_score) / 2
            return min(100, max(0, quality))
            
        except Exception as e:
            logger.warning(f"⚠️ 計算投手質量失敗: {e}")
            return 50.0
    
    def _get_recent_team_pitcher(self, team: str, is_home: bool) -> Dict:
        """獲取球隊最近的投手信息"""
        try:
            from models.database import db, Game, PlayerGameStats, Player, Team

            # 查找該球隊最近的比賽
            team_obj = Team.query.filter_by(team_code=team).first()
            if not team_obj:
                return None

            # 查找該球隊最近5場比賽
            recent_games = Game.query.filter(
                (Game.home_team == team) if is_home else (Game.away_team == team),
                Game.game_status == 'completed'
            ).order_by(Game.date.desc()).limit(5).all()

            for game in recent_games:
                # 從每場比賽中找先發投手
                pitcher_info = self._get_starting_pitcher_from_game_stats(
                    game.game_id, team, is_home
                )
                if pitcher_info:
                    logger.info(f"✅ 從最近比賽找到 {team} 投手: {pitcher_info['name']}")
                    return pitcher_info

            logger.warning(f"⚠️ 找不到 {team} 最近的投手信息")
            return None

        except Exception as e:
            logger.warning(f"⚠️ 獲取最近投手失敗: {e}")
            return None

    def _get_team_average_era(self, team: str) -> float:
        """獲取球隊平均ERA"""
        try:
            from models.database import PlayerStats, Player, Team

            # 先獲取球隊ID
            team_obj = Team.query.filter_by(team_code=team).first()
            if not team_obj:
                logger.warning(f"⚠️ 找不到球隊: {team}")
                return 4.50

            # 查找該球隊的球員
            team_players = Player.query.filter_by(current_team_id=team_obj.team_id).all()
            if not team_players:
                logger.warning(f"⚠️ 找不到球隊 {team} 的球員")
                return 4.50

            # 獲取這些球員的投手統計
            player_ids = [p.player_id for p in team_players]
            team_pitchers = PlayerStats.query.filter(
                PlayerStats.player_id.in_(player_ids),
                PlayerStats.position == 'P',
                PlayerStats.era.isnot(None)
            ).all()

            if team_pitchers:
                total_era = sum(float(p.era) for p in team_pitchers)
                return total_era / len(team_pitchers)

            # 如果找不到，使用所有投手的平均ERA
            all_pitchers = PlayerStats.query.filter(
                PlayerStats.position == 'P',
                PlayerStats.era.isnot(None)
            ).limit(50).all()

            if all_pitchers:
                total_era = sum(float(p.era) for p in all_pitchers)
                return total_era / len(all_pitchers)

            return 4.50  # 默認ERA

        except Exception as e:
            logger.warning(f"⚠️ 獲取球隊平均ERA失敗: {e}")
            return 4.50
    
    def _era_to_quality(self, era: float) -> float:
        """ERA轉換為質量分數"""
        return max(0, 100 - (era - 2.0) * 20)
    
    def _determine_matchup_type(self, home_strength: str, away_strength: str) -> str:
        """確定對戰類型"""
        strength_order = {'王牌': 4, '優秀': 3, '普通': 2, '弱勢': 1}
        
        home_level = strength_order.get(home_strength, 2)
        away_level = strength_order.get(away_strength, 2)
        
        if home_level >= 3 and away_level >= 3:
            return "王牌對決"
        elif home_level <= 1 and away_level <= 1:
            return "打擊戰"
        elif abs(home_level - away_level) >= 2:
            return "強弱對戰"
        else:
            return "普通對戰"
    
    def _determine_prediction_strategy(self, pitcher_analysis: Dict) -> Dict:
        """根據投手質量確定預測策略"""
        matchup_type = pitcher_analysis['matchup_type']
        avg_era = pitcher_analysis['average_era']
        
        if matchup_type == "王牌對決":
            return {
                'name': '王牌對決策略',
                'type': 'low_scoring',
                'target_total': 7.0,
                'score_adjustment': -1.5,
                'confidence_boost': 0.1,
                'description': '兩位王牌投手對決，預期低分比賽'
            }
        elif matchup_type == "打擊戰":
            return {
                'name': '打擊戰策略',
                'type': 'high_scoring',
                'target_total': 12.0,
                'score_adjustment': 2.0,
                'confidence_boost': 0.1,
                'description': '兩位弱勢投手，預期高分打擊戰'
            }
        elif matchup_type == "強弱對戰":
            return {
                'name': '強弱對戰策略',
                'type': 'unbalanced',
                'target_total': 9.0,
                'score_adjustment': 0.5,
                'confidence_boost': 0.05,
                'description': '強弱投手對戰，分數不平衡'
            }
        else:
            return {
                'name': '標準策略',
                'type': 'standard',
                'target_total': 9.5,
                'score_adjustment': 0.0,
                'confidence_boost': 0.0,
                'description': '普通投手對戰，使用標準預測'
            }
    
    def _execute_simple_prediction(self, home_team: str, away_team: str, 
                                 game_date: date, strategy: Dict) -> Dict:
        """執行簡化預測"""
        try:
            # 基於球隊歷史表現的簡化預測
            base_prediction = self._get_team_based_prediction(home_team, away_team)
            
            # 根據策略調整預測
            adjusted_prediction = self._adjust_prediction_by_strategy(base_prediction, strategy)
            
            return adjusted_prediction
            
        except Exception as e:
            logger.error(f"❌ 執行簡化預測失敗: {e}")
            return self._get_fallback_prediction(home_team, away_team)
    
    def _get_team_based_prediction(self, home_team: str, away_team: str) -> Dict:
        """基於球隊的簡化預測"""
        try:
            from models.database import Game
            
            # 獲取兩隊最近的比賽表現
            home_recent_games = Game.query.filter(
                (Game.home_team == home_team) | (Game.away_team == home_team),
                Game.game_status == 'completed',
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).order_by(Game.date.desc()).limit(10).all()
            
            away_recent_games = Game.query.filter(
                (Game.home_team == away_team) | (Game.away_team == away_team),
                Game.game_status == 'completed',
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).order_by(Game.date.desc()).limit(10).all()
            
            # 計算平均得分
            home_avg_score = self._calculate_team_avg_score(home_recent_games, home_team)
            away_avg_score = self._calculate_team_avg_score(away_recent_games, away_team)
            
            # 主場優勢
            home_advantage = 0.3
            
            return {
                'predicted_home_score': home_avg_score + home_advantage,
                'predicted_away_score': away_avg_score,
                'total_runs': home_avg_score + away_avg_score + home_advantage,
                'confidence': 0.6,
                'home_win_probability': 0.52
            }
            
        except Exception as e:
            logger.warning(f"⚠️ 球隊預測失敗，使用默認值: {e}")
            return {
                'predicted_home_score': 5.0,
                'predicted_away_score': 4.5,
                'total_runs': 9.5,
                'confidence': 0.6,
                'home_win_probability': 0.52
            }
    
    def _calculate_team_avg_score(self, games, team) -> float:
        """計算球隊平均得分"""
        if not games:
            return 4.5
        
        total_score = 0
        count = 0
        
        for game in games:
            if game.home_team == team:
                total_score += game.home_score or 0
            else:
                total_score += game.away_score or 0
            count += 1
        
        return total_score / count if count > 0 else 4.5
    
    def _adjust_prediction_by_strategy(self, base_prediction: Dict, strategy: Dict) -> Dict:
        """根據策略調整預測結果"""
        try:
            home_score = base_prediction['predicted_home_score']
            away_score = base_prediction['predicted_away_score']
            current_total = home_score + away_score
            target_total = strategy['target_total']
            adjustment = strategy['score_adjustment']
            
            if strategy['type'] == 'low_scoring':
                # 王牌對決 - 降低總分
                if current_total > target_total:
                    scale_factor = target_total / current_total
                    home_score *= scale_factor
                    away_score *= scale_factor
                home_score = max(2.0, home_score + adjustment)
                away_score = max(2.0, away_score + adjustment)
                
            elif strategy['type'] == 'high_scoring':
                # 打擊戰 - 提高總分
                if current_total < target_total:
                    scale_factor = target_total / current_total
                    home_score *= scale_factor
                    away_score *= scale_factor
                home_score = min(15.0, home_score + adjustment)
                away_score = min(15.0, away_score + adjustment)
                
            elif strategy['type'] == 'unbalanced':
                # 強弱對戰 - 調整平衡
                home_score += adjustment * 0.5
                away_score += adjustment * 0.5
            
            # 確保合理範圍
            home_score = max(1.0, min(20.0, round(home_score, 1)))
            away_score = max(1.0, min(20.0, round(away_score, 1)))
            
            # 調整信心度
            confidence = base_prediction.get('confidence', 0.6)
            confidence = min(0.95, confidence + strategy['confidence_boost'])
            
            return {
                'predicted_home_score': home_score,
                'predicted_away_score': away_score,
                'total_runs': home_score + away_score,
                'confidence': confidence,
                'home_win_probability': home_score / (home_score + away_score),
                'strategy_applied': strategy['name']
            }
            
        except Exception as e:
            logger.error(f"❌ 策略調整失敗: {e}")
            return base_prediction
    
    def _get_default_pitcher_analysis(self) -> Dict:
        """獲取默認投手分析"""
        return {
            'home_pitcher': {'name': '未知投手', 'era': 4.50, 'quality': 50.0, 'strength': '普通'},
            'away_pitcher': {'name': '未知投手', 'era': 4.50, 'quality': 50.0, 'strength': '普通'},
            'matchup_type': '普通對戰',
            'average_era': 4.50,
            'average_quality': 50.0
        }
    
    def _get_fallback_prediction(self, home_team: str, away_team: str) -> Dict:
        """獲取保守預測"""
        return {
            'predicted_home_score': 5.0,
            'predicted_away_score': 4.5,
            'total_runs': 9.5,
            'confidence': 0.6,
            'home_win_probability': 0.52,
            'pitcher_analysis': self._get_default_pitcher_analysis(),
            'strategy': {
                'name': '保守策略',
                'type': 'standard',
                'description': '使用保守預測'
            },
            'explanation': f'保守預測 {away_team} @ {home_team}: 4.5-5.0 (總分9.5)'
        }
    
    def _generate_explanation(self, pitcher_analysis: Dict, strategy: Dict, prediction: Dict) -> str:
        """生成預測說明"""
        home_pitcher = pitcher_analysis['home_pitcher']
        away_pitcher = pitcher_analysis['away_pitcher']
        
        explanation = f"""
🎯 簡化智能預測分析:

📊 投手分析:
• 主隊: {home_pitcher['name']} (ERA: {home_pitcher['era']:.2f}, 等級: {home_pitcher['strength']})
• 客隊: {away_pitcher['name']} (ERA: {away_pitcher['era']:.2f}, 等級: {away_pitcher['strength']})
• 對戰類型: {pitcher_analysis['matchup_type']}

🧠 預測策略: {strategy['name']}
• {strategy['description']}
• 目標總分: {strategy['target_total']}分

📈 預測結果:
• 比分: {prediction['predicted_away_score']:.1f} - {prediction['predicted_home_score']:.1f}
• 總分: {prediction['total_runs']:.1f}分
• 信心度: {prediction['confidence']:.1%}
        """.strip()
        
        return explanation
