#!/usr/bin/env python3
"""
超快速預測器
基於簡單投手邏輯的快速預測系統
"""

import logging
import time
from datetime import date
from typing import Dict, Optional

from models.simple_pitcher_manager import simple_pitcher_manager

logger = logging.getLogger(__name__)

class UltraFastPredictor:
    """超快速預測器"""
    
    def __init__(self):
        self.cache = {}
    
    def predict_game_ultra_fast(self, game_id: str, home_team: str, away_team: str, game_date: date = None) -> Dict:
        """
        超快速預測 - 基於簡單投手邏輯
        
        邏輯：
        1. 快速獲取先發投手
        2. 根據投手ERA判斷比賽類型
        3. 應用簡單預測公式
        4. 返回結果
        """
        start_time = time.time()
        
        try:
            # 檢查緩存
            cache_key = f"ultra_{game_id}_{home_team}_{away_team}"
            if cache_key in self.cache:
                result = self.cache[cache_key].copy()
                result['prediction_time'] = time.time() - start_time
                result['from_cache'] = True
                return result
            
            logger.info(f"⚡ 超快速預測: {away_team} @ {home_team} ({game_id})")
            
            # 1. 獲取先發投手
            home_pitcher, away_pitcher = simple_pitcher_manager.get_starting_pitchers_simple(
                game_id, home_team, away_team
            )
            
            # 2. 執行簡單預測邏輯
            prediction = simple_pitcher_manager.predict_game_simple_logic(
                home_pitcher, away_pitcher
            )
            
            # 3. 添加額外信息
            prediction.update({
                'game_id': game_id,
                'home_team': home_team,
                'away_team': away_team,
                'prediction_time': time.time() - start_time,
                'predictor': 'ultra_fast',
                'from_cache': False
            })
            
            # 4. 緩存結果
            self.cache[cache_key] = prediction.copy()
            
            logger.info(f"✅ 超快速預測完成: {prediction['scenario']} - {prediction['predicted_away_score']:.1f}:{prediction['predicted_home_score']:.1f} (耗時: {prediction['prediction_time']:.3f}秒)")
            
            return prediction
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ 超快速預測失敗: {e} (耗時: {elapsed_time:.3f}秒)")
            
            # 返回默認預測
            return {
                'game_id': game_id,
                'home_team': home_team,
                'away_team': away_team,
                'scenario': '預測失敗',
                'predicted_home_score': 5.0,
                'predicted_away_score': 4.0,
                'predicted_total': 9.0,
                'confidence': 0.50,
                'prediction_time': elapsed_time,
                'predictor': 'ultra_fast',
                'error': str(e),
                'from_cache': False
            }
    
    def predict_date_ultra_fast(self, target_date: date, exclude_postponed: bool = True) -> Dict:
        """
        超快速預測指定日期的所有比賽
        """
        start_time = time.time()
        
        try:
            from models.database import Game
            
            # 獲取該日期的比賽
            games_query = Game.query.filter_by(date=target_date)
            
            if exclude_postponed:
                games_query = games_query.filter(
                    ~Game.game_status.in_(['postponed', 'cancelled', 'rescheduled'])
                )
            
            games = games_query.all()
            
            if not games:
                return {
                    'success': False,
                    'error': f'找不到 {target_date} 的比賽',
                    'date': target_date.isoformat(),
                    'count': 0,
                    'predictions': [],
                    'elapsed_time': time.time() - start_time
                }
            
            logger.info(f"⚡ 超快速預測日期: {target_date} ({len(games)} 場比賽)")
            
            # 批量預測
            predictions = []
            for game in games:
                try:
                    prediction = self.predict_game_ultra_fast(
                        game.game_id, game.home_team, game.away_team, game.date
                    )
                    
                    # 添加比賽基本信息
                    prediction.update({
                        'date': game.date.isoformat(),
                        'time': game.time,
                        'venue': game.venue,
                        'game_status': game.game_status
                    })
                    
                    # 添加實際結果（如果有）
                    if game.home_score is not None and game.away_score is not None:
                        prediction.update({
                            'actual_home_score': game.home_score,
                            'actual_away_score': game.away_score,
                            'actual_total': game.home_score + game.away_score
                        })
                    
                    predictions.append(prediction)
                    
                except Exception as e:
                    logger.error(f"❌ 預測比賽 {game.game_id} 失敗: {e}")
                    continue
            
            elapsed_time = time.time() - start_time
            
            result = {
                'success': True,
                'date': target_date.isoformat(),
                'count': len(predictions),
                'predictions': predictions,
                'elapsed_time': elapsed_time,
                'average_time_per_game': elapsed_time / len(predictions) if predictions else 0
            }
            
            logger.info(f"✅ 超快速日期預測完成: {len(predictions)} 場比賽 (總耗時: {elapsed_time:.3f}秒, 平均: {result['average_time_per_game']:.3f}秒/場)")
            
            return result
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ 超快速日期預測失敗: {e}")
            
            return {
                'success': False,
                'error': str(e),
                'date': target_date.isoformat() if target_date else None,
                'count': 0,
                'predictions': [],
                'elapsed_time': elapsed_time
            }
    
    def get_pitcher_summary(self, game_id: str, home_team: str, away_team: str) -> Dict:
        """
        獲取投手摘要信息 - 用於快速查看
        """
        try:
            home_pitcher, away_pitcher = simple_pitcher_manager.get_starting_pitchers_simple(
                game_id, home_team, away_team
            )
            
            return {
                'game_id': game_id,
                'home_team': home_team,
                'away_team': away_team,
                'pitchers': {
                    'home': {
                        'name': home_pitcher['name'],
                        'era': home_pitcher['era'],
                        'level': simple_pitcher_manager._classify_pitcher_level(home_pitcher['era'])
                    },
                    'away': {
                        'name': away_pitcher['name'],
                        'era': away_pitcher['era'],
                        'level': simple_pitcher_manager._classify_pitcher_level(away_pitcher['era'])
                    }
                },
                'matchup_preview': self._get_matchup_preview(home_pitcher, away_pitcher)
            }
            
        except Exception as e:
            logger.error(f"❌ 獲取投手摘要失敗: {e}")
            return {
                'error': str(e),
                'game_id': game_id,
                'home_team': home_team,
                'away_team': away_team
            }
    
    def _get_matchup_preview(self, home_pitcher: Dict, away_pitcher: Dict) -> Dict:
        """獲取對戰預覽"""
        home_level = simple_pitcher_manager._classify_pitcher_level(home_pitcher['era'])
        away_level = simple_pitcher_manager._classify_pitcher_level(away_pitcher['era'])
        
        if home_level == "王牌" and away_level == "王牌":
            return {
                'type': '王牌對決',
                'description': '低分投手戰',
                'expected_total': '6-7分',
                'confidence': '高'
            }
        elif home_level == "弱勢" and away_level == "弱勢":
            return {
                'type': '打擊戰',
                'description': '高分比賽',
                'expected_total': '12-13分',
                'confidence': '高'
            }
        elif (home_level == "王牌" and away_level == "弱勢") or (home_level == "弱勢" and away_level == "王牌"):
            return {
                'type': '強弱對戰',
                'description': '不平衡比賽',
                'expected_total': '8-9分',
                'confidence': '中等'
            }
        else:
            return {
                'type': '普通對戰',
                'description': '標準比賽',
                'expected_total': '9-10分',
                'confidence': '中等'
            }
    
    def clear_cache(self):
        """清除緩存"""
        self.cache.clear()
        logger.info("🗑️ 超快速預測器緩存已清除")
    
    def get_cache_stats(self) -> Dict:
        """獲取緩存統計"""
        return {
            'cache_size': len(self.cache),
            'cached_games': list(self.cache.keys())
        }

# 創建全局實例
ultra_fast_predictor = UltraFastPredictor()
