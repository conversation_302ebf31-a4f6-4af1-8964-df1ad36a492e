#!/usr/bin/env python3
"""
校正預測器 v2.0 - 修復預測偏差
"""

import os
import sys
import json
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, date
import logging

# 添加項目路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from models.database import db, Game, Prediction, BettingOdds

logger = logging.getLogger(__name__)

class CalibratedPredictorV2:
    """校正預測器 v2.0"""
    
    def __init__(self, model_path='models/calibrated_v2.0'):
        self.model_path = model_path
        self.total_score_model = None
        self.home_score_model = None
        self.away_score_model = None
        self.scaler = None
        self.feature_columns = []
        self.calibration_factors = {}
        self.is_loaded = False
        
        # 嘗試載入模型
        self.load_model()
    
    def load_model(self):
        """載入校正模型"""
        try:
            if not os.path.exists(self.model_path):
                logger.warning(f"校正模型路徑不存在: {self.model_path}")
                return False
            
            # 載入模型文件
            self.total_score_model = joblib.load(f"{self.model_path}/total_score_model.pkl")
            self.home_score_model = joblib.load(f"{self.model_path}/home_score_model.pkl")
            self.away_score_model = joblib.load(f"{self.model_path}/away_score_model.pkl")
            self.scaler = joblib.load(f"{self.model_path}/scaler.pkl")
            
            # 載入配置
            with open(f"{self.model_path}/config.json", 'r') as f:
                config = json.load(f)
                self.feature_columns = config['feature_columns']
                self.calibration_factors = config['calibration_factors']
            
            self.is_loaded = True
            logger.info(f"✅ 校正模型 v2.0 載入成功")
            return True
            
        except Exception as e:
            logger.error(f"載入校正模型失敗: {e}")
            return False
    
    def create_features_for_prediction(self, game_data, original_prediction):
        """為單場比賽創建特徵"""
        try:
            # 基本特徵
            features = {
                'predicted_home_score': original_prediction.get('predicted_home_score', 5.0),
                'predicted_away_score': original_prediction.get('predicted_away_score', 5.0),
                'predicted_total': original_prediction.get('predicted_home_score', 5.0) + original_prediction.get('predicted_away_score', 5.0),
                'confidence': original_prediction.get('confidence', 0.5),
                'confidence_squared': original_prediction.get('confidence', 0.5) ** 2,
                'confidence_log': np.log(original_prediction.get('confidence', 0.5) + 0.01),
            }
            
            # 博彩盤口特徵
            betting_line = game_data.get('betting_line')
            if betting_line:
                features['has_betting_line'] = 1
                features['betting_line_filled'] = betting_line
                features['pred_vs_line'] = features['predicted_total'] - betting_line
                features['pred_vs_line_ratio'] = features['predicted_total'] / betting_line
            else:
                features['has_betting_line'] = 0
                features['betting_line_filled'] = features['predicted_total']
                features['pred_vs_line'] = 0
                features['pred_vs_line_ratio'] = 1.0
            
            # 隊伍特徵 (使用默認值)
            features['home_team_error'] = 3.0
            features['home_team_bias'] = 0.0
            features['away_team_error'] = 3.0
            features['away_team_bias'] = 0.0
            
            # 時間特徵
            game_date = game_data.get('date', date.today())
            if isinstance(game_date, str):
                game_date = datetime.strptime(game_date, '%Y-%m-%d').date()
            
            features['day_of_week'] = game_date.weekday()
            features['month'] = game_date.month
            features['days_since_start'] = (game_date - date(2025, 6, 1)).days
            
            # 模型版本特徵 (設置默認值)
            model_version = original_prediction.get('model_version', 'unified_v1.0')
            for col in self.feature_columns:
                if col.startswith('model_'):
                    features[col] = 1 if col == f'model_{model_version}' else 0
            
            return features
            
        except Exception as e:
            logger.error(f"創建特徵失敗: {e}")
            return None
    
    def predict_calibrated_scores(self, game_data, original_prediction):
        """預測校正後的得分"""
        if not self.is_loaded:
            logger.warning("校正模型未載入，返回原始預測")
            return original_prediction
        
        try:
            # 創建特徵
            features = self.create_features_for_prediction(game_data, original_prediction)
            if not features:
                return original_prediction
            
            # 準備特徵向量
            feature_vector = []
            for col in self.feature_columns:
                feature_vector.append(features.get(col, 0))
            
            # 轉換為numpy數組並標準化
            X = np.array(feature_vector).reshape(1, -1)
            X_scaled = self.scaler.transform(X)
            
            # 預測校正後的得分
            calibrated_total = self.total_score_model.predict(X_scaled)[0]
            calibrated_home = self.home_score_model.predict(X_scaled)[0]
            calibrated_away = self.away_score_model.predict(X_scaled)[0]
            
            # 應用信心度校正因子
            confidence = original_prediction.get('confidence', 0.5)
            calibration_factor = self.get_calibration_factor(confidence)
            
            if calibration_factor:
                calibrated_total -= calibration_factor.get('total_bias', 0)
                calibrated_home -= calibration_factor.get('home_bias', 0)
                calibrated_away -= calibration_factor.get('away_bias', 0)
            
            # 確保得分為正數且合理
            calibrated_total = max(2.0, min(25.0, calibrated_total))
            calibrated_home = max(0.0, min(20.0, calibrated_home))
            calibrated_away = max(0.0, min(20.0, calibrated_away))
            
            # 調整總分一致性
            predicted_sum = calibrated_home + calibrated_away
            if abs(predicted_sum - calibrated_total) > 2.0:
                # 按比例調整
                ratio = calibrated_total / predicted_sum if predicted_sum > 0 else 1.0
                calibrated_home *= ratio
                calibrated_away *= ratio
            
            result = {
                'predicted_home_score': round(calibrated_home, 1),
                'predicted_away_score': round(calibrated_away, 1),
                'predicted_total': round(calibrated_total, 1),
                'confidence': confidence,
                'model_version': 'calibrated_v2.0',
                'calibration_applied': True,
                'original_prediction': original_prediction
            }
            
            logger.info(f"校正預測: {original_prediction.get('predicted_away_score', 0):.1f}-{original_prediction.get('predicted_home_score', 0):.1f} → {calibrated_away:.1f}-{calibrated_home:.1f}")
            
            return result
            
        except Exception as e:
            logger.error(f"校正預測失敗: {e}")
            return original_prediction
    
    def get_calibration_factor(self, confidence):
        """根據信心度獲取校正因子"""
        if confidence <= 0.4:
            return self.calibration_factors.get('low', {})
        elif confidence <= 0.6:
            return self.calibration_factors.get('medium_low', {})
        elif confidence <= 0.8:
            return self.calibration_factors.get('medium_high', {})
        else:
            return self.calibration_factors.get('high', {})
    
    def predict_with_betting_line_analysis(self, game_data, original_prediction):
        """結合博彩盤口分析的預測"""
        calibrated_prediction = self.predict_calibrated_scores(game_data, original_prediction)
        
        # 添加大小分分析
        betting_line = game_data.get('betting_line')
        if betting_line:
            predicted_total = calibrated_prediction['predicted_total']
            
            # 計算超過盤口的概率
            if predicted_total > betting_line + 1.5:
                over_probability = 0.75
                recommendation = "大分"
            elif predicted_total > betting_line + 0.5:
                over_probability = 0.65
                recommendation = "大分"
            elif predicted_total < betting_line - 1.5:
                over_probability = 0.25
                recommendation = "小分"
            elif predicted_total < betting_line - 0.5:
                over_probability = 0.35
                recommendation = "小分"
            else:
                over_probability = 0.50
                recommendation = "中性"
            
            calibrated_prediction.update({
                'betting_line': betting_line,
                'over_probability': over_probability,
                'over_under_recommendation': recommendation,
                'predicted_vs_line': predicted_total - betting_line
            })
        
        return calibrated_prediction
    
    def batch_calibrate_predictions(self, predictions_data):
        """批量校正預測"""
        calibrated_results = []
        
        for game_data, original_prediction in predictions_data:
            calibrated = self.predict_with_betting_line_analysis(game_data, original_prediction)
            calibrated_results.append(calibrated)
        
        return calibrated_results
    
    def get_model_info(self):
        """獲取模型信息"""
        return {
            'model_version': 'calibrated_v2.0',
            'is_loaded': self.is_loaded,
            'features_count': len(self.feature_columns),
            'calibration_factors': self.calibration_factors,
            'description': '校正預測器 v2.0 - 修復系統性偏差，提高預測準確性'
        }

# 全局實例
_calibrated_predictor_v2 = None

def get_calibrated_predictor_v2():
    """獲取校正預測器實例"""
    global _calibrated_predictor_v2
    if _calibrated_predictor_v2 is None:
        _calibrated_predictor_v2 = CalibratedPredictorV2()
    return _calibrated_predictor_v2
