#!/usr/bin/env python3
"""
快速智能預測系統
優先從數據庫獲取數據，避免網絡請求，提高預測速度
"""

import logging
from datetime import date
from typing import Dict, Tuple, Optional
import time

logger = logging.getLogger(__name__)

class FastIntelligentPredictor:
    """快速智能預測器 - 優先使用數據庫數據"""
    
    def __init__(self):
        self.pitcher_cache = {}  # 投手信息緩存
        
    def predict_game_intelligent_fast(self, home_team: str, away_team: str, 
                                    game_date: date, game_id: str = None) -> Dict:
        """
        快速智能預測比賽 - 優先從數據庫獲取數據
        
        Args:
            home_team: 主隊代碼
            away_team: 客隊代碼  
            game_date: 比賽日期
            game_id: 比賽ID
            
        Returns:
            包含預測結果和策略說明的字典
        """
        start_time = time.time()
        
        try:
            logger.info(f"🚀 開始快速智能預測: {away_team} @ {home_team} ({game_date})")
            
            # 1. 快速分析投手質量（優先數據庫）
            pitcher_analysis = self._analyze_pitcher_quality_fast(
                home_team, away_team, game_date, game_id
            )
            
            # 2. 根據投手質量選擇預測策略
            strategy = self._determine_prediction_strategy(pitcher_analysis)
            
            # 3. 執行快速預測（基於數據庫數據）
            prediction = self._execute_fast_prediction(
                home_team, away_team, game_date, strategy
            )
            
            # 4. 組合結果
            result = {
                **prediction,
                'pitcher_analysis': pitcher_analysis,
                'strategy': strategy,
                'explanation': self._generate_explanation(pitcher_analysis, strategy, prediction),
                'prediction_time': round(time.time() - start_time, 2)
            }
            
            logger.info(f"✅ 快速智能預測完成: {strategy['name']} - {result['predicted_away_score']:.1f}-{result['predicted_home_score']:.1f} (耗時: {result['prediction_time']}秒)")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 快速智能預測失敗: {e}")
            # 返回保守預測
            return self._get_fallback_prediction(home_team, away_team, time.time() - start_time)
    
    def _analyze_pitcher_quality_fast(self, home_team: str, away_team: str, 
                                    game_date: date, game_id: str = None) -> Dict:
        """快速投手質量分析 - 優先數據庫"""
        try:
            # 方法1: 從BoxScore獲取投手信息（最快）
            if game_id:
                analysis = self._get_pitcher_from_boxscore_fast(game_id, home_team, away_team)
                if analysis:
                    return analysis
            
            # 方法2: 從數據庫獲取球隊最近投手信息
            analysis = self._get_pitcher_from_database_fast(home_team, away_team, game_date)
            if analysis:
                return analysis
            
            # 方法3: 使用球隊平均數據（最後選擇）
            return self._get_team_average_pitcher_analysis(home_team, away_team)
            
        except Exception as e:
            logger.error(f"❌ 快速投手質量分析失敗: {e}")
            return self._get_default_pitcher_analysis()
    
    def _get_pitcher_from_boxscore_fast(self, game_id: str, home_team: str, away_team: str) -> Optional[Dict]:
        """從BoxScore快速獲取投手信息"""
        try:
            from models.boxscore_pitcher_extractor import boxscore_pitcher_extractor
            
            # 檢查緩存
            cache_key = f"{game_id}_{home_team}_{away_team}"
            if cache_key in self.pitcher_cache:
                logger.info(f"📦 使用緩存的投手分析: {cache_key}")
                return self.pitcher_cache[cache_key]
            
            # 從BoxScore獲取
            analysis = boxscore_pitcher_extractor.analyze_pitcher_matchup(game_id, home_team, away_team)
            
            if analysis and analysis.get('home_pitcher') and analysis.get('away_pitcher'):
                # 緩存結果
                self.pitcher_cache[cache_key] = analysis
                logger.info(f"✅ 從BoxScore快速獲取投手: {analysis['home_pitcher']['name']} vs {analysis['away_pitcher']['name']}")
                return analysis
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ BoxScore快速獲取失敗: {e}")
            return None
    
    def _get_pitcher_from_database_fast(self, home_team: str, away_team: str, game_date: date) -> Optional[Dict]:
        """從數據庫快速獲取投手信息"""
        try:
            from models.database import db, PlayerStats, Team, Player
            
            # 獲取球隊ID
            home_team_obj = Team.query.filter_by(team_code=home_team).first()
            away_team_obj = Team.query.filter_by(team_code=away_team).first()
            
            if not home_team_obj or not away_team_obj:
                return None
            
            # 獲取球隊最佳投手（ERA最低的）
            home_pitcher = self._get_team_best_pitcher(home_team_obj.team_id)
            away_pitcher = self._get_team_best_pitcher(away_team_obj.team_id)
            
            if home_pitcher and away_pitcher:
                # 分析投手對戰
                home_strength = self._classify_pitcher_strength(home_pitcher['era'], home_pitcher['quality'])
                away_strength = self._classify_pitcher_strength(away_pitcher['era'], away_pitcher['quality'])
                matchup_type = self._determine_matchup_type(home_strength, away_strength)
                
                analysis = {
                    'home_pitcher': {
                        'name': home_pitcher['name'],
                        'era': home_pitcher['era'],
                        'quality': home_pitcher['quality'],
                        'strength': home_strength
                    },
                    'away_pitcher': {
                        'name': away_pitcher['name'],
                        'era': away_pitcher['era'],
                        'quality': away_pitcher['quality'],
                        'strength': away_strength
                    },
                    'matchup_type': matchup_type,
                    'average_era': (home_pitcher['era'] + away_pitcher['era']) / 2,
                    'average_quality': (home_pitcher['quality'] + away_pitcher['quality']) / 2
                }
                
                logger.info(f"✅ 從數據庫快速獲取投手: {home_pitcher['name']} vs {away_pitcher['name']}")
                return analysis
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ 數據庫快速獲取失敗: {e}")
            return None
    
    def _get_team_best_pitcher(self, team_id: int) -> Optional[Dict]:
        """獲取球隊最佳投手"""
        try:
            from models.database import db, PlayerStats, Player
            
            # 查找該球隊ERA最低的投手
            best_pitcher = db.session.query(PlayerStats, Player).join(
                Player, PlayerStats.player_id == Player.player_id
            ).filter(
                Player.current_team_id == team_id,
                PlayerStats.position == 'P',
                PlayerStats.era.isnot(None),
                PlayerStats.innings_pitched > 50  # 至少投球50局
            ).order_by(PlayerStats.era.asc()).first()
            
            if best_pitcher:
                stats, player = best_pitcher
                era = float(stats.era)
                quality = self._calculate_pitcher_quality_from_era(era)
                
                return {
                    'name': player.full_name,
                    'era': era,
                    'quality': quality,
                    'innings_pitched': float(stats.innings_pitched or 0)
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ 獲取球隊最佳投手失敗: {e}")
            return None
    
    def _get_team_average_pitcher_analysis(self, home_team: str, away_team: str) -> Dict:
        """獲取球隊平均投手分析"""
        try:
            from models.database import db, PlayerStats, Team, Player
            
            # 獲取球隊平均ERA
            home_era = self._get_team_average_era_fast(home_team)
            away_era = self._get_team_average_era_fast(away_team)
            
            home_quality = self._calculate_pitcher_quality_from_era(home_era)
            away_quality = self._calculate_pitcher_quality_from_era(away_era)
            
            home_strength = self._classify_pitcher_strength(home_era, home_quality)
            away_strength = self._classify_pitcher_strength(away_era, away_quality)
            matchup_type = self._determine_matchup_type(home_strength, away_strength)
            
            analysis = {
                'home_pitcher': {
                    'name': f'{home_team} 平均投手',
                    'era': home_era,
                    'quality': home_quality,
                    'strength': home_strength
                },
                'away_pitcher': {
                    'name': f'{away_team} 平均投手',
                    'era': away_era,
                    'quality': away_quality,
                    'strength': away_strength
                },
                'matchup_type': matchup_type,
                'average_era': (home_era + away_era) / 2,
                'average_quality': (home_quality + away_quality) / 2
            }
            
            logger.info(f"✅ 使用球隊平均投手分析: {home_team}({home_strength}) vs {away_team}({away_strength})")
            return analysis
            
        except Exception as e:
            logger.warning(f"⚠️ 球隊平均投手分析失敗: {e}")
            return self._get_default_pitcher_analysis()
    
    def _get_team_average_era_fast(self, team: str) -> float:
        """快速獲取球隊平均ERA"""
        try:
            from models.database import db, PlayerStats, Team, Player
            
            # 檢查緩存
            cache_key = f"team_era_{team}"
            if cache_key in self.pitcher_cache:
                return self.pitcher_cache[cache_key]
            
            # 獲取球隊ID
            team_obj = Team.query.filter_by(team_code=team).first()
            if not team_obj:
                return 4.50
            
            # 計算球隊平均ERA
            avg_era = db.session.query(db.func.avg(PlayerStats.era)).join(
                Player, PlayerStats.player_id == Player.player_id
            ).filter(
                Player.current_team_id == team_obj.team_id,
                PlayerStats.position == 'P',
                PlayerStats.era.isnot(None)
            ).scalar()
            
            era = float(avg_era) if avg_era else 4.50
            
            # 緩存結果
            self.pitcher_cache[cache_key] = era
            
            return era
            
        except Exception as e:
            logger.warning(f"⚠️ 快速獲取球隊平均ERA失敗: {e}")
            return 4.50
    
    def _execute_fast_prediction(self, home_team: str, away_team: str, 
                               game_date: date, strategy: Dict) -> Dict:
        """執行快速預測"""
        try:
            # 基於數據庫的快速預測
            base_prediction = self._get_database_based_prediction(home_team, away_team)
            
            # 根據策略調整預測
            adjusted_prediction = self._adjust_prediction_by_strategy(base_prediction, strategy)
            
            return adjusted_prediction
            
        except Exception as e:
            logger.error(f"❌ 執行快速預測失敗: {e}")
            return self._get_simple_prediction(home_team, away_team)
    
    def _get_database_based_prediction(self, home_team: str, away_team: str) -> Dict:
        """基於數據庫的快速預測"""
        try:
            from models.database import db, Game
            
            # 獲取兩隊最近的比賽表現（限制查詢數量）
            home_recent = Game.query.filter(
                (Game.home_team == home_team) | (Game.away_team == home_team),
                Game.game_status == 'completed',
                Game.home_score.isnot(None)
            ).order_by(Game.date.desc()).limit(5).all()
            
            away_recent = Game.query.filter(
                (Game.home_team == away_team) | (Game.away_team == away_team),
                Game.game_status == 'completed',
                Game.away_score.isnot(None)
            ).order_by(Game.date.desc()).limit(5).all()
            
            # 計算平均得分
            home_avg = self._calculate_team_avg_score_fast(home_recent, home_team)
            away_avg = self._calculate_team_avg_score_fast(away_recent, away_team)
            
            # 主場優勢
            home_advantage = 0.3
            
            return {
                'predicted_home_score': home_avg + home_advantage,
                'predicted_away_score': away_avg,
                'total_runs': home_avg + away_avg + home_advantage,
                'confidence': 0.65,
                'home_win_probability': 0.52
            }
            
        except Exception as e:
            logger.warning(f"⚠️ 數據庫預測失敗: {e}")
            return self._get_simple_prediction(home_team, away_team)
    
    def _calculate_team_avg_score_fast(self, games, team) -> float:
        """快速計算球隊平均得分"""
        if not games:
            return 4.5
        
        total_score = 0
        count = 0
        
        for game in games:
            if game.home_team == team and game.home_score is not None:
                total_score += game.home_score
                count += 1
            elif game.away_team == team and game.away_score is not None:
                total_score += game.away_score
                count += 1
        
        return total_score / count if count > 0 else 4.5
    
    def _get_simple_prediction(self, home_team: str, away_team: str) -> Dict:
        """簡單預測（最後備選）"""
        return {
            'predicted_home_score': 5.0,
            'predicted_away_score': 4.5,
            'total_runs': 9.5,
            'confidence': 0.6,
            'home_win_probability': 0.52
        }
    
    # 以下方法與原來的智能預測系統相同
    def _calculate_pitcher_quality_from_era(self, era: float) -> float:
        """根據ERA計算投手質量分數"""
        return max(0, 100 - (era - 2.0) * 20)
    
    def _classify_pitcher_strength(self, era: float, quality: float) -> str:
        """分類投手強度"""
        if era <= 2.50 and quality >= 80:
            return "王牌"
        elif era <= 3.50 and quality >= 70:
            return "優秀"
        elif era <= 4.50 and quality >= 50:
            return "普通"
        else:
            return "弱勢"
    
    def _determine_matchup_type(self, home_strength: str, away_strength: str) -> str:
        """確定對戰類型"""
        strength_order = {'王牌': 4, '優秀': 3, '普通': 2, '弱勢': 1}
        
        home_level = strength_order.get(home_strength, 2)
        away_level = strength_order.get(away_strength, 2)
        
        if home_level >= 3 and away_level >= 3:
            return "王牌對決"
        elif home_level <= 1 and away_level <= 1:
            return "打擊戰"
        elif abs(home_level - away_level) >= 2:
            return "強弱對戰"
        else:
            return "普通對戰"
    
    def _determine_prediction_strategy(self, pitcher_analysis: Dict) -> Dict:
        """根據投手質量確定預測策略"""
        matchup_type = pitcher_analysis['matchup_type']
        
        if matchup_type == "王牌對決":
            return {
                'name': '王牌對決策略',
                'type': 'low_scoring',
                'target_total': 7.0,
                'score_adjustment': -1.5,
                'confidence_boost': 0.1,
                'description': '兩位王牌投手對決，預期低分比賽'
            }
        elif matchup_type == "打擊戰":
            return {
                'name': '打擊戰策略',
                'type': 'high_scoring',
                'target_total': 12.0,
                'score_adjustment': 2.0,
                'confidence_boost': 0.1,
                'description': '兩位弱勢投手，預期高分打擊戰'
            }
        elif matchup_type == "強弱對戰":
            return {
                'name': '強弱對戰策略',
                'type': 'unbalanced',
                'target_total': 9.0,
                'score_adjustment': 0.5,
                'confidence_boost': 0.05,
                'description': '強弱投手對戰，分數不平衡'
            }
        else:
            return {
                'name': '標準策略',
                'type': 'standard',
                'target_total': 9.5,
                'score_adjustment': 0.0,
                'confidence_boost': 0.0,
                'description': '普通投手對戰，使用標準預測'
            }
    
    def _adjust_prediction_by_strategy(self, base_prediction: Dict, strategy: Dict) -> Dict:
        """根據策略調整預測結果"""
        try:
            home_score = base_prediction['predicted_home_score']
            away_score = base_prediction['predicted_away_score']
            current_total = home_score + away_score
            target_total = strategy['target_total']
            adjustment = strategy['score_adjustment']
            
            if strategy['type'] == 'low_scoring':
                # 王牌對決 - 降低總分
                if current_total > target_total:
                    scale_factor = target_total / current_total
                    home_score *= scale_factor
                    away_score *= scale_factor
                home_score = max(2.0, home_score + adjustment)
                away_score = max(2.0, away_score + adjustment)
                
            elif strategy['type'] == 'high_scoring':
                # 打擊戰 - 提高總分
                if current_total < target_total:
                    scale_factor = target_total / current_total
                    home_score *= scale_factor
                    away_score *= scale_factor
                home_score = min(15.0, home_score + adjustment)
                away_score = min(15.0, away_score + adjustment)
                
            elif strategy['type'] == 'unbalanced':
                # 強弱對戰 - 調整平衡
                home_score += adjustment * 0.5
                away_score += adjustment * 0.5
            
            # 確保合理範圍
            home_score = max(1.0, min(20.0, round(home_score, 1)))
            away_score = max(1.0, min(20.0, round(away_score, 1)))
            
            # 調整信心度
            confidence = base_prediction.get('confidence', 0.6)
            confidence = min(0.95, confidence + strategy['confidence_boost'])
            
            return {
                'predicted_home_score': home_score,
                'predicted_away_score': away_score,
                'total_runs': home_score + away_score,
                'confidence': confidence,
                'home_win_probability': home_score / (home_score + away_score),
                'strategy_applied': strategy['name']
            }
            
        except Exception as e:
            logger.error(f"❌ 策略調整失敗: {e}")
            return base_prediction
    
    def _get_default_pitcher_analysis(self) -> Dict:
        """獲取默認投手分析"""
        return {
            'home_pitcher': {'name': '未知投手', 'era': 4.50, 'quality': 50.0, 'strength': '普通'},
            'away_pitcher': {'name': '未知投手', 'era': 4.50, 'quality': 50.0, 'strength': '普通'},
            'matchup_type': '普通對戰',
            'average_era': 4.50,
            'average_quality': 50.0
        }
    
    def _get_fallback_prediction(self, home_team: str, away_team: str, elapsed_time: float) -> Dict:
        """獲取保守預測"""
        return {
            'predicted_home_score': 5.0,
            'predicted_away_score': 4.5,
            'total_runs': 9.5,
            'confidence': 0.6,
            'home_win_probability': 0.52,
            'pitcher_analysis': self._get_default_pitcher_analysis(),
            'strategy': {
                'name': '保守策略',
                'type': 'standard',
                'description': '使用保守預測'
            },
            'explanation': f'保守預測 {away_team} @ {home_team}: 4.5-5.0 (總分9.5)',
            'prediction_time': round(elapsed_time, 2)
        }
    
    def _generate_explanation(self, pitcher_analysis: Dict, strategy: Dict, prediction: Dict) -> str:
        """生成預測說明"""
        home_pitcher = pitcher_analysis['home_pitcher']
        away_pitcher = pitcher_analysis['away_pitcher']
        
        explanation = f"""
🚀 快速智能預測分析:

📊 投手分析:
• 主隊: {home_pitcher['name']} (ERA: {home_pitcher['era']:.2f}, 等級: {home_pitcher['strength']})
• 客隊: {away_pitcher['name']} (ERA: {away_pitcher['era']:.2f}, 等級: {away_pitcher['strength']})
• 對戰類型: {pitcher_analysis['matchup_type']}

🧠 預測策略: {strategy['name']}
• {strategy['description']}
• 目標總分: {strategy['target_total']}分

📈 預測結果:
• 比分: {prediction['predicted_away_score']:.1f} - {prediction['predicted_home_score']:.1f}
• 總分: {prediction['total_runs']:.1f}分
• 信心度: {prediction['confidence']:.1%}
• 預測耗時: {prediction.get('prediction_time', 0):.2f}秒
        """.strip()
        
        return explanation

# 創建全局實例
fast_intelligent_predictor = FastIntelligentPredictor()
