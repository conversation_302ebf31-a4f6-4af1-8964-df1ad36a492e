#!/usr/bin/env python3
"""
分析 CHC @ NYY 和 SEA @ DET 比賽中投手的賽季表現
檢查我們是否有正確的投手統計數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, PlayerStats, BoxScore
import json

def analyze_pitcher_season_stats():
    """分析投手賽季統計"""
    app = create_app()
    
    with app.app_context():
        print("🔍 分析投手賽季表現...")
        
        # 目標投手
        pitchers = [
            '<PERSON>',  # CHC
            'Max Fried',     # NYY
            '<PERSON>',  # SEA
            '<PERSON>'     # DET
        ]
        
        print(f"\n📊 投手賽季統計分析:")
        
        for pitcher_name in pitchers:
            print(f"\n🥎 {pitcher_name}:")
            
            # 查找投手統計
            pitcher_stats = PlayerStats.query.filter(
                PlayerStats.player_name.ilike(f'%{pitcher_name}%'),
                PlayerStats.innings_pitched > 10  # 確保有足夠投球局數
            ).all()
            
            if pitcher_stats:
                for stats in pitcher_stats:
                    print(f"  隊伍: {stats.team_id}")
                    print(f"  賽季: {stats.season}")
                    print(f"  ERA: {stats.era:.2f}")
                    print(f"  WHIP: {stats.whip:.2f}")
                    print(f"  投球局數: {stats.innings_pitched:.1f}")
                    print(f"  戰績: {stats.wins}-{stats.losses}")
                    print(f"  三振: {stats.strikeouts_pitching}")
                    print(f"  ----")
            else:
                print(f"  ❌ 未找到 {pitcher_name} 的統計數據")

def analyze_recent_pitcher_performance():
    """分析投手近期表現"""
    app = create_app()
    
    with app.app_context():
        print(f"\n📈 投手近期表現分析:")
        
        # 查找 CHC @ NYY 比賽
        chc_nyy_game = Game.query.filter(
            Game.date == date(2025, 7, 12),
            Game.away_team == 'CHC',
            Game.home_team == 'NYY'
        ).first()
        
        if chc_nyy_game:
            print(f"\n⚾ CHC @ NYY (Game ID: {chc_nyy_game.game_id}):")
            print(f"  比分: {chc_nyy_game.away_score} - {chc_nyy_game.home_score}")
            
            # 查找 BoxScore
            boxscores = BoxScore.query.filter_by(game_id=chc_nyy_game.game_id).all()
            for boxscore in boxscores:
                team_type = "主隊 NYY" if boxscore.is_home else "客隊 CHC"
                print(f"  {team_type}: 得分 {boxscore.runs}, 安打 {boxscore.hits}, 失誤 {boxscore.errors}")
        
        # 查找 SEA @ DET 比賽
        sea_det_game = Game.query.filter(
            Game.date == date(2025, 7, 12),
            Game.away_team == 'SEA',
            Game.home_team == 'DET'
        ).first()
        
        if sea_det_game:
            print(f"\n⚾ SEA @ DET (Game ID: {sea_det_game.game_id}):")
            print(f"  比分: {sea_det_game.away_score} - {sea_det_game.home_score}")
            
            # 查找 BoxScore
            boxscores = BoxScore.query.filter_by(game_id=sea_det_game.game_id).all()
            for boxscore in boxscores:
                team_type = "主隊 DET" if boxscore.is_home else "客隊 SEA"
                print(f"  {team_type}: 得分 {boxscore.runs}, 安打 {boxscore.hits}, 失誤 {boxscore.errors}")

def analyze_team_recent_performance():
    """分析球隊近期表現"""
    app = create_app()
    
    with app.app_context():
        print(f"\n📊 球隊近期表現分析:")
        
        teams = ['CHC', 'NYY', 'SEA', 'DET']
        target_date = date(2025, 7, 12)
        
        for team in teams:
            print(f"\n🏟️ {team} 近期表現:")
            
            # 獲取最近5場比賽
            recent_games = Game.query.filter(
                ((Game.home_team == team) | (Game.away_team == team)),
                Game.date < target_date,
                Game.game_status == 'completed',
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).order_by(Game.date.desc()).limit(5).all()
            
            if recent_games:
                total_scores = []
                team_scores = []
                
                for game in recent_games:
                    total_score = game.home_score + game.away_score
                    total_scores.append(total_score)
                    
                    # 該隊得分
                    if game.home_team == team:
                        team_score = game.home_score
                        opponent = game.away_team
                    else:
                        team_score = game.away_score
                        opponent = game.home_team
                    
                    team_scores.append(team_score)
                    print(f"  {game.date} vs {opponent}: {team} 得分 {team_score}, 總分 {total_score}")
                
                avg_total = sum(total_scores) / len(total_scores)
                avg_team_score = sum(team_scores) / len(team_scores)
                
                print(f"  近期平均總分: {avg_total:.1f}")
                print(f"  近期平均得分: {avg_team_score:.1f}")
            else:
                print(f"  ❌ 未找到 {team} 的近期比賽數據")

def check_prediction_accuracy():
    """檢查預測準確性"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🎯 預測準確性分析:")
        
        # CHC @ NYY
        print(f"\n📈 CHC @ NYY:")
        print(f"  預測: CHC 5.6 - NYY 10.9 (總分 16.5)")
        print(f"  實際: CHC 5 - NYY 2 (總分 7)")
        print(f"  預測偏差: +9.5 分 (預測過高)")
        print(f"  勝負預測: ❌ (預測 NYY 勝，實際 CHC 勝)")
        
        # SEA @ DET  
        print(f"\n📈 SEA @ DET:")
        print(f"  預測: SEA 4.7 - DET 6.9 (總分 11.6)")
        print(f"  實際: SEA 15 - DET 7 (總分 22)")
        print(f"  預測偏差: -10.4 分 (預測過低)")
        print(f"  勝負預測: ❌ (預測 DET 勝，實際 SEA 勝)")
        
        print(f"\n🚨 問題總結:")
        print(f"  1. CHC @ NYY: 預測 NYY 大勝，實際 CHC 勝")
        print(f"  2. SEA @ DET: 預測低分比賽，實際高分比賽")
        print(f"  3. 兩場比賽勝負預測都錯誤")
        print(f"  4. 總分預測偏差都超過 9 分")

if __name__ == "__main__":
    print("🚀 分析投手和球隊表現...")
    
    # 1. 分析投手賽季統計
    analyze_pitcher_season_stats()
    
    # 2. 分析投手近期表現
    analyze_recent_pitcher_performance()
    
    # 3. 分析球隊近期表現
    analyze_team_recent_performance()
    
    # 4. 檢查預測準確性
    check_prediction_accuracy()
    
    print("\n✅ 分析完成！")
