<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型版本選擇預覽</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> 模型版本選擇預覽</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="modelVersionSuffix" class="form-label">模型版本後綴</label>
                                <select class="form-control" id="modelVersionSuffix">
                                    <option value="">選擇版本 (可選)</option>
                                    <optgroup label="🎯 預測改進版本">
                                        <option value="improved_v1">improved_v1 - 基礎改進版本</option>
                                        <option value="improved_v2">improved_v2 - 進階改進版本</option>
                                        <option value="improved_v3">improved_v3 - 最新改進版本</option>
                                    </optgroup>
                                    <optgroup label="⚾ 投手分析版本">
                                        <option value="pitcher_analysis">pitcher_analysis - 投手深度分析</option>
                                        <option value="ace_pitcher_v1">ace_pitcher_v1 - 王牌投手特化</option>
                                        <option value="pitcher_matchup">pitcher_matchup - 投手對戰分析</option>
                                        <option value="era_calibrated">era_calibrated - ERA校正版本</option>
                                    </optgroup>
                                    <optgroup label="🛡️ 壓制效應版本">
                                        <option value="boyd_suppression">boyd_suppression - Boyd壓制分析</option>
                                        <option value="lineup_suppression">lineup_suppression - 打線壓制分析</option>
                                        <option value="ace_dominance">ace_dominance - 王牌主宰效應</option>
                                        <option value="pitcher_dominance">pitcher_dominance - 投手主導分析</option>
                                    </optgroup>
                                    <optgroup label="💥 意外情況版本">
                                        <option value="outlier_detection">outlier_detection - 意外情況檢測</option>
                                        <option value="explosion_analysis">explosion_analysis - 爆發分析</option>
                                        <option value="upset_prediction">upset_prediction - 冷門預測</option>
                                        <option value="variance_adjusted">variance_adjusted - 變異調整</option>
                                    </optgroup>
                                    <optgroup label="📊 數據源版本">
                                        <option value="real_odds_only">real_odds_only - 僅真實盤口</option>
                                        <option value="historical_validation">historical_validation - 歷史驗證</option>
                                        <option value="recent_form_focus">recent_form_focus - 近期狀態重點</option>
                                        <option value="comprehensive_data">comprehensive_data - 綜合數據</option>
                                    </optgroup>
                                    <optgroup label="🧪 實驗版本">
                                        <option value="experimental_v1">experimental_v1 - 實驗版本1</option>
                                        <option value="experimental_v2">experimental_v2 - 實驗版本2</option>
                                        <option value="test_algorithm">test_algorithm - 測試算法</option>
                                        <option value="beta_features">beta_features - Beta功能</option>
                                    </optgroup>
                                    <optgroup label="🎲 自定義版本">
                                        <option value="custom_logic">custom_logic - 自定義邏輯</option>
                                        <option value="user_defined">user_defined - 用戶定義</option>
                                    </optgroup>
                                </select>
                                <small class="form-text text-muted">選擇或標識改進的預測邏輯版本</small>
                            </div>
                            <div class="col-md-6">
                                <label for="customVersionInput" class="form-label">自定義版本名稱 (可選)</label>
                                <input type="text" class="form-control" id="customVersionInput" placeholder="輸入自定義版本名稱">
                                <small class="form-text text-muted">如果需要自定義版本名稱，會覆蓋上方選擇</small>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> 當前選擇:</h6>
                                    <div id="currentSelection">請選擇一個版本</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6><i class="fas fa-lightbulb"></i> 版本說明:</h6>
                                <div id="versionDescription" class="text-muted">
                                    選擇一個版本查看詳細說明
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 版本說明
        const versionDescriptions = {
            'improved_v1': '基礎改進版本 - 整體預測準確性提升，適合一般使用',
            'improved_v2': '進階改進版本 - 加入更多分析因子，提升複雜情況預測',
            'improved_v3': '最新改進版本 - 最新算法和數據處理邏輯',
            'pitcher_analysis': '投手深度分析 - 專注於投手表現的深度分析',
            'ace_pitcher_v1': '王牌投手特化 - 針對王牌投手的特殊處理邏輯',
            'pitcher_matchup': '投手對戰分析 - 分析投手與特定球隊的對戰歷史',
            'era_calibrated': 'ERA校正版本 - 基於ERA的校正預測邏輯',
            'boyd_suppression': 'Boyd壓制分析 - 專門分析Matthew Boyd等王牌投手的壓制效應',
            'lineup_suppression': '打線壓制分析 - 分析強投手對特定打線的壓制效果',
            'ace_dominance': '王牌主宰效應 - 王牌投手主導比賽的分析邏輯',
            'pitcher_dominance': '投手主導分析 - 投手主導比賽情況的預測',
            'outlier_detection': '意外情況檢測 - 檢測和處理意外爆發/壓制情況',
            'explosion_analysis': '爆發分析 - 專門分析球隊突然爆發得分的情況',
            'upset_prediction': '冷門預測 - 預測可能的冷門結果',
            'variance_adjusted': '變異調整 - 根據歷史變異性調整預測',
            'real_odds_only': '僅真實盤口 - 只使用真實博彩盤口數據',
            'historical_validation': '歷史驗證 - 基於歷史數據驗證的預測邏輯',
            'recent_form_focus': '近期狀態重點 - 重點考慮球隊近期表現',
            'comprehensive_data': '綜合數據 - 使用所有可用數據源的綜合分析',
            'experimental_v1': '實驗版本1 - 實驗性算法和邏輯',
            'experimental_v2': '實驗版本2 - 更新的實驗性功能',
            'test_algorithm': '測試算法 - 用於測試新算法的版本',
            'beta_features': 'Beta功能 - 包含Beta階段功能的版本',
            'custom_logic': '自定義邏輯 - 用戶自定義的預測邏輯',
            'user_defined': '用戶定義 - 完全由用戶定義的版本'
        };

        // 更新當前選擇顯示
        function updateSelection() {
            const customInput = document.getElementById('customVersionInput').value.trim();
            const selectedVersion = document.getElementById('modelVersionSuffix').value;
            const currentVersion = customInput || selectedVersion;
            
            const selectionDiv = document.getElementById('currentSelection');
            const descriptionDiv = document.getElementById('versionDescription');
            
            if (currentVersion) {
                selectionDiv.innerHTML = `<strong>${currentVersion}</strong>`;
                
                if (customInput) {
                    descriptionDiv.innerHTML = `<em>自定義版本: ${customInput}</em>`;
                } else if (versionDescriptions[selectedVersion]) {
                    descriptionDiv.innerHTML = versionDescriptions[selectedVersion];
                } else {
                    descriptionDiv.innerHTML = '無可用說明';
                }
            } else {
                selectionDiv.innerHTML = '請選擇一個版本';
                descriptionDiv.innerHTML = '選擇一個版本查看詳細說明';
            }
        }

        // 事件監聽
        document.getElementById('modelVersionSuffix').addEventListener('change', function() {
            if (this.value) {
                document.getElementById('customVersionInput').value = '';
            }
            updateSelection();
        });

        document.getElementById('customVersionInput').addEventListener('input', function() {
            if (this.value.trim()) {
                document.getElementById('modelVersionSuffix').value = '';
            }
            updateSelection();
        });

        // 初始化
        updateSelection();
    </script>
</body>
</html>
