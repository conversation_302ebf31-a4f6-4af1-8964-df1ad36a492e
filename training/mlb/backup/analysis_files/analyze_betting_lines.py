#!/usr/bin/env python3
"""
分析博彩盤口數據和預測準確性
特別針對大小分盤口的統計分析
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction, BettingOdds
import numpy as np
import pandas as pd
from collections import defaultdict

def analyze_over_under_accuracy():
    """分析大小分盤口的準確性"""
    app = create_app()
    
    with app.app_context():
        print("🎯 分析大小分盤口準確性...")
        
        # 獲取有完整數據的比賽
        query = """
        SELECT
            g.game_id,
            g.date,
            g.home_team,
            g.away_team,
            g.home_score,
            g.away_score,
            (g.home_score + g.away_score) as actual_total,
            p.predicted_home_score,
            p.predicted_away_score,
            (p.predicted_home_score + p.predicted_away_score) as predicted_total,
            p.confidence,
            bo.total_point,
            bo.over_price,
            bo.under_price,
            bo.bookmaker
        FROM games g
        JOIN predictions p ON g.game_id = p.game_id
        LEFT JOIN betting_odds bo ON g.game_id = bo.game_id
        WHERE g.home_score IS NOT NULL
        AND g.away_score IS NOT NULL
        AND p.predicted_home_score IS NOT NULL
        AND p.predicted_away_score IS NOT NULL
        AND g.date >= DATE('now', '-30 days')
        ORDER BY g.date DESC
        """
        
        results = db.session.execute(db.text(query)).fetchall()
        
        if not results:
            print("❌ 沒有找到足夠的數據進行分析")
            return
        
        print(f"📊 找到 {len(results)} 場比賽的完整數據")
        
        # 轉換為 DataFrame 進行分析
        columns = ['game_id', 'date', 'home_team', 'away_team', 'home_score', 'away_score',
                  'actual_total', 'predicted_home_score', 'predicted_away_score', 'predicted_total',
                  'confidence', 'total_point', 'over_price', 'under_price', 'bookmaker']

        df = pd.DataFrame(results, columns=columns)
        
        # 1. 基本統計
        print(f"\n📈 基本統計:")
        print(f"  平均實際總分: {df['actual_total'].mean():.1f}")
        print(f"  平均預測總分: {df['predicted_total'].mean():.1f}")
        print(f"  總分標準差: {df['actual_total'].std():.1f}")
        
        # 2. 分析不同盤口線的準確性
        analyze_line_accuracy(df)
        
        # 3. 分析特定盤口（如8.5）的概率
        analyze_specific_line(df, 8.5)
        
        # 4. 分析預測vs實際的差異
        analyze_prediction_accuracy(df)
        
        # 5. 分析博彩盤口的準確性
        analyze_bookmaker_accuracy(df)

def analyze_line_accuracy(df):
    """分析不同盤口線的準確性"""
    print(f"\n🎯 盤口線準確性分析:")
    
    # 過濾有盤口數據的比賽
    df_with_lines = df[df['total_point'].notna()].copy()
    
    if len(df_with_lines) == 0:
        print("  ❌ 沒有找到盤口數據")
        return
    
    print(f"  有盤口數據的比賽: {len(df_with_lines)} 場")
    
    # 計算實際結果vs盤口
    df_with_lines['actual_over'] = df_with_lines['actual_total'] > df_with_lines['total_point']
    df_with_lines['predicted_over'] = df_with_lines['predicted_total'] > df_with_lines['total_point']

    # 按盤口線分組分析
    line_groups = df_with_lines.groupby('total_point')
    
    print(f"\n  各盤口線統計:")
    for line, group in line_groups:
        total_games = len(group)
        actual_over_count = group['actual_over'].sum()
        actual_over_rate = actual_over_count / total_games * 100
        
        predicted_over_count = group['predicted_over'].sum()
        predicted_over_rate = predicted_over_count / total_games * 100
        
        # 計算預測準確性
        correct_predictions = (group['actual_over'] == group['predicted_over']).sum()
        accuracy = correct_predictions / total_games * 100
        
        print(f"    盤口 {line}: {total_games} 場比賽")
        print(f"      實際大分率: {actual_over_rate:.1f}% ({actual_over_count}/{total_games})")
        print(f"      預測大分率: {predicted_over_rate:.1f}% ({predicted_over_count}/{total_games})")
        print(f"      預測準確率: {accuracy:.1f}% ({correct_predictions}/{total_games})")
        print()

def analyze_specific_line(df, target_line):
    """分析特定盤口線的概率"""
    print(f"\n🔍 盤口 {target_line} 詳細分析:")
    
    # 找到接近目標盤口的比賽
    tolerance = 0.5  # 允許0.5的誤差
    df_target = df[
        (df['total_point'] >= target_line - tolerance) &
        (df['total_point'] <= target_line + tolerance)
    ].copy()
    
    if len(df_target) == 0:
        print(f"  ❌ 沒有找到盤口 {target_line} 附近的數據")
        
        # 分析所有數據中接近這個總分的比賽
        print(f"\n  📊 分析實際總分接近 {target_line} 的比賽:")
        df_near_total = df[
            (df['actual_total'] >= target_line - 1) & 
            (df['actual_total'] <= target_line + 1)
        ]
        
        if len(df_near_total) > 0:
            over_count = (df_near_total['actual_total'] > target_line).sum()
            over_rate = over_count / len(df_near_total) * 100
            print(f"    總分接近 {target_line} 的比賽: {len(df_near_total)} 場")
            print(f"    超過 {target_line} 的比例: {over_rate:.1f}% ({over_count}/{len(df_near_total)})")
        
        return
    
    print(f"  找到 {len(df_target)} 場盤口接近 {target_line} 的比賽")
    
    # 計算統計數據
    actual_over = (df_target['actual_total'] > target_line).sum()
    actual_over_rate = actual_over / len(df_target) * 100
    
    predicted_over = (df_target['predicted_total'] > target_line).sum()
    predicted_over_rate = predicted_over / len(df_target) * 100
    
    # 計算平均總分
    avg_actual = df_target['actual_total'].mean()
    avg_predicted = df_target['predicted_total'].mean()
    
    print(f"  實際結果:")
    print(f"    平均總分: {avg_actual:.1f}")
    print(f"    超過 {target_line} 的概率: {actual_over_rate:.1f}% ({actual_over}/{len(df_target)})")
    print(f"  預測結果:")
    print(f"    平均預測總分: {avg_predicted:.1f}")
    print(f"    預測超過 {target_line} 的概率: {predicted_over_rate:.1f}% ({predicted_over}/{len(df_target)})")
    
    # 分析預測準確性
    correct_over_under = ((df_target['actual_total'] > target_line) == 
                         (df_target['predicted_total'] > target_line)).sum()
    accuracy = correct_over_under / len(df_target) * 100
    print(f"  大小分預測準確率: {accuracy:.1f}% ({correct_over_under}/{len(df_target)})")

def analyze_prediction_accuracy(df):
    """分析預測準確性"""
    print(f"\n📊 預測準確性分析:")
    
    # 計算預測誤差
    df['total_error'] = abs(df['actual_total'] - df['predicted_total'])
    
    print(f"  平均預測誤差: {df['total_error'].mean():.1f} 分")
    print(f"  預測誤差標準差: {df['total_error'].std():.1f}")
    print(f"  最大預測誤差: {df['total_error'].max():.1f} 分")
    print(f"  最小預測誤差: {df['total_error'].min():.1f} 分")
    
    # 按誤差範圍分組
    error_ranges = [
        (0, 1, "非常準確"),
        (1, 2, "很準確"), 
        (2, 3, "準確"),
        (3, 5, "一般"),
        (5, float('inf'), "不準確")
    ]
    
    print(f"\n  預測準確度分布:")
    for min_err, max_err, label in error_ranges:
        if max_err == float('inf'):
            count = (df['total_error'] >= min_err).sum()
        else:
            count = ((df['total_error'] >= min_err) & (df['total_error'] < max_err)).sum()
        
        percentage = count / len(df) * 100
        print(f"    {label} (誤差 {min_err}-{max_err if max_err != float('inf') else '∞'}): {percentage:.1f}% ({count}/{len(df)})")

def analyze_bookmaker_accuracy(df):
    """分析博彩商盤口的準確性"""
    print(f"\n🏦 博彩商盤口準確性:")
    
    df_with_lines = df[df['total_point'].notna()].copy()
    
    if len(df_with_lines) == 0:
        print("  ❌ 沒有盤口數據")
        return
    
    # 計算盤口預測準確性
    df_with_lines['bookmaker_over'] = df_with_lines['actual_total'] > df_with_lines['total_point']
    
    # 假設盤口線代表50%的概率點
    bookmaker_accuracy = (df_with_lines['bookmaker_over'].mean() * 100)
    
    print(f"  博彩商大分命中率: {bookmaker_accuracy:.1f}%")
    print(f"  (理論上應該接近50%，因為盤口設計為平衡點)")
    
    # 分析盤口線與實際平均分的差異
    avg_line = df_with_lines['total_point'].mean()
    avg_actual = df_with_lines['actual_total'].mean()
    line_bias = avg_actual - avg_line
    
    print(f"  平均盤口線: {avg_line:.1f}")
    print(f"  平均實際總分: {avg_actual:.1f}")
    print(f"  盤口偏差: {line_bias:+.1f} ({'偏高' if line_bias > 0 else '偏低' if line_bias < 0 else '準確'})")

def generate_probability_model(target_line=8.5):
    """基於歷史數據生成概率模型"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🎲 生成盤口 {target_line} 的概率模型:")
        
        # 獲取歷史數據
        query = """
        SELECT 
            (g.home_score + g.away_score) as actual_total,
            (p.predicted_home_score + p.predicted_away_score) as predicted_total
        FROM games g
        JOIN predictions p ON g.game_id = p.game_id
        WHERE g.home_score IS NOT NULL 
        AND g.away_score IS NOT NULL
        AND p.predicted_home_score IS NOT NULL
        AND p.predicted_away_score IS NOT NULL
        AND g.date >= DATE('now', '-90 days')
        """
        
        results = db.session.execute(db.text(query)).fetchall()
        columns = ['actual_total', 'predicted_total']
        df = pd.DataFrame(results, columns=columns)
        
        if len(df) == 0:
            print("  ❌ 沒有足夠的歷史數據")
            return
        
        # 計算基於歷史數據的概率
        total_games = len(df)
        over_count = (df['actual_total'] > target_line).sum()
        historical_over_probability = over_count / total_games * 100
        
        print(f"  基於 {total_games} 場歷史比賽:")
        print(f"  超過 {target_line} 分的歷史概率: {historical_over_probability:.1f}%")
        
        # 按預測總分範圍分析
        print(f"\n  按預測總分範圍分析超過 {target_line} 的概率:")
        
        ranges = [
            (0, target_line-2, f"預測 < {target_line-2}"),
            (target_line-2, target_line-1, f"預測 {target_line-2}-{target_line-1}"),
            (target_line-1, target_line, f"預測 {target_line-1}-{target_line}"),
            (target_line, target_line+1, f"預測 {target_line}-{target_line+1}"),
            (target_line+1, target_line+2, f"預測 {target_line+1}-{target_line+2}"),
            (target_line+2, 100, f"預測 > {target_line+2}")
        ]
        
        for min_pred, max_pred, label in ranges:
            subset = df[(df['predicted_total'] >= min_pred) & (df['predicted_total'] < max_pred)]
            if len(subset) > 0:
                over_in_range = (subset['actual_total'] > target_line).sum()
                prob_in_range = over_in_range / len(subset) * 100
                print(f"    {label}: {prob_in_range:.1f}% ({over_in_range}/{len(subset)})")

if __name__ == "__main__":
    print("🚀 開始分析博彩盤口數據...")
    
    analyze_over_under_accuracy()
    generate_probability_model(8.5)
    
    print("\n✅ 分析完成！")
