#!/usr/bin/env python3
"""
MLB預測系統回測驗證模組
用於評估預測模型的歷史表現和準確性
"""

import os
import sys
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import json
import logging

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import Game, Prediction, BettingOdds, db

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BacktestValidationSystem:
    """回測驗證系統"""
    
    def __init__(self, app=None):
        self.app = app or create_app()
        self.db_path = os.path.join(os.path.dirname(__file__), "instance", "mlb_data.db")
        
    def run_backtest_analysis(self, start_date: str, end_date: str, model_versions: List[str] = None) -> Dict:
        """運行回測分析"""
        with self.app.app_context():
            logger.info(f"🔍 開始回測分析: {start_date} 到 {end_date}")
            
            if model_versions is None:
                model_versions = ['Core_v1.0', 'unified_v1.0', 'enhanced']
            
            results = {}
            
            for model_version in model_versions:
                logger.info(f"📊 分析模型: {model_version}")
                model_results = self._analyze_model_performance(start_date, end_date, model_version)
                results[model_version] = model_results
                
            return results
    
    def _analyze_model_performance(self, start_date: str, end_date: str, model_version: str) -> Dict:
        """分析單個模型的表現"""
        try:
            # 查詢預測記錄
            predictions = Prediction.query.join(Game).filter(
                Game.date >= start_date,
                Game.date <= end_date,
                Prediction.model_version.like(f'%{model_version}%'),
                Game.game_status.in_(['Final', 'completed'])  # 只分析已完成的比賽
            ).all()
            
            if not predictions:
                logger.warning(f"沒有找到 {model_version} 模型的預測記錄")
                return {}
            
            logger.info(f"找到 {len(predictions)} 筆預測記錄")
            
            # 計算各種準確性指標
            win_loss_accuracy = self._calculate_win_loss_accuracy(predictions)
            score_accuracy = self._calculate_score_accuracy(predictions)
            over_under_accuracy = self._calculate_over_under_accuracy(predictions)
            confidence_analysis = self._analyze_confidence_levels(predictions)
            betting_performance = self._analyze_betting_performance(predictions)
            
            return {
                'total_predictions': len(predictions),
                'win_loss_accuracy': win_loss_accuracy,
                'score_accuracy': score_accuracy,
                'over_under_accuracy': over_under_accuracy,
                'confidence_analysis': confidence_analysis,
                'betting_performance': betting_performance,
                'date_range': f"{start_date} 到 {end_date}"
            }
            
        except Exception as e:
            logger.error(f"分析模型表現失敗: {e}")
            return {}
    
    def _calculate_win_loss_accuracy(self, predictions: List[Prediction]) -> Dict:
        """計算勝負預測準確性"""
        correct_predictions = 0
        total_predictions = 0
        
        for pred in predictions:
            game = pred.game
            if game and game.home_score is not None and game.away_score is not None:
                # 預測結果
                predicted_home_wins = pred.predicted_home_score > pred.predicted_away_score
                # 實際結果
                actual_home_wins = game.home_score > game.away_score
                
                if predicted_home_wins == actual_home_wins:
                    correct_predictions += 1
                total_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        return {
            'accuracy': round(accuracy * 100, 2),
            'correct': correct_predictions,
            'total': total_predictions
        }
    
    def _calculate_score_accuracy(self, predictions: List[Prediction]) -> Dict:
        """計算分數預測準確性"""
        home_score_errors = []
        away_score_errors = []
        total_score_errors = []
        
        for pred in predictions:
            game = pred.game
            if game and game.home_score is not None and game.away_score is not None:
                # 計算誤差
                home_error = abs(pred.predicted_home_score - game.home_score)
                away_error = abs(pred.predicted_away_score - game.away_score)
                total_error = abs((pred.predicted_home_score + pred.predicted_away_score) - 
                                (game.home_score + game.away_score))
                
                home_score_errors.append(home_error)
                away_score_errors.append(away_error)
                total_score_errors.append(total_error)
        
        if not total_score_errors:
            return {}
        
        return {
            'mean_home_error': round(np.mean(home_score_errors), 2),
            'mean_away_error': round(np.mean(away_score_errors), 2),
            'mean_total_error': round(np.mean(total_score_errors), 2),
            'median_total_error': round(np.median(total_score_errors), 2),
            'std_total_error': round(np.std(total_score_errors), 2)
        }
    
    def _calculate_over_under_accuracy(self, predictions: List[Prediction]) -> Dict:
        """計算大小分預測準確性"""
        correct_over_under = 0
        total_over_under = 0
        
        for pred in predictions:
            game = pred.game
            if (game and game.home_score is not None and game.away_score is not None 
                and pred.over_under_line is not None):
                
                predicted_total = pred.predicted_home_score + pred.predicted_away_score
                actual_total = game.home_score + game.away_score
                line = pred.over_under_line
                
                # 預測是Over還是Under
                predicted_over = predicted_total > line
                actual_over = actual_total > line
                
                if predicted_over == actual_over:
                    correct_over_under += 1
                total_over_under += 1
        
        accuracy = correct_over_under / total_over_under if total_over_under > 0 else 0
        
        return {
            'accuracy': round(accuracy * 100, 2),
            'correct': correct_over_under,
            'total': total_over_under
        }
    
    def _analyze_confidence_levels(self, predictions: List[Prediction]) -> Dict:
        """分析信心度與準確性的關係"""
        confidence_buckets = {
            'high': {'range': '70-100%', 'predictions': [], 'correct': 0},
            'medium': {'range': '50-70%', 'predictions': [], 'correct': 0},
            'low': {'range': '0-50%', 'predictions': [], 'correct': 0}
        }
        
        for pred in predictions:
            game = pred.game
            if (game and game.home_score is not None and game.away_score is not None 
                and pred.confidence is not None):
                
                confidence = pred.confidence * 100 if pred.confidence <= 1 else pred.confidence
                
                # 判斷預測是否正確
                predicted_home_wins = pred.predicted_home_score > pred.predicted_away_score
                actual_home_wins = game.home_score > game.away_score
                is_correct = predicted_home_wins == actual_home_wins
                
                # 分配到信心度區間
                if confidence >= 70:
                    bucket = 'high'
                elif confidence >= 50:
                    bucket = 'medium'
                else:
                    bucket = 'low'
                
                confidence_buckets[bucket]['predictions'].append(pred)
                if is_correct:
                    confidence_buckets[bucket]['correct'] += 1
        
        # 計算各區間的準確率
        for bucket_name, bucket_data in confidence_buckets.items():
            total = len(bucket_data['predictions'])
            if total > 0:
                accuracy = bucket_data['correct'] / total * 100
                bucket_data['accuracy'] = round(accuracy, 2)
                bucket_data['total'] = total
            else:
                bucket_data['accuracy'] = 0
                bucket_data['total'] = 0
            
            # 移除predictions列表以減少輸出大小
            del bucket_data['predictions']
        
        return confidence_buckets
    
    def _analyze_betting_performance(self, predictions: List[Prediction]) -> Dict:
        """分析博彩表現"""
        over_under_profit = 0
        over_under_bets = 0
        
        for pred in predictions:
            game = pred.game
            if (game and game.home_score is not None and game.away_score is not None 
                and pred.over_under_line is not None):
                
                predicted_total = pred.predicted_home_score + pred.predicted_away_score
                actual_total = game.home_score + game.away_score
                line = pred.over_under_line
                
                # 假設我們根據預測下注
                if abs(predicted_total - line) > 0.5:  # 只在有足夠信心時下注
                    bet_over = predicted_total > line
                    actual_over = actual_total > line
                    
                    if bet_over == actual_over:
                        over_under_profit += 0.91  # 贏得0.91單位 (考慮-110賠率)
                    else:
                        over_under_profit -= 1.0   # 輸掉1單位
                    
                    over_under_bets += 1
        
        roi = (over_under_profit / over_under_bets * 100) if over_under_bets > 0 else 0
        
        return {
            'over_under_profit': round(over_under_profit, 2),
            'over_under_bets': over_under_bets,
            'roi_percentage': round(roi, 2)
        }
    
    def generate_backtest_report(self, results: Dict, output_file: str = None) -> str:
        """生成回測報告"""
        report_lines = []
        report_lines.append("# MLB預測系統回測驗證報告")
        report_lines.append(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        for model_name, model_results in results.items():
            if not model_results:
                continue
                
            report_lines.append(f"## 📊 {model_name} 模型表現")
            report_lines.append(f"**分析期間**: {model_results.get('date_range', 'N/A')}")
            report_lines.append(f"**總預測數**: {model_results.get('total_predictions', 0)}")
            report_lines.append("")
            
            # 勝負預測準確性
            win_loss = model_results.get('win_loss_accuracy', {})
            if win_loss:
                report_lines.append("### 🎯 勝負預測準確性")
                report_lines.append(f"- **準確率**: {win_loss.get('accuracy', 0)}%")
                report_lines.append(f"- **正確預測**: {win_loss.get('correct', 0)}/{win_loss.get('total', 0)}")
                report_lines.append("")
            
            # 分數預測準確性
            score_acc = model_results.get('score_accuracy', {})
            if score_acc:
                report_lines.append("### 📈 分數預測準確性")
                report_lines.append(f"- **平均總分誤差**: {score_acc.get('mean_total_error', 0)} 分")
                report_lines.append(f"- **中位數誤差**: {score_acc.get('median_total_error', 0)} 分")
                report_lines.append(f"- **標準差**: {score_acc.get('std_total_error', 0)} 分")
                report_lines.append("")
            
            # 大小分預測準確性
            over_under = model_results.get('over_under_accuracy', {})
            if over_under:
                report_lines.append("### 🎲 大小分預測準確性")
                report_lines.append(f"- **準確率**: {over_under.get('accuracy', 0)}%")
                report_lines.append(f"- **正確預測**: {over_under.get('correct', 0)}/{over_under.get('total', 0)}")
                report_lines.append("")
            
            # 信心度分析
            confidence = model_results.get('confidence_analysis', {})
            if confidence:
                report_lines.append("### 🎯 信心度分析")
                for level, data in confidence.items():
                    if data.get('total', 0) > 0:
                        report_lines.append(f"- **{level.upper()}信心度 ({data.get('range', 'N/A')})**: "
                                          f"{data.get('accuracy', 0)}% ({data.get('correct', 0)}/{data.get('total', 0)})")
                report_lines.append("")
            
            # 博彩表現
            betting = model_results.get('betting_performance', {})
            if betting and betting.get('over_under_bets', 0) > 0:
                report_lines.append("### 💰 博彩表現")
                report_lines.append(f"- **大小分投注**: {betting.get('over_under_bets', 0)} 場")
                report_lines.append(f"- **總盈虧**: {betting.get('over_under_profit', 0)} 單位")
                report_lines.append(f"- **投資回報率**: {betting.get('roi_percentage', 0)}%")
                report_lines.append("")
            
            report_lines.append("---")
            report_lines.append("")
        
        report_content = "\n".join(report_lines)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            logger.info(f"回測報告已保存到: {output_file}")
        
        return report_content

def main():
    """主函數"""
    backtest_system = BacktestValidationSystem()
    
    # 設置回測期間 (最近30天)
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    print(f"🚀 開始回測驗證系統")
    print(f"📅 分析期間: {start_date} 到 {end_date}")
    
    # 運行回測分析
    results = backtest_system.run_backtest_analysis(
        start_date=start_date.isoformat(),
        end_date=end_date.isoformat(),
        model_versions=['Core_v1.0', 'unified_v1.0']
    )
    
    # 生成報告
    report_file = f"backtest_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    report_content = backtest_system.generate_backtest_report(results, report_file)
    
    print("\n" + "="*60)
    print("📋 回測驗證報告預覽:")
    print("="*60)
    print(report_content[:1000] + "..." if len(report_content) > 1000 else report_content)
    
    print(f"\n✅ 回測驗證完成！完整報告已保存到: {report_file}")

if __name__ == "__main__":
    main()
