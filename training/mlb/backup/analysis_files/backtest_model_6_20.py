#!/usr/bin/env python3
"""
6/20 回測實驗 - 模型調校和訓練時間長度測試
只使用 6/19 及之前的數據訓練模型，預測 6/20 的比賽
"""

import sys
import os
from datetime import date, timedelta
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, r2_score
import joblib

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, BoxScore, TeamStats, Prediction
from models.feature_engineer import FeatureEngineer

def backtest_6_20():
    """6/20 回測實驗"""
    print("🎯 6/20 回測實驗 - 模型調校和時間長度測試")
    print("=" * 80)
    
    # 定義關鍵日期
    target_date = date(2025, 6, 20)  # 預測目標日期
    cutoff_date = date(2025, 6, 19)  # 訓練數據截止日期
    
    print(f"📅 預測目標日期: {target_date}")
    print(f"📅 訓練數據截止: {cutoff_date} (含)")
    print(f"📅 測試原則: 只使用 {cutoff_date} 及之前的數據訓練模型")
    
    app = create_app()
    
    with app.app_context():
        # 1. 檢查目標日期的比賽
        target_games = Game.query.filter_by(date=target_date).all()
        completed_target_games = [g for g in target_games if g.game_status == 'completed']
        
        print(f"\n🏟️  {target_date} 比賽情況:")
        print(f"   總比賽數: {len(target_games)}")
        print(f"   已完成: {len(completed_target_games)}")
        print(f"   可用於驗證: {'✅' if len(completed_target_games) > 0 else '❌'}")
        
        if len(completed_target_games) == 0:
            print(f"❌ {target_date} 沒有已完成的比賽，無法進行回測")
            return
        
        # 顯示目標比賽
        print(f"\n📋 目標比賽列表:")
        for i, game in enumerate(completed_target_games, 1):
            print(f"   [{i:2d}] {game.away_team} @ {game.home_team} "
                  f"({game.away_score}-{game.home_score})")
        
        # 2. 測試不同的訓練時間長度
        training_periods = [
            ("7天", 7),
            ("14天", 14), 
            ("30天", 30),
            ("60天", 60),
            ("90天", 90)
        ]
        
        results = []
        
        for period_name, days in training_periods:
            print(f"\n{'='*60}")
            print(f"🔄 測試訓練期間: {period_name} ({days}天)")
            print(f"{'='*60}")
            
            # 計算訓練數據起始日期
            training_start = cutoff_date - timedelta(days=days-1)
            
            print(f"   訓練數據範圍: {training_start} 到 {cutoff_date}")
            
            # 獲取訓練數據
            training_games = Game.query.filter(
                Game.date >= training_start,
                Game.date <= cutoff_date,
                Game.game_status == 'completed'
            ).all()
            
            print(f"   訓練比賽數量: {len(training_games)}")
            
            if len(training_games) < 50:  # 最少需要50場比賽
                print(f"   ⚠️  訓練數據不足 (<50場)，跳過此期間")
                continue
            
            # 訓練模型
            model_result = train_model_for_period(
                training_games, completed_target_games, period_name, days
            )
            
            if model_result:
                results.append(model_result)
        
        # 3. 比較結果
        print(f"\n{'='*80}")
        print(f"📊 回測結果比較")
        print(f"{'='*80}")
        
        if results:
            # 創建結果表格
            df_results = pd.DataFrame(results)
            
            print(f"\n📈 準確率比較:")
            print(f"{'期間':<10} {'訓練場次':<8} {'主隊得分MAE':<12} {'客隊得分MAE':<12} {'勝負準確率':<10}")
            print(f"{'-'*60}")
            
            for _, row in df_results.iterrows():
                print(f"{row['period']:<10} {row['training_games']:<8} "
                      f"{row['home_mae']:<12.2f} {row['away_mae']:<12.2f} "
                      f"{row['win_accuracy']:<10.1f}%")
            
            # 找出最佳模型
            best_home = df_results.loc[df_results['home_mae'].idxmin()]
            best_away = df_results.loc[df_results['away_mae'].idxmin()]
            best_win = df_results.loc[df_results['win_accuracy'].idxmax()]
            
            print(f"\n🏆 最佳表現:")
            print(f"   主隊得分預測: {best_home['period']} (MAE: {best_home['home_mae']:.2f})")
            print(f"   客隊得分預測: {best_away['period']} (MAE: {best_away['away_mae']:.2f})")
            print(f"   勝負預測: {best_win['period']} (準確率: {best_win['win_accuracy']:.1f}%)")
            
        else:
            print(f"❌ 沒有成功的測試結果")

def train_model_for_period(training_games, target_games, period_name, days):
    """為特定時間期間訓練模型"""
    try:
        print(f"   🔧 開始訓練 {period_name} 模型...")
        
        # 1. 特徵工程
        feature_engineer = FeatureEngineer()
        
        # 提取訓練特徵
        training_features = []
        training_targets = {
            'home_score': [],
            'away_score': [],
            'home_win': []
        }
        
        valid_training_count = 0
        
        for game in training_games:
            try:
                features = feature_engineer.extract_comprehensive_features(
                    game.home_team, game.away_team, game.date
                )
                if features is not None and len(features) > 0:
                    # 轉換字典為數值列表
                    feature_values = list(features.values())
                    training_features.append(feature_values)
                    training_targets['home_score'].append(game.home_score or 0)
                    training_targets['away_score'].append(game.away_score or 0)
                    training_targets['home_win'].append(1 if (game.home_score or 0) > (game.away_score or 0) else 0)
                    valid_training_count += 1
            except Exception as e:
                continue
        
        if valid_training_count < 30:
            print(f"   ❌ 有效訓練數據不足: {valid_training_count}")
            return None
        
        print(f"   ✅ 有效訓練數據: {valid_training_count} 場")
        
        # 轉換為numpy數組
        X_train = np.array(training_features)
        
        # 2. 訓練三個模型
        models = {}
        
        # 主隊得分模型
        models['home_score'] = RandomForestRegressor(
            n_estimators=100, 
            max_depth=10, 
            random_state=42
        )
        models['home_score'].fit(X_train, training_targets['home_score'])
        
        # 客隊得分模型
        models['away_score'] = RandomForestRegressor(
            n_estimators=100, 
            max_depth=10, 
            random_state=42
        )
        models['away_score'].fit(X_train, training_targets['away_score'])
        
        # 勝負模型
        models['home_win'] = RandomForestRegressor(
            n_estimators=100, 
            max_depth=10, 
            random_state=42
        )
        models['home_win'].fit(X_train, training_targets['home_win'])
        
        print(f"   ✅ 模型訓練完成")
        
        # 3. 預測目標比賽
        predictions = []
        actuals = {
            'home_score': [],
            'away_score': [],
            'home_win': []
        }
        
        valid_predictions = 0
        
        for game in target_games:
            try:
                features = feature_engineer.extract_comprehensive_features(
                    game.home_team, game.away_team, game.date
                )
                if features is not None and len(features) > 0:
                    # 轉換字典為數值列表
                    feature_values = list(features.values())
                    features_array = np.array([feature_values])

                    pred_home = models['home_score'].predict(features_array)[0]
                    pred_away = models['away_score'].predict(features_array)[0]
                    pred_win = models['home_win'].predict(features_array)[0]
                    
                    predictions.append({
                        'game_id': game.game_id,
                        'home_team': game.home_team,
                        'away_team': game.away_team,
                        'pred_home': pred_home,
                        'pred_away': pred_away,
                        'pred_win': pred_win,
                        'actual_home': game.home_score,
                        'actual_away': game.away_score,
                        'actual_win': 1 if game.home_score > game.away_score else 0
                    })
                    
                    actuals['home_score'].append(game.home_score)
                    actuals['away_score'].append(game.away_score)
                    actuals['home_win'].append(1 if game.home_score > game.away_score else 0)
                    
                    valid_predictions += 1
                    
            except Exception as e:
                continue
        
        if valid_predictions == 0:
            print(f"   ❌ 無法生成有效預測")
            return None
        
        print(f"   ✅ 成功預測: {valid_predictions} 場比賽")
        
        # 4. 計算準確率
        pred_home_scores = [p['pred_home'] for p in predictions]
        pred_away_scores = [p['pred_away'] for p in predictions]
        pred_wins = [1 if p['pred_win'] > 0.5 else 0 for p in predictions]
        
        home_mae = mean_absolute_error(actuals['home_score'], pred_home_scores)
        away_mae = mean_absolute_error(actuals['away_score'], pred_away_scores)
        win_accuracy = sum(1 for i in range(len(pred_wins)) 
                          if pred_wins[i] == actuals['home_win'][i]) / len(pred_wins) * 100
        
        print(f"   📊 性能指標:")
        print(f"      主隊得分 MAE: {home_mae:.2f}")
        print(f"      客隊得分 MAE: {away_mae:.2f}")
        print(f"      勝負準確率: {win_accuracy:.1f}%")
        
        # 顯示詳細預測結果
        print(f"\n   📋 詳細預測結果:")
        for p in predictions[:5]:  # 只顯示前5場
            print(f"      {p['away_team']} @ {p['home_team']}: "
                  f"預測 {p['pred_away']:.1f}-{p['pred_home']:.1f}, "
                  f"實際 {p['actual_away']}-{p['actual_home']}")
        
        return {
            'period': period_name,
            'days': days,
            'training_games': valid_training_count,
            'prediction_games': valid_predictions,
            'home_mae': home_mae,
            'away_mae': away_mae,
            'win_accuracy': win_accuracy,
            'predictions': predictions
        }
        
    except Exception as e:
        print(f"   ❌ 訓練失敗: {e}")
        return None

if __name__ == "__main__":
    backtest_6_20()
