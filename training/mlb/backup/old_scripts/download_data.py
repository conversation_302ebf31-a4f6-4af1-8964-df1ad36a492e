#!/usr/bin/env python3
"""
MLB數據下載控制腳本
"""

import sys
import os
import argparse
from datetime import datetime, date, timedelta

# 添加當前目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.download_manager import DownloadManager
from models.smart_downloader import SmartDownloader
from models.database import db

def download_complete_data():
    """下載完整數據（智能模式）"""
    print("=== MLB 完整數據下載（智能模式）===")
    print("分析缺失數據並智能下載...")

    app = create_app()

    with app.app_context():
        downloader = SmartDownloader()

        # 分析缺失數據
        print("\n📋 分析數據完整性...")
        status = downloader.get_download_status()

        print(f"數據完整性: {status['completeness_percentage']:.1f}%")
        print(f"缺失天數: {len(status['download_plan']['missing_dates'])}")
        print(f"預估時間: {status['download_plan']['estimated_time_minutes']} 分鐘")

        if len(status['download_plan']['missing_dates']) == 0:
            print("✅ 所有數據已是最新！")
            return

        # 詢問用戶是否繼續
        print(f"\n將下載 {len(status['download_plan']['missing_dates'])} 天的數據")
        response = input("是否繼續下載？(y/N): ").lower().strip()

        if response != 'y':
            print("下載已取消")
            return

        # 開始下載
        print("\n🚀 開始智能下載...")
        success = downloader.download_complete_history()

        if success:
            print("\n✅ 完整數據下載完成！")
            show_final_statistics(downloader)
        else:
            print("\n❌ 下載過程中出現錯誤！")

def download_historical_data():
    """下載前五年歷史數據（傳統模式）"""
    print("=== MLB 歷史數據下載（傳統模式）===")
    print("準備下載 2019-2024 年的完整MLB數據...")

    app = create_app()

    with app.app_context():
        manager = DownloadManager()

        # 下載前五年數據
        years = [2019, 2020, 2021, 2022, 2023, 2024, 2025]

        print(f"開始下載 {years} 年的數據...")
        print("這可能需要較長時間，請耐心等待...")

        success = manager.download_historical_data(years)

        if success:
            print("\n✅ 歷史數據下載完成！")
            show_final_statistics(manager)
        else:
            print("\n❌ 歷史數據下載失敗！")
            progress = manager.load_progress()
            if progress and progress.error_message:
                print(f"錯誤信息: {progress.error_message}")

def show_final_statistics(manager):
    """顯示最終統計信息"""
    stats = manager.get_download_statistics()
    print("\n📊 最終統計:")
    print(f"  - 球隊數量: {stats['total_teams']}")
    print(f"  - 比賽記錄: {stats['total_games']}")
    print(f"  - 球隊統計: {stats['total_team_stats']}")
    print(f"  - 球員統計: {stats['total_player_stats']}")

    if stats['date_range']:
        print(f"  - 數據範圍: {stats['date_range']['start']} 到 {stats['date_range']['end']}")

    if stats['games_by_year']:
        print("  - 各年比賽數量:")
        for year, count in sorted(stats['games_by_year'].items()):
            print(f"    {year}: {count} 場")

def download_daily_update():
    """每日數據更新（智能模式）"""
    print("=== MLB 每日數據更新（智能模式）===")

    app = create_app()

    with app.app_context():
        downloader = SmartDownloader()

        print("📅 執行每日增量更新...")
        success = downloader.download_daily_update()

        if success:
            print("✅ 每日更新完成！")

            # 顯示更新後的統計
            status = downloader.get_download_status()
            print(f"\n📊 更新後統計:")
            print(f"  - 數據完整性: {status['completeness_percentage']:.1f}%")
            print(f"  - 總比賽記錄: {status['database_stats']['total_games']}")

        else:
            print("❌ 每日更新失敗！")

def repair_missing_data():
    """修復缺失數據"""
    print("=== 修復缺失的MLB數據 ===")

    app = create_app()

    with app.app_context():
        downloader = SmartDownloader()

        print("🔍 檢查數據完整性...")
        status = downloader.get_download_status()

        missing_count = len(status['download_plan']['missing_dates'])
        if missing_count == 0:
            print("✅ 沒有發現缺失的數據！")
            return

        print(f"發現 {missing_count} 天的缺失數據")
        print(f"數據完整性: {status['completeness_percentage']:.1f}%")

        # 顯示一些缺失的日期
        missing_dates = status['download_plan']['missing_dates']
        print(f"缺失日期範例: {missing_dates[:5]}")
        if len(missing_dates) > 5:
            print(f"... 還有 {len(missing_dates) - 5} 個缺失日期")

        response = input("\n是否開始修復？(y/N): ").lower().strip()
        if response != 'y':
            print("修復已取消")
            return

        print("🔧 開始修復缺失數據...")
        success = downloader.repair_missing_data()

        if success:
            print("✅ 數據修復完成！")
        else:
            print("❌ 數據修復失敗！")

def analyze_data_status():
    """分析數據狀態"""
    print("=== MLB 數據狀態分析 ===")

    app = create_app()

    with app.app_context():
        downloader = SmartDownloader()

        print("📊 分析數據庫狀態...")
        status = downloader.get_download_status()

        print(f"\n📈 數據完整性報告:")
        print(f"  - 完整性: {status['completeness_percentage']:.1f}%")
        print(f"  - 總可能天數: {status['total_possible_days']}")
        print(f"  - 缺失天數: {len(status['download_plan']['missing_dates'])}")
        print(f"  - 優先修復: {len(status['download_plan']['priority_dates'])} 天")

        print(f"\n📊 數據庫統計:")
        db_stats = status['database_stats']
        print(f"  - 球隊: {db_stats['total_teams']}")
        print(f"  - 比賽: {db_stats['total_games']}")
        print(f"  - 球隊統計: {db_stats['total_team_stats']}")
        print(f"  - 球員統計: {db_stats['total_player_stats']}")

        if db_stats['date_range']:
            print(f"  - 數據範圍: {db_stats['date_range']['start']} 到 {db_stats['date_range']['end']}")

        if db_stats['games_by_year']:
            print(f"\n📅 各年比賽數量:")
            for year, count in sorted(db_stats['games_by_year'].items()):
                print(f"    {year}: {count} 場")

        print(f"\n⚙️ 下載配置:")
        config = status['config']
        print(f"  - 起始年份: {config['start_year']}")
        print(f"  - 批次大小: {config['batch_size']} 天")
        print(f"  - 每日最大下載: {config['max_daily_downloads']} 天")
        print(f"  - 包含比賽詳情: {config['include_game_details']}")
        print(f"  - 包含球員信息: {config['include_player_info']}")

def download_date_range(start_date_str: str, end_date_str: str):
    """下載指定日期範圍的數據"""
    print(f"=== 下載 {start_date_str} 到 {end_date_str} 的數據 ===")
    
    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    except ValueError:
        print("❌ 日期格式錯誤！請使用 YYYY-MM-DD 格式")
        return
    
    if start_date > end_date:
        print("❌ 開始日期不能晚於結束日期！")
        return
    
    app = create_app()
    
    with app.app_context():
        manager = DownloadManager()
        
        success = manager.download_date_range(start_date, end_date)
        
        if success:
            print("✅ 指定範圍數據下載完成！")
        else:
            print("❌ 指定範圍數據下載失敗！")

def show_download_status():
    """顯示下載狀態"""
    print("=== MLB 數據下載狀態 ===")
    
    app = create_app()
    
    with app.app_context():
        manager = DownloadManager()
        
        # 顯示當前進度
        progress = manager.load_progress()
        if progress:
            print(f"\n📋 最近任務: {progress.task_name}")
            print(f"   狀態: {progress.status}")
            print(f"   開始時間: {progress.start_time}")
            if progress.end_time:
                print(f"   結束時間: {progress.end_time}")
            print(f"   進度: {progress.completed_items}/{progress.total_items} ({progress.progress_percentage:.1f}%)")
            if progress.current_item:
                print(f"   當前項目: {progress.current_item}")
            if progress.error_message:
                print(f"   錯誤信息: {progress.error_message}")
        else:
            print("沒有找到下載進度記錄")
        
        # 顯示數據庫統計
        stats = manager.get_download_statistics()
        print("\n📊 數據庫統計:")
        print(f"  - 球隊數量: {stats['total_teams']}")
        print(f"  - 比賽記錄: {stats['total_games']}")
        print(f"  - 球隊統計: {stats['total_team_stats']}")
        print(f"  - 球員統計: {stats['total_player_stats']}")
        
        if stats['date_range']:
            print(f"  - 數據範圍: {stats['date_range']['start']} 到 {stats['date_range']['end']}")
        
        if stats['last_update']:
            print(f"  - 最後更新: {stats['last_update']}")
        
        if stats['games_by_year']:
            print("  - 各年比賽數量:")
            for year, count in sorted(stats['games_by_year'].items()):
                print(f"    {year}: {count} 場")

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='MLB數據下載工具（智能版）')
    parser.add_argument('command', choices=[
        'complete', 'historical', 'daily', 'range', 'status', 'repair', 'analyze'
    ], help='下載命令')
    parser.add_argument('--start-date', help='開始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='結束日期 (YYYY-MM-DD)')
    parser.add_argument('--force', action='store_true', help='強制重新下載')

    args = parser.parse_args()

    if args.command == 'complete':
        download_complete_data()
    elif args.command == 'historical':
        download_historical_data()
    elif args.command == 'daily':
        download_daily_update()
    elif args.command == 'range':
        if not args.start_date or not args.end_date:
            print("❌ 範圍下載需要指定 --start-date 和 --end-date")
            return
        download_date_range(args.start_date, args.end_date)
    elif args.command == 'status':
        show_download_status()
    elif args.command == 'repair':
        repair_missing_data()
    elif args.command == 'analyze':
        analyze_data_status()

if __name__ == '__main__':
    if len(sys.argv) == 1:
        # 如果沒有參數，顯示幫助信息
        print("🏟️ MLB數據下載工具（智能版）")
        print("\n📋 可用命令:")
        print("  complete     # 智能下載完整數據（推薦）")
        print("  historical   # 下載前五年歷史數據（傳統模式）")
        print("  daily        # 每日數據更新")
        print("  repair       # 修復缺失數據")
        print("  analyze      # 分析數據狀態")
        print("  status       # 顯示下載狀態")
        print("  range        # 下載指定日期範圍")
        print("\n🚀 推薦使用方式:")
        print("  python download_data.py complete       # 首次使用，智能下載所有數據")
        print("  python download_data.py daily          # 日常使用，增量更新")
        print("  python download_data.py repair         # 發現缺失時修復")
        print("  python download_data.py analyze        # 查看數據狀態")
        print("\n📖 其他範例:")
        print("  python download_data.py range --start-date 2024-01-01 --end-date 2024-01-31")
        print("  python download_data.py historical --force")
        print("\n💡 提示: 首次使用建議執行 'complete' 命令進行智能下載")
    else:
        main()
