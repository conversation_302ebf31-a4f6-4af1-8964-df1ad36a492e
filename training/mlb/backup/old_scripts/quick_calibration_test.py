#!/usr/bin/env python3
"""
快速測試校正效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.ml_predictor import MLBPredictor
from models.over_under_predictor import OverUnderPredictor
import sqlite3

def quick_test():
    """快速測試校正效果"""
    print("🎯 快速校正效果測試")
    print("=" * 40)
    
    app = create_app()
    with app.app_context():
        # 測試ML預測器校正
        print("1. ML預測器校正測試:")
        ml_predictor = MLBPredictor()
        
        test_cases = [
            (8.0, 6.0, "典型高估案例"),
            (10.0, 9.0, "極端高估案例")
        ]
        
        for home, away, desc in test_cases:
            calibrated_home, calibrated_away = ml_predictor._apply_score_calibration(home, away)
            original_total = home + away
            calibrated_total = calibrated_home + calibrated_away
            reduction = original_total - calibrated_total
            
            print(f"  {desc}: {home:.1f}-{away:.1f} ({original_total:.1f}) → {calibrated_home:.1f}-{calibrated_away:.1f} ({calibrated_total:.1f}) [減少{reduction:.1f}分]")
        
        # 測試大小分預測器校正
        print(f"\n2. 大小分預測器校正測試:")
        ou_predictor = OverUnderPredictor(app)
        
        for home, away, desc in test_cases:
            calibrated_home, calibrated_away = ou_predictor._apply_score_calibration(home, away)
            original_total = home + away
            calibrated_total = calibrated_home + calibrated_away
            reduction = original_total - calibrated_total
            
            print(f"  {desc}: {home:.1f}-{away:.1f} ({original_total:.1f}) → {calibrated_home:.1f}-{calibrated_away:.1f} ({calibrated_total:.1f}) [減少{reduction:.1f}分]")
        
        # 檢查數據庫中的最新預測
        print(f"\n3. 檢查最新預測數據:")
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        # 檢查最新的預測記錄
        cursor.execute("""
            SELECT 
                p.predicted_home_score,
                p.predicted_away_score,
                (p.predicted_home_score + p.predicted_away_score) as total,
                g.home_team,
                g.away_team,
                p.prediction_date
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            ORDER BY p.prediction_date DESC
            LIMIT 5
        """)
        
        recent_predictions = cursor.fetchall()
        
        if recent_predictions:
            print("  最新5個預測:")
            for pred in recent_predictions:
                home_score, away_score, total, home_team, away_team, pred_date = pred
                print(f"    {away_team} @ {home_team}: {away_score:.1f}-{home_score:.1f} (總分: {total:.1f}) [{pred_date}]")
        else:
            print("  沒有找到最新預測數據")
        
        conn.close()
        
        print(f"\n✅ 校正測試完成！")
        print("如果看到總分在6-11分範圍內，說明校正正常工作")

if __name__ == "__main__":
    quick_test()
