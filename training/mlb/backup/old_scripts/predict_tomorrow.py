#!/usr/bin/env python3
"""
生成明天的MLB比賽預測
"""

from datetime import datetime, timedelta
import sys
import os

from models.intelligent_predictor import IntelligentPredictor
from models.database import db, Game
from flask import Flask
from config import config

def predict_tomorrow():
    """生成明天的比賽預測"""
    app = Flask(__name__)
    app.config.from_object(config['development'])
    db.init_app(app)

    # 計算明天的日期
    tomorrow = datetime.now().date() + timedelta(days=1)
    print(f'🎯 生成明天 {tomorrow} 的比賽預測...')

    with app.app_context():
        # 獲取明天的比賽
        tomorrow_games = Game.query.filter(Game.date == tomorrow).all()
        print(f'📅 明天有 {len(tomorrow_games)} 場比賽')
        
        if not tomorrow_games:
            print('❌ 沒有找到明天的比賽數據')
            return
        
        # 初始化智能預測器
        try:
            predictor = IntelligentPredictor()
            print('✅ 智能預測器初始化成功')
        except Exception as e:
            print(f'❌ 預測器初始化失敗: {e}')
            return
        
        print('\n🔥 明天比賽預測結果:')
        print('=' * 60)
        
        for i, game in enumerate(tomorrow_games, 1):
            print(f'\n🏟️ 比賽 {i}: {game.away_team} @ {game.home_team}')
            print(f'   時間: {game.date}')
            
            try:
                # 生成預測
                prediction = predictor.predict_game_intelligent(game.home_team, game.away_team, game.date, game.game_id)
                
                away_score = prediction.get("predicted_away_score", 0)
                home_score = prediction.get("predicted_home_score", 0)
                total_score = away_score + home_score
                confidence = prediction.get("confidence", 60.0)
                strategy_name = prediction.get("strategy", {}).get("name", "標準")
                
                print(f'   🎯 預測: {away_score:.1f} - {home_score:.1f}')
                print(f'   📊 總分: {total_score:.1f}')
                print(f'   💯 信心度: {confidence:.1f}%')
                print(f'   🧠 策略: {strategy_name}')
                
                if prediction.get('home_win_prob'):
                    home_team_win_prob = prediction['home_win_prob'] * 100
                    away_team_win_prob = 100 - home_team_win_prob
                    print(f'   🏆 勝率: {game.home_team} {home_team_win_prob:.1f}% vs {game.away_team} {away_team_win_prob:.1f}%')
                
            except Exception as e:
                print(f'   ❌ 預測失敗: {e}')
                continue
            
            # 限制顯示前10場比賽
            if i >= 10:
                print(f'\n... 還有 {len(tomorrow_games) - 10} 場比賽')
                break
        
        print('\n✅ 明天比賽預測完成!')

if __name__ == '__main__':
    predict_tomorrow()