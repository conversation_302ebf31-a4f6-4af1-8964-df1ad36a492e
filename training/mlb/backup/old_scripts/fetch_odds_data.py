#!/usr/bin/env python3
"""
賠率數據抓取執行腳本
抓取讓分盤 (spreads) 和大小分 (totals) 賠率數據並存儲到數據庫
"""

import sys
import os
from datetime import date, datetime, timedelta

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, BettingOdds
from models.odds_data_fetcher import OddsDataFetcher

def test_api_connection():
    """測試API連接狀態"""
    print("🔑 測試賠率API連接狀態")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        fetcher = OddsDataFetcher(app)
        
        # 檢查API狀態
        status = fetcher.check_api_status()
        
        print(f"API密鑰配置: {'✅ 已配置' if status['api_key_configured'] else '❌ 未配置'}")
        print(f"API連接狀態: {'✅ 正常' if status['api_accessible'] else '❌ 異常'}")
        print(f"剩餘請求次數: {status.get('remaining_requests', 'N/A')}")
        print(f"狀態訊息: {status['message']}")
        
        return status['api_accessible']

def fetch_odds_for_date(target_date: date = None):
    """抓取指定日期的賠率數據"""
    if target_date is None:
        target_date = date.today()
    
    print(f"\n📊 抓取 {target_date} 的賠率數據")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        fetcher = OddsDataFetcher(app)
        
        # 檢查該日期的比賽
        games = Game.query.filter(Game.date == target_date).all()
        print(f"找到 {len(games)} 場比賽:")
        
        for game in games:
            print(f"  - {game.away_team} @ {game.home_team} ({game.game_status})")
        
        if not games:
            print("❌ 沒有比賽記錄，無法抓取賠率")
            return None
        
        # 抓取賠率數據
        print(f"\n🔄 正在抓取賠率數據...")
        odds_result = fetcher.fetch_odds_for_games(target_date)
        
        if odds_result['success']:
            print(f"✅ 成功抓取賠率數據")
            print(f"   總比賽數: {odds_result['total_games']}")
            print(f"   匹配比賽數: {odds_result['matched_games']}")
            print(f"   匹配率: {odds_result['summary']['match_rate']:.1f}%")
            print(f"   可用市場: {', '.join(odds_result['summary']['available_markets'])}")
            print(f"   博彩商: {', '.join(odds_result['summary']['bookmakers'])}")
            
            # 顯示匹配的比賽詳情
            print(f"\n📋 匹配的比賽詳情:")
            for game_data in odds_result['games']:
                print(f"\n🏟️  {game_data['away_team']} @ {game_data['home_team']}")
                print(f"   比賽ID: {game_data['game_id']}")
                
                odds = game_data['odds']
                
                # 顯示讓分盤
                if odds.get('spreads'):
                    print(f"   📈 讓分盤:")
                    for bookmaker, spreads in odds['spreads'].items():
                        print(f"     {bookmaker}:")
                        for team, data in spreads.items():
                            point = data.get('point', 0)
                            price = data.get('price', 0)
                            print(f"       {team}: {point:+} ({price:+})")
                
                # 顯示大小分
                if odds.get('totals'):
                    print(f"   🎯 大小分:")
                    for bookmaker, totals in odds['totals'].items():
                        print(f"     {bookmaker}:")
                        for outcome, data in totals.items():
                            point = data.get('point', 0)
                            price = data.get('price', 0)
                            print(f"       {outcome} {point}: {price:+}")
            
            return odds_result
        else:
            print(f"❌ 抓取賠率數據失敗: {odds_result['error']}")
            return None

def save_odds_to_database(odds_result):
    """將賠率數據保存到數據庫"""
    if not odds_result:
        print("❌ 沒有賠率數據可保存")
        return
    
    print(f"\n💾 保存賠率數據到數據庫")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        fetcher = OddsDataFetcher(app)
        
        # 保存數據
        save_result = fetcher.save_odds_to_database(odds_result)
        
        if save_result['success']:
            print(f"✅ 賠率數據保存成功")
            print(f"   新增記錄: {save_result['saved_count']}")
            print(f"   更新記錄: {save_result['updated_count']}")
            print(f"   總處理數: {save_result['total_processed']}")
        else:
            print(f"❌ 保存賠率數據失敗: {save_result['error']}")

def query_odds_from_database(game_id: str = None, target_date: date = None):
    """從數據庫查詢賠率數據"""
    print(f"\n🔍 查詢數據庫中的賠率數據")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        if game_id:
            # 查詢特定比賽
            fetcher = OddsDataFetcher(app)
            odds_data = fetcher.get_odds_for_game(game_id)
            
            if odds_data['success']:
                print(f"✅ 找到比賽 {game_id} 的賠率數據")
                print(f"   博彩商: {', '.join(odds_data['bookmakers'])}")
                
                if odds_data['spreads']:
                    print(f"   讓分盤: {len(odds_data['spreads'])} 個博彩商")
                if odds_data['totals']:
                    print(f"   大小分: {len(odds_data['totals'])} 個博彩商")
            else:
                print(f"❌ 沒有找到比賽 {game_id} 的賠率數據")
        
        elif target_date:
            # 查詢特定日期的所有賠率
            games = Game.query.filter(Game.date == target_date).all()
            
            total_odds = 0
            for game in games:
                odds_count = BettingOdds.query.filter_by(game_id=game.game_id).count()
                total_odds += odds_count
                
                if odds_count > 0:
                    print(f"✅ {game.away_team} @ {game.home_team}: {odds_count} 筆賠率記錄")
                else:
                    print(f"❌ {game.away_team} @ {game.home_team}: 沒有賠率記錄")
            
            print(f"\n📊 總計: {len(games)} 場比賽, {total_odds} 筆賠率記錄")
        
        else:
            # 查詢所有賠率統計
            total_odds = BettingOdds.query.count()
            spreads_count = BettingOdds.query.filter_by(market_type='spreads').count()
            totals_count = BettingOdds.query.filter_by(market_type='totals').count()
            
            print(f"📊 數據庫賠率統計:")
            print(f"   總賠率記錄: {total_odds}")
            print(f"   讓分盤記錄: {spreads_count}")
            print(f"   大小分記錄: {totals_count}")
            
            # 顯示最近的記錄
            recent_odds = BettingOdds.query.order_by(BettingOdds.created_at.desc()).limit(5).all()
            print(f"\n🕒 最近 5 筆記錄:")
            for odds in recent_odds:
                print(f"   {odds.game_id} - {odds.bookmaker} - {odds.market_type}")

def main():
    """主函數"""
    print("🎯 MLB 賠率數據抓取系統")
    print("=" * 80)
    
    # 1. 測試API連接
    if not test_api_connection():
        print("❌ API連接失敗，無法繼續")
        return
    
    # 2. 選擇日期 (默認今天)
    target_date = date.today()
    print(f"\n🗓️  目標日期: {target_date}")
    
    # 3. 抓取賠率數據
    odds_result = fetch_odds_for_date(target_date)
    
    # 4. 保存到數據庫
    if odds_result:
        save_odds_to_database(odds_result)
        
        # 5. 驗證保存結果
        query_odds_from_database(target_date=target_date)
    
    print(f"\n✅ 賠率數據抓取完成!")

if __name__ == "__main__":
    main()
