#!/usr/bin/env python3
"""
MLB博彩系統Web應用
簡化版本的Flask Web界面
"""

from flask import Flask, render_template, jsonify, request
from datetime import datetime
import os
import sys

# 添加系統路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 嘗試導入博彩系統模組
try:
    from betting_strategy_executor import BettingStrategyExecutor
    from profit_monitoring_system import ProfitMonitoringSystem
    print("✅ 博彩系統模組導入成功")
except ImportError as e:
    print(f"❌ 博彩系統模組導入失敗: {e}")
    BettingStrategyExecutor = None
    ProfitMonitoringSystem = None

app = Flask(__name__)

@app.route('/')
def index():
    """主頁 - 博彩系統控制台"""
    return render_template('betting_dashboard.html')

@app.route('/api/strategy')
def api_daily_strategy():
    """API: 生成今日投注策略"""
    try:
        if not BettingStrategyExecutor:
            return jsonify({'error': '博彩系統模組未就緒'}), 500
            
        executor = BettingStrategyExecutor()
        target_date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
        
        strategy = executor.generate_daily_betting_strategy(target_date)
        
        if 'error' in strategy:
            return jsonify(strategy), 400
            
        return jsonify({
            'status': 'success',
            'data': strategy,
            'generated_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'策略生成失敗: {str(e)}',
            'generated_at': datetime.now().isoformat()
        }), 500

@app.route('/api/monitoring')
def api_profit_monitoring():
    """API: 盈利監控報告"""
    try:
        if not ProfitMonitoringSystem:
            return jsonify({'error': '監控系統模組未就緒'}), 500
            
        monitor = ProfitMonitoringSystem()
        days_back = request.args.get('days', 7, type=int)
        
        report = monitor.generate_performance_report(days_back)
        
        if 'error' in report:
            return jsonify(report), 400
            
        return jsonify({
            'status': 'success',
            'data': report,
            'generated_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'監控報告生成失敗: {str(e)}',
            'generated_at': datetime.now().isoformat()
        }), 500

@app.route('/api/system-status')
def api_system_status():
    """API: 系統狀態檢查"""
    try:
        import sqlite3
        
        # 檢查核心文件
        core_files = [
            'betting_strategy_executor.py',
            'profit_monitoring_system.py',
            'dynamic_spread_optimizer.py', 
            'enhanced_total_predictor.py',
            'instance/mlb_data.db'
        ]
        
        file_status = {}
        for file in core_files:
            file_status[file] = os.path.exists(file)
        
        # 檢查資料庫
        db_status = {}
        try:
            conn = sqlite3.connect('instance/mlb_data.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM games")
            games_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM predictions")
            predictions_count = cursor.fetchone()[0]
            
            conn.close()
            
            db_status = {
                'connected': True,
                'games_count': games_count,
                'predictions_count': predictions_count
            }
            
        except Exception as e:
            db_status = {
                'connected': False,
                'error': str(e)
            }
        
        # 檢查模組可用性
        module_status = {
            'BettingStrategyExecutor': BettingStrategyExecutor is not None,
            'ProfitMonitoringSystem': ProfitMonitoringSystem is not None
        }
        
        all_systems_ok = (
            all(file_status.values()) and 
            db_status.get('connected', False) and 
            all(module_status.values())
        )
        
        return jsonify({
            'status': 'healthy' if all_systems_ok else 'degraded',
            'file_status': file_status,
            'database_status': db_status,
            'module_status': module_status,
            'overall_health': all_systems_ok,
            'checked_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'系統狀態檢查失敗: {str(e)}',
            'checked_at': datetime.now().isoformat()
        }), 500

@app.route('/api/quick-demo')
def api_quick_demo():
    """API: 快速系統演示"""
    try:
        demo_data = {}
        
        # 1. 嘗試生成策略
        if BettingStrategyExecutor:
            executor = BettingStrategyExecutor()
            strategy = executor.generate_daily_betting_strategy()
            if 'error' not in strategy:
                demo_data['strategy'] = {
                    'date': strategy['date'],
                    'total_games': strategy['total_games'],
                    'recommended_bets': strategy['recommended_bets'],
                    'top_bet': strategy['betting_recommendations'][0] if strategy['betting_recommendations'] else None
                }
        
        # 2. 嘗試生成監控報告
        if ProfitMonitoringSystem:
            monitor = ProfitMonitoringSystem()
            report = monitor.generate_performance_report(7)
            if 'error' not in report:
                metrics = report['overall_performance']
                demo_data['monitoring'] = {
                    'total_bets': metrics['total_bets'],
                    'win_rate': metrics['win_rate'],
                    'roi': metrics['overall_roi'],
                    'sharpe_ratio': metrics['sharpe_ratio']
                }
        
        return jsonify({
            'status': 'success',
            'demo_data': demo_data,
            'message': '系統演示完成',
            'generated_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'演示生成失敗: {str(e)}',
            'generated_at': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    print("🚀 啟動MLB博彩系統Web界面")
    print("🌐 Web地址: http://localhost:5001")
    print("📊 主控台: http://localhost:5001")
    print("📈 API文檔: http://localhost:5001/api/system-status")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=5001)