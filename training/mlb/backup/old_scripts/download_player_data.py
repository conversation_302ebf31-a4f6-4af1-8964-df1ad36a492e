#!/usr/bin/env python3
"""
下載球員和對戰歷史數據腳本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Team, Player, PlayerStats, BoxScore, PlayerGameStats
from models.data_fetcher import MLBDataFetcher
from models.detailed_data_fetcher import DetailedDataFetcher
from datetime import date, timedelta
import time

def download_player_and_historical_data():
    """下載球員和對戰歷史數據"""
    print("=" * 80)
    print("🚀 開始下載球員和對戰歷史數據")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 初始化數據獲取器
            data_fetcher = MLBDataFetcher()
            detailed_fetcher = DetailedDataFetcher()
            
            # 1. 下載球員基本信息
            print("\n1. 📋 下載球員基本信息")
            print("-" * 50)
            
            # 獲取所有球隊
            teams = Team.query.all()
            print(f"找到 {len(teams)} 支球隊")
            
            total_players = 0
            for i, team in enumerate(teams, 1):
                print(f"[{i}/{len(teams)}] 獲取 {team.team_name} 球員信息...")
                
                try:
                    # 獲取球隊球員名單
                    success = data_fetcher.fetch_team_roster(team.team_id)
                    if success:
                        print(f"  ✅ {team.team_name} 球員信息獲取成功")
                    else:
                        print(f"  ❌ {team.team_name} 球員信息獲取失敗")
                    
                    # 避免請求過於頻繁
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"  ❌ {team.team_name} 獲取失敗: {e}")
            
            # 檢查球員數量
            current_players = Player.query.count()
            print(f"\n✅ 當前球員總數: {current_players}")
            
            # 2. 下載球員統計數據
            print("\n2. 📊 下載球員統計數據")
            print("-" * 50)
            
            # 獲取最近幾個賽季的統計
            current_year = date.today().year
            seasons = [current_year, current_year - 1, current_year - 2]
            
            for season in seasons:
                print(f"\n獲取 {season} 賽季統計數據...")
                
                for i, team in enumerate(teams, 1):
                    print(f"  [{i}/{len(teams)}] {team.team_name} {season}賽季...")
                    
                    try:
                        success = data_fetcher.fetch_team_player_stats(team.team_id, season)
                        if success:
                            print(f"    ✅ 成功")
                        else:
                            print(f"    ❌ 失敗")
                        
                        time.sleep(0.5)
                        
                    except Exception as e:
                        print(f"    ❌ 錯誤: {e}")
            
            # 檢查統計數據
            current_stats = PlayerStats.query.count()
            print(f"\n✅ 當前球員統計記錄: {current_stats}")
            
            # 3. 下載詳細比賽數據（Box Score）
            print("\n3. ⚾ 下載詳細比賽數據")
            print("-" * 50)
            
            # 獲取最近30天的已完成比賽
            end_date = date.today()
            start_date = end_date - timedelta(days=30)
            
            print(f"獲取 {start_date} 到 {end_date} 的詳細比賽數據...")
            
            completed_games = Game.query.filter(
                Game.date >= start_date,
                Game.date <= end_date,
                Game.game_status == 'completed',
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).order_by(Game.date.desc()).all()
            
            print(f"找到 {len(completed_games)} 場已完成的比賽")
            
            success_count = 0
            for i, game in enumerate(completed_games, 1):
                print(f"[{i}/{len(completed_games)}] {game.away_team} @ {game.home_team} ({game.date})")
                
                try:
                    success = detailed_fetcher.fetch_game_boxscore(game.game_id)
                    if success:
                        success_count += 1
                        print(f"  ✅ 詳細數據獲取成功")
                    else:
                        print(f"  ❌ 詳細數據獲取失敗")
                    
                    # 避免請求過於頻繁
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"  ❌ 錯誤: {e}")
                
                # 每10場比賽顯示進度
                if i % 10 == 0:
                    print(f"  進度: {i}/{len(completed_games)} ({success_count} 成功)")
            
            print(f"\n✅ 詳細比賽數據: {success_count}/{len(completed_games)} 成功")
            
            # 4. 檢查最終數據統計
            print("\n4. 📈 最終數據統計")
            print("-" * 50)
            
            final_stats = {
                "比賽記錄": Game.query.count(),
                "球隊記錄": Team.query.count(),
                "球員記錄": Player.query.count(),
                "球員統計": PlayerStats.query.count(),
                "Box Score": BoxScore.query.count(),
                "球員比賽統計": PlayerGameStats.query.count()
            }
            
            for stat_name, count in final_stats.items():
                print(f"✅ {stat_name}: {count:,}")
            
            # 5. 顯示樣本數據
            print("\n5. 🔍 數據樣本")
            print("-" * 50)
            
            # 顯示球員樣本
            sample_players = Player.query.limit(5).all()
            print("球員樣本:")
            for player in sample_players:
                print(f"  - {player.full_name} ({player.primary_position}) - {player.current_team_id}")
            
            # 顯示統計樣本
            sample_stats = PlayerStats.query.limit(3).all()
            print(f"\n統計樣本:")
            for stat in sample_stats:
                print(f"  - {stat.player_name} ({stat.season}): AVG {stat.batting_avg:.3f}, ERA {stat.era:.2f}")
            
            # 顯示Box Score樣本
            sample_box_scores = BoxScore.query.limit(3).all()
            print(f"\nBox Score樣本:")
            for box_score in sample_box_scores:
                team_type = "主隊" if box_score.is_home else "客隊"
                print(f"  - 比賽 {box_score.game_id} {team_type}: {box_score.runs}分 {box_score.hits}安打")
            
            print("\n" + "=" * 80)
            print("🎉 球員和對戰歷史數據下載完成！")
            print("=" * 80)
            
            return True
            
        except Exception as e:
            print(f"\n❌ 下載過程中發生錯誤: {e}")
            return False

def download_specific_date_range():
    """下載特定日期範圍的數據"""
    print("\n" + "=" * 80)
    print("📅 下載特定日期範圍的詳細數據")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        try:
            detailed_fetcher = DetailedDataFetcher()
            
            # 設定日期範圍（最近一週）
            end_date = date.today()
            start_date = end_date - timedelta(days=7)
            
            print(f"日期範圍: {start_date} 到 {end_date}")
            
            current_date = start_date
            total_success = 0
            total_games = 0
            
            while current_date <= end_date:
                print(f"\n處理日期: {current_date}")
                
                # 獲取該日期的比賽
                daily_games = Game.query.filter(
                    Game.date == current_date,
                    Game.game_status == 'completed',
                    Game.home_score.isnot(None)
                ).all()
                
                if daily_games:
                    print(f"  找到 {len(daily_games)} 場比賽")
                    
                    success = detailed_fetcher.fetch_games_by_date(current_date)
                    if success:
                        total_success += len(daily_games)
                        print(f"  ✅ 成功獲取詳細數據")
                    else:
                        print(f"  ❌ 獲取詳細數據失敗")
                    
                    total_games += len(daily_games)
                else:
                    print(f"  沒有已完成的比賽")
                
                current_date += timedelta(days=1)
                time.sleep(1)
            
            print(f"\n✅ 總計: {total_success}/{total_games} 場比賽獲取成功")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 特定日期範圍下載失敗: {e}")
            return False

def main():
    """主函數"""
    print("🚀 MLB球員和對戰歷史數據下載工具")
    print("=" * 80)
    
    # 1. 下載球員和歷史數據
    success1 = download_player_and_historical_data()
    
    # 2. 下載特定日期範圍的詳細數據
    success2 = download_specific_date_range()
    
    if success1 and success2:
        print("\n🎉 所有數據下載完成！")
        print("\n📊 建議下一步操作:")
        print("1. 訪問球員頁面查看球員信息: http://localhost:5500/players/")
        print("2. 查看球員統計排行榜: http://localhost:5500/players/stats")
        print("3. 瀏覽比賽詳情頁面查看Box Score: http://localhost:5500/games/")
        print("4. 在管理員面板中監控數據狀態: http://localhost:5500/admin/")
    else:
        print("\n❌ 部分數據下載失敗，請檢查網絡連接和API狀態")

if __name__ == "__main__":
    main()
