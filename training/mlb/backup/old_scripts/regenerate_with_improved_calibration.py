#!/usr/bin/env python3
"""
使用改進的校正算法重新生成預測
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction
from models.unified_betting_predictor import UnifiedBettingPredictor
from datetime import date, timedelta
from sqlalchemy import text

def regenerate_recent_predictions():
    """重新生成最近的預測"""
    print("🔄 使用改進校正算法重新生成預測...")
    
    app = create_app()
    with app.app_context():
        # 獲取最近3天的比賽
        end_date = date.today()
        start_date = end_date - timedelta(days=3)
        
        games = Game.query.filter(
            Game.date.between(start_date, end_date)
        ).all()
        
        if not games:
            print("❌ 沒有找到最近的比賽")
            return
        
        print(f"📋 找到 {len(games)} 場比賽需要重新預測")
        
        # 初始化預測器
        predictor = UnifiedBettingPredictor(app)
        predictor.model_version = "improved_calibration_v1"
        
        success_count = 0
        error_count = 0
        
        for game in games:
            try:
                print(f"\n🎯 重新預測: {game.away_team} @ {game.home_team} ({game.date})")
                
                # 刪除舊預測
                old_predictions = Prediction.query.filter_by(game_id=game.game_id).all()
                for old_pred in old_predictions:
                    db.session.delete(old_pred)
                
                # 生成新預測
                result = predictor.generate_unified_prediction(game.game_id)
                
                if result and result.get('success'):
                    # 顯示新預測結果
                    predictions = result.get('predictions', {})
                    win_loss = predictions.get('score', {})  # 修正鍵名
                    over_under = predictions.get('over_under', {})
                    
                    if win_loss and 'error' not in win_loss:
                        home_score = win_loss.get('home_score', 0)
                        away_score = win_loss.get('away_score', 0)
                        total_score = home_score + away_score
                        print(f"  新預測得分: {away_score:.1f} - {home_score:.1f} (總分 {total_score:.1f})")
                    
                    if over_under and 'error' not in over_under:
                        ou_line = over_under.get('盤口', 'N/A')
                        ou_recommendation = over_under.get('建議', 'N/A')
                        print(f"  大小分: {ou_line} ({ou_recommendation})")
                    
                    # 如果有實際結果，顯示比較
                    if game.home_score is not None and game.away_score is not None:
                        actual_total = game.home_score + game.away_score
                        if win_loss and 'error' not in win_loss:
                            predicted_total = win_loss.get('home_score', 0) + win_loss.get('away_score', 0)
                            error = abs(predicted_total - actual_total)
                            print(f"  實際得分: {game.away_score} - {game.home_score} (總分 {actual_total})")
                            print(f"  預測誤差: {error:.1f} 分")
                    
                    success_count += 1
                    print("  ✅ 預測成功")
                    
                else:
                    error_msg = result.get('error', '未知錯誤') if result else '預測失敗'
                    print(f"  ❌ 預測失敗: {error_msg}")
                    error_count += 1
                    
            except Exception as e:
                print(f"  ❌ 處理失敗: {e}")
                error_count += 1
        
        # 提交數據庫更改
        try:
            db.session.commit()
            print(f"\n💾 數據庫更新成功")
            print(f"📊 重新預測結果:")
            print(f"  成功: {success_count} 場")
            print(f"  失敗: {error_count} 場")
            print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%")
            
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ 數據庫更新失敗: {e}")

def test_new_predictions():
    """測試新預測的效果"""
    print(f"\n🧪 測試新預測效果...")
    
    app = create_app()
    with app.app_context():
        # 獲取最近有實際結果的比賽
        query = text("""
            SELECT g.game_id, g.home_team, g.away_team, g.date,
                   g.home_score, g.away_score,
                   p.predicted_home_score, p.predicted_away_score,
                   p.model_version
            FROM games g
            LEFT JOIN predictions p ON g.game_id = p.game_id
            WHERE g.home_score IS NOT NULL 
            AND g.away_score IS NOT NULL
            AND p.predicted_home_score IS NOT NULL
            AND p.model_version = 'improved_calibration_v1'
            ORDER BY g.date DESC
            LIMIT 10
        """)
        
        results = db.session.execute(query).fetchall()
        
        if results:
            print(f"📋 測試 {len(results)} 場新預測:")
            print("-" * 70)
            print("比賽                    實際    新預測   誤差    改善狀況")
            print("-" * 70)
            
            total_error = 0
            for row in results:
                game_id, home_team, away_team, game_date, home_score, away_score, pred_home, pred_away, model_version = row
                
                actual_total = home_score + away_score
                predicted_total = pred_home + pred_away
                error = abs(predicted_total - actual_total)
                total_error += error
                
                status = "✅" if error < 3 else "⚠️" if error < 5 else "❌"
                
                print(f"{away_team:3} @ {home_team:3} {game_date}  {actual_total:4.0f}    {predicted_total:6.1f}   {error:4.1f}   {status}")
            
            avg_error = total_error / len(results)
            print("-" * 70)
            print(f"平均預測誤差: {avg_error:.2f} 分")
            
            if avg_error < 3:
                print("✅ 預測準確度大幅改善！")
            elif avg_error < 4:
                print("⚠️ 預測準確度有所改善，但仍需優化")
            else:
                print("❌ 預測準確度仍需進一步改善")
        else:
            print("❌ 沒有找到新預測數據")

def main():
    """主要執行流程"""
    print("🎯 改進校正算法部署")
    print("=" * 50)
    print("目標: 修復7.9%準確率和+2.12分系統性偏差")
    print("新校正: 主隊-35%, 客隊-25%, 上限9.5分")
    print("=" * 50)
    
    # 1. 重新生成預測
    regenerate_recent_predictions()
    
    # 2. 測試新預測效果
    test_new_predictions()
    
    print(f"\n🚀 部署完成！")
    print("建議:")
    print("1. 監控未來幾天的預測準確率")
    print("2. 如果準確率提升到50%以上，繼續使用")
    print("3. 如果仍然偏低，考慮重新訓練模型")

if __name__ == "__main__":
    main()
