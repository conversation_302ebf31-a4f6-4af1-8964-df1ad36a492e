#!/usr/bin/env python3
"""
MLB預測系統全面功能測試
測試所有主要功能模組
"""

import requests
import logging
from datetime import date, timedelta
from app import create_app
from models.database import db, Game, Team, Player, PlayerStats, BoxScore, Prediction
from models.prediction_service import PredictionService
from models.automated_predictor import automated_predictor
from models.detailed_data_fetcher import DetailedDataFetcher

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:5500"

class SystemTester:
    def __init__(self):
        self.app = create_app()
        self.results = {
            'web_pages': {},
            'api_endpoints': {},
            'admin_functions': {},
            'prediction_system': {},
            'data_integrity': {},
            'automation': {}
        }
    
    def test_web_pages(self):
        """測試主要網頁功能"""
        logger.info("🌐 測試網頁功能...")
        
        pages = [
            ('/', '首頁'),
            ('/dashboard/', '儀表板'),
            ('/teams/', '球隊列表'),
            ('/games/', '比賽列表'),
            ('/players/', '球員列表'),
            ('/predictions/', '預測列表'),
            ('/predictions/accuracy', '預測準確率'),
            ('/analysis/', '分析儀表板'),
            ('/admin/', '系統管理')
        ]
        
        for url, name in pages:
            try:
                response = requests.get(f"{BASE_URL}{url}", timeout=10)
                success = response.status_code == 200
                self.results['web_pages'][name] = {
                    'status': 'success' if success else 'failed',
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds()
                }
                
                if success:
                    logger.info(f"  ✅ {name}: {response.status_code} ({response.elapsed.total_seconds():.2f}s)")
                else:
                    logger.error(f"  ❌ {name}: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"  ❌ {name}: {e}")
                self.results['web_pages'][name] = {
                    'status': 'error',
                    'error': str(e)
                }
    
    def test_api_endpoints(self):
        """測試API端點"""
        logger.info("🔌 測試API端點...")
        
        endpoints = [
            ('/api/health', 'GET', '健康檢查'),
            ('/analysis/api/team-stats/NYY', 'GET', '球隊統計API')
        ]
        
        for url, method, name in endpoints:
            try:
                if method == 'GET':
                    response = requests.get(f"{BASE_URL}{url}", timeout=10)
                
                success = response.status_code == 200
                self.results['api_endpoints'][name] = {
                    'status': 'success' if success else 'failed',
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds()
                }
                
                if success:
                    logger.info(f"  ✅ {name}: {response.status_code}")
                    # 檢查JSON響應
                    try:
                        data = response.json()
                        logger.info(f"    📄 JSON響應正常: {len(str(data))} 字符")
                    except:
                        logger.warning(f"    ⚠️ 非JSON響應")
                else:
                    logger.error(f"  ❌ {name}: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"  ❌ {name}: {e}")
                self.results['api_endpoints'][name] = {
                    'status': 'error',
                    'error': str(e)
                }
    
    def test_database_integrity(self):
        """測試數據庫完整性"""
        logger.info("💾 測試數據庫完整性...")
        
        with self.app.app_context():
            try:
                # 基本數據統計
                stats = {
                    'games': Game.query.count(),
                    'teams': Team.query.count(),
                    'players': Player.query.count(),
                    'player_stats': PlayerStats.query.count(),
                    'box_scores': BoxScore.query.count(),
                    'predictions': Prediction.query.count()
                }
                
                logger.info(f"  📊 數據統計:")
                for table, count in stats.items():
                    logger.info(f"    {table}: {count:,}")
                
                # Box Score覆蓋率檢查
                years = [2023, 2024, 2025]
                coverage_stats = {}
                
                for year in years:
                    completed_games = Game.query.filter(
                        db.func.strftime('%Y', Game.date) == str(year),
                        Game.game_status == 'completed'
                    ).count()
                    
                    games_with_boxscore = db.session.query(Game).filter(
                        db.func.strftime('%Y', Game.date) == str(year),
                        Game.game_status == 'completed',
                        Game.game_id.in_(
                            db.session.query(BoxScore.game_id).distinct()
                        )
                    ).count()
                    
                    coverage = games_with_boxscore / completed_games if completed_games > 0 else 0
                    coverage_stats[year] = {
                        'completed_games': completed_games,
                        'games_with_boxscore': games_with_boxscore,
                        'coverage': coverage
                    }
                    
                    status = "✅" if coverage >= 0.95 else "⚠️" if coverage >= 0.8 else "❌"
                    logger.info(f"  {status} {year}年 Box Score覆蓋率: {coverage:.1%} ({games_with_boxscore}/{completed_games})")
                
                self.results['data_integrity'] = {
                    'status': 'success',
                    'stats': stats,
                    'coverage_stats': coverage_stats
                }
                
            except Exception as e:
                logger.error(f"  ❌ 數據庫完整性檢查失敗: {e}")
                self.results['data_integrity'] = {
                    'status': 'error',
                    'error': str(e)
                }
    
    def test_prediction_system(self):
        """測試預測系統"""
        logger.info("🤖 測試預測系統...")
        
        with self.app.app_context():
            try:
                service = PredictionService()
                
                # 檢查模型是否載入
                models_loaded = service.models_loaded
                logger.info(f"  📦 模型載入狀態: {'✅ 已載入' if models_loaded else '❌ 未載入'}")
                
                if models_loaded:
                    # 測試預測生成
                    upcoming_game = Game.query.filter(
                        Game.date >= date.today(),
                        Game.game_status == 'scheduled'
                    ).first()
                    
                    if upcoming_game:
                        prediction = service.generate_game_prediction(upcoming_game.game_id)
                        if prediction:
                            logger.info(f"  ✅ 預測生成成功: {upcoming_game.away_team} @ {upcoming_game.home_team}")
                            logger.info(f"    主隊勝率: {prediction.home_win_probability:.3f}")
                            logger.info(f"    總分預測: {prediction.predicted_total_runs:.1f}")
                        else:
                            logger.warning(f"  ⚠️ 預測生成失敗")
                    
                    # 測試準確率統計
                    accuracy_stats = service.get_prediction_accuracy_stats(days=30)
                    if accuracy_stats:
                        logger.info(f"  📈 預測準確率: {accuracy_stats.get('home_win_accuracy', 0):.3f}")
                        logger.info(f"  📊 總預測數: {accuracy_stats.get('total_predictions', 0)}")
                
                self.results['prediction_system'] = {
                    'status': 'success',
                    'models_loaded': models_loaded
                }
                
            except Exception as e:
                logger.error(f"  ❌ 預測系統測試失敗: {e}")
                self.results['prediction_system'] = {
                    'status': 'error',
                    'error': str(e)
                }
    
    def test_admin_functions(self):
        """測試管理員功能"""
        logger.info("⚙️ 測試管理員功能...")
        
        with self.app.app_context():
            try:
                # 測試數據獲取器
                fetcher = DetailedDataFetcher()
                logger.info(f"  ✅ DetailedDataFetcher 初始化成功")
                
                # 測試自動化預測器
                status = automated_predictor.get_system_status()
                logger.info(f"  📊 自動化預測器狀態:")
                logger.info(f"    模型就緒: {'✅' if status.get('models_ready') else '❌'}")
                logger.info(f"    調度器運行: {'✅' if status.get('scheduler_running') else '❌'}")
                
                self.results['admin_functions'] = {
                    'status': 'success',
                    'system_status': status
                }
                
            except Exception as e:
                logger.error(f"  ❌ 管理員功能測試失敗: {e}")
                self.results['admin_functions'] = {
                    'status': 'error',
                    'error': str(e)
                }
    
    def test_automation(self):
        """測試自動化功能"""
        logger.info("🔄 測試自動化功能...")
        
        with self.app.app_context():
            try:
                # 測試手動每日更新
                today = date.today()
                result = automated_predictor.manual_daily_update(today)
                
                if result.get('status') == 'success':
                    logger.info(f"  ✅ 每日更新功能正常")
                else:
                    logger.warning(f"  ⚠️ 每日更新結果: {result}")
                
                # 測試手動預測生成
                pred_result = automated_predictor.manual_generate_predictions()
                logger.info(f"  📊 預測生成結果: {pred_result.get('successful_predictions', 0)} 個預測")
                
                self.results['automation'] = {
                    'status': 'success',
                    'daily_update': result,
                    'prediction_generation': pred_result
                }
                
            except Exception as e:
                logger.error(f"  ❌ 自動化功能測試失敗: {e}")
                self.results['automation'] = {
                    'status': 'error',
                    'error': str(e)
                }
    
    def run_all_tests(self):
        """運行所有測試"""
        logger.info("🚀 開始全面系統測試...")
        logger.info("=" * 60)
        
        # 運行所有測試
        self.test_web_pages()
        self.test_api_endpoints()
        self.test_database_integrity()
        self.test_prediction_system()
        self.test_admin_functions()
        self.test_automation()
        
        # 生成測試報告
        self.generate_report()
    
    def generate_report(self):
        """生成測試報告"""
        logger.info("\n" + "=" * 60)
        logger.info("📋 測試結果摘要")
        logger.info("=" * 60)
        
        categories = [
            ('web_pages', '網頁功能'),
            ('api_endpoints', 'API端點'),
            ('data_integrity', '數據完整性'),
            ('prediction_system', '預測系統'),
            ('admin_functions', '管理員功能'),
            ('automation', '自動化功能')
        ]
        
        total_tests = 0
        passed_tests = 0
        
        for category, name in categories:
            results = self.results[category]
            
            if isinstance(results, dict) and 'status' in results:
                # 單一測試結果
                status = "✅ 通過" if results['status'] == 'success' else "❌ 失敗"
                logger.info(f"{name}: {status}")
                total_tests += 1
                if results['status'] == 'success':
                    passed_tests += 1
            else:
                # 多個測試結果
                category_passed = 0
                category_total = len(results)
                
                for test_name, result in results.items():
                    if isinstance(result, dict) and 'status' in result:
                        if result['status'] == 'success':
                            category_passed += 1
                
                status = f"{category_passed}/{category_total} 通過"
                logger.info(f"{name}: {status}")
                total_tests += category_total
                passed_tests += category_passed
        
        logger.info("=" * 60)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        if success_rate >= 90:
            logger.info(f"🎉 系統測試完成！總體通過率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
            logger.info("✅ 系統運行狀態優秀！")
        elif success_rate >= 70:
            logger.info(f"⚠️ 系統測試完成！總體通過率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
            logger.info("🔧 系統基本正常，但有部分功能需要注意")
        else:
            logger.info(f"❌ 系統測試完成！總體通過率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
            logger.info("🚨 系統存在重要問題，需要修復")

def main():
    """主函數"""
    tester = SystemTester()
    tester.run_all_tests()

if __name__ == '__main__':
    main()
