#!/usr/bin/env python3
"""
詳細預測展示系統
顯示先發投手數據、打線數據等詳細信息
"""

import logging
from datetime import date, timedelta
from typing import Dict, List, Optional, Tuple
from app import create_app
from models.database import db, Game, GameDetail, PlayerStats, Player, Team
from models.improved_predictor import ImprovedMLBPredictor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DetailedPredictionDisplay:
    """詳細預測展示類"""
    
    def __init__(self):
        self.app = create_app()
        self.predictor = ImprovedMLBPredictor()
        
    def get_starting_pitcher_info(self, team: str, game_date: date) -> Optional[Dict]:
        """獲取先發投手信息"""
        with self.app.app_context():
            try:
                # 查找該日期的比賽先發投手
                game = Game.query.filter(
                    Game.date == game_date,
                    ((Game.home_team == team) | (Game.away_team == team))
                ).first()
                
                if game:
                    game_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
                    if game_detail:
                        pitcher_name = (game_detail.home_starting_pitcher 
                                      if game.home_team == team 
                                      else game_detail.away_starting_pitcher)
                        
                        if pitcher_name:
                            # 查找投手統計數據
                            pitcher_stats = PlayerStats.query.filter(
                                PlayerStats.player_name.like(f'%{pitcher_name}%'),
                                PlayerStats.innings_pitched > 0,
                                PlayerStats.season == game_date.year
                            ).first()
                            
                            if pitcher_stats:
                                return {
                                    'name': pitcher_name,
                                    'era': pitcher_stats.era,
                                    'whip': pitcher_stats.whip,
                                    'wins': pitcher_stats.wins,
                                    'losses': pitcher_stats.losses,
                                    'innings_pitched': pitcher_stats.innings_pitched,
                                    'strikeouts': pitcher_stats.strikeouts_pitching,
                                    'walks_allowed': pitcher_stats.walks_allowed,
                                    'hits_allowed': pitcher_stats.hits_allowed
                                }
                
                # 如果找不到確切的先發投手，嘗試找該球隊的王牌投手
                return self._get_team_ace_pitcher(team, game_date.year)
                
            except Exception as e:
                logger.error(f"獲取先發投手信息失敗 {team}: {e}")
                return None
    
    def _get_team_ace_pitcher(self, team: str, season: int) -> Optional[Dict]:
        """獲取球隊王牌投手"""
        try:
            # 查找該球隊投球局數最多且ERA較低的投手
            ace_pitcher = PlayerStats.query.filter(
                PlayerStats.team_id == team,
                PlayerStats.season == season,
                PlayerStats.innings_pitched > 50,
                PlayerStats.era > 0
            ).order_by(
                PlayerStats.innings_pitched.desc(),
                PlayerStats.era.asc()
            ).first()
            
            if ace_pitcher:
                return {
                    'name': ace_pitcher.player_name,
                    'era': ace_pitcher.era,
                    'whip': ace_pitcher.whip,
                    'wins': ace_pitcher.wins,
                    'losses': ace_pitcher.losses,
                    'innings_pitched': ace_pitcher.innings_pitched,
                    'strikeouts': ace_pitcher.strikeouts_pitching,
                    'walks_allowed': ace_pitcher.walks_allowed,
                    'hits_allowed': ace_pitcher.hits_allowed,
                    'note': '(王牌投手)'
                }
            return None
            
        except Exception as e:
            logger.error(f"獲取王牌投手失敗 {team}: {e}")
            return None
    
    def get_lineup_stats(self, team: str, season: int) -> List[Dict]:
        """獲取打線統計"""
        with self.app.app_context():
            try:
                # 獲取該球隊主要打者（至少100個打席）
                batters = PlayerStats.query.filter(
                    PlayerStats.team_id == team,
                    PlayerStats.season == season,
                    PlayerStats.at_bats >= 100
                ).order_by(PlayerStats.at_bats.desc()).limit(9).all()
                
                lineup = []
                for i, batter in enumerate(batters, 1):
                    lineup.append({
                        'order': i,
                        'name': batter.player_name,
                        'position': batter.position,
                        'batting_avg': batter.batting_avg,
                        'on_base_percentage': batter.on_base_percentage,
                        'slugging_percentage': batter.slugging_percentage,
                        'ops': batter.ops,
                        'home_runs': batter.home_runs,
                        'rbi': batter.rbi,
                        'stolen_bases': batter.stolen_bases,
                        'at_bats': batter.at_bats
                    })
                
                return lineup
                
            except Exception as e:
                logger.error(f"獲取打線統計失敗 {team}: {e}")
                return []
    
    def get_team_recent_performance(self, team: str, game_date: date, days: int = 10) -> Dict:
        """獲取球隊近期表現"""
        with self.app.app_context():
            try:
                start_date = game_date - timedelta(days=days)
                
                recent_games = Game.query.filter(
                    Game.date.between(start_date, game_date),
                    ((Game.home_team == team) | (Game.away_team == team)),
                    Game.game_status == 'completed'
                ).order_by(Game.date.desc()).all()
                
                if not recent_games:
                    return {'games': 0, 'wins': 0, 'losses': 0, 'win_pct': 0.0}
                
                wins = 0
                total_runs_scored = 0
                total_runs_allowed = 0
                
                for game in recent_games:
                    if game.home_team == team:
                        if game.home_score > game.away_score:
                            wins += 1
                        total_runs_scored += game.home_score or 0
                        total_runs_allowed += game.away_score or 0
                    else:
                        if game.away_score > game.home_score:
                            wins += 1
                        total_runs_scored += game.away_score or 0
                        total_runs_allowed += game.home_score or 0
                
                games_count = len(recent_games)
                return {
                    'games': games_count,
                    'wins': wins,
                    'losses': games_count - wins,
                    'win_pct': wins / games_count if games_count > 0 else 0.0,
                    'avg_runs_scored': total_runs_scored / games_count if games_count > 0 else 0.0,
                    'avg_runs_allowed': total_runs_allowed / games_count if games_count > 0 else 0.0
                }
                
            except Exception as e:
                logger.error(f"獲取近期表現失敗 {team}: {e}")
                return {'games': 0, 'wins': 0, 'losses': 0, 'win_pct': 0.0}
    
    def display_detailed_prediction(self, home_team: str, away_team: str, game_date: date = None):
        """顯示詳細預測信息"""
        if game_date is None:
            game_date = date.today()
        
        print("=" * 80)
        print(f"🏟️  詳細比賽預測: {away_team} @ {home_team}")
        print(f"📅 比賽日期: {game_date}")
        print("=" * 80)
        
        # 1. 獲取預測結果
        try:
            with self.app.app_context():
                self.predictor.load_models('models/enhanced')
                prediction = self.predictor.predict_game(home_team, away_team, game_date)
            
            print(f"\n🎯 預測結果:")
            print(f"   比分預測: {prediction['predicted_away_score']:.1f} - {prediction['predicted_home_score']:.1f}")
            print(f"   主隊勝率: {prediction['home_win_probability']:.1%}")
            print(f"   客隊勝率: {prediction['away_win_probability']:.1%}")
            print(f"   信心度: {prediction['confidence']:.1%}")
            print(f"   總得分: {prediction['total_runs']:.1f}")
            
        except Exception as e:
            print(f"❌ 預測失敗: {e}")
            return
        
        # 2. 先發投手對比
        print(f"\n⚾ 先發投手對比:")
        print("-" * 60)
        
        home_pitcher = self.get_starting_pitcher_info(home_team, game_date)
        away_pitcher = self.get_starting_pitcher_info(away_team, game_date)
        
        if away_pitcher:
            print(f"客隊先發: {away_pitcher['name']} {away_pitcher.get('note', '')}")
            print(f"   ERA: {away_pitcher['era']:.2f} | WHIP: {away_pitcher['whip']:.2f}")
            print(f"   戰績: {away_pitcher['wins']}-{away_pitcher['losses']} | 投球局數: {away_pitcher['innings_pitched']:.1f}")
            print(f"   三振: {away_pitcher['strikeouts']} | 保送: {away_pitcher['walks_allowed']}")
        else:
            print(f"客隊先發: 數據不足")
        
        print()
        
        if home_pitcher:
            print(f"主隊先發: {home_pitcher['name']} {home_pitcher.get('note', '')}")
            print(f"   ERA: {home_pitcher['era']:.2f} | WHIP: {home_pitcher['whip']:.2f}")
            print(f"   戰績: {home_pitcher['wins']}-{home_pitcher['losses']} | 投球局數: {home_pitcher['innings_pitched']:.1f}")
            print(f"   三振: {home_pitcher['strikeouts']} | 保送: {home_pitcher['walks_allowed']}")
        else:
            print(f"主隊先發: 數據不足")
        
        # 3. 打線對比
        print(f"\n🏏 主要打者統計:")
        print("-" * 60)
        
        away_lineup = self.get_lineup_stats(away_team, game_date.year)
        home_lineup = self.get_lineup_stats(home_team, game_date.year)
        
        print(f"客隊 {away_team} 主要打者:")
        if away_lineup:
            for batter in away_lineup[:5]:  # 顯示前5名
                print(f"   {batter['order']}. {batter['name']} ({batter['position']})")
                print(f"      AVG: {batter['batting_avg']:.3f} | OPS: {batter['ops']:.3f} | HR: {batter['home_runs']} | RBI: {batter['rbi']}")
        else:
            print("   數據不足")
        
        print(f"\n主隊 {home_team} 主要打者:")
        if home_lineup:
            for batter in home_lineup[:5]:  # 顯示前5名
                print(f"   {batter['order']}. {batter['name']} ({batter['position']})")
                print(f"      AVG: {batter['batting_avg']:.3f} | OPS: {batter['ops']:.3f} | HR: {batter['home_runs']} | RBI: {batter['rbi']}")
        else:
            print("   數據不足")
        
        # 4. 近期表現
        print(f"\n📈 近期表現 (最近10場):")
        print("-" * 60)
        
        away_recent = self.get_team_recent_performance(away_team, game_date)
        home_recent = self.get_team_recent_performance(home_team, game_date)
        
        print(f"客隊 {away_team}: {away_recent['wins']}-{away_recent['losses']} ({away_recent['win_pct']:.1%})")
        print(f"   平均得分: {away_recent['avg_runs_scored']:.1f} | 平均失分: {away_recent['avg_runs_allowed']:.1f}")
        
        print(f"主隊 {home_team}: {home_recent['wins']}-{home_recent['losses']} ({home_recent['win_pct']:.1%})")
        print(f"   平均得分: {home_recent['avg_runs_scored']:.1f} | 平均失分: {home_recent['avg_runs_allowed']:.1f}")
        
        print("\n" + "=" * 80)

def main():
    """主函數"""
    display = DetailedPredictionDisplay()
    
    # 測試幾場比賽的詳細預測
    test_games = [
        ('NYY', 'BOS'),
        ('LAD', 'SF'),
        ('HOU', 'TEX')
    ]
    
    for home, away in test_games:
        display.display_detailed_prediction(home, away)
        print("\n")

if __name__ == "__main__":
    main()
