#!/usr/bin/env python3
"""
歷史先發投手收集工具
用於回測時收集和記錄歷史比賽的先發投手信息
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, GameDetail, PlayerGameStats
from datetime import date, datetime, timedelta
import json
import logging

logger = logging.getLogger(__name__)

class HistoricalPitcherCollector:
    """歷史先發投手收集器"""
    
    def __init__(self):
        self.logger = logger
        self.pitcher_cache = {}  # 緩存已找到的投手信息
        
    def collect_starting_pitchers_for_date_range(self, start_date: date, end_date: date) -> dict:
        """
        收集指定日期範圍的先發投手信息
        
        Args:
            start_date: 開始日期
            end_date: 結束日期
            
        Returns:
            dict: 收集結果統計和投手信息
        """
        app = create_app()
        
        with app.app_context():
            stats = {
                'total_games': 0,
                'found_in_gamedetail': 0,
                'found_in_boxscore': 0,
                'missing_pitchers': 0,
                'pitcher_data': {}
            }
            
            try:
                # 獲取指定日期範圍的已完成比賽
                games = Game.query.filter(
                    Game.date >= start_date,
                    Game.date <= end_date,
                    Game.game_status == 'completed'
                ).order_by(Game.date).all()
                
                stats['total_games'] = len(games)
                print(f"🔍 開始收集 {start_date} 到 {end_date} 的先發投手信息")
                print(f"📊 找到 {len(games)} 場已完成的比賽")
                
                for game in games:
                    game_pitcher_info = self._collect_game_pitchers(game)
                    
                    if game_pitcher_info['home_pitcher'] and game_pitcher_info['away_pitcher']:
                        if game_pitcher_info['source'] == 'gamedetail':
                            stats['found_in_gamedetail'] += 1
                        elif game_pitcher_info['source'] == 'boxscore':
                            stats['found_in_boxscore'] += 1
                        
                        # 存儲投手信息
                        stats['pitcher_data'][game.game_id] = {
                            'date': game.date.isoformat(),
                            'home_team': game.home_team,
                            'away_team': game.away_team,
                            'home_pitcher': game_pitcher_info['home_pitcher'],
                            'away_pitcher': game_pitcher_info['away_pitcher'],
                            'source': game_pitcher_info['source'],
                            'confidence': game_pitcher_info['confidence']
                        }
                    else:
                        stats['missing_pitchers'] += 1
                        print(f"⚠️ 缺少投手信息: {game.game_id} ({game.away_team} @ {game.home_team}, {game.date})")
                
                # 保存結果到文件
                self._save_pitcher_data(stats, start_date, end_date)
                
                print(f"\n📈 收集結果統計:")
                print(f"  總比賽數: {stats['total_games']}")
                print(f"  從GameDetail找到: {stats['found_in_gamedetail']}")
                print(f"  從BoxScore找到: {stats['found_in_boxscore']}")
                print(f"  缺少投手信息: {stats['missing_pitchers']}")
                print(f"  成功率: {((stats['found_in_gamedetail'] + stats['found_in_boxscore']) / stats['total_games'] * 100):.1f}%")
                
                return stats
                
            except Exception as e:
                self.logger.error(f"收集先發投手失敗: {e}")
                return stats
    
    def _collect_game_pitchers(self, game: Game) -> dict:
        """收集單場比賽的先發投手信息"""
        result = {
            'home_pitcher': None,
            'away_pitcher': None,
            'source': None,
            'confidence': 'low'
        }
        
        # 1. 首先檢查GameDetail表
        game_detail = GameDetail.query.filter_by(game_id=game.game_id).first()
        if game_detail and game_detail.home_starting_pitcher and game_detail.away_starting_pitcher:
            result.update({
                'home_pitcher': game_detail.home_starting_pitcher,
                'away_pitcher': game_detail.away_starting_pitcher,
                'source': 'gamedetail',
                'confidence': 'high'
            })
            return result
        
        # 2. 從BoxScore/PlayerGameStats中推斷先發投手
        boxscore_pitchers = self._extract_starting_pitchers_from_stats(game.game_id)
        if boxscore_pitchers['home_pitcher'] and boxscore_pitchers['away_pitcher']:
            result.update({
                'home_pitcher': boxscore_pitchers['home_pitcher'],
                'away_pitcher': boxscore_pitchers['away_pitcher'],
                'source': 'boxscore',
                'confidence': 'medium'
            })
            return result
        
        return result
    
    def _extract_starting_pitchers_from_stats(self, game_id: str) -> dict:
        """從PlayerGameStats中提取先發投手"""
        result = {'home_pitcher': None, 'away_pitcher': None}
        
        try:
            # 查找主隊先發投手 (投球局數最多且大於等於4局的投手)
            home_pitchers = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.is_home == True,
                PlayerGameStats.innings_pitched >= 4.0  # 至少投4局才算先發
            ).order_by(PlayerGameStats.innings_pitched.desc()).all()
            
            if home_pitchers:
                result['home_pitcher'] = home_pitchers[0].player_name
            
            # 查找客隊先發投手
            away_pitchers = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.is_home == False,
                PlayerGameStats.innings_pitched >= 4.0
            ).order_by(PlayerGameStats.innings_pitched.desc()).all()
            
            if away_pitchers:
                result['away_pitcher'] = away_pitchers[0].player_name
                
        except Exception as e:
            self.logger.error(f"從統計數據提取投手失敗: {e}")
        
        return result
    
    def _save_pitcher_data(self, stats: dict, start_date: date, end_date: date):
        """保存投手數據到JSON文件"""
        try:
            filename = f"historical_pitchers_{start_date}_{end_date}.json"
            filepath = os.path.join('data', filename)
            
            # 確保data目錄存在
            os.makedirs('data', exist_ok=True)
            
            # 保存數據
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"💾 投手數據已保存到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存投手數據失敗: {e}")
    
    def load_pitcher_data(self, start_date: date, end_date: date) -> dict:
        """從文件載入投手數據"""
        try:
            filename = f"historical_pitchers_{start_date}_{end_date}.json"
            filepath = os.path.join('data', filename)
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"⚠️ 投手數據文件不存在: {filepath}")
                return {}
                
        except Exception as e:
            self.logger.error(f"載入投手數據失敗: {e}")
            return {}
    
    def get_game_starting_pitchers(self, game_id: str, game_date: date = None) -> dict:
        """
        獲取特定比賽的先發投手信息
        
        Args:
            game_id: 比賽ID
            game_date: 比賽日期 (可選，用於查找緩存文件)
            
        Returns:
            dict: 包含先發投手信息
        """
        # 首先嘗試從緩存中獲取
        if game_id in self.pitcher_cache:
            return self.pitcher_cache[game_id]
        
        # 如果有日期，嘗試從對應的數據文件中查找
        if game_date:
            # 嘗試載入該月份的數據
            month_start = game_date.replace(day=1)
            if game_date.month == 12:
                month_end = game_date.replace(year=game_date.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                month_end = game_date.replace(month=game_date.month + 1, day=1) - timedelta(days=1)
            
            monthly_data = self.load_pitcher_data(month_start, month_end)
            if game_id in monthly_data.get('pitcher_data', {}):
                pitcher_info = monthly_data['pitcher_data'][game_id]
                self.pitcher_cache[game_id] = pitcher_info
                return pitcher_info
        
        # 實時查詢數據庫
        app = create_app()
        with app.app_context():
            game = Game.query.filter_by(game_id=game_id).first()
            if game:
                pitcher_info = self._collect_game_pitchers(game)
                if pitcher_info['home_pitcher'] and pitcher_info['away_pitcher']:
                    result = {
                        'home_pitcher': pitcher_info['home_pitcher'],
                        'away_pitcher': pitcher_info['away_pitcher'],
                        'source': pitcher_info['source'],
                        'confidence': pitcher_info['confidence']
                    }
                    self.pitcher_cache[game_id] = result
                    return result
        
        return {'home_pitcher': None, 'away_pitcher': None, 'source': None, 'confidence': 'none'}

def main():
    """主函數 - 收集歷史投手數據"""
    import argparse
    
    parser = argparse.ArgumentParser(description='收集歷史先發投手數據')
    parser.add_argument('--start-date', type=str, required=True, help='開始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, required=True, help='結束日期 (YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    try:
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d').date()
        
        collector = HistoricalPitcherCollector()
        stats = collector.collect_starting_pitchers_for_date_range(start_date, end_date)
        
        print(f"\n✅ 歷史投手數據收集完成！")
        
    except ValueError as e:
        print(f"❌ 日期格式錯誤: {e}")
    except Exception as e:
        print(f"❌ 收集失敗: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        main()
    else:
        # 默認收集最近30天的數據
        end_date = date.today()
        start_date = end_date - timedelta(days=30)
        
        collector = HistoricalPitcherCollector()
        stats = collector.collect_starting_pitchers_for_date_range(start_date, end_date)
        
        print(f"\n✅ 默認收集最近30天的投手數據完成！")
