#!/usr/bin/env python3
"""
MLB實時盈利監控系統
追蹤ROI、風險指標、投注表現和資金管理
"""

import sqlite3
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import matplotlib.pyplot as plt
import pandas as pd

@dataclass
class BettingResult:
    """投注結果數據類"""
    date: str
    game: str
    bet_type: str  # 'spread', 'total', 'moneyline'
    recommendation: str
    confidence: float
    stake: float
    odds: float
    result: str  # 'win', 'loss', 'push'
    profit: float
    roi: float

@dataclass
class DailyPerformance:
    """每日表現數據類"""
    date: str
    total_bets: int
    winning_bets: int
    losing_bets: int
    pushes: int
    total_staked: float
    total_profit: float
    daily_roi: float
    win_rate: float
    avg_odds: float
    avg_confidence: float

@dataclass
class PerformanceMetrics:
    """整體表現指標"""
    period_start: str
    period_end: str
    total_bets: int
    win_rate: float
    total_staked: float
    total_profit: float
    overall_roi: float
    sharpe_ratio: float
    max_drawdown: float
    profit_factor: float
    kelly_efficiency: float
    confidence_calibration: float

class ProfitMonitoringSystem:
    """實時盈利監控系統"""
    
    def __init__(self, db_path='instance/mlb_data.db', results_db='betting_results.db'):
        self.db_path = db_path
        self.results_db = results_db
        self._initialize_results_database()
        
        # 監控參數
        self.monitoring_params = {
            'target_annual_roi': 0.15,  # 目標年度ROI 15%
            'max_drawdown_threshold': 0.20,  # 最大回撤警戒線20%
            'min_sharpe_ratio': 1.0,  # 最低夏普比率
            'confidence_tolerance': 0.05,  # 信心度校準容忍度
            'kelly_efficiency_threshold': 0.80,  # 凱利效率閾值
            'stop_loss_threshold': 0.15,  # 止損線15%
            'profit_taking_threshold': 0.25  # 獲利了結線25%
        }
    
    def _initialize_results_database(self):
        """初始化投注結果資料庫"""
        try:
            conn = sqlite3.connect(self.results_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS betting_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    game TEXT NOT NULL,
                    bet_type TEXT NOT NULL,
                    recommendation TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    stake REAL NOT NULL,
                    odds REAL NOT NULL,
                    result TEXT NOT NULL,
                    profit REAL NOT NULL,
                    roi REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS daily_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT UNIQUE NOT NULL,
                    total_bets INTEGER NOT NULL,
                    winning_bets INTEGER NOT NULL,
                    losing_bets INTEGER NOT NULL,
                    pushes INTEGER NOT NULL,
                    total_staked REAL NOT NULL,
                    total_profit REAL NOT NULL,
                    daily_roi REAL NOT NULL,
                    win_rate REAL NOT NULL,
                    avg_odds REAL NOT NULL,
                    avg_confidence REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 結果資料庫初始化錯誤: {e}")
    
    def record_betting_result(self, result: BettingResult) -> bool:
        """記錄投注結果"""
        try:
            conn = sqlite3.connect(self.results_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO betting_results 
                (date, game, bet_type, recommendation, confidence, stake, odds, result, profit, roi)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                result.date, result.game, result.bet_type, result.recommendation,
                result.confidence, result.stake, result.odds, result.result,
                result.profit, result.roi
            ))
            
            conn.commit()
            conn.close()
            
            # 更新每日表現
            self._update_daily_performance(result.date)
            
            print(f"✅ 投注結果已記錄: {result.game} - {result.result} - 利潤: ${result.profit:.2f}")
            return True
            
        except Exception as e:
            print(f"❌ 記錄投注結果錯誤: {e}")
            return False
    
    def _update_daily_performance(self, date: str):
        """更新每日表現統計"""
        try:
            conn = sqlite3.connect(self.results_db)
            cursor = conn.cursor()
            
            # 計算當日統計
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_bets,
                    SUM(CASE WHEN result = 'win' THEN 1 ELSE 0 END) as winning_bets,
                    SUM(CASE WHEN result = 'loss' THEN 1 ELSE 0 END) as losing_bets,
                    SUM(CASE WHEN result = 'push' THEN 1 ELSE 0 END) as pushes,
                    SUM(stake) as total_staked,
                    SUM(profit) as total_profit,
                    AVG(odds) as avg_odds,
                    AVG(confidence) as avg_confidence
                FROM betting_results
                WHERE date = ?
            ''', (date,))
            
            result = cursor.fetchone()
            
            if result and result[0] > 0:
                total_bets, winning_bets, losing_bets, pushes, total_staked, total_profit, avg_odds, avg_confidence = result
                
                daily_roi = (total_profit / total_staked) if total_staked > 0 else 0
                win_rate = (winning_bets / (total_bets - pushes)) if (total_bets - pushes) > 0 else 0
                
                # 更新或插入每日表現
                cursor.execute('''
                    INSERT OR REPLACE INTO daily_performance 
                    (date, total_bets, winning_bets, losing_bets, pushes, total_staked, 
                     total_profit, daily_roi, win_rate, avg_odds, avg_confidence)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    date, total_bets, winning_bets, losing_bets, pushes, total_staked,
                    total_profit, daily_roi, win_rate, avg_odds or 0, avg_confidence or 0
                ))
                
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 更新每日表現錯誤: {e}")
    
    def generate_performance_report(self, days_back: int = 30) -> Dict:
        """生成表現報告"""
        print(f"📊 生成 {days_back} 天表現報告")
        print("=" * 60)
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # 獲取期間內的表現數據
            performance_metrics = self._calculate_performance_metrics(
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            # 獲取每日表現
            daily_performances = self._get_daily_performances(days_back)
            
            # 獲取分類表現
            category_performance = self._get_category_performance(days_back)
            
            # 風險分析
            risk_analysis = self._calculate_risk_metrics(days_back)
            
            # 生成預警
            alerts = self._generate_performance_alerts(performance_metrics, risk_analysis)
            
            report = {
                'report_period': {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'days': days_back
                },
                'overall_performance': asdict(performance_metrics),
                'daily_performances': [asdict(dp) for dp in daily_performances],
                'category_performance': category_performance,
                'risk_analysis': risk_analysis,
                'alerts': alerts,
                'recommendations': self._generate_recommendations(performance_metrics, risk_analysis)
            }
            
            self._print_performance_summary(performance_metrics)
            
            return report
            
        except Exception as e:
            print(f"❌ 生成表現報告錯誤: {e}")
            return {'error': str(e)}
    
    def _calculate_performance_metrics(self, start_date: str, end_date: str) -> PerformanceMetrics:
        """計算表現指標"""
        try:
            conn = sqlite3.connect(self.results_db)
            cursor = conn.cursor()
            
            # 基本統計
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_bets,
                    SUM(CASE WHEN result = 'win' THEN 1 ELSE 0 END) as winning_bets,
                    SUM(stake) as total_staked,
                    SUM(profit) as total_profit,
                    AVG(confidence) as avg_confidence
                FROM betting_results
                WHERE date BETWEEN ? AND ?
            ''', (start_date, end_date))
            
            basic_stats = cursor.fetchone()
            
            if not basic_stats or basic_stats[0] == 0:
                return PerformanceMetrics(
                    period_start=start_date,
                    period_end=end_date,
                    total_bets=0,
                    win_rate=0.0,
                    total_staked=0.0,
                    total_profit=0.0,
                    overall_roi=0.0,
                    sharpe_ratio=0.0,
                    max_drawdown=0.0,
                    profit_factor=0.0,
                    kelly_efficiency=0.0,
                    confidence_calibration=0.0
                )
            
            total_bets, winning_bets, total_staked, total_profit, avg_confidence = basic_stats
            
            # 計算基本指標
            win_rate = winning_bets / total_bets if total_bets > 0 else 0
            overall_roi = total_profit / total_staked if total_staked > 0 else 0
            
            # 獲取每日利潤用於進階計算
            cursor.execute('''
                SELECT date, SUM(profit) as daily_profit
                FROM betting_results
                WHERE date BETWEEN ? AND ?
                GROUP BY date
                ORDER BY date
            ''', (start_date, end_date))
            
            daily_profits = [row[1] for row in cursor.fetchall()]
            
            # 計算進階指標
            sharpe_ratio = self._calculate_sharpe_ratio(daily_profits)
            max_drawdown = self._calculate_max_drawdown(daily_profits)
            profit_factor = self._calculate_profit_factor(daily_profits)
            kelly_efficiency = self._calculate_kelly_efficiency(start_date, end_date)
            confidence_calibration = self._calculate_confidence_calibration(start_date, end_date)
            
            conn.close()
            
            return PerformanceMetrics(
                period_start=start_date,
                period_end=end_date,
                total_bets=total_bets,
                win_rate=win_rate,
                total_staked=total_staked or 0,
                total_profit=total_profit or 0,
                overall_roi=overall_roi,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                profit_factor=profit_factor,
                kelly_efficiency=kelly_efficiency,
                confidence_calibration=confidence_calibration
            )
            
        except Exception as e:
            print(f"❌ 計算表現指標錯誤: {e}")
            return PerformanceMetrics(
                period_start=start_date, period_end=end_date, total_bets=0, win_rate=0.0,
                total_staked=0.0, total_profit=0.0, overall_roi=0.0, sharpe_ratio=0.0,
                max_drawdown=0.0, profit_factor=0.0, kelly_efficiency=0.0, confidence_calibration=0.0
            )
    
    def _calculate_sharpe_ratio(self, daily_profits: List[float]) -> float:
        """計算夏普比率"""
        if not daily_profits or len(daily_profits) < 2:
            return 0.0
        
        mean_profit = np.mean(daily_profits)
        std_profit = np.std(daily_profits)
        
        if std_profit == 0:
            return 0.0
        
        # 假設無風險利率為年化2%
        risk_free_rate = 0.02 / 365  # 日化無風險利率
        return (mean_profit - risk_free_rate) / std_profit
    
    def _calculate_max_drawdown(self, daily_profits: List[float]) -> float:
        """計算最大回撤"""
        if not daily_profits:
            return 0.0
        
        cumulative_profits = np.cumsum(daily_profits)
        peak = np.maximum.accumulate(cumulative_profits)
        drawdown = (peak - cumulative_profits) / np.abs(peak)
        drawdown[np.isnan(drawdown) | np.isinf(drawdown)] = 0
        
        return float(np.max(drawdown))
    
    def _calculate_profit_factor(self, daily_profits: List[float]) -> float:
        """計算利潤因子"""
        if not daily_profits:
            return 0.0
        
        gross_profit = sum(p for p in daily_profits if p > 0)
        gross_loss = abs(sum(p for p in daily_profits if p < 0))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    def _calculate_kelly_efficiency(self, start_date: str, end_date: str) -> float:
        """計算凱利效率"""
        # 簡化版本 - 實際實施中需要比較實際投注與理論最優凱利比例
        try:
            conn = sqlite3.connect(self.results_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT confidence, result
                FROM betting_results
                WHERE date BETWEEN ? AND ?
            ''', (start_date, end_date))
            
            results = cursor.fetchall()
            conn.close()
            
            if not results:
                return 0.0
            
            # 簡化計算：比較預期勝率與實際勝率
            predicted_wins = sum(confidence for confidence, _ in results)
            actual_wins = sum(1 for _, result in results if result == 'win')
            
            if predicted_wins == 0:
                return 0.0
            
            efficiency = actual_wins / predicted_wins
            return min(1.0, efficiency)  # 最高100%效率
            
        except Exception as e:
            print(f"❌ 凱利效率計算錯誤: {e}")
            return 0.0
    
    def _calculate_confidence_calibration(self, start_date: str, end_date: str) -> float:
        """計算信心度校準"""
        try:
            conn = sqlite3.connect(self.results_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT confidence, CASE WHEN result = 'win' THEN 1 ELSE 0 END as won
                FROM betting_results
                WHERE date BETWEEN ? AND ?
            ''', (start_date, end_date))
            
            results = cursor.fetchall()
            conn.close()
            
            if not results:
                return 0.0
            
            # 計算校準誤差
            bins = np.linspace(0.5, 1.0, 11)  # 50%-100%信心度分箱
            calibration_error = 0.0
            bin_count = 0
            
            for i in range(len(bins) - 1):
                bin_min, bin_max = bins[i], bins[i + 1]
                bin_data = [(conf, won) for conf, won in results if bin_min <= conf < bin_max]
                
                if bin_data:
                    predicted_prob = np.mean([conf for conf, _ in bin_data])
                    actual_prob = np.mean([won for _, won in bin_data])
                    calibration_error += abs(predicted_prob - actual_prob)
                    bin_count += 1
            
            if bin_count == 0:
                return 0.0
            
            # 返回校準準確度 (1 - 平均誤差)
            return max(0.0, 1.0 - (calibration_error / bin_count))
            
        except Exception as e:
            print(f"❌ 信心度校準計算錯誤: {e}")
            return 0.0
    
    def _get_daily_performances(self, days_back: int) -> List[DailyPerformance]:
        """獲取每日表現數據"""
        try:
            conn = sqlite3.connect(self.results_db)
            cursor = conn.cursor()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            cursor.execute('''
                SELECT date, total_bets, winning_bets, losing_bets, pushes,
                       total_staked, total_profit, daily_roi, win_rate,
                       avg_odds, avg_confidence
                FROM daily_performance
                WHERE date BETWEEN ? AND ?
                ORDER BY date DESC
                LIMIT ?
            ''', (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'), days_back))
            
            performances = []
            for row in cursor.fetchall():
                performances.append(DailyPerformance(
                    date=row[0],
                    total_bets=row[1],
                    winning_bets=row[2],
                    losing_bets=row[3],
                    pushes=row[4],
                    total_staked=row[5],
                    total_profit=row[6],
                    daily_roi=row[7],
                    win_rate=row[8],
                    avg_odds=row[9],
                    avg_confidence=row[10]
                ))
            
            conn.close()
            return performances
            
        except Exception as e:
            print(f"❌ 獲取每日表現錯誤: {e}")
            return []
    
    def _get_category_performance(self, days_back: int) -> Dict:
        """獲取分類表現"""
        try:
            conn = sqlite3.connect(self.results_db)
            cursor = conn.cursor()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            cursor.execute('''
                SELECT bet_type,
                       COUNT(*) as total_bets,
                       SUM(CASE WHEN result = 'win' THEN 1 ELSE 0 END) as wins,
                       SUM(stake) as total_staked,
                       SUM(profit) as total_profit
                FROM betting_results
                WHERE date BETWEEN ? AND ?
                GROUP BY bet_type
            ''', (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
            
            category_performance = {}
            for row in cursor.fetchall():
                bet_type, total_bets, wins, total_staked, total_profit = row
                win_rate = wins / total_bets if total_bets > 0 else 0
                roi = total_profit / total_staked if total_staked > 0 else 0
                
                category_performance[bet_type] = {
                    'total_bets': total_bets,
                    'wins': wins,
                    'win_rate': win_rate,
                    'total_staked': total_staked or 0,
                    'total_profit': total_profit or 0,
                    'roi': roi
                }
            
            conn.close()
            return category_performance
            
        except Exception as e:
            print(f"❌ 獲取分類表現錯誤: {e}")
            return {}
    
    def _calculate_risk_metrics(self, days_back: int) -> Dict:
        """計算風險指標"""
        try:
            conn = sqlite3.connect(self.results_db)
            cursor = conn.cursor()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # 獲取每日利潤
            cursor.execute('''
                SELECT total_profit
                FROM daily_performance
                WHERE date BETWEEN ? AND ?
                ORDER BY date
            ''', (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
            
            daily_profits = [row[0] for row in cursor.fetchall()]
            
            if not daily_profits:
                return {'var_95': 0, 'var_99': 0, 'expected_shortfall': 0, 'volatility': 0}
            
            # 計算VaR和ES
            var_95 = np.percentile(daily_profits, 5)  # 95% VaR
            var_99 = np.percentile(daily_profits, 1)  # 99% VaR
            
            # Expected Shortfall (條件VaR)
            tail_losses = [p for p in daily_profits if p <= var_95]
            expected_shortfall = np.mean(tail_losses) if tail_losses else 0
            
            # 波動率
            volatility = np.std(daily_profits) if len(daily_profits) > 1 else 0
            
            conn.close()
            
            return {
                'var_95': var_95,
                'var_99': var_99, 
                'expected_shortfall': expected_shortfall,
                'volatility': volatility
            }
            
        except Exception as e:
            print(f"❌ 風險指標計算錯誤: {e}")
            return {'var_95': 0, 'var_99': 0, 'expected_shortfall': 0, 'volatility': 0}
    
    def _generate_performance_alerts(self, metrics: PerformanceMetrics, risk: Dict) -> List[str]:
        """生成表現預警"""
        alerts = []
        
        # ROI預警
        if metrics.overall_roi < -0.10:
            alerts.append("🚨 嚴重虧損警告: ROI低於-10%")
        elif metrics.overall_roi < 0:
            alerts.append("⚠️ 虧損警告: ROI為負數")
        
        # 最大回撤預警
        if metrics.max_drawdown > self.monitoring_params['max_drawdown_threshold']:
            alerts.append(f"🚨 回撤警告: 最大回撤 {metrics.max_drawdown:.1%} 超過閾值")
        
        # 夏普比率預警
        if metrics.sharpe_ratio < self.monitoring_params['min_sharpe_ratio']:
            alerts.append(f"⚠️ 風險調整報酬不佳: 夏普比率 {metrics.sharpe_ratio:.2f}")
        
        # 勝率預警
        if metrics.win_rate < 0.45:
            alerts.append(f"⚠️ 勝率偏低: {metrics.win_rate:.1%}")
        
        # 信心度校準預警
        if metrics.confidence_calibration < 0.80:
            alerts.append("⚠️ 信心度校準偏差，預測準確性需要改善")
        
        # 凱利效率預警
        if metrics.kelly_efficiency < self.monitoring_params['kelly_efficiency_threshold']:
            alerts.append("⚠️ 資金管理效率偏低，建議調整投注比例")
        
        if not alerts:
            alerts.append("✅ 所有指標正常")
        
        return alerts
    
    def _generate_recommendations(self, metrics: PerformanceMetrics, risk: Dict) -> List[str]:
        """生成改善建議"""
        recommendations = []
        
        # 基於ROI的建議
        if metrics.overall_roi < 0:
            recommendations.append("暫停投注，重新評估策略和模型準確性")
        elif metrics.overall_roi < 0.05:
            recommendations.append("降低投注規模，專注於高信心度的投注")
        
        # 基於勝率的建議
        if metrics.win_rate < 0.50:
            recommendations.append("重新校準預測模型，提高預測準確性")
        
        # 基於風險的建議
        if metrics.max_drawdown > 0.15:
            recommendations.append("加強風險管理，減少單次投注金額")
        
        # 基於夏普比率的建議
        if metrics.sharpe_ratio < 1.0:
            recommendations.append("改善風險調整報酬，專注於高期望值的投注")
        
        # 基於信心度校準的建議
        if metrics.confidence_calibration < 0.85:
            recommendations.append("改善信心度評估，重新訓練模型或調整參數")
        
        if not recommendations:
            recommendations.append("維持當前策略，持續監控表現")
        
        return recommendations
    
    def _print_performance_summary(self, metrics: PerformanceMetrics):
        """列印表現摘要"""
        print(f"\n📈 表現摘要 ({metrics.period_start} 至 {metrics.period_end}):")
        print(f"  總投注數: {metrics.total_bets}")
        print(f"  勝率: {metrics.win_rate:.1%}")
        print(f"  總投注金額: ${metrics.total_staked:.2f}")
        print(f"  總利潤: ${metrics.total_profit:.2f}")
        print(f"  整體ROI: {metrics.overall_roi:.2%}")
        print(f"  夏普比率: {metrics.sharpe_ratio:.2f}")
        print(f"  最大回撤: {metrics.max_drawdown:.1%}")
        print(f"  利潤因子: {metrics.profit_factor:.2f}")

def main():
    """測試盈利監控系統"""
    print("🚀 MLB實時盈利監控系統")
    print("=" * 60)
    
    monitor = ProfitMonitoringSystem()
    
    # 模擬一些投注結果進行測試
    test_results = [
        BettingResult("2025-08-15", "NYY vs BOS", "total", "over", 0.85, 100, 1.91, "win", 91, 0.91),
        BettingResult("2025-08-15", "LAD vs SF", "spread", "home", 0.75, 100, 1.91, "loss", -100, -1.0),
        BettingResult("2025-08-14", "CHC vs STL", "total", "over", 0.90, 100, 1.91, "win", 91, 0.91),
    ]
    
    # 記錄測試結果
    for result in test_results:
        monitor.record_betting_result(result)
    
    # 生成報告
    report = monitor.generate_performance_report(7)
    
    if 'error' not in report:
        print(f"\n✅ 盈利監控系統測試完成!")
        print(f"🎯 核心功能: 實時ROI追蹤 + 風險監控 + 表現分析")
    else:
        print(f"❌ 測試失敗: {report['error']}")

if __name__ == "__main__":
    main()