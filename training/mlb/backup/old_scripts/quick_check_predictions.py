#!/usr/bin/env python3
"""
快速檢查預測和球隊統計
"""

import sqlite3
import os

def quick_check():
    """快速檢查數據庫"""
    db_path = "mlb_data.db"
    if not os.path.exists(db_path):
        print(f"❌ 數據庫文件不存在: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("🔍 檢查最近預測")
    print("=" * 50)
    
    # 檢查最近的預測
    cursor.execute("""
        SELECT p.predicted_away_score, p.predicted_home_score, 
               g.away_team, g.home_team, g.date
        FROM predictions p
        JOIN games g ON p.game_id = g.game_id
        ORDER BY g.date DESC
        LIMIT 10
    """)
    
    predictions = cursor.fetchall()
    if predictions:
        print("最近10個預測:")
        for pred in predictions:
            away_score, home_score, away_team, home_team, date = pred
            print(f"{away_team} @ {home_team}: {away_score:.1f} - {home_score:.1f} ({date})")
        
        # 分析預測分數
        away_scores = [p[0] for p in predictions]
        home_scores = [p[1] for p in predictions]
        
        print(f"\n📊 預測分數統計:")
        print(f"客隊得分: {min(away_scores):.1f} - {max(away_scores):.1f}")
        print(f"主隊得分: {min(home_scores):.1f} - {max(home_scores):.1f}")
        
        # 檢查是否都相同
        unique_away = len(set(f"{s:.1f}" for s in away_scores))
        unique_home = len(set(f"{s:.1f}" for s in home_scores))
        
        if unique_away == 1 and unique_home == 1:
            print("❌ 問題: 所有預測分數都相同!")
        else:
            print(f"✅ 預測變異性: 客隊{unique_away}種, 主隊{unique_home}種")
    else:
        print("❌ 沒有找到預測數據")
    
    print(f"\n🏟️ 檢查球隊統計")
    print("=" * 50)
    
    # 檢查球隊統計
    cursor.execute("""
        SELECT team_id, runs_per_game, era, win_percentage
        FROM team_stats
        ORDER BY runs_per_game DESC
        LIMIT 10
    """)
    
    team_stats = cursor.fetchall()
    if team_stats:
        print("球隊得分能力排名:")
        for i, (team, rpg, era, win_pct) in enumerate(team_stats, 1):
            print(f"{i:2d}. {team}: {rpg:.2f}分/場, ERA={era:.2f}, 勝率={win_pct:.3f}")
        
        # 分析統計差異
        runs_list = [t[1] for t in team_stats]
        era_list = [t[2] for t in team_stats]
        
        print(f"\n📈 球隊統計變異:")
        print(f"得分範圍: {min(runs_list):.2f} - {max(runs_list):.2f}")
        print(f"ERA範圍: {min(era_list):.2f} - {max(era_list):.2f}")
        
        if max(runs_list) - min(runs_list) < 1.0:
            print("❌ 問題: 球隊得分差異太小")
        else:
            print("✅ 球隊統計有合理差異")
    else:
        print("❌ 沒有找到球隊統計數據")
    
    # 檢查模型文件
    print(f"\n🤖 檢查模型文件")
    print("=" * 50)
    
    model_dir = "models/saved"
    if os.path.exists(model_dir):
        files = os.listdir(model_dir)
        print(f"模型文件數量: {len(files)}")
        for f in files:
            size = os.path.getsize(os.path.join(model_dir, f))
            print(f"  {f}: {size/1024:.1f} KB")
    else:
        print("❌ 模型目錄不存在")
    
    conn.close()

if __name__ == "__main__":
    quick_check()
