#!/usr/bin/env python3
"""
改進的MLB預測接口 v3.0
整合數據質量改進、偏差修正和sklearn兼容性修復
"""

import joblib
import numpy as np
import pandas as pd
from datetime import datetime
import os

class ImprovedMLBPredictor:
    """改進的MLB預測器"""
    
    def __init__(self, model_dir="models/improved_v3.0"):
        self.model_dir = model_dir
        self.models = {}
        self.scaler = None
        self.load_models()
    
    def load_models(self):
        """載入模型"""
        try:
            self.models['home_score'] = joblib.load(os.path.join(self.model_dir, 'home_score_model.joblib'))
            self.models['away_score'] = joblib.load(os.path.join(self.model_dir, 'away_score_model.joblib'))
            self.models['win_probability'] = joblib.load(os.path.join(self.model_dir, 'win_probability_model.joblib'))
            self.scaler = joblib.load(os.path.join(self.model_dir, 'feature_scaler.joblib'))
            print("✅ 改進模型載入成功")
        except Exception as e:
            print(f"❌ 模型載入失敗: {e}")
    
    def predict_game(self, features: dict) -> dict:
        """預測比賽結果"""
        try:
            # 準備特徵
            feature_array = self.prepare_features(features)
            feature_scaled = self.scaler.transform([feature_array])
            
            # 預測
            home_score = self.models['home_score'].predict(feature_scaled)[0]
            away_score = self.models['away_score'].predict(feature_scaled)[0]
            win_prob = self.models['win_probability'].predict_proba(feature_scaled)[0]
            
            # 應用偏差修正（已內建在模型中）
            home_score = max(0, min(20, home_score))
            away_score = max(0, min(20, away_score))
            
            return {
                'predicted_home_score': round(home_score, 1),
                'predicted_away_score': round(away_score, 1),
                'home_win_probability': round(win_prob[1], 3),
                'away_win_probability': round(win_prob[0], 3),
                'total_score': round(home_score + away_score, 1),
                'confidence': round(max(win_prob), 3),
                'model_version': 'v3.0_improved'
            }
            
        except Exception as e:
            print(f"預測失敗: {e}")
            return None
    
    def prepare_features(self, features: dict) -> np.ndarray:
        """準備特徵數組"""
        # 這裡需要根據實際的特徵列順序來調整
        # 示例特徵順序
        feature_order = [
            'home_wins', 'home_losses', 'home_runs_scored', 'home_runs_allowed',
            'home_era', 'home_batting_avg', 'home_win_rate', 'home_run_diff',
            'away_wins', 'away_losses', 'away_runs_scored', 'away_runs_allowed',
            'away_era', 'away_batting_avg', 'away_win_rate', 'away_run_diff',
            'home_advantage', 'home_recent_performance', 'away_recent_performance',
            'h2h_home_advantage', 'h2h_avg_home_score', 'h2h_avg_away_score',
            'month', 'day_of_week', 'is_weekend'
        ]
        
        feature_array = []
        for feature_name in feature_order:
            feature_array.append(features.get(feature_name, 0))
        
        return np.array(feature_array)

def main():
    """測試預測器"""
    predictor = ImprovedMLBPredictor()
    
    # 示例特徵
    sample_features = {
        'home_wins': 50, 'home_losses': 40, 'home_runs_scored': 450, 
        'home_runs_allowed': 400, 'home_era': 3.8, 'home_batting_avg': 0.265,
        'away_wins': 45, 'away_losses': 45, 'away_runs_scored': 420,
        'away_runs_allowed': 430, 'away_era': 4.1, 'away_batting_avg': 0.255,
        'month': 7, 'day_of_week': 1, 'is_weekend': 0
    }
    
    prediction = predictor.predict_game(sample_features)
    print(f"預測結果: {prediction}")

if __name__ == "__main__":
    main()
