#!/usr/bin/env python3
"""
監控數據下載進度
"""

import time
import sqlite3
from datetime import datetime

def check_progress():
    """檢查數據庫進度"""
    try:
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        # 查詢各表記錄數
        queries = {
            'Games': 'SELECT COUNT(*) FROM games',
            'Teams': 'SELECT COUNT(*) FROM teams',
            'Players': 'SELECT COUNT(*) FROM players',
            'Player Stats': 'SELECT COUNT(*) FROM player_stats',
            'Box Scores': 'SELECT COUNT(*) FROM box_scores',
            'Player Game Stats': 'SELECT COUNT(*) FROM player_game_stats'
        }
        
        print(f"\n📊 數據庫狀態 - {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 50)
        
        for table_name, query in queries.items():
            cursor.execute(query)
            count = cursor.fetchone()[0]
            print(f"{table_name:18}: {count:,}")
        
        # 查詢球員統計按賽季分布
        cursor.execute("""
            SELECT season, COUNT(*) 
            FROM player_stats 
            GROUP BY season 
            ORDER BY season DESC
        """)
        
        stats_by_season = cursor.fetchall()
        if stats_by_season:
            print("\n球員統計按賽季:")
            for season, count in stats_by_season:
                print(f"  {season}賽季: {count:,} 條")
        
        conn.close()
        
    except Exception as e:
        print(f"檢查進度失敗: {e}")

if __name__ == '__main__':
    while True:
        check_progress()
        time.sleep(30)  # 每30秒檢查一次
