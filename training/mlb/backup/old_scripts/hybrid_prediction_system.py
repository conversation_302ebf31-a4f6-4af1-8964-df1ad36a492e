#!/usr/bin/env python3
"""
混合MLB預測系統
整合機器學習模型和歷史分析的優勢
針對不同投注類型使用最佳預測方法
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import json

# 添加路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from improved_mlb_predictor import ImprovedMLBPredictor
except ImportError:
    print("⚠️ 改進的ML預測器不可用，使用模擬")
    
try:
    from historical_backtest_engine import HistoricalBacktester
except ImportError:
    print("⚠️ 歷史回測引擎不可用")

class HybridMLBPredictionSystem:
    """混合MLB預測系統"""
    
    def __init__(self):
        self.ml_predictor = None
        self.historical_predictor = None
        self.performance_weights = {
            'spread': {'ml': 0.3, 'historical': 0.7},  # 讓分偏向歷史分析
            'total': {'ml': 0.8, 'historical': 0.2},   # 總分偏向ML模型
            'win': {'ml': 0.9, 'historical': 0.1}      # 勝負主要用ML模型
        }
        self.confidence_adjustments = {
            'spread': 1.2,    # 讓分表現好，提高信心
            'total': 0.8,     # 總分表現差，降低信心
            'win': 1.0        # 勝負維持原有
        }
        
        self.initialize_predictors()
        
    def initialize_predictors(self):
        """初始化預測器"""
        try:
            self.ml_predictor = ImprovedMLBPredictor()
            print("✅ ML預測器載入成功")
        except:
            print("⚠️ ML預測器載入失敗，使用模擬")
            self.ml_predictor = None
            
        try:
            self.historical_predictor = HistoricalBacktester()
            print("✅ 歷史預測器載入成功")
        except:
            print("⚠️ 歷史預測器載入失敗")
            self.historical_predictor = None
    
    def get_ml_prediction(self, features: Dict) -> Dict:
        """獲取ML模型預測"""
        if self.ml_predictor:
            try:
                return self.ml_predictor.predict_game(features)
            except:
                pass
        
        # 模擬ML預測
        return {
            'predicted_home_score': 5.2,
            'predicted_away_score': 4.8,
            'home_win_probability': 0.58,
            'away_win_probability': 0.42,
            'total_score': 10.0,
            'confidence': 0.65,
            'model_version': 'simulated_ml'
        }
    
    def get_historical_prediction(self, game_date: str, home_team: str, away_team: str) -> Dict:
        """獲取歷史分析預測"""
        if self.historical_predictor:
            try:
                features = self.historical_predictor.prepare_features_for_date(
                    game_date, home_team, away_team
                )
                return self.historical_predictor.predict_with_simple_model(features)
            except:
                pass
        
        # 模擬歷史預測
        return {
            'predicted_home_score': 4.9,
            'predicted_away_score': 4.3,
            'home_win_probability': 0.62,
            'away_win_probability': 0.38,
            'total_score': 9.2,
            'confidence': 0.72,
            'spread': 0.6
        }
    
    def calculate_hybrid_prediction(self, ml_pred: Dict, hist_pred: Dict, bet_type: str) -> Dict:
        """計算混合預測"""
        weights = self.performance_weights[bet_type]
        ml_weight = weights['ml']
        hist_weight = weights['historical']
        
        # 加權平均預測
        hybrid_pred = {
            'predicted_home_score': (ml_pred['predicted_home_score'] * ml_weight + 
                                   hist_pred['predicted_home_score'] * hist_weight),
            'predicted_away_score': (ml_pred['predicted_away_score'] * ml_weight +
                                   hist_pred['predicted_away_score'] * hist_weight),
            'home_win_probability': (ml_pred['home_win_probability'] * ml_weight +
                                   hist_pred['home_win_probability'] * hist_weight),
            'away_win_probability': (ml_pred['away_win_probability'] * ml_weight +
                                   hist_pred['away_win_probability'] * hist_weight)
        }
        
        # 計算衍生指標
        hybrid_pred['total_score'] = hybrid_pred['predicted_home_score'] + hybrid_pred['predicted_away_score']
        hybrid_pred['spread'] = hybrid_pred['predicted_home_score'] - hybrid_pred['predicted_away_score']
        
        # 混合信心度
        base_confidence = (ml_pred['confidence'] * ml_weight + hist_pred['confidence'] * hist_weight)
        adjusted_confidence = base_confidence * self.confidence_adjustments[bet_type]
        hybrid_pred['confidence'] = max(0.05, min(0.95, adjusted_confidence))
        
        # 預測來源信息
        hybrid_pred['prediction_source'] = f"hybrid_{bet_type}"
        hybrid_pred['ml_weight'] = ml_weight
        hybrid_pred['historical_weight'] = hist_weight
        
        return hybrid_pred
    
    def generate_betting_recommendations(self, predictions: Dict, game_info: Dict) -> Dict:
        """生成投注建議"""
        recommendations = {
            'game_info': game_info,
            'predictions': predictions,
            'betting_recommendations': {}
        }
        
        # 讓分投注建議
        spread_pred = predictions['spread']
        if spread_pred['confidence'] > 0.65:  # 基於回測結果的優秀表現
            spread_value = spread_pred['spread']
            recommendation = {
                'bet_type': 'spread',
                'recommendation': 'home' if spread_value > 0 else 'away',
                'predicted_spread': round(spread_value, 1),
                'confidence': spread_pred['confidence'],
                'expected_value': self.calculate_expected_value(spread_pred, 'spread'),
                'kelly_fraction': self.calculate_kelly_fraction(spread_pred, 'spread'),
                'reasoning': f"歷史分析優勢，預測讓分{spread_value:+.1f}"
            }
            recommendations['betting_recommendations']['spread'] = recommendation
        
        # 大小分投注建議
        total_pred = predictions['total']
        if total_pred['confidence'] > 0.70:  # 更嚴格的門檻
            total_value = total_pred['total_score']
            # 假設市場線為預測值的±0.5
            market_line = round(total_value)
            recommendation = {
                'bet_type': 'total',
                'recommendation': 'over' if total_value > market_line else 'under',
                'predicted_total': round(total_value, 1),
                'market_line': market_line,
                'confidence': total_pred['confidence'],
                'expected_value': self.calculate_expected_value(total_pred, 'total'),
                'kelly_fraction': self.calculate_kelly_fraction(total_pred, 'total'),
                'reasoning': f"ML模型優勢，預測總分{total_value:.1f}"
            }
            recommendations['betting_recommendations']['total'] = recommendation
        
        # 勝負盤投注建議
        win_pred = predictions['win']
        if win_pred['confidence'] > 0.75:  # 最嚴格的門檻
            home_prob = win_pred['home_win_probability']
            if home_prob > 0.6 or home_prob < 0.4:  # 明確的優勢
                recommendation = {
                    'bet_type': 'moneyline',
                    'recommendation': 'home' if home_prob > 0.5 else 'away',
                    'win_probability': home_prob,
                    'confidence': win_pred['confidence'],
                    'expected_value': self.calculate_expected_value(win_pred, 'win'),
                    'kelly_fraction': self.calculate_kelly_fraction(win_pred, 'win'),
                    'reasoning': f"ML模型主導，{home_prob:.1%}勝率"
                }
                recommendations['betting_recommendations']['moneyline'] = recommendation
        
        return recommendations
    
    def calculate_expected_value(self, prediction: Dict, bet_type: str) -> float:
        """計算期望值"""
        confidence = prediction['confidence']
        
        # 簡化的期望值計算
        if bet_type == 'spread':
            # 假設-110賠率
            win_prob = confidence
            ev = (win_prob * 0.91) - ((1 - win_prob) * 1.0)
        elif bet_type == 'total':
            # 假設-110賠率
            win_prob = confidence  
            ev = (win_prob * 0.91) - ((1 - win_prob) * 1.0)
        elif bet_type == 'win':
            # 假設根據勝率計算的賠率
            home_prob = prediction['home_win_probability']
            if home_prob > 0.6:
                odds = 1.6  # 熱門
                win_prob = home_prob
            else:
                odds = 2.4  # 冷門
                win_prob = 1 - home_prob
            ev = (win_prob * (odds - 1)) - ((1 - win_prob) * 1.0)
        else:
            ev = 0
        
        return round(ev, 3)
    
    def calculate_kelly_fraction(self, prediction: Dict, bet_type: str) -> float:
        """計算凱利比例"""
        ev = self.calculate_expected_value(prediction, bet_type)
        
        # 簡化的凱利公式
        if ev > 0:
            # f = (bp - q) / b, 其中b是賠率-1, p是勝率, q是敗率
            if bet_type in ['spread', 'total']:
                b = 0.91  # -110賠率
                p = prediction['confidence']
            else:  # moneyline
                home_prob = prediction['home_win_probability']
                if home_prob > 0.6:
                    b = 0.6
                    p = home_prob
                else:
                    b = 1.4
                    p = 1 - home_prob
            
            q = 1 - p
            kelly = (b * p - q) / b
            
            # 限制最大比例為5%
            return max(0, min(0.05, kelly))
        
        return 0
    
    def predict_game(self, game_date: str, home_team: str, away_team: str, 
                    detailed_features: Dict = None) -> Dict:
        """預測單場比賽"""
        
        print(f"🎯 混合預測: {away_team} @ {home_team} ({game_date})")
        print("-" * 50)
        
        # 準備基本特徵
        if detailed_features is None:
            detailed_features = self.prepare_basic_features(game_date, home_team, away_team)
        
        # 獲取ML預測
        print("🤖 獲取ML模型預測...")
        ml_prediction = self.get_ml_prediction(detailed_features)
        
        # 獲取歷史預測
        print("📊 獲取歷史分析預測...")
        historical_prediction = self.get_historical_prediction(game_date, home_team, away_team)
        
        # 計算各種投注類型的混合預測
        predictions = {}
        
        for bet_type in ['spread', 'total', 'win']:
            predictions[bet_type] = self.calculate_hybrid_prediction(
                ml_prediction, historical_prediction, bet_type
            )
        
        # 生成投注建議
        game_info = {
            'date': game_date,
            'home_team': home_team,
            'away_team': away_team,
            'prediction_time': datetime.now().isoformat()
        }
        
        recommendations = self.generate_betting_recommendations(predictions, game_info)
        
        # 顯示結果
        self.display_predictions(recommendations)
        
        return recommendations
    
    def prepare_basic_features(self, game_date: str, home_team: str, away_team: str) -> Dict:
        """準備基本特徵"""
        # 簡化的特徵，實際使用時應該從數據庫獲取
        return {
            'home_recent_wins': 15,
            'home_recent_games': 25,
            'home_season_wins': 45,
            'home_season_games': 85,
            'away_recent_wins': 12,
            'away_recent_games': 25,
            'away_season_wins': 40,
            'away_season_games': 85,
            'h2h_home_wins': 3,
            'h2h_away_wins': 2,
            'month': datetime.strptime(game_date, '%Y-%m-%d').month,
            'day_of_week': datetime.strptime(game_date, '%Y-%m-%d').weekday(),
            'is_weekend': 1 if datetime.strptime(game_date, '%Y-%m-%d').weekday() >= 5 else 0
        }
    
    def display_predictions(self, recommendations: Dict):
        """顯示預測結果"""
        print(f"\n📊 混合預測結果:")
        print("=" * 60)
        
        game_info = recommendations['game_info']
        predictions = recommendations['predictions']
        bets = recommendations['betting_recommendations']
        
        print(f"比賽: {game_info['away_team']} @ {game_info['home_team']}")
        print(f"日期: {game_info['date']}")
        
        print(f"\n🏠 預測分數:")
        for bet_type, pred in predictions.items():
            print(f"  {bet_type}: 主隊 {pred['predicted_home_score']:.1f} - 客隊 {pred['predicted_away_score']:.1f}")
            print(f"           總分 {pred['total_score']:.1f}, 讓分 {pred.get('spread', 0):.1f}")
            print(f"           信心度 {pred['confidence']:.1%}")
        
        print(f"\n💰 投注建議:")
        if not bets:
            print("  暫無高信心度投注建議")
        else:
            for bet_type, rec in bets.items():
                print(f"  📈 {bet_type.upper()}: {rec['recommendation']}")
                print(f"     信心度: {rec['confidence']:.1%}")
                print(f"     期望值: {rec['expected_value']:+.3f}")
                print(f"     凱利比例: {rec['kelly_fraction']:.1%}")
                print(f"     理由: {rec['reasoning']}")
                print()
    
    def batch_predict(self, games_list: List[Tuple[str, str, str]]) -> List[Dict]:
        """批量預測"""
        results = []
        
        print(f"🚀 開始批量預測 {len(games_list)} 場比賽")
        print("=" * 60)
        
        for i, (game_date, home_team, away_team) in enumerate(games_list, 1):
            print(f"\n[{i}/{len(games_list)}]", end=" ")
            
            try:
                result = self.predict_game(game_date, home_team, away_team)
                results.append(result)
            except Exception as e:
                print(f"❌ 預測失敗: {e}")
                continue
        
        print(f"\n✅ 批量預測完成，成功 {len(results)} 場")
        return results
    
    def save_predictions(self, predictions: List[Dict], filename: str = None):
        """保存預測結果"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"hybrid_predictions_{timestamp}.json"
        
        # 創建目錄
        os.makedirs("prediction_results", exist_ok=True)
        filepath = os.path.join("prediction_results", filename)
        
        # 保存結果
        output_data = {
            'metadata': {
                'prediction_system': 'hybrid_mlb_v1.0',
                'created_at': datetime.now().isoformat(),
                'total_predictions': len(predictions)
            },
            'predictions': predictions
        }
        
        with open(filepath, 'w') as f:
            json.dump(output_data, f, indent=2, default=str)
        
        print(f"💾 預測結果已保存到 {filepath}")

def main():
    """示例使用"""
    print("🚀 混合MLB預測系統演示")
    print("=" * 60)
    
    # 創建混合預測系統
    hybrid_system = HybridMLBPredictionSystem()
    
    # 示例1: 單場預測
    print("\n📍 示例1: 單場預測")
    result = hybrid_system.predict_game('2025-08-19', 'ATL', 'CWS')
    
    # 示例2: 批量預測
    print("\n📍 示例2: 批量預測")
    games_to_predict = [
        ('2025-08-19', 'NYY', 'BOS'),
        ('2025-08-19', 'LAD', 'SF'),
        ('2025-08-19', 'HOU', 'SEA')
    ]
    
    batch_results = hybrid_system.batch_predict(games_to_predict)
    
    # 保存結果
    if batch_results:
        hybrid_system.save_predictions(batch_results)
    
    print(f"\n🎉 演示完成!")
    print(f"💡 系統特點:")
    print(f"   - 讓分預測: 歷史分析主導 (70%)")
    print(f"   - 總分預測: ML模型主導 (80%)")  
    print(f"   - 勝負預測: ML模型主導 (90%)")
    print(f"   - 動態信心度調整")
    print(f"   - 期望值和凱利比例計算")

if __name__ == "__main__":
    main()