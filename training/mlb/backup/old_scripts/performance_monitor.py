#!/usr/bin/env python3
"""
MLB預測系統性能監控腳本
定期監控系統性能並記錄指標
"""

import time
import psutil
import sqlite3
import json
from datetime import datetime

def monitor_performance():
    """監控系統性能"""
    
    # 獲取系統信息
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('.')
    
    # 數據庫查詢性能測試
    start_time = time.time()
    try:
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM predictions')
        result = cursor.fetchone()
        conn.close()
        db_query_time = time.time() - start_time
    except:
        db_query_time = -1
    
    # 記錄性能指標
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'memory_available_gb': memory.available / (1024**3),
        'disk_percent': disk.percent,
        'db_query_time': db_query_time
    }
    
    # 保存到日誌文件
    with open('performance_monitor.log', 'a', encoding='utf-8') as f:
        f.write(json.dumps(metrics) + '\n')
    
    print(f"[{metrics['timestamp']}] "
          f"CPU: {cpu_percent:.1f}% | "
          f"MEM: {memory.percent:.1f}% | "
          f"DB: {db_query_time:.3f}s")

if __name__ == "__main__":
    monitor_performance()
