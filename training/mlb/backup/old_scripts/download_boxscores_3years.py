#!/usr/bin/env python3
"""
下載最近三年的Box Score數據
專門針對Box Score數據的下載腳本
"""

import logging
from datetime import date, timedelta
from app import create_app
from models.detailed_data_fetcher import DetailedDataFetcher
from models.database import db, Game, BoxScore

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_boxscores_by_year(year):
    """下載指定年份的Box Score數據"""
    logger.info(f"📊 開始下載 {year} 年的Box Score數據...")
    
    fetcher = DetailedDataFetcher()
    
    # 設定年份的日期範圍
    start_date = date(year, 1, 1)
    end_date = date(year, 12, 31)
    
    # 查找該年份沒有Box Score的已完成比賽
    games_without_boxscore = db.session.query(Game).filter(
        Game.date >= start_date,
        Game.date <= end_date,
        Game.game_status == 'completed',
        ~Game.game_id.in_(
            db.session.query(BoxScore.game_id).distinct()
        )
    ).order_by(Game.date.desc()).all()
    
    logger.info(f"找到 {len(games_without_boxscore)} 場 {year} 年需要下載Box Score的比賽")
    
    if not games_without_boxscore:
        logger.info(f"✅ {year} 年的Box Score已經完整")
        return 0
    
    success_count = 0
    for i, game in enumerate(games_without_boxscore, 1):
        try:
            logger.info(f"[{i}/{len(games_without_boxscore)}] {year} - {game.away_team} @ {game.home_team} ({game.date})")
            success = fetcher.fetch_game_boxscore(game.game_id)
            if success:
                success_count += 1
                logger.info(f"  ✓ 成功下載")
            else:
                logger.warning(f"  ✗ 下載失敗")
            
            # 每5場比賽後暫停一下，避免請求過於頻繁
            if i % 5 == 0:
                import time
                time.sleep(1)
                
                # 每50場比賽提交一次數據庫
                if i % 50 == 0:
                    try:
                        db.session.commit()
                        logger.info(f"  💾 已保存前 {i} 場比賽的數據")
                    except Exception as e:
                        logger.error(f"  ❌ 保存數據失敗: {e}")
                        db.session.rollback()
                
        except Exception as e:
            logger.error(f"下載比賽 {game.game_id} 失敗: {e}")
    
    # 最終提交
    try:
        db.session.commit()
        logger.info(f"✅ {year} 年Box Score下載完成: {success_count}/{len(games_without_boxscore)} 成功")
    except Exception as e:
        logger.error(f"❌ 最終保存失敗: {e}")
        db.session.rollback()
    
    return success_count

def print_boxscore_summary():
    """打印Box Score數據摘要"""
    logger.info("📈 Box Score數據摘要:")
    
    # 總數
    total_boxscores = BoxScore.query.count()
    logger.info(f"  總Box Score數量: {total_boxscores:,}")
    
    # 按年份統計
    boxscores_by_year = db.session.query(
        db.func.strftime('%Y', Game.date).label('year'),
        db.func.count(BoxScore.box_score_id).label('count')
    ).join(Game, BoxScore.game_id == Game.game_id)\
     .group_by(db.func.strftime('%Y', Game.date))\
     .order_by('year').all()
    
    logger.info("  Box Score按年份分布:")
    for year, count in boxscores_by_year:
        logger.info(f"    {year}年: {count:,} 場")
    
    # 統計缺失的Box Score
    current_year = date.today().year
    for year in [current_year-2, current_year-1, current_year]:
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        
        total_games = Game.query.filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.game_status == 'completed'
        ).count()
        
        games_with_boxscore = db.session.query(Game).filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.game_status == 'completed',
            Game.game_id.in_(
                db.session.query(BoxScore.game_id).distinct()
            )
        ).count()
        
        missing = total_games - games_with_boxscore
        logger.info(f"  {year}年: {games_with_boxscore}/{total_games} 場有Box Score，缺失 {missing} 場")

def main():
    """主函數"""
    app = create_app()
    
    with app.app_context():
        logger.info("🚀 開始下載最近三年的Box Score數據...")
        
        # 打印初始狀態
        print_boxscore_summary()
        
        # 獲取當前年份
        current_year = date.today().year
        
        # 下載最近三年的數據（從最新年份開始）
        total_downloaded = 0
        for year in [current_year, current_year-1, current_year-2]:
            try:
                downloaded = download_boxscores_by_year(year)
                total_downloaded += downloaded
                
                # 每年之間暫停一下
                import time
                time.sleep(3)
                
            except Exception as e:
                logger.error(f"下載 {year} 年數據失敗: {e}")
        
        logger.info(f"🎉 三年Box Score下載完成！總共下載了 {total_downloaded} 場比賽")
        
        # 打印最終摘要
        print_boxscore_summary()

if __name__ == '__main__':
    main()
