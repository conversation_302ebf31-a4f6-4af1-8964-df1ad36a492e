#!/usr/bin/env python3
"""
MLB預測系統性能優化器
提升系統響應速度、內存使用效率和整體性能
"""

import os
import sqlite3
import time
import psutil
import json
from datetime import datetime
from typing import Dict, List, Optional

class SystemPerformanceOptimizer:
    """系統性能優化器"""
    
    def __init__(self, db_path='instance/mlb_data.db'):
        self.db_path = db_path
        self.optimization_results = {}
        
    def analyze_database_performance(self) -> Dict:
        """分析數據庫性能"""
        print("📊 數據庫性能分析")
        print("=" * 50)
        
        try:
            # 檢查數據庫文件大小
            db_size = os.path.getsize(self.db_path) / (1024 * 1024)  # MB
            print(f"數據庫大小: {db_size:.1f} MB")
            
            # 測試查詢性能
            start_time = time.time()
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 測試複雜查詢
            cursor.execute('''
                SELECT COUNT(*) FROM predictions p 
                JOIN games g ON p.game_id = g.game_id 
                WHERE p.created_at >= date('now', '-30 days')
            ''')
            result = cursor.fetchone()
            query_time = time.time() - start_time
            
            print(f"複雜查詢執行時間: {query_time:.3f}秒")
            print(f"最近30天預測數: {result[0]}")
            
            # 檢查索引使用情況
            cursor.execute("PRAGMA index_list(predictions)")
            indexes = cursor.fetchall()
            print(f"predictions表索引數量: {len(indexes)}")
            
            conn.close()
            
            return {
                'db_size_mb': db_size,
                'query_time': query_time,
                'indexes_count': len(indexes),
                'performance_score': self._calculate_db_performance_score(db_size, query_time)
            }
            
        except Exception as e:
            print(f"❌ 數據庫分析錯誤: {e}")
            return {}
    
    def _calculate_db_performance_score(self, size_mb: float, query_time: float) -> int:
        """計算數據庫性能評分 (0-100)"""
        # 基於大小和查詢速度的評分
        size_score = max(0, 100 - (size_mb / 50) * 10)  # 50MB為基準
        speed_score = max(0, 100 - (query_time * 1000))  # 1秒為基準
        
        return int((size_score + speed_score) / 2)
    
    def optimize_database_indexes(self) -> bool:
        """優化數據庫索引"""
        print("\\n🔧 優化數據庫索引...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 創建必要的索引
            indexes_to_create = [
                ("idx_predictions_game_id", "CREATE INDEX IF NOT EXISTS idx_predictions_game_id ON predictions(game_id)"),
                ("idx_predictions_created_at", "CREATE INDEX IF NOT EXISTS idx_predictions_created_at ON predictions(created_at)"),
                ("idx_predictions_model_version", "CREATE INDEX IF NOT EXISTS idx_predictions_model_version ON predictions(model_version)"),
                ("idx_games_date", "CREATE INDEX IF NOT EXISTS idx_games_date ON games(date)"),
                ("idx_games_teams", "CREATE INDEX IF NOT EXISTS idx_games_teams ON games(home_team, away_team)"),
                ("idx_predictions_validation", "CREATE INDEX IF NOT EXISTS idx_predictions_validation ON predictions(is_correct, actual_home_score)")
            ]
            
            created_count = 0
            for index_name, sql in indexes_to_create:
                try:
                    cursor.execute(sql)
                    created_count += 1
                    print(f"  ✅ 創建索引: {index_name}")
                except Exception as e:
                    print(f"  ⚠️  索引已存在或創建失敗: {index_name}")
            
            # 優化數據庫
            cursor.execute("VACUUM")
            cursor.execute("ANALYZE")
            
            conn.commit()
            conn.close()
            
            print(f"✅ 索引優化完成，創建了 {created_count} 個索引")
            return True
            
        except Exception as e:
            print(f"❌ 索引優化錯誤: {e}")
            return False
    
    def analyze_memory_usage(self) -> Dict:
        """分析內存使用情況"""
        print("\\n💾 內存使用分析")
        print("=" * 50)
        
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            # 系統內存信息
            system_memory = psutil.virtual_memory()
            
            print(f"當前進程內存使用: {memory_info.rss / (1024*1024):.1f} MB")
            print(f"內存使用百分比: {memory_percent:.1f}%")
            print(f"系統總內存: {system_memory.total / (1024*1024*1024):.1f} GB")
            print(f"系統可用內存: {system_memory.available / (1024*1024*1024):.1f} GB")
            
            return {
                'process_memory_mb': memory_info.rss / (1024*1024),
                'memory_percent': memory_percent,
                'system_available_gb': system_memory.available / (1024*1024*1024),
                'memory_score': self._calculate_memory_score(memory_percent, system_memory.percent)
            }
            
        except Exception as e:
            print(f"❌ 內存分析錯誤: {e}")
            return {}
    
    def _calculate_memory_score(self, process_percent: float, system_percent: float) -> int:
        """計算內存性能評分"""
        # 進程內存使用越少越好，系統剩餘內存越多越好
        process_score = max(0, 100 - process_percent * 2)
        system_score = max(0, 100 - system_percent)
        
        return int((process_score + system_score) / 2)
    
    def optimize_model_loading(self) -> Dict:
        """優化模型載入"""
        print("\\n🧠 模型載入優化")
        print("=" * 50)
        
        try:
            # 檢查模型文件
            model_dirs = ['models/saved', 'models/calibrated_v2.0', 'models/enhanced']
            total_model_size = 0
            model_count = 0
            
            for model_dir in model_dirs:
                if os.path.exists(model_dir):
                    for file in os.listdir(model_dir):
                        if file.endswith(('.pkl', '.joblib')):
                            file_path = os.path.join(model_dir, file)
                            file_size = os.path.getsize(file_path) / (1024*1024)  # MB
                            total_model_size += file_size
                            model_count += 1
            
            print(f"模型文件總數: {model_count}")
            print(f"模型文件總大小: {total_model_size:.1f} MB")
            
            # 測試模型載入時間
            start_time = time.time()
            
            # 模擬載入操作
            try:
                import joblib
                import pickle
                
                # 測試載入一個小文件
                test_files = []
                for model_dir in model_dirs:
                    if os.path.exists(model_dir):
                        files = [f for f in os.listdir(model_dir) if f.endswith('.pkl')]
                        if files:
                            test_files.append(os.path.join(model_dir, files[0]))
                            break
                
                if test_files:
                    with open(test_files[0], 'rb') as f:
                        pickle.load(f)
                
                load_time = time.time() - start_time
                print(f"模型載入測試時間: {load_time:.3f}秒")
                
            except Exception as e:
                load_time = 0
                print(f"⚠️  模型載入測試失敗: {e}")
            
            return {
                'model_count': model_count,
                'total_size_mb': total_model_size,
                'load_time': load_time,
                'loading_score': self._calculate_loading_score(total_model_size, load_time)
            }
            
        except Exception as e:
            print(f"❌ 模型分析錯誤: {e}")
            return {}
    
    def _calculate_loading_score(self, size_mb: float, load_time: float) -> int:
        """計算載入性能評分"""
        # 文件越小載入越快評分越高
        size_score = max(0, 100 - (size_mb / 10) * 5)  # 10MB為基準
        speed_score = max(0, 100 - (load_time * 100))  # 1秒為基準
        
        return int((size_score + speed_score) / 2)
    
    def create_performance_monitoring(self) -> bool:
        """創建性能監控腳本"""
        print("\\n📈 創建性能監控腳本")
        print("=" * 50)
        
        monitor_script = '''#!/usr/bin/env python3
"""
MLB預測系統性能監控腳本
定期監控系統性能並記錄指標
"""

import time
import psutil
import sqlite3
import json
from datetime import datetime

def monitor_performance():
    """監控系統性能"""
    
    # 獲取系統信息
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('.')
    
    # 數據庫查詢性能測試
    start_time = time.time()
    try:
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM predictions')
        result = cursor.fetchone()
        conn.close()
        db_query_time = time.time() - start_time
    except:
        db_query_time = -1
    
    # 記錄性能指標
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'memory_available_gb': memory.available / (1024**3),
        'disk_percent': disk.percent,
        'db_query_time': db_query_time
    }
    
    # 保存到日誌文件
    with open('performance_monitor.log', 'a', encoding='utf-8') as f:
        f.write(json.dumps(metrics) + '\\n')
    
    print(f"[{metrics['timestamp']}] "
          f"CPU: {cpu_percent:.1f}% | "
          f"MEM: {memory.percent:.1f}% | "
          f"DB: {db_query_time:.3f}s")

if __name__ == "__main__":
    monitor_performance()
'''
        
        try:
            with open('performance_monitor.py', 'w', encoding='utf-8') as f:
                f.write(monitor_script)
            
            # 設置執行權限
            os.chmod('performance_monitor.py', 0o755)
            
            print("✅ 性能監控腳本已創建: performance_monitor.py")
            print("   使用方法: python performance_monitor.py")
            return True
            
        except Exception as e:
            print(f"❌ 監控腳本創建錯誤: {e}")
            return False
    
    def generate_optimization_recommendations(self, db_stats: Dict, memory_stats: Dict, model_stats: Dict) -> List[str]:
        """生成優化建議"""
        recommendations = []
        
        # 數據庫優化建議
        if db_stats.get('performance_score', 0) < 80:
            recommendations.append("🔧 建議優化數據庫查詢和索引")
        
        if db_stats.get('db_size_mb', 0) > 100:
            recommendations.append("📦 考慮數據庫歸檔或清理舊數據")
        
        # 內存優化建議
        if memory_stats.get('memory_percent', 0) > 50:
            recommendations.append("💾 考慮優化內存使用或增加系統內存")
        
        # 模型優化建議
        if model_stats.get('total_size_mb', 0) > 50:
            recommendations.append("🧠 考慮壓縮或優化模型文件")
        
        if model_stats.get('load_time', 0) > 0.5:
            recommendations.append("⚡ 考慮實施模型預載入或緩存機制")
        
        # 通用建議
        recommendations.extend([
            "📈 定期運行性能監控腳本",
            "🔄 考慮實施預測結果緩存",
            "⚙️  優化數據庫連接池配置",
            "🚀 考慮使用異步處理提升並發性能"
        ])
        
        return recommendations
    
    def generate_performance_report(self, db_stats: Dict, memory_stats: Dict, model_stats: Dict, recommendations: List[str]):
        """生成性能報告"""
        
        overall_score = (
            db_stats.get('performance_score', 0) * 0.4 +
            memory_stats.get('memory_score', 0) * 0.3 +
            model_stats.get('loading_score', 0) * 0.3
        )
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_performance_score': overall_score,
            'database_performance': db_stats,
            'memory_performance': memory_stats,
            'model_performance': model_stats,
            'optimization_recommendations': recommendations,
            'status': 'excellent' if overall_score >= 90 else 'good' if overall_score >= 70 else 'needs_improvement'
        }
        
        filename = f"system_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"\\n📄 性能報告已保存: {filename}")
            print(f"📊 系統整體性能評分: {overall_score:.1f}/100")
            print(f"🎯 性能狀態: {report['status']}")
            
        except Exception as e:
            print(f"❌ 報告保存錯誤: {e}")

def main():
    """主執行函數"""
    print("🚀 MLB預測系統性能優化器")
    print("=" * 60)
    
    optimizer = SystemPerformanceOptimizer()
    
    # 1. 分析數據庫性能
    db_stats = optimizer.analyze_database_performance()
    
    # 2. 優化數據庫索引
    optimizer.optimize_database_indexes()
    
    # 3. 分析內存使用
    memory_stats = optimizer.analyze_memory_usage()
    
    # 4. 分析模型載入性能
    model_stats = optimizer.optimize_model_loading()
    
    # 5. 創建性能監控腳本
    optimizer.create_performance_monitoring()
    
    # 6. 生成優化建議
    recommendations = optimizer.generate_optimization_recommendations(db_stats, memory_stats, model_stats)
    
    print("\\n💡 優化建議:")
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. {rec}")
    
    # 7. 生成性能報告
    optimizer.generate_performance_report(db_stats, memory_stats, model_stats, recommendations)
    
    print(f"\\n✅ 系統性能優化完成!")
    print(f"🔍 建議定期執行性能監控: python performance_monitor.py")

if __name__ == "__main__":
    main()