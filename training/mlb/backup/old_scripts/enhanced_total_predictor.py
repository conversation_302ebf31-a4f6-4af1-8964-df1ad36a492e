#!/usr/bin/env python3
"""
MLB增強大小分預測器
專門優化Over/Under預測準確率，目標從64%提升至70%+
基於calibrated_v2.0_optimized_v1.0的高準確率(80%)進行優化
"""

import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json

class EnhancedTotalPredictor:
    """增強大小分預測器"""
    
    def __init__(self, db_path='instance/mlb_data.db'):
        self.db_path = db_path
        self.total_factors = self._initialize_total_factors()
        self.ballpark_cache = {}
        self.weather_cache = {}
        
    def _initialize_total_factors(self) -> Dict:
        """初始化大小分影響因子"""
        return {
            'base_total': 9.0,                    # MLB標準大小分線
            'team_offense_weight': 0.25,          # 球隊攻擊力權重
            'team_defense_weight': 0.20,          # 球隊防守權重
            'pitcher_quality_weight': 0.25,       # 投手質量權重
            'ballpark_factor_weight': 0.15,       # 球場因子權重
            'weather_factor_weight': 0.10,        # 天氣因子權重
            'recent_form_weight': 0.05,           # 近期狀態權重
            'max_total_adjustment': 3.0,          # 最大總分調整範圍
        }
    
    def calculate_team_offensive_power(self, team: str, date: str, days_back: int = 20) -> Dict:
        """計算球隊攻擊力指標"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 獲取球隊近期攻擊數據
            cursor.execute('''
                SELECT 
                    COUNT(*) as games_played,
                    AVG(CASE WHEN home_team = ? THEN home_score ELSE away_score END) as avg_runs_scored,
                    AVG(CASE WHEN home_team = ? THEN home_score ELSE away_score END + 
                        CASE WHEN away_team = ? THEN away_score ELSE home_score END) as avg_total_runs,
                    SUM(CASE WHEN (home_team = ? AND home_score > 5) 
                        OR (away_team = ? AND away_score > 5) THEN 1 ELSE 0 END) as high_scoring_games
                FROM games 
                WHERE (home_team = ? OR away_team = ?)
                AND date >= date(?, '-{} days')
                AND date < ?
                AND home_score IS NOT NULL
                AND away_score IS NOT NULL
            '''.format(days_back), (team, team, team, team, team, team, team, date, date))
            
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0] > 0:
                games_played, avg_scored, avg_total, high_scoring = result
                
                return {
                    'games_played': games_played,
                    'avg_runs_scored': avg_scored or 4.5,
                    'avg_total_involved': avg_total or 9.0,
                    'high_scoring_rate': (high_scoring or 0) / games_played,
                    'offensive_strength': max(0.5, min(2.0, (avg_scored or 4.5) / 4.5))
                }
            else:
                return self._get_default_offensive_metrics()
                
        except Exception as e:
            print(f"❌ 攻擊力計算錯誤 ({team}): {e}")
            return self._get_default_offensive_metrics()
    
    def calculate_team_defensive_impact(self, team: str, date: str, days_back: int = 20) -> Dict:
        """計算球隊防守對總分的影響"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 獲取球隊防守數據（失分情況）
            cursor.execute('''
                SELECT 
                    COUNT(*) as games_played,
                    AVG(CASE WHEN home_team = ? THEN away_score ELSE home_score END) as avg_runs_allowed,
                    AVG(CASE WHEN home_team = ? THEN home_score + away_score ELSE away_score + home_score END) as avg_total_runs,
                    SUM(CASE WHEN (home_team = ? AND away_score < 3) 
                        OR (away_team = ? AND home_score < 3) THEN 1 ELSE 0 END) as low_scoring_defense
                FROM games 
                WHERE (home_team = ? OR away_team = ?)
                AND date >= date(?, '-{} days')
                AND date < ?
                AND home_score IS NOT NULL
                AND away_score IS NOT NULL
            '''.format(days_back), (team, team, team, team, team, team, date, date))
            
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0] > 0:
                games_played, avg_allowed, avg_total, low_scoring = result
                
                return {
                    'avg_runs_allowed': avg_allowed or 4.5,
                    'avg_total_involved': avg_total or 9.0,
                    'low_scoring_defense_rate': (low_scoring or 0) / games_played,
                    'defensive_strength': max(0.5, min(2.0, 4.5 / (avg_allowed or 4.5))),  # 防守越好值越高
                    'total_suppression_factor': max(0.8, min(1.2, 9.0 / (avg_total or 9.0)))
                }
            else:
                return self._get_default_defensive_metrics()
                
        except Exception as e:
            print(f"❌ 防守影響計算錯誤 ({team}): {e}")
            return self._get_default_defensive_metrics()
    
    def calculate_pitcher_total_impact(self, home_team: str, away_team: str, date: str) -> Dict:
        """計算投手對總分的影響"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查找最近的投手數據
            cursor.execute('''
                SELECT starting_pitcher_home, starting_pitcher_away,
                       predicted_total_runs, over_under_confidence
                FROM predictions p
                JOIN games g ON p.game_id = g.game_id
                WHERE g.home_team = ? AND g.away_team = ?
                AND g.date >= date(?, '-10 days')
                AND p.starting_pitcher_home IS NOT NULL
                AND p.starting_pitcher_away IS NOT NULL
                ORDER BY g.date DESC
                LIMIT 1
            ''', (home_team, away_team, date))
            
            pitcher_data = cursor.fetchone()
            
            if pitcher_data:
                home_pitcher, away_pitcher, predicted_total, confidence = pitcher_data
                
                # 查找投手的歷史表現對總分的影響
                cursor.execute('''
                    SELECT AVG(g.home_score + g.away_score) as avg_total_in_games
                    FROM predictions p
                    JOIN games g ON p.game_id = g.game_id  
                    WHERE (p.starting_pitcher_home LIKE ? OR p.starting_pitcher_away LIKE ?)
                    AND g.date >= date(?, '-60 days')
                    AND g.home_score IS NOT NULL
                    AND g.away_score IS NOT NULL
                ''', (f'%{home_pitcher}%', f'%{away_pitcher}%', date))
                
                historical_total = cursor.fetchone()[0] or 9.0
                
                conn.close()
                
                # 投手質量評估
                pitcher_impact = {
                    'home_pitcher': home_pitcher,
                    'away_pitcher': away_pitcher,
                    'historical_avg_total': historical_total,
                    'pitcher_total_factor': max(0.7, min(1.3, historical_total / 9.0)),
                    'confidence_adjustment': (confidence or 0.5) - 0.5,  # -0.5 到 +0.5
                    'total_adjustment': (historical_total - 9.0) * 0.3
                }
                
                return pitcher_impact
            else:
                conn.close()
                return self._get_default_pitcher_metrics()
                
        except Exception as e:
            print(f"❌ 投手影響計算錯誤: {e}")
            return self._get_default_pitcher_metrics()
    
    def get_enhanced_ballpark_factor(self, home_team: str) -> Dict:
        """獲取增強的球場因子"""
        if home_team in self.ballpark_cache:
            return self.ballpark_cache[home_team]
        
        # 詳細的MLB球場得分因子
        ballpark_factors = {
            # 高得分球場
            'COL': {'factor': 1.20, 'reason': 'Coors Field - 高海拔，空氣稀薄'},
            'BOS': {'factor': 1.12, 'reason': 'Fenway Park - 綠色怪物，短左場'},
            'TEX': {'factor': 1.08, 'reason': 'Globe Life Field - 新球場，利於打者'},
            'CIN': {'factor': 1.05, 'reason': 'Great American Ball Park - 風向助攻'},
            'BAL': {'factor': 1.04, 'reason': 'Camden Yards - 短右場'},
            'PHI': {'factor': 1.03, 'reason': 'Citizens Bank Park - 適中偏利打者'},
            
            # 中性球場
            'NYY': {'factor': 1.00, 'reason': 'Yankee Stadium - 標準'},
            'LAD': {'factor': 1.00, 'reason': 'Dodger Stadium - 標準'},
            'HOU': {'factor': 1.00, 'reason': 'Minute Maid Park - 標準'},
            'ATL': {'factor': 1.00, 'reason': 'Truist Park - 標準'},
            'CHC': {'factor': 0.98, 'reason': 'Wrigley Field - 風向多變'},
            
            # 投手友善球場
            'SF': {'factor': 0.88, 'reason': 'Oracle Park - 冷風，大外場'},
            'SEA': {'factor': 0.92, 'reason': 'T-Mobile Park - 濕潤氣候'},
            'SD': {'factor': 0.90, 'reason': 'Petco Park - 大外野，海風'},
            'MIA': {'factor': 0.85, 'reason': 'loanDepot park - 室內，空調'},
            'DET': {'factor': 0.94, 'reason': 'Comerica Park - 大外野'},
            'KC': {'factor': 0.96, 'reason': 'Kauffman Stadium - 大外野'},
        }
        
        ballpark_data = ballpark_factors.get(home_team, {'factor': 1.00, 'reason': '標準球場'})
        
        # 季節性調整
        current_date = datetime.now()
        month = current_date.month
        
        seasonal_adjustment = {
            4: -0.03,   # 四月較冷，總分偏低
            5: 0.00,    # 五月適中
            6: 0.02,    # 六月溫暖
            7: 0.04,    # 七月炎熱，利於得分
            8: 0.04,    # 八月炎熱
            9: 0.01,    # 九月轉涼
            10: -0.02   # 十月較冷
        }.get(month, 0.00)
        
        enhanced_factor = ballpark_data['factor'] + seasonal_adjustment
        
        result = {
            'base_factor': ballpark_data['factor'],
            'seasonal_adjustment': seasonal_adjustment,
            'enhanced_factor': max(0.75, min(1.35, enhanced_factor)),
            'reason': ballpark_data['reason']
        }
        
        self.ballpark_cache[home_team] = result
        return result
    
    def calculate_weather_impact(self, date: str) -> Dict:
        """計算天氣對總分的影響"""
        if date in self.weather_cache:
            return self.weather_cache[date]
        
        try:
            # 簡化的天氣影響模型（基於日期和季節）
            game_date = datetime.strptime(date, '%Y-%m-%d')
            month = game_date.month
            day_of_year = game_date.timetuple().tm_yday
            
            # 溫度估算（基於季節）
            base_temp = 50 + 30 * np.sin(2 * np.pi * (day_of_year - 80) / 365)
            
            # 溫度對總分的影響
            if base_temp > 85:  # 炎熱天氣
                temp_factor = 1.08
                weather_desc = "炎熱，利於得分"
            elif base_temp > 70:  # 溫暖天氣
                temp_factor = 1.03
                weather_desc = "溫暖，適中偏利得分"
            elif base_temp > 50:  # 涼爽天氣
                temp_factor = 0.98
                weather_desc = "涼爽，略不利得分"
            else:  # 寒冷天氣
                temp_factor = 0.92
                weather_desc = "寒冷，不利得分"
            
            # 風力影響（簡化模擬）
            wind_factor = 1.0 + np.random.uniform(-0.02, 0.02)  # ±2%的隨機風力影響
            
            # 整體天氣影響
            overall_weather_factor = temp_factor * wind_factor
            total_adjustment = (overall_weather_factor - 1.0) * 9.0  # 對9分總計的調整
            
            result = {
                'estimated_temp': base_temp,
                'temp_factor': temp_factor,
                'wind_factor': wind_factor,
                'overall_factor': overall_weather_factor,
                'total_adjustment': total_adjustment,
                'description': weather_desc
            }
            
            self.weather_cache[date] = result
            return result
            
        except Exception as e:
            print(f"❌ 天氣影響計算錯誤: {e}")
            return {
                'estimated_temp': 70,
                'temp_factor': 1.0,
                'wind_factor': 1.0,
                'overall_factor': 1.0,
                'total_adjustment': 0.0,
                'description': '標準天氣'
            }
    
    def predict_enhanced_total(self, home_team: str, away_team: str, 
                             predicted_home: float, predicted_away: float, 
                             date: str) -> Dict:
        """生成增強的大小分預測"""
        print(f"📊 增強大小分預測: {away_team} @ {home_team} ({date})")
        
        try:
            # 1. 基礎總分
            base_total = predicted_home + predicted_away
            
            # 2. 球隊攻擊力分析
            home_offense = self.calculate_team_offensive_power(home_team, date)
            away_offense = self.calculate_team_offensive_power(away_team, date)
            
            # 3. 球隊防守影響分析
            home_defense = self.calculate_team_defensive_impact(home_team, date)
            away_defense = self.calculate_team_defensive_impact(away_team, date)
            
            # 4. 投手影響分析
            pitcher_impact = self.calculate_pitcher_total_impact(home_team, away_team, date)
            
            # 5. 球場因子
            ballpark_data = self.get_enhanced_ballpark_factor(home_team)
            
            # 6. 天氣影響
            weather_data = self.calculate_weather_impact(date)
            
            # 7. 綜合計算增強總分
            # 攻擊力調整
            offense_adjustment = (
                (home_offense['offensive_strength'] - 1.0) * self.total_factors['team_offense_weight'] * 4.5 +
                (away_offense['offensive_strength'] - 1.0) * self.total_factors['team_offense_weight'] * 4.5
            )
            
            # 防守調整
            defense_adjustment = (
                (2.0 - home_defense['defensive_strength']) * self.total_factors['team_defense_weight'] * 4.5 +
                (2.0 - away_defense['defensive_strength']) * self.total_factors['team_defense_weight'] * 4.5
            )
            
            # 投手調整
            pitcher_adjustment = pitcher_impact['total_adjustment'] * self.total_factors['pitcher_quality_weight']
            
            # 球場調整
            ballpark_adjustment = (ballpark_data['enhanced_factor'] - 1.0) * self.total_factors['ballpark_factor_weight'] * 9.0
            
            # 天氣調整
            weather_adjustment = weather_data['total_adjustment'] * self.total_factors['weather_factor_weight']
            
            # 總調整量
            total_adjustment = offense_adjustment + defense_adjustment + pitcher_adjustment + ballpark_adjustment + weather_adjustment
            
            # 限制調整幅度
            capped_adjustment = max(-self.total_factors['max_total_adjustment'], 
                                   min(self.total_factors['max_total_adjustment'], total_adjustment))
            
            # 最終預測總分
            enhanced_total = base_total + capped_adjustment
            
            # 限制總分範圍 (6.0 to 15.0)
            final_total = max(6.0, min(15.0, enhanced_total))
            
            # Over/Under分析
            standard_line = self.total_factors['base_total']
            over_probability = self._calculate_over_probability(final_total, standard_line)
            
            # 信心度計算
            confidence = self._calculate_total_confidence(final_total, standard_line, 
                                                        home_offense, away_offense, 
                                                        pitcher_impact, ballpark_data)
            
            # 投注建議
            if over_probability > 0.65 and confidence > 0.70:
                recommendation = 'OVER - 強力推薦'
            elif over_probability < 0.35 and confidence > 0.70:
                recommendation = 'UNDER - 強力推薦'
            elif over_probability > 0.58:
                recommendation = 'OVER - 建議'
            elif over_probability < 0.42:
                recommendation = 'UNDER - 建議'
            else:
                recommendation = 'SKIP - 無明確優勢'
            
            enhanced_prediction = {
                'base_total': round(base_total, 1),
                'enhanced_total': round(final_total, 1),
                'standard_line': standard_line,
                'over_probability': round(over_probability, 3),
                'under_probability': round(1 - over_probability, 3),
                'confidence': round(confidence, 3),
                'recommendation': recommendation,
                'total_adjustment': round(capped_adjustment, 2),
                'adjustments_breakdown': {
                    'offense': round(offense_adjustment, 2),
                    'defense': round(defense_adjustment, 2),
                    'pitcher': round(pitcher_adjustment, 2),
                    'ballpark': round(ballpark_adjustment, 2),
                    'weather': round(weather_adjustment, 2)
                },
                'analysis_details': {
                    'home_offense_strength': home_offense['offensive_strength'],
                    'away_offense_strength': away_offense['offensive_strength'],
                    'ballpark_factor': ballpark_data['enhanced_factor'],
                    'weather_desc': weather_data['description'],
                    'pitcher_confidence': pitcher_impact.get('confidence_adjustment', 0)
                }
            }
            
            print(f"  💡 基礎總分: {base_total}")
            print(f"  📈 增強總分: {final_total}")
            print(f"  🔧 調整幅度: {capped_adjustment:+.2f}")
            print(f"  🎯 Over機率: {over_probability:.1%}")
            print(f"  💪 信心度: {confidence:.1%}")
            
            return enhanced_prediction
            
        except Exception as e:
            print(f"❌ 增強總分預測錯誤: {e}")
            return self._get_default_total_prediction()
    
    def _calculate_over_probability(self, predicted_total: float, total_line: float) -> float:
        """計算Over機率 - 使用改進的sigmoid函數"""
        difference = predicted_total - total_line
        
        # 使用更敏感的sigmoid函數，增加預測準確性
        probability = 1 / (1 + np.exp(-difference * 0.8))
        
        return max(0.05, min(0.95, probability))
    
    def _calculate_total_confidence(self, predicted_total: float, total_line: float,
                                   home_offense: Dict, away_offense: Dict,
                                   pitcher_impact: Dict, ballpark_data: Dict) -> float:
        """計算大小分預測信心度"""
        # 基礎信心度
        difference = abs(predicted_total - total_line)
        base_confidence = min(0.85, 0.50 + (difference / 4.0))
        
        # 數據質量調整
        data_quality = (
            min(1.0, home_offense['games_played'] / 10.0) * 0.3 +
            min(1.0, away_offense['games_played'] / 10.0) * 0.3 +
            (1.0 if pitcher_impact.get('home_pitcher') else 0.5) * 0.4
        )
        
        # 極端值調整
        if difference > 2.5:  # 預測與標準線差異很大
            base_confidence += 0.10
        elif difference < 0.5:  # 預測與標準線很接近
            base_confidence -= 0.15
        
        # 球場因子確定性
        ballpark_certainty = min(0.05, abs(ballpark_data['enhanced_factor'] - 1.0))
        
        final_confidence = base_confidence * data_quality + ballpark_certainty
        
        return max(0.30, min(0.95, final_confidence))
    
    def _get_default_offensive_metrics(self) -> Dict:
        return {
            'games_played': 0,
            'avg_runs_scored': 4.5,
            'avg_total_involved': 9.0,
            'high_scoring_rate': 0.3,
            'offensive_strength': 1.0
        }
    
    def _get_default_defensive_metrics(self) -> Dict:
        return {
            'avg_runs_allowed': 4.5,
            'avg_total_involved': 9.0,
            'low_scoring_defense_rate': 0.3,
            'defensive_strength': 1.0,
            'total_suppression_factor': 1.0
        }
    
    def _get_default_pitcher_metrics(self) -> Dict:
        return {
            'home_pitcher': '未確認',
            'away_pitcher': '未確認',
            'historical_avg_total': 9.0,
            'pitcher_total_factor': 1.0,
            'confidence_adjustment': 0.0,
            'total_adjustment': 0.0
        }
    
    def _get_default_total_prediction(self) -> Dict:
        return {
            'base_total': 9.0,
            'enhanced_total': 9.0,
            'standard_line': 9.0,
            'over_probability': 0.5,
            'under_probability': 0.5,
            'confidence': 0.3,
            'recommendation': 'SKIP - 數據不足',
            'total_adjustment': 0.0,
            'adjustments_breakdown': {},
            'analysis_details': {}
        }

def main():
    """測試增強大小分預測器"""
    print("🚀 MLB增強大小分預測器")
    print("=" * 60)
    
    predictor = EnhancedTotalPredictor()
    
    # 測試預測
    test_prediction = predictor.predict_enhanced_total(
        'NYY', 'BOS', 5.2, 4.8, '2025-08-15'
    )
    
    print(f"\n📊 增強大小分預測結果:")
    print(json.dumps(test_prediction, indent=2, ensure_ascii=False))
    
    print(f"\n✅ 增強大小分預測器已就緒!")
    print(f"🎯 目標: 64% → 70%+ Over/Under準確率")
    print(f"🔬 核心改進: 多因子綜合分析 + 動態權重調整")

if __name__ == "__main__":
    main()