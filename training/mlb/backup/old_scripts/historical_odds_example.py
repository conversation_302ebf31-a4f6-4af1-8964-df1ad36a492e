#!/usr/bin/env python3
"""
The Odds API 歷史賠率數據示例
展示如何獲取 MLB 歷史賠率數據
"""

import requests
import json
from datetime import datetime, timedelta

class HistoricalOddsAPI:
    """歷史賠率 API 客戶端"""
    
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.the-odds-api.com/v4/historical"
        
    def get_mlb_historical_odds(self, date_str, markets=None, regions="us", odds_format="american"):
        """
        獲取 MLB 歷史賠率數據
        
        Args:
            date_str: 日期字符串 (ISO 8601 格式, 如 "2024-06-30T12:00:00Z")
            markets: 市場類型列表 (如 ["h2h", "spreads", "totals"])
            regions: 地區 (如 "us")
            odds_format: 賠率格式 (如 "american", "decimal")
        """
        if markets is None:
            markets = ["h2h", "spreads", "totals"]
        
        url = f"{self.base_url}/sports/baseball_mlb/odds"
        
        params = {
            "apiKey": self.api_key,
            "date": date_str,
            "regions": regions,
            "markets": ",".join(markets),
            "oddsFormat": odds_format
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API 錯誤: {response.status_code}")
                print(f"響應: {response.text}")
                return None
                
        except Exception as e:
            print(f"請求失敗: {e}")
            return None
    
    def get_mlb_event_historical_odds(self, event_id, date_str, markets=None, regions="us"):
        """
        獲取特定 MLB 比賽的歷史賠率數據 (包含球員道具投注)
        
        Args:
            event_id: 比賽 ID
            date_str: 日期字符串
            markets: 市場類型 (如 ["player_points", "h2h", "spreads"])
            regions: 地區
        """
        if markets is None:
            markets = ["h2h", "spreads", "totals", "player_points"]
        
        url = f"{self.base_url}/sports/baseball_mlb/events/{event_id}/odds"
        
        params = {
            "apiKey": self.api_key,
            "date": date_str,
            "regions": regions,
            "markets": ",".join(markets)
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API 錯誤: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"請求失敗: {e}")
            return None

def demo_historical_odds():
    """演示歷史賠率數據獲取"""
    
    # 讀取 API 密鑰
    try:
        with open('models/odds-api.txt', 'r') as f:
            api_key = f.read().strip()
    except FileNotFoundError:
        print("❌ 找不到 API 密鑰文件: models/odds-api.txt")
        return
    
    # 初始化 API 客戶端
    odds_api = HistoricalOddsAPI(api_key)
    
    print("🔍 The Odds API 歷史賠率數據示例")
    print("=" * 50)
    
    # 示例1: 獲取 2024年6月30日的 MLB 賠率
    print("\n📅 示例1: 2024年6月30日 MLB 賠率")
    date_str = "2024-06-30T12:00:00Z"
    
    historical_data = odds_api.get_mlb_historical_odds(
        date_str=date_str,
        markets=["h2h", "spreads", "totals"]
    )
    
    if historical_data:
        timestamp = historical_data.get('timestamp')
        data = historical_data.get('data', [])
        
        print(f"✅ 快照時間: {timestamp}")
        print(f"📊 比賽數量: {len(data)}")
        
        # 顯示前3場比賽的賠率
        for i, game in enumerate(data[:3]):
            home_team = game.get('home_team')
            away_team = game.get('away_team')
            commence_time = game.get('commence_time')
            
            print(f"\n🏟️  比賽 {i+1}: {away_team} @ {home_team}")
            print(f"   開始時間: {commence_time}")
            
            # 顯示 DraftKings 的賠率
            for bookmaker in game.get('bookmakers', []):
                if bookmaker.get('key') == 'draftkings':
                    print(f"   📈 DraftKings 賠率:")
                    
                    for market in bookmaker.get('markets', []):
                        market_key = market.get('key')
                        outcomes = market.get('outcomes', [])
                        
                        if market_key == 'h2h':
                            print(f"     勝負盤:")
                            for outcome in outcomes:
                                name = outcome.get('name')
                                price = outcome.get('price')
                                print(f"       {name}: {price}")
                        
                        elif market_key == 'spreads':
                            print(f"     讓分盤:")
                            for outcome in outcomes:
                                name = outcome.get('name')
                                price = outcome.get('price')
                                point = outcome.get('point')
                                print(f"       {name} {point:+}: {price}")
                        
                        elif market_key == 'totals':
                            print(f"     大小分:")
                            for outcome in outcomes:
                                name = outcome.get('name')
                                price = outcome.get('price')
                                point = outcome.get('point')
                                print(f"       {name} {point}: {price}")
                    break
    else:
        print("❌ 無法獲取歷史賠率數據")
    
    print(f"\n💡 使用說明:")
    print(f"   1. 歷史數據僅限付費訂閱用戶")
    print(f"   2. 每次請求消耗 10 個配額 (每個地區每個市場)")
    print(f"   3. 數據從 2020年6月6日開始提供")
    print(f"   4. 快照頻率: 5分鐘間隔 (2022年9月後)")

def analyze_historical_value():
    """分析歷史賠率數據的價值"""
    print(f"\n📈 歷史賠率數據的價值分析")
    print("=" * 50)
    
    print(f"🎯 對 MLB 預測系統的幫助:")
    print(f"   1. 📊 市場預期分析:")
    print(f"      - 比較我們的預測與博彩公司賠率")
    print(f"      - 識別價值投注機會")
    print(f"      - 驗證預測模型準確性")
    
    print(f"\n   2. 🔍 賠率變化追蹤:")
    print(f"      - 監控賠率移動趨勢")
    print(f"      - 分析市場情緒變化")
    print(f"      - 識別內幕信息影響")
    
    print(f"\n   3. 🏆 模型改進:")
    print(f"      - 使用市場賠率作為特徵")
    print(f"      - 校準預測概率")
    print(f"      - 提高預測準確性")
    
    print(f"\n   4. 💰 投注策略優化:")
    print(f"      - 回測投注策略")
    print(f"      - 計算歷史投資回報率")
    print(f"      - 風險管理優化")
    
    print(f"\n⚠️  注意事項:")
    print(f"   - 需要付費訂閱")
    print(f"   - API 配額消耗較高")
    print(f"   - 數據量龐大，需要存儲策略")

if __name__ == "__main__":
    demo_historical_odds()
    analyze_historical_value()
