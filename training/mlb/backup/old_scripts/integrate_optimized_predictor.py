#!/usr/bin/env python3
"""
整合優化預測器到主系統
將基於回測實驗的混合預測策略整合到現有的預測系統中
"""

import sys
import os
from datetime import date, timedelta

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction
from models.optimized_predictor import OptimizedMLBPredictor

def _convert_confidence_to_float(confidence_level):
    """將信心等級轉換為數值"""
    confidence_map = {
        '高': 0.8,
        '中': 0.6,
        '低': 0.4
    }
    return confidence_map.get(confidence_level, 0.5)

def integrate_optimized_predictor():
    """整合優化預測器到主系統"""
    print("🔧 整合優化預測器到主系統")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        predictor = OptimizedMLBPredictor()
        
        # 1. 訓練最新模型
        today = date.today()
        print(f"📅 訓練日期: {today}")
        print(f"   勝負模型: 使用最近14天數據")
        print(f"   得分模型: 使用最近90天數據")
        
        training_result = predictor.train_models(today)
        
        if not training_result['success']:
            print(f"❌ 模型訓練失敗: {training_result.get('error')}")
            return False
        
        print("✅ 模型訓練成功!")
        stats = training_result['training_stats']
        print(f"   勝負模型: {stats['win_training_games']} 場比賽")
        print(f"   得分模型: {stats['score_training_games']} 場比賽")
        
        # 2. 為今天的比賽生成預測
        today_games = Game.query.filter_by(date=today).all()
        
        if not today_games:
            print(f"⚠️  今天 ({today}) 沒有比賽")
            # 嘗試明天的比賽
            tomorrow = today + timedelta(days=1)
            tomorrow_games = Game.query.filter_by(date=tomorrow).all()
            
            if tomorrow_games:
                print(f"🔄 改為預測明天 ({tomorrow}) 的 {len(tomorrow_games)} 場比賽")
                target_games = tomorrow_games
                target_date = tomorrow
            else:
                print(f"❌ 明天也沒有比賽數據")
                return False
        else:
            print(f"🎯 為今天的 {len(today_games)} 場比賽生成預測")
            target_games = today_games
            target_date = today
        
        # 3. 生成預測並保存到數據庫
        successful_predictions = 0
        total_games = len(target_games)
        
        for i, game in enumerate(target_games, 1):
            print(f"\n[{i:2d}/{total_games}] {game.away_team} @ {game.home_team}")
            
            try:
                # 生成預測
                prediction_result = predictor.predict_game(
                    game.home_team, game.away_team, game.date
                )
                
                if prediction_result['success']:
                    pred = prediction_result['predictions']
                    
                    print(f"   預測: {game.away_team} {pred['away_score']} - "
                          f"{pred['home_score']} {game.home_team}")
                    print(f"   勝者: {pred['predicted_winner']} "
                          f"({pred['home_win_probability']:.1%})")
                    print(f"   信心: {pred['confidence_level']}")
                    
                    # 檢查是否已有預測記錄
                    existing_prediction = Prediction.query.filter_by(
                        game_id=game.game_id
                    ).first()
                    
                    if existing_prediction:
                        # 更新現有預測
                        existing_prediction.predicted_home_score = pred['home_score']
                        existing_prediction.predicted_away_score = pred['away_score']
                        existing_prediction.home_win_probability = pred['home_win_probability']
                        existing_prediction.away_win_probability = pred['away_win_probability']
                        existing_prediction.model_version = 'optimized_v1.0'
                        existing_prediction.confidence = _convert_confidence_to_float(pred['confidence_level'])
                        print(f"   ✅ 更新現有預測記錄")
                    else:
                        # 創建新預測記錄
                        new_prediction = Prediction(
                            game_id=game.game_id,
                            predicted_home_score=pred['home_score'],
                            predicted_away_score=pred['away_score'],
                            home_win_probability=pred['home_win_probability'],
                            away_win_probability=pred['away_win_probability'],
                            model_version='optimized_v1.0',
                            confidence=_convert_confidence_to_float(pred['confidence_level']),
                            prediction_date=today
                        )
                        db.session.add(new_prediction)
                        print(f"   ✅ 創建新預測記錄")
                    
                    successful_predictions += 1
                    
                else:
                    print(f"   ❌ 預測失敗: {prediction_result.get('error')}")
                    
            except Exception as e:
                print(f"   ❌ 處理失敗: {e}")
        
        # 4. 提交數據庫更改
        try:
            db.session.commit()
            print(f"\n💾 數據庫更新成功")
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ 數據庫更新失敗: {e}")
            return False
        
        # 5. 總結
        print(f"\n📊 整合總結:")
        print(f"   目標日期: {target_date}")
        print(f"   總比賽數: {total_games}")
        print(f"   成功預測: {successful_predictions}")
        print(f"   成功率: {successful_predictions/total_games*100:.1f}%")
        print(f"   模型版本: optimized_v1.0")
        
        # 6. 驗證整合結果
        optimized_predictions = Prediction.query.filter_by(
            model_version='optimized_v1.0'
        ).count()
        
        print(f"\n🔍 驗證結果:")
        print(f"   數據庫中 optimized_v1.0 預測記錄: {optimized_predictions}")
        
        return True

def test_web_integration():
    """測試Web界面整合"""
    print(f"\n🌐 測試Web界面整合:")
    
    app = create_app()
    
    with app.app_context():
        # 檢查最新預測記錄
        latest_predictions = Prediction.query.filter_by(
            model_version='optimized_v1.0'
        ).limit(5).all()
        
        print(f"   最新 optimized_v1.0 預測記錄: {len(latest_predictions)}")
        
        for pred in latest_predictions:
            game = Game.query.get(pred.game_id)
            if game:
                print(f"   {game.away_team} @ {game.home_team}: "
                      f"{pred.away_score_prediction}-{pred.home_score_prediction} "
                      f"(信心: {pred.confidence_score})")

def create_api_endpoint():
    """創建API端點供Web界面調用"""
    print(f"\n🔌 創建API端點:")
    
    api_code = '''
# 添加到 views/api.py 或相關路由文件

from models.optimized_predictor import OptimizedMLBPredictor

@app.route('/api/optimized_prediction/<game_id>')
def get_optimized_prediction(game_id):
    """獲取優化預測結果"""
    try:
        game = Game.query.get_or_404(game_id)
        
        predictor = OptimizedMLBPredictor()
        predictor.train_models()  # 確保模型已訓練
        
        prediction = predictor.predict_game(
            game.home_team, game.away_team, game.date
        )
        
        if prediction['success']:
            return jsonify({
                'success': True,
                'prediction': prediction['predictions'],
                'model_info': prediction['model_info']
            })
        else:
            return jsonify({
                'success': False,
                'error': prediction['error']
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/batch_optimized_predictions')
def get_batch_optimized_predictions():
    """批量獲取優化預測"""
    try:
        date_str = request.args.get('date', date.today().isoformat())
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        games = Game.query.filter_by(date=target_date).all()
        
        predictor = OptimizedMLBPredictor()
        predictor.train_models(target_date)
        
        predictions = []
        for game in games:
            pred = predictor.predict_game(
                game.home_team, game.away_team, game.date
            )
            if pred['success']:
                predictions.append({
                    'game_id': game.game_id,
                    'teams': f"{game.away_team} @ {game.home_team}",
                    'prediction': pred['predictions']
                })
        
        return jsonify({
            'success': True,
            'date': date_str,
            'predictions': predictions,
            'total': len(predictions)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
'''
    
    print("   API端點代碼已準備，可添加到路由文件中")
    print("   主要端點:")
    print("   - /api/optimized_prediction/<game_id>")
    print("   - /api/batch_optimized_predictions")

if __name__ == "__main__":
    success = integrate_optimized_predictor()
    
    if success:
        test_web_integration()
        create_api_endpoint()
        
        print(f"\n🎉 優化預測器整合完成!")
        print(f"   ✅ 模型已訓練並部署")
        print(f"   ✅ 預測記錄已保存到數據庫")
        print(f"   ✅ Web界面可以使用 optimized_v1.0 模型")
        print(f"   ✅ API端點代碼已準備")
    else:
        print(f"\n❌ 整合失敗，請檢查錯誤信息")
