2025-07-12 08:58:06,130 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:06,139 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:06,148 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:06,148 - models.prediction_service - INFO - 預測服務初始化成功 - 模型已載入
2025-07-12 08:58:06,157 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:06,165 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:06,165 - models.prediction_service - INFO - 預測服務初始化成功 - 模型已載入
2025-07-12 08:58:06,165 - models.automated_predictor - INFO - 定時任務設置完成
2025-07-12 08:58:06,166 - models.automated_predictor - INFO - 自動化預測調度器已啟動
 * Serving Flask app 'app'
 * Debug mode: on
2025-07-12 08:58:06,361 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5500
 * Running on http://*********:5500
2025-07-12 08:58:06,361 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 08:58:06,362 - werkzeug - INFO -  * Restarting with stat
2025-07-12 08:58:07,472 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:07,480 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:07,489 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:07,489 - models.prediction_service - INFO - 預測服務初始化成功 - 模型已載入
2025-07-12 08:58:07,498 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:07,507 - models.ml_predictor - INFO - 模型載入完成: models/saved
2025-07-12 08:58:07,507 - models.prediction_service - INFO - 預測服務初始化成功 - 模型已載入
2025-07-12 08:58:07,507 - models.automated_predictor - INFO - 定時任務設置完成
2025-07-12 08:58:07,507 - models.automated_predictor - INFO - 自動化預測調度器已啟動
2025-07-12 08:58:07,519 - werkzeug - WARNING -  * Debugger is active!
2025-07-12 08:58:07,526 - werkzeug - INFO -  * Debugger PIN: 110-879-090
Fatal Python error: init_sys_streams: can't initialize sys standard streams
Python runtime state: core initialized
/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_tracker.py:301: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown: {'/loky-78987-afxu3jg3'}
  warnings.warn(
OSError: [Errno 9] Bad file descriptor

Current thread 0x00000002096a1f00 (most recent call first):
  <no Python frame>
