#!/usr/bin/env python3
"""
歷史回測引擎
使用比賽前的數據進行預測，然後與實際結果對比
避免前瞻性偏差，確保回測結果的可靠性
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
import json
import os
import logging
from typing import Dict, List, Tuple, Optional
import sys

# 添加路徑以載入我們的模型
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HistoricalBacktester:
    """歷史回測引擎"""
    
    def __init__(self, db_path='instance/mlb_data.db'):
        self.db_path = db_path
        self.results = []
        self.betting_results = []
        self.performance_metrics = {}
        
    def get_historical_games(self, start_date: str, end_date: str) -> pd.DataFrame:
        """獲取指定日期範圍的歷史比賽"""
        print(f"📅 獲取 {start_date} 到 {end_date} 的歷史比賽...")
        
        conn = sqlite3.connect(self.db_path)
        
        query = """
        SELECT game_id, date, home_team, away_team, home_score, away_score,
               game_status
        FROM games 
        WHERE date BETWEEN ? AND ?
        AND game_status = 'completed'
        AND home_score IS NOT NULL 
        AND away_score IS NOT NULL
        ORDER BY date ASC
        """
        
        df = pd.read_sql_query(query, conn, params=(start_date, end_date))
        conn.close()
        
        print(f"✅ 找到 {len(df)} 場已完成的比賽")
        return df
    
    def prepare_features_for_date(self, game_date: str, home_team: str, away_team: str) -> Dict:
        """為指定日期的比賽準備特徵，只使用該日期之前的數據"""
        
        # 計算截止日期（比賽日期的前一天）
        cutoff_date = (datetime.strptime(game_date, '%Y-%m-%d') - timedelta(days=1)).strftime('%Y-%m-%d')
        
        conn = sqlite3.connect(self.db_path)
        
        # 只使用截止日期之前的數據
        features = {}
        
        # 1. 主隊最近30天表現
        home_recent_query = """
        SELECT 
            COUNT(CASE WHEN (home_team = ? AND home_score > away_score) OR 
                           (away_team = ? AND away_score > home_score) THEN 1 END) as wins,
            COUNT(*) as games,
            AVG(CASE WHEN home_team = ? THEN home_score 
                     WHEN away_team = ? THEN away_score END) as avg_score,
            AVG(CASE WHEN home_team = ? THEN away_score 
                     WHEN away_team = ? THEN home_score END) as avg_allowed
        FROM games 
        WHERE (home_team = ? OR away_team = ?)
        AND date BETWEEN date(?, '-30 days') AND ?
        AND game_status = 'completed'
        AND home_score IS NOT NULL
        """
        
        cursor = conn.cursor()
        cursor.execute(home_recent_query, (home_team, home_team, home_team, home_team, 
                                          home_team, home_team, home_team, home_team, 
                                          cutoff_date, cutoff_date))
        home_stats = cursor.fetchone()
        
        features['home_recent_wins'] = home_stats[0] if home_stats[0] else 0
        features['home_recent_games'] = home_stats[1] if home_stats[1] else 0
        features['home_recent_win_rate'] = (home_stats[0] / home_stats[1]) if home_stats[1] > 0 else 0
        features['home_recent_avg_score'] = home_stats[2] if home_stats[2] else 0
        features['home_recent_avg_allowed'] = home_stats[3] if home_stats[3] else 0
        
        # 2. 客隊最近30天表現
        cursor.execute(home_recent_query, (away_team, away_team, away_team, away_team,
                                          away_team, away_team, away_team, away_team,
                                          cutoff_date, cutoff_date))
        away_stats = cursor.fetchone()
        
        features['away_recent_wins'] = away_stats[0] if away_stats[0] else 0
        features['away_recent_games'] = away_stats[1] if away_stats[1] else 0
        features['away_recent_win_rate'] = (away_stats[0] / away_stats[1]) if away_stats[1] > 0 else 0
        features['away_recent_avg_score'] = away_stats[2] if away_stats[2] else 0
        features['away_recent_avg_allowed'] = away_stats[3] if away_stats[3] else 0
        
        # 3. 對戰歷史記錄
        h2h_query = """
        SELECT 
            SUM(CASE WHEN home_score > away_score THEN 1 ELSE 0 END) as home_wins,
            SUM(CASE WHEN away_score > home_score THEN 1 ELSE 0 END) as away_wins,
            AVG(home_score) as avg_home_score,
            AVG(away_score) as avg_away_score,
            COUNT(*) as total_games
        FROM games 
        WHERE home_team = ? AND away_team = ?
        AND date <= ?
        AND game_status = 'completed'
        AND home_score IS NOT NULL
        """
        
        cursor.execute(h2h_query, (home_team, away_team, cutoff_date))
        h2h_stats = cursor.fetchone()
        
        features['h2h_home_wins'] = h2h_stats[0] if h2h_stats[0] else 0
        features['h2h_away_wins'] = h2h_stats[1] if h2h_stats[1] else 0
        features['h2h_avg_home_score'] = h2h_stats[2] if h2h_stats[2] else 0
        features['h2h_avg_away_score'] = h2h_stats[3] if h2h_stats[3] else 0
        features['h2h_total_games'] = h2h_stats[4] if h2h_stats[4] else 0
        features['h2h_home_win_rate'] = (h2h_stats[0] / h2h_stats[4]) if h2h_stats[4] > 0 else 0.5
        
        # 4. 賽季總體表現
        season_query = """
        SELECT 
            COUNT(CASE WHEN (home_team = ? AND home_score > away_score) OR 
                           (away_team = ? AND away_score > home_score) THEN 1 END) as wins,
            COUNT(*) as games,
            AVG(CASE WHEN home_team = ? THEN home_score 
                     WHEN away_team = ? THEN away_score END) as avg_score,
            AVG(CASE WHEN home_team = ? THEN away_score 
                     WHEN away_team = ? THEN home_score END) as avg_allowed
        FROM games 
        WHERE (home_team = ? OR away_team = ?)
        AND date BETWEEN date(?, 'start of year') AND ?
        AND game_status = 'completed'
        AND home_score IS NOT NULL
        """
        
        cursor.execute(season_query, (home_team, home_team, home_team, home_team,
                                     home_team, home_team, home_team, home_team,
                                     cutoff_date, cutoff_date))
        home_season = cursor.fetchone()
        
        features['home_season_wins'] = home_season[0] if home_season[0] else 0
        features['home_season_games'] = home_season[1] if home_season[1] else 0
        features['home_season_win_rate'] = (home_season[0] / home_season[1]) if home_season[1] > 0 else 0
        features['home_season_avg_score'] = home_season[2] if home_season[2] else 0
        features['home_season_avg_allowed'] = home_season[3] if home_season[3] else 0
        
        cursor.execute(season_query, (away_team, away_team, away_team, away_team,
                                     away_team, away_team, away_team, away_team,
                                     cutoff_date, cutoff_date))
        away_season = cursor.fetchone()
        
        features['away_season_wins'] = away_season[0] if away_season[0] else 0
        features['away_season_games'] = away_season[1] if away_season[1] else 0
        features['away_season_win_rate'] = (away_season[0] / away_season[1]) if away_season[1] > 0 else 0
        features['away_season_avg_score'] = away_season[2] if away_season[2] else 0
        features['away_season_avg_allowed'] = away_season[3] if away_season[3] else 0
        
        # 5. 計算衍生特徵
        features['recent_performance_diff'] = features['home_recent_win_rate'] - features['away_recent_win_rate']
        features['season_performance_diff'] = features['home_season_win_rate'] - features['away_season_win_rate']
        features['home_scoring_advantage'] = features['home_recent_avg_score'] - features['away_recent_avg_allowed']
        features['away_scoring_advantage'] = features['away_recent_avg_score'] - features['home_recent_avg_allowed']
        
        # 6. 日期特徵
        game_dt = datetime.strptime(game_date, '%Y-%m-%d')
        features['month'] = game_dt.month
        features['day_of_week'] = game_dt.weekday()
        features['is_weekend'] = 1 if game_dt.weekday() >= 5 else 0
        
        conn.close()
        return features
    
    def predict_with_simple_model(self, features: Dict) -> Dict:
        """使用簡單規則模型進行預測"""
        
        # 基於歷史數據的簡單預測邏輯
        home_advantage = 0.3  # 主場優勢
        
        # 計算預測分數
        base_score = 4.5
        
        home_score_factors = [
            features['home_recent_avg_score'] * 0.4,
            features['home_season_avg_score'] * 0.3,
            features['h2h_avg_home_score'] * 0.2 if features['h2h_total_games'] > 0 else base_score * 0.2,
            home_advantage
        ]
        
        away_score_factors = [
            features['away_recent_avg_score'] * 0.4,
            features['away_season_avg_score'] * 0.3,
            features['h2h_avg_away_score'] * 0.2 if features['h2h_total_games'] > 0 else base_score * 0.2,
            -0.1  # 客場劣勢
        ]
        
        predicted_home_score = max(0, sum(home_score_factors))
        predicted_away_score = max(0, sum(away_score_factors))
        
        # 計算勝率
        score_diff = predicted_home_score - predicted_away_score
        home_win_prob = 1 / (1 + np.exp(-score_diff))  # Sigmoid function
        
        # 計算信心度
        performance_diff = abs(features['recent_performance_diff'])
        data_quality = min(features['home_recent_games'], features['away_recent_games']) / 30
        h2h_factor = min(features['h2h_total_games'] / 10, 1) if features['h2h_total_games'] > 0 else 0
        
        confidence = (performance_diff * 0.4 + data_quality * 0.4 + h2h_factor * 0.2)
        confidence = max(0.1, min(0.9, confidence))
        
        return {
            'predicted_home_score': round(predicted_home_score, 1),
            'predicted_away_score': round(predicted_away_score, 1),
            'home_win_probability': round(home_win_prob, 3),
            'away_win_probability': round(1 - home_win_prob, 3),
            'confidence': round(confidence, 3),
            'total_score': round(predicted_home_score + predicted_away_score, 1),
            'spread': round(predicted_home_score - predicted_away_score, 1)
        }
    
    def calculate_betting_metrics(self, prediction: Dict, actual_home: int, actual_away: int) -> Dict:
        """計算投注相關指標"""
        
        # 實際結果
        actual_total = actual_home + actual_away
        actual_spread = actual_home - actual_away
        home_won = actual_home > actual_away
        
        # 預測結果
        pred_total = prediction['total_score']
        pred_spread = prediction['spread']
        pred_home_win = prediction['home_win_probability'] > 0.5
        
        # 準確性評估
        score_accuracy = {
            'home_score_error': abs(prediction['predicted_home_score'] - actual_home),
            'away_score_error': abs(prediction['predicted_away_score'] - actual_away),
            'total_score_error': abs(pred_total - actual_total),
            'spread_error': abs(pred_spread - actual_spread),
            'win_prediction_correct': pred_home_win == home_won
        }
        
        # 博彩結果模擬
        confidence = prediction['confidence']
        
        # 假設的盤口（簡化）
        total_line = pred_total
        spread_line = pred_spread
        
        # 投注決策邏輯
        betting_decisions = {}
        
        # 大小分投注
        if confidence > 0.6:
            if actual_total > total_line:
                betting_decisions['total_bet'] = 'over'
                betting_decisions['total_result'] = 'win'
                betting_decisions['total_payout'] = 1.91  # -110 odds
            else:
                betting_decisions['total_bet'] = 'under'
                betting_decisions['total_result'] = 'lose'
                betting_decisions['total_payout'] = 0
        else:
            betting_decisions['total_bet'] = 'no_bet'
            betting_decisions['total_result'] = 'no_bet'
            betting_decisions['total_payout'] = 0
        
        # 讓分投注
        if confidence > 0.65:
            if actual_spread > spread_line:
                betting_decisions['spread_bet'] = 'home'
                betting_decisions['spread_result'] = 'win' if actual_spread > spread_line else 'lose'
            else:
                betting_decisions['spread_bet'] = 'away'
                betting_decisions['spread_result'] = 'win' if actual_spread < spread_line else 'lose'
            
            betting_decisions['spread_payout'] = 1.91 if betting_decisions['spread_result'] == 'win' else 0
        else:
            betting_decisions['spread_bet'] = 'no_bet'
            betting_decisions['spread_result'] = 'no_bet'
            betting_decisions['spread_payout'] = 0
        
        # 勝負盤投注
        if confidence > 0.7:
            expected_win_prob = prediction['home_win_probability']
            if expected_win_prob > 0.6:
                betting_decisions['moneyline_bet'] = 'home'
                betting_decisions['moneyline_result'] = 'win' if home_won else 'lose'
                betting_decisions['moneyline_payout'] = 1.8 if home_won else 0
            elif expected_win_prob < 0.4:
                betting_decisions['moneyline_bet'] = 'away'
                betting_decisions['moneyline_result'] = 'win' if not home_won else 'lose'
                betting_decisions['moneyline_payout'] = 2.2 if not home_won else 0
            else:
                betting_decisions['moneyline_bet'] = 'no_bet'
                betting_decisions['moneyline_result'] = 'no_bet'
                betting_decisions['moneyline_payout'] = 0
        else:
            betting_decisions['moneyline_bet'] = 'no_bet'
            betting_decisions['moneyline_result'] = 'no_bet'
            betting_decisions['moneyline_payout'] = 0
        
        return {**score_accuracy, **betting_decisions}
    
    def run_backtest(self, start_date: str, end_date: str, sample_size: int = None) -> Dict:
        """執行回測"""
        print(f"🚀 開始回測 {start_date} 到 {end_date}")
        print("=" * 60)
        
        # 獲取歷史比賽
        games_df = self.get_historical_games(start_date, end_date)
        
        if len(games_df) == 0:
            print("❌ 沒有找到符合條件的比賽")
            return {}
        
        # 如果指定了樣本大小，隨機抽樣
        if sample_size and sample_size < len(games_df):
            games_df = games_df.sample(n=sample_size, random_state=42)
            print(f"📊 隨機抽樣 {sample_size} 場比賽進行回測")
        
        results = []
        betting_results = []
        
        print(f"🔍 開始逐場預測...")
        
        for idx, row in games_df.iterrows():
            try:
                # 準備特徵（只使用比賽前的數據）
                features = self.prepare_features_for_date(
                    row['date'], row['home_team'], row['away_team']
                )
                
                # 進行預測
                prediction = self.predict_with_simple_model(features)
                
                # 計算指標
                metrics = self.calculate_betting_metrics(
                    prediction, row['home_score'], row['away_score']
                )
                
                # 記錄結果
                result = {
                    'game_id': row['game_id'],
                    'date': row['date'],
                    'home_team': row['home_team'],
                    'away_team': row['away_team'],
                    'actual_home_score': row['home_score'],
                    'actual_away_score': row['away_score'],
                    **prediction,
                    **metrics
                }
                
                results.append(result)
                
                if (idx + 1) % 50 == 0:
                    print(f"   已處理 {idx + 1}/{len(games_df)} 場比賽")
                    
            except Exception as e:
                logger.warning(f"預測比賽 {row['game_id']} 失敗: {e}")
                continue
        
        print(f"✅ 回測完成，成功預測 {len(results)} 場比賽")
        
        # 計算整體性能
        if len(results) > 0:
            performance = self.calculate_performance_metrics(results)
            
            # 保存結果
            self.save_backtest_results(results, performance, start_date, end_date)
            
            return performance
        else:
            print("❌ 沒有成功的預測結果")
            return {}
    
    def calculate_performance_metrics(self, results: List[Dict]) -> Dict:
        """計算性能指標"""
        print(f"\n📊 計算性能指標...")
        
        df = pd.DataFrame(results)
        
        # 基本準確性指標
        accuracy_metrics = {
            'total_games': len(df),
            'win_prediction_accuracy': df['win_prediction_correct'].mean(),
            'avg_home_score_error': df['home_score_error'].mean(),
            'avg_away_score_error': df['away_score_error'].mean(),
            'avg_total_score_error': df['total_score_error'].mean(),
            'avg_spread_error': df['spread_error'].mean(),
            'avg_confidence': df['confidence'].mean()
        }
        
        # 投注性能指標
        betting_metrics = {}
        
        # 大小分投注
        total_bets = df[df['total_bet'] != 'no_bet']
        if len(total_bets) > 0:
            total_wins = (total_bets['total_result'] == 'win').sum()
            betting_metrics['total_bets_count'] = len(total_bets)
            betting_metrics['total_bet_accuracy'] = total_wins / len(total_bets)
            betting_metrics['total_bet_roi'] = (total_bets['total_payout'].sum() - len(total_bets)) / len(total_bets)
        
        # 讓分投注
        spread_bets = df[df['spread_bet'] != 'no_bet']
        if len(spread_bets) > 0:
            spread_wins = (spread_bets['spread_result'] == 'win').sum()
            betting_metrics['spread_bets_count'] = len(spread_bets)
            betting_metrics['spread_bet_accuracy'] = spread_wins / len(spread_bets)
            betting_metrics['spread_bet_roi'] = (spread_bets['spread_payout'].sum() - len(spread_bets)) / len(spread_bets)
        
        # 勝負盤投注
        ml_bets = df[df['moneyline_bet'] != 'no_bet']
        if len(ml_bets) > 0:
            ml_wins = (ml_bets['moneyline_result'] == 'win').sum()
            betting_metrics['moneyline_bets_count'] = len(ml_bets)
            betting_metrics['moneyline_bet_accuracy'] = ml_wins / len(ml_bets)
            betting_metrics['moneyline_bet_roi'] = (ml_bets['moneyline_payout'].sum() - len(ml_bets)) / len(ml_bets)
        
        # 信心度分層分析
        confidence_analysis = {}
        for conf_threshold in [0.6, 0.7, 0.8]:
            high_conf = df[df['confidence'] >= conf_threshold]
            if len(high_conf) > 0:
                confidence_analysis[f'high_confidence_{conf_threshold}'] = {
                    'count': len(high_conf),
                    'win_accuracy': high_conf['win_prediction_correct'].mean(),
                    'avg_score_error': high_conf[['home_score_error', 'away_score_error']].mean().mean()
                }
        
        performance = {
            **accuracy_metrics,
            **betting_metrics,
            'confidence_analysis': confidence_analysis
        }
        
        # 顯示結果
        print(f"📈 性能總結:")
        print(f"   總比賽數: {accuracy_metrics['total_games']}")
        print(f"   勝負預測準確率: {accuracy_metrics['win_prediction_accuracy']:.3f}")
        print(f"   平均分數誤差: {accuracy_metrics['avg_total_score_error']:.2f}")
        print(f"   平均信心度: {accuracy_metrics['avg_confidence']:.3f}")
        
        if 'total_bet_accuracy' in betting_metrics:
            print(f"   大小分投注準確率: {betting_metrics['total_bet_accuracy']:.3f} ({betting_metrics['total_bets_count']}注)")
            print(f"   大小分ROI: {betting_metrics['total_bet_roi']:.3f}")
        
        if 'spread_bet_accuracy' in betting_metrics:
            print(f"   讓分投注準確率: {betting_metrics['spread_bet_accuracy']:.3f} ({betting_metrics['spread_bets_count']}注)")
            print(f"   讓分ROI: {betting_metrics['spread_bet_roi']:.3f}")
        
        return performance
    
    def save_backtest_results(self, results: List[Dict], performance: Dict, start_date: str, end_date: str) -> None:
        """保存回測結果"""
        
        # 創建結果目錄
        results_dir = "backtest_results"
        os.makedirs(results_dir, exist_ok=True)
        
        # 保存詳細結果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"{results_dir}/backtest_{start_date}_{end_date}_{timestamp}.json"
        
        output_data = {
            'metadata': {
                'start_date': start_date,
                'end_date': end_date,
                'created_at': datetime.now().isoformat(),
                'total_games': len(results)
            },
            'performance': performance,
            'detailed_results': results
        }
        
        with open(results_file, 'w') as f:
            json.dump(output_data, f, indent=2, default=str)
        
        print(f"💾 回測結果已保存到 {results_file}")
        
        # 創建簡化報告
        report_file = f"{results_dir}/backtest_report_{start_date}_{end_date}_{timestamp}.md"
        
        report_content = f"""# 回測報告

## 基本信息
- **測試期間**: {start_date} 到 {end_date}
- **總比賽數**: {performance.get('total_games', 0)}
- **生成時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 預測準確性
- **勝負預測準確率**: {performance.get('win_prediction_accuracy', 0):.3f}
- **平均分數誤差**: {performance.get('avg_total_score_error', 0):.2f}
- **平均信心度**: {performance.get('avg_confidence', 0):.3f}

## 投注表現
"""
        
        if 'total_bet_accuracy' in performance:
            report_content += f"""
### 大小分投注
- **投注數量**: {performance.get('total_bets_count', 0)}
- **準確率**: {performance.get('total_bet_accuracy', 0):.3f}
- **ROI**: {performance.get('total_bet_roi', 0):.3f}
"""
        
        if 'spread_bet_accuracy' in performance:
            report_content += f"""
### 讓分投注
- **投注數量**: {performance.get('spread_bets_count', 0)}
- **準確率**: {performance.get('spread_bet_accuracy', 0):.3f}
- **ROI**: {performance.get('spread_bet_roi', 0):.3f}
"""
        
        with open(report_file, 'w') as f:
            f.write(report_content)
        
        print(f"📄 回測報告已保存到 {report_file}")
    
    def compare_with_existing_system(self, backtest_performance: Dict) -> Dict:
        """與現有系統比較"""
        print(f"\n🔍 與現有系統比較...")
        
        # 載入現有系統的性能數據（從之前的分析）
        existing_performance = {
            'win_prediction_accuracy': 0.56,  # 56% from previous analysis
            'total_games_analyzed': 50,
            'home_score_bias': 0.64,
            'away_score_bias': -1.36
        }
        
        comparison = {
            'backtest_system': {
                'accuracy': backtest_performance.get('win_prediction_accuracy', 0),
                'total_games': backtest_performance.get('total_games', 0),
                'avg_confidence': backtest_performance.get('avg_confidence', 0)
            },
            'existing_system': existing_performance,
            'improvements': {}
        }
        
        # 計算改進幅度
        if existing_performance['win_prediction_accuracy'] > 0:
            accuracy_improvement = (backtest_performance.get('win_prediction_accuracy', 0) - 
                                   existing_performance['win_prediction_accuracy'])
            comparison['improvements']['accuracy_change'] = accuracy_improvement
        
        print(f"📊 比較結果:")
        print(f"   回測系統準確率: {comparison['backtest_system']['accuracy']:.3f}")
        print(f"   現有系統準確率: {comparison['existing_system']['win_prediction_accuracy']:.3f}")
        if 'accuracy_change' in comparison['improvements']:
            print(f"   準確率變化: {comparison['improvements']['accuracy_change']:+.3f}")
        
        return comparison

def main():
    """主函數"""
    print("🎯 MLB歷史回測引擎")
    print("=" * 60)
    
    # 創建回測器
    backtester = HistoricalBacktester()
    
    # 執行回測（最近3個月）
    end_date = date.today().strftime('%Y-%m-%d')
    start_date = (date.today() - timedelta(days=90)).strftime('%Y-%m-%d')
    
    # 運行回測
    performance = backtester.run_backtest(start_date, end_date, sample_size=100)
    
    if performance:
        # 與現有系統比較
        comparison = backtester.compare_with_existing_system(performance)
        
        print(f"\n💡 改進建議:")
        if performance.get('win_prediction_accuracy', 0) > 0.56:
            print("✅ 回測系統表現優於現有系統")
            print("   建議: 整合回測邏輯到主系統")
        else:
            print("⚠️  回測系統需要改進")
            print("   建議: 優化特徵工程和預測模型")
        
        print("   其他改進機會:")
        print("   1. 整合更多實時數據源")
        print("   2. 加入投手對戰分析")
        print("   3. 考慮天氣和傷病因素")
        print("   4. 實施ensemble方法")

if __name__ == "__main__":
    main()