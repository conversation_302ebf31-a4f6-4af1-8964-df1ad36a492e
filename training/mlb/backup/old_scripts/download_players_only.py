#!/usr/bin/env python3
"""
專門下載球員數據的腳本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Team, Player, PlayerStats
from models.player_data_fetcher import PlayerDataFetcher
from datetime import date
import time

def download_player_data():
    """下載球員數據"""
    print("=" * 80)
    print("🚀 開始下載球員數據")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 初始化球員數據獲取器
            player_fetcher = PlayerDataFetcher()
            
            # 1. 下載球員基本信息
            print("\n1. 👥 下載球員基本信息")
            print("-" * 50)
            
            current_season = date.today().year
            print(f"獲取 {current_season} 賽季球員名單...")
            
            roster_results = player_fetcher.fetch_all_teams_rosters(current_season)
            
            success_teams = sum(1 for success in roster_results.values() if success)
            total_teams = len(roster_results)
            
            print(f"✅ 球員名單獲取結果: {success_teams}/{total_teams} 支球隊成功")
            
            # 檢查球員數量
            current_players = Player.query.count()
            active_players = Player.query.filter_by(active=True).count()
            
            print(f"✅ 當前球員總數: {current_players}")
            print(f"✅ 活躍球員數: {active_players}")
            
            # 2. 下載球員統計數據
            print("\n2. 📊 下載球員統計數據")
            print("-" * 50)
            
            if active_players > 0:
                print(f"開始獲取 {active_players} 名活躍球員的統計數據...")
                
                stats_results = player_fetcher.fetch_all_players_stats(current_season)
                
                success_players = sum(1 for success in stats_results.values() if success)
                
                print(f"✅ 球員統計獲取結果: {success_players}/{len(stats_results)} 名球員成功")
            else:
                print("❌ 沒有活躍球員，跳過統計數據獲取")
            
            # 3. 檢查最終數據統計
            print("\n3. 📈 最終數據統計")
            print("-" * 50)
            
            final_stats = {
                "球員記錄": Player.query.count(),
                "活躍球員": Player.query.filter_by(active=True).count(),
                "球員統計": PlayerStats.query.count(),
                "本季統計": PlayerStats.query.filter_by(season=current_season).count()
            }
            
            for stat_name, count in final_stats.items():
                print(f"✅ {stat_name}: {count:,}")
            
            # 4. 顯示球員樣本
            print("\n4. 🔍 球員數據樣本")
            print("-" * 50)
            
            # 顯示球員樣本
            sample_players = Player.query.filter_by(active=True).limit(10).all()
            print("活躍球員樣本:")
            for player in sample_players:
                team_name = "未知球隊"
                if player.current_team_id:
                    team = Team.query.filter_by(team_id=player.current_team_id).first()
                    if team:
                        team_name = team.team_code
                
                print(f"  - {player.full_name} ({player.primary_position}) - {team_name}")
            
            # 顯示統計樣本
            sample_stats = PlayerStats.query.filter_by(season=current_season).limit(5).all()
            if sample_stats:
                print(f"\n{current_season}賽季統計樣本:")
                for stat in sample_stats:
                    avg = f"{stat.batting_avg:.3f}" if stat.batting_avg else "N/A"
                    era = f"{stat.era:.2f}" if stat.era else "N/A"
                    print(f"  - {stat.player_name}: AVG {avg}, ERA {era}")
            
            print("\n" + "=" * 80)
            print("🎉 球員數據下載完成！")
            print("=" * 80)
            
            return True
            
        except Exception as e:
            print(f"\n❌ 下載過程中發生錯誤: {e}")
            return False

def main():
    """主函數"""
    print("🚀 MLB球員數據下載工具")
    print("=" * 80)
    
    success = download_player_data()
    
    if success:
        print("\n🎉 球員數據下載成功！")
        print("\n📱 現在可以訪問:")
        print("  • 球員頁面: http://localhost:5500/players/")
        print("  • 球員統計: http://localhost:5500/players/stats")
        print("  • 比賽詳情: http://localhost:5500/games/")
    else:
        print("\n❌ 球員數據下載失敗，請檢查網絡連接和API狀態")

if __name__ == "__main__":
    main()
