#!/usr/bin/env python3
"""
完整數據下載腳本
下載所有缺失的球員統計和Box Score數據
"""

import logging
from datetime import date, timedelta
from app import create_app
from models.player_data_fetcher import PlayerDataFetcher
from models.detailed_data_fetcher import DetailedDataFetcher
from models.database import db, Player, Game, PlayerStats, BoxScore

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_all_player_stats():
    """下載所有球員的統計數據"""
    logger.info("🏃‍♂️ 開始下載所有球員統計數據...")
    
    fetcher = PlayerDataFetcher()
    
    # 下載2024賽季統計
    logger.info("下載2024賽季統計...")
    result_2024 = fetcher.fetch_all_players_stats(2024)
    
    # 下載2023賽季統計
    logger.info("下載2023賽季統計...")
    result_2023 = fetcher.fetch_all_players_stats(2023)
    
    success_2024 = sum(1 for success in result_2024.values() if success)
    success_2023 = sum(1 for success in result_2023.values() if success)
    
    logger.info(f"✅ 球員統計下載完成:")
    logger.info(f"   2024賽季: {success_2024}/{len(result_2024)} 成功")
    logger.info(f"   2023賽季: {success_2023}/{len(result_2023)} 成功")
    
    return result_2024, result_2023

def download_more_box_scores():
    """下載最近三年的Box Score數據"""
    logger.info("📊 開始下載最近三年的Box Score數據...")

    fetcher = DetailedDataFetcher()

    # 獲取最近三年已完成但沒有Box Score的比賽
    end_date = date.today()
    start_date = end_date - timedelta(days=3*365)  # 三年

    # 查找沒有Box Score的已完成比賽
    games_without_boxscore = db.session.query(Game).filter(
        Game.date >= start_date,
        Game.date <= end_date,
        Game.game_status == 'completed',
        ~Game.game_id.in_(
            db.session.query(BoxScore.game_id).distinct()
        )
    ).order_by(Game.date.desc()).limit(1000).all()  # 限制1000場比賽，從最新開始

    logger.info(f"找到 {len(games_without_boxscore)} 場需要下載Box Score的比賽（最近三年）")

    success_count = 0
    for i, game in enumerate(games_without_boxscore, 1):
        try:
            logger.info(f"[{i}/{len(games_without_boxscore)}] 下載 {game.away_team} @ {game.home_team} ({game.date})")
            success = fetcher.fetch_game_boxscore(game.game_id)
            if success:
                success_count += 1

            # 每5場比賽後暫停一下，避免請求過於頻繁
            if i % 5 == 0:
                import time
                time.sleep(1)

        except Exception as e:
            logger.error(f"下載比賽 {game.game_id} 失敗: {e}")

    logger.info(f"✅ Box Score下載完成: {success_count}/{len(games_without_boxscore)} 成功")
    return success_count

def download_recent_detailed_data():
    """下載最近三年的詳細數據（按月處理）"""
    logger.info("🔄 下載最近三年的詳細數據...")

    fetcher = DetailedDataFetcher()

    # 下載最近三年的詳細數據，但只處理每月的第1天和第15天作為樣本
    end_date = date.today()
    start_date = end_date - timedelta(days=3*365)  # 三年

    logger.info(f"下載 {start_date} 到 {end_date} 的詳細數據（樣本日期）")

    current_date = start_date
    total_success = 0
    processed_dates = 0

    while current_date <= end_date:
        # 只處理每月的1號和15號，減少處理量
        if current_date.day in [1, 15]:
            try:
                logger.info(f"處理樣本日期: {current_date}")
                success = fetcher.fetch_games_by_date(current_date)

                if success:
                    total_success += 1
                    logger.info(f"  ✓ 成功處理日期 {current_date}")
                else:
                    logger.warning(f"  ✗ 處理日期 {current_date} 失敗")

                processed_dates += 1

            except Exception as e:
                logger.error(f"處理日期 {current_date} 失敗: {e}")

            # 避免請求過於頻繁
            import time
            time.sleep(2)

        current_date += timedelta(days=1)

    logger.info(f"✅ 詳細數據下載完成: 總共處理 {total_success}/{processed_dates} 個樣本日期")
    return total_success

def print_data_summary():
    """打印數據摘要"""
    logger.info("📈 數據庫狀態摘要:")
    
    # 查詢各表記錄數
    games_count = Game.query.count()
    players_count = Player.query.count()
    player_stats_count = PlayerStats.query.count()
    box_scores_count = BoxScore.query.count()
    
    logger.info(f"  比賽記錄: {games_count:,}")
    logger.info(f"  球員記錄: {players_count:,}")
    logger.info(f"  球員統計: {player_stats_count:,}")
    logger.info(f"  Box Scores: {box_scores_count:,}")
    
    # 統計不同賽季的球員統計
    stats_by_season = db.session.query(
        PlayerStats.season, 
        db.func.count(PlayerStats.id)
    ).group_by(PlayerStats.season).all()
    
    logger.info("  球員統計按賽季:")
    for season, count in stats_by_season:
        logger.info(f"    {season}賽季: {count:,} 條")

def main():
    """主函數"""
    app = create_app()
    
    with app.app_context():
        logger.info("🚀 開始完整數據下載...")
        
        # 1. 下載球員統計
        download_all_player_stats()
        
        # 2. 下載更多Box Score
        download_more_box_scores()
        
        # 3. 下載最近的詳細數據
        download_recent_detailed_data()
        
        # 4. 提交所有更改
        try:
            db.session.commit()
            logger.info("✅ 所有數據已保存到數據庫")
        except Exception as e:
            logger.error(f"❌ 保存數據失敗: {e}")
            db.session.rollback()
        
        # 5. 打印最終摘要
        print_data_summary()
        
        logger.info("🎉 完整數據下載完成！")

if __name__ == '__main__':
    main()
