#!/usr/bin/env python3
"""
執行全面數據系統測試
"""

import sys
import os
import logging
from datetime import date

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """主函數"""
    logger.info("=== 開始全面數據系統測試 ===")
    
    try:
        # 1. 測試數據模型
        logger.info("1. 測試數據模型...")
        from test_comprehensive_data import test_data_models
        
        if test_data_models():
            logger.info("✅ 數據模型測試通過")
        else:
            logger.error("❌ 數據模型測試失敗")
            return
        
        # 2. 測試全面數據系統
        logger.info("2. 測試全面數據系統...")
        from test_comprehensive_data import test_comprehensive_data_system
        
        if test_comprehensive_data_system():
            logger.info("✅ 全面數據系統測試通過")
        else:
            logger.error("❌ 全面數據系統測試失敗")
            return
        
        # 3. 測試Web界面
        logger.info("3. 測試Web界面...")
        test_web_interface()
        
        logger.info("🎉 所有測試完成！")
        logger.info("您現在可以：")
        logger.info("1. 啟動Web應用: python app.py")
        logger.info("2. 訪問管理面板: http://localhost:5500/admin")
        logger.info("3. 點擊 '全面數據管理' 開始使用新功能")
        
    except Exception as e:
        logger.error(f"測試過程中發生錯誤: {e}")

def test_web_interface():
    """測試Web界面"""
    try:
        from app import create_app

        app = create_app()

        with app.app_context():
            # 檢查新路由
            routes = [
                'admin.comprehensive_data_dashboard',
                'admin.fetch_all_comprehensive_data',
                'admin.fetch_team_comprehensive_data',
                'admin.update_player_trends',
                'admin.get_comprehensive_data_status',
                'admin.cleanup_old_comprehensive_data'
            ]

            for route in routes:
                try:
                    url = app.url_map.bind('localhost').build(route)
                    logger.info(f"✅ 路由 {route} 註冊成功: {url}")
                except Exception as e:
                    logger.warning(f"⚠️ 路由 {route} 註冊失敗: {e}")

            logger.info("✅ Web界面測試完成")

    except Exception as e:
        logger.error(f"Web界面測試失敗: {e}")

if __name__ == '__main__':
    main()
