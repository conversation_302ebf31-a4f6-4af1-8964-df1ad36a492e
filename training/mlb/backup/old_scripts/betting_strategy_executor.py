#!/usr/bin/env python3
"""
MLB博彩策略執行器
基於優化後的預測準確率提供投注建議和風險管理
整合動態讓分、增強大小分預測和盈利監控
"""

import sqlite3
import json
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass
from dynamic_spread_optimizer import DynamicSpreadOptimizer
from enhanced_total_predictor import EnhancedTotalPredictor
from models.smart_odds_fetcher import SmartOddsFetcher

logger = logging.getLogger(__name__)

@dataclass
class BettingRecommendation:
    """投注建議數據類"""
    game: str
    bet_type: str  # 'spread', 'total', 'moneyline'
    recommendation: str  # 'home', 'away', 'over', 'under', 'skip'
    confidence: float
    expected_value: float
    risk_level: str  # 'low', 'medium', 'high'
    kelly_percentage: float
    reasoning: str

@dataclass  
class BettingSession:
    """投注會話數據類"""
    date: str
    total_recommendations: int
    high_confidence_bets: int
    expected_roi: float
    risk_score: float
    bankroll_percentage: float

class BettingStrategyExecutor:
    """博彩策略執行器"""
    
    def __init__(self, db_path='instance/mlb_data.db', initial_bankroll: float = 10000.0):
        self.db_path = db_path
        self.spread_optimizer = DynamicSpreadOptimizer(db_path)
        self.total_predictor = EnhancedTotalPredictor(db_path)
        self.initial_bankroll = initial_bankroll
        self.current_bankroll = initial_bankroll
        # 初始化智能盤口獲取器（爬蟲優先，API備用）
        self.odds_fetcher = SmartOddsFetcher()
        logger.info("🎯 博彩策略執行器已整合智能盤口獲取器")
        
        # 策略參數
        self.strategy_params = {
            'min_confidence_threshold': 0.65,
            'max_risk_per_bet': 0.05,  # 最大單注5%
            'max_daily_risk': 0.20,    # 每日最大風險20%
            'profitable_accuracy_threshold': 0.524,  # 盈利線52.4%
            'kelly_multiplier': 0.25,  # 凱利公式保守系數
            'stop_loss_threshold': 0.15,  # 15%止損
            'target_roi': 0.15  # 年度目標15% ROI
        }
        
        # 準確率數據 (基於我們的測試結果)
        self.accuracy_data = {
            'spread': {
                'standard': 0.50,
                'dynamic': 0.55,  # 動態讓分改善目標
                'breakeven': 0.524
            },
            'total': {
                'standard': 0.64,
                'enhanced': 0.68,  # 增強預測目標 
                'breakeven': 0.524
            },
            'moneyline': {
                'current': 0.61,
                'breakeven': 0.524
            }
        }
    
    def generate_daily_betting_strategy(self, target_date: str = None) -> Dict:
        """生成每日投注策略 - 整合智能盤口獲取器"""
        if target_date is None:
            target_date = datetime.now().strftime('%Y-%m-%d')
        
        print(f"📊 生成每日投注策略 - {target_date}")
        print("=" * 60)
        
        try:
            # 使用智能盤口獲取器獲取真實博彩數據
            print("🕷️  使用智能盤口獲取器：爬蟲優先，數據庫備用，API最後...")
            odds_data = self.odds_fetcher.get_mlb_odds_today(target_date)
            
            if not odds_data or not odds_data.get('success'):
                print(f"⚠️  無法獲取 {target_date} 的真實盤口數據，使用預測數據")
                # 回退到原有的預測數據
                games = self._get_daily_games(target_date)
            else:
                print(f"✅ 成功獲取盤口數據 - 來源: {odds_data.get('data_source', 'unknown')}")
                print(f"📊 數據類型: {odds_data.get('data_type', 'unknown')}")
                print(f"🎯 找到 {odds_data.get('total_games', 0)} 場比賽的真實盤口")
                # 整合真實盤口數據
                games = self._integrate_real_odds_data(odds_data, target_date)
            
            if not games:
                return {'error': '沒有找到比賽數據', 'date': target_date}
            
            print(f"📅 最終處理 {len(games)} 場比賽")
            
            # 生成各類型投注建議
            recommendations = []
            
            for game in games:
                try:
                    # 讓分盤建議
                    spread_rec = self._generate_spread_recommendation(game)
                    if spread_rec:
                        recommendations.append(spread_rec)
                    
                    # 大小分建議  
                    total_rec = self._generate_total_recommendation(game)
                    if total_rec:
                        recommendations.append(total_rec)
                    
                    # 勝負盤建議
                    ml_rec = self._generate_moneyline_recommendation(game)
                    if ml_rec:
                        recommendations.append(ml_rec)
                        
                except Exception as e:
                    print(f"⚠️  處理比賽 {game['home_team']} vs {game['away_team']} 時出錯: {e}")
                    continue
            
            # 風險評估和資金管理
            session = self._evaluate_betting_session(recommendations, target_date)
            
            # 最終建議篩選
            final_recommendations = self._filter_recommendations_by_risk(recommendations, session)
            
            strategy = {
                'date': target_date,
                'total_games': len(games),
                'total_opportunities': len(recommendations),
                'recommended_bets': len(final_recommendations),
                'session_analysis': session.__dict__,
                'betting_recommendations': [rec.__dict__ for rec in final_recommendations],
                'risk_management': {
                    'daily_risk_budget': session.bankroll_percentage,
                    'expected_daily_return': session.expected_roi,
                    'risk_level_distribution': self._get_risk_distribution(final_recommendations)
                },
                'strategy_summary': self._generate_strategy_summary(final_recommendations, session)
            }
            
            return strategy
            
        except Exception as e:
            return {'error': f'策略生成失敗: {str(e)}', 'date': target_date}
    
    def _get_daily_games(self, date: str) -> List[Dict]:
        """獲取指定日期的比賽"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT g.home_team, g.away_team, g.date,
                       p.predicted_home_score, p.predicted_away_score,
                       p.home_win_probability, p.away_win_probability,
                       p.confidence, p.model_version
                FROM games g
                LEFT JOIN predictions p ON g.game_id = p.game_id
                WHERE g.date = ?
                AND g.game_status IN ('scheduled', 'pre-game')
                ORDER BY g.date
            ''', (date,))
            
            games = []
            for row in cursor.fetchall():
                games.append({
                    'home_team': row[0],
                    'away_team': row[1],
                    'date': row[2],
                    'predicted_home_score': row[3] or 4.5,
                    'predicted_away_score': row[4] or 4.5,
                    'home_win_probability': row[5] or 0.5,
                    'away_win_probability': row[6] or 0.5,
                    'confidence': row[7] or 0.5,
                    'model_version': row[8] or 'default'
                })
            
            conn.close()
            return games
            
        except Exception as e:
            print(f"❌ 獲取比賽數據錯誤: {e}")
            return []
    
    def _integrate_real_odds_data(self, odds_data: Dict, target_date: str) -> List[Dict]:
        """整合真實盤口數據與預測數據"""
        try:
            # 獲取基礎遊戲數據和預測
            base_games = self._get_daily_games(target_date)
            base_games_dict = {
                f"{game['away_team']}@{game['home_team']}": game 
                for game in base_games
            }
            
            integrated_games = []
            
            for odds_game in odds_data.get('games', []):
                away_team = odds_game.get('away_team', '')
                home_team = odds_game.get('home_team', '')
                game_key = f"{away_team}@{home_team}"
                
                # 查找對應的基礎數據
                base_game = base_games_dict.get(game_key)
                if not base_game:
                    # 創建基礎數據結構
                    base_game = {
                        'home_team': home_team,
                        'away_team': away_team,
                        'date': target_date,
                        'predicted_home_score': 4.5,  # 默認值
                        'predicted_away_score': 4.5,
                        'home_win_probability': 0.5,
                        'away_win_probability': 0.5,
                        'confidence': 0.6,
                        'model_version': 'default_with_odds'
                    }
                
                # 整合真實盤口數據
                odds_info = odds_game.get('odds', {})
                base_game.update({
                    'real_odds_available': True,
                    'data_source': odds_data.get('data_source', 'unknown'),
                    'data_type': odds_data.get('data_type', 'unknown'),
                    'total_line': self._parse_float(odds_info.get('total_line')),
                    'over_price': odds_info.get('over_price', -110),
                    'under_price': odds_info.get('under_price', -110),
                    'spread_line': self._parse_float(odds_info.get('spread_line')),
                    'home_spread_price': odds_info.get('home_spread_price', -110),
                    'away_spread_price': odds_info.get('away_spread_price', -110),
                    'bookmaker': odds_info.get('bookmaker', 'unknown')
                })
                
                integrated_games.append(base_game)
                logger.info(f"✅ 整合比賽: {away_team} @ {home_team}, 總分線: {base_game.get('total_line')}")
            
            return integrated_games
            
        except Exception as e:
            logger.error(f"❌ 整合真實盤口數據失敗: {e}")
            return self._get_daily_games(target_date)  # 回退到原有數據
    
    def _parse_float(self, value) -> Optional[float]:
        """安全解析浮點數"""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def _generate_spread_recommendation(self, game: Dict) -> Optional[BettingRecommendation]:
        """生成讓分盤投注建議 - 使用真實盤口數據"""
        try:
            # 檢查是否有真實讓分線
            real_spread_line = game.get('spread_line')
            if real_spread_line is not None:
                # 使用真實讓分線
                home_spread_price = game.get('home_spread_price', -110)
                away_spread_price = game.get('away_spread_price', -110)
                
                # 轉換美式賠率為歐洲賠率
                home_odds = self._american_to_decimal(home_spread_price)
                away_odds = self._american_to_decimal(away_spread_price)
                
                # 預測結果與真實讓分線比較
                predicted_result = game['predicted_home_score'] - game['predicted_away_score']
                spread_edge = predicted_result - real_spread_line
                
                if spread_edge > 0.8:  # 主隊優勢
                    recommendation = 'home'
                    win_probability = min(0.90, 0.55 + abs(spread_edge) / 20.0)
                    odds_win, odds_lose = home_odds, away_odds
                elif spread_edge < -0.8:  # 客隊優勢
                    recommendation = 'away'  
                    win_probability = min(0.90, 0.55 + abs(spread_edge) / 20.0)
                    odds_win, odds_lose = away_odds, home_odds
                else:
                    return None  # 沒有明確優勢
                
                reasoning = f"真實讓分: {real_spread_line:+.1f}, 預測優勢: {spread_edge:+.1f}, 數據源: {game.get('data_source', 'unknown')}"
                
            else:
                # 回退到動態讓分算法
                spread_analysis = self.spread_optimizer.calculate_dynamic_spread(
                    game['home_team'], game['away_team'], game['date']
                )
                
                dynamic_spread = spread_analysis['dynamic_spread']
                predicted_result = game['predicted_home_score'] + dynamic_spread - game['predicted_away_score']
                
                if predicted_result > 0.5:
                    recommendation = 'home'
                    win_probability = min(0.95, 0.5 + abs(predicted_result) / 10.0)
                elif predicted_result < -0.5:
                    recommendation = 'away'
                    win_probability = min(0.95, 0.5 + abs(predicted_result) / 10.0)
                else:
                    return None
                
                odds_win, odds_lose = 1.91, 1.91  # 標準讓分賠率
                reasoning = f"動態讓分 {dynamic_spread}, 預測結果: {predicted_result:+.1f} (預測: 主隊{game['predicted_home_score']:.1f} 客隊{game['predicted_away_score']:.1f})"
            
            # 信心度檢查
            if win_probability < self.strategy_params['min_confidence_threshold']:
                return None
            
            # 計算期望值和凱利比例
            expected_value = self._calculate_expected_value(win_probability, odds_win, odds_lose)
            if expected_value <= 0:
                return None
                
            kelly_percentage = self._calculate_kelly_percentage(win_probability, odds_win, odds_lose)
            
            return BettingRecommendation(
                game=f"{game['away_team']} @ {game['home_team']}",
                bet_type='spread',
                recommendation=recommendation,
                confidence=win_probability,
                expected_value=expected_value,
                risk_level=self._assess_risk_level(win_probability, expected_value),
                kelly_percentage=kelly_percentage,
                reasoning=reasoning
            )
            
        except Exception as e:
            print(f"❌ 讓分盤建議生成錯誤: {e}")
            return None
    
    def _generate_total_recommendation(self, game: Dict) -> Optional[BettingRecommendation]:
        """生成大小分投注建議 - 使用真實盤口數據"""
        try:
            # 檢查是否有真實大小分線
            real_total_line = game.get('total_line')
            
            if real_total_line is not None:
                # 使用真實大小分線
                over_price = game.get('over_price', -110)
                under_price = game.get('under_price', -110)
                
                # 轉換美式賠率為歐洲賠率
                over_odds = self._american_to_decimal(over_price)
                under_odds = self._american_to_decimal(under_price)
                
                # 預測總得分
                predicted_total = game['predicted_home_score'] + game['predicted_away_score']
                total_edge = predicted_total - real_total_line
                
                # 判斷投注方向
                if total_edge > 0.5:  # Over
                    recommendation = 'over'
                    # 根據差距計算勝率
                    win_probability = min(0.85, 0.55 + abs(total_edge) / 15.0)
                    odds_win, odds_lose = over_odds, under_odds
                elif total_edge < -0.5:  # Under  
                    recommendation = 'under'
                    win_probability = min(0.85, 0.55 + abs(total_edge) / 15.0)
                    odds_win, odds_lose = under_odds, over_odds
                else:
                    return None  # 差距太小
                
                reasoning = f"真實總分線: {real_total_line}, 預測總分: {predicted_total:.1f}, 優勢: {total_edge:+.1f}, 數據源: {game.get('data_source', 'unknown')}"
                
            else:
                # 回退到增強大小分預測
                total_prediction = self.total_predictor.predict_enhanced_total(
                    game['home_team'], game['away_team'],
                    game['predicted_home_score'], game['predicted_away_score'],
                    game['date']
                )
                
                enhanced_total = total_prediction['enhanced_total']
                over_probability = total_prediction['over_probability']
                confidence = total_prediction['confidence']
                standard_line = total_prediction['standard_line']
                
                if over_probability > 0.60 and confidence > self.strategy_params['min_confidence_threshold']:
                    recommendation = 'over'
                    win_probability = over_probability
                elif over_probability < 0.40 and confidence > self.strategy_params['min_confidence_threshold']:
                    recommendation = 'under'
                    win_probability = 1 - over_probability
                else:
                    return None
                
                odds_win, odds_lose = 1.91, 1.91
                reasoning = f"預測總分: {enhanced_total}, Over機率: {over_probability:.1%}, 線: {standard_line} (預測: 主隊{game['predicted_home_score']:.1f} 客隊{game['predicted_away_score']:.1f})"
            
            # 信心度檢查
            if win_probability < self.strategy_params['min_confidence_threshold']:
                return None
            
            # 計算期望值和凱利比例
            expected_value = self._calculate_expected_value(win_probability, odds_win, odds_lose)
            if expected_value <= 0:
                return None
                
            kelly_percentage = self._calculate_kelly_percentage(win_probability, odds_win, odds_lose)
            
            return BettingRecommendation(
                game=f"{game['away_team']} @ {game['home_team']}",
                bet_type='total',
                recommendation=recommendation,
                confidence=win_probability,
                expected_value=expected_value,
                risk_level=self._assess_risk_level(win_probability, expected_value),
                kelly_percentage=kelly_percentage,
                reasoning=reasoning
            )
            
        except Exception as e:
            print(f"❌ 大小分建議生成錯誤: {e}")
            return None
    
    def _generate_moneyline_recommendation(self, game: Dict) -> Optional[BettingRecommendation]:
        """生成勝負盤投注建議"""
        try:
            home_prob = game['home_win_probability']
            away_prob = game['away_win_probability']
            confidence = game['confidence']
            
            # 只考慮高信心度的勝負盤
            if confidence < 0.70:
                return None
            
            # 判斷投注方向
            if home_prob > 0.58:
                recommendation = 'home'
                win_probability = home_prob
            elif away_prob > 0.58:
                recommendation = 'away'
                win_probability = away_prob
            else:
                return None
            
            # 簡化賠率計算 (實際應該從博彩公司獲取)
            if win_probability > 0.6:
                odds_win = 1.65
                odds_lose = 2.25
            else:
                odds_win = 1.85
                odds_lose = 1.95
            
            expected_value = self._calculate_expected_value(win_probability, odds_win, odds_lose)
            
            if expected_value <= 0:
                return None
            
            kelly_percentage = self._calculate_kelly_percentage(win_probability, odds_win, odds_lose)
            
            return BettingRecommendation(
                game=f"{game['away_team']} @ {game['home_team']}",
                bet_type='moneyline',
                recommendation=recommendation,
                confidence=confidence,
                expected_value=expected_value,
                risk_level=self._assess_risk_level(confidence, expected_value),
                kelly_percentage=kelly_percentage,
                reasoning=f"勝率: {win_probability:.1%}, 模型信心: {confidence:.1%}"
            )
            
        except Exception as e:
            print(f"❌ 勝負盤建議生成錯誤: {e}")
            return None
    
    def _calculate_expected_value(self, win_prob: float, win_odds: float, lose_odds: float) -> float:
        """計算期望值"""
        return win_prob * (win_odds - 1) - (1 - win_prob) * 1
    
    def _calculate_kelly_percentage(self, win_prob: float, win_odds: float, lose_odds: float) -> float:
        """計算凱利公式投注比例"""
        ev = self._calculate_expected_value(win_prob, win_odds, lose_odds)
        if ev <= 0:
            return 0.0
        
        # 保守的凱利公式
        kelly = (win_prob * win_odds - 1) / (win_odds - 1)
        kelly_conservative = kelly * self.strategy_params['kelly_multiplier']
        
        # 限制最大單注比例
        return min(kelly_conservative, self.strategy_params['max_risk_per_bet'])
    
    def _assess_risk_level(self, confidence: float, expected_value: float) -> str:
        """評估風險等級"""
        if confidence >= 0.80 and expected_value >= 0.10:
            return 'low'
        elif confidence >= 0.70 and expected_value >= 0.05:
            return 'medium'  
        else:
            return 'high'
    
    def _evaluate_betting_session(self, recommendations: List[BettingRecommendation], date: str) -> BettingSession:
        """評估投注會話"""
        if not recommendations:
            return BettingSession(
                date=date,
                total_recommendations=0,
                high_confidence_bets=0,
                expected_roi=0.0,
                risk_score=0.0,
                bankroll_percentage=0.0
            )
        
        high_confidence_bets = len([r for r in recommendations if r.confidence >= 0.75])
        expected_roi = sum(r.expected_value * r.kelly_percentage for r in recommendations)
        total_risk = sum(r.kelly_percentage for r in recommendations)
        risk_score = min(1.0, total_risk / self.strategy_params['max_daily_risk'])
        
        return BettingSession(
            date=date,
            total_recommendations=len(recommendations),
            high_confidence_bets=high_confidence_bets,
            expected_roi=expected_roi,
            risk_score=risk_score,
            bankroll_percentage=min(total_risk, self.strategy_params['max_daily_risk'])
        )
    
    def _filter_recommendations_by_risk(self, recommendations: List[BettingRecommendation], 
                                       session: BettingSession) -> List[BettingRecommendation]:
        """根據風險管理篩選建議"""
        if not recommendations:
            return []
        
        # 按期望值排序
        sorted_recs = sorted(recommendations, key=lambda x: x.expected_value, reverse=True)
        
        # 控制每日風險
        filtered_recs = []
        cumulative_risk = 0.0
        
        for rec in sorted_recs:
            if cumulative_risk + rec.kelly_percentage <= self.strategy_params['max_daily_risk']:
                filtered_recs.append(rec)
                cumulative_risk += rec.kelly_percentage
            elif rec.risk_level == 'low' and rec.expected_value > 0.15:
                # 對於高期望值的低風險投注，可以稍微超出限制
                filtered_recs.append(rec)
                break
        
        return filtered_recs
    
    def _get_risk_distribution(self, recommendations: List[BettingRecommendation]) -> Dict:
        """獲取風險等級分佈"""
        risk_counts = {'low': 0, 'medium': 0, 'high': 0}
        for rec in recommendations:
            risk_counts[rec.risk_level] += 1
        return risk_counts
    
    def _generate_strategy_summary(self, recommendations: List[BettingRecommendation], 
                                 session: BettingSession) -> Dict:
        """生成策略摘要"""
        if not recommendations:
            return {'summary': '今日無推薦投注', 'reason': '沒有符合標準的投注機會'}
        
        bet_types = {}
        for rec in recommendations:
            if rec.bet_type not in bet_types:
                bet_types[rec.bet_type] = 0
            bet_types[rec.bet_type] += 1
        
        total_bankroll_risk = sum(rec.kelly_percentage for rec in recommendations)
        expected_return = sum(rec.expected_value * rec.kelly_percentage for rec in recommendations)
        
        summary = {
            'recommendation_count': len(recommendations),
            'bet_type_distribution': bet_types,
            'total_bankroll_risk': f"{total_bankroll_risk:.1%}",
            'expected_daily_return': f"{expected_return:.2%}",
            'risk_assessment': 'conservative' if total_bankroll_risk < 0.10 else 'moderate' if total_bankroll_risk < 0.15 else 'aggressive',
            'top_recommendation': {
                'game': recommendations[0].game,
                'type': recommendations[0].bet_type,
                'pick': recommendations[0].recommendation,
                'confidence': f"{recommendations[0].confidence:.1%}",
                'expected_value': f"{recommendations[0].expected_value:.2%}"
            }
        }
        
        return summary
    
    def _american_to_decimal(self, american_odds: int) -> float:
        """將美式賠率轉換為歐洲賠率"""
        try:
            if american_odds > 0:
                return (american_odds / 100) + 1
            elif american_odds < 0:
                return (100 / abs(american_odds)) + 1
            else:
                return 2.0  # 默認賠率
        except (ValueError, TypeError, ZeroDivisionError):
            return 1.91  # 標準賠率

def main():
    """測試博彩策略執行器"""
    print("🚀 MLB博彩策略執行器")
    print("=" * 60)
    
    executor = BettingStrategyExecutor()
    
    # 測試今日策略
    strategy = executor.generate_daily_betting_strategy('2025-08-15')
    
    if 'error' not in strategy:
        print(f"\n📊 每日投注策略結果:")
        print(json.dumps(strategy, indent=2, ensure_ascii=False))
    else:
        print(f"❌ 策略生成失敗: {strategy['error']}")
    
    print(f"\n✅ 博彩策略執行器已就緒!")
    print(f"🎯 核心功能: 風險管理 + 期望值最大化 + 凱利公式")

if __name__ == "__main__":
    main()