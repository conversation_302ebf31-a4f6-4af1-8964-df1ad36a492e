#!/usr/bin/env python3
"""
大小分準確率驗證器
基於真實歷史數據驗證增強大小分預測的改善效果
"""

import sqlite3
import json
from typing import Dict, List
from enhanced_total_predictor import EnhancedTotalPredictor

class TotalAccuracyValidator:
    """大小分準確率驗證器"""
    
    def __init__(self, db_path='instance/mlb_data.db'):
        self.db_path = db_path
        self.predictor = EnhancedTotalPredictor(db_path)
    
    def validate_enhanced_total_predictions(self) -> Dict:
        """驗證增強大小分預測的準確率"""
        print("🎯 驗證增強大小分預測 - 基於真實比賽結果")
        print("=" * 60)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 獲取有完整數據的歷史比賽
            cursor.execute('''
                SELECT g.home_team, g.away_team, g.date, g.home_score, g.away_score,
                       p.predicted_home_score, p.predicted_away_score,
                       p.model_version
                FROM games g
                JOIN predictions p ON g.game_id = p.game_id
                WHERE g.home_score IS NOT NULL 
                AND g.away_score IS NOT NULL
                AND p.predicted_home_score IS NOT NULL
                AND g.date >= '2025-07-01'
                AND g.date <= '2025-08-18'
                ORDER BY g.date DESC
                LIMIT 100
            ''')
            
            games_data = []
            for row in cursor.fetchall():
                games_data.append({
                    'home_team': row[0],
                    'away_team': row[1],
                    'date': row[2],
                    'home_score': row[3],
                    'away_score': row[4],
                    'predicted_home': row[5],
                    'predicted_away': row[6],
                    'model_version': row[7]
                })
            
            conn.close()
            
            if not games_data:
                print("❌ 沒有找到可用的測試數據")
                return {'error': '無測試數據'}
            
            print(f"📊 找到 {len(games_data)} 場有完整數據的比賽")
            
            # 評估標準大小分預測(9.0線)的表現
            standard_results = self._evaluate_standard_total(games_data)
            
            # 評估基礎預測總分的表現
            base_results = self._evaluate_base_total_predictions(games_data)
            
            # 評估增強大小分預測的表現
            enhanced_results = self._evaluate_enhanced_total_predictions(games_data)
            
            # 比較結果
            comparison = self._compare_total_results(standard_results, base_results, enhanced_results)
            
            return {
                'total_games': len(games_data),
                'standard_total_performance': standard_results,
                'base_prediction_performance': base_results,
                'enhanced_prediction_performance': enhanced_results,
                'comparison': comparison,
                'test_period': '2025-07-01 to 2025-08-18'
            }
            
        except Exception as e:
            print(f"❌ 驗證過程錯誤: {e}")
            return {'error': str(e)}
    
    def _evaluate_standard_total(self, games_data: List[Dict]) -> Dict:
        """評估標準大小分線(9.0)的表現"""
        print("\n📏 評估標準大小分線(9.0)表現...")
        
        over_correct = 0
        under_correct = 0
        total_games = 0
        details = []
        
        for game in games_data:
            try:
                actual_total = game['home_score'] + game['away_score']
                standard_line = 9.0
                
                # 實際結果
                actual_over = actual_total > standard_line
                
                # 標準預測 - 使用50%機率作為基準
                # 實際上這裡我們評估的是如果隨機選擇的準確率
                if actual_over:
                    over_correct += 1
                else:
                    under_correct += 1
                
                details.append({
                    'game': f"{game['away_team']} @ {game['home_team']}",
                    'actual_total': actual_total,
                    'standard_line': standard_line,
                    'actual_over': actual_over
                })
                
                total_games += 1
                
            except Exception as e:
                continue
        
        over_accuracy = (over_correct / total_games * 100) if total_games > 0 else 0
        under_accuracy = (under_correct / total_games * 100) if total_games > 0 else 0
        
        # 假設隨機選擇的準確率約為50%
        baseline_accuracy = 50.0
        
        print(f"  Over實際發生率: {over_accuracy:.1f}% ({over_correct}/{total_games})")
        print(f"  Under實際發生率: {under_accuracy:.1f}% ({under_correct}/{total_games})")
        print(f"  隨機選擇基準: {baseline_accuracy:.1f}%")
        
        return {
            'over_rate': over_accuracy,
            'under_rate': under_accuracy,
            'baseline_accuracy': baseline_accuracy,
            'total_games': total_games,
            'sample_details': details[:5]
        }
    
    def _evaluate_base_total_predictions(self, games_data: List[Dict]) -> Dict:
        """評估基礎總分預測的準確率"""
        print("\n📊 評估基礎總分預測表現...")
        
        correct = 0
        total = 0
        details = []
        total_errors = []
        
        for game in games_data:
            try:
                actual_total = game['home_score'] + game['away_score']
                predicted_total = game['predicted_home'] + game['predicted_away']
                standard_line = 9.0
                
                # 實際Over/Under
                actual_over = actual_total > standard_line
                
                # 預測Over/Under
                predicted_over = predicted_total > standard_line
                
                # 準確性判斷
                is_correct = actual_over == predicted_over
                if is_correct:
                    correct += 1
                
                # 計算總分預測誤差
                total_error = abs(actual_total - predicted_total)
                total_errors.append(total_error)
                
                details.append({
                    'game': f"{game['away_team']} @ {game['home_team']}",
                    'actual_total': actual_total,
                    'predicted_total': round(predicted_total, 1),
                    'actual_over': actual_over,
                    'predicted_over': predicted_over,
                    'correct': is_correct,
                    'total_error': round(total_error, 1)
                })
                
                total += 1
                
            except Exception as e:
                continue
        
        accuracy = (correct / total * 100) if total > 0 else 0
        avg_error = sum(total_errors) / len(total_errors) if total_errors else 0
        
        print(f"  基礎預測準確率: {accuracy:.1f}% ({correct}/{total})")
        print(f"  平均總分誤差: {avg_error:.2f}分")
        
        return {
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'avg_total_error': avg_error,
            'sample_details': details[:5]
        }
    
    def _evaluate_enhanced_total_predictions(self, games_data: List[Dict]) -> Dict:
        """評估增強大小分預測的準確率"""
        print("\n📈 評估增強大小分預測表現...")
        
        correct = 0
        total = 0
        high_confidence_correct = 0
        high_confidence_total = 0
        details = []
        total_errors = []
        confidence_scores = []
        
        for game in games_data:
            try:
                actual_total = game['home_score'] + game['away_score']
                
                # 生成增強預測
                enhanced_prediction = self.predictor.predict_enhanced_total(
                    game['home_team'],
                    game['away_team'],
                    game['predicted_home'],
                    game['predicted_away'],
                    game['date']
                )
                
                enhanced_total = enhanced_prediction['enhanced_total']
                confidence = enhanced_prediction['confidence']
                standard_line = enhanced_prediction['standard_line']
                
                # 實際Over/Under
                actual_over = actual_total > standard_line
                
                # 增強預測Over/Under
                predicted_over = enhanced_total > standard_line
                
                # 準確性判斷
                is_correct = actual_over == predicted_over
                if is_correct:
                    correct += 1
                
                # 高信心度預測
                if confidence > 0.65:
                    high_confidence_total += 1
                    if is_correct:
                        high_confidence_correct += 1
                
                # 計算總分預測誤差
                total_error = abs(actual_total - enhanced_total)
                total_errors.append(total_error)
                confidence_scores.append(confidence)
                
                details.append({
                    'game': f"{game['away_team']} @ {game['home_team']}",
                    'actual_total': actual_total,
                    'enhanced_total': enhanced_total,
                    'actual_over': actual_over,
                    'predicted_over': predicted_over,
                    'confidence': confidence,
                    'correct': is_correct,
                    'total_error': round(total_error, 1),
                    'recommendation': enhanced_prediction['recommendation']
                })
                
                total += 1
                
            except Exception as e:
                print(f"⚠️  增強預測錯誤: {e}")
                continue
        
        accuracy = (correct / total * 100) if total > 0 else 0
        high_conf_accuracy = (high_confidence_correct / high_confidence_total * 100) if high_confidence_total > 0 else 0
        avg_error = sum(total_errors) / len(total_errors) if total_errors else 0
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        print(f"  增強預測準確率: {accuracy:.1f}% ({correct}/{total})")
        print(f"  高信心度準確率: {high_conf_accuracy:.1f}% ({high_confidence_correct}/{high_confidence_total})")
        print(f"  平均總分誤差: {avg_error:.2f}分")
        print(f"  平均信心度: {avg_confidence:.1%}")
        
        return {
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'high_confidence_accuracy': high_conf_accuracy,
            'high_confidence_games': high_confidence_total,
            'avg_total_error': avg_error,
            'avg_confidence': avg_confidence,
            'sample_details': details[:5]
        }
    
    def _compare_total_results(self, standard: Dict, base: Dict, enhanced: Dict) -> Dict:
        """比較不同方法的大小分預測結果"""
        base_improvement = enhanced['accuracy'] - base['accuracy']
        baseline_improvement = enhanced['accuracy'] - standard['baseline_accuracy']
        
        comparison = {
            'accuracy_vs_base': round(base_improvement, 2),
            'accuracy_vs_baseline': round(baseline_improvement, 2),
            'error_reduction': round(base['avg_total_error'] - enhanced['avg_total_error'], 2),
            'high_confidence_advantage': enhanced['high_confidence_accuracy'] - enhanced['accuracy'],
            'is_better_than_base': base_improvement > 0,
            'is_better_than_baseline': baseline_improvement > 5.0,  # 超過55%才算顯著改善
            'confidence_level': 'high' if abs(base_improvement) > 5 else 'medium' if abs(base_improvement) > 2 else 'low',
            'target_achievement': '達標' if enhanced['accuracy'] >= 70.0 else '未達標'
        }
        
        print(f"\n🏆 比較結果:")
        print(f"  vs基礎預測改善: {base_improvement:+.1f}%")
        print(f"  vs隨機基準改善: {baseline_improvement:+.1f}%") 
        print(f"  總分誤差減少: {comparison['error_reduction']:+.2f}分")
        print(f"  70%目標: {comparison['target_achievement']}")
        
        return comparison

def main():
    """執行大小分準確率驗證"""
    print("🚀 大小分準確率驗證器")
    print("=" * 60)
    
    validator = TotalAccuracyValidator()
    results = validator.validate_enhanced_total_predictions()
    
    if 'error' not in results:
        print(f"\n✅ 驗證完成!")
        print(f"\n📊 完整驗證結果:")
        print(json.dumps(results, indent=2, ensure_ascii=False))
        
        # 判斷是否達成70%目標
        if results['enhanced_prediction_performance']['accuracy'] >= 70.0:
            print(f"\n🎉 恭喜！已達成70%+準確率目標！")
        else:
            print(f"\n📈 持續改進中，目標70%準確率")
    else:
        print(f"❌ 驗證失敗: {results['error']}")

if __name__ == "__main__":
    main()