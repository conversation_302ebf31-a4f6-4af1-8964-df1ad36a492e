#!/usr/bin/env python3
"""
MLB統一預測系統 - 查詢和追蹤預測結果工具

使用方法:
python query_predictions.py --date 2025-06-28                    # 查詢特定日期的預測結果
python query_predictions.py --yesterday                          # 查詢昨天的預測結果
python query_predictions.py --update --date 2025-06-28          # 更新特定日期的預測結果
python query_predictions.py --update --yesterday                # 更新昨天的預測結果
python query_predictions.py --stats --days 7                    # 查看過去7天的統計
"""

import argparse
import sys
import os
from datetime import date, datetime, timedelta
from pathlib import Path

# 添加項目根目錄到Python路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app
from models.unified_betting_predictor import UnifiedBettingPredictor
from models.database import db, Prediction, Game

def query_predictions(target_date: date, app):
    """查詢特定日期的預測結果"""
    print(f"\n🔍 查詢 {target_date} 的預測結果...")

    with app.app_context():
        predictor = UnifiedBettingPredictor(app)

        # 首先嘗試獲取統一預測器版本的預測
        predictions = Prediction.query.join(Game).filter(
            Game.date >= target_date,
            Game.date < target_date + timedelta(days=1),
            Prediction.model_version == predictor.model_version
        ).all()

        # 如果沒有統一版本的預測，查找所有版本的預測
        if not predictions:
            print(f"⚠️  沒有找到統一預測器版本 ({predictor.model_version}) 的記錄，查找所有版本...")
            predictions = Prediction.query.join(Game).filter(
                Game.date >= target_date,
                Game.date < target_date + timedelta(days=1)
            ).all()
        
        if not predictions:
            print(f"❌ {target_date} 沒有找到預測記錄")
            return
        
        print(f"📊 找到 {len(predictions)} 個預測記錄")

        # 顯示模型版本信息
        model_versions = set(p.model_version for p in predictions)
        print(f"📋 模型版本: {', '.join(model_versions)}\n")

        # 統計數據
        total_predictions = len(predictions)
        correct_predictions = sum(1 for p in predictions if p.is_correct is True)
        completed_predictions = sum(1 for p in predictions if p.is_correct is not None)
        accuracy_rate = (correct_predictions / completed_predictions * 100) if completed_predictions > 0 else 0

        print(f"📈 統計摘要:")
        print(f"   總預測數: {total_predictions}")
        print(f"   已完成: {completed_predictions}")
        print(f"   正確預測: {correct_predictions}")
        print(f"   準確率: {accuracy_rate:.1f}%")
        print()
        
        # 詳細預測結果
        print("📋 詳細結果:")
        print("-" * 100)
        print(f"{'比賽':<20} {'預測得分':<15} {'實際得分':<15} {'勝負':<8} {'大小分':<15} {'信心度':<8} {'品質':<6}")
        print("-" * 100)
        
        for pred in predictions:
            game = Game.query.filter_by(game_id=pred.game_id).first()
            if not game:
                continue
                
            # 比賽信息
            matchup = f"{game.away_team}@{game.home_team}"
            
            # 預測得分
            pred_score = f"{pred.predicted_away_score or 0}-{pred.predicted_home_score or 0}"
            
            # 實際得分
            if pred.actual_away_score is not None and pred.actual_home_score is not None:
                actual_score = f"{pred.actual_away_score}-{pred.actual_home_score}"
            else:
                actual_score = "未完成"
            
            # 勝負結果
            if pred.is_correct is not None:
                win_result = "✓" if pred.is_correct else "✗"
            else:
                win_result = "?"
            
            # 大小分結果
            if hasattr(pred, 'over_under_line') and pred.over_under_line:
                if hasattr(pred, 'over_under_correct') and pred.over_under_correct is not None:
                    ou_result = f"✓ ({pred.over_under_line})" if pred.over_under_correct else f"✗ ({pred.over_under_line})"
                else:
                    ou_result = f"? ({pred.over_under_line})"
            else:
                ou_result = "N/A"

            # 信心度
            confidence = f"{pred.confidence*100:.0f}%" if pred.confidence else "N/A"

            # 品質分數
            if hasattr(pred, 'quality_score') and pred.quality_score:
                quality_grade = 'A' if pred.quality_score >= 80 else 'B' if pred.quality_score >= 60 else 'C' if pred.quality_score >= 40 else 'D'
                quality = f"{quality_grade}({pred.quality_score})"
            else:
                quality = "N/A"
            
            print(f"{matchup:<20} {pred_score:<15} {actual_score:<15} {win_result:<8} {ou_result:<15} {confidence:<8} {quality:<6}")

def update_predictions(target_date: date, app):
    """更新特定日期的預測結果"""
    print(f"\n🔄 更新 {target_date} 的預測結果...")
    
    with app.app_context():
        predictor = UnifiedBettingPredictor(app)
        result = predictor.update_prediction_results(target_date)
        
        if result['success']:
            print(f"✅ 成功更新 {result.get('updated_count', 0)} 個預測結果")
        else:
            print(f"❌ 更新失敗: {result.get('error', '未知錯誤')}")

def show_stats(days_back: int, app):
    """顯示統計信息"""
    print(f"\n📊 過去 {days_back} 天的預測統計...")
    
    with app.app_context():
        predictor = UnifiedBettingPredictor(app)
        analytics = predictor.get_performance_analytics(days_back)
        
        if not analytics['success']:
            print(f"❌ 獲取統計失敗: {analytics.get('error', '未知錯誤')}")
            return
        
        if 'analytics' not in analytics or not analytics['analytics']:
            print("❌ 沒有找到統計數據")
            return
        
        stats = analytics['analytics']
        
        print(f"📈 基本統計:")
        print(f"   總預測數: {stats.get('total_predictions', 0)}")
        print(f"   正確預測: {stats.get('correct_predictions', 0)}")
        print(f"   準確率: {stats.get('accuracy_rate', 0):.1f}%")
        print(f"   平均信心度: {stats.get('average_confidence', 0):.1f}%")
        print(f"   平均得分差異: {stats.get('average_score_difference', 0):.2f}")
        
        if 'confidence_analysis' in stats:
            conf_analysis = stats['confidence_analysis']
            print(f"\n🎯 信心度分析:")
            print(f"   高信心度 (>80%): {conf_analysis.get('high_confidence_count', 0)} 個, 準確率: {conf_analysis.get('high_confidence_accuracy', 0):.1f}%")
            print(f"   中信心度 (60-80%): {conf_analysis.get('medium_confidence_count', 0)} 個, 準確率: {conf_analysis.get('medium_confidence_accuracy', 0):.1f}%")
            print(f"   低信心度 (<60%): {conf_analysis.get('low_confidence_count', 0)} 個, 準確率: {conf_analysis.get('low_confidence_accuracy', 0):.1f}%")

def main():
    parser = argparse.ArgumentParser(description='MLB統一預測系統查詢工具')
    parser.add_argument('--date', type=str, help='查詢日期 (YYYY-MM-DD)')
    parser.add_argument('--yesterday', action='store_true', help='查詢昨天的結果')
    parser.add_argument('--update', action='store_true', help='更新預測結果')
    parser.add_argument('--stats', action='store_true', help='顯示統計信息')
    parser.add_argument('--days', type=int, default=7, help='統計天數 (默認7天)')
    
    args = parser.parse_args()
    
    # 確定目標日期
    if args.yesterday:
        target_date = date.today() - timedelta(days=1)
    elif args.date:
        try:
            target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
        except ValueError:
            print("❌ 日期格式錯誤，請使用 YYYY-MM-DD 格式")
            return
    elif args.stats:
        target_date = None
    else:
        print("❌ 請指定 --date 或 --yesterday 參數")
        parser.print_help()
        return
    
    # 創建Flask應用
    app = create_app()
    
    try:
        if args.stats:
            show_stats(args.days, app)
        elif args.update:
            update_predictions(target_date, app)
            # 更新後顯示結果
            query_predictions(target_date, app)
        else:
            query_predictions(target_date, app)
            
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
