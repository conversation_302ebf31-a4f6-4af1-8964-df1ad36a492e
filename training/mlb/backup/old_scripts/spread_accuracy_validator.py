#!/usr/bin/env python3
"""
讓分盤準確率驗證器
基於真實歷史數據驗證動態讓分算法的改善效果
"""

import sqlite3
import json
from typing import Dict, List
from dynamic_spread_optimizer import DynamicSpreadOptimizer

class SpreadAccuracyValidator:
    """讓分盤準確率驗證器"""
    
    def __init__(self, db_path='instance/mlb_data.db'):
        self.db_path = db_path
        self.optimizer = DynamicSpreadOptimizer(db_path)
    
    def validate_with_real_betting_data(self) -> Dict:
        """使用真實博彩數據驗證動態讓分算法"""
        print("🎯 驗證動態讓分算法 - 基於真實博彩結果")
        print("=" * 60)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 獲取有完整博彩數據的歷史比賽
            cursor.execute('''
                SELECT g.home_team, g.away_team, g.date, g.home_score, g.away_score,
                       p.predicted_home_score, p.predicted_away_score,
                       p.model_version
                FROM games g
                JOIN predictions p ON g.game_id = p.game_id
                WHERE g.home_score IS NOT NULL 
                AND g.away_score IS NOT NULL
                AND p.predicted_home_score IS NOT NULL
                AND g.date >= '2025-07-01'
                AND g.date <= '2025-08-18'
                ORDER BY g.date DESC
                LIMIT 100
            ''')
            
            games_data = []
            for row in cursor.fetchall():
                games_data.append({
                    'home_team': row[0],
                    'away_team': row[1],
                    'date': row[2],
                    'home_score': row[3],
                    'away_score': row[4],
                    'predicted_home': row[5],
                    'predicted_away': row[6],
                    'model_version': row[7]
                })
            
            conn.close()
            
            if not games_data:
                print("❌ 沒有找到可用的測試數據")
                return {'error': '無測試數據'}
            
            print(f"📊 找到 {len(games_data)} 場有完整數據的比賽")
            
            # 評估標準讓分(-1.5)的表現
            standard_results = self._evaluate_standard_spread(games_data)
            
            # 評估動態讓分的表現
            dynamic_results = self._evaluate_dynamic_spread(games_data)
            
            # 比較結果
            comparison = self._compare_results(standard_results, dynamic_results)
            
            return {
                'total_games': len(games_data),
                'standard_spread_performance': standard_results,
                'dynamic_spread_performance': dynamic_results,
                'comparison': comparison,
                'test_period': '2025-07-01 to 2025-08-18'
            }
            
        except Exception as e:
            print(f"❌ 驗證過程錯誤: {e}")
            return {'error': str(e)}
    
    def _evaluate_standard_spread(self, games_data: List[Dict]) -> Dict:
        """評估標準讓分(-1.5)的表現"""
        print("\n📏 評估標準讓分(-1.5)表現...")
        
        correct = 0
        total = 0
        details = []
        
        for game in games_data:
            try:
                home_score = game['home_score']
                away_score = game['away_score']
                
                # 標準讓分結果
                spread_result = home_score - 1.5 - away_score
                home_covers = spread_result > 0
                
                # 實際結果 (用於正確性驗證)
                actual_home_wins = home_score > away_score
                
                # 讓分預測正確性: 如果預測主隊蓋分且主隊確實蓋分，或預測客隊蓋分且客隊確實蓋分
                if spread_result > 0:  # 預測主隊蓋分
                    correct_prediction = home_score + 1.5 > away_score
                else:  # 預測客隊蓋分
                    correct_prediction = home_score + 1.5 <= away_score
                
                if correct_prediction:
                    correct += 1
                
                details.append({
                    'game': f"{game['away_team']} @ {game['home_team']}",
                    'actual_score': f"{away_score}-{home_score}",
                    'spread_result': round(spread_result, 1),
                    'home_covers': home_covers,
                    'correct': correct_prediction
                })
                
                total += 1
                
            except Exception as e:
                continue
        
        accuracy = (correct / total * 100) if total > 0 else 0
        
        print(f"  標準讓分準確率: {accuracy:.1f}% ({correct}/{total})")
        
        return {
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'sample_details': details[:5]
        }
    
    def _evaluate_dynamic_spread(self, games_data: List[Dict]) -> Dict:
        """評估動態讓分的表現"""
        print("\n📈 評估動態讓分表現...")
        
        correct = 0
        total = 0
        details = []
        spread_adjustments = []
        
        for game in games_data:
            try:
                home_score = game['home_score']
                away_score = game['away_score']
                
                # 計算動態讓分
                spread_analysis = self.optimizer.calculate_dynamic_spread(
                    game['home_team'],
                    game['away_team'], 
                    game['date']
                )
                
                dynamic_spread = spread_analysis['dynamic_spread']
                spread_adjustments.append(spread_analysis['total_adjustment'])
                
                # 動態讓分結果
                spread_result = home_score + dynamic_spread - away_score
                home_covers = spread_result > 0
                
                # 讓分預測正確性
                if spread_result > 0:  # 預測主隊蓋分
                    correct_prediction = home_score + dynamic_spread > away_score
                else:  # 預測客隊蓋分
                    correct_prediction = home_score + dynamic_spread <= away_score
                
                if correct_prediction:
                    correct += 1
                
                details.append({
                    'game': f"{game['away_team']} @ {game['home_team']}",
                    'actual_score': f"{away_score}-{home_score}",
                    'dynamic_spread': dynamic_spread,
                    'spread_result': round(spread_result, 1),
                    'home_covers': home_covers,
                    'correct': correct_prediction,
                    'adjustment': spread_analysis['total_adjustment']
                })
                
                total += 1
                
            except Exception as e:
                continue
        
        accuracy = (correct / total * 100) if total > 0 else 0
        avg_adjustment = sum(spread_adjustments) / len(spread_adjustments) if spread_adjustments else 0
        
        print(f"  動態讓分準確率: {accuracy:.1f}% ({correct}/{total})")
        print(f"  平均調整幅度: {avg_adjustment:+.3f}")
        
        return {
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'avg_adjustment': avg_adjustment,
            'sample_details': details[:5]
        }
    
    def _compare_results(self, standard: Dict, dynamic: Dict) -> Dict:
        """比較標準讓分與動態讓分的結果"""
        improvement = dynamic['accuracy'] - standard['accuracy']
        
        comparison = {
            'accuracy_improvement': round(improvement, 2),
            'improvement_percentage': round((improvement / standard['accuracy'] * 100), 1) if standard['accuracy'] > 0 else 0,
            'is_better': improvement > 0,
            'confidence_level': 'high' if abs(improvement) > 3 else 'medium' if abs(improvement) > 1 else 'low'
        }
        
        print(f"\n🏆 比較結果:")
        print(f"  準確率改善: {improvement:+.1f}%")
        print(f"  改善百分比: {comparison['improvement_percentage']:+.1f}%")
        print(f"  演算法優勢: {'是' if improvement > 0 else '否'}")
        
        return comparison

def main():
    """執行讓分盤準確率驗證"""
    print("🚀 讓分盤準確率驗證器")
    print("=" * 60)
    
    validator = SpreadAccuracyValidator()
    results = validator.validate_with_real_betting_data()
    
    if 'error' not in results:
        print(f"\n✅ 驗證完成!")
        print(f"\n📊 完整驗證結果:")
        print(json.dumps(results, indent=2, ensure_ascii=False))
    else:
        print(f"❌ 驗證失敗: {results['error']}")

if __name__ == "__main__":
    main()