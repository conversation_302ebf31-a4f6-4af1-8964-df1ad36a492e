#!/usr/bin/env python3
"""
MLB預測系統博彩準確率評估器
基於真實博彩盤口(讓分盤、大小分)評估預測準確率
"""

import sqlite3
import numpy as np
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional

class BettingAccuracyEvaluator:
    """基於博彩盤口的預測準確率評估器"""
    
    def __init__(self, db_path='instance/mlb_data.db'):
        self.db_path = db_path
        self.evaluation_results = {}
        
    def get_predictions_with_odds(self) -> List[Dict]:
        """獲取有盤口數據的預測記錄"""
        print("🔍 查詢有博彩盤口數據的預測記錄...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查詢有實際結果的預測，使用標準盤口
            cursor.execute('''
                SELECT p.id, p.game_id, p.predicted_home_score, p.predicted_away_score,
                       p.actual_home_score, p.actual_away_score, p.model_version,
                       p.over_under_line, p.predicted_total_runs, p.actual_total_runs,
                       g.date, g.home_team, g.away_team, g.home_score, g.away_score
                FROM predictions p
                JOIN games g ON p.game_id = g.game_id
                WHERE p.actual_home_score IS NOT NULL 
                AND p.actual_away_score IS NOT NULL
                AND g.home_score IS NOT NULL
                AND g.away_score IS NOT NULL
                ORDER BY g.date DESC
                LIMIT 200
            ''')
            
            records = cursor.fetchall()
            conn.close()
            
            predictions = []
            for row in records:
                # 計算標準盤口
                predicted_total = float(row[2]) + float(row[3]) if row[2] and row[3] else 9.0
                actual_total = int(row[4]) + int(row[5]) if row[4] and row[5] else 0
                
                # 使用標準MLB盤口
                standard_spread = -1.5  # 主隊標準讓1.5分
                standard_total = float(row[7]) if row[7] else 9.0  # 使用預測中的大小分線，預設9分
                
                pred = {
                    'pred_id': row[0],
                    'game_id': row[1],
                    'predicted_home': float(row[2]) if row[2] else 0,
                    'predicted_away': float(row[3]) if row[3] else 0,
                    'actual_home': int(row[4]) if row[4] else 0,
                    'actual_away': int(row[5]) if row[5] else 0,
                    'model_version': row[6],
                    'ou_line': standard_total,
                    'predicted_total': predicted_total,
                    'actual_total': actual_total,
                    'game_date': row[10],
                    'home_team': row[11],
                    'away_team': row[12],
                    'home_score': int(row[13]) if row[13] else 0,
                    'away_score': int(row[14]) if row[14] else 0,
                    'spread_home': standard_spread,    # 標準讓分盤
                    'spread_away': -standard_spread,
                    'total_line': standard_total,      # 標準大小分
                    'home_ml': -110,                   # 標準勝負盤賠率
                    'away_ml': -110
                }
                predictions.append(pred)
            
            print(f"找到 {len(predictions)} 筆有效預測記錄")
            return predictions
            
        except Exception as e:
            print(f"❌ 查詢錯誤: {e}")
            return []
    
    def evaluate_spread_betting(self, prediction: Dict) -> Dict:
        """評估讓分盤預測準確率"""
        pred_home = prediction['predicted_home']
        pred_away = prediction['predicted_away']
        actual_home = prediction['actual_home']
        actual_away = prediction['actual_away']
        spread_home = prediction['spread_home']  # 主隊讓分，負值表示讓分
        
        # 計算預測的讓分後結果
        predicted_spread_result = pred_home + spread_home - pred_away
        
        # 計算實際的讓分後結果
        actual_spread_result = actual_home + spread_home - actual_away
        
        # 判斷預測是否正確
        # 如果預測讓分後主隊贏(>0)，實際也是讓分後主隊贏，則正確
        predicted_home_covers = predicted_spread_result > 0
        actual_home_covers = actual_spread_result > 0
        
        spread_correct = predicted_home_covers == actual_home_covers
        
        return {
            'spread_line': spread_home,
            'predicted_spread_result': predicted_spread_result,
            'actual_spread_result': actual_spread_result,
            'predicted_home_covers': predicted_home_covers,
            'actual_home_covers': actual_home_covers,
            'spread_correct': spread_correct,
            'spread_margin': abs(actual_spread_result)  # 讓分勝負的幅度
        }
    
    def evaluate_total_betting(self, prediction: Dict) -> Dict:
        """評估大小分預測準確率"""
        predicted_total = prediction['predicted_total']
        actual_total = prediction['actual_total']
        total_line = prediction['total_line']
        
        # 預測Over/Under
        predicted_over = predicted_total > total_line
        actual_over = actual_total > total_line
        
        # 判斷預測是否正確
        total_correct = predicted_over == actual_over
        
        # 計算與盤口的差距
        predicted_difference = predicted_total - total_line
        actual_difference = actual_total - total_line
        
        return {
            'total_line': total_line,
            'predicted_total': predicted_total,
            'actual_total': actual_total,
            'predicted_over': predicted_over,
            'actual_over': actual_over,
            'total_correct': total_correct,
            'predicted_difference': predicted_difference,
            'actual_difference': actual_difference,
            'total_margin': abs(actual_difference)  # 大小分的幅度
        }
    
    def evaluate_moneyline_betting(self, prediction: Dict) -> Dict:
        """評估勝負盤預測準確率"""
        pred_home = prediction['predicted_home']
        pred_away = prediction['predicted_away']
        actual_home = prediction['actual_home']
        actual_away = prediction['actual_away']
        
        # 預測勝負
        predicted_home_wins = pred_home > pred_away
        actual_home_wins = actual_home > actual_away
        
        # 判斷預測是否正確
        ml_correct = predicted_home_wins == actual_home_wins
        
        return {
            'predicted_home_wins': predicted_home_wins,
            'actual_home_wins': actual_home_wins,
            'ml_correct': ml_correct,
            'win_margin': abs(actual_home - actual_away)  # 實際勝負幅度
        }
    
    def comprehensive_evaluation(self, predictions: List[Dict]) -> Dict:
        """綜合評估所有預測"""
        print("\\n📊 開始綜合博彩準確率評估...")
        
        if not predictions:
            print("❌ 沒有可評估的預測數據")
            return {}
        
        spread_results = []
        total_results = []
        ml_results = []
        
        evaluation_details = []
        
        for pred in predictions:
            try:
                # 評估讓分盤
                spread_eval = self.evaluate_spread_betting(pred)
                spread_results.append(spread_eval)
                
                # 評估大小分
                total_eval = self.evaluate_total_betting(pred)
                total_results.append(total_eval)
                
                # 評估勝負盤
                ml_eval = self.evaluate_moneyline_betting(pred)
                ml_results.append(ml_eval)
                
                # 記錄詳細結果
                detail = {
                    'game': f"{pred['away_team']} @ {pred['home_team']}",
                    'date': pred['game_date'],
                    'actual_score': f"{pred['actual_away']}-{pred['actual_home']}",
                    'predicted_score': f"{pred['predicted_away']:.1f}-{pred['predicted_home']:.1f}",
                    'spread_line': spread_eval['spread_line'],
                    'spread_correct': spread_eval['spread_correct'],
                    'total_line': total_eval['total_line'],
                    'total_correct': total_eval['total_correct'],
                    'ml_correct': ml_eval['ml_correct'],
                    'model_version': pred['model_version']
                }
                evaluation_details.append(detail)
                
            except Exception as e:
                print(f"⚠️  評估錯誤: {pred.get('game_id', 'unknown')} - {e}")
                continue
        
        # 計算總體統計
        total_games = len(spread_results)
        
        if total_games == 0:
            return {}
        
        # 讓分盤統計
        spread_correct = sum(1 for r in spread_results if r['spread_correct'])
        spread_accuracy = (spread_correct / total_games) * 100
        
        # 大小分統計
        total_correct = sum(1 for r in total_results if r['total_correct'])
        total_accuracy = (total_correct / total_games) * 100
        
        # 勝負盤統計
        ml_correct = sum(1 for r in ml_results if r['ml_correct'])
        ml_accuracy = (ml_correct / total_games) * 100
        
        # 按模型版本統計
        model_stats = self._calculate_model_accuracy(evaluation_details)
        
        results = {
            'evaluation_date': datetime.now().isoformat(),
            'total_games_evaluated': total_games,
            'spread_betting': {
                'accuracy': spread_accuracy,
                'correct': spread_correct,
                'total': total_games,
                'avg_margin': np.mean([r['spread_margin'] for r in spread_results])
            },
            'total_betting': {
                'accuracy': total_accuracy,
                'correct': total_correct,
                'total': total_games,
                'avg_margin': np.mean([r['total_margin'] for r in total_results])
            },
            'moneyline_betting': {
                'accuracy': ml_accuracy,
                'correct': ml_correct,
                'total': total_games,
                'avg_margin': np.mean([r['win_margin'] for r in ml_results])
            },
            'by_model': model_stats,
            'evaluation_details': evaluation_details[:20]  # 只保存前20筆詳細記錄
        }
        
        return results
    
    def _calculate_model_accuracy(self, details: List[Dict]) -> Dict:
        """按模型版本計算準確率"""
        model_stats = {}
        
        for detail in details:
            version = detail['model_version']
            if version not in model_stats:
                model_stats[version] = {
                    'spread': {'correct': 0, 'total': 0},
                    'total': {'correct': 0, 'total': 0},
                    'ml': {'correct': 0, 'total': 0}
                }
            
            model_stats[version]['spread']['total'] += 1
            model_stats[version]['total']['total'] += 1
            model_stats[version]['ml']['total'] += 1
            
            if detail['spread_correct']:
                model_stats[version]['spread']['correct'] += 1
            if detail['total_correct']:
                model_stats[version]['total']['correct'] += 1
            if detail['ml_correct']:
                model_stats[version]['ml']['correct'] += 1
        
        # 計算百分比
        for version, stats in model_stats.items():
            for bet_type in ['spread', 'total', 'ml']:
                total = stats[bet_type]['total']
                correct = stats[bet_type]['correct']
                stats[bet_type]['accuracy'] = (correct / total * 100) if total > 0 else 0
        
        return model_stats
    
    def display_results(self, results: Dict):
        """顯示評估結果"""
        if not results:
            print("❌ 沒有評估結果可顯示")
            return
        
        print(f"\\n📈 博彩準確率評估結果")
        print("=" * 60)
        print(f"評估總場次: {results['total_games_evaluated']}")
        print()
        
        # 讓分盤結果
        spread = results['spread_betting']
        print(f"🎯 讓分盤準確率: {spread['accuracy']:.1f}% ({spread['correct']}/{spread['total']})")
        print(f"   平均讓分幅度: {spread['avg_margin']:.1f}分")
        
        # 大小分結果
        total = results['total_betting']
        print(f"🎲 大小分準確率: {total['accuracy']:.1f}% ({total['correct']}/{total['total']})")
        print(f"   平均分差幅度: {total['avg_margin']:.1f}分")
        
        # 勝負盤結果
        ml = results['moneyline_betting']
        print(f"🏆 勝負盤準確率: {ml['accuracy']:.1f}% ({ml['correct']}/{ml['total']})")
        print(f"   平均勝負幅度: {ml['avg_margin']:.1f}分")
        
        # 按模型版本顯示
        print(f"\\n📊 各模型版本表現:")
        for version, stats in results['by_model'].items():
            spread_acc = stats['spread']['accuracy']
            total_acc = stats['total']['accuracy']
            ml_acc = stats['ml']['accuracy']
            total_games = stats['spread']['total']
            
            print(f"\\n  {version} ({total_games}場):")
            print(f"    讓分盤: {spread_acc:.1f}%")
            print(f"    大小分: {total_acc:.1f}%")
            print(f"    勝負盤: {ml_acc:.1f}%")
        
        # 顯示最近幾場詳細結果
        print(f"\\n🔍 最近比賽詳細結果:")
        print("-" * 80)
        print("比賽                     | 比分    | 讓分✓ | 大小分✓ | 勝負✓")
        print("-" * 80)
        
        for detail in results['evaluation_details'][:10]:
            spread_mark = "✅" if detail['spread_correct'] else "❌"
            total_mark = "✅" if detail['total_correct'] else "❌"
            ml_mark = "✅" if detail['ml_correct'] else "❌"
            
            print(f"{detail['game']:<25} | {detail['actual_score']:<7} | {spread_mark:<5} | {total_mark:<7} | {ml_mark}")
    
    def save_evaluation_report(self, results: Dict):
        """保存評估報告"""
        if not results:
            return
        
        filename = f"betting_accuracy_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"\\n📄 博彩準確率評估報告已保存: {filename}")
            
        except Exception as e:
            print(f"❌ 報告保存錯誤: {e}")
    
    def generate_improvement_suggestions(self, results: Dict) -> List[str]:
        """生成改進建議"""
        suggestions = []
        
        if not results:
            return suggestions
        
        spread_acc = results['spread_betting']['accuracy']
        total_acc = results['total_betting']['accuracy']
        ml_acc = results['moneyline_betting']['accuracy']
        
        # 讓分盤建議
        if spread_acc < 52:  # 博彩盈虧平衡點約52.4%
            suggestions.append("🎯 讓分盤準確率偏低，建議調整得分預測模型")
        elif spread_acc > 55:
            suggestions.append("🎯 讓分盤表現優秀，可考慮增加讓分盤投注策略")
        
        # 大小分建議
        if total_acc < 52:
            suggestions.append("🎲 大小分準確率需要改善，檢查總分預測算法")
        elif total_acc > 55:
            suggestions.append("🎲 大小分表現良好，可專注於大小分投注")
        
        # 綜合建議
        best_type = max([
            ('讓分盤', spread_acc),
            ('大小分', total_acc),
            ('勝負盤', ml_acc)
        ], key=lambda x: x[1])
        
        suggestions.append(f"💡 當前{best_type[0]}表現最佳({best_type[1]:.1f}%)，建議重點發展")
        
        # 模型建議
        if results['by_model']:
            best_model = max(results['by_model'].items(), 
                           key=lambda x: (x[1]['spread']['accuracy'] + x[1]['total']['accuracy']) / 2)
            suggestions.append(f"🧠 模型{best_model[0]}表現最佳，建議優化其他模型向此靠攏")
        
        return suggestions

def main():
    """主執行函數"""
    print("🎰 MLB預測系統博彩準確率評估器")
    print("=" * 60)
    print("基於真實博彩盤口評估預測準確率")
    print()
    
    evaluator = BettingAccuracyEvaluator()
    
    # 1. 獲取預測數據
    predictions = evaluator.get_predictions_with_odds()
    
    # 2. 進行綜合評估
    results = evaluator.comprehensive_evaluation(predictions)
    
    # 3. 顯示結果
    evaluator.display_results(results)
    
    # 4. 生成改進建議
    if results:
        suggestions = evaluator.generate_improvement_suggestions(results)
        print(f"\\n💡 改進建議:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
    
    # 5. 保存報告
    evaluator.save_evaluation_report(results)
    
    print(f"\\n✅ 博彩準確率評估完成!")
    
    if results:
        spread_acc = results['spread_betting']['accuracy']
        total_acc = results['total_betting']['accuracy']
        print(f"🎯 關鍵指標: 讓分盤 {spread_acc:.1f}% | 大小分 {total_acc:.1f}%")
        
        if spread_acc > 52.4 or total_acc > 52.4:
            print("🎉 部分投注類型已達到盈利水準!")
        else:
            print("⚠️  投注準確率仍需改善以達到盈利水準")

if __name__ == "__main__":
    main()