#!/usr/bin/env python3
"""
測試真實API密鑰功能
驗證 The Odds API 是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from flask import Flask
from models.database import db, Game
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from models.over_under_predictor import OverUnderPredictor
from models.run_line_predictor import RunLinePredictor

def create_test_app():
    """創建測試Flask應用"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///mlb_data.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    return app

def test_api_key_configuration():
    """測試API密鑰配置"""
    print("=" * 80)
    print("🔑 測試API密鑰配置")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        print(f"API密鑰狀態: {'已配置' if fetcher.odds_api_key else '未配置'}")
        if fetcher.odds_api_key:
            # 只顯示前8個字符，保護隱私
            masked_key = fetcher.odds_api_key[:8] + "..." + fetcher.odds_api_key[-4:]
            print(f"API密鑰: {masked_key}")
        
        # 檢查API狀態
        status = fetcher.check_api_status()
        print(f"API狀態: {status['message']}")
        print(f"API可訪問: {'是' if status['api_accessible'] else '否'}")
        
        return fetcher

def test_real_odds_fetching():
    """測試真實盤口獲取"""
    print("\n" + "=" * 80)
    print("📊 測試真實博彩盤口獲取")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        if not fetcher.odds_api_key:
            print("❌ 沒有API密鑰，無法測試真實盤口")
            return None
        
        # 獲取今日MLB盤口
        print("正在獲取今日MLB博彩盤口...")
        odds_data = fetcher.get_mlb_odds_today()
        
        print(f"比賽數量: {odds_data['summary']['total_games']}")
        print(f"可用市場: {', '.join(odds_data['summary']['markets_available'])}")
        print(f"博彩商數量: {odds_data['summary'].get('bookmakers_count', 0)}")
        
        if odds_data['summary'].get('note'):
            print(f"⚠️  注意: {odds_data['summary']['note']}")
        else:
            print("✅ 成功獲取真實博彩盤口數據")
        
        # 顯示前3場比賽的真實盤口
        print(f"\n🏟️  前3場比賽的真實博彩盤口:")
        for i, game in enumerate(odds_data['games'][:3]):
            print(f"\n比賽 {i+1}: {game['away_team']} @ {game['home_team']}")
            
            # 勝負盤
            ml = game['odds']['moneyline']
            if ml['home'] and ml['away']:
                print(f"  勝負盤: 主隊 {ml['home']} | 客隊 {ml['away']} ({ml['bookmaker']})")
            
            # 讓分盤
            rl = game['odds']['run_line']
            if rl['home_line'] and rl['away_line']:
                print(f"  讓分盤: 主隊 {rl['home_line']} ({rl['home_odds']}) | 客隊 {rl['away_line']} ({rl['away_odds']}) ({rl['bookmaker']})")
            
            # 大小分
            total = game['odds']['total']
            if total['line']:
                print(f"  大小分: {total['line']} (大分 {total['over_odds']} | 小分 {total['under_odds']}) ({total['bookmaker']})")
        
        return odds_data

def test_real_predictions():
    """測試使用真實盤口的預測"""
    print("\n" + "=" * 80)
    print("🎯 測試使用真實盤口的預測")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        # 獲取今日比賽
        today_games = Game.query.filter(
            Game.date == date.today(),
            Game.game_status.in_(['scheduled', 'in_progress'])
        ).limit(2).all()
        
        if not today_games:
            print("❌ 今日沒有可預測的比賽")
            return
        
        # 測試大小分預測
        print("\n📈 大小分預測 (使用真實盤口):")
        over_under_predictor = OverUnderPredictor(app)
        
        for i, game in enumerate(today_games[:1]):  # 只測試1場
            print(f"\n🏟️  比賽 {i+1}: {game.away_team} @ {game.home_team}")
            
            result = over_under_predictor.predict_over_under(game.game_id)
            
            if 'error' in result:
                print(f"❌ 預測失敗: {result['error']}")
            else:
                print(f"✅ 預測成功")
                print(f"大小分盤口: {result['total_line']}")
                print(f"盤口來源: {'真實博彩商' if result.get('real_odds_used') else '算法計算'}")
                print(f"預期總得分: {result['expected_runs']['total']:.1f}")
                print(f"大分概率: {result['over_probability']:.1%}")
                print(f"推薦: {result['recommendation']}")
        
        # 測試讓分盤預測
        print("\n📊 讓分盤預測 (使用真實盤口):")
        run_line_predictor = RunLinePredictor(app)
        
        for i, game in enumerate(today_games[:1]):  # 只測試1場
            print(f"\n🏟️  比賽 {i+1}: {game.away_team} @ {game.home_team}")
            
            result = run_line_predictor.predict_run_line(game.game_id)
            
            if 'error' in result:
                print(f"❌ 預測失敗: {result['error']}")
            else:
                print(f"✅ 預測成功")
                run_line_data = result['run_line_data']
                prediction = result['prediction']
                
                print(f"讓分盤: 主隊 {run_line_data['home_line']} | 客隊 {run_line_data['away_line']}")
                print(f"盤口來源: {run_line_data['source']} ({'真實' if run_line_data['is_real_data'] else '模擬'})")
                print(f"推薦: {prediction['recommendation']}")

def compare_real_vs_simulated():
    """比較真實盤口 vs 模擬盤口"""
    print("\n" + "=" * 80)
    print("⚖️  真實盤口 vs 模擬盤口比較")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        if not fetcher.odds_api_key:
            print("❌ 沒有API密鑰，無法進行比較")
            return
        
        # 獲取真實盤口
        real_odds = fetcher.get_mlb_odds_today()
        
        # 獲取模擬盤口 (臨時移除API密鑰)
        temp_key = fetcher.odds_api_key
        fetcher.odds_api_key = None
        simulated_odds = fetcher.get_mlb_odds_today()
        fetcher.odds_api_key = temp_key
        
        print("📊 數據比較:")
        print(f"真實盤口比賽數: {real_odds['summary']['total_games']}")
        print(f"模擬盤口比賽數: {simulated_odds['summary']['total_games']}")
        
        if real_odds['games'] and simulated_odds['games']:
            real_game = real_odds['games'][0]
            sim_game = simulated_odds['games'][0]
            
            print(f"\n🏟️  示例比賽: {real_game['away_team']} @ {real_game['home_team']}")
            
            # 比較大小分
            real_total = real_game['odds']['total']['line']
            sim_total = sim_game['odds']['total']['line']
            print(f"大小分 - 真實: {real_total} | 模擬: {sim_total}")
            
            # 比較讓分盤
            real_rl = real_game['odds']['run_line']['home_line']
            sim_rl = sim_game['odds']['run_line']['home_line']
            print(f"讓分盤 - 真實: {real_rl} | 模擬: {sim_rl}")
            
            if real_total != sim_total or real_rl != sim_rl:
                print("✅ 真實盤口與模擬盤口存在差異，API正常工作")
            else:
                print("⚠️  真實盤口與模擬盤口相同，可能API未正常工作")

def main():
    """主測試函數"""
    print("🔑 MLB真實API密鑰測試")
    print("測試時間:", date.today().strftime('%Y-%m-%d'))
    
    try:
        # 1. 測試API密鑰配置
        fetcher = test_api_key_configuration()
        
        # 2. 測試真實盤口獲取
        odds_data = test_real_odds_fetching()
        
        # 3. 測試真實盤口預測
        test_real_predictions()
        
        # 4. 比較真實 vs 模擬盤口
        compare_real_vs_simulated()
        
        print("\n" + "=" * 80)
        print("✅ 所有測試完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
