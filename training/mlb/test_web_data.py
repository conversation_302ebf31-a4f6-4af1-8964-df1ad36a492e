#!/usr/bin/env python3
"""
測試Web應用是否顯示正確的數據
"""

import requests
import json
from datetime import date

def test_web_app():
    """測試Web應用數據"""
    base_url = "http://localhost:5500"
    
    print("🧪 測試Web應用數據")
    print("=" * 50)
    
    # 測試1: 檢查大小分預測頁面
    print("1. 測試大小分預測頁面...")
    try:
        url = f"{base_url}/predictions/over_under?date=2025-07-01"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print(f"  ✅ 頁面加載成功 (狀態碼: {response.status_code})")
            
            # 檢查是否包含STL@PIT
            if "STL" in response.text and "PIT" in response.text:
                print("  ✅ 找到STL@PIT比賽")
            else:
                print("  ❌ 沒有找到STL@PIT比賽")
            
            # 檢查是否有修復後的數據
            if "7.6" in response.text or "7.0" in response.text:
                print("  ✅ 可能包含修復後的數據")
            else:
                print("  ⚠️  沒有找到修復後的數據")
                
        else:
            print(f"  ❌ 頁面加載失敗 (狀態碼: {response.status_code})")
            
    except Exception as e:
        print(f"  ❌ 請求失敗: {e}")
    
    print()
    
    # 測試2: 直接查詢數據庫
    print("2. 直接查詢數據庫...")
    try:
        import sqlite3
        import os
        
        # 使用與Flask相同的數據庫路徑
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.join(basedir, "instance", "mlb_data.db")
        
        print(f"  數據庫路徑: {db_path}")
        
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查詢STL@PIT的數據
            cursor.execute("""
                SELECT 
                    g.away_team || '@' || g.home_team as matchup,
                    p.predicted_total_runs,
                    p.over_under_line,
                    p.predicted_home_score,
                    p.predicted_away_score,
                    p.updated_at
                FROM predictions p
                JOIN games g ON p.game_id = g.game_id
                WHERE g.date = '2025-07-01'
                AND g.away_team = 'STL' AND g.home_team = 'PIT'
            """)
            
            result = cursor.fetchone()
            
            if result:
                matchup, total, line, home, away, updated = result
                print(f"  ✅ 找到數據: {matchup}")
                print(f"    預測得分: {away}-{home} (總分: {total})")
                print(f"    盤口: {line}")
                print(f"    更新時間: {updated}")
                
                # 檢查是否是修復後的數據
                if total and total < 10 and line and line > 0:
                    print("  ✅ 數據已修復")
                else:
                    print("  ❌ 數據未修復")
            else:
                print("  ❌ 沒有找到STL@PIT的數據")
            
            conn.close()
        else:
            print(f"  ❌ 數據庫文件不存在: {db_path}")
            
    except Exception as e:
        print(f"  ❌ 數據庫查詢失敗: {e}")
    
    print()
    
    # 測試3: 檢查所有2025-07-01的預測
    print("3. 檢查所有2025-07-01的預測...")
    try:
        import sqlite3
        import os
        
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.join(basedir, "instance", "mlb_data.db")
        
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    g.away_team || '@' || g.home_team as matchup,
                    p.predicted_total_runs,
                    p.over_under_line
                FROM predictions p
                JOIN games g ON p.game_id = g.game_id
                WHERE g.date = '2025-07-01'
                ORDER BY g.game_id
            """)
            
            results = cursor.fetchall()
            
            print(f"  找到 {len(results)} 場比賽:")
            normal_count = 0
            abnormal_count = 0
            
            for matchup, total, line in results:
                status = "✅" if (total and 4 <= total <= 12 and line and line > 0) else "❌"
                if status == "✅":
                    normal_count += 1
                else:
                    abnormal_count += 1
                    
                print(f"    {status} {matchup}: 總分={total}, 盤口={line}")
            
            print(f"  📊 統計: 正常 {normal_count} 個，異常 {abnormal_count} 個")
            
            conn.close()
        
    except Exception as e:
        print(f"  ❌ 查詢失敗: {e}")

if __name__ == '__main__':
    test_web_app()
