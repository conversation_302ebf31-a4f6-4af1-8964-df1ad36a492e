#!/usr/bin/env python3
"""
大小分預測系統測試腳本
測試投手對戰分析和大小分預測功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction
from models.over_under_predictor import OverUnderPredictor
from models.prediction_service import PredictionService
from datetime import date, datetime
import json

def test_over_under_predictor():
    """測試大小分預測器"""
    print("=" * 60)
    print("測試大小分預測系統")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        # 初始化預測器
        over_under_predictor = OverUnderPredictor()
        
        # 獲取今日比賽
        today = date.today()
        games = Game.query.filter(
            Game.date == today,
            Game.game_status.in_(['scheduled', 'pre-game'])
        ).limit(3).all()
        
        if not games:
            print("❌ 今日沒有可預測的比賽")
            # 嘗試獲取最近的比賽
            games = Game.query.filter(
                Game.game_status.in_(['scheduled', 'pre-game', 'final'])
            ).order_by(Game.date.desc()).limit(3).all()
        
        print(f"📊 找到 {len(games)} 場比賽進行測試")
        
        success_count = 0
        total_count = len(games)
        
        for i, game in enumerate(games, 1):
            print(f"\n🏟️  測試比賽 {i}/{total_count}: {game.away_team} @ {game.home_team}")
            print(f"   比賽日期: {game.date}")
            print(f"   比賽狀態: {game.game_status}")
            
            try:
                # 測試大小分預測
                prediction_result = over_under_predictor.predict_over_under(game.game_id)
                
                if 'error' in prediction_result:
                    print(f"   ❌ 預測失敗: {prediction_result['error']}")
                    continue
                
                # 顯示預測結果
                print(f"   ✅ 預測成功!")
                print(f"   📈 預期總分: {prediction_result.get('expected_runs', {}).get('total', 'N/A')}")
                print(f"   🎯 大小分盤口: {prediction_result.get('total_line', 'N/A')}")
                print(f"   📊 大分概率: {prediction_result.get('over_probability', 0):.1%}")
                print(f"   📉 小分概率: {prediction_result.get('under_probability', 0):.1%}")
                print(f"   🎲 信心度: {prediction_result.get('confidence', 0):.1%}")
                print(f"   💡 推薦: {prediction_result.get('recommendation', 'N/A')}")
                
                # 顯示投手信息
                pitcher_info = prediction_result.get('pitcher_info', {})
                if pitcher_info:
                    print(f"   🥎 主隊投手: {pitcher_info.get('home_pitcher', '未確認')}")
                    print(f"   🥎 客隊投手: {pitcher_info.get('away_pitcher', '未確認')}")
                
                # 顯示關鍵因素
                key_factors = prediction_result.get('key_factors', [])
                if key_factors:
                    print(f"   🔑 關鍵因素:")
                    for factor in key_factors[:3]:  # 只顯示前3個
                        print(f"      • {factor}")
                
                success_count += 1
                
            except Exception as e:
                print(f"   ❌ 測試失敗: {str(e)}")
        
        print(f"\n📊 測試結果: {success_count}/{total_count} 成功")
        return success_count == total_count

def test_prediction_service_integration():
    """測試預測服務整合"""
    print("\n" + "=" * 60)
    print("測試預測服務整合")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        prediction_service = PredictionService()
        
        # 獲取一場比賽進行測試
        game = Game.query.filter(
            Game.game_status.in_(['scheduled', 'pre-game', 'final'])
        ).first()
        
        if not game:
            print("❌ 沒有可測試的比賽")
            return False
        
        print(f"🏟️  測試比賽: {game.away_team} @ {game.home_team}")
        
        try:
            # 測試生成預測（包含大小分）
            prediction_result = prediction_service._create_new_prediction(game)
            
            if not prediction_result:
                print("❌ 預測服務整合失敗")
                return False
            
            print("✅ 預測服務整合成功!")
            print(f"📊 勝負預測: {prediction_result.get('predicted_home_score', 'N/A')} - {prediction_result.get('predicted_away_score', 'N/A')}")
            
            # 檢查大小分數據
            over_under = prediction_result.get('over_under', {})
            if over_under:
                print(f"📈 大小分預測: {over_under.get('predicted_total_runs', 'N/A')}")
                print(f"🎯 盤口: {over_under.get('total_line', 'N/A')}")
                print(f"💡 推薦: {over_under.get('recommendation', 'N/A')}")
            
            # 檢查投手分析
            pitcher_analysis = prediction_result.get('pitcher_analysis', {})
            if pitcher_analysis:
                print(f"🥎 投手對戰優勢: {pitcher_analysis.get('matchup_advantage', 'N/A')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 預測服務整合測試失敗: {str(e)}")
            return False

def test_database_schema():
    """測試數據庫架構"""
    print("\n" + "=" * 60)
    print("測試數據庫架構")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 檢查Prediction模型是否有新的大小分字段
            prediction = Prediction.query.first()
            
            if not prediction:
                print("⚠️  沒有預測數據進行測試")
                return True
            
            # 檢查新字段
            new_fields = [
                'predicted_total_runs',
                'over_under_line', 
                'over_probability',
                'under_probability',
                'over_under_confidence',
                'pitcher_vs_batter_analysis',
                'starting_pitcher_home',
                'starting_pitcher_away',
                'pitcher_matchup_advantage'
            ]
            
            missing_fields = []
            for field in new_fields:
                if not hasattr(prediction, field):
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 缺少字段: {', '.join(missing_fields)}")
                print("💡 請運行數據庫遷移來添加新字段")
                return False
            else:
                print("✅ 數據庫架構檢查通過")
                return True
                
        except Exception as e:
            print(f"❌ 數據庫架構測試失敗: {str(e)}")
            return False

def test_web_interface():
    """測試Web界面"""
    print("\n" + "=" * 60)
    print("測試Web界面")
    print("=" * 60)
    
    app = create_app()
    
    with app.test_client() as client:
        try:
            # 測試大小分預測頁面
            response = client.get('/predictions/over_under')
            
            if response.status_code == 200:
                print("✅ 大小分預測頁面載入成功")
            else:
                print(f"❌ 大小分預測頁面載入失敗: {response.status_code}")
                return False
            
            # 測試API端點
            response = client.get('/predictions/api/generate_over_under')
            
            if response.status_code == 200:
                print("✅ 大小分預測API正常")
            else:
                print(f"⚠️  大小分預測API響應: {response.status_code}")
            
            return True
            
        except Exception as e:
            print(f"❌ Web界面測試失敗: {str(e)}")
            return False

def main():
    """主測試函數"""
    print("🚀 開始大小分預測系統測試")
    
    tests = [
        ("數據庫架構", test_database_schema),
        ("大小分預測器", test_over_under_predictor),
        ("預測服務整合", test_prediction_service_integration),
        ("Web界面", test_web_interface)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 測試 {test_name} 出現異常: {str(e)}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("測試總結")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！大小分預測系統已準備就緒")
    else:
        print("⚠️  部分測試失敗，請檢查相關功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
