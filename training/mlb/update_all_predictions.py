#!/usr/bin/env python3
"""
MLB預測結果更新工具 - 更新所有版本的預測結果

使用方法:
python update_all_predictions.py --date 2025-06-28    # 更新特定日期的所有預測結果
python update_all_predictions.py --yesterday          # 更新昨天的所有預測結果
"""

import argparse
import sys
from datetime import date, datetime, timedelta
from pathlib import Path

# 添加項目根目錄到Python路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app
from models.database import db, Prediction, Game, BoxScore

def update_prediction_results(target_date: date, app):
    """更新特定日期的所有預測結果（所有版本）"""
    print(f"\n🔄 更新 {target_date} 的所有預測結果...")
    
    with app.app_context():
        # 獲取該日期的所有預測（不限制版本）
        predictions = Prediction.query.join(Game).filter(
            Game.date >= target_date,
            Game.date < target_date + timedelta(days=1)
        ).all()
        
        if not predictions:
            print(f"❌ {target_date} 沒有找到預測記錄")
            return
        
        print(f"📊 找到 {len(predictions)} 個預測記錄")
        
        updated_count = 0
        
        for prediction in predictions:
            try:
                game = Game.query.filter_by(game_id=prediction.game_id).first()
                if not game:
                    print(f"⚠️  找不到遊戲記錄: {prediction.game_id}")
                    continue
                
                # 查找對應的box score（主隊和客隊）
                home_box_score = BoxScore.query.filter_by(game_id=prediction.game_id, is_home=True).first()
                away_box_score = BoxScore.query.filter_by(game_id=prediction.game_id, is_home=False).first()

                if not home_box_score or not away_box_score:
                    print(f"⚠️  找不到比賽結果: {game.away_team}@{game.home_team}")
                    continue

                # 檢查是否已經更新過
                if prediction.actual_home_score is not None and prediction.actual_away_score is not None:
                    print(f"✓ 已更新: {game.away_team}@{game.home_team}")
                    continue

                # 更新實際得分
                prediction.actual_home_score = home_box_score.runs
                prediction.actual_away_score = away_box_score.runs
                prediction.actual_total_runs = (home_box_score.runs or 0) + (away_box_score.runs or 0)
                
                # 計算勝負預測是否正確
                if prediction.predicted_home_score is not None and prediction.predicted_away_score is not None:
                    predicted_home_wins = prediction.predicted_home_score > prediction.predicted_away_score
                    actual_home_wins = home_box_score.runs > away_box_score.runs
                    prediction.is_correct = predicted_home_wins == actual_home_wins

                    # 計算得分差異
                    home_diff = abs((prediction.predicted_home_score or 0) - (home_box_score.runs or 0))
                    away_diff = abs((prediction.predicted_away_score or 0) - (away_box_score.runs or 0))
                    prediction.score_difference = (home_diff + away_diff) / 2
                
                # 計算總分預測是否正確（如果有大小分線）
                if hasattr(prediction, 'over_under_line') and prediction.over_under_line:
                    actual_total = prediction.actual_total_runs
                    if hasattr(prediction, 'over_under_prediction') and prediction.over_under_prediction:
                        if prediction.over_under_prediction.lower() == 'over':
                            prediction.over_under_correct = actual_total > prediction.over_under_line
                        elif prediction.over_under_prediction.lower() == 'under':
                            prediction.over_under_correct = actual_total < prediction.over_under_line
                
                # 計算總分差異
                if prediction.predicted_total_runs is not None:
                    prediction.total_runs_difference = abs(prediction.predicted_total_runs - prediction.actual_total_runs)
                
                # 更新時間戳
                prediction.updated_at = datetime.now()
                
                updated_count += 1
                print(f"✅ 更新: {game.away_team}@{game.home_team} - 實際: {away_box_score.runs}-{home_box_score.runs}")
                
            except Exception as e:
                print(f"❌ 更新失敗 {prediction.game_id}: {e}")
                continue
        
        # 提交更改
        try:
            db.session.commit()
            print(f"\n✅ 成功更新 {updated_count} 個預測結果")
            
            # 顯示統計
            total_predictions = len(predictions)
            correct_predictions = sum(1 for p in predictions if p.is_correct is True)
            completed_predictions = sum(1 for p in predictions if p.is_correct is not None)
            accuracy_rate = (correct_predictions / completed_predictions * 100) if completed_predictions > 0 else 0
            
            print(f"\n📈 更新後統計:")
            print(f"   總預測數: {total_predictions}")
            print(f"   已完成: {completed_predictions}")
            print(f"   正確預測: {correct_predictions}")
            print(f"   準確率: {accuracy_rate:.1f}%")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 提交更改失敗: {e}")

def main():
    parser = argparse.ArgumentParser(description='MLB預測結果更新工具')
    parser.add_argument('--date', type=str, help='更新日期 (YYYY-MM-DD)')
    parser.add_argument('--yesterday', action='store_true', help='更新昨天的結果')
    
    args = parser.parse_args()
    
    # 確定目標日期
    if args.yesterday:
        target_date = date.today() - timedelta(days=1)
    elif args.date:
        try:
            target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
        except ValueError:
            print("❌ 日期格式錯誤，請使用 YYYY-MM-DD 格式")
            return
    else:
        print("❌ 請指定 --date 或 --yesterday 參數")
        parser.print_help()
        return
    
    # 創建Flask應用
    app = create_app()
    
    try:
        update_prediction_results(target_date, app)
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
