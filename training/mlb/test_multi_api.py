#!/usr/bin/env python3
"""
測試多 API 博彩賠率獲取系統
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import json
from datetime import date, datetime
from models.enhanced_odds_fetcher import EnhancedOddsFetcher

def print_separator(title: str):
    """打印分隔線"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_api_status(status_report: dict):
    """打印 API 狀態報告"""
    print("\n📊 API 狀態報告:")
    print("-" * 40)
    
    # oddsapi 包狀態
    oddsapi_info = status_report.get('oddsapi_package', {})
    print(f"📦 oddsapi 包:")
    print(f"   可用: {'✅' if oddsapi_info.get('available') else '❌'}")
    print(f"   已初始化: {'✅' if oddsapi_info.get('client_initialized') else '❌'}")
    
    # 多 API 管理器狀態
    multi_api = status_report.get('multi_api_manager', {})
    print(f"\n🔄 多 API 管理器:")
    for api_name, api_info in multi_api.items():
        status_icon = {
            'active': '✅',
            'rate_limited': '⏰',
            'error': '❌',
            'disabled': '🚫'
        }.get(api_info.get('status'), '❓')
        
        print(f"   {api_name}: {status_icon} {api_info.get('status')}")
        print(f"      優先級: {api_info.get('priority')}")
        print(f"      請求數: {api_info.get('requests_count')}/{api_info.get('rate_limit')}")
        if api_info.get('last_error'):
            print(f"      最後錯誤: {api_info.get('last_error')}")
    
    # 備用獲取器狀態
    fallback = status_report.get('fallback_fetcher', {})
    print(f"\n🔙 備用獲取器:")
    print(f"   可用: {'✅' if fallback.get('available') else '❌'}")
    print(f"   類型: {fallback.get('type', 'N/A')}")

def print_odds_data(odds_data: dict):
    """打印賠率數據摘要"""
    if not odds_data.get('success'):
        print(f"❌ 獲取失敗: {odds_data.get('error', '未知錯誤')}")
        return
    
    print(f"✅ 數據來源: {odds_data.get('source', 'N/A')}")
    print(f"📅 時間戳: {odds_data.get('timestamp', 'N/A')}")
    
    # 處理不同的數據格式
    games = odds_data.get('games', [])
    if games:
        print(f"🎮 比賽數量: {len(games)}")
        
        # 顯示前幾場比賽的摘要
        for i, game in enumerate(games[:3]):
            print(f"\n   比賽 {i+1}:")
            print(f"      主隊: {game.get('home_team', 'N/A')}")
            print(f"      客隊: {game.get('away_team', 'N/A')}")
            print(f"      開始時間: {game.get('commence_time', 'N/A')}")
            
            bookmakers = game.get('bookmakers', [])
            if bookmakers:
                print(f"      博彩公司數: {len(bookmakers)}")
                
                # 顯示第一個博彩公司的賠率
                first_bookmaker = bookmakers[0]
                print(f"      示例賠率 ({first_bookmaker.get('title', 'N/A')}):")
                
                markets = first_bookmaker.get('markets', [])
                for market in markets[:2]:  # 只顯示前兩個市場
                    market_key = market.get('key', 'N/A')
                    outcomes = market.get('outcomes', [])
                    print(f"         {market_key}: {len(outcomes)} 個選項")
        
        if len(games) > 3:
            print(f"   ... 還有 {len(games) - 3} 場比賽")
    
    # 顯示 API 使用統計
    rate_info = odds_data.get('rate_info', {})
    if rate_info:
        print(f"\n📈 API 使用統計:")
        print(f"   剩餘請求: {rate_info.get('requests_remaining', 'N/A')}")
        print(f"   已使用請求: {rate_info.get('requests_used', 'N/A')}")

async def test_async_fetch(fetcher: EnhancedOddsFetcher):
    """測試異步獲取"""
    print_separator("測試異步獲取賠率")
    
    try:
        start_time = datetime.now()
        result = await fetcher.fetch_odds_async()
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        print(f"⏱️  異步獲取耗時: {duration:.2f} 秒")
        
        if result.get('success'):
            print("✅ 異步獲取成功")
            markets = result.get('markets', {})
            print(f"📊 獲取到 {len(markets)} 個市場的數據")
            for market_name in markets.keys():
                print(f"   - {market_name}")
        else:
            print(f"❌ 異步獲取失敗: {result.get('error', '未知錯誤')}")
            
    except Exception as e:
        print(f"❌ 異步測試失敗: {e}")

def test_installation_guide(fetcher: EnhancedOddsFetcher):
    """測試安裝指導"""
    print_separator("oddsapi 包安裝指導")
    
    guide = fetcher.install_oddsapi_package()
    print(guide.get('message', ''))
    
    print("\n💻 安裝命令:")
    for cmd in guide.get('commands', []):
        if cmd.startswith('#'):
            print(f"   {cmd}")
        else:
            print(f"   $ {cmd}")
    
    print(f"\n📚 文檔: {guide.get('documentation', 'N/A')}")
    print(f"🔑 API 密鑰設置: {guide.get('api_key_setup', 'N/A')}")

def main():
    """主測試函數"""
    print_separator("多 API 博彩賠率獲取系統測試")
    
    # 初始化增強版獲取器
    print("🚀 初始化增強版賠率獲取器...")
    fetcher = EnhancedOddsFetcher()
    
    # 1. 檢查 API 狀態
    print_separator("API 狀態檢查")
    status_report = fetcher.get_api_status_report()
    print_api_status(status_report)
    
    # 2. 測試同步獲取
    print_separator("測試同步獲取賠率")
    print("📡 開始獲取今日 MLB 賠率...")
    
    start_time = datetime.now()
    odds_data = fetcher.get_mlb_odds_today()
    end_time = datetime.now()
    
    duration = (end_time - start_time).total_seconds()
    print(f"⏱️  同步獲取耗時: {duration:.2f} 秒")
    
    print_odds_data(odds_data)
    
    # 3. 測試異步獲取 (如果支援)
    try:
        asyncio.run(test_async_fetch(fetcher))
    except Exception as e:
        print(f"\n⚠️  異步測試跳過: {e}")
    
    # 4. 顯示安裝指導
    test_installation_guide(fetcher)
    
    # 5. 最終狀態檢查
    print_separator("最終 API 狀態")
    final_status = fetcher.get_api_status_report()
    print_api_status(final_status)
    
    print_separator("測試完成")
    print("🎉 多 API 系統測試完成！")
    
    # 提供使用建議
    print("\n💡 使用建議:")
    print("1. 如果 oddsapi 包未安裝，請按照上述指導安裝")
    print("2. 確保在 models/odds-api.txt 中設置了有效的 API 密鑰")
    print("3. 可以在 models/rapidapi-key.txt 中添加 RapidAPI 密鑰作為備用")
    print("4. 系統會自動在多個 API 之間切換，確保數據獲取的穩定性")

if __name__ == "__main__":
    main()
