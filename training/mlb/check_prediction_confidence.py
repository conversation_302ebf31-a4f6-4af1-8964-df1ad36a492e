#!/usr/bin/env python3
"""
檢查現有預測的信心度
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game, Prediction

def check_prediction_confidence():
    """檢查預測信心度"""
    app = create_app()
    
    with app.app_context():
        target_date = date(2025, 7, 12)
        
        print(f"🔍 檢查 {target_date} 的預測信心度...")
        
        games = Game.query.filter(Game.date == target_date).all()
        
        for game in games:
            prediction = Prediction.query.filter_by(game_id=game.game_id).first()
            
            if prediction:
                print(f"📊 {game.away_team} @ {game.home_team}:")
                print(f"  預測: {prediction.predicted_away_score:.1f} - {prediction.predicted_home_score:.1f}")
                print(f"  實際: {game.away_score} - {game.home_score}")
                print(f"  信心度: {prediction.confidence:.3f}")
                print(f"  模型版本: {prediction.model_version}")
                print(f"  創建時間: {prediction.created_at}")
                print()

if __name__ == "__main__":
    check_prediction_confidence()
