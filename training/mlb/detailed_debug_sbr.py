#!/usr/bin/env python3
"""
詳細調試SportsbookReview的JSON結構
"""

import requests
import json
import re
from pprint import pprint

def analyze_json_structure():
    """詳細分析JSON數據結構"""
    url = 'https://www.sportsbookreview.com/betting-odds/mlb-baseball/pointspread/full-game/?date=2025-07-10'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    response = requests.get(url, headers=headers, timeout=30)
    html_content = response.text
    
    # 提取Next.js數據
    next_data_pattern = r'<script id="__NEXT_DATA__" type="application/json">(.*?)</script>'
    match = re.search(next_data_pattern, html_content, re.DOTALL)
    
    if match:
        try:
            data = json.loads(match.group(1))
            
            # 導航到賠率數據
            odds_tables = data['props']['pageProps']['oddsTables']
            first_table = odds_tables[0]
            game_rows = first_table['oddsTableModel']['gameRows']
            
            print(f"🎮 總共 {len(game_rows)} 場比賽")
            print()
            
            # 分析前3場比賽的結構
            for i, game_row in enumerate(game_rows[:3], 1):
                print(f"📊 比賽 {i} 數據結構分析:")
                print(f"   頂層鍵: {list(game_row.keys())}")
                
                # gameView 分析
                if 'gameView' in game_row:
                    game_view = game_row['gameView']
                    print(f"   🎯 gameView 鍵: {list(game_view.keys())}")
                    
                    # 提取隊伍信息（基於實際結構）
                    away_team = game_view.get('awayTeam', 'N/A')
                    home_team = game_view.get('homeTeam', 'N/A') 
                    away_starter = game_view.get('awayStarter', 'N/A')
                    home_starter = game_view.get('homeStarter', 'N/A')
                    
                    print(f"   🏟️ 比賽: {away_team} @ {home_team}")
                    print(f"   ⚾ 投手: {away_starter} vs {home_starter}")
                    print(f"   📅 開始日期: {game_view.get('startDate', 'N/A')}")
                    print(f"   🏟️ 場地: {game_view.get('venueName', 'N/A')}")
                
                # oddsViews 分析
                if 'oddsViews' in game_row:
                    odds_views = game_row['oddsViews']
                    print(f"   💰 賠率視圖數量: {len(odds_views)}")
                    
                    for j, odds_view in enumerate(odds_views[:2]):  # 只看前2個
                        print(f"      賠率視圖 {j+1}: {list(odds_view.keys())}")
                        
                        if 'sportsbook' in odds_view:
                            sportsbook = odds_view['sportsbook']
                            book_name = sportsbook.get('name', 'N/A') if isinstance(sportsbook, dict) else str(sportsbook)
                            print(f"        博彩商: {book_name}")
                        
                        # 查看賠率數據結構（基於實際結構）
                        if 'currentLine' in odds_view:
                            current_line = odds_view['currentLine']
                            print(f"        當前盤口鍵: {list(current_line.keys()) if isinstance(current_line, dict) else current_line}")
                            
                            # 查看讓分盤
                            if isinstance(current_line, dict) and 'spread' in current_line:
                                spread = current_line['spread']
                                print(f"        讓分盤類型: {type(spread)}")
                                if isinstance(spread, dict):
                                    print(f"        讓分盤鍵: {list(spread.keys())}")
                                    print(f"        讓分盤內容: {spread}")
                                else:
                                    print(f"        讓分盤: {spread}")
                        
                        if 'spreadHistory' in odds_view:
                            spread_history = odds_view['spreadHistory']
                            print(f"        讓分歷史數量: {len(spread_history) if spread_history else 0}")
                            if spread_history and len(spread_history) > 0:
                                latest = spread_history[0]  # 最新賠率
                                print(f"        最新讓分類型: {type(latest)}")
                                if isinstance(latest, dict):
                                    print(f"        最新讓分鍵: {list(latest.keys())}")
                                    print(f"        最新讓分內容: {latest}")
                                else:
                                    print(f"        最新讓分: {latest}")
                
                print("-" * 60)
            
            # 輸出完整的第一場比賽JSON（便於分析）
            print("\n📋 完整第一場比賽JSON結構:")
            print(json.dumps(game_rows[0], indent=2, ensure_ascii=False)[:2000] + "..." if len(json.dumps(game_rows[0])) > 2000 else json.dumps(game_rows[0], indent=2, ensure_ascii=False))
            
        except Exception as e:
            print(f"❌ 解析失敗: {e}")
    else:
        print("❌ 未找到Next.js數據")

if __name__ == "__main__":
    analyze_json_structure()