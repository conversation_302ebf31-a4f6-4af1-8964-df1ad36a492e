#!/usr/bin/env python3
"""
模型比較功能測試腳本
測試優化後的模型比較API性能
"""

import requests
import time
import json
from datetime import datetime

def test_model_comparison():
    """測試模型比較API"""
    print("🔬 測試模型比較功能")
    print("=" * 50)
    
    # 測試URL
    base_url = "http://127.0.0.1:5500"
    api_url = f"{base_url}/simulation/api/compare_models"
    
    # 測試日期
    test_dates = [
        "2025-06-30",  # 用戶關心的日期
        "2025-06-28",  # 有預測的日期
        "2025-06-29"   # 可能沒有預測的日期
    ]
    
    for date_str in test_dates:
        print(f"\n📅 測試日期: {date_str}")
        
        # 記錄開始時間
        start_time = time.time()
        
        try:
            # 發送API請求
            response = requests.post(
                api_url,
                json={"date": date_str},
                headers={"Content-Type": "application/json"},
                timeout=30  # 30秒超時
            )
            
            # 記錄結束時間
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"⏱️  執行時間: {duration:.2f} 秒")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    comparison = data.get('comparison', {})
                    performance = comparison.get('performance', {})
                    
                    print(f"✅ 比較成功")
                    print(f"   總比賽: {comparison.get('total_games', 0)}")
                    print(f"   模型版本: {len(performance)}")
                    
                    # 顯示各版本表現
                    for version, stats in performance.items():
                        accuracy = stats.get('accuracy', 0)
                        total = stats.get('total_predictions', 0)
                        evaluated = stats.get('evaluated_games', 0)
                        print(f"   {version}: {total}個預測, {evaluated}個評估, {accuracy:.1f}%準確率")
                        
                else:
                    print(f"❌ 比較失敗: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP錯誤: {response.status_code}")
                print(f"   響應: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ 請求超時 (>30秒)")
        except requests.exceptions.RequestException as e:
            print(f"❌ 網絡錯誤: {e}")
        except Exception as e:
            print(f"❌ 未知錯誤: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 模型比較測試完成")

def test_enhanced_v1_performance():
    """專門測試增強版 v1.0 的性能"""
    print("\n🚀 增強版 v1.0 性能測試")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5500"
    test_date = "2025-06-30"  # 用戶測試的日期
    
    print(f"📊 測試增強版 v1.0 在 {test_date} 的表現")
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{base_url}/simulation/api/compare_models",
            json={"date": test_date},
            headers={"Content-Type": "application/json"},
            timeout=60  # 給增強版更多時間
        )
        
        duration = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 增強版測試: {duration:.2f} 秒")
            
            if data.get('success'):
                comparison = data.get('comparison', {})
                performance = comparison.get('performance', {})
                
                # 專門查看增強版表現
                enhanced_stats = performance.get('enhanced_v1.0')
                if enhanced_stats:
                    print(f"🎯 增強版 v1.0 結果:")
                    print(f"   總預測: {enhanced_stats.get('total_predictions', 0)}")
                    print(f"   已評估: {enhanced_stats.get('evaluated_games', 0)}")
                    print(f"   正確數: {enhanced_stats.get('correct_predictions', 0)}")
                    print(f"   準確率: {enhanced_stats.get('accuracy', 0):.1f}%")
                else:
                    print(f"⚠️  沒有找到增強版 v1.0 的預測記錄")
                    print(f"   可用版本: {list(performance.keys())}")
            else:
                print(f"   錯誤: {data.get('error')}")
        else:
            print(f"❌ 增強版測試失敗: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 增強版測試錯誤: {e}")
    
    print(f"\n📈 性能總結:")
    print(f"   增強版 v1.0: {duration:.2f} 秒")
    
    if duration < 5.0:
        print(f"🎉 性能優秀! (< 5秒)")
    elif duration < 15.0:
        print(f"✅ 性能良好 (< 15秒)")
    elif duration < 30.0:
        print(f"⚠️  性能可接受 (< 30秒)")
    else:
        print(f"❌ 性能需要改進 (> 30秒)")

def benchmark_comparison():
    """基準測試 - 比較優化前後的性能"""
    print("\n🏁 基準性能測試")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5500"
    test_dates = ["2025-06-30", "2025-06-28"]
    
    total_time = 0
    successful_tests = 0
    
    for date_str in test_dates:
        print(f"📊 基準測試: {date_str}")
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{base_url}/simulation/api/compare_models",
                json={"date": date_str},
                headers={"Content-Type": "application/json"},
                timeout=45
            )
            
            duration = time.time() - start_time
            total_time += duration
            
            if response.status_code == 200:
                successful_tests += 1
                print(f"   ✅ {duration:.2f} 秒")
            else:
                print(f"   ❌ 失敗 ({response.status_code})")
                
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")
    
    if successful_tests > 0:
        avg_time = total_time / successful_tests
        print(f"\n📊 基準結果:")
        print(f"   成功測試: {successful_tests}/{len(test_dates)}")
        print(f"   平均時間: {avg_time:.2f} 秒")
        print(f"   總時間: {total_time:.2f} 秒")
        
        if avg_time < 3.0:
            print(f"🏆 優化效果: 優秀")
        elif avg_time < 10.0:
            print(f"✅ 優化效果: 良好")
        else:
            print(f"⚠️  優化效果: 需要進一步改進")

if __name__ == "__main__":
    print("🧪 模型比較功能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 執行測試
    test_model_comparison()
    test_enhanced_v1_performance()
    benchmark_comparison()
    
    print(f"\n✨ 測試完成!")
    print("💡 如果增強版 v1.0 仍然很慢，可能需要進一步優化特徵工程邏輯")
