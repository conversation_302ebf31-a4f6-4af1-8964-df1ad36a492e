#!/usr/bin/env python3
"""
調試投手姓名格式，找出匹配失敗的原因
"""

import logging
from app import create_app
from models.database import db, Game, GameDetail, PlayerStats

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_pitcher_names():
    """調試投手姓名格式"""
    app = create_app()
    
    with app.app_context():
        print("=" * 80)
        print("🔍 投手姓名格式調試")
        print("=" * 80)
        
        # 1. 檢查先發投手姓名格式
        print("\n⚾ 先發投手姓名樣本:")
        print("-" * 50)
        
        sample_games = db.session.query(Game, GameDetail).join(
            GameDetail, Game.game_id == GameDetail.game_id
        ).filter(
            GameDetail.home_starting_pitcher.isnot(None),
            GameDetail.away_starting_pitcher.isnot(None)
        ).limit(10).all()
        
        starting_pitcher_names = set()
        for game, detail in sample_games:
            if detail.home_starting_pitcher:
                starting_pitcher_names.add(detail.home_starting_pitcher)
            if detail.away_starting_pitcher:
                starting_pitcher_names.add(detail.away_starting_pitcher)
        
        print("先發投手姓名樣本:")
        for name in sorted(starting_pitcher_names):
            print(f"  '{name}'")
        
        # 2. 檢查PlayerStats中的投手姓名格式
        print(f"\n🏏 PlayerStats投手姓名樣本:")
        print("-" * 50)
        
        pitcher_stats = PlayerStats.query.filter(
            PlayerStats.innings_pitched > 0,
            PlayerStats.season == 2024
        ).limit(20).all()
        
        print("PlayerStats投手姓名樣本:")
        for stats in pitcher_stats:
            print(f"  '{stats.player_name}' - ERA: {stats.era:.2f}, IP: {stats.innings_pitched:.1f}")
        
        # 3. 嘗試手動匹配幾個失敗的案例
        print(f"\n🔍 手動匹配測試:")
        print("-" * 50)
        
        test_names = ['Michael Pineda', 'Joe Musgrove', 'Nick Pivetta', 'Spencer Turnbull']
        
        for test_name in test_names:
            print(f"\n測試投手: '{test_name}'")
            
            # 精確匹配
            exact_match = PlayerStats.query.filter(
                PlayerStats.player_name == test_name,
                PlayerStats.innings_pitched > 0
            ).first()
            
            if exact_match:
                print(f"  ✅ 精確匹配: {exact_match.player_name}")
                continue
            
            # 模糊匹配
            fuzzy_matches = PlayerStats.query.filter(
                PlayerStats.player_name.like(f'%{test_name.split()[0]}%'),
                PlayerStats.innings_pitched > 0
            ).all()
            
            if fuzzy_matches:
                print(f"  🔍 名字模糊匹配:")
                for match in fuzzy_matches[:3]:
                    print(f"    - {match.player_name}")
            
            # 姓氏匹配
            last_name = test_name.split()[-1]
            last_name_matches = PlayerStats.query.filter(
                PlayerStats.player_name.like(f'%{last_name}%'),
                PlayerStats.innings_pitched > 0
            ).all()
            
            if last_name_matches:
                print(f"  🔍 姓氏模糊匹配 ('{last_name}'):")
                for match in last_name_matches[:3]:
                    print(f"    - {match.player_name}")
            
            if not fuzzy_matches and not last_name_matches:
                print(f"  ❌ 無任何匹配")
        
        # 4. 檢查賽季分布
        print(f"\n📅 投手統計賽季分布:")
        print("-" * 50)
        
        season_stats = db.session.query(
            PlayerStats.season,
            db.func.count(PlayerStats.id)
        ).filter(
            PlayerStats.innings_pitched > 0
        ).group_by(PlayerStats.season).order_by(PlayerStats.season.desc()).all()
        
        for season, count in season_stats:
            print(f"  {season}賽季: {count}名投手")
        
        # 5. 檢查是否有重複或格式問題
        print(f"\n🔄 姓名格式分析:")
        print("-" * 50)
        
        # 檢查包含特殊字符的姓名
        special_char_names = PlayerStats.query.filter(
            PlayerStats.player_name.like('%Jr.%'),
            PlayerStats.innings_pitched > 0
        ).limit(5).all()
        
        if special_char_names:
            print("包含 'Jr.' 的姓名:")
            for stats in special_char_names:
                print(f"  '{stats.player_name}'")
        
        # 檢查包含中間名的姓名
        middle_name_stats = PlayerStats.query.filter(
            db.func.length(PlayerStats.player_name) > 15,
            PlayerStats.innings_pitched > 0
        ).limit(5).all()
        
        if middle_name_stats:
            print("\n較長的姓名 (可能包含中間名):")
            for stats in middle_name_stats:
                print(f"  '{stats.player_name}' (長度: {len(stats.player_name)})")
        
        print("\n" + "=" * 80)

def main():
    """主函數"""
    debug_pitcher_names()

if __name__ == "__main__":
    main()
