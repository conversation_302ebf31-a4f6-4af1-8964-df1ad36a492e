#!/usr/bin/env python3
"""
測試禁用模擬數據後的系統行為
確保不再生成任何estimated、Simulated數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime
from app import create_app
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from models.spread_odds_generator import SpreadOddsGenerator
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_real_betting_odds_fetcher():
    """測試真實博彩數據獲取器"""
    print("🧪 測試真實博彩數據獲取器...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        # 測試獲取今日數據
        test_date = date(2025, 7, 8)  # 使用一個測試日期
        
        print(f"📅 測試日期: {test_date}")
        
        # 獲取博彩數據
        result = fetcher.get_mlb_odds_today(test_date)
        
        print(f"📊 結果摘要:")
        print(f"   - 成功: {result.get('summary', {}).get('total_games', 0) > 0}")
        print(f"   - 比賽數量: {result.get('summary', {}).get('total_games', 0)}")
        print(f"   - 有賠率比賽: {result.get('summary', {}).get('games_with_odds', 0)}")
        print(f"   - 數據源: {result.get('summary', {}).get('data_source', 'Unknown')}")
        
        # 檢查是否有模擬數據
        has_simulated = False
        for game in result.get('games', []):
            odds = game.get('odds', {})
            if odds.get('is_simulated'):
                has_simulated = True
                break
            
            # 檢查各個市場的bookmaker
            for market in ['moneyline', 'run_line', 'total']:
                market_data = odds.get(market, {})
                if isinstance(market_data, dict):
                    bookmaker = market_data.get('bookmaker', '')
                    if bookmaker in ['Simulated', 'estimated']:
                        has_simulated = True
                        print(f"⚠️  發現模擬數據: {market} - {bookmaker}")
        
        if has_simulated:
            print("❌ 仍有模擬數據生成")
        else:
            print("✅ 沒有模擬數據生成")
        
        return not has_simulated

def test_spread_odds_generator():
    """測試讓分盤生成器"""
    print("\n🧪 測試讓分盤生成器...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        generator = SpreadOddsGenerator(app)
        
        # 測試生成讓分盤
        test_date = date(2025, 7, 8)
        
        print(f"📅 測試日期: {test_date}")
        
        result = generator.generate_missing_spreads(test_date)
        
        print(f"📊 結果:")
        print(f"   - 成功: {result.get('success', False)}")
        print(f"   - 消息: {result.get('message', 'N/A')}")
        print(f"   - 生成數量: {result.get('generated_count', 0)}")
        
        # 檢查是否被正確禁用
        if result.get('success') == False and '禁用' in result.get('message', ''):
            print("✅ 讓分盤生成器已正確禁用")
            return True
        else:
            print("❌ 讓分盤生成器未正確禁用")
            return False

def test_database_for_simulated_data():
    """檢查數據庫中是否還有模擬數據"""
    print("\n🧪 檢查數據庫中的模擬數據...")
    print("=" * 60)
    
    import sqlite3
    
    conn = sqlite3.connect('instance/mlb_data.db')
    cursor = conn.cursor()
    
    try:
        # 檢查是否還有模擬數據
        cursor.execute("""
            SELECT COUNT(*) 
            FROM betting_odds 
            WHERE bookmaker IN ('estimated', 'Simulated', 'realistic_estimate')
        """)
        
        simulated_count = cursor.fetchone()[0]
        
        print(f"📊 數據庫中的模擬數據數量: {simulated_count}")
        
        if simulated_count == 0:
            print("✅ 數據庫中沒有模擬數據")
            return True
        else:
            print("❌ 數據庫中仍有模擬數據")
            
            # 顯示詳細信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as count,
                    market_type,
                    bookmaker
                FROM betting_odds 
                WHERE bookmaker IN ('estimated', 'Simulated', 'realistic_estimate')
                GROUP BY market_type, bookmaker
                ORDER BY market_type, bookmaker
            """)
            
            details = cursor.fetchall()
            print("詳細分布:")
            for count, market_type, bookmaker in details:
                print(f"   {market_type:^8} | {bookmaker:^18} | {count:^5}")
            
            return False
    
    finally:
        conn.close()

def main():
    """主測試函數"""
    print("🧪 MLB系統模擬數據禁用測試")
    print("=" * 80)
    
    tests_passed = 0
    total_tests = 3
    
    # 測試1: 真實博彩數據獲取器
    if test_real_betting_odds_fetcher():
        tests_passed += 1
    
    # 測試2: 讓分盤生成器
    if test_spread_odds_generator():
        tests_passed += 1
    
    # 測試3: 數據庫檢查
    if test_database_for_simulated_data():
        tests_passed += 1
    
    # 總結
    print("\n" + "=" * 80)
    print(f"🎯 測試總結: {tests_passed}/{total_tests} 通過")
    
    if tests_passed == total_tests:
        print("🎉 所有測試通過！模擬數據已成功禁用")
        print("\n✅ 系統現在只會使用真實博彩數據")
        print("✅ 不會再生成任何estimated、Simulated數據")
    else:
        print("⚠️  部分測試失敗，請檢查相關配置")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
