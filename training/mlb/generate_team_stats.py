#!/usr/bin/env python3
"""
生成TeamStats數據
解決模型訓練不充分的根本問題
"""

import sys
import os
from datetime import date, timedelta
from collections import defaultdict

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, TeamStats, Team

def calculate_team_stats():
    """計算球隊統計數據"""
    print("📊 計算球隊統計數據...")
    
    # 獲取最近90天的比賽數據
    end_date = date.today()
    start_date = end_date - timedelta(days=90)
    
    games = Game.query.filter(
        Game.date >= start_date,
        Game.date < end_date,
        Game.home_score.isnot(None),
        Game.away_score.isnot(None),
        Game.game_status == 'completed'
    ).all()
    
    print(f"分析 {len(games)} 場比賽")
    
    # 初始化統計字典
    team_stats = defaultdict(lambda: {
        'games': 0,
        'wins': 0,
        'losses': 0,
        'home_games': 0,
        'home_wins': 0,
        'home_losses': 0,
        'away_games': 0,
        'away_wins': 0,
        'away_losses': 0,
        'runs_scored': 0,
        'runs_allowed': 0,
        'hits': 0,
        'hits_allowed': 0,
        'errors': 0,
    })
    
    # 處理每場比賽
    for game in games:
        home_team = game.home_team
        away_team = game.away_team
        home_score = game.home_score
        away_score = game.away_score
        
        # 主隊統計
        team_stats[home_team]['games'] += 1
        team_stats[home_team]['home_games'] += 1
        team_stats[home_team]['runs_scored'] += home_score
        team_stats[home_team]['runs_allowed'] += away_score
        
        if home_score > away_score:
            team_stats[home_team]['wins'] += 1
            team_stats[home_team]['home_wins'] += 1
        else:
            team_stats[home_team]['losses'] += 1
            team_stats[home_team]['home_losses'] += 1
        
        # 客隊統計
        team_stats[away_team]['games'] += 1
        team_stats[away_team]['away_games'] += 1
        team_stats[away_team]['runs_scored'] += away_score
        team_stats[away_team]['runs_allowed'] += home_score
        
        if away_score > home_score:
            team_stats[away_team]['wins'] += 1
            team_stats[away_team]['away_wins'] += 1
        else:
            team_stats[away_team]['losses'] += 1
            team_stats[away_team]['away_losses'] += 1
    
    return team_stats

def save_team_stats(team_stats_data):
    """保存球隊統計到數據庫"""
    print("💾 保存球隊統計數據...")
    
    # 清空現有數據
    TeamStats.query.delete()
    
    saved_count = 0
    
    for team_id, stats in team_stats_data.items():
        try:
            # 🔧 修復: 獲取正確的數字team_id
            team_obj = Team.query.filter_by(team_code=team_id).first()
            if not team_obj:
                print(f"找不到球隊: {team_id}")
                continue

            actual_team_id = team_obj.team_id  # 使用數字ID

            # 計算衍生統計
            games = stats['games']
            if games > 0:
                batting_avg = min(0.400, max(0.200, 0.250 + (stats['runs_scored'] - stats['runs_allowed']) / games / 20))
                era = max(2.00, min(6.00, 4.50 - (stats['runs_scored'] - stats['runs_allowed']) / games / 2))
                win_pct = stats['wins'] / games
            else:
                batting_avg = 0.250
                era = 4.50
                win_pct = 0.500

            # 創建TeamStats記錄
            team_stat = TeamStats(
                team_id=actual_team_id,  # 使用數字ID
                season=2025,  # 添加必需的season字段
                wins=stats['wins'],
                losses=stats['losses'],
                win_percentage=win_pct,
                runs_scored=stats['runs_scored'],
                runs_allowed=stats['runs_allowed'],
                batting_avg=batting_avg,
                era=era,
                home_wins=stats['home_wins'],
                home_losses=stats['home_losses'],
                away_wins=stats['away_wins'],
                away_losses=stats['away_losses'],
                games_played=games
            )
            
            db.session.add(team_stat)
            saved_count += 1
            
            print(f"  {team_id}: {stats['wins']}-{stats['losses']} ({win_pct:.3f}), "
                  f"得分差: {stats['runs_scored'] - stats['runs_allowed']:+d}, "
                  f"打擊率: {batting_avg:.3f}, ERA: {era:.2f}")
        
        except Exception as e:
            print(f"保存 {team_id} 統計時出錯: {e}")
    
    try:
        db.session.commit()
        print(f"✅ 成功保存 {saved_count} 支球隊的統計數據")
        return True
    except Exception as e:
        print(f"❌ 提交數據庫時出錯: {e}")
        db.session.rollback()
        return False

def verify_team_stats():
    """驗證球隊統計數據"""
    print("🔍 驗證球隊統計數據...")
    
    team_stats_count = TeamStats.query.count()
    print(f"TeamStats 記錄數: {team_stats_count}")
    
    if team_stats_count > 0:
        # 顯示前5支球隊的統計
        top_teams = TeamStats.query.order_by(TeamStats.win_percentage.desc()).limit(5).all()
        print(f"\n🏆 勝率最高的5支球隊:")
        for i, team in enumerate(top_teams, 1):
            print(f"  {i}. {team.team_id}: {team.wins}-{team.losses} "
                  f"({team.win_percentage:.3f}), ERA: {team.era:.2f}")
        
        # 檢查是否能找到比賽對應的統計
        recent_game = Game.query.filter(
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).first()
        
        if recent_game:
            home_stats = TeamStats.query.filter_by(team_id=recent_game.home_team).first()
            away_stats = TeamStats.query.filter_by(team_id=recent_game.away_team).first()
            
            print(f"\n✅ 測試比賽: {recent_game.away_team} @ {recent_game.home_team}")
            print(f"  主隊統計: {'✅' if home_stats else '❌'}")
            print(f"  客隊統計: {'✅' if away_stats else '❌'}")
            
            return home_stats is not None and away_stats is not None
    
    return False

def main():
    """主函數"""
    print("🚀 生成TeamStats數據")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 計算球隊統計
            team_stats_data = calculate_team_stats()
            
            if not team_stats_data:
                print("❌ 沒有找到比賽數據")
                return
            
            print(f"計算了 {len(team_stats_data)} 支球隊的統計")
            
            # 2. 保存到數據庫
            success = save_team_stats(team_stats_data)
            
            if not success:
                print("❌ 保存統計數據失敗")
                return
            
            # 3. 驗證數據
            verification_success = verify_team_stats()
            
            if verification_success:
                print("\n🎉 TeamStats數據生成完成！")
                print("💡 現在可以重新訓練模型了")
                print("📝 建議執行: python train_simple_models.py")
            else:
                print("\n❌ 數據驗證失敗")
        
        except Exception as e:
            print(f"\n❌ 生成過程中出錯: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
