#!/usr/bin/env python3
"""
尋找並修復已完成的比賽結果
"""

from app import create_app
from models.database import db, Game
from datetime import date, timedelta
import logging
import statsapi as mlb

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_and_fix_completed_games():
    """尋找並修復已完成的比賽"""
    app = create_app()
    
    with app.app_context():
        print("🔍 尋找已完成但未更新的比賽...")
        print("=" * 60)
        
        # 檢查過去3天的比賽
        for days_ago in range(1, 4):
            test_date = date.today() - timedelta(days=days_ago)
            print(f"\n📅 檢查日期: {test_date}")
            
            # 獲取該日期狀態為 scheduled 但可能已完成的比賽
            games = Game.query.filter(
                Game.date == test_date,
                Game.game_status == 'scheduled'
            ).limit(5).all()  # 限制檢查數量
            
            print(f"📊 找到 {len(games)} 場待檢查比賽")
            
            updated_count = 0
            
            for game in games:
                try:
                    game_pk = game.game_id.split('_')[-1] if '_' in game.game_id else game.game_id
                    if not game_pk.isdigit():
                        continue
                    
                    print(f"   🎯 {game.away_team} @ {game.home_team} (PK: {game_pk})")
                    
                    # 獲取比賽資料
                    game_data = mlb.get('game', {'gamePk': game_pk})
                    if not game_data:
                        continue
                    
                    # 檢查狀態
                    status_data = game_data.get('gameData', {}).get('status', {})
                    detailed_state = status_data.get('detailedState', 'Unknown')
                    status_code = status_data.get('statusCode', 'S')
                    
                    print(f"      狀態: {detailed_state} ({status_code})")
                    
                    # 如果比賽已完成
                    if status_code in ['F', 'O']:  # Final, Over
                        line_score = game_data.get('liveData', {}).get('linescore', {})
                        if line_score and 'teams' in line_score:
                            teams = line_score['teams']
                            away_runs = teams.get('away', {}).get('runs')
                            home_runs = teams.get('home', {}).get('runs')
                            
                            if away_runs is not None and home_runs is not None:
                                print(f"      🎯 比分: {away_runs} - {home_runs}")
                                
                                # 更新資料庫
                                game.game_status = 'completed'
                                game.away_score = away_runs
                                game.home_score = home_runs
                                
                                db.session.add(game)
                                updated_count += 1
                                print(f"      ✅ 已更新")
                    else:
                        print(f"      ⏳ 尚未完成")
                        
                except Exception as e:
                    print(f"      ❌ 錯誤: {e}")
            
            if updated_count > 0:
                try:
                    db.session.commit()
                    print(f"✅ {test_date} 更新了 {updated_count} 場比賽")
                except Exception as e:
                    db.session.rollback()
                    print(f"❌ 資料庫更新失敗: {e}")
            else:
                print(f"📝 {test_date} 沒有需要更新的比賽")

if __name__ == "__main__":
    find_and_fix_completed_games()