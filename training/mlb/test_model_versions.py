#!/usr/bin/env python3
"""
測試不同模型版本的預測效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from enhanced_custom_predict import EnhancedCustomPredictor
import json

def test_model_versions():
    """測試不同模型版本"""
    predictor = EnhancedCustomPredictor()
    target_date = date(2025, 7, 12)
    
    # 定義測試版本
    test_versions = [
        {
            'name': 'improved_v1',
            'description': '基礎改進版本',
            'focus': '整體預測準確性提升'
        },
        {
            'name': 'boyd_suppression', 
            'description': 'Boyd壓制分析',
            'focus': '王牌投手壓制強打線效應'
        },
        {
            'name': 'pitcher_analysis',
            'description': '投手深度分析', 
            'focus': '投手表現深度分析'
        },
        {
            'name': 'ace_dominance',
            'description': '王牌主宰效應',
            'focus': '王牌投手主導比賽分析'
        },
        {
            'name': 'outlier_detection',
            'description': '意外情況檢測',
            'focus': '檢測和處理意外爆發/壓制'
        }
    ]
    
    print("🧪 測試不同模型版本的預測效果...")
    print("=" * 80)
    
    results_comparison = {}
    
    for version in test_versions:
        print(f"\n🔬 測試版本: {version['name']}")
        print(f"📝 描述: {version['description']}")
        print(f"🎯 重點: {version['focus']}")
        print("-" * 60)
        
        # 測試選項
        options = {
            'force_regenerate': False,  # 不強制重新生成，只更新
            'update_existing': True,
            'skip_existing': False,
            'backup_existing': True,
            'model_version_suffix': version['name'],
            'exclude_postponed': True,
            'confidence_threshold': 0.0
        }
        
        try:
            results = predictor.predict_with_options(target_date, options)
            
            if results['success']:
                print(f"✅ 成功處理: {results['processed_games']}/{results['total_games']} 場比賽")
                print(f"📊 更新預測: {results['updated_games']}")
                print(f"💾 備份預測: {results['backed_up_predictions']}")
                
                # 分析預測結果
                if results['predictions']:
                    # 重點分析 CHC @ NYY 和 SEA @ DET
                    key_games = []
                    for pred in results['predictions']:
                        if 'CHC @ NYY' in pred['matchup'] or 'SEA @ DET' in pred['matchup']:
                            key_games.append({
                                'matchup': pred['matchup'],
                                'predicted': f"{pred['prediction']['away_score']:.1f} - {pred['prediction']['home_score']:.1f}" if pred['prediction'] else 'N/A',
                                'actual': pred['actual_score'],
                                'confidence': pred['prediction']['confidence'] if pred['prediction'] else 0,
                                'action': pred['action']
                            })
                    
                    if key_games:
                        print(f"🎯 重點比賽分析:")
                        for game in key_games:
                            print(f"  {game['matchup']}: 預測 {game['predicted']}, 實際 {game['actual']}, 信心度 {game['confidence']:.3f}")
                    
                    results_comparison[version['name']] = {
                        'version_info': version,
                        'results': results,
                        'key_games': key_games
                    }
                
            else:
                print(f"❌ 測試失敗: {results.get('error', '未知錯誤')}")
                
        except Exception as e:
            print(f"❌ 版本測試異常: {e}")
    
    # 比較結果
    print("\n" + "=" * 80)
    print("📊 版本比較總結")
    print("=" * 80)
    
    if results_comparison:
        print(f"\n🎯 CHC @ NYY 比賽預測比較 (實際: 5-2):")
        print("-" * 60)
        for version_name, data in results_comparison.items():
            chc_nyy = next((g for g in data['key_games'] if 'CHC @ NYY' in g['matchup']), None)
            if chc_nyy:
                print(f"{version_name:20} | {chc_nyy['predicted']:12} | 信心度: {chc_nyy['confidence']:.3f}")
        
        print(f"\n🎯 SEA @ DET 比賽預測比較 (實際: 15-7):")
        print("-" * 60)
        for version_name, data in results_comparison.items():
            sea_det = next((g for g in data['key_games'] if 'SEA @ DET' in g['matchup']), None)
            if sea_det:
                print(f"{version_name:20} | {sea_det['predicted']:12} | 信心度: {sea_det['confidence']:.3f}")
    
    print(f"\n💡 建議:")
    print(f"  1. boyd_suppression 版本應該更準確預測 CHC @ NYY (Boyd 壓制 NYY)")
    print(f"  2. outlier_detection 版本應該檢測到 SEA @ DET 的異常高分")
    print(f"  3. ace_dominance 版本應該識別王牌投手的主導效應")
    print(f"  4. pitcher_analysis 版本應該提供更詳細的投手分析")

def show_available_versions():
    """顯示可用的模型版本"""
    print("📋 可用的模型版本:")
    print("=" * 50)
    
    versions = {
        "🎯 預測改進版本": [
            "improved_v1 - 基礎改進版本",
            "improved_v2 - 進階改進版本", 
            "improved_v3 - 最新改進版本"
        ],
        "⚾ 投手分析版本": [
            "pitcher_analysis - 投手深度分析",
            "ace_pitcher_v1 - 王牌投手特化",
            "pitcher_matchup - 投手對戰分析",
            "era_calibrated - ERA校正版本"
        ],
        "🛡️ 壓制效應版本": [
            "boyd_suppression - Boyd壓制分析",
            "lineup_suppression - 打線壓制分析", 
            "ace_dominance - 王牌主宰效應",
            "pitcher_dominance - 投手主導分析"
        ],
        "💥 意外情況版本": [
            "outlier_detection - 意外情況檢測",
            "explosion_analysis - 爆發分析",
            "upset_prediction - 冷門預測",
            "variance_adjusted - 變異調整"
        ],
        "📊 數據源版本": [
            "real_odds_only - 僅真實盤口",
            "historical_validation - 歷史驗證",
            "recent_form_focus - 近期狀態重點",
            "comprehensive_data - 綜合數據"
        ],
        "🧪 實驗版本": [
            "experimental_v1 - 實驗版本1",
            "experimental_v2 - 實驗版本2",
            "test_algorithm - 測試算法",
            "beta_features - Beta功能"
        ]
    }
    
    for category, version_list in versions.items():
        print(f"\n{category}:")
        for version in version_list:
            print(f"  • {version}")

if __name__ == "__main__":
    print("🚀 模型版本測試工具")
    print("1. 顯示可用版本")
    print("2. 測試版本效果")
    
    choice = input("\n請選擇 (1/2): ").strip()
    
    if choice == "1":
        show_available_versions()
    elif choice == "2":
        test_model_versions()
    else:
        print("無效選擇，顯示可用版本:")
        show_available_versions()
