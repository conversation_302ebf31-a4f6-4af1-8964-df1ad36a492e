#!/usr/bin/env python3
"""
測試賠率API的最終修復
驗證所有問題是否都已解決
"""

from app import create_app
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_final_odds_fix():
    """測試賠率API的最終修復"""
    print("🔍 測試賠率API的最終修復...")
    print("=" * 80)
    
    app = create_app()
    
    with app.test_client() as client:
        
        # 1. 測試基本查詢（更寬的日期範圍）
        print("\n📊 測試基本查詢...")
        query_data = {
            "date_from": "2025-07-01",  # 更寬的日期範圍
            "date_to": "2025-08-30",
            "limit": 20
        }
        
        response = client.post('/unified/api/betting_odds/query',
                             json=query_data,
                             content_type='application/json')
        
        print(f"   狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                if data.get('success'):
                    records = data.get('records', [])
                    stats = data.get('stats', {})
                    print(f"   ✅ 查詢成功，找到 {len(records)} 條記錄")
                    print(f"      統計總記錄: {stats.get('total_records', 0)}")
                    
                    # 分析記錄類型
                    linked_count = sum(1 for r in records if not r.get('is_orphaned'))
                    orphaned_count = sum(1 for r in records if r.get('is_orphaned'))
                    print(f"      關聯記錄: {linked_count}")
                    print(f"      孤立記錄: {orphaned_count}")
                    
                    # 顯示前幾條記錄
                    print("   📋 記錄詳情:")
                    for i, record in enumerate(records[:5]):
                        if record.get('is_orphaned'):
                            print(f"      {i+1}. [孤立] {record.get('matchup')} - {record.get('bookmaker')} ({record.get('date')})")
                        else:
                            print(f"      {i+1}. [關聯] {record.get('matchup')} - {record.get('bookmaker')} ({record.get('date')})")
                else:
                    print(f"   ❌ 查詢失敗: {data.get('error', '未知錯誤')}")
            except Exception as e:
                print(f"   ❌ 數據解析失敗: {e}")
        else:
            print(f"   ❌ API無法訪問: {response.status_code}")
        
        # 2. 測試摘要API
        print("\n📈 測試摘要API...")
        response = client.get('/unified/api/betting_odds/summary')
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                if data.get('success'):
                    summary = data.get('summary', {})
                    print("   ✅ 摘要API成功")
                    print(f"      總記錄數: {summary.get('total_records', 0)}")
                    print(f"      博彩商數量: {len(summary.get('bookmakers', []))}")
                    print(f"      市場類型: {summary.get('market_types', [])}")
                    print(f"      日期範圍: {summary.get('date_range', {}).get('earliest')} 到 {summary.get('date_range', {}).get('latest')}")
                else:
                    print(f"   ❌ 摘要API失敗: {data.get('error', '未知錯誤')}")
            except Exception as e:
                print(f"   ❌ 摘要數據解析失敗: {e}")
        else:
            print(f"   ❌ 摘要API無法訪問: {response.status_code}")
        
        # 3. 測試特定日期查詢（包含孤立記錄的日期）
        print("\n🎯 測試特定日期查詢...")
        specific_query = {
            "date_from": "2025-08-25",
            "date_to": "2025-08-25",
            "limit": 10
        }
        
        response = client.post('/unified/api/betting_odds/query',
                             json=specific_query,
                             content_type='application/json')
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                if data.get('success'):
                    records = data.get('records', [])
                    print(f"   ✅ 特定日期查詢成功，找到 {len(records)} 條記錄")
                    
                    for i, record in enumerate(records[:3]):
                        print(f"      {i+1}. {record.get('matchup')} - {record.get('bookmaker')} - {record.get('market_type')}")
                else:
                    print(f"   ❌ 特定日期查詢失敗: {data.get('error', '未知錯誤')}")
            except Exception as e:
                print(f"   ❌ 特定日期查詢數據解析失敗: {e}")
        else:
            print(f"   ❌ 特定日期查詢API無法訪問: {response.status_code}")
        
        # 4. 測試無過濾查詢
        print("\n🌐 測試無過濾查詢...")
        no_filter_query = {"limit": 10}
        
        response = client.post('/unified/api/betting_odds/query',
                             json=no_filter_query,
                             content_type='application/json')
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                if data.get('success'):
                    records = data.get('records', [])
                    print(f"   ✅ 無過濾查詢成功，找到 {len(records)} 條記錄")
                else:
                    print(f"   ❌ 無過濾查詢失敗: {data.get('error', '未知錯誤')}")
            except Exception as e:
                print(f"   ❌ 無過濾查詢數據解析失敗: {e}")
        else:
            print(f"   ❌ 無過濾查詢API無法訪問: {response.status_code}")

def test_web_ui():
    """測試Web UI可訪問性"""
    print("\n🌐 測試Web UI可訪問性...")
    
    try:
        import requests
        response = requests.get("http://localhost:5000/unified/betting_odds", timeout=5)
        
        if response.status_code == 200:
            print("   ✅ Web UI可以正常訪問")
            print("      建議在瀏覽器中測試完整功能")
        else:
            print(f"   ❌ Web UI無法訪問: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ⚠️ 無法連接到Web服務器: {e}")
        print("      建議確認服務器是否正在運行")

if __name__ == "__main__":
    test_final_odds_fix()
    test_web_ui()