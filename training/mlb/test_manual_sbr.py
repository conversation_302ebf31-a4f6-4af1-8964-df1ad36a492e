#!/usr/bin/env python3
"""
手動測試sportsbookreview數據抓取（不使用WebDriver）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
from bs4 import BeautifulSoup
from datetime import date
import re

def test_manual_sbr():
    """手動測試sportsbookreview數據抓取"""
    base_url = "https://www.sportsbookreview.com"
    test_date = "2025-07-07"
    
    print(f"🧪 手動測試sportsbookreview數據抓取 - {test_date}")
    
    # 測試讓分盤頁面
    pointspread_url = f"{base_url}/betting-odds/mlb-baseball/pointspread/full-game/?date={test_date}"
    
    try:
        print(f"\n📡 抓取讓分盤數據:")
        print(f"  URL: {pointspread_url}")
        
        response = requests.get(pointspread_url, timeout=30)
        response.raise_for_status()
        
        print(f"  狀態碼: {response.status_code}")
        print(f"  內容長度: {len(response.content):,} bytes")
        
        # 解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找表格或比賽數據
        tables = soup.find_all('table')
        print(f"  找到表格數: {len(tables)}")
        
        # 查找包含球隊名稱的元素
        team_links = soup.find_all('a', href=re.compile(r'/teams/'))
        print(f"  找到球隊鏈接: {len(team_links)}")
        
        if team_links:
            print(f"  球隊示例:")
            for link in team_links[:5]:
                print(f"    - {link.get_text().strip()}")
        
        # 查找包含數字的文本（可能是賠率）
        text_content = soup.get_text()
        
        # 查找讓分盤模式 (如 -1.5, +1.5)
        spread_patterns = re.findall(r'[+-]\d+\.5', text_content)
        print(f"  找到讓分盤模式: {len(set(spread_patterns))}")
        if spread_patterns:
            print(f"    示例: {list(set(spread_patterns))[:5]}")
        
        # 查找賠率模式 (如 -110, +105)
        odds_patterns = re.findall(r'[+-]\d{3}', text_content)
        print(f"  找到賠率模式: {len(set(odds_patterns))}")
        if odds_patterns:
            print(f"    示例: {list(set(odds_patterns))[:5]}")
        
        # 查找MLB球隊縮寫
        mlb_teams = ['NYY', 'BOS', 'LAD', 'SF', 'CHC', 'STL', 'HOU', 'TEX', 'ATL', 'MIA']
        found_teams = []
        for team in mlb_teams:
            if team in text_content:
                found_teams.append(team)
        
        print(f"  找到MLB球隊: {len(found_teams)}")
        if found_teams:
            print(f"    示例: {found_teams[:5]}")
        
        # 保存部分HTML用於調試
        with open('sbr_pointspread_debug.html', 'w', encoding='utf-8') as f:
            f.write(str(soup.prettify())[:50000])  # 只保存前50KB
        print(f"  已保存調試HTML到 sbr_pointspread_debug.html")
        
    except Exception as e:
        print(f"❌ 抓取失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_manual_sbr()
