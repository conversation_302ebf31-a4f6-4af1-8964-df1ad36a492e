#!/usr/bin/env python3
"""
細化分析總結報告
總結隊伍主客場、投手ERA差異的關鍵發現
"""

import sys
sys.path.append('.')

from team_pitcher_detailed_analyzer import TeamPitcherDetailedAnalyzer
from score_prediction_calibrator import ScorePredictionCalibrator

def generate_analysis_summary():
    """生成分析總結報告"""
    
    print("="*80)
    print("🎯 MLB 預測系統細化分析報告")
    print("="*80)
    
    analyzer = TeamPitcherDetailedAnalyzer()
    calibrator = ScorePredictionCalibrator()
    
    # 1. 預測校正分析
    print("\n📊 第一部分：預測準確性問題診斷")
    print("-" * 50)
    analysis = calibrator.analyze_prediction_accuracy(days_back=30)
    
    if analysis:
        print(f"✅ 分析了 {analysis['total_games']} 場比賽的預測表現")
        print(f"📈 預測分數變異性問題:")
        print(f"   - 實際比賽變異性是預測的 {analysis['distribution_comparison']['variance_ratio_home']:.1f}倍 (主場)")
        print(f"   - 實際比賽變異性是預測的 {analysis['distribution_comparison']['variance_ratio_away']:.1f}倍 (客場)")
        print(f"🎯 平均預測誤差:")
        print(f"   - 主場: {analysis['accuracy_metrics']['home_mae']:.2f} 分")
        print(f"   - 客場: {analysis['accuracy_metrics']['away_mae']:.2f} 分")
        print(f"   - 總分: {analysis['accuracy_metrics']['total_mae']:.2f} 分")
        
        bias = analysis['bias_analysis']
        print(f"📐 系統性偏差:")
        print(f"   - 主場偏差: {bias['home_bias']:.2f} ({'高估' if bias['home_bias'] > 0 else '低估'})")
        print(f"   - 客場偏差: {bias['away_bias']:.2f} ({'高估' if bias['away_bias'] > 0 else '低估'})")
        print(f"   - 總分偏差: {bias['total_bias']:.2f} ({'高估' if bias['total_bias'] > 0 else '低估'})")
    
    # 2. 隊伍對戰模式分析
    print(f"\n⚾ 第二部分：隊伍對戰模式細化分析")
    print("-" * 50)
    
    # 分析幾個重要的對戰組合
    key_matchups = [
        ("NYY", "BOS", "Yankees vs Red Sox 經典對戰"),
        ("LAD", "SF", "加州德比 Dodgers vs Giants"),
        ("HOU", "TEX", "德州內戰 Astros vs Rangers")
    ]
    
    for team1, team2, description in key_matchups:
        try:
            matchup = analyzer.analyze_team_matchup_patterns(team1, team2, days_back=365)
            
            if 'error' not in matchup:
                print(f"\n🔥 {description}:")
                print(f"   📅 分析了 {matchup['total_games']} 場歷史對戰")
                
                for team in [team1, team2]:
                    team_stats = matchup['teams'][team]
                    home_perf = team_stats['home_performance']
                    away_perf = team_stats['away_performance']
                    
                    print(f"\n   🏟️ {team} 表現分析:")
                    if home_perf['games'] > 0:
                        print(f"     主場: {home_perf['games']}場, 平均 {home_perf['avg_score']:.2f}±{home_perf['std_score']:.2f} 分")
                    if away_perf['games'] > 0:
                        print(f"     客場: {away_perf['games']}場, 平均 {away_perf['avg_score']:.2f}±{away_perf['std_score']:.2f} 分")
                    
                    if 'home_away_difference' in team_stats:
                        diff = team_stats['home_away_difference']
                        advantage = "主場優勢" if diff['home_advantage'] else "客場更強"
                        print(f"     🎯 {advantage}: 得分差 {abs(diff['score_diff']):.2f}")
                        
                        # 預測誤差比較
                        if home_perf.get('prediction_error') and away_perf.get('prediction_error'):
                            home_error = home_perf['prediction_error']
                            away_error = away_perf['prediction_error']
                            print(f"     📊 預測誤差: 主場 {home_error:.2f} vs 客場 {away_error:.2f}")
            else:
                print(f"   ❌ {description}: 數據不足")
                
        except Exception as e:
            print(f"   ⚠️ {description}: 分析出錯 - {e}")
    
    # 3. 投手分析
    print(f"\n🎯 第三部分：明星投手主客場ERA分析")
    print("-" * 50)
    
    star_pitchers = ["Cole", "Kershaw", "Verlander", "Scherzer", "deGrom"]
    
    for pitcher in star_pitchers:
        try:
            pitcher_analysis = analyzer.analyze_pitcher_home_away_performance(pitcher, days_back=365)
            
            if 'error' not in pitcher_analysis:
                print(f"\n⚾ {pitcher} ({pitcher_analysis['total_starts']}次先發):")
                
                if 'home_starts' in pitcher_analysis:
                    home_stats = pitcher_analysis['home_starts']
                    print(f"   🏠 主場: {home_stats['games']}場, ERA {home_stats['era_equivalent']:.2f}")
                
                if 'away_starts' in pitcher_analysis:
                    away_stats = pitcher_analysis['away_starts']
                    print(f"   ✈️  客場: {away_stats['games']}場, ERA {away_stats['era_equivalent']:.2f}")
                
                if 'home_away_difference' in pitcher_analysis:
                    diff = pitcher_analysis['home_away_difference']
                    better_loc = "主場" if diff['better_location'] == 'home' else "客場"
                    print(f"   📈 {better_loc}更佳, ERA差距 {abs(diff['era_difference']):.2f}")
            else:
                print(f"   ❌ {pitcher}: 數據不足")
                
        except Exception as e:
            print(f"   ⚠️ {pitcher}: 分析出錯")
    
    # 4. 關鍵發現和建議
    print(f"\n🚀 第四部分：關鍵發現和改進建議")
    print("-" * 50)
    
    print("✅ 重要發現:")
    print("   1. 預測分數變異性嚴重不足 - 實際比賽變異性是預測的6-10倍")
    print("   2. 某些隊伍有極強的主客場差異(如NYY主場優勢3.18分)")
    print("   3. 投手主客場ERA差異顯著，需要納入預測模型")
    print("   4. 平均預測誤差2.6-2.7分，有很大改進空間")
    
    print(f"\n🎯 改進建議:")
    print("   1. 實施預測校正系統，解決分數聚集問題")
    print("   2. 增加隊伍特定主客場調整因子")
    print("   3. 整合投手主客場ERA差異數據")
    print("   4. 建立 boxscore 數據收集系統獲取更詳細統計")
    print("   5. 實施上下文感知預測(天氣、球場因子等)")
    
    print(f"\n📋 技術實施優先級:")
    print("   🥇 高優先級: 預測校正系統(已完成)")
    print("   🥈 中優先級: 隊伍主客場因子整合")
    print("   🥉 低優先級: Boxscore 數據收集擴展")
    
    print("="*80)
    print("📝 報告完成 - MLB 預測系統具備明確的改進路徑")
    print("="*80)

if __name__ == "__main__":
    generate_analysis_summary()