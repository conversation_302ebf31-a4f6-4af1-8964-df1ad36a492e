#!/usr/bin/env python3
"""
為管理員界面添加新的賠率抓取端點
"""

# 添加到 views/admin.py 的新端點代碼
new_endpoint_code = '''
@admin_bp.route('/api/fetch-enhanced-odds', methods=['POST'])
def api_fetch_enhanced_odds():
    """API: 使用增強型抓取器抓取賠率數據"""
    try:
        from integrated_odds_system import IntegratedOddsSystem
        from datetime import datetime, date
        
        data = request.get_json()
        target_date_str = data.get('date')
        market_type = data.get('market_type', 'both')
        
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()
        
        logger.info(f"🚀 開始增強型賠率抓取: {target_date} ({market_type})")
        
        # 使用集成系統
        system = IntegratedOddsSystem()
        result = system.fetch_and_save_odds(target_date, market_type)
        
        if result.get('success'):
            return jsonify({
                'success': True,
                'message': result.get('message'),
                'data': {
                    'games_processed': result.get('games_processed', 0),
                    'legacy_saved': result.get('legacy_saved', 0),
                    'enhanced_saved': result.get('enhanced_saved', 0),
                    'total_saved': result.get('total_saved', 0),
                    'date': target_date.isoformat(),
                    'market_type': market_type
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': result.get('message', '抓取失敗'),
                'error': result.get('message')
            }), 500
            
    except Exception as e:
        logger.error(f"增強型賠率抓取失敗: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'抓取失敗: {str(e)}',
            'error': str(e)
        }), 500

@admin_bp.route('/api/odds-statistics', methods=['GET'])
def api_odds_statistics():
    """API: 獲取賠率數據統計"""
    try:
        from models.database import db, BettingOdds
        from sqlalchemy import func
        
        # 基本統計
        spreads_count = BettingOdds.query.filter_by(market_type='spreads').count()
        totals_count = BettingOdds.query.filter_by(market_type='totals').count()
        total_count = BettingOdds.query.count()
        
        # 博彩商統計
        bookmaker_stats = db.session.query(
            BettingOdds.bookmaker,
            func.count(BettingOdds.id).label('count')
        ).group_by(BettingOdds.bookmaker).all()
        
        # 數據來源統計
        source_stats = db.session.query(
            BettingOdds.data_source,
            func.count(BettingOdds.id).label('count')
        ).group_by(BettingOdds.data_source).all()
        
        # 最新記錄
        latest_record = BettingOdds.query.order_by(BettingOdds.created_at.desc()).first()
        
        return jsonify({
            'success': True,
            'statistics': {
                'total_records': total_count,
                'spreads_records': spreads_count,
                'totals_records': totals_count,
                'bookmaker_breakdown': {bm: count for bm, count in bookmaker_stats},
                'source_breakdown': {source: count for source, count in source_stats},
                'latest_update': latest_record.created_at.isoformat() if latest_record else None
            }
        })
        
    except Exception as e:
        logger.error(f"獲取賠率統計失敗: {e}")
        return jsonify({
            'success': False,
            'message': f'獲取統計失敗: {str(e)}',
            'error': str(e)
        }), 500
'''

print("📝 新增端點代碼已準備完成")
print("請將以下代碼添加到 views/admin.py 文件中：")
print("=" * 60)
print(new_endpoint_code)
print("=" * 60)
print("\n🔧 使用方法:")
print("1. POST /admin/api/fetch-enhanced-odds - 抓取增強型賠率數據")
print("2. GET /admin/api/odds-statistics - 獲取賠率數據統計")
print("\n📊 預期效果:")
print("- 讓分盤記錄將從0增加到實際抓取的數量")
print("- 支持多博彩商數據（BetMGM, FanDuel, Caesars等）")
print("- 提供詳細的數據統計和狀態報告")