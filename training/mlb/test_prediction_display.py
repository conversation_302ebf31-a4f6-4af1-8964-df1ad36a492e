#!/usr/bin/env python3
"""
測試預測顯示頁面是否正確顯示大小分和讓分盤
"""

import sys
sys.path.append('.')

from app import app
from models.database import db, Game, Prediction, BettingOdds
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data():
    """創建測試數據來驗證顯示"""
    with app.app_context():
        # 創建測試比賽
        test_date = datetime.now().date()
        test_game_id = f"TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info(f"創建測試比賽: {test_game_id}")
        
        # 創建比賽
        game = Game(
            game_id=test_game_id,
            date=test_date,
            home_team="TB",
            away_team="LAD",
            game_status="scheduled"
        )
        db.session.add(game)
        
        # 創建預測
        prediction = Prediction(
            game_id=test_game_id,
            predicted_home_score=4.2,
            predicted_away_score=3.8,
            home_win_probability=0.55,
            away_win_probability=0.45,
            confidence=0.72,
            over_under_line=8.5,  # 大小分線
            model_version="enhanced_v2"
        )
        db.session.add(prediction)
        
        # 創建賠率數據
        betting_odds = BettingOdds(
            game_id=test_game_id,
            bookmaker="DraftKings",
            market_type="spreads",
            home_spread_point=-1.5,  # 主隊讓1.5分
            home_spread_price=-110,
            away_spread_point=1.5,
            away_spread_price=-110,
            total_point=8.5,  # 大小分
            over_price=-105,
            under_price=-115,
            data_source="test"
        )
        db.session.add(betting_odds)
        
        db.session.commit()
        logger.info("✅ 測試數據創建成功")
        
        return test_game_id

def check_display_elements():
    """檢查顯示元素是否存在"""
    with app.app_context():
        # 獲取今天的預測
        today = datetime.now().date()
        
        # 查詢預測（模擬視圖的查詢）
        predictions = db.session.query(Game, Prediction)\
            .join(Prediction, Game.game_id == Prediction.game_id)\
            .filter(Game.date >= today)\
            .order_by(Game.date.desc())\
            .limit(5)\
            .all()
        
        logger.info(f"\n找到 {len(predictions)} 筆預測")
        
        for game, prediction in predictions:
            # 獲取賠率數據
            betting_odds = BettingOdds.query.filter_by(game_id=game.game_id)\
                .order_by(BettingOdds.odds_time.desc())\
                .first()
            
            logger.info(f"\n{'='*50}")
            logger.info(f"比賽: {game.away_team} @ {game.home_team}")
            logger.info(f"日期: {game.date}")
            logger.info(f"預測分數: {prediction.predicted_away_score:.1f} - {prediction.predicted_home_score:.1f}")
            
            # 檢查大小分
            if prediction.over_under_line:
                total = prediction.predicted_home_score + prediction.predicted_away_score
                ou_pick = "大" if total > prediction.over_under_line else "小"
                logger.info(f"✅ 大小分線: {prediction.over_under_line} → 預測{ou_pick}分 (總分{total:.1f})")
            else:
                logger.info("❌ 缺少大小分線數據")
            
            # 檢查讓分盤
            if betting_odds and betting_odds.home_spread_point is not None:
                if betting_odds.home_spread_point > 0:
                    spread_desc = f"主隊受讓 {betting_odds.home_spread_point}"
                else:
                    spread_desc = f"主隊讓 {-betting_odds.home_spread_point}"
                
                # 計算讓分後的勝負
                spread_diff = (prediction.predicted_home_score - prediction.predicted_away_score) + betting_odds.home_spread_point
                spread_pick = "主隊" if spread_diff > 0 else "客隊"
                
                logger.info(f"✅ 讓分盤: {spread_desc} → 推薦買{spread_pick}")
            else:
                logger.info("❌ 缺少讓分盤數據")

def test_template_rendering():
    """測試模板渲染"""
    with app.test_client() as client:
        # 訪問預測頁面
        response = client.get('/predictions')
        
        if response.status_code == 200:
            html = response.data.decode('utf-8')
            
            # 檢查關鍵元素
            checks = {
                '大小分': '大小分' in html,
                '讓分盤': '讓分盤' in html,
                'badge bg-info': 'badge bg-info' in html,  # 大小分標籤
                'badge bg-success': 'badge bg-success' in html,  # 讓分標籤
                'badge bg-danger': 'badge bg-danger' in html,  # 受讓標籤
            }
            
            logger.info("\n模板元素檢查:")
            for element, exists in checks.items():
                status = "✅" if exists else "❌"
                logger.info(f"  {status} {element}")
            
            # 如果有預測，保存HTML供檢查
            if '預測' in html:
                with open('test_prediction_page.html', 'w') as f:
                    f.write(html)
                logger.info("\n頁面已保存到 test_prediction_page.html")
        else:
            logger.error(f"無法訪問預測頁面: {response.status_code}")

def main():
    """主測試函數"""
    logger.info("="*60)
    logger.info("MLB預測顯示測試")
    logger.info("="*60)
    
    try:
        # 1. 創建測試數據
        test_game_id = create_test_data()
        
        # 2. 檢查顯示元素
        check_display_elements()
        
        # 3. 測試模板渲染
        test_template_rendering()
        
        logger.info("\n" + "="*60)
        logger.info("測試完成！")
        logger.info("如果看到 ✅ 表示該功能正常")
        logger.info("如果看到 ❌ 表示該功能需要檢查")
        logger.info("="*60)
        
    except Exception as e:
        logger.error(f"測試失敗: {e}", exc_info=True)

if __name__ == "__main__":
    main()