#!/bin/bash

# MLB預測系統啟動腳本
echo "🏀 啟動MLB預測系統..."

# 檢查虛擬環境
if [ ! -d ".venv" ]; then
    echo "❌ 找不到虛擬環境，請先運行 setup.sh"
    exit 1
fi

# 激活虛擬環境
echo "🔧 激活虛擬環境..."
source .venv/bin/activate

# 檢查數據庫
echo "📊 檢查數據庫狀態..."
python -c "
from app import create_app
from models.database import db, Game
app = create_app()
with app.app_context():
    try:
        game_count = Game.query.count()
        print(f'✅ 數據庫連接正常，共有 {game_count:,} 場比賽記錄')
    except Exception as e:
        print(f'❌ 數據庫連接失敗: {e}')
        exit(1)
"

# 啟動Flask應用
echo "🌐 啟動Web服務器..."
echo "📱 請在瀏覽器中打開: http://localhost:5500"
echo "⏹️  按 Ctrl+C 停止服務器"
echo ""

export FLASK_APP=app.py
export FLASK_ENV=development
python app.py
