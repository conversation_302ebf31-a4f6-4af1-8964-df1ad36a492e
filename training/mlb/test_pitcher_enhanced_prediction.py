#!/usr/bin/env python3
"""
測試增強投手因素的預測系統
"""

import sys
import os
from datetime import date, timedelta
import pandas as pd

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.ml_predictor import MLBPredictor
from models.database import Game, PlayerStats, Team

def test_pitcher_factors():
    """測試投手因素提取功能"""
    app = create_app()
    
    with app.app_context():
        print("🔍 測試投手因素增強預測系統")
        print("=" * 60)
        
        # 初始化預測器
        predictor = MLBPredictor()
        
        # 獲取一場測試比賽
        test_game = Game.query.filter(
            Game.date == date(2025, 6, 28)
        ).first()
        
        if not test_game:
            print("❌ 找不到測試比賽")
            return
        
        print(f"📊 測試比賽: {test_game.away_team} @ {test_game.home_team}")
        print(f"📅 比賽日期: {test_game.date}")
        
        # 創建測試數據
        game_data = pd.Series({
            'game_id': test_game.game_id,
            'home_team': test_game.home_team,
            'away_team': test_game.away_team,
            'date': test_game.date,
            'home_score': None,  # 預測時不知道結果
            'away_score': None
        })
        
        # 測試投手因素提取
        print("\n🎯 投手因素分析:")
        pitcher_factors = predictor._get_pitcher_factors(game_data)
        
        for factor, value in pitcher_factors.items():
            if isinstance(value, float):
                print(f"  {factor}: {value:.2f}")
            else:
                print(f"  {factor}: {value}")
        
        # 檢查王牌投手數據
        print(f"\n⚾ 主隊 ({test_game.home_team}) 王牌投手:")
        home_ace = predictor._get_team_ace_pitcher_stats(test_game.home_team)
        if home_ace:
            print(f"  ERA: {home_ace['era']:.2f}")
            print(f"  WHIP: {home_ace['whip']:.2f}")
            print(f"  投球局數: {home_ace['innings_pitched']:.1f}")
        else:
            print("  未找到投手數據")
        
        print(f"\n⚾ 客隊 ({test_game.away_team}) 王牌投手:")
        away_ace = predictor._get_team_ace_pitcher_stats(test_game.away_team)
        if away_ace:
            print(f"  ERA: {away_ace['era']:.2f}")
            print(f"  WHIP: {away_ace['whip']:.2f}")
            print(f"  投球局數: {away_ace['innings_pitched']:.1f}")
        else:
            print("  未找到投手數據")
        
        # 分析投手對戰優勢
        print(f"\n🥊 投手對戰分析:")
        if home_ace and away_ace:
            home_quality = predictor._calculate_pitcher_quality(home_ace)
            away_quality = predictor._calculate_pitcher_quality(away_ace)
            
            print(f"  主隊投手質量: {home_quality:.1f}/100")
            print(f"  客隊投手質量: {away_quality:.1f}/100")
            
            advantage = pitcher_factors['pitcher_matchup_advantage']
            if advantage > 0.2:
                print(f"  ✅ 主隊投手優勢 (+{advantage:.2f})")
            elif advantage < -0.2:
                print(f"  ✅ 客隊投手優勢 ({advantage:.2f})")
            else:
                print(f"  ⚖️ 投手實力相當 ({advantage:.2f})")
        
        # 檢查是否有王牌投手
        print(f"\n👑 王牌投手檢測:")
        if pitcher_factors['home_has_ace']:
            print(f"  🌟 主隊有王牌投手 (ERA < 3.00)")
        if pitcher_factors['away_has_ace']:
            print(f"  🌟 客隊有王牌投手 (ERA < 3.00)")
        if not pitcher_factors['home_has_ace'] and not pitcher_factors['away_has_ace']:
            print(f"  📊 雙方都是普通投手")

def test_enhanced_prediction():
    """測試增強後的預測功能"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("🚀 測試增強後的預測功能")
        print("=" * 60)
        
        predictor = MLBPredictor()
        
        # 測試預測一場比賽
        try:
            prediction = predictor.predict_game(
                'New York Yankees', 'Boston Red Sox', date(2025, 6, 30)
            )
            
            print("📈 預測結果:")
            print(f"  主隊得分: {prediction['predicted_home_score']}")
            print(f"  客隊得分: {prediction['predicted_away_score']}")
            print(f"  主隊勝率: {prediction['home_win_probability']:.1%}")
            print(f"  信心度: {prediction['confidence']:.1%}")
            
        except Exception as e:
            print(f"❌ 預測失敗: {e}")

def analyze_pitcher_impact():
    """分析投手因素對預測的影響"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("📊 投手因素影響分析")
        print("=" * 60)
        
        # 統計各隊的王牌投手情況
        teams_with_aces = []
        teams_without_aces = []
        
        predictor = MLBPredictor()
        
        # 獲取所有球隊
        teams = Team.query.all()
        
        for team in teams[:10]:  # 測試前10支球隊
            ace_stats = predictor._get_team_ace_pitcher_stats(team.team_name)
            if ace_stats and ace_stats['era'] < 3.0:
                teams_with_aces.append({
                    'team': team.team_name,
                    'era': ace_stats['era'],
                    'quality': predictor._calculate_pitcher_quality(ace_stats)
                })
            else:
                teams_without_aces.append(team.team_name)
        
        print(f"🌟 有王牌投手的球隊 ({len(teams_with_aces)}):")
        for team_info in sorted(teams_with_aces, key=lambda x: x['era']):
            print(f"  {team_info['team']}: ERA {team_info['era']:.2f}, 質量 {team_info['quality']:.1f}/100")
        
        print(f"\n📊 普通投手球隊 ({len(teams_without_aces)}):")
        for team in teams_without_aces:
            print(f"  {team}")

if __name__ == "__main__":
    print("⚾ MLB投手因素增強預測系統測試")
    print("=" * 60)
    
    # 測試投手因素提取
    test_pitcher_factors()
    
    # 測試增強預測
    test_enhanced_prediction()
    
    # 分析投手影響
    analyze_pitcher_impact()
    
    print("\n✅ 測試完成")
