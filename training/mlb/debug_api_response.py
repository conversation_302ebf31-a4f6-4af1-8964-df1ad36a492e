#!/usr/bin/env python3
"""
調試MLB API響應結構
"""

import requests
import json
from datetime import date, timedelta

def debug_api_response():
    """調試API響應結構"""
    print("=" * 80)
    print("🔍 MLB API響應結構調試")
    print("=" * 80)
    
    base_url = "https://statsapi.mlb.com/api/v1"
    yesterday = date.today() - timedelta(days=1)
    date_str = yesterday.strftime('%Y-%m-%d')
    
    # 測試不同的hydrate參數
    test_cases = [
        {
            'name': '基本比賽信息',
            'params': {
                'sportId': 1,
                'date': date_str
            }
        },
        {
            'name': '包含投手信息',
            'params': {
                'sportId': 1,
                'date': date_str,
                'hydrate': 'probablePitchers'
            }
        },
        {
            'name': '包含打線信息',
            'params': {
                'sportId': 1,
                'date': date_str,
                'hydrate': 'lineups'
            }
        },
        {
            'name': '包含所有信息',
            'params': {
                'sportId': 1,
                'date': date_str,
                'hydrate': 'probablePitchers,lineups,boxscore'
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📡 測試: {test_case['name']}")
        print("-" * 50)
        
        try:
            url = f"{base_url}/schedule"
            response = requests.get(url, params=test_case['params'], timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # 檢查響應結構
            print(f"響應狀態: {response.status_code}")
            print(f"響應大小: {len(response.text)} 字符")
            
            if 'dates' in data and data['dates']:
                first_date = data['dates'][0]
                if 'games' in first_date and first_date['games']:
                    first_game = first_date['games'][0]
                    
                    print(f"第一場比賽結構:")
                    print(f"  Game ID: {first_game.get('gamePk')}")
                    print(f"  狀態: {first_game.get('status', {}).get('detailedState')}")
                    
                    # 檢查投手信息
                    if 'probablePitchers' in first_game:
                        pitchers = first_game['probablePitchers']
                        print(f"  投手信息: {pitchers}")
                    
                    # 檢查打線信息
                    if 'lineups' in first_game:
                        lineups = first_game['lineups']
                        print(f"  打線信息結構: {list(lineups.keys()) if lineups else 'Empty'}")
                        
                        if lineups:
                            # 顯示打線詳細結構
                            for key, value in lineups.items():
                                print(f"    {key}: {type(value)} (長度: {len(value) if isinstance(value, list) else 'N/A'})")
                                if isinstance(value, list) and value:
                                    print(f"      第一個元素: {value[0]}")
                    
                    # 檢查boxscore信息
                    if 'boxscore' in first_game:
                        boxscore = first_game['boxscore']
                        print(f"  Boxscore信息: {list(boxscore.keys()) if boxscore else 'Empty'}")
                    
                    # 顯示完整的第一場比賽結構（前500字符）
                    game_json = json.dumps(first_game, indent=2)[:500]
                    print(f"\n  比賽JSON結構樣本:\n{game_json}...")
            
        except Exception as e:
            print(f"❌ 請求失敗: {e}")
    
    # 測試特定比賽的詳細信息
    print(f"\n🎯 測試特定比賽詳細信息:")
    print("-" * 50)
    
    try:
        # 先獲取比賽列表
        url = f"{base_url}/schedule"
        params = {'sportId': 1, 'date': date_str}
        response = requests.get(url, params=params, timeout=30)
        data = response.json()
        
        if data['dates'] and data['dates'][0]['games']:
            game_id = data['dates'][0]['games'][0]['gamePk']
            
            # 獲取特定比賽的詳細信息
            game_url = f"{base_url}/game/{game_id}/boxscore"
            game_response = requests.get(game_url, timeout=30)
            game_data = game_response.json()
            
            print(f"比賽 {game_id} 詳細信息:")
            print(f"  響應大小: {len(game_response.text)} 字符")
            
            if 'teams' in game_data:
                teams = game_data['teams']
                print(f"  球隊信息: {list(teams.keys())}")
                
                for team_type in ['home', 'away']:
                    if team_type in teams:
                        team_data = teams[team_type]
                        print(f"  {team_type}隊結構: {list(team_data.keys())}")
                        
                        if 'players' in team_data:
                            players = team_data['players']
                            print(f"    球員數量: {len(players)}")
                            
                            # 顯示第一個球員信息
                            if players:
                                first_player_id = list(players.keys())[0]
                                first_player = players[first_player_id]
                                print(f"    第一個球員: {first_player.get('person', {}).get('fullName')}")
                                print(f"    球員結構: {list(first_player.keys())}")
    
    except Exception as e:
        print(f"❌ 特定比賽請求失敗: {e}")

def main():
    """主函數"""
    debug_api_response()

if __name__ == "__main__":
    main()
