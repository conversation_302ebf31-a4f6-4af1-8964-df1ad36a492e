# MLB 預測系統資料分析報告

## 📊 預測模型需要的資料欄位

### 1. 基礎比賽特徵
- **時間特徵**
  - `is_weekend`: 是否為週末 (0/1)
  - `month`: 月份 (1-12)
  - `day_of_week`: 星期幾 (0-6)
  - `game_date`: 比賽日期

### 2. 球隊基本統計
- **勝負記錄**
  - `home_win_pct`: 主隊勝率
  - `away_win_pct`: 客隊勝率
  - `win_pct_diff`: 勝率差異
  - `games_played`: 已比賽場次
  - `wins`: 勝場數
  - `losses`: 敗場數

- **得分能力**
  - `home_runs_scored_avg`: 主隊平均得分
  - `away_runs_scored_avg`: 客隊平均得分
  - `runs_scored_avg_diff`: 得分能力差異
  - `home_runs_allowed_avg`: 主隊平均失分
  - `away_runs_allowed_avg`: 客隊平均失分
  - `runs_allowed_avg_diff`: 防守能力差異

### 3. 打擊統計
- **基礎打擊指標**
  - `batting_avg`: 打擊率 (.250-.350)
  - `on_base_percentage`: 上壘率 (OBP)
  - `slugging_percentage`: 長打率 (SLG)
  - `ops`: 攻擊指數 (OBP + SLG)
  - `home_runs`: 全壘打數
  - `stolen_bases`: 盜壘數

- **進階打擊評級**
  - `home_offense_rating`: 主隊攻擊評級
  - `away_offense_rating`: 客隊攻擊評級
  - `offense_rating_diff`: 攻擊力差異

### 4. 投手統計
- **基礎投手指標**
  - `era`: 自責分率 (2.00-6.00)
  - `whip`: 每局被上壘率 (1.00-1.80)
  - `strikeouts`: 三振數
  - `walks`: 保送數
  - `saves`: 救援成功數

- **投手質量評估**
  - `home_pitcher_strength`: 主隊投手實力
  - `away_pitcher_strength`: 客隊投手實力
  - `is_pitcher_duel`: 是否為投手對決 (0/1)
  - `is_slugfest`: 是否為打擊戰 (0/1)

### 5. 近期表現
- **近期戰績**
  - `home_recent_win_pct`: 主隊近期勝率
  - `away_recent_win_pct`: 客隊近期勝率
  - `recent_win_pct_diff`: 近期勝率差異

- **近期得分表現**
  - `home_recent_runs_avg`: 主隊近期平均得分
  - `away_recent_runs_avg`: 客隊近期平均得分

### 6. 歷史對戰 (H2H)
- **對戰記錄**
  - `h2h_home_win_pct`: 主隊歷史對戰勝率
  - `h2h_games_count`: 歷史對戰場次
  - `h2h_avg_total_runs`: 歷史對戰平均總得分

### 7. 主客場優勢
- **主場優勢**
  - `home_advantage`: 主場優勢係數
  - `home_wins`: 主場勝場
  - `home_losses`: 主場敗場
  - `away_wins`: 客場勝場
  - `away_losses`: 客場敗場

### 8. 投手對戰分析
- **先發投手信息**
  - `starting_pitcher_home`: 主隊先發投手
  - `starting_pitcher_away`: 客隊先發投手
  - `home_pitcher_era`: 主隊投手ERA
  - `away_pitcher_era`: 客隊投手ERA
  - `pitcher_matchup_advantage`: 投手對戰優勢

### 9. 博彩相關數據
- **盤口信息**
  - `over_under_line`: 大小分盤口
  - `run_line_spread`: 讓分盤口
  - `moneyline_home`: 主隊獨贏賠率
  - `moneyline_away`: 客隊獨贏賠率

### 10. 進階特徵
- **ELO評級**
  - `home_elo_rating`: 主隊ELO評級
  - `away_elo_rating`: 客隊ELO評級
  - `elo_rating_diff`: ELO評級差異

- **疲勞因素**
  - `home_rest_days`: 主隊休息天數
  - `away_rest_days`: 客隊休息天數
  - `fatigue_factor`: 疲勞因素

- **天氣影響**
  - `weather_condition`: 天氣狀況
  - `temperature`: 溫度
  - `wind_speed`: 風速
  - `wind_direction`: 風向

## 🗄️ 目前資料庫中的資料表

### 📊 資料庫統計概覽
- **球隊數量**: 30 支 (完整的MLB球隊)
- **比賽記錄**: 15,202 場比賽
- **球隊統計**: 30 筆賽季統計
- **預測記錄**: 655 筆預測結果
- **資料表總數**: 15 個表格

### 1. games (比賽記錄) - 20個欄位
**用途**: 存儲所有比賽的基本信息和結果
**記錄數**: 15,202 場比賽
- `game_id`: 比賽唯一識別碼 (PK)
- `date`: 比賽日期
- `home_team`: 主隊代碼
- `away_team`: 客隊代碼
- `home_score`: 主隊得分
- `away_score`: 客隊得分
- `game_status`: 比賽狀態
- `venue`: 比賽場地
- `weather`: 天氣狀況
- `temperature`: 溫度
- `original_date`: 原定比賽日期
- `is_makeup_game`: 是否為補賽
- `postponement_reason`: 延期原因
- `doubleheader_game`: 雙重賽場次
- `inning`: 局數
- `inning_state`: 局數狀態

### 2. teams (球隊基本信息) - 10個欄位
**用途**: 存儲30支MLB球隊的基本資料
**記錄數**: 30 支球隊 (完整MLB)
- `team_id`: 球隊ID (PK)
- `team_code`: 球隊代碼 (如 NYY, BOS)
- `team_name`: 球隊全名
- `team_name_short`: 球隊簡稱
- `division`: 分區
- `league`: 聯盟 (AL/NL)
- `venue_name`: 主場名稱
- `active`: 是否活躍

**球隊分布**:
- **美國聯盟 (AL)**: 15支球隊
  - AL Central: CWS, CLE, DET, KC, MIN
  - AL East: BAL, BOS, NYY, TB, TOR
  - AL West: ATH, HOU, LAA, SEA, TEX
- **國家聯盟 (NL)**: 15支球隊
  - NL Central: CHC, CIN, MIL, PIT, STL
  - NL East: ATL, MIA, NYM, PHI, WSH
  - NL West: AZ, COL, LAD, SD, SF

### 3. team_stats (球隊統計) - 25個欄位
**用途**: 存儲球隊的賽季統計數據
**記錄數**: 30 筆 (2025賽季)
- `team_id`: 球隊ID (FK)
- `season`: 賽季年份
- `games_played`: 已比賽場次
- `wins`: 勝場數
- `losses`: 敗場數
- `win_percentage`: 勝率
- `runs_scored`: 總得分
- `runs_allowed`: 總失分
- `batting_avg`: 打擊率
- `on_base_percentage`: 上壘率
- `slugging_percentage`: 長打率
- `ops`: 攻擊指數
- `home_runs`: 全壘打數
- `era`: 自責分率
- `whip`: 每局被上壘率
- `strikeouts`: 三振數
- `walks`: 保送數
- `home_wins/losses`: 主場戰績
- `away_wins/losses`: 客場戰績

**目前戰績領先球隊** (2025賽季):
1. DET: 39勝23敗 (勝率 .629)
2. MIL: 37勝22敗 (勝率 .627)
3. HOU: 38勝24敗 (勝率 .613)
4. LAD: 40勝26敗 (勝率 .606)
5. CHC: 35勝23敗 (勝率 .603)

### 4. player_stats (球員統計) - 42個欄位
**用途**: 存儲個別球員的統計數據
**記錄數**: 大量球員統計記錄
- `player_id`: 球員ID
- `player_name`: 球員姓名
- `team_id`: 所屬球隊ID (FK)
- `season`: 賽季年份
- `position`: 守備位置
- `games_played`: 出賽場次
- `at_bats`: 打數
- `runs`: 得分
- `hits`: 安打
- `home_runs`: 全壘打
- `rbi`: 打點
- `batting_avg`: 打擊率
- `ops`: 攻擊指數
- `innings_pitched`: 投球局數
- `era`: 自責分率
- `whip`: 每局被上壘率
- `strikeouts_pitching`: 投手三振數
- `wins/losses`: 投手勝敗
- `fielding_percentage`: 守備率
- `errors`: 失誤數

### 5. predictions (預測記錄) - 28個欄位
**用途**: 存儲模型預測結果和驗證數據
**記錄數**: 655 筆預測記錄
- `game_id`: 比賽ID (FK)
- `predicted_home_score`: 預測主隊得分
- `predicted_away_score`: 預測客隊得分
- `home_win_probability`: 主隊勝率
- `confidence`: 預測信心度
- `model_version`: 模型版本
- `predicted_total_runs`: 預測總得分
- `over_under_line`: 大小分盤口
- `starting_pitcher_home`: 主隊先發投手
- `starting_pitcher_away`: 客隊先發投手
- `pitcher_vs_batter_analysis`: 投手對打者分析 (JSON)
- `features_used`: 使用的特徵 (JSON)
- `actual_home_score`: 實際主隊得分
- `actual_away_score`: 實際客隊得分
- `is_correct`: 預測是否正確
- `over_under_correct`: 大小分預測是否正確
- `pitcher_matchup_advantage`: 投手對戰優勢

**最近預測範例**:
- 777079: 預測 4.1-3.6 (信心度 70%)
- 777076: 預測 4.1-4.5 (信心度 70%)
- 777077: 預測 3.8-4.6 (信心度 70%)

### 6. betting_odds (博彩賠率) - 24個欄位
**用途**: 存儲真實博彩盤口數據
- `game_id`: 比賽ID (FK)
- `bookmaker`: 博彩商
- `market_type`: 市場類型 (spreads/totals)
- `home_spread_point`: 主隊讓分點數
- `away_spread_point`: 客隊讓分點數
- `total_point`: 大小分點數
- `over_price`: 大分賠率
- `under_price`: 小分賠率
- `odds_time`: 賠率時間
- `data_source`: 數據來源
- `is_real`: 是否為真實數據

### 7. game_details (比賽詳情) - 22個欄位
**用途**: 存儲比賽的詳細信息
- `game_id`: 比賽ID (FK)
- `attendance`: 觀眾人數
- `game_duration`: 比賽時長
- `weather_condition`: 天氣狀況
- `wind_speed`: 風速
- `wind_direction`: 風向
- `home_starting_pitcher`: 主隊先發投手
- `away_starting_pitcher`: 客隊先發投手
- `winning_pitcher`: 勝投
- `losing_pitcher`: 敗投
- `save_pitcher`: 救援投手
- `game_summary`: 比賽摘要

### 8. box_scores (比賽Box Score) - 15個欄位
**用途**: 存儲詳細的比賽統計
- `game_id`: 比賽ID (FK)
- `team_id`: 球隊ID (FK)
- `is_home`: 是否為主隊
- `runs`: 得分
- `hits`: 安打
- `errors`: 失誤
- `left_on_base`: 殘壘
- `inning_scores`: 各局得分 (JSON)

### 9. player_game_stats (球員單場統計) - 31個欄位
**用途**: 存儲球員在單場比賽中的表現
- `game_id`: 比賽ID (FK)
- `player_id`: 球員ID (FK)
- `position`: 守備位置
- `batting_order`: 打序
- `at_bats`: 打數
- `runs`: 得分
- `hits`: 安打
- `rbi`: 打點
- `innings_pitched`: 投球局數
- `hits_allowed`: 被安打
- `earned_runs`: 自責分
- `strikeouts_pitched`: 三振數
- `putouts`: 刺殺
- `assists`: 助殺
- `errors`: 失誤

### 10. 進階分析表格
**新增的進階分析表格**:
- `players` (球員基本信息) - 19個欄位
- `team_advanced_stats` (球隊進階統計) - 39個欄位
- `player_injury_reports` (球員傷病報告) - 17個欄位
- `player_performance_trends` (球員表現趨勢) - 23個欄位
- `team_chemistry` (球隊化學反應) - 13個欄位
- `weather_conditions` (天氣條件) - 16個欄位

## 📈 資料完整性分析

### ✅ 已有充足資料的領域
1. **基礎比賽數據**: 15,202 場比賽記錄，涵蓋完整賽季
2. **球隊基本統計**: 30 支球隊的詳細賽季統計
3. **預測記錄**: 655 筆預測結果，包含驗證數據
4. **博彩數據**: betting_odds 表有真實盤口數據
5. **球員統計**: 詳細的個人和單場表現數據
6. **進階分析**: 包含傷病、天氣、化學反應等進階因素

### ⚠️ 資料品質需要改善的領域
1. **即時先發投手信息**: 需要更準確的每日先發投手數據
2. **投手ERA數據**: 目前使用默認值4.5，需要實際ERA數據
3. **球隊平均ERA計算**: 需要修復team字段查詢問題
4. **博彩盤口完整性**: 部分比賽缺少over_under等欄位
5. **近期表現趨勢**: 需要更精確的近期表現計算

### 🎯 預測模型使用的核心特徵

#### 1. Core_v1.0 模型 (19個基礎特徵)
- 時間特徵: is_weekend, month, day_of_week
- 球隊統計: win_pct, runs_scored_avg, runs_allowed_avg
- 打擊指標: batting_avg, ops
- 投手指標: era, whip
- 差異特徵: win_pct_diff, runs_scored_avg_diff

#### 2. Enhanced 模型 (50+個進階特徵)
- 包含Core_v1.0所有特徵
- 投手對戰分析: pitcher_matchup_features
- 王牌投手因素: ace_pitcher_factors
- 疲勞度分析: fatigue_features
- 天氣影響: weather_impact_features
- ELO評級: elo_rating_features

#### 3. Calibrated 模型
- 在基礎模型上加入校正因子
- 使用歷史預測準確度進行調整

### 📊 資料庫使用狀況
- **資料庫位置**: `/Users/<USER>/python/training/mlb/instance/mlb_data.db`
- **總表格數**: 15個表格
- **總欄位數**: 約300+個欄位
- **資料時間範圍**: 2025賽季 (最新到9月28日)

## 🔧 改進建議

### 1. 資料品質優化 (高優先級)
- **修復投手ERA查詢**: 解決PlayerStats.team字段問題
- **完善博彩數據**: 補充missing的over_under欄位
- **投手信息整合**: 確保先發投手信息正確存儲和讀取
- **數據驗證**: 建立自動化數據品質檢查

### 2. 特徵工程改進 (中優先級)
- **動態權重系統**: 根據比賽重要性調整特徵權重
- **球員疲勞度**: 基於出賽頻率計算疲勞因子
- **投手對打者**: 建立詳細的投打對戰歷史分析
- **主場優勢**: 細化不同球場的主場優勢係數

### 3. 模型驗證強化 (中優先級)
- **回測系統**: 建立時間序列回測驗證
- **準確度追蹤**: 實時監控預測準確度
- **模型比較**: 不同模型版本的性能對比
- **預測信心度**: 改進信心度計算方法

### 4. 系統架構優化 (低優先級)
- **API性能**: 優化預測API響應時間
- **數據更新**: 建立自動化數據更新流程
- **監控儀表板**: 開發實時監控界面
- **備份策略**: 建立數據備份和恢復機制

## 🎯 下一步行動計劃

### 立即執行 (本週)
1. 修復投手ERA查詢問題
2. 完善投手信息在API中的顯示
3. 驗證博彩數據完整性

### 短期目標 (本月)
1. 建立投手數據品質監控
2. 改進預測準確度計算
3. 優化特徵提取效率

### 長期目標 (下季)
1. 建立完整的回測系統
2. 開發預測性能儀表板
3. 整合更多外部數據源
