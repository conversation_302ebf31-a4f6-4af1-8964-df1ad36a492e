#!/usr/bin/env python3
"""
測試賠率數據獲取功能
檢查賠率抓取器和數據庫中的賠率數據狀態
"""

from app import create_app
from models.database import db, Game, BettingOdds
from models.real_betting_odds_fetcher import RealBettingOddsFetcher
from datetime import date, timedelta
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_odds_fetching():
    """測試賠率數據獲取功能"""
    app = create_app()
    
    with app.app_context():
        print("🔍 測試賠率數據獲取功能...")
        print("=" * 80)
        
        # 1. 檢查數據庫中現有的賠率數據
        print("\n📊 檢查數據庫中的賠率數據...")
        
        # 檢查過去7天的賠率數據
        total_odds_count = BettingOdds.query.count()
        print(f"   📈 總賠率記錄數: {total_odds_count}")
        
        recent_dates = [date.today() - timedelta(days=x) for x in range(7)]
        
        for check_date in recent_dates:
            # 查找該日期的比賽
            games_on_date = Game.query.filter_by(date=check_date).all()
            
            # 查找該日期的賠率數據
            odds_count = BettingOdds.query.join(Game).filter(
                Game.date == check_date
            ).count()
            
            print(f"   📅 {check_date}: {len(games_on_date)} 場比賽, {odds_count} 條賠率記錄")
            
            # 如果有賠率數據，顯示詳細信息
            if odds_count > 0:
                odds_records = BettingOdds.query.join(Game).filter(
                    Game.date == check_date
                ).limit(3).all()
                
                print(f"      💰 賠率範例:")
                for odds in odds_records:
                    game = odds.game
                    if odds.market_type == 'totals':
                        print(f"         - {game.away_team}@{game.home_team}: 大小分 {odds.total_point} (O{odds.over_price}/U{odds.under_price}) [{odds.bookmaker}]")
                    elif odds.market_type == 'spreads':
                        print(f"         - {game.away_team}@{game.home_team}: 讓分盤 {odds.away_spread_point}/{odds.home_spread_point} [{odds.bookmaker}]")
        
        # 2. 測試賠率獲取器
        print(f"\n🔧 測試賠率獲取器...")
        
        try:
            fetcher = RealBettingOddsFetcher(app)
            
            # 檢查API狀態
            print("   📡 檢查API狀態...")
            api_status = fetcher.check_api_status()
            print(f"      API密鑰已配置: {api_status['api_key_configured']}")
            print(f"      API可訪問: {api_status['api_accessible']}")
            print(f"      狀態消息: {api_status['message']}")
            
            if hasattr(fetcher, 'check_multi_api_status'):
                multi_status = fetcher.check_multi_api_status()
                print(f"      多API系統狀態: {multi_status['overall_status']}")
                if multi_status['recommendations']:
                    for rec in multi_status['recommendations'][:3]:
                        print(f"         - {rec}")
            
            # 3. 測試獲取昨天的賠率數據
            yesterday = date.today() - timedelta(days=1)
            print(f"\n🎯 測試獲取 {yesterday} 的賠率數據...")
            
            odds_result = fetcher.get_mlb_odds_today(yesterday)
            
            if odds_result and odds_result.get('success'):
                games = odds_result.get('games', [])
                print(f"   ✅ 成功獲取 {len(games)} 場比賽的賠率數據")
                
                # 顯示前3場比賽的賠率信息
                for i, game in enumerate(games[:3]):
                    print(f"      🏟️ 比賽 {i+1}: {game.get('away_team')} @ {game.get('home_team')}")
                    
                    bookmakers = game.get('bookmakers', [])
                    if bookmakers:
                        for bookmaker in bookmakers[:1]:  # 只顯示第一個博彩商
                            print(f"         📊 {bookmaker.get('title', 'Unknown')}")
                            
                            for market in bookmaker.get('markets', []):
                                market_type = market.get('key')
                                outcomes = market.get('outcomes', [])
                                
                                if market_type == 'totals' and len(outcomes) >= 2:
                                    over = next((o for o in outcomes if o.get('name') == 'Over'), {})
                                    under = next((o for o in outcomes if o.get('name') == 'Under'), {})
                                    if over and under:
                                        print(f"            大小分: {over.get('point')} (O{over.get('price')}/U{under.get('price')})")
                                
                                elif market_type == 'spreads' and len(outcomes) >= 2:
                                    home_spread = next((o for o in outcomes if o.get('name') == game.get('home_team')), {})
                                    away_spread = next((o for o in outcomes if o.get('name') == game.get('away_team')), {})
                                    if home_spread and away_spread:
                                        print(f"            讓分盤: {away_spread.get('point')}/{home_spread.get('point')} ({away_spread.get('price')}/{home_spread.get('price')})")
            else:
                print(f"   ❌ 獲取賠率數據失敗")
                if odds_result:
                    print(f"      錯誤信息: {odds_result.get('message', '未知錯誤')}")
                    if 'summary' in odds_result:
                        summary = odds_result['summary']
                        print(f"      數據來源: {summary.get('data_source', 'unknown')}")
                        print(f"      是否真實數據: {summary.get('is_real_data', False)}")
                
        except Exception as e:
            print(f"   ❌ 賠率獲取器測試失敗: {e}")
        
        # 4. 檢查不同的抓取器組件
        print(f"\n🛠️ 檢查抓取器組件...")
        
        # 檢查各個抓取器模組
        scrapers = [
            ('json_sbr_scraper', 'JSONSBRScraper'),
            ('covers_scraper', 'CoversMLBScraper'),
            ('sportsbookreview_scraper', 'SportsBookReviewScraper'),
            ('modern_sbr_scraper', 'ModernSBRScraper'),
            ('reliable_odds_fetcher', 'ReliableOddsFetcher')
        ]
        
        for module_name, class_name in scrapers:
            try:
                module = __import__(f'models.{module_name}', fromlist=[class_name])
                scraper_class = getattr(module, class_name)
                print(f"   ✅ {class_name} 可用")
                
                # 嘗試實例化
                try:
                    scraper = scraper_class()
                    print(f"      ✅ {class_name} 實例化成功")
                except Exception as e:
                    print(f"      ⚠️ {class_name} 實例化失敗: {e}")
                    
            except ImportError as e:
                print(f"   ❌ {class_name} 導入失敗: {e}")
            except Exception as e:
                print(f"   ❌ {class_name} 檢查失敗: {e}")
        
        print(f"\n📋 總結:")
        print(f"   - 數據庫賠率記錄: {total_odds_count} 條")
        print(f"   - 賠率獲取器可用性: {'✅' if 'fetcher' in locals() else '❌'}")
        print(f"   - 建議: 檢查網絡連接和API配置")

if __name__ == "__main__":
    test_odds_fetching()