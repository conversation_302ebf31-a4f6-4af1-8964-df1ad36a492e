#!/usr/bin/env python3
"""
測試預測系統功能
"""

from app import create_app
from models.prediction_service import PredictionService
from models.automated_predictor import automated_predictor
from models.database import db, Game, Prediction
from datetime import date, timedelta

def test_prediction_service():
    """測試預測服務"""
    print("🤖 測試預測服務...")
    
    try:
        service = PredictionService()
        
        # 檢查模型是否已載入
        if service.models_loaded:
            print("✅ 預測模型已成功載入")
        else:
            print("❌ 預測模型未載入")
            return False
        
        # 檢查模型組件
        components = ['home_win_model', 'total_runs_model', 'feature_extractor']
        for component in components:
            if hasattr(service, component) and getattr(service, component) is not None:
                print(f"✅ {component} 已載入")
            else:
                print(f"❌ {component} 未載入")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 預測服務測試失敗: {e}")
        return False

def test_daily_predictions():
    """測試每日預測生成"""
    print("\n📅 測試每日預測生成...")
    
    try:
        service = PredictionService()
        
        # 生成今天的預測
        today = date.today()
        result = service.generate_daily_predictions(today)
        
        print(f"今天預測結果: {result}")
        
        if result.get('successful_predictions', 0) > 0:
            print(f"✅ 成功生成 {result['successful_predictions']} 個預測")
        else:
            print("ℹ️  今天沒有需要預測的比賽")
        
        # 生成明天的預測
        tomorrow = today + timedelta(days=1)
        result = service.generate_daily_predictions(tomorrow)
        
        print(f"明天預測結果: {result}")
        
        if result.get('successful_predictions', 0) > 0:
            print(f"✅ 成功生成 {result['successful_predictions']} 個明天的預測")
        else:
            print("ℹ️  明天沒有需要預測的比賽")
        
        return True
        
    except Exception as e:
        print(f"❌ 每日預測測試失敗: {e}")
        return False

def test_single_game_prediction():
    """測試單場比賽預測"""
    print("\n🎯 測試單場比賽預測...")
    
    try:
        # 找一場即將進行的比賽
        upcoming_game = Game.query.filter(
            Game.date >= date.today(),
            Game.game_status == 'scheduled'
        ).first()
        
        if not upcoming_game:
            print("ℹ️  沒有找到即將進行的比賽，使用最近的比賽進行測試")
            upcoming_game = Game.query.filter(
                Game.date >= date.today() - timedelta(days=7)
            ).first()
        
        if not upcoming_game:
            print("❌ 沒有找到合適的比賽進行測試")
            return False
        
        print(f"測試比賽: {upcoming_game.away_team} @ {upcoming_game.home_team} ({upcoming_game.date})")
        
        service = PredictionService()
        
        # 生成預測
        prediction = service.generate_game_prediction(upcoming_game.game_id)
        
        if prediction:
            print("✅ 成功生成單場比賽預測")
            print(f"   主隊勝率: {prediction.home_win_probability:.3f}")
            print(f"   總分預測: {prediction.predicted_total_runs:.1f}")
            print(f"   信心度: {prediction.confidence:.3f}")
            return True
        else:
            print("❌ 單場比賽預測失敗")
            return False
        
    except Exception as e:
        print(f"❌ 單場比賽預測測試失敗: {e}")
        return False

def test_prediction_accuracy():
    """測試預測準確率統計"""
    print("\n📊 測試預測準確率統計...")
    
    try:
        service = PredictionService()
        
        # 獲取準確率統計
        accuracy_stats = service.get_prediction_accuracy_stats(days=30)
        
        print(f"準確率統計結果: {accuracy_stats}")
        
        if accuracy_stats:
            print("✅ 成功獲取預測準確率統計")
            
            if 'total_predictions' in accuracy_stats:
                print(f"   總預測數: {accuracy_stats['total_predictions']}")
            if 'home_win_accuracy' in accuracy_stats:
                print(f"   主隊勝負準確率: {accuracy_stats['home_win_accuracy']:.3f}")
            if 'total_runs_mae' in accuracy_stats:
                print(f"   總分預測誤差: {accuracy_stats['total_runs_mae']:.2f}")
            
            return True
        else:
            print("ℹ️  沒有足夠的歷史預測數據進行統計")
            return True
        
    except Exception as e:
        print(f"❌ 預測準確率測試失敗: {e}")
        return False

def test_automated_predictor():
    """測試自動化預測器"""
    print("\n🔄 測試自動化預測器...")
    
    try:
        # 測試手動生成預測
        result = automated_predictor.manual_generate_predictions()
        
        print(f"自動化預測結果: {result}")
        
        if result.get('successful_predictions', 0) > 0:
            print(f"✅ 自動化預測器成功生成 {result['successful_predictions']} 個預測")
        else:
            print("ℹ️  自動化預測器沒有生成新預測（可能今天沒有比賽）")
        
        # 測試系統狀態
        status = automated_predictor.get_system_status()
        
        print(f"系統狀態: {status}")
        
        if status.get('models_ready'):
            print("✅ 自動化預測器模型就緒")
        else:
            print("❌ 自動化預測器模型未就緒")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 自動化預測器測試失敗: {e}")
        return False

def check_prediction_database():
    """檢查預測數據庫"""
    print("\n💾 檢查預測數據庫...")
    
    try:
        total_predictions = Prediction.query.count()
        print(f"總預測數: {total_predictions}")
        
        # 檢查最近的預測
        recent_predictions = Prediction.query.filter(
            Prediction.prediction_date >= date.today() - timedelta(days=7)
        ).count()
        
        print(f"最近7天預測: {recent_predictions}")
        
        # 檢查最新預測
        latest_prediction = Prediction.query.order_by(
            Prediction.prediction_date.desc()
        ).first()
        
        if latest_prediction:
            print(f"最新預測日期: {latest_prediction.prediction_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ 預測數據庫檢查失敗: {e}")
        return False

def main():
    """主測試函數"""
    app = create_app()
    
    with app.app_context():
        print("🚀 MLB預測系統測試")
        print("=" * 50)
        
        tests = [
            ("預測服務", test_prediction_service),
            ("每日預測生成", test_daily_predictions),
            ("單場比賽預測", test_single_game_prediction),
            ("預測準確率統計", test_prediction_accuracy),
            ("自動化預測器", test_automated_predictor),
            ("預測數據庫", check_prediction_database)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n--- 測試 {test_name} ---")
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} 測試異常: {e}")
                results.append((test_name, False))
        
        print("\n" + "="*50)
        print("📋 預測系統測試結果摘要:")
        print("="*50)
        
        all_passed = True
        for test_name, result in results:
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
        
        print("="*50)
        if all_passed:
            print("🎉 所有預測系統測試通過！")
        else:
            print("⚠️  部分預測系統測試失敗")
        
        return all_passed

if __name__ == '__main__':
    main()
