#!/usr/bin/env python3
"""
簡化的大小分預測系統測試
避免Flask Blueprint重複註冊問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from models.database import db, Game, Prediction
from models.over_under_predictor import OverUnderPredictor
from datetime import date, datetime
import json

def create_test_app():
    """創建測試用的Flask應用"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///mlb_data.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    return app

def test_over_under_predictor():
    """測試大小分預測器"""
    print("=" * 60)
    print("測試大小分預測系統")
    print("=" * 60)
    
    app = create_test_app()
    
    with app.app_context():
        # 初始化預測器
        try:
            over_under_predictor = OverUnderPredictor()
            print("✅ 大小分預測器初始化成功")
        except Exception as e:
            print(f"❌ 大小分預測器初始化失敗: {str(e)}")
            return False
        
        # 獲取測試比賽
        games = Game.query.filter(
            Game.game_status.in_(['scheduled', 'pre-game', 'final'])
        ).order_by(Game.date.desc()).limit(3).all()
        
        if not games:
            print("❌ 沒有可測試的比賽")
            return False
        
        print(f"📊 找到 {len(games)} 場比賽進行測試")
        
        success_count = 0
        total_count = len(games)
        
        for i, game in enumerate(games, 1):
            print(f"\n🏟️  測試比賽 {i}/{total_count}: {game.away_team} @ {game.home_team}")
            print(f"   比賽日期: {game.date}")
            print(f"   比賽狀態: {game.game_status}")
            
            try:
                # 測試大小分預測
                prediction_result = over_under_predictor.predict_over_under(game.game_id)
                
                if 'error' in prediction_result:
                    print(f"   ❌ 預測失敗: {prediction_result['error']}")
                    continue
                
                # 顯示預測結果
                print(f"   ✅ 預測成功!")
                
                expected_runs = prediction_result.get('expected_runs', {})
                print(f"   📈 預期總分: {expected_runs.get('total', 'N/A')}")
                print(f"   🎯 大小分盤口: {prediction_result.get('total_line', 'N/A')}")
                print(f"   📊 大分概率: {prediction_result.get('over_probability', 0):.1%}")
                print(f"   📉 小分概率: {prediction_result.get('under_probability', 0):.1%}")
                print(f"   🎲 信心度: {prediction_result.get('confidence', 0):.1%}")
                print(f"   💡 推薦: {prediction_result.get('recommendation', 'N/A')}")
                
                # 顯示投手信息
                pitcher_info = prediction_result.get('pitcher_info', {})
                if pitcher_info:
                    print(f"   🥎 主隊投手: {pitcher_info.get('home_pitcher', '未確認')}")
                    print(f"   🥎 客隊投手: {pitcher_info.get('away_pitcher', '未確認')}")
                
                # 顯示關鍵因素
                key_factors = prediction_result.get('key_factors', [])
                if key_factors:
                    print(f"   🔑 關鍵因素:")
                    for factor in key_factors[:3]:  # 只顯示前3個
                        print(f"      • {factor}")
                
                success_count += 1
                
            except Exception as e:
                print(f"   ❌ 測試失敗: {str(e)}")
                import traceback
                traceback.print_exc()
        
        print(f"\n📊 測試結果: {success_count}/{total_count} 成功")
        return success_count > 0

def test_database_schema():
    """測試數據庫架構"""
    print("\n" + "=" * 60)
    print("測試數據庫架構")
    print("=" * 60)
    
    app = create_test_app()
    
    with app.app_context():
        try:
            # 檢查Prediction模型是否有新的大小分字段
            prediction = Prediction.query.first()
            
            if not prediction:
                print("⚠️  沒有預測數據進行測試")
                return True
            
            # 檢查新字段
            new_fields = [
                'predicted_total_runs',
                'over_under_line', 
                'over_probability',
                'under_probability',
                'over_under_confidence',
                'pitcher_vs_batter_analysis',
                'starting_pitcher_home',
                'starting_pitcher_away',
                'pitcher_matchup_advantage'
            ]
            
            missing_fields = []
            existing_fields = []
            
            for field in new_fields:
                if hasattr(prediction, field):
                    existing_fields.append(field)
                else:
                    missing_fields.append(field)
            
            print(f"✅ 現有字段 ({len(existing_fields)}): {', '.join(existing_fields)}")
            
            if missing_fields:
                print(f"❌ 缺少字段 ({len(missing_fields)}): {', '.join(missing_fields)}")
                print("💡 請運行數據庫遷移來添加新字段")
                return False
            else:
                print("✅ 數據庫架構檢查通過")
                return True
                
        except Exception as e:
            print(f"❌ 數據庫架構測試失敗: {str(e)}")
            return False

def test_pitcher_matchup_analysis():
    """測試投手對戰分析"""
    print("\n" + "=" * 60)
    print("測試投手對戰分析")
    print("=" * 60)
    
    app = create_test_app()
    
    with app.app_context():
        try:
            from models.pitcher_batter_matchup_analyzer import PitcherBatterMatchupAnalyzer
            
            analyzer = PitcherBatterMatchupAnalyzer()
            print("✅ 投手對戰分析器初始化成功")
            
            # 獲取一場比賽進行測試
            game = Game.query.filter(
                Game.game_status == 'final'
            ).first()
            
            if not game:
                print("⚠️  沒有已完成的比賽進行測試")
                return True
            
            print(f"🏟️  測試比賽: {game.away_team} @ {game.home_team}")
            
            # 測試分析功能
            analysis_result = analyzer.analyze_game_matchups(game.game_id)
            
            if analysis_result:
                print("✅ 投手對戰分析成功")
                print(f"   📊 分析結果包含 {len(analysis_result)} 個關鍵數據")
                return True
            else:
                print("⚠️  投手對戰分析返回空結果")
                return True
                
        except Exception as e:
            print(f"❌ 投手對戰分析測試失敗: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_prediction_components():
    """測試預測組件"""
    print("\n" + "=" * 60)
    print("測試預測組件")
    print("=" * 60)
    
    app = create_test_app()
    
    with app.app_context():
        components_status = {}
        
        # 測試投手姓名匹配器
        try:
            from models.pitcher_name_matcher import PitcherNameMatcher
            matcher = PitcherNameMatcher()
            components_status['投手姓名匹配器'] = True
            print("✅ 投手姓名匹配器初始化成功")
        except Exception as e:
            components_status['投手姓名匹配器'] = False
            print(f"❌ 投手姓名匹配器初始化失敗: {str(e)}")
        
        # 測試每日陣容獲取器
        try:
            from models.daily_lineup_fetcher import DailyLineupFetcher
            fetcher = DailyLineupFetcher()
            components_status['每日陣容獲取器'] = True
            print("✅ 每日陣容獲取器初始化成功")
        except Exception as e:
            components_status['每日陣容獲取器'] = False
            print(f"❌ 每日陣容獲取器初始化失敗: {str(e)}")
        
        # 測試投手對戰分析器
        try:
            from models.pitcher_batter_matchup_analyzer import PitcherBatterMatchupAnalyzer
            analyzer = PitcherBatterMatchupAnalyzer()
            components_status['投手對戰分析器'] = True
            print("✅ 投手對戰分析器初始化成功")
        except Exception as e:
            components_status['投手對戰分析器'] = False
            print(f"❌ 投手對戰分析器初始化失敗: {str(e)}")
        
        success_count = sum(components_status.values())
        total_count = len(components_status)
        
        print(f"\n📊 組件測試結果: {success_count}/{total_count} 成功")
        return success_count == total_count

def main():
    """主測試函數"""
    print("🚀 開始大小分預測系統測試")
    
    tests = [
        ("數據庫架構", test_database_schema),
        ("預測組件", test_prediction_components),
        ("投手對戰分析", test_pitcher_matchup_analysis),
        ("大小分預測器", test_over_under_predictor)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 測試 {test_name} 出現異常: {str(e)}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("測試總結")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！大小分預測系統已準備就緒")
    else:
        print("⚠️  部分測試失敗，請檢查相關功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
