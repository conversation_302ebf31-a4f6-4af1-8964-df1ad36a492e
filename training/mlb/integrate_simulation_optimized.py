#!/usr/bin/env python3
"""
整合優化預測器到模擬系統
將優化預測器整合到現有的模擬測試系統中，並備存測試結果
"""

import sys
import os
import json
from datetime import date, datetime, timedelta

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction, Team
from models.optimized_predictor import OptimizedMLBPredictor

def backup_current_simulation_results():
    """備存當前模擬系統的測試結果"""
    print("💾 備存當前模擬系統測試結果")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        # 創建備份目錄
        backup_dir = os.path.join(os.path.dirname(__file__), 'simulation_backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # 備存所有預測記錄
        all_predictions = Prediction.query.all()
        
        backup_data = {
            'backup_date': datetime.now().isoformat(),
            'total_predictions': len(all_predictions),
            'model_versions': {},
            'predictions_by_date': {},
            'accuracy_stats': {}
        }
        
        # 統計模型版本
        for pred in all_predictions:
            version = pred.model_version
            if version not in backup_data['model_versions']:
                backup_data['model_versions'][version] = 0
            backup_data['model_versions'][version] += 1
        
        # 按日期分組預測
        for pred in all_predictions:
            game = Game.query.filter_by(game_id=pred.game_id).first()
            if game:
                date_str = game.date.isoformat()
                if date_str not in backup_data['predictions_by_date']:
                    backup_data['predictions_by_date'][date_str] = []
                
                backup_data['predictions_by_date'][date_str].append({
                    'game_id': pred.game_id,
                    'home_team': game.home_team,
                    'away_team': game.away_team,
                    'predicted_home_score': pred.predicted_home_score,
                    'predicted_away_score': pred.predicted_away_score,
                    'home_win_probability': pred.home_win_probability,
                    'model_version': pred.model_version,
                    'confidence': pred.confidence,
                    'actual_home_score': pred.actual_home_score,
                    'actual_away_score': pred.actual_away_score,
                    'is_correct': pred.is_correct
                })
        
        # 計算準確率統計
        for version in backup_data['model_versions']:
            version_predictions = [p for p in all_predictions if p.model_version == version]
            correct_predictions = [p for p in version_predictions if p.is_correct is True]
            
            backup_data['accuracy_stats'][version] = {
                'total': len(version_predictions),
                'correct': len(correct_predictions),
                'accuracy': len(correct_predictions) / len(version_predictions) if version_predictions else 0
            }
        
        # 保存備份文件
        backup_filename = f"simulation_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 備份完成: {backup_path}")
        print(f"   總預測記錄: {backup_data['total_predictions']}")
        print(f"   模型版本: {list(backup_data['model_versions'].keys())}")
        print(f"   日期範圍: {len(backup_data['predictions_by_date'])} 天")
        
        # 顯示準確率統計
        print(f"\n📊 各模型準確率:")
        for version, stats in backup_data['accuracy_stats'].items():
            if stats['total'] > 0:
                print(f"   {version}: {stats['correct']}/{stats['total']} ({stats['accuracy']:.1%})")
        
        return backup_path

def integrate_optimized_to_simulation():
    """將優化預測器整合到模擬系統"""
    print(f"\n🔧 整合優化預測器到模擬系統")
    print("=" * 80)
    
    # 1. 修改模擬視圖以支持優化預測器
    simulation_view_path = os.path.join(os.path.dirname(__file__), 'views', 'simulation.py')
    
    # 讀取當前文件
    with open(simulation_view_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 檢查是否已經整合
    if 'OptimizedMLBPredictor' in content:
        print("✅ 優化預測器已經整合到模擬系統")
        return True
    
    # 添加優化預測器導入
    import_line = "from models.optimized_predictor import OptimizedMLBPredictor\n"
    
    # 在現有導入後添加
    if "from models.database import db, Game, Prediction, Team" in content:
        content = content.replace(
            "from models.database import db, Game, Prediction, Team",
            "from models.database import db, Game, Prediction, Team\nfrom models.optimized_predictor import OptimizedMLBPredictor"
        )
    
    # 添加優化預測功能
    optimized_function = '''
@simulation_bp.route('/api/optimized_simulate', methods=['POST'])
def api_optimized_simulate():
    """API: 使用優化預測器進行模擬"""
    try:
        data = request.get_json()
        date_str = data.get('date')
        
        if not date_str:
            return jsonify({'success': False, 'error': '請提供日期'}), 400
        
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # 使用優化預測器
        predictor = OptimizedMLBPredictor()
        
        # 訓練模型
        training_result = predictor.train_models(target_date)
        if not training_result['success']:
            return jsonify({
                'success': False,
                'error': f'模型訓練失敗: {training_result.get("error")}'
            }), 500
        
        # 獲取該日期的比賽
        games = Game.query.filter(Game.date == target_date).all()
        
        results = []
        for game in games:
            prediction = predictor.predict_game(
                game.home_team, game.away_team, game.date
            )
            
            if prediction['success']:
                results.append({
                    'game_id': game.game_id,
                    'teams': f"{game.away_team} @ {game.home_team}",
                    'prediction': prediction['predictions'],
                    'model_info': prediction['model_info']
                })
        
        return jsonify({
            'success': True,
            'date': date_str,
            'training_stats': training_result['training_stats'],
            'predictions': results,
            'total_games': len(results)
        })
        
    except Exception as e:
        logger.error(f"優化模擬失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

'''
    
    # 在文件末尾添加新功能
    content += optimized_function
    
    # 寫回文件
    with open(simulation_view_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 優化預測器已整合到模擬系統")
    return True

def update_simulation_template():
    """更新模擬模板以支持優化預測器"""
    print(f"\n🎨 更新模擬模板")
    print("=" * 80)
    
    template_path = os.path.join(os.path.dirname(__file__), 'templates', 'simulation', 'date_simulation.html')
    
    # 讀取模板
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 檢查是否已經更新
    if 'optimized-simulate-btn' in content:
        print("✅ 模擬模板已經更新")
        return True
    
    # 添加優化預測按鈕
    button_html = '''
                        <div class="col-md-3">
                            <button type="button" class="btn btn-success" id="optimized-simulate-btn">
                                <i class="fas fa-rocket"></i> 優化預測
                            </button>
                        </div>'''
    
    # 在現有按鈕後添加
    if '<button type="submit" class="btn btn-primary">' in content:
        content = content.replace(
            '                        </div>',
            '                        </div>' + button_html,
            1  # 只替換第一個
        )
    
    # 添加JavaScript
    js_code = '''
<script>
document.getElementById('optimized-simulate-btn').addEventListener('click', function() {
    const dateInput = document.getElementById('date');
    const selectedDate = dateInput.value;
    
    if (!selectedDate) {
        alert('請選擇日期');
        return;
    }
    
    // 顯示載入狀態
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 處理中...';
    this.disabled = true;
    
    // 發送請求
    fetch('/simulation/api/optimized_simulate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            date: selectedDate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 顯示結果
            showOptimizedResults(data);
        } else {
            alert('優化預測失敗: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('請求失敗: ' + error.message);
    })
    .finally(() => {
        // 恢復按鈕狀態
        this.innerHTML = '<i class="fas fa-rocket"></i> 優化預測';
        this.disabled = false;
    });
});

function showOptimizedResults(data) {
    // 創建結果顯示區域
    const resultsHtml = `
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle"></i> 優化預測完成</h5>
            <p>日期: ${data.date}</p>
            <p>總比賽: ${data.total_games} 場</p>
            <p>訓練統計: 勝負模型 ${data.training_stats.win_training_games} 場, 得分模型 ${data.training_stats.score_training_games} 場</p>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>比賽</th>
                        <th>預測得分</th>
                        <th>勝率</th>
                        <th>信心</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.predictions.map(pred => `
                        <tr>
                            <td>${pred.teams}</td>
                            <td>${pred.prediction.away_score} - ${pred.prediction.home_score}</td>
                            <td>${pred.prediction.predicted_winner} (${(pred.prediction.home_win_probability * 100).toFixed(1)}%)</td>
                            <td>${pred.prediction.confidence_level}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    // 顯示結果
    const resultsContainer = document.getElementById('optimized-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = resultsHtml;
    } else {
        // 創建結果容器
        const container = document.createElement('div');
        container.id = 'optimized-results';
        container.className = 'mt-4';
        container.innerHTML = resultsHtml;
        document.querySelector('.container-fluid').appendChild(container);
    }
}
</script>'''
    
    # 在</body>前添加JavaScript
    if '</body>' in content:
        content = content.replace('</body>', js_code + '\n</body>')
    
    # 寫回文件
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 模擬模板已更新")
    return True

def test_integrated_system():
    """測試整合後的系統"""
    print(f"\n🧪 測試整合後的系統")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        # 測試優化預測器
        predictor = OptimizedMLBPredictor()
        
        # 測試日期
        test_date = date(2025, 7, 4)
        
        print(f"📅 測試日期: {test_date}")
        
        # 訓練模型
        training_result = predictor.train_models(test_date)
        
        if training_result['success']:
            print("✅ 模型訓練成功")
            stats = training_result['training_stats']
            print(f"   勝負模型: {stats['win_training_games']} 場比賽")
            print(f"   得分模型: {stats['score_training_games']} 場比賽")
        else:
            print(f"❌ 模型訓練失敗: {training_result.get('error')}")
            return False
        
        # 測試預測
        test_games = Game.query.filter_by(date=test_date).limit(3).all()
        
        print(f"\n🎯 測試預測 ({len(test_games)} 場比賽):")
        
        for i, game in enumerate(test_games, 1):
            prediction = predictor.predict_game(
                game.home_team, game.away_team, game.date
            )
            
            if prediction['success']:
                pred = prediction['predictions']
                print(f"   [{i}] {game.away_team} @ {game.home_team}: "
                      f"{pred['away_score']:.1f}-{pred['home_score']:.1f} "
                      f"({pred['predicted_winner']}, {pred['confidence_level']})")
            else:
                print(f"   [{i}] {game.away_team} @ {game.home_team}: 預測失敗")
        
        return True

if __name__ == "__main__":
    print("🚀 開始整合優化預測器到模擬系統")
    print("=" * 80)
    
    # 1. 備存當前結果
    backup_path = backup_current_simulation_results()
    
    # 2. 整合優化預測器
    if integrate_optimized_to_simulation():
        print("✅ 後端整合完成")
    else:
        print("❌ 後端整合失敗")
        exit(1)
    
    # 3. 更新模板
    if update_simulation_template():
        print("✅ 前端整合完成")
    else:
        print("❌ 前端整合失敗")
        exit(1)
    
    # 4. 測試系統
    if test_integrated_system():
        print("✅ 系統測試通過")
    else:
        print("❌ 系統測試失敗")
        exit(1)
    
    print(f"\n🎉 整合完成!")
    print(f"   📁 備份文件: {backup_path}")
    print(f"   🌐 模擬頁面: http://localhost:5500/simulation/date_simulation")
    print(f"   🚀 新功能: 優化預測按鈕")
    print(f"   📊 支持模型: optimized_v1.0")
