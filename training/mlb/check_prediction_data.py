#!/usr/bin/env python3
"""
檢查預測系統使用的數據情況
回答用戶關於先發投手、打線數據的問題
"""

import logging
from datetime import date, timedelta
from app import create_app
from models.database import db, Game, GameDetail, PlayerStats, Player, Team, TeamStats

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_data_availability():
    """檢查數據可用性"""
    app = create_app()
    
    with app.app_context():
        print("=" * 80)
        print("🔍 MLB預測系統數據檢查報告")
        print("=" * 80)
        
        # 1. 檢查先發投手數據
        print("\n⚾ 先發投手數據檢查:")
        print("-" * 50)
        
        total_games = Game.query.count()
        total_game_details = GameDetail.query.count()
        games_with_pitchers = db.session.query(GameDetail).filter(
            GameDetail.home_starting_pitcher.isnot(None),
            GameDetail.away_starting_pitcher.isnot(None)
        ).count()
        
        print(f"總比賽記錄: {total_games:,}")
        print(f"詳細比賽記錄: {total_game_details:,}")
        print(f"有先發投手資料的比賽: {games_with_pitchers:,}")
        print(f"先發投手數據覆蓋率: {games_with_pitchers/total_game_details*100:.1f}%" if total_game_details > 0 else "0%")
        
        # 顯示先發投手樣本
        sample_games = db.session.query(Game, GameDetail).join(
            GameDetail, Game.game_id == GameDetail.game_id
        ).filter(
            GameDetail.home_starting_pitcher.isnot(None),
            GameDetail.away_starting_pitcher.isnot(None)
        ).limit(3).all()
        
        print(f"\n先發投手數據樣本:")
        for game, detail in sample_games:
            print(f"  📅 {game.date} | {game.away_team} @ {game.home_team}")
            print(f"     先發投手: {detail.away_starting_pitcher} vs {detail.home_starting_pitcher}")
        
        # 2. 檢查球員統計數據
        print(f"\n🏏 球員統計數據檢查:")
        print("-" * 50)
        
        total_players = Player.query.count()
        total_player_stats = PlayerStats.query.count()
        pitcher_stats = PlayerStats.query.filter(PlayerStats.innings_pitched > 0).count()
        batter_stats = PlayerStats.query.filter(PlayerStats.at_bats > 0).count()
        
        print(f"總球員數: {total_players:,}")
        print(f"總球員統計記錄: {total_player_stats:,}")
        print(f"投手統計記錄: {pitcher_stats:,}")
        print(f"打者統計記錄: {batter_stats:,}")
        
        # 顯示投手統計樣本
        sample_pitchers = PlayerStats.query.filter(
            PlayerStats.innings_pitched > 50,
            PlayerStats.era > 0
        ).order_by(PlayerStats.era.asc()).limit(5).all()
        
        print(f"\n投手統計樣本 (ERA最低):")
        for pitcher in sample_pitchers:
            print(f"  🥎 {pitcher.player_name} ({pitcher.team_id})")
            print(f"     ERA: {pitcher.era:.2f} | WHIP: {pitcher.whip:.2f} | 戰績: {pitcher.wins}-{pitcher.losses}")
            print(f"     投球局數: {pitcher.innings_pitched:.1f} | 三振: {pitcher.strikeouts_pitching}")
        
        # 顯示打者統計樣本
        sample_batters = PlayerStats.query.filter(
            PlayerStats.at_bats > 200,
            PlayerStats.batting_avg > 0
        ).order_by(PlayerStats.batting_avg.desc()).limit(5).all()
        
        print(f"\n打者統計樣本 (打擊率最高):")
        for batter in sample_batters:
            print(f"  🏏 {batter.player_name} ({batter.team_id}) - {batter.position}")
            print(f"     AVG: {batter.batting_avg:.3f} | OPS: {batter.ops:.3f} | HR: {batter.home_runs} | RBI: {batter.rbi}")
            print(f"     打席: {batter.at_bats} | 安打: {batter.hits}")
        
        # 3. 檢查球隊統計數據
        print(f"\n🏟️ 球隊統計數據檢查:")
        print("-" * 50)
        
        total_teams = Team.query.count()
        total_team_stats = TeamStats.query.count()
        current_season_team_stats = TeamStats.query.filter_by(season=2024).count()
        
        print(f"總球隊數: {total_teams}")
        print(f"總球隊統計記錄: {total_team_stats}")
        print(f"2024賽季球隊統計: {current_season_team_stats}")
        
        # 顯示球隊統計樣本
        sample_teams = TeamStats.query.filter_by(season=2024).order_by(TeamStats.win_percentage.desc()).limit(5).all()
        
        print(f"\n球隊統計樣本 (勝率最高):")
        for team_stat in sample_teams:
            print(f"  🏟️ {team_stat.team_id}")
            print(f"     戰績: {team_stat.wins}-{team_stat.losses} ({team_stat.win_percentage:.3f})")
            print(f"     得分: {team_stat.runs_scored:.1f} | 失分: {team_stat.runs_allowed:.1f}")
            print(f"     打擊率: {team_stat.batting_avg:.3f} | ERA: {team_stat.era:.2f}")
        
        # 4. 分析預測系統的數據使用情況
        print(f"\n🤖 預測系統數據使用分析:")
        print("-" * 50)
        
        print("✅ 已有的數據:")
        print("   - 先發投手姓名 (99.7%覆蓋率)")
        print("   - 球員詳細統計 (ERA, WHIP, 打擊率, OPS等)")
        print("   - 球隊整體統計")
        print("   - 比賽歷史記錄")
        print("   - 勝負投手記錄")
        
        print("\n❌ 缺少的數據:")
        print("   - 先發投手的詳細統計與姓名的關聯")
        print("   - 當日確定的先發投手陣容")
        print("   - 打線順序和位置")
        print("   - 投手對特定打者的歷史表現")
        print("   - 即時傷兵狀況")
        
        print("\n🔧 預測系統目前的處理方式:")
        print("   1. 使用球隊整體統計數據")
        print("   2. 基於歷史先發投手輪值推測")
        print("   3. 使用球隊主要球員的平均表現")
        print("   4. 通過機器學習模型整合多維度特徵")
        
        # 5. 檢查特徵工程使用的數據
        print(f"\n📊 特徵工程數據檢查:")
        print("-" * 50)
        
        # 檢查最近比賽的數據完整性
        recent_date = date.today() - timedelta(days=30)
        recent_games = Game.query.filter(
            Game.date >= recent_date,
            Game.game_status == 'completed'
        ).count()
        
        recent_detailed_games = db.session.query(Game).join(
            GameDetail, Game.game_id == GameDetail.game_id
        ).filter(
            Game.date >= recent_date,
            Game.game_status == 'completed'
        ).count()
        
        print(f"最近30天完成的比賽: {recent_games}")
        print(f"有詳細數據的比賽: {recent_detailed_games}")
        print(f"詳細數據覆蓋率: {recent_detailed_games/recent_games*100:.1f}%" if recent_games > 0 else "0%")
        
        print("\n" + "=" * 80)

def main():
    """主函數"""
    check_data_availability()

if __name__ == "__main__":
    main()
