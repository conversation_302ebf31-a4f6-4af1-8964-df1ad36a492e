#!/usr/bin/env python3
"""
檢查預測數據結構和可用性
"""

from app import create_app
from models.database import db, PredictionHistory, Game
from datetime import datetime, timedelta, date
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_prediction_data():
    """檢查預測數據"""
    app = create_app()
    
    with app.app_context():
        print("🔍 檢查預測數據結構...")
        
        # 檢查預測歷史表
        total_predictions = PredictionHistory.query.count()
        print(f"   總預測記錄數: {total_predictions}")
        
        if total_predictions == 0:
            print("   ❌ 沒有預測歷史記錄")
            return
        
        # 檢查有實際結果的預測
        predictions_with_actual = PredictionHistory.query.filter(
            PredictionHistory.actual_away_score.isnot(None),
            PredictionHistory.actual_home_score.isnot(None)
        ).count()
        
        print(f"   有實際結果的預測: {predictions_with_actual}")
        
        # 檢查最近的預測記錄
        recent_predictions = PredictionHistory.query.order_by(
            PredictionHistory.prediction_date.desc()
        ).limit(10).all()
        
        print(f"\n📊 最近的預測記錄:")
        for i, pred in enumerate(recent_predictions[:5], 1):
            print(f"   {i}. {pred.game_id} ({pred.prediction_date})")
            print(f"      預測: {pred.predicted_away_score:.1f} - {pred.predicted_home_score:.1f}")
            print(f"      實際: {pred.actual_away_score} - {pred.actual_home_score}")
            confidence_str = f"{pred.overall_confidence:.3f}" if pred.overall_confidence is not None else "N/A"
            print(f"      信心度: {confidence_str}")
            print()
        
        # 檢查比賽表
        total_games = Game.query.count()
        print(f"   總比賽記錄數: {total_games}")
        
        games_with_scores = Game.query.filter(
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).count()
        
        print(f"   有比分的比賽: {games_with_scores}")
        
        # 檢查最近的比賽記錄
        recent_games = Game.query.filter(
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).order_by(Game.date.desc()).limit(5).all()
        
        print(f"\n🏟️ 最近的比賽結果:")
        for i, game in enumerate(recent_games, 1):
            print(f"   {i}. {game.game_id} ({game.date})")
            print(f"      {game.away_team} {game.away_score} - {game.home_score} {game.home_team}")
            print()
        
        # 檢查JOIN查詢
        print("🔗 檢查預測記錄與比賽記錄的關聯...")
        
        # 嘗試不同的JOIN方式
        join_query = db.session.query(PredictionHistory, Game).join(
            Game, PredictionHistory.game_id == Game.game_id
        ).filter(
            PredictionHistory.actual_away_score.isnot(None),
            PredictionHistory.actual_home_score.isnot(None),
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        )
        
        joined_count = join_query.count()
        print(f"   JOIN查詢結果: {joined_count} 條記錄")
        
        if joined_count > 0:
            # 顯示幾個成功JOIN的例子
            examples = join_query.limit(3).all()
            print("   ✅ JOIN成功的例子:")
            for pred, game in examples:
                print(f"      {pred.game_id}: 預測({pred.predicted_away_score:.1f}-{pred.predicted_home_score:.1f}) vs 實際({game.away_score}-{game.home_score})")
        
        # 檢查日期範圍
        print("\n📅 檢查日期範圍...")
        
        # 預測記錄的日期範圍
        pred_dates = db.session.query(
            db.func.min(PredictionHistory.prediction_date),
            db.func.max(PredictionHistory.prediction_date)
        ).first()
        
        if pred_dates[0]:
            print(f"   預測記錄日期範圍: {pred_dates[0]} 到 {pred_dates[1]}")
        
        # 比賽記錄的日期範圍
        game_dates = db.session.query(
            db.func.min(Game.date),
            db.func.max(Game.date)
        ).first()
        
        if game_dates[0]:
            print(f"   比賽記錄日期範圍: {game_dates[0]} 到 {game_dates[1]}")
        
        # 檢查最近7天的數據
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        recent_pred_count = PredictionHistory.query.filter(
            PredictionHistory.prediction_date >= start_date,
            PredictionHistory.prediction_date <= end_date
        ).count()
        
        recent_game_count = Game.query.filter(
            Game.date >= start_date,
            Game.date <= end_date,
            Game.home_score.isnot(None)
        ).count()
        
        print(f"\n📊 最近7天數據統計:")
        print(f"   預測記錄: {recent_pred_count}")
        print(f"   完成比賽: {recent_game_count}")

if __name__ == "__main__":
    check_prediction_data()