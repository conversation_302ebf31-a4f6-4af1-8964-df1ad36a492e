#!/usr/bin/env python3
"""
修復部分缺失比分的已完成比賽
針對1606場completed但只有部分比分的比賽進行修復
"""

import sys
sys.path.append('.')

import os
import sqlite3
from datetime import datetime, timedelta
import time
import requests

def fix_partial_boxscores():
    """修復部分缺失的boxscore數據"""
    
    print("🔧 修復部分缺失boxscore數據")
    print("=" * 60)
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    try:
        # 連接數據庫
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        print("✅ 已連接數據庫: instance/mlb_data.db")
        
        # 1. 查找部分缺失比分的比賽
        print("\n🔍 查找部分缺失比分的比賽...")
        
        cursor.execute("""
            SELECT game_id, date, home_team, away_team, home_score, away_score, game_status
            FROM games 
            WHERE game_status = 'completed'
            AND (
                (home_score IS NULL AND away_score IS NOT NULL) OR
                (home_score IS NOT NULL AND away_score IS NULL)
            )
            ORDER BY date DESC
        """)
        
        partial_games = cursor.fetchall()
        
        if not partial_games:
            print("   ✅ 沒有發現部分缺失比分的比賽")
            conn.close()
            return True
        
        print(f"   🎯 發現 {len(partial_games)} 場比賽需要修復")
        
        # 顯示前10場作為示例
        print(f"\n📋 需要修復的比賽示例:")
        for i, (game_id, date, home, away, h_score, a_score, status) in enumerate(partial_games[:10]):
            missing_part = "主隊分數" if h_score is None else "客隊分數"
            print(f"   {i+1:2d}. {date} | {away} vs {home} | 比分: {a_score}-{h_score} | 缺失: {missing_part}")
        
        if len(partial_games) > 10:
            print(f"   ... 還有 {len(partial_games) - 10} 場比賽需要修復")
        
        # 2. 嘗試從box_scores表中修復
        print(f"\n🔧 嘗試從box_scores表修復數據...")
        
        fixed_count = 0
        
        for game_id, date, home_team, away_team, home_score, away_score, status in partial_games:
            
            # 從box_scores表獲取比分數據
            cursor.execute("""
                SELECT 
                    runs,
                    is_home
                FROM box_scores 
                WHERE game_id = ?
                ORDER BY is_home
            """, (game_id,))
            
            box_scores = cursor.fetchall()
            
            if len(box_scores) == 2:
                # 有完整的box_scores數據
                away_runs = None
                home_runs = None
                
                for runs, is_home in box_scores:
                    if is_home:
                        home_runs = runs
                    else:
                        away_runs = runs
                
                if away_runs is not None and home_runs is not None:
                    # 更新games表中的比分
                    update_needed = False
                    new_home_score = home_score if home_score is not None else home_runs
                    new_away_score = away_score if away_score is not None else away_runs
                    
                    if home_score != new_home_score or away_score != new_away_score:
                        cursor.execute("""
                            UPDATE games 
                            SET home_score = ?, away_score = ?, updated_at = ?
                            WHERE game_id = ?
                        """, (new_home_score, new_away_score, datetime.now(), game_id))
                        
                        fixed_count += 1
                        if fixed_count <= 5:  # 顯示前5個修復
                            print(f"   ✅ 修復 {game_id}: {away_team} {new_away_score}-{new_home_score} {home_team}")
        
        # 提交更改
        conn.commit()
        
        print(f"\n🎉 修復完成:")
        print(f"   ✅ 成功修復 {fixed_count} 場比賽的比分數據")
        print(f"   📊 剩餘需要從外部API獲取的: {len(partial_games) - fixed_count} 場")
        
        # 3. 檢查修復後的覆蓋率
        print(f"\n📊 修復後覆蓋率:")
        
        cursor.execute("""
            SELECT 
                COUNT(*) as total_completed,
                SUM(CASE WHEN home_score IS NOT NULL AND away_score IS NOT NULL THEN 1 ELSE 0 END) as with_scores
            FROM games 
            WHERE game_status = 'completed'
        """)
        
        post_fix_data = cursor.fetchone()
        if post_fix_data:
            total_completed, with_scores = post_fix_data
            new_rate = (with_scores / total_completed * 100) if total_completed > 0 else 0
            print(f"   📈 新覆蓋率: {new_rate:.1f}% ({with_scores}/{total_completed})")
            
            remaining_missing = total_completed - with_scores
            print(f"   ⚠️ 仍缺失: {remaining_missing} 場")
        
        # 4. 為剩餘缺失數據生成修復建議
        if len(partial_games) - fixed_count > 0:
            print(f"\n💡 針對剩餘 {len(partial_games) - fixed_count} 場比賽的建議:")
            print(f"   🔧 需要從外部MLB API獲取boxscore數據")
            print(f"   📡 建議實現MLB Stats API整合")
            print(f"   🔄 可以運行批量boxscore下載程序")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 MLB Boxscore 部分缺失數據修復工具")
    print("針對已完成但缺失部分比分的比賽進行修復")
    print("")
    
    success = fix_partial_boxscores()
    
    if success:
        print(f"\n✅ 修復程序執行完成")
        print(f"💡 檢查修復結果並考慮是否需要從外部API補充剩餘數據")
    else:
        print(f"\n❌ 修復失敗，請檢查錯誤信息")