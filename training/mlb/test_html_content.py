#!/usr/bin/env python3
"""
檢查HTML內容
"""

from app import create_app
import re

def test_html_content():
    """檢查HTML內容中的數據"""
    app = create_app()
    
    with app.test_client() as client:
        print("🔍 檢查HTML內容")
        print("=" * 50)
        
        response = client.get('/admin/historical-odds-dashboard')
        
        if response.status_code == 200:
            content = response.get_data(as_text=True)
            
            print(f"📄 響應長度: {len(content)} 字符")
            
            # 提取數字統計
            numbers = re.findall(r'<span class="fs-4 fw-bold[^>]*>(\d+)', content)
            print(f"📊 找到的統計數字: {numbers}")
            
            # 檢查特定的數據值
            if '5200' in content:
                print("✅ 找到總記錄數: 5200")
            if '3591' in content:
                print("✅ 找到讓分盤記錄: 3591") 
            if '1562' in content:
                print("✅ 找到大小分記錄: 1562")
            
            # 檢查錯誤消息
            if 'error' in content.lower() and 'object has no attribute' in content:
                print("⚠️ 仍然存在錯誤")
            
            # 查找覆蓋天數
            coverage_match = re.search(r'覆蓋天數.*?(\d+)', content)
            if coverage_match:
                print(f"📅 覆蓋天數: {coverage_match.group(1)}")
            
            # 檢查日期範圍
            if '2025-06-01' in content:
                print("✅ 找到開始日期: 2025-06-01")
            if '2025-08-25' in content:
                print("✅ 找到結束日期: 2025-08-25")
                
        else:
            print(f"❌ 請求失敗: {response.status_code}")

if __name__ == "__main__":
    test_html_content()