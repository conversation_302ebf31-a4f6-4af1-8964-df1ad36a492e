#!/usr/bin/env python3
"""
測試投手對打者對戰數據庫建立
"""

import sys
import os

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.pitcher_batter_matchups import PitcherBatterMatchupAnalyzer

def test_matchup_database():
    """測試對戰數據庫建立"""
    print("=" * 80)
    print("⚾ 投手對打者對戰數據庫測試")
    print("=" * 80)
    
    analyzer = PitcherBatterMatchupAnalyzer()
    
    # 測試1: 從最近7天的比賽建立對戰數據庫
    print(f"\n📊 測試1: 建立最近7天的對戰數據庫")
    print("-" * 50)
    
    matchup_db = analyzer.build_matchup_database_from_recent_games(days_back=7)
    
    print(f"處理比賽數: {matchup_db['total_games_processed']}")
    print(f"總對戰數: {matchup_db['total_matchups_found']}")
    print(f"獨特投手-打者組合: {matchup_db['unique_pitcher_batter_pairs']}")
    
    # 顯示一些有趣的對戰統計
    print(f"\n🎯 有趣的對戰統計:")
    
    # 找出對戰次數最多的組合
    top_matchups = sorted(
        matchup_db['matchups'].items(),
        key=lambda x: x[1]['plate_appearances'],
        reverse=True
    )[:10]
    
    print(f"\n對戰次數最多的組合:")
    for i, (pair_key, stats) in enumerate(top_matchups, 1):
        if stats['plate_appearances'] > 0:
            avg = stats['batting_average'] if stats['total_at_bats'] > 0 else 0
            print(f"  {i}. {stats['pitcher_name']} vs {stats['batter_name']}")
            print(f"     打席: {stats['plate_appearances']}, 打數: {stats['total_at_bats']}, 打擊率: {avg:.3f}")
    
    # 找出打擊率最高的對戰（至少3個打數）
    high_avg_matchups = [
        (pair_key, stats) for pair_key, stats in matchup_db['matchups'].items()
        if stats['total_at_bats'] >= 3
    ]
    
    if high_avg_matchups:
        high_avg_matchups.sort(key=lambda x: x[1]['batting_average'], reverse=True)
        
        print(f"\n打擊率最高的對戰 (至少3個打數):")
        for i, (pair_key, stats) in enumerate(high_avg_matchups[:5], 1):
            print(f"  {i}. {stats['batter_name']} vs {stats['pitcher_name']}")
            print(f"     {stats['hits']}/{stats['total_at_bats']} = {stats['batting_average']:.3f}")
    
    # 找出三振率最高的投手對戰
    strikeout_matchups = [
        (pair_key, stats) for pair_key, stats in matchup_db['matchups'].items()
        if stats['plate_appearances'] >= 3
    ]
    
    if strikeout_matchups:
        strikeout_matchups.sort(
            key=lambda x: x[1]['strikeouts'] / x[1]['plate_appearances'] if x[1]['plate_appearances'] > 0 else 0,
            reverse=True
        )
        
        print(f"\n三振率最高的投手對戰 (至少3個打席):")
        for i, (pair_key, stats) in enumerate(strikeout_matchups[:5], 1):
            strikeout_rate = stats['strikeouts'] / stats['plate_appearances'] * 100
            print(f"  {i}. {stats['pitcher_name']} vs {stats['batter_name']}")
            print(f"     三振: {stats['strikeouts']}/{stats['plate_appearances']} = {strikeout_rate:.1f}%")
    
    # 測試2: 測試特定比賽的對戰提取
    print(f"\n📊 測試2: 特定比賽對戰提取")
    print("-" * 50)
    
    # 獲取最近一場比賽的ID
    with analyzer.app.app_context():
        from models.database import Game
        from datetime import date, timedelta
        
        recent_game = Game.query.filter(
            Game.game_status == 'completed',
            Game.date >= date.today() - timedelta(days=3)
        ).first()
        
        if recent_game:
            print(f"測試比賽: {recent_game.away_team} @ {recent_game.home_team} ({recent_game.date})")
            
            all_matchups = analyzer.get_all_game_matchups(recent_game.game_id)
            print(f"總對戰數: {len(all_matchups)}")
            
            if all_matchups:
                print(f"\n前5個對戰:")
                for i, matchup in enumerate(all_matchups[:5], 1):
                    print(f"  {i}. 第{matchup['inning']}局: {matchup['batter_name']} vs {matchup['pitcher_name']}")
                    print(f"     結果: {matchup['result']}")
                
                # 統計這場比賽的投手表現
                pitcher_stats = {}
                for matchup in all_matchups:
                    pitcher_id = matchup['pitcher_id']
                    if pitcher_id not in pitcher_stats:
                        pitcher_stats[pitcher_id] = {
                            'name': matchup['pitcher_name'],
                            'batters_faced': 0,
                            'strikeouts': 0,
                            'hits_allowed': 0,
                            'walks': 0
                        }
                    
                    stats = pitcher_stats[pitcher_id]
                    stats['batters_faced'] += 1
                    
                    result = matchup['result'].lower()
                    if 'strikeout' in result or 'strikes out' in result:
                        stats['strikeouts'] += 1
                    elif any(x in result for x in ['single', 'double', 'triple', 'home run']):
                        stats['hits_allowed'] += 1
                    elif 'walk' in result:
                        stats['walks'] += 1
                
                print(f"\n投手表現統計:")
                for pitcher_id, stats in pitcher_stats.items():
                    if stats['batters_faced'] >= 5:  # 只顯示面對至少5個打者的投手
                        strikeout_rate = stats['strikeouts'] / stats['batters_faced'] * 100
                        print(f"  {stats['name']}: 面對{stats['batters_faced']}人, 三振{stats['strikeouts']}人 ({strikeout_rate:.1f}%)")
                        print(f"    被安打{stats['hits_allowed']}支, 保送{stats['walks']}次")
        else:
            print("❌ 找不到最近的已完成比賽")
    
    # 測試3: 數據質量分析
    print(f"\n📊 測試3: 數據質量分析")
    print("-" * 50)
    
    if matchup_db['unique_pitcher_batter_pairs'] > 0:
        # 分析對戰數據的分布
        pa_distribution = {}
        for stats in matchup_db['matchups'].values():
            pa = stats['plate_appearances']
            pa_range = f"{pa//5*5}-{pa//5*5+4}" if pa < 20 else "20+"
            pa_distribution[pa_range] = pa_distribution.get(pa_range, 0) + 1
        
        print(f"對戰次數分布:")
        for pa_range, count in sorted(pa_distribution.items()):
            percentage = count / matchup_db['unique_pitcher_batter_pairs'] * 100
            print(f"  {pa_range}次: {count}組 ({percentage:.1f}%)")
        
        # 計算有意義對戰的比例（至少3次對戰）
        meaningful_matchups = sum(1 for stats in matchup_db['matchups'].values() if stats['plate_appearances'] >= 3)
        meaningful_percentage = meaningful_matchups / matchup_db['unique_pitcher_batter_pairs'] * 100
        
        print(f"\n有意義對戰 (≥3次): {meaningful_matchups}/{matchup_db['unique_pitcher_batter_pairs']} ({meaningful_percentage:.1f}%)")
        
        # 計算平均統計
        total_pa = sum(stats['plate_appearances'] for stats in matchup_db['matchups'].values())
        avg_pa_per_pair = total_pa / matchup_db['unique_pitcher_batter_pairs']
        
        print(f"平均每組對戰次數: {avg_pa_per_pair:.1f}")
    
    return matchup_db

def main():
    """主函數"""
    test_matchup_database()

if __name__ == "__main__":
    main()
