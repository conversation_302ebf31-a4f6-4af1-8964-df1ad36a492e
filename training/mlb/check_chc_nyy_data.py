#!/usr/bin/env python3
"""
檢查 CHC @ NYY 比賽的詳細數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime
from app import create_app
from models.database import db, Game, BoxScore, PlayerStats, Prediction
from models.daily_lineup_fetcher import DailyLineupFetcher
import json

def check_chc_nyy_game_data():
    """檢查 CHC @ NYY 比賽的詳細數據"""
    app = create_app()
    
    with app.app_context():
        print("🔍 檢查 CHC @ NYY 比賽數據...")
        
        # 查找 2025-07-12 的 CHC @ NYY 比賽
        game = Game.query.filter(
            Game.date == date(2025, 7, 12),
            Game.away_team == 'CHC',
            Game.home_team == 'NYY'
        ).first()
        
        if not game:
            print("❌ 未找到 CHC @ NYY 比賽記錄")
            return
        
        print(f"📅 比賽信息:")
        print(f"  Game ID: {game.game_id}")
        print(f"  日期: {game.date}")
        print(f"  狀態: {game.game_status}")
        print(f"  比分: {game.away_score} - {game.home_score}")
        
        # 檢查預測數據
        prediction = Prediction.query.filter_by(game_id=game.game_id).first()
        if prediction:
            print(f"\n🎯 預測數據:")
            print(f"  預測比分: {prediction.predicted_away_score:.1f} - {prediction.predicted_home_score:.1f}")
            print(f"  預測總分: {prediction.predicted_away_score + prediction.predicted_home_score:.1f}")
            print(f"  信心度: {prediction.confidence:.1%}")
            print(f"  預測時間: {prediction.created_at}")
        
        # 檢查打線數據
        print(f"\n⚾ 打線數據:")

        # 使用 DailyLineupFetcher 獲取打線信息
        lineup_fetcher = DailyLineupFetcher()
        try:
            daily_data = lineup_fetcher.get_daily_lineups(game.date)

            # 查找對應的比賽
            target_game = None
            for game_info in daily_data.get('games', []):
                if (game_info.get('away_team') == 'CHC' and
                    game_info.get('home_team') == 'NYY'):
                    target_game = game_info
                    break

            if target_game:
                print(f"  ✅ 找到比賽打線數據")

                # 先發投手
                print(f"  先發投手:")
                print(f"    客隊 CHC: {target_game.get('away_pitcher', '未知')}")
                print(f"    主隊 NYY: {target_game.get('home_pitcher', '未知')}")

                # 打線信息
                home_lineup = target_game.get('home_lineup', [])
                away_lineup = target_game.get('away_lineup', [])

                if away_lineup:
                    print(f"  客隊 CHC 打線 ({len(away_lineup)} 人):")
                    for i, player in enumerate(away_lineup[:3]):
                        print(f"    {i+1}棒: {player.get('player_name', 'Unknown')} ({player.get('position', 'Unknown')})")
                else:
                    print(f"  客隊 CHC: ❌ 無打線數據")

                if home_lineup:
                    print(f"  主隊 NYY 打線 ({len(home_lineup)} 人):")
                    for i, player in enumerate(home_lineup[:3]):
                        print(f"    {i+1}棒: {player.get('player_name', 'Unknown')} ({player.get('position', 'Unknown')})")
                else:
                    print(f"  主隊 NYY: ❌ 無打線數據")
            else:
                print("  ❌ 未找到對應比賽的打線數據")

        except Exception as e:
            print(f"  ❌ 獲取打線數據失敗: {e}")
        
        # 檢查 BoxScore 數據
        print(f"\n📊 BoxScore 數據:")
        boxscores = BoxScore.query.filter_by(game_id=game.game_id).all()
        
        if boxscores:
            for boxscore in boxscores:
                team_type = "主隊 NYY" if boxscore.is_home else "客隊 CHC"
                print(f"  {team_type}:")
                print(f"    得分: {boxscore.runs}")
                print(f"    安打: {boxscore.hits}")
                print(f"    失誤: {boxscore.errors}")
                print(f"    殘壘: {boxscore.left_on_base}")
        else:
            print("  ❌ 無 BoxScore 數據")
        
        # 檢查球員統計
        print(f"\n👥 相關球員統計:")
        
        # CHC 球員
        chc_players = PlayerStats.query.filter_by(team_id='CHC').order_by(PlayerStats.batting_avg.desc()).limit(3).all()
        print(f"  CHC 主要打者:")
        for player in chc_players:
            if player.at_bats > 100:
                print(f"    {player.player_name}: AVG {player.batting_avg:.3f}, HR {player.home_runs}, RBI {player.rbi}")
        
        # NYY 球員
        nyy_players = PlayerStats.query.filter_by(team_id='NYY').order_by(PlayerStats.batting_avg.desc()).limit(3).all()
        print(f"  NYY 主要打者:")
        for player in nyy_players:
            if player.at_bats > 100:
                print(f"    {player.player_name}: AVG {player.batting_avg:.3f}, HR {player.home_runs}, RBI {player.rbi}")
        
        # 投手統計
        print(f"\n🥎 投手統計:")
        
        # CHC 投手
        chc_pitchers = PlayerStats.query.filter(
            PlayerStats.team_id == 'CHC',
            PlayerStats.innings_pitched > 50
        ).order_by(PlayerStats.era.asc()).limit(3).all()
        print(f"  CHC 主要投手:")
        for pitcher in chc_pitchers:
            print(f"    {pitcher.player_name}: ERA {pitcher.era:.2f}, 戰績 {pitcher.wins}-{pitcher.losses}")
        
        # NYY 投手
        nyy_pitchers = PlayerStats.query.filter(
            PlayerStats.team_id == 'NYY',
            PlayerStats.innings_pitched > 50
        ).order_by(PlayerStats.era.asc()).limit(3).all()
        print(f"  NYY 主要投手:")
        for pitcher in nyy_pitchers:
            print(f"    {pitcher.player_name}: ERA {pitcher.era:.2f}, 戰績 {pitcher.wins}-{pitcher.losses}")

def analyze_prediction_factors():
    """分析預測因子"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔬 分析預測因子...")
        
        # 查找最近 CHC 和 NYY 的比賽
        print(f"\n📈 最近比賽表現:")
        
        # CHC 最近5場
        chc_recent = Game.query.filter(
            ((Game.home_team == 'CHC') | (Game.away_team == 'CHC')),
            Game.game_status == 'completed',
            Game.date < date(2025, 7, 12)
        ).order_by(Game.date.desc()).limit(5).all()
        
        print(f"  CHC 最近5場:")
        chc_total_scores = []
        for game in chc_recent:
            if game.home_score is not None and game.away_score is not None:
                total_score = game.home_score + game.away_score
                chc_total_scores.append(total_score)
                vs_team = game.away_team if game.home_team == 'CHC' else game.home_team
                print(f"    {game.date} vs {vs_team}: 總分 {total_score}")
        
        if chc_total_scores:
            chc_avg = sum(chc_total_scores) / len(chc_total_scores)
            print(f"    CHC 最近平均總分: {chc_avg:.1f}")
        
        # NYY 最近5場
        nyy_recent = Game.query.filter(
            ((Game.home_team == 'NYY') | (Game.away_team == 'NYY')),
            Game.game_status == 'completed',
            Game.date < date(2025, 7, 12)
        ).order_by(Game.date.desc()).limit(5).all()
        
        print(f"  NYY 最近5場:")
        nyy_total_scores = []
        for game in nyy_recent:
            if game.home_score is not None and game.away_score is not None:
                total_score = game.home_score + game.away_score
                nyy_total_scores.append(total_score)
                vs_team = game.away_team if game.home_team == 'NYY' else game.home_team
                print(f"    {game.date} vs {vs_team}: 總分 {total_score}")
        
        if nyy_total_scores:
            nyy_avg = sum(nyy_total_scores) / len(nyy_total_scores)
            print(f"    NYY 最近平均總分: {nyy_avg:.1f}")
        
        # 分析為什麼預測這麼高
        if chc_total_scores and nyy_total_scores:
            combined_avg = (chc_avg + nyy_avg) / 2
            print(f"\n🤔 預測分析:")
            print(f"  兩隊最近平均總分: {combined_avg:.1f}")
            print(f"  實際預測總分: 16.5")
            print(f"  預測偏高: {16.5 - combined_avg:.1f} 分")
            
            if combined_avg < 10:
                print(f"  ⚠️ 警告: 最近比賽總分偏低，預測可能過於樂觀")

if __name__ == "__main__":
    print("🚀 檢查 CHC @ NYY 比賽數據...")
    
    # 1. 檢查比賽基本數據
    check_chc_nyy_game_data()
    
    # 2. 分析預測因子
    analyze_prediction_factors()
    
    print("\n✅ 檢查完成！")
