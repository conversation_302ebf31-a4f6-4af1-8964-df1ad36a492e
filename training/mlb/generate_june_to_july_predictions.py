#!/usr/bin/env python3
"""
重新生成6月到7/10的完整預測數據，確保使用真實博彩盤口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from datetime import datetime, date, timed<PERSON><PERSON>

def generate_comprehensive_predictions():
    """生成6月到7/10的完整預測"""
    print("🔄 生成6月到7/10的完整預測數據")
    print("=" * 70)
    
    from models.unified_betting_predictor import UnifiedBettingPredictor
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        # 獲取所有有真實博彩盤口的比賽
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT DISTINCT 
                g.game_id, 
                g.date, 
                g.home_team, 
                g.away_team,
                bo.total_point,
                g.home_score,
                g.away_score
            FROM games g
            JOIN betting_odds bo ON g.game_id = bo.game_id
            WHERE bo.market_type = 'totals'
            AND bo.bookmaker = 'bet365'
            AND g.date >= '2025-06-01' 
            AND g.date <= '2025-07-10'
            ORDER BY g.date, g.game_id
        """)
        
        all_games = cursor.fetchall()
        conn.close()
        
        print(f"   找到 {len(all_games)} 場有真實博彩盤口的比賽")
        
        predictor = UnifiedBettingPredictor()
        
        # 按日期分組處理
        games_by_date = {}
        for game_data in all_games:
            game_date = game_data[1]
            if game_date not in games_by_date:
                games_by_date[game_date] = []
            games_by_date[game_date].append(game_data)
        
        total_generated = 0
        total_updated = 0
        
        for game_date_str in sorted(games_by_date.keys()):
            games = games_by_date[game_date_str]
            game_date = datetime.strptime(game_date_str, '%Y-%m-%d').date()
            training_end_date = game_date - timedelta(days=1)
            
            print(f"\n📅 處理日期: {game_date} ({len(games)} 場比賽)")
            
            for game_id, _, home_team, away_team, real_line, home_score, away_score in games:
                try:
                    # 檢查是否已有預測
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    cursor.execute("""
                        SELECT id FROM predictions
                        WHERE game_id = ? AND model_version = 'unified_v1.0'
                    """, (game_id,))
                    
                    existing = cursor.fetchone()
                    conn.close()
                    
                    if existing:
                        # 更新現有預測確保使用真實盤口
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        
                        actual_total = (home_score + away_score) if home_score is not None and away_score is not None else None
                        
                        cursor.execute("""
                            UPDATE predictions 
                            SET 
                                over_under_line = ?,
                                actual_total_runs = ?,
                                updated_at = ?
                            WHERE game_id = ? AND model_version = 'unified_v1.0'
                        """, (real_line, actual_total, datetime.now().isoformat(), game_id))
                        
                        conn.commit()
                        conn.close()
                        total_updated += 1
                        
                    else:
                        # 生成新預測
                        result = predictor.predict_game_comprehensive(
                            game_id,
                            target_date=game_date,
                            training_end_date=training_end_date
                        )
                        
                        if result:
                            total_generated += 1
                            print(f"   ✅ {away_team}@{home_team}: 盤口 {real_line}")
                        else:
                            print(f"   ❌ {away_team}@{home_team}: 預測失敗")
                            
                except Exception as e:
                    print(f"   ⚠️  {away_team}@{home_team} 處理失敗: {e}")
                    continue
        
        print(f"\n✅ 處理完成:")
        print(f"   新生成預測: {total_generated}")
        print(f"   更新現有預測: {total_updated}")
        
    except Exception as e:
        print(f"❌ 生成預測失敗: {e}")
        import traceback
        traceback.print_exc()

def recalculate_all_accuracy():
    """重新計算所有預測的準確性"""
    print("\n🔄 重新計算所有預測準確性")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 重新計算所有預測的準確性
        cursor.execute("""
            UPDATE predictions 
            SET 
                actual_total_runs = (
                    SELECT g.home_score + g.away_score 
                    FROM games g 
                    WHERE g.game_id = predictions.game_id
                    AND g.home_score IS NOT NULL 
                    AND g.away_score IS NOT NULL
                ),
                is_correct = CASE 
                    WHEN (
                        SELECT g.home_score + g.away_score 
                        FROM games g 
                        WHERE g.game_id = predictions.game_id
                        AND g.home_score IS NOT NULL 
                        AND g.away_score IS NOT NULL
                    ) > predictions.over_under_line THEN 
                        CASE WHEN predictions.over_probability > 0.5 THEN 1 ELSE 0 END
                    WHEN (
                        SELECT g.home_score + g.away_score 
                        FROM games g 
                        WHERE g.game_id = predictions.game_id
                        AND g.home_score IS NOT NULL 
                        AND g.away_score IS NOT NULL
                    ) <= predictions.over_under_line THEN 
                        CASE WHEN predictions.under_probability > 0.5 THEN 1 ELSE 0 END
                    ELSE NULL
                END,
                updated_at = ?
            WHERE model_version = 'unified_v1.0'
            AND game_id IN (
                SELECT g.game_id 
                FROM games g 
                WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
            )
        """, (datetime.now().isoformat(),))
        
        updated_count = cursor.rowcount
        print(f"   ✅ 重新計算了 {updated_count} 個預測的準確性")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"❌ 重新計算失敗: {e}")

def final_verification():
    """最終驗證"""
    print("\n📊 最終數據驗證")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 按月統計
        cursor.execute("""
            SELECT 
                SUBSTR(g.date, 1, 7) as month,
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN p.is_correct = 1 THEN 1 END) as correct,
                COUNT(CASE WHEN p.is_correct IS NOT NULL THEN 1 END) as with_results,
                AVG(p.over_under_line) as avg_line,
                COUNT(CASE WHEN p.actual_total_runs > p.over_under_line THEN 1 END) as actual_overs,
                COUNT(CASE WHEN p.over_probability > 0.5 THEN 1 END) as predicted_overs
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
            AND p.model_version = 'unified_v1.0'
            GROUP BY SUBSTR(g.date, 1, 7)
            ORDER BY month
        """)
        
        results = cursor.fetchall()
        
        print("月份    | 預測數 | 正確 | 有結果 | 準確率 | 平均盤口 | 實際Over | 預測Over")
        print("-" * 80)
        
        total_all = 0
        total_correct = 0
        total_with_results = 0
        
        for month, total, correct, with_results, avg_line, actual_overs, predicted_overs in results:
            accuracy = (correct / with_results * 100) if with_results > 0 else 0
            avg_line_str = f"{avg_line:.1f}" if avg_line else "N/A"
            
            print(f"{month} | {total:6d} | {correct:4d} | {with_results:6d} | {accuracy:6.1f}% | {avg_line_str:8s} | {actual_overs:8d} | {predicted_overs:8d}")
            
            total_all += total
            total_correct += correct
            total_with_results += with_results
        
        overall_accuracy = (total_correct / total_with_results * 100) if total_with_results > 0 else 0
        
        print("-" * 80)
        print(f"總計    | {total_all:6d} | {total_correct:4d} | {total_with_results:6d} | {overall_accuracy:6.1f}%")
        
        # 檢查數據完整性
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT g.date) as total_days,
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN p.actual_total_runs IS NOT NULL THEN 1 END) as with_actual_scores
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
            AND p.model_version = 'unified_v1.0'
        """)
        
        days, predictions, with_scores = cursor.fetchone()
        
        print(f"\n📈 數據完整性:")
        print(f"   涵蓋天數: {days}")
        print(f"   總預測數: {predictions}")
        print(f"   有實際比分: {with_scores}")
        print(f"   完成率: {(with_scores/predictions*100):.1f}%")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")

if __name__ == '__main__':
    generate_comprehensive_predictions()
    recalculate_all_accuracy()
    final_verification()
    
    print("\n" + "=" * 70)
    print("🎯 完成！6月到7/10的預測數據已準備就緒")
    print("所有數據都使用真實的bet365博彩盤口")
    print("可以用於分析預測錯誤並進行模型校正")
    print("=" * 70)
