#!/usr/bin/env python3
"""
測試投手詳細統計功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.pitcher_detailed_stats import PitcherDetailedStats
from models.database import db
from app import app
import j<PERSON>

def test_pitcher_stats():
    """測試投手統計功能"""
    
    with app.app_context():
        print("=== 投手詳細統計測試 ===\n")
        
        # 測試投手名稱列表
        test_pitchers = [
            "<PERSON><PERSON><PERSON>",
            "<PERSON>", 
            "<PERSON>",
            "<PERSON>",
            "<PERSON>"
        ]
        
        stats_analyzer = PitcherDetailedStats()
        
        for pitcher_name in test_pitchers:
            print(f"📊 測試投手: {pitcher_name}")
            print("-" * 50)
            
            try:
                # 獲取投手統計
                pitcher_stats = stats_analyzer.get_pitcher_comprehensive_stats(pitcher_name)
                
                if pitcher_stats.get('error'):
                    print(f"❌ 錯誤: {pitcher_stats['error']}")
                    continue
                
                # 顯示基本信息
                basic_info = pitcher_stats.get('basic_info', {})
                print(f"姓名: {basic_info.get('name', 'Unknown')}")
                print(f"球隊: {basic_info.get('team', 'Unknown')}")
                print(f"投球手: {basic_info.get('throws', 'Unknown')}")
                print(f"年齡: {basic_info.get('age', 0)}")
                
                # 顯示生涯統計
                career_stats = pitcher_stats.get('career_stats', {})
                print(f"\n生涯統計:")
                print(f"  出賽場次: {career_stats.get('games', 0)}")
                print(f"  勝-敗: {career_stats.get('wins', 0)}-{career_stats.get('losses', 0)}")
                print(f"  防禦率: {career_stats.get('era', 0):.2f}")
                print(f"  WHIP: {career_stats.get('whip', 0):.2f}")
                print(f"  三振數: {career_stats.get('strikeouts', 0)}")
                print(f"  投球局數: {career_stats.get('innings_pitched', 0):.1f}")
                
                # 顯示最近表現
                recent_performance = pitcher_stats.get('recent_performance', {})
                last_5_games = recent_performance.get('last_5_games', [])
                print(f"\n最近5場比賽: {len(last_5_games)} 場")
                for i, game in enumerate(last_5_games[:3]):  # 只顯示前3場
                    print(f"  {i+1}. {game.get('date', 'Unknown')} - "
                          f"局數: {game.get('innings_pitched', 0):.1f}, "
                          f"ERA: {game.get('era', 0):.2f}, "
                          f"K: {game.get('strikeouts', 0)}")
                
                # 顯示逐年統計
                season_stats = pitcher_stats.get('season_by_season', [])
                print(f"\n逐年統計: {len(season_stats)} 年")
                for season in season_stats[:3]:  # 只顯示前3年
                    print(f"  {season.get('season', 0)}: "
                          f"{season.get('games', 0)}場, "
                          f"{season.get('wins', 0)}-{season.get('losses', 0)}, "
                          f"ERA: {season.get('era', 0):.2f}")
                
                # 顯示對各隊統計
                vs_teams_stats = pitcher_stats.get('vs_teams_stats', {})
                print(f"\n對各隊統計: {len(vs_teams_stats)} 支球隊")
                for team, stats in list(vs_teams_stats.items())[:3]:  # 只顯示前3支球隊
                    print(f"  vs {team}: "
                          f"{stats.get('games', 0)}場, "
                          f"ERA: {stats.get('era', 0):.2f}")
                
                print(f"\n✅ {pitcher_name} 統計獲取成功")
                
            except Exception as e:
                print(f"❌ 測試 {pitcher_name} 失敗: {e}")
            
            print("\n" + "="*60 + "\n")

def test_pitcher_detail_route():
    """測試投手詳細頁面路由"""
    
    with app.test_client() as client:
        print("=== 測試投手詳細頁面路由 ===\n")
        
        test_pitchers = ["Gerrit Cole", "Jacob deGrom"]
        
        for pitcher_name in test_pitchers:
            print(f"🌐 測試路由: /admin/pitcher_details/{pitcher_name}")
            
            try:
                response = client.get(f'/admin/pitcher_details/{pitcher_name}')
                
                print(f"狀態碼: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 頁面載入成功")
                    # 檢查頁面內容
                    content = response.get_data(as_text=True)
                    if pitcher_name in content:
                        print(f"✅ 頁面包含投手姓名: {pitcher_name}")
                    if "生涯統計" in content:
                        print("✅ 頁面包含生涯統計區塊")
                    if "最近表現" in content:
                        print("✅ 頁面包含最近表現區塊")
                else:
                    print(f"❌ 頁面載入失敗: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 路由測試失敗: {e}")
            
            print("-" * 50)

def test_api_endpoint():
    """測試API端點"""
    
    with app.test_client() as client:
        print("=== 測試API端點 ===\n")
        
        test_pitcher = "Gerrit Cole"
        
        print(f"🔗 測試API: /admin/api/pitcher_stats/{test_pitcher}")
        
        try:
            response = client.get(f'/admin/api/pitcher_stats/{test_pitcher}')
            
            print(f"狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                print("✅ API回應成功")
                
                # 檢查數據結構
                expected_keys = ['basic_info', 'career_stats', 'recent_performance', 
                               'season_by_season', 'vs_teams_stats', 'vs_batters_stats']
                
                for key in expected_keys:
                    if key in data:
                        print(f"✅ 包含 {key} 數據")
                    else:
                        print(f"❌ 缺少 {key} 數據")
                
                # 顯示部分數據
                if 'basic_info' in data:
                    basic_info = data['basic_info']
                    print(f"投手姓名: {basic_info.get('name', 'Unknown')}")
                    print(f"球隊: {basic_info.get('team', 'Unknown')}")
                
                if 'career_stats' in data:
                    career_stats = data['career_stats']
                    print(f"生涯ERA: {career_stats.get('era', 0):.2f}")
                    print(f"生涯三振: {career_stats.get('strikeouts', 0)}")
                
            else:
                print(f"❌ API回應失敗: {response.status_code}")
                
        except Exception as e:
            print(f"❌ API測試失敗: {e}")

if __name__ == "__main__":
    print("開始投手詳細統計功能測試...\n")
    
    # 測試統計功能
    test_pitcher_stats()
    
    # 測試路由
    test_pitcher_detail_route()
    
    # 測試API
    test_api_endpoint()
    
    print("測試完成！")
