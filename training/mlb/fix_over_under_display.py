#!/usr/bin/env python3
"""
修復大小分顯示問題 - 確保Web界面顯示校正後的數值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction
from datetime import date
import sqlite3

def fix_over_under_display():
    """修復大小分顯示問題"""
    print("🔧 修復大小分顯示問題")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        today = date.today()
        
        # 獲取今天的所有預測
        predictions = db.session.query(Prediction).join(Game).filter(
            Game.date == today
        ).all()
        
        if not predictions:
            print("❌ 沒有找到今天的預測數據")
            return
        
        print(f"📊 找到 {len(predictions)} 個今天的預測")
        
        fixed_count = 0
        
        for pred in predictions:
            try:
                game = Game.query.filter_by(game_id=pred.game_id).first()
                if not game:
                    continue
                
                # 檢查當前數據
                current_total = pred.predicted_total_runs or 0
                current_line = pred.over_under_line or 0
                home_score = pred.predicted_home_score or 0
                away_score = pred.predicted_away_score or 0
                calculated_total = home_score + away_score
                
                print(f"\n🏟️ {game.away_team} @ {game.home_team}:")
                print(f"  當前存儲總分: {current_total:.1f}")
                print(f"  計算總分: {calculated_total:.1f}")
                print(f"  當前大小分線: {current_line:.1f}")
                
                # 如果數據不一致或過高，進行修復
                needs_fix = False
                
                if abs(current_total - calculated_total) > 0.1:
                    print(f"  ⚠️ 總分不一致，需要修復")
                    needs_fix = True
                
                if calculated_total > 12.0:
                    print(f"  ⚠️ 總分過高 ({calculated_total:.1f})，需要校正")
                    needs_fix = True
                
                if current_line > 12.0:
                    print(f"  ⚠️ 大小分線過高 ({current_line:.1f})，需要校正")
                    needs_fix = True
                
                if needs_fix:
                    # 使用校正後的分數
                    new_total = calculated_total
                    
                    # 如果總分仍然過高，應用額外校正
                    if new_total > 11.0:
                        scale_factor = 10.5 / new_total  # 縮放到10.5分
                        new_home_score = home_score * scale_factor
                        new_away_score = away_score * scale_factor
                        new_total = new_home_score + new_away_score
                        
                        # 更新預測分數
                        pred.predicted_home_score = new_home_score
                        pred.predicted_away_score = new_away_score
                        
                        print(f"  🔧 應用額外校正: {away_score:.1f}-{home_score:.1f} → {new_away_score:.1f}-{new_home_score:.1f}")
                    
                    # 更新總分
                    pred.predicted_total_runs = new_total
                    
                    # 設置合理的大小分線（通常比預測總分低0.5-1.0分）
                    new_line = round((new_total - 0.7) * 2) / 2  # 四捨五入到0.5
                    new_line = max(6.5, min(11.5, new_line))  # 限制範圍
                    pred.over_under_line = new_line
                    
                    # 重新計算概率
                    difference = new_total - new_line
                    if difference > 0.5:
                        pred.over_probability = 0.65
                        pred.under_probability = 0.35
                    elif difference < -0.5:
                        pred.over_probability = 0.35
                        pred.under_probability = 0.65
                    else:
                        pred.over_probability = 0.52
                        pred.under_probability = 0.48
                    
                    pred.over_under_confidence = min(0.8, 0.5 + abs(difference) * 0.2)
                    
                    # 更新模型版本
                    pred.model_version = 'calibrated_v1.1'
                    
                    print(f"  ✅ 修復完成: 總分={new_total:.1f}, 線={new_line:.1f}")
                    fixed_count += 1
                else:
                    print(f"  ✅ 數據正常，無需修復")
                    
            except Exception as e:
                print(f"  ❌ 修復失敗: {e}")
        
        # 提交更改
        try:
            db.session.commit()
            print(f"\n📊 修復完成:")
            print(f"  修復預測: {fixed_count} 個")
            print(f"  總計檢查: {len(predictions)} 個")
            
            if fixed_count > 0:
                print(f"\n🎯 修復效果:")
                print("  - 消除了過高的大小分數值")
                print("  - 確保總分與分數一致")
                print("  - 大小分線在合理範圍內 (6.5-11.5)")
                print("  - Web界面將顯示正確的校正後數值")
                
        except Exception as e:
            print(f"❌ 提交失敗: {e}")
            db.session.rollback()

def main():
    fix_over_under_display()

if __name__ == "__main__":
    main()
