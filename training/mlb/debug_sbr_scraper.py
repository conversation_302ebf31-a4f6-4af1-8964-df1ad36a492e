#!/usr/bin/env python3
"""
調試版本的SportsbookReview抓取器
專門用於診斷和測試HTML解析
"""

import requests
import re
import json
from datetime import date
from bs4 import BeautifulSoup

def debug_sbr_content():
    """調試SportsbookReview的HTML內容"""
    url = 'https://www.sportsbookreview.com/betting-odds/mlb-baseball/pointspread/full-game/?date=2025-07-10'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    response = requests.get(url, headers=headers, timeout=30)
    html_content = response.text
    
    print(f"📊 SportsbookReview調試報告")
    print(f"🌐 URL: {url}")
    print(f"📄 內容長度: {len(html_content):,} 字符")
    print(f"✅ HTTP狀態: {response.status_code}")
    print()
    
    # 1. 查找Next.js數據
    print("🔍 搜索Next.js數據...")
    next_data_pattern = r'<script id="__NEXT_DATA__" type="application/json">(.*?)</script>'
    next_matches = re.findall(next_data_pattern, html_content, re.DOTALL)
    print(f"   找到 {len(next_matches)} 個 __NEXT_DATA__ 區塊")
    
    if next_matches:
        try:
            next_data = json.loads(next_matches[0])
            print(f"   ✅ JSON解析成功，頂層鍵: {list(next_data.keys())}")
            
            # 深入分析結構
            if 'props' in next_data:
                props = next_data['props']
                print(f"   📁 props鍵: {list(props.keys())}")
                
                if 'pageProps' in props:
                    page_props = props['pageProps']
                    print(f"   📁 pageProps鍵: {list(page_props.keys())}")
                    
                    if 'oddsTables' in page_props:
                        odds_tables = page_props['oddsTables']
                        print(f"   🎯 找到oddsTables！包含 {len(odds_tables)} 個表格")
                        
                        # 分析第一個表格
                        if odds_tables:
                            first_table = odds_tables[0]
                            print(f"   📊 第一個表格鍵: {list(first_table.keys())}")
                            
                            if 'oddsTableModel' in first_table:
                                model = first_table['oddsTableModel']
                                print(f"   🏗️ oddsTableModel鍵: {list(model.keys())}")
                                
                                if 'gameRows' in model:
                                    game_rows = model['gameRows']
                                    print(f"   🎮 找到 {len(game_rows)} 行比賽數據!")
                                    
                                    # 顯示第一場比賽
                                    if game_rows:
                                        first_game = game_rows[0]
                                        print(f"   ⚾ 第一場比賽結構: {list(first_game.keys())}")
                                        
                                        if 'game' in first_game:
                                            game_info = first_game['game']
                                            participants = game_info.get('participants', [])
                                            if len(participants) >= 2:
                                                away = participants[0].get('source', {}).get('abbreviation', 'N/A')
                                                home = participants[1].get('source', {}).get('abbreviation', 'N/A')
                                                print(f"   🏟️ 比賽: {away} @ {home}")
                                        
                                        if 'sportsbookInfo' in first_game:
                                            sportsbooks = first_game['sportsbookInfo']
                                            print(f"   💰 博彩商數量: {len(sportsbooks)}")
                                            
                                            for sb in sportsbooks[:2]:  # 顯示前2個
                                                sb_name = sb.get('sportsbook', {}).get('name', 'N/A')
                                                print(f"      📈 {sb_name}")
                        
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON解析失敗: {e}")
    
    # 2. 使用BeautifulSoup查找表格
    print(f"\n🍜 BeautifulSoup HTML解析...")
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找可能的表格選擇器
    table_selectors = [
        'table',
        '.odds-table', 
        '.game-table',
        '[data-testid*="game"]',
        '[class*="Game"]',
        '[class*="Row"]',
        'tr'
    ]
    
    for selector in table_selectors:
        elements = soup.select(selector)
        if elements:
            print(f"   🎯 '{selector}' 找到 {len(elements)} 個元素")
            
            if selector == 'tr' and len(elements) > 10:  # 查看表格行
                # 查找包含MLB隊伍代碼的行
                team_rows = []
                mlb_teams = ['NYM', 'BAL', 'CHC', 'NYY', 'BOS', 'LAD', 'HOU', 'ATL']
                
                for row in elements[:50]:  # 檢查前50行
                    row_text = row.get_text()
                    if any(team in row_text for team in mlb_teams):
                        team_rows.append(row)
                
                if team_rows:
                    print(f"   ⚾ 找到 {len(team_rows)} 行包含MLB隊伍的數據!")
                    
                    # 分析第一行
                    first_row = team_rows[0]
                    print(f"   📊 第一行內容: {first_row.get_text()[:100]}...")
                    print(f"   🏷️ 第一行類別: {first_row.get('class')}")
    
    # 3. 正則表達式搜索
    print(f"\n🔎 正則表達式模式搜索...")
    
    patterns = {
        '時間模式': r'\d{1,2}:\d{2}\s*(?:AM|PM)\s*EDT?',
        '隊伍代碼': r'\b[A-Z]{3}\b',
        '賠率模式': r'[+-]\d+\.?\d*\s*[+-]\d+',
        '投手名稱': r'[A-Z]\.\s*[A-Za-z]+\s*\([LR]\)',
    }
    
    for pattern_name, pattern in patterns.items():
        matches = re.findall(pattern, html_content)
        print(f"   📋 {pattern_name}: 找到 {len(matches)} 個匹配")
        if matches:
            print(f"      例如: {matches[:3]}")
    
    # 4. 查找JavaScript變數
    print(f"\n🔧 搜索JavaScript變數...")
    js_patterns = [
        r'window\.__INITIAL_STATE__\s*=',
        r'window\.__APP_STATE__\s*=', 
        r'var\s+gameData\s*=',
        r'const\s+initialProps\s*=',
        r'oddsTables\s*:\s*\[',
    ]
    
    for pattern in js_patterns:
        if re.search(pattern, html_content):
            print(f"   ✅ 找到模式: {pattern}")
        else:
            print(f"   ❌ 未找到: {pattern}")

if __name__ == "__main__":
    debug_sbr_content()