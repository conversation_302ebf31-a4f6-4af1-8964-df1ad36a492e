#!/usr/bin/env python3
"""
測試MLB API數據結構
檢查2025-08-29的API數據格式
"""

import requests
import json
from pprint import pprint

def test_mlb_api_structure():
    """測試MLB API數據結構"""
    
    print("🔍 測試 MLB API 數據結構")
    print("=" * 50)
    
    target_date = '2025-08-29'
    api_url = f"https://statsapi.mlb.com/api/v1/schedule?sportId=1&date={target_date}"
    
    print(f"📡 請求URL: {api_url}")
    
    try:
        response = requests.get(api_url, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        print(f"✅ API 請求成功")
        print(f"📊 響應狀態碼: {response.status_code}")
        
        # 檢查基本結構
        print(f"\n🏗️ 數據結構分析:")
        print(f"   頂層鍵: {list(data.keys())}")
        
        if 'dates' in data:
            dates = data['dates']
            print(f"   dates 數量: {len(dates)}")
            
            if dates:
                date_entry = dates[0]
                print(f"   第一個date的鍵: {list(date_entry.keys())}")
                
                if 'games' in date_entry:
                    games = date_entry['games']
                    print(f"   games 數量: {len(games)}")
                    
                    if games:
                        # 檢查第一場比賽的結構
                        first_game = games[0]
                        print(f"\n🏟️ 第一場比賽結構:")
                        print(f"   頂層鍵: {list(first_game.keys())}")
                        
                        # 檢查teams結構
                        if 'teams' in first_game:
                            teams = first_game['teams']
                            print(f"\n👥 Teams 結構:")
                            print(f"   teams 鍵: {list(teams.keys())}")
                            
                            for side in ['away', 'home']:
                                if side in teams:
                                    team_data = teams[side]
                                    print(f"\n   {side} team 結構:")
                                    print(f"     鍵: {list(team_data.keys())}")
                                    
                                    if 'team' in team_data:
                                        team_info = team_data['team']
                                        print(f"     team info 鍵: {list(team_info.keys())}")
                                        
                                        # 打印實際的隊伍名稱和縮寫
                                        if 'name' in team_info:
                                            print(f"     隊伍名稱: {team_info['name']}")
                                        if 'abbreviation' in team_info:
                                            print(f"     隊伍縮寫: {team_info['abbreviation']}")
                        
                        # 檢查狀態結構
                        if 'status' in first_game:
                            status = first_game['status']
                            print(f"\n📊 Status 結構:")
                            print(f"   status 鍵: {list(status.keys())}")
                            for key, value in status.items():
                                print(f"     {key}: {value}")
                        
                        # 顯示完整的第一場比賽數據（簡化版）
                        print(f"\n🎯 第一場比賽完整數據（主要字段）:")
                        simplified_game = {}
                        
                        # 提取主要字段
                        for key in ['gamePk', 'gameDate', 'status', 'teams']:
                            if key in first_game:
                                if key == 'teams':
                                    # 簡化teams數據
                                    simplified_teams = {}
                                    for side in ['away', 'home']:
                                        if side in first_game['teams']:
                                            team_data = first_game['teams'][side]
                                            simplified_teams[side] = {
                                                'score': team_data.get('score'),
                                                'team': {
                                                    'id': team_data.get('team', {}).get('id'),
                                                    'name': team_data.get('team', {}).get('name'),
                                                    'abbreviation': team_data.get('team', {}).get('abbreviation')
                                                }
                                            }
                                    simplified_game[key] = simplified_teams
                                else:
                                    simplified_game[key] = first_game[key]
                        
                        pprint(simplified_game)
                        
        else:
            print(f"   ❌ 沒有找到 dates 數據")
        
        return True
        
    except requests.RequestException as e:
        print(f"❌ API 請求失敗: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 MLB API 數據結構測試工具")
    print("分析2025-08-29的API返回格式")
    print()
    
    success = test_mlb_api_structure()
    
    if success:
        print(f"\n✅ API 數據結構分析完成")
    else:
        print(f"\n❌ API 數據結構分析失敗")