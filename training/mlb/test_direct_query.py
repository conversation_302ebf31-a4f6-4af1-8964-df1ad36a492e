#!/usr/bin/env python3
"""
直接測試數據庫查詢邏輯
檢查LEFT JOIN是否正確工作
"""

from app import create_app
from models.database import db, Game, BettingOdds
from datetime import date, timedelta, datetime
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_direct_query():
    """直接測試數據庫查詢邏輯"""
    app = create_app()
    
    with app.app_context():
        print("🔍 直接測試數據庫查詢邏輯...")
        print("=" * 80)
        
        # 1. 檢查基本數據
        print("\n📊 基本數據統計...")
        total_odds = BettingOdds.query.count()
        total_games = Game.query.count()
        print(f"   總賠率記錄: {total_odds}")
        print(f"   總比賽記錄: {total_games}")
        
        # 2. 測試日期參數
        date_from = "2025-08-25"
        date_to = "2025-08-30"
        start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        
        print(f"\n🎯 測試日期範圍: {start_date} 到 {end_date}")
        
        # 3. 測試原始INNER JOIN查詢（會失敗）
        print("\n❌ 測試原始INNER JOIN查詢...")
        try:
            inner_query = db.session.query(BettingOdds, Game).join(Game, BettingOdds.game_id == Game.game_id)
            inner_query = inner_query.filter(Game.date >= start_date)
            inner_query = inner_query.filter(Game.date <= end_date)
            inner_results = inner_query.all()
            print(f"   INNER JOIN結果: {len(inner_results)} 條記錄")
        except Exception as e:
            print(f"   INNER JOIN查詢失敗: {e}")
            inner_results = []
        
        # 4. 測試LEFT JOIN查詢（應該成功）
        print("\n✅ 測試LEFT JOIN查詢...")
        try:
            left_query = db.session.query(BettingOdds, Game).outerjoin(Game, BettingOdds.game_id == Game.game_id)
            
            # 修改日期過濾邏輯以包含孤立記錄
            left_query = left_query.filter(
                db.or_(
                    db.and_(Game.date >= start_date, Game.date <= end_date),  # 有關聯的記錄
                    Game.date.is_(None)  # 孤立記錄
                )
            )
            
            left_results = left_query.limit(10).all()
            print(f"   LEFT JOIN結果: {len(left_results)} 條記錄")
            
            # 顯示前幾條記錄詳情
            for i, (betting_odds, game) in enumerate(left_results[:5]):
                if game:
                    print(f"      記錄 {i+1}: {game.away_team}@{game.home_team} ({game.date}) - {betting_odds.bookmaker}")
                else:
                    print(f"      記錄 {i+1}: 孤立記錄 (Game ID: {betting_odds.game_id}) - {betting_odds.bookmaker}")
                    
        except Exception as e:
            print(f"   LEFT JOIN查詢失敗: {e}")
            left_results = []
        
        # 5. 測試無日期過濾的LEFT JOIN
        print("\n🔍 測試無日期過濾的LEFT JOIN...")
        try:
            no_filter_query = db.session.query(BettingOdds, Game).outerjoin(Game, BettingOdds.game_id == Game.game_id)
            no_filter_results = no_filter_query.limit(10).all()
            print(f"   無過濾LEFT JOIN結果: {len(no_filter_results)} 條記錄")
            
            # 分析結果
            linked_count = sum(1 for _, game in no_filter_results if game is not None)
            orphaned_count = sum(1 for _, game in no_filter_results if game is None)
            
            print(f"      關聯記錄: {linked_count}")
            print(f"      孤立記錄: {orphaned_count}")
            
        except Exception as e:
            print(f"   無過濾LEFT JOIN查詢失敗: {e}")
        
        # 6. 檢查孤立記錄的日期範圍
        print("\n📅 檢查孤立記錄的日期範圍...")
        try:
            orphaned_odds = db.session.query(BettingOdds).outerjoin(Game, BettingOdds.game_id == Game.game_id).filter(
                Game.game_id.is_(None)
            ).all()
            
            if orphaned_odds:
                print(f"   找到 {len(orphaned_odds)} 條孤立記錄")
                
                # 檢查孤立記錄的時間戳
                dates_with_times = []
                for odds in orphaned_odds[:10]:
                    if odds.odds_time:
                        dates_with_times.append(odds.odds_time.date() if hasattr(odds.odds_time, 'date') else odds.odds_time)
                    elif odds.created_at:
                        dates_with_times.append(odds.created_at.date() if hasattr(odds.created_at, 'date') else odds.created_at)
                
                if dates_with_times:
                    print(f"      孤立記錄日期範例: {dates_with_times[:5]}")
                    
                    # 檢查是否有在查詢範圍內的孤立記錄
                    in_range_count = sum(1 for d in dates_with_times if isinstance(d, date) and start_date <= d <= end_date)
                    print(f"      在查詢範圍內的孤立記錄: {in_range_count}")
            
        except Exception as e:
            print(f"   檢查孤立記錄失敗: {e}")
        
        # 7. 測試具體的查詢邏輯
        print("\n🎯 測試具體的查詢邏輯...")
        try:
            # 模擬API中的查詢邏輯
            query = db.session.query(BettingOdds, Game).outerjoin(Game, BettingOdds.game_id == Game.game_id)
            
            # 添加日期過濾 - 修正版本
            query = query.filter(
                db.or_(
                    Game.date >= start_date,  # 有關聯的記錄在範圍內
                    db.and_(
                        Game.date.is_(None),  # 孤立記錄
                        BettingOdds.odds_time >= datetime.combine(start_date, datetime.min.time()),  # 孤立記錄的時間在範圍內
                        BettingOdds.odds_time <= datetime.combine(end_date, datetime.max.time())
                    )
                )
            )
            
            query = query.filter(Game.date <= end_date) if False else query  # 暫時不加這個過濾
            
            final_results = query.limit(10).all()
            print(f"   最終查詢結果: {len(final_results)} 條記錄")
            
            for i, (betting_odds, game) in enumerate(final_results[:3]):
                if game:
                    print(f"      記錄 {i+1}: 關聯記錄 - {game.away_team}@{game.home_team} ({game.date})")
                else:
                    print(f"      記錄 {i+1}: 孤立記錄 - Game ID: {betting_odds.game_id}, 時間: {betting_odds.odds_time}")
                    
        except Exception as e:
            print(f"   最終查詢失敗: {e}")

if __name__ == "__main__":
    test_direct_query()