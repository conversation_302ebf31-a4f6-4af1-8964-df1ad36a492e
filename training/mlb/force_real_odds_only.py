#!/usr/bin/env python3
"""
強制所有預測只使用真實博彩盤口數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from datetime import datetime, date, timedelta

def force_real_odds_only():
    """強制所有預測使用真實博彩盤口"""
    print("🔧 強制所有預測使用真實博彩盤口")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 刪除所有沒有真實博彩盤口的預測
        print("1️⃣  刪除沒有真實博彩盤口的預測:")
        cursor.execute("""
            DELETE FROM predictions 
            WHERE game_id NOT IN (
                SELECT DISTINCT game_id 
                FROM betting_odds 
                WHERE market_type = 'totals' 
                AND bookmaker = 'bet365'
            )
            AND model_version = 'unified_v1.0'
        """)
        
        deleted_count = cursor.rowcount
        print(f"   ✅ 刪除了 {deleted_count} 個沒有真實盤口的預測")
        
        # 2. 更新所有剩餘預測使用真實博彩盤口
        print("\n2️⃣  更新預測使用真實博彩盤口:")
        cursor.execute("""
            UPDATE predictions 
            SET over_under_line = (
                SELECT bo.total_point 
                FROM betting_odds bo 
                WHERE bo.game_id = predictions.game_id 
                AND bo.market_type = 'totals' 
                AND bo.bookmaker = 'bet365'
                LIMIT 1
            ),
            updated_at = ?
            WHERE model_version = 'unified_v1.0'
            AND game_id IN (
                SELECT DISTINCT game_id 
                FROM betting_odds 
                WHERE market_type = 'totals' 
                AND bookmaker = 'bet365'
            )
        """, (datetime.now().isoformat(),))
        
        updated_count = cursor.rowcount
        print(f"   ✅ 更新了 {updated_count} 個預測的盤口數據")
        
        # 3. 重新計算準確性
        print("\n3️⃣  重新計算預測準確性:")
        cursor.execute("""
            UPDATE predictions 
            SET 
                actual_total_runs = (
                    SELECT g.home_score + g.away_score 
                    FROM games g 
                    WHERE g.game_id = predictions.game_id
                    AND g.home_score IS NOT NULL 
                    AND g.away_score IS NOT NULL
                ),
                is_correct = CASE 
                    WHEN (
                        SELECT g.home_score + g.away_score 
                        FROM games g 
                        WHERE g.game_id = predictions.game_id
                        AND g.home_score IS NOT NULL 
                        AND g.away_score IS NOT NULL
                    ) > predictions.over_under_line THEN 
                        CASE WHEN predictions.over_probability > 0.5 THEN 1 ELSE 0 END
                    WHEN (
                        SELECT g.home_score + g.away_score 
                        FROM games g 
                        WHERE g.game_id = predictions.game_id
                        AND g.home_score IS NOT NULL 
                        AND g.away_score IS NOT NULL
                    ) <= predictions.over_under_line THEN 
                        CASE WHEN predictions.under_probability > 0.5 THEN 1 ELSE 0 END
                    ELSE NULL
                END,
                updated_at = ?
            WHERE model_version = 'unified_v1.0'
        """, (datetime.now().isoformat(),))
        
        accuracy_updated = cursor.rowcount
        print(f"   ✅ 重新計算了 {accuracy_updated} 個預測的準確性")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        import traceback
        traceback.print_exc()

def generate_missing_predictions():
    """為有真實盤口但沒有預測的比賽生成預測"""
    print("\n🔄 為缺失的比賽生成預測")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 找到有真實盤口但沒有預測的比賽
        cursor.execute("""
            SELECT DISTINCT 
                g.game_id, 
                g.date, 
                g.home_team, 
                g.away_team,
                bo.total_point
            FROM games g
            JOIN betting_odds bo ON g.game_id = bo.game_id
            LEFT JOIN predictions p ON g.game_id = p.game_id 
                AND p.model_version = 'unified_v1.0'
            WHERE bo.market_type = 'totals'
            AND bo.bookmaker = 'bet365'
            AND g.date >= '2025-06-01' 
            AND g.date <= '2025-07-10'
            AND p.game_id IS NULL
            ORDER BY g.date, g.game_id
        """)
        
        missing_games = cursor.fetchall()
        conn.close()
        
        print(f"   找到 {len(missing_games)} 場需要生成預測的比賽")
        
        if missing_games:
            from models.unified_betting_predictor import UnifiedBettingPredictor
            
            predictor = UnifiedBettingPredictor()
            generated_count = 0
            
            for game_id, game_date_str, home_team, away_team, real_line in missing_games:
                try:
                    game_date = datetime.strptime(game_date_str, '%Y-%m-%d').date()
                    training_end_date = game_date - timedelta(days=1)
                    
                    print(f"   📅 {game_date}: {away_team}@{home_team} (盤口: {real_line})")
                    
                    # 生成預測
                    result = predictor.predict_game_comprehensive(
                        game_id,
                        target_date=game_date,
                        training_end_date=training_end_date
                    )
                    
                    if result:
                        generated_count += 1
                        
                except Exception as e:
                    print(f"   ⚠️  {away_team}@{home_team} 預測失敗: {e}")
                    continue
            
            print(f"\n   ✅ 成功生成了 {generated_count} 個新預測")
        
    except Exception as e:
        print(f"❌ 生成預測失敗: {e}")

def verify_final_results():
    """驗證最終結果"""
    print("\n✅ 驗證最終結果")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查每日預測統計
        cursor.execute("""
            SELECT 
                g.date,
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN p.is_correct = 1 THEN 1 END) as correct,
                COUNT(CASE WHEN p.is_correct IS NOT NULL THEN 1 END) as with_results,
                AVG(p.over_under_line) as avg_line,
                MIN(p.over_under_line) as min_line,
                MAX(p.over_under_line) as max_line
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
            AND p.model_version = 'unified_v1.0'
            GROUP BY g.date
            ORDER BY g.date DESC
            LIMIT 15
        """)
        
        results = cursor.fetchall()
        
        print("日期       | 預測數 | 正確 | 有結果 | 準確率 | 平均盤口 | 盤口範圍")
        print("-" * 75)
        
        total_predictions = 0
        total_correct = 0
        total_with_results = 0
        
        for date_str, count, correct, with_results, avg_line, min_line, max_line in results:
            accuracy = (correct / with_results * 100) if with_results > 0 else 0
            line_range = f"{min_line:.1f}-{max_line:.1f}" if min_line and max_line else "N/A"
            avg_line_str = f"{avg_line:.1f}" if avg_line else "N/A"
            
            print(f"{date_str} | {count:6d} | {correct:4d} | {with_results:6d} | {accuracy:6.1f}% | {avg_line_str:8s} | {line_range}")
            
            total_predictions += count
            total_correct += correct
            total_with_results += with_results
        
        overall_accuracy = (total_correct / total_with_results * 100) if total_with_results > 0 else 0
        
        print("-" * 75)
        print(f"總計       | {total_predictions:6d} | {total_correct:4d} | {total_with_results:6d} | {overall_accuracy:6.1f}%")
        
        # 檢查是否所有預測都有真實盤口
        cursor.execute("""
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN EXISTS (
                       SELECT 1 FROM betting_odds bo 
                       WHERE bo.game_id = p.game_id 
                       AND bo.market_type = 'totals' 
                       AND bo.bookmaker = 'bet365'
                   ) THEN 1 END) as with_real_odds
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
            AND p.model_version = 'unified_v1.0'
        """)
        
        total, with_real = cursor.fetchone()
        real_odds_rate = (with_real / total * 100) if total > 0 else 0
        
        print(f"\n📊 真實盤口覆蓋率: {with_real}/{total} ({real_odds_rate:.1f}%)")
        
        if real_odds_rate == 100.0:
            print("✅ 所有預測都使用真實博彩盤口！")
        else:
            print("⚠️  仍有預測使用模擬盤口")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")

if __name__ == '__main__':
    force_real_odds_only()
    generate_missing_predictions()
    verify_final_results()
    
    print("\n" + "=" * 70)
    print("🎯 修復完成！所有預測現在都使用真實的bet365博彩盤口")
    print("可以用於分析錯誤預測並進行校正")
    print("=" * 70)
