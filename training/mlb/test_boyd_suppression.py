#!/usr/bin/env python3
"""
測試 Boyd 壓制邏輯
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from enhanced_custom_predict import EnhancedCustomPredictor
import json

def test_boyd_suppression():
    """測試 Boyd 壓制邏輯"""
    predictor = EnhancedCustomPredictor()
    target_date = date(2025, 7, 12)
    
    print("🧪 測試 Boyd 壓制邏輯...")
    print("=" * 60)
    
    # 測試 Boyd 壓制版本
    options = {
        'force_regenerate': True,
        'update_existing': True,
        'skip_existing': False,
        'backup_existing': True,
        'model_version_suffix': 'boyd_suppression',
        'exclude_postponed': True,
        'confidence_threshold': 0.0
    }
    
    print(f"🔬 使用版本: {options['model_version_suffix']}")
    print(f"📅 目標日期: {target_date}")
    print(f"🎯 重點關注: CHC @ NYY (Boyd 壓制 NYY 強打線)")
    print("-" * 60)
    
    results = predictor.predict_with_options(target_date, options)
    
    if results['success']:
        print(f"✅ 成功處理: {results['processed_games']}/{results['total_games']} 場比賽")
        
        # 重點分析 CHC @ NYY
        chc_nyy_found = False
        for pred in results['predictions']:
            if 'CHC @ NYY' in pred['matchup']:
                chc_nyy_found = True
                print(f"\n🎯 CHC @ NYY 分析:")
                print(f"  預測比分: {pred['prediction']['away_score']:.1f} - {pred['prediction']['home_score']:.1f}")
                print(f"  實際比分: {pred['actual_score']}")
                print(f"  信心度: {pred['prediction']['confidence']:.3f}")
                print(f"  模型版本: {pred['prediction'].get('model_version', 'N/A')}")
                print(f"  訓練截止: {pred['prediction'].get('training_end_date', 'N/A')}")
                
                # 分析 Boyd 壓制效果
                predicted_home = pred['prediction']['home_score']
                if predicted_home <= 3.0:
                    print(f"  ✅ Boyd 壓制生效: NYY 得分被壓制到 {predicted_home:.1f}")
                else:
                    print(f"  ❌ Boyd 壓制未生效: NYY 得分仍為 {predicted_home:.1f}")
                
                break
        
        if not chc_nyy_found:
            print("❌ 未找到 CHC @ NYY 比賽")
        
        # 顯示所有預測結果
        print(f"\n📊 所有預測結果:")
        print("-" * 60)
        for pred in results['predictions']:
            if pred['action'] == 'updated' and pred['prediction']:
                print(f"{pred['matchup']:15} | "
                      f"{pred['prediction']['away_score']:.1f}-{pred['prediction']['home_score']:.1f} | "
                      f"實際: {pred['actual_score']:8} | "
                      f"信心度: {pred['prediction']['confidence']:.3f}")
    
    else:
        print(f"❌ 測試失敗: {results.get('error', '未知錯誤')}")

def test_outlier_detection():
    """測試意外情況檢測邏輯"""
    predictor = EnhancedCustomPredictor()
    target_date = date(2025, 7, 12)
    
    print("\n🧪 測試意外情況檢測邏輯...")
    print("=" * 60)
    
    # 測試意外檢測版本
    options = {
        'force_regenerate': True,
        'update_existing': True,
        'skip_existing': False,
        'backup_existing': True,
        'model_version_suffix': 'outlier_detection',
        'exclude_postponed': True,
        'confidence_threshold': 0.0
    }
    
    print(f"🔬 使用版本: {options['model_version_suffix']}")
    print(f"🎯 重點關注: SEA @ DET (意外爆發 15-7)")
    print("-" * 60)
    
    results = predictor.predict_with_options(target_date, options)
    
    if results['success']:
        # 重點分析 SEA @ DET
        sea_det_found = False
        for pred in results['predictions']:
            if 'SEA @ DET' in pred['matchup']:
                sea_det_found = True
                print(f"\n🎯 SEA @ DET 分析:")
                print(f"  預測比分: {pred['prediction']['away_score']:.1f} - {pred['prediction']['home_score']:.1f}")
                print(f"  實際比分: {pred['actual_score']}")
                print(f"  信心度: {pred['prediction']['confidence']:.3f}")
                
                # 分析意外爆發檢測
                predicted_total = pred['prediction']['away_score'] + pred['prediction']['home_score']
                if predicted_total >= 15.0:
                    print(f"  ✅ 意外爆發檢測生效: 總分預測 {predicted_total:.1f}")
                else:
                    print(f"  ❌ 意外爆發未檢測: 總分預測 {predicted_total:.1f}")
                
                break
        
        if not sea_det_found:
            print("❌ 未找到 SEA @ DET 比賽")
    
    else:
        print(f"❌ 測試失敗: {results.get('error', '未知錯誤')}")

def compare_versions():
    """比較不同版本的預測結果"""
    predictor = EnhancedCustomPredictor()
    target_date = date(2025, 7, 12)
    
    print("\n📊 版本比較分析...")
    print("=" * 80)
    
    versions = ['test_v1', 'boyd_suppression', 'outlier_detection', 'ace_dominance']
    results_comparison = {}
    
    for version in versions:
        print(f"\n🔬 測試版本: {version}")
        
        options = {
            'force_regenerate': True,
            'update_existing': True,
            'skip_existing': False,
            'backup_existing': False,  # 不重複備份
            'model_version_suffix': version,
            'exclude_postponed': True,
            'confidence_threshold': 0.0
        }
        
        results = predictor.predict_with_options(target_date, options)
        
        if results['success']:
            # 提取重點比賽
            key_games = {}
            for pred in results['predictions']:
                if pred['action'] == 'updated' and pred['prediction']:
                    matchup = pred['matchup']
                    if 'CHC @ NYY' in matchup or 'SEA @ DET' in matchup:
                        key_games[matchup] = {
                            'predicted': f"{pred['prediction']['away_score']:.1f}-{pred['prediction']['home_score']:.1f}",
                            'confidence': pred['prediction']['confidence'],
                            'actual': pred['actual_score']
                        }
            
            results_comparison[version] = key_games
            print(f"  ✅ 完成")
        else:
            print(f"  ❌ 失敗: {results.get('error', '未知錯誤')}")
    
    # 顯示比較結果
    print(f"\n📋 版本比較結果:")
    print("=" * 80)
    
    for matchup in ['CHC @ NYY', 'SEA @ DET']:
        print(f"\n🎯 {matchup}:")
        print("-" * 50)
        for version, games in results_comparison.items():
            if matchup in games:
                game = games[matchup]
                print(f"{version:20} | {game['predicted']:12} | 信心度: {game['confidence']:.3f} | 實際: {game['actual']}")
            else:
                print(f"{version:20} | {'N/A':12} | 信心度: {'N/A':7} | 實際: N/A")

if __name__ == "__main__":
    test_boyd_suppression()
    test_outlier_detection()
    compare_versions()
