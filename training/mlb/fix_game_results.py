#!/usr/bin/env python3
"""
修復比賽結果更新功能
"""

from app import create_app
from models.database import db, Game
from models.data_fetcher import MLBDataFetcher
from datetime import date, timedelta
import logging
import statsapi as mlb

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_game_results():
    """修復比賽結果"""
    app = create_app()
    
    with app.app_context():
        print("🔧 修復比賽結果更新功能...")
        print("=" * 60)
        
        # 測試日期：昨天
        test_date = date.today() - timedelta(days=1)
        print(f"🎯 測試日期: {test_date}")
        
        # 獲取該日期的比賽
        games = Game.query.filter_by(date=test_date).all()
        print(f"📊 找到 {len(games)} 場比賽")
        
        # 初始化資料擷取器
        fetcher = MLBDataFetcher()
        
        updated_count = 0
        
        for game in games[:3]:  # 只測試前3場
            print(f"\n🔍 檢查比賽: {game.away_team} @ {game.home_team}")
            print(f"   當前狀態: {game.game_status}")
            print(f"   當前比分: {game.away_team} {game.away_score} @ {game.home_team} {game.home_score}")
            
            # 使用不同的 API 方法獲取比賽資訊
            try:
                # 方法1: 直接使用 statsapi
                game_pk = game.game_id.split('_')[-1] if '_' in game.game_id else game.game_id
                if game_pk.isdigit():
                    print(f"   🔌 使用 Game PK: {game_pk}")
                    
                    game_data = mlb.get('game', {'gamePk': game_pk})
                    if game_data:
                        # 檢查比賽狀態
                        status_data = game_data.get('gameData', {}).get('status', {})
                        detailed_state = status_data.get('detailedState', 'Unknown')
                        status_code = status_data.get('statusCode', 'S')
                        
                        print(f"   📡 API狀態: {detailed_state} (Code: {status_code})")
                        
                        # 檢查比分
                        line_score = game_data.get('liveData', {}).get('linescore', {})
                        if line_score and 'teams' in line_score:
                            teams = line_score['teams']
                            away_runs = teams.get('away', {}).get('runs')
                            home_runs = teams.get('home', {}).get('runs')
                            
                            print(f"   🎯 API比分: {away_runs} - {home_runs}")
                            
                            # 如果比賽已結束且有比分，更新資料庫
                            if (status_code in ['F', 'O', 'C'] and  # Final, Over, Complete
                                away_runs is not None and home_runs is not None):
                                
                                print(f"   🔄 更新比賽結果...")
                                game.game_status = 'completed'
                                game.away_score = away_runs
                                game.home_score = home_runs
                                
                                db.session.add(game)
                                updated_count += 1
                                print(f"   ✅ 已更新: {game.away_team} {away_runs} @ {game.home_team} {home_runs}")
                            else:
                                print(f"   ⏸️ 比賽尚未結束或無比分資料")
                        else:
                            print(f"   ❌ 無法獲取比分資料")
                else:
                    print(f"   ❌ 無效的 Game ID: {game.game_id}")
                    
            except Exception as e:
                print(f"   ❌ API錯誤: {e}")
        
        if updated_count > 0:
            try:
                db.session.commit()
                print(f"\n✅ 成功更新 {updated_count} 場比賽結果")
            except Exception as e:
                db.session.rollback()
                print(f"\n❌ 資料庫更新失敗: {e}")
        else:
            print(f"\n📝 沒有需要更新的比賽")

if __name__ == "__main__":
    fix_game_results()