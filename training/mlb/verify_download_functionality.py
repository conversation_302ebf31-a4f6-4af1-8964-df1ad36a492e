#!/usr/bin/env python3
"""
驗證所有下載功能的完整性
確認每個數據下載功能都能正常工作並保存到正確位置
"""

import sys
sys.path.append('.')

import os
from datetime import datetime, timedelta

def verify_all_download_functions():
    """驗證所有下載功能"""
    
    print("🔍 MLB 數據下載功能完整性驗證")
    print("=" * 70)
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    verification_results = {
        'database_connection': False,
        'monthly_data_manager': False,
        'boxscore_collector': False,
        'odds_fetcher': False,
        'progress_monitor': False,
        'data_persistence': False
    }
    
    try:
        # 1. 驗證數據庫連接
        print("\n📊 1. 驗證數據庫連接:")
        
        from app import app
        with app.app_context():
            db_uri = app.config.get('SQLALCHEMY_DATABASE_URI')
            print(f"   ✅ Flask數據庫URI: ...{db_uri[-30:]}")  # 只顯示後30字符
            
            if 'instance/mlb_data.db' in db_uri:
                print(f"   ✅ 使用正確的數據庫路徑")
                verification_results['database_connection'] = True
            else:
                print(f"   ❌ 數據庫路徑不正確")
        
        # 2. 驗證MonthlyDataManager
        print(f"\n📅 2. 驗證MonthlyDataManager:")
        
        try:
            from monthly_data_manager import MonthlyDataManager
            manager = MonthlyDataManager()
            
            print(f"   ✅ MonthlyDataManager初始化成功")
            
            # 檢查是否有保存boxscore的方法
            if hasattr(manager, '_save_boxscore_to_database'):
                print(f"   ✅ 包含boxscore保存功能")
            else:
                print(f"   ❌ 缺少boxscore保存功能")
                return False
                
            # 檢查組件
            if hasattr(manager, 'odds_fetcher'):
                print(f"   ✅ 包含賠率獲取器")
            if hasattr(manager, 'boxscore_collector'):
                print(f"   ✅ 包含boxscore收集器")
            
            verification_results['monthly_data_manager'] = True
            
        except Exception as e:
            print(f"   ❌ MonthlyDataManager初始化失敗: {e}")
        
        # 3. 驗證BoxscoreDataCollector
        print(f"\n🏟️ 3. 驗證BoxscoreDataCollector:")
        
        try:
            from boxscore_data_collector import BoxscoreDataCollector
            collector = BoxscoreDataCollector()
            
            print(f"   ✅ BoxscoreDataCollector初始化成功")
            print(f"   ✅ MLB API基礎URL: {collector.base_url}")
            
            verification_results['boxscore_collector'] = True
            
        except Exception as e:
            print(f"   ❌ BoxscoreDataCollector初始化失敗: {e}")
        
        # 4. 驗證RealBettingOddsFetcher
        print(f"\n💰 4. 驗證RealBettingOddsFetcher:")
        
        try:
            from models.real_betting_odds_fetcher import RealBettingOddsFetcher
            odds_fetcher = RealBettingOddsFetcher()
            
            print(f"   ✅ RealBettingOddsFetcher初始化成功")
            
            # 檢查API方法
            if hasattr(odds_fetcher, 'get_mlb_odds_today'):
                print(f"   ✅ 包含get_mlb_odds_today方法")
            else:
                print(f"   ❌ 缺少get_mlb_odds_today方法")
            
            verification_results['odds_fetcher'] = True
            
        except Exception as e:
            print(f"   ❌ RealBettingOddsFetcher初始化失敗: {e}")
        
        # 5. 驗證進度監控系統
        print(f"\n📊 5. 驗證進度監控系統:")
        
        try:
            from progress_monitor import progress_monitor
            
            print(f"   ✅ 進度監控器初始化成功")
            
            # 創建測試任務來驗證保存功能
            test_task_id = f"verify_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            progress_monitor.start_task(test_task_id, "驗證測試任務", 5)
            progress_monitor.update_progress(test_task_id, 3, "測試進度更新")
            progress_monitor.complete_task(test_task_id, True, "測試完成")
            
            print(f"   ✅ 進度監控功能正常")
            verification_results['progress_monitor'] = True
            
        except Exception as e:
            print(f"   ❌ 進度監控系統驗證失敗: {e}")
        
        # 6. 驗證數據持久化
        print(f"\n💾 6. 驗證數據持久化:")
        
        with app.app_context():
            try:
                from models.database import db, Game
                
                # 檢查數據庫連接
                game_count = db.session.query(Game).count()
                print(f"   ✅ 數據庫連接正常")
                print(f"   📊 當前比賽記錄數: {game_count:,}")
                
                # 檢查最近的數據
                recent_game = db.session.query(Game).order_by(Game.id.desc()).first()
                if recent_game:
                    print(f"   ✅ 最新比賽記錄: {recent_game.date} {recent_game.away_team} vs {recent_game.home_team}")
                
                verification_results['data_persistence'] = True
                
            except Exception as e:
                print(f"   ❌ 數據持久化驗證失敗: {e}")
        
        # 7. 總結驗證結果
        print(f"\n🎯 驗證結果總結:")
        
        passed_tests = sum(verification_results.values())
        total_tests = len(verification_results)
        
        for test_name, result in verification_results.items():
            status = "✅" if result else "❌"
            print(f"   {status} {test_name.replace('_', ' ').title()}")
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"\n📈 驗證通過率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        # 8. 功能狀態說明
        print(f"\n🚀 系統功能狀態:")
        
        if success_rate >= 100:
            print(f"   🎉 所有下載功能完全正常!")
            print(f"   ✅ 數據將正確保存到 instance/mlb_data.db")
            print(f"   ✅ 進度監控功能正常工作")
            print(f"   ✅ Boxscore數據可以正確下載和保存")
            print(f"   ✅ 賠率數據可以正確獲取")
        elif success_rate >= 80:
            print(f"   ⚠️ 大部分功能正常，少數組件需要檢查")
        else:
            print(f"   ❌ 發現重要功能問題需要修復")
        
        # 9. 使用說明
        if success_rate >= 80:
            print(f"\n📋 現在您可以:")
            print(f"   🔄 運行月度數據下載: python monthly_data_manager.py")
            print(f"   📊 查看進度監控: http://localhost:5502/admin/progress-monitor")
            print(f"   🎯 所有數據都會保存到正確位置")
            print(f"   💾 數據庫路徑: instance/mlb_data.db")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ 驗證過程失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 MLB 數據下載功能完整性驗證工具")
    print("驗證所有下載組件是否正常工作並保存到正確位置")
    print("")
    
    success = verify_all_download_functions()
    
    if success:
        print(f"\n✅ 所有下載功能驗證通過")
        print(f"🎯 系統已準備好進行數據下載操作")
    else:
        print(f"\n⚠️ 部分功能需要進一步檢查")