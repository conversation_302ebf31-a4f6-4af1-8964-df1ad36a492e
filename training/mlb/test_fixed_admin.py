#!/usr/bin/env python3
"""
測試修正後的管理面板功能
"""

from datetime import date
from app import create_app
from models.data_fetcher import MLBDataFetcher
from models.detailed_data_fetcher import DetailedDataFetcher

def test_fixed_daily_update():
    """測試修正後的每日更新功能"""
    print("🔧 測試修正後的每日更新功能...")
    
    app = create_app()
    with app.app_context():
        try:
            # 使用修正後的MLBDataFetcher
            game_fetcher = MLBDataFetcher()
            detail_fetcher = DetailedDataFetcher()
            
            target_date = date(2025, 6, 24)
            
            # 1. 測試比賽行程更新
            print(f"📅 更新 {target_date} 的比賽行程...")
            schedule_result = game_fetcher.fetch_games_by_date(target_date)
            
            if schedule_result:
                print("✅ 比賽行程更新成功")
            else:
                print("❌ 比賽行程更新失敗")
            
            # 2. 測試Box Score下載
            print(f"📦 下載 {target_date} 的Box Score...")
            boxscore_result = detail_fetcher.fetch_completed_games_boxscores(target_date)
            
            if boxscore_result:
                print("✅ Box Score下載成功")
            else:
                print("⚠️ Box Score下載完成 (可能沒有新數據)")
            
            return schedule_result and boxscore_result
            
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            return False

def check_game_status():
    """檢查比賽狀態"""
    print("\n📊 檢查比賽狀態...")
    
    app = create_app()
    with app.app_context():
        from models.database import Game
        
        target_date = date(2025, 6, 24)
        games = Game.query.filter(Game.date == target_date).all()
        
        if games:
            completed_games = [g for g in games if g.game_status == 'completed']
            scheduled_games = [g for g in games if g.game_status == 'scheduled']
            
            print(f"✅ {target_date}: {len(games)} 場比賽")
            print(f"   🏁 已完成: {len(completed_games)} 場")
            print(f"   ⏳ 預定: {len(scheduled_games)} 場")
            
            # 顯示比賽詳情
            for game in games[:5]:
                status_icon = "🏁" if game.game_status == 'completed' else "⏳"
                score_info = ""
                if game.home_score is not None and game.away_score is not None:
                    score_info = f"({game.away_score}-{game.home_score})"
                
                print(f"   {status_icon} {game.away_team} @ {game.home_team} {score_info}")
                
            return len(completed_games) > 0
        else:
            print(f"❌ {target_date} 沒有比賽數據")
            return False

def test_api_direct_call():
    """測試直接API調用"""
    print("\n🔧 測試直接API調用...")
    
    app = create_app()
    with app.app_context():
        try:
            fetcher = MLBDataFetcher()
            
            # 測試直接API調用
            target_date = date(2025, 6, 24)
            games = fetcher.get_games_for_date(target_date)
            
            print(f"✅ 成功獲取 {len(games)} 場比賽")
            
            for game in games[:3]:
                status_icon = "🏁" if game['game_status'] == 'completed' else "⏳"
                score_info = ""
                if game['home_score'] is not None and game['away_score'] is not None:
                    score_info = f"({game['away_score']}-{game['home_score']})"
                
                print(f"   {status_icon} {game['away_team']} @ {game['home_team']} {score_info}")
            
            return len(games) > 0
            
        except Exception as e:
            print(f"❌ API調用失敗: {e}")
            return False

if __name__ == "__main__":
    print("🚀 開始測試修正後的管理面板功能...\n")
    
    # 測試API調用
    api_success = test_api_direct_call()
    
    # 檢查當前狀態
    status_success = check_game_status()
    
    # 測試更新功能
    update_success = test_fixed_daily_update()
    
    # 再次檢查狀態
    final_status = check_game_status()
    
    print("\n" + "="*50)
    print("📋 測試結果總結:")
    print(f"   🔧 API調用: {'✅ 成功' if api_success else '❌ 失敗'}")
    print(f"   📊 數據狀態: {'✅ 正常' if status_success else '❌ 異常'}")
    print(f"   🔄 更新功能: {'✅ 成功' if update_success else '❌ 失敗'}")
    print(f"   🎯 最終狀態: {'✅ 正常' if final_status else '❌ 異常'}")
    
    if all([api_success, status_success, update_success, final_status]):
        print("\n🎉 所有測試通過！管理面板功能已修正完成！")
    else:
        print("\n⚠️ 部分測試失敗，需要進一步檢查。")
