#!/usr/bin/env python3
"""
進度監控系統
追蹤後台任務執行進度，包括整月數據下載
"""

import sys
sys.path.append('.')

import json
import time
from datetime import datetime
from typing import Dict, List
import logging
import os
from collections import defaultdict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProgressMonitor:
    """進度監控器"""
    
    def __init__(self):
        self.progress_file = "/tmp/mlb_progress.json"
        self.task_status = {}
        
    def start_task(self, task_id: str, task_name: str, total_steps: int = 100) -> Dict:
        """開始一個新任務"""
        task_info = {
            'task_id': task_id,
            'task_name': task_name,
            'start_time': datetime.now().isoformat(),
            'status': 'running',
            'progress': 0,
            'total_steps': total_steps,
            'current_step': 0,
            'messages': [],
            'errors': []
        }
        
        self.task_status[task_id] = task_info
        self._save_progress()
        
        logger.info(f"🚀 啟動任務: {task_name} ({task_id})")
        return task_info
    
    def update_progress(self, task_id: str, current_step: int, message: str = ""):
        """更新任務進度"""
        if task_id in self.task_status:
            task = self.task_status[task_id]
            task['current_step'] = current_step
            task['progress'] = (current_step / task['total_steps']) * 100
            task['last_update'] = datetime.now().isoformat()
            
            if message:
                task['messages'].append({
                    'time': datetime.now().isoformat(),
                    'message': message
                })
                logger.info(f"📊 {task['task_name']} [{current_step}/{task['total_steps']}]: {message}")
            
            self._save_progress()
    
    def add_error(self, task_id: str, error: str):
        """添加錯誤信息"""
        if task_id in self.task_status:
            self.task_status[task_id]['errors'].append({
                'time': datetime.now().isoformat(),
                'error': error
            })
            logger.error(f"❌ {self.task_status[task_id]['task_name']}: {error}")
            self._save_progress()
    
    def complete_task(self, task_id: str, success: bool = True, final_message: str = ""):
        """完成任務"""
        if task_id in self.task_status:
            task = self.task_status[task_id]
            task['status'] = 'completed' if success else 'failed'
            task['end_time'] = datetime.now().isoformat()
            task['progress'] = 100 if success else task['progress']
            
            if final_message:
                task['messages'].append({
                    'time': datetime.now().isoformat(),
                    'message': final_message
                })
            
            duration = (datetime.fromisoformat(task['end_time']) - datetime.fromisoformat(task['start_time'])).total_seconds()
            task['duration_seconds'] = duration
            
            status_emoji = "✅" if success else "❌"
            logger.info(f"{status_emoji} 任務完成: {task['task_name']} ({duration:.1f}秒)")
            
            self._save_progress()
    
    def get_task_status(self, task_id: str) -> Dict:
        """獲取任務狀態"""
        return self.task_status.get(task_id, {})
    
    def get_all_tasks(self) -> Dict:
        """獲取所有任務狀態"""
        return self.task_status
    
    def get_running_tasks(self) -> List[Dict]:
        """獲取正在運行的任務"""
        return [task for task in self.task_status.values() if task['status'] == 'running']
    
    def _save_progress(self):
        """保存進度到文件"""
        try:
            with open(self.progress_file, 'w') as f:
                json.dump(self.task_status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存進度失敗: {e}")
    
    def load_progress(self):
        """從文件加載進度"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r') as f:
                    self.task_status = json.load(f)
                logger.info(f"加載了 {len(self.task_status)} 個任務的進度")
        except Exception as e:
            logger.error(f"加載進度失敗: {e}")
    
    def clean_completed_tasks(self, hours_old: int = 24):
        """清理已完成的舊任務"""
        current_time = datetime.now()
        tasks_to_remove = []
        
        for task_id, task in self.task_status.items():
            if task['status'] in ['completed', 'failed']:
                if 'end_time' in task:
                    end_time = datetime.fromisoformat(task['end_time'])
                    if (current_time - end_time).total_seconds() > hours_old * 3600:
                        tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.task_status[task_id]
        
        if tasks_to_remove:
            logger.info(f"清理了 {len(tasks_to_remove)} 個舊任務")
            self._save_progress()
    
    def display_status(self):
        """顯示當前所有任務狀態"""
        print("="*80)
        print("📊 MLB 系統任務進度監控")
        print("="*80)
        
        if not self.task_status:
            print("🔍 沒有活動的任務")
            return
        
        # 按狀態分組顯示
        status_groups = defaultdict(list)
        for task in self.task_status.values():
            status_groups[task['status']].append(task)
        
        # 顯示正在運行的任務
        if 'running' in status_groups:
            print(f"\n🔄 正在運行的任務 ({len(status_groups['running'])} 個):")
            for task in status_groups['running']:
                progress_bar = "█" * int(task['progress'] / 5) + "░" * (20 - int(task['progress'] / 5))
                print(f"   📋 {task['task_name']}")
                print(f"      ⏱️ 進度: {task['progress']:.1f}% [{progress_bar}] ({task['current_step']}/{task['total_steps']})")
                print(f"      🕐 開始時間: {task['start_time']}")
                if task.get('messages'):
                    latest_msg = task['messages'][-1]['message']
                    print(f"      💬 最新狀態: {latest_msg}")
                if task.get('errors'):
                    print(f"      ⚠️ 錯誤數量: {len(task['errors'])}")
        
        # 顯示已完成的任務
        if 'completed' in status_groups:
            print(f"\n✅ 已完成的任務 ({len(status_groups['completed'])} 個):")
            for task in status_groups['completed']:
                duration = task.get('duration_seconds', 0)
                print(f"   📋 {task['task_name']} (耗時 {duration:.1f}秒)")
                print(f"      🏁 完成時間: {task.get('end_time', 'N/A')}")
                if task.get('messages'):
                    final_msg = task['messages'][-1]['message']
                    print(f"      💬 最終狀態: {final_msg}")
        
        # 顯示失敗的任務
        if 'failed' in status_groups:
            print(f"\n❌ 失敗的任務 ({len(status_groups['failed'])} 個):")
            for task in status_groups['failed']:
                print(f"   📋 {task['task_name']}")
                print(f"      💥 失敗時間: {task.get('end_time', 'N/A')}")
                print(f"      📊 進度: {task['progress']:.1f}%")
                if task.get('errors'):
                    latest_error = task['errors'][-1]['error']
                    print(f"      ⚠️ 最新錯誤: {latest_error}")
        
        print("="*80)
    
    def watch_tasks(self, refresh_interval: int = 3):
        """持續監控任務進度"""
        print("🔍 開始監控任務進度，按 Ctrl+C 退出")
        try:
            while True:
                self.load_progress()  # 重新加載最新進度
                
                # 清屏並顯示狀態
                os.system('clear' if os.name == 'posix' else 'cls')
                self.display_status()
                
                # 檢查是否有運行中的任務
                running_tasks = self.get_running_tasks()
                if not running_tasks:
                    print(f"\n✨ 所有任務已完成，將在 {refresh_interval} 秒後退出監控...")
                    time.sleep(refresh_interval)
                    break
                
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            print("\n👋 退出監控")

# 全局進度監控器實例
progress_monitor = ProgressMonitor()

def main():
    """主函數 - 顯示當前進度或開始監控"""
    import argparse
    parser = argparse.ArgumentParser(description="MLB 系統進度監控")
    parser.add_argument('--watch', '-w', action='store_true', help='持續監控模式')
    parser.add_argument('--clean', '-c', action='store_true', help='清理已完成的舊任務')
    parser.add_argument('--interval', '-i', type=int, default=3, help='刷新間隔(秒)')
    
    args = parser.parse_args()
    
    # 加載現有進度
    progress_monitor.load_progress()
    
    if args.clean:
        progress_monitor.clean_completed_tasks()
        print("🧹 已清理舊任務")
    
    if args.watch:
        progress_monitor.watch_tasks(args.interval)
    else:
        progress_monitor.display_status()

if __name__ == "__main__":
    main()