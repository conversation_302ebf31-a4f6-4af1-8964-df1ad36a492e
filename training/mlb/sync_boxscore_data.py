#!/usr/bin/env python3
"""
同步boxscore數據到games表
從box_scores表中同步完整的比分數據到games表
"""

import sys
sys.path.append('.')

import os
import sqlite3
from datetime import datetime

def sync_boxscore_data():
    """從box_scores表同步數據到games表"""
    
    print("🔄 同步boxscore數據到games表")
    print("=" * 60)
    
    try:
        # 連接數據庫
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        print("✅ 已連接數據庫: instance/mlb_data.db")
        
        # 1. 查找有box_scores但games表中缺失比分的比賽
        print("\n🔍 查找可以從box_scores同步的比賽...")
        
        cursor.execute("""
            SELECT DISTINCT 
                g.game_id, 
                g.date, 
                g.home_team, 
                g.away_team,
                g.home_score,
                g.away_score,
                g.game_status
            FROM games g
            INNER JOIN box_scores bs ON g.game_id = bs.game_id
            WHERE g.game_status = 'completed'
            AND (g.home_score IS NULL OR g.away_score IS NULL)
        """)
        
        syncable_games = cursor.fetchall()
        
        if not syncable_games:
            print("   ✅ 沒有需要同步的比賽")
            conn.close()
            return True
        
        print(f"   🎯 發現 {len(syncable_games)} 場可以同步的比賽")
        
        # 2. 對每場比賽進行同步
        fixed_count = 0
        
        for game_id, game_date, home_team, away_team, current_home, current_away, status in syncable_games:
            
            # 從box_scores表獲取該比賽的兩隊比分
            cursor.execute("""
                SELECT runs, is_home
                FROM box_scores 
                WHERE game_id = ?
                ORDER BY is_home
            """, (game_id,))
            
            box_scores = cursor.fetchall()
            
            if len(box_scores) == 2:
                away_runs = None
                home_runs = None
                
                for runs, is_home in box_scores:
                    if is_home == 1:  # 主隊
                        home_runs = runs
                    else:  # 客隊
                        away_runs = runs
                
                if home_runs is not None and away_runs is not None:
                    # 決定是否需要更新
                    update_needed = False
                    new_home_score = current_home if current_home is not None else home_runs
                    new_away_score = current_away if current_away is not None else away_runs
                    
                    if current_home != new_home_score or current_away != new_away_score:
                        # 更新games表
                        cursor.execute("""
                            UPDATE games 
                            SET home_score = ?, away_score = ?, updated_at = ?
                            WHERE game_id = ?
                        """, (new_home_score, new_away_score, datetime.now(), game_id))
                        
                        fixed_count += 1
                        print(f"   ✅ 同步 {game_id}: {away_team} {new_away_score}-{new_home_score} {home_team}")
                        
                        if fixed_count >= 20:  # 限制輸出
                            print(f"   ... 繼續同步中 ...")
                            break
            
            elif len(box_scores) == 1:
                # 只有一隊的數據，部分同步
                runs, is_home = box_scores[0]
                
                if is_home == 1 and current_home is None:
                    cursor.execute("""
                        UPDATE games 
                        SET home_score = ?, updated_at = ?
                        WHERE game_id = ?
                    """, (runs, datetime.now(), game_id))
                    fixed_count += 1
                    print(f"   🔧 部分同步 {game_id}: 主隊 {runs} 分")
                    
                elif is_home == 0 and current_away is None:
                    cursor.execute("""
                        UPDATE games 
                        SET away_score = ?, updated_at = ?
                        WHERE game_id = ?
                    """, (runs, datetime.now(), game_id))
                    fixed_count += 1
                    print(f"   🔧 部分同步 {game_id}: 客隊 {runs} 分")
        
        # 提交所有更改
        conn.commit()
        
        print(f"\n🎉 同步完成:")
        print(f"   ✅ 成功同步 {fixed_count} 場比賽的比分數據")
        
        # 3. 檢查同步後的覆蓋率
        print(f"\n📊 同步後統計:")
        
        cursor.execute("""
            SELECT 
                COUNT(*) as total_completed,
                SUM(CASE WHEN home_score IS NOT NULL AND away_score IS NOT NULL THEN 1 ELSE 0 END) as with_complete_scores,
                SUM(CASE WHEN home_score IS NOT NULL OR away_score IS NOT NULL THEN 1 ELSE 0 END) as with_any_score
            FROM games 
            WHERE game_status = 'completed'
        """)
        
        stats = cursor.fetchone()
        if stats:
            total, complete, any_score = stats
            complete_rate = (complete / total * 100) if total > 0 else 0
            partial_rate = (any_score / total * 100) if total > 0 else 0
            
            print(f"   📈 完整比分覆蓋率: {complete_rate:.1f}% ({complete}/{total})")
            print(f"   📊 部分比分覆蓋率: {partial_rate:.1f}% ({any_score}/{total})")
            print(f"   ⚠️ 完全缺失: {total - any_score} 場")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 同步失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔄 MLB Boxscore 數據同步工具")
    print("從box_scores表同步比分數據到games表")
    print("")
    
    success = sync_boxscore_data()
    
    if success:
        print(f"\n✅ 同步程序執行完成")
        print(f"💡 比賽結果更新問題應該已經解決")
    else:
        print(f"\n❌ 同步失敗，請檢查錯誤信息")