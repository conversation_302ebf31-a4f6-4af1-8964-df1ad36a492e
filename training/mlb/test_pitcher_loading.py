#!/usr/bin/env python3
"""
測試投手載入功能
驗證系統是否正確載入每場比賽的實際投手信息
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath('.')))

from app import create_app
from models.database import db, Game
from models.enhanced_feature_engineer import EnhancedFeatureEngineer
from models.unified_betting_predictor import UnifiedBettingPredictor
from datetime import date, datetime
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_pitcher_loading():
    """測試投手載入功能"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("🎯 投手載入功能測試")
        print("=" * 60)
        
        # 測試日期：2025-07-12 (有NYM @ BAL重賽的日期)
        test_date = date(2025, 7, 12)
        
        # 獲取該日期的比賽
        games = Game.query.filter(Game.date == test_date).all()
        
        if not games:
            print(f"❌ 找不到 {test_date} 的比賽")
            return
        
        print(f"📅 測試日期: {test_date}")
        print(f"🎯 找到 {len(games)} 場比賽")
        
        # 初始化特徵工程器
        feature_engineer = EnhancedFeatureEngineer()
        
        # 測試每場比賽的投手載入
        for i, game in enumerate(games, 1):
            print(f"\n🎲 比賽 {i}: {game.away_team} @ {game.home_team} (ID: {game.game_id})")
            
            try:
                # 測試獲取先發投手
                home_pitcher = feature_engineer._get_probable_starter(game.home_team, test_date)
                away_pitcher = feature_engineer._get_probable_starter(game.away_team, test_date)
                
                print(f"  主隊投手: {home_pitcher or '未知'}")
                print(f"  客隊投手: {away_pitcher or '未知'}")
                
                # 測試特徵提取
                features = feature_engineer.extract_comprehensive_features(
                    game.home_team, game.away_team, test_date
                )
                
                # 顯示關鍵投手特徵
                pitcher_features = {k: v for k, v in features.items() if 'pitcher' in k.lower()}
                
                if pitcher_features:
                    print("  投手特徵:")
                    for key, value in list(pitcher_features.items())[:5]:  # 只顯示前5個
                        print(f"    {key}: {value}")
                else:
                    print("  ⚠️  沒有找到投手特徵")
                
                # 檢查是否使用了實際投手信息
                has_real_pitcher_data = (
                    home_pitcher is not None or 
                    away_pitcher is not None or
                    any('pitcher_quality' in k for k in features.keys())
                )
                
                status = "✅ 有實際投手數據" if has_real_pitcher_data else "❌ 使用默認數據"
                print(f"  狀態: {status}")
                
            except Exception as e:
                print(f"  ❌ 錯誤: {e}")

def test_prediction_differences():
    """測試重賽預測差異"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("🔄 重賽預測差異測試")
        print("=" * 60)
        
        # 查找NYM @ BAL的重賽
        nyy_bal_games = Game.query.filter(
            Game.home_team == 'BAL',
            Game.away_team == 'NYM',
            Game.date >= date(2025, 7, 9),
            Game.date <= date(2025, 7, 12)
        ).all()
        
        if len(nyy_bal_games) < 2:
            print("❌ 找不到足夠的NYM @ BAL比賽進行測試")
            return
        
        print(f"找到 {len(nyy_bal_games)} 場 NYM @ BAL 比賽:")
        
        # 初始化預測器
        predictor = UnifiedBettingPredictor(app)
        predictor.initialize_prediction_models()
        
        predictions = []
        
        for i, game in enumerate(nyy_bal_games, 1):
            print(f"\n🎯 比賽 {i}: {game.date} (ID: {game.game_id})")
            
            try:
                # 生成預測
                prediction = predictor.generate_unified_prediction(game.game_id, game.date)
                
                if prediction.get('success'):
                    score_pred = prediction.get('predictions', {}).get('score', {})
                    home_score = score_pred.get('home_score', 0)
                    away_score = score_pred.get('away_score', 0)
                    
                    print(f"  預測比分: {away_score:.1f} - {home_score:.1f}")
                    predictions.append({
                        'game_id': game.game_id,
                        'date': game.date,
                        'home_score': home_score,
                        'away_score': away_score
                    })
                else:
                    print(f"  ❌ 預測失敗: {prediction.get('error')}")
                    
            except Exception as e:
                print(f"  ❌ 預測錯誤: {e}")
        
        # 檢查預測是否相同
        if len(predictions) >= 2:
            print(f"\n📊 預測結果比較:")
            
            all_same = True
            first_pred = predictions[0]
            
            for pred in predictions[1:]:
                home_diff = abs(pred['home_score'] - first_pred['home_score'])
                away_diff = abs(pred['away_score'] - first_pred['away_score'])
                
                if home_diff > 0.1 or away_diff > 0.1:
                    all_same = False
                    break
            
            if all_same:
                print("❌ 所有預測結果相同 - 可能沒有使用實際投手數據")
            else:
                print("✅ 預測結果有差異 - 正確使用了不同的投手數據")

if __name__ == "__main__":
    print("🧪 開始投手載入功能測試...")
    test_pitcher_loading()
    test_prediction_differences()
    print("\n✅ 測試完成")
