#!/usr/bin/env python3
"""
測試增強的優化預測功能
驗證博彩盤口預測整合
"""

import sys
import os
import json
import requests
from datetime import date, datetime

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_optimized_prediction():
    """測試增強的優化預測功能"""
    print("🚀 測試增強的優化預測功能")
    print("=" * 80)
    
    api_url = "http://localhost:5500/simulation/api/optimized_simulate"
    test_data = {"date": "2025-07-04"}
    
    try:
        print(f"📡 發送增強優化預測請求...")
        response = requests.post(
            api_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print(f"✅ 增強優化預測成功")
                print(f"   日期: {data.get('date')}")
                print(f"   總比賽: {data.get('total_games')} 場")
                
                predictions = data.get('predictions', [])
                print(f"   預測結果: {len(predictions)} 場比賽")
                
                # 詳細分析前3個預測
                for i, pred in enumerate(predictions[:3], 1):
                    print(f"\n📊 比賽 {i}: {pred.get('teams')}")
                    
                    prediction = pred.get('prediction', {})
                    print(f"   得分預測: {prediction.get('away_score'):.1f} - {prediction.get('home_score'):.1f}")
                    print(f"   總分: {prediction.get('total_runs', 0):.1f}")
                    print(f"   勝率: {prediction.get('predicted_winner')} ({prediction.get('home_win_probability', 0)*100:.1f}%)")
                    print(f"   信心: {prediction.get('confidence_level')}")
                    
                    # 檢查博彩預測
                    betting = prediction.get('betting_predictions', {})
                    if betting:
                        print(f"   📈 博彩預測:")
                        
                        # 大小分
                        over_under = betting.get('over_under')
                        if over_under:
                            print(f"     大小分盤口: {over_under.get('total_line')}")
                            print(f"     預測總分: {over_under.get('predicted_total')}")
                            print(f"     大分概率: {over_under.get('over_probability', 0)*100:.1f}%")
                            print(f"     建議: {over_under.get('recommendation')}")
                        else:
                            print(f"     大小分: 無數據")
                        
                        # 讓分盤
                        run_line = betting.get('run_line')
                        if run_line:
                            print(f"     讓分盤: 主隊 {run_line.get('home_line')}, 客隊 {run_line.get('away_line')}")
                            print(f"     主隊覆蓋概率: {run_line.get('home_cover_probability', 0)*100:.1f}%")
                            print(f"     建議: {run_line.get('recommendation')}")
                        else:
                            print(f"     讓分盤: 無數據")
                        
                        # 錯誤信息
                        if betting.get('error'):
                            print(f"     ⚠️ 博彩預測錯誤: {betting['error']}")
                    else:
                        print(f"   ⚠️ 無博彩預測數據")
                
                return True
            else:
                print(f"❌ 增強優化預測失敗: {data.get('error')}")
                return False
        else:
            print(f"❌ API請求失敗: {response.status_code}")
            print(f"   響應內容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 增強優化預測錯誤: {e}")
        return False

def test_simulation_page_with_betting():
    """測試模擬頁面的博彩功能"""
    print(f"\n🌐 測試模擬頁面博彩功能")
    print("=" * 80)
    
    page_url = "http://localhost:5500/simulation/date_simulation"
    
    try:
        response = requests.get(page_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 檢查博彩相關元素
            betting_elements = [
                '大小分',
                '讓分盤',
                'betting_predictions',
                'over_under',
                'run_line'
            ]
            
            print(f"✅ 頁面訪問成功")
            for element in betting_elements:
                if element in content:
                    print(f"   ✅ 找到博彩元素: {element}")
                else:
                    print(f"   ⚠️ 缺少博彩元素: {element}")
            
            return True
        else:
            print(f"❌ 頁面訪問失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 頁面測試錯誤: {e}")
        return False

def test_betting_prediction_accuracy():
    """測試博彩預測準確性"""
    print(f"\n🎯 測試博彩預測準確性")
    print("=" * 80)
    
    # 測試歷史日期的預測
    test_dates = ["2025-06-29", "2025-06-27"]
    
    for test_date in test_dates:
        print(f"\n📅 測試日期: {test_date}")
        
        api_url = "http://localhost:5500/simulation/api/optimized_simulate"
        test_data = {"date": test_date}
        
        try:
            response = requests.post(
                api_url,
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    predictions = data.get('predictions', [])
                    
                    # 統計博彩預測覆蓋率
                    total_games = len(predictions)
                    over_under_count = 0
                    run_line_count = 0
                    
                    for pred in predictions:
                        betting = pred.get('prediction', {}).get('betting_predictions', {})
                        if betting.get('over_under'):
                            over_under_count += 1
                        if betting.get('run_line'):
                            run_line_count += 1
                    
                    print(f"   總比賽: {total_games}")
                    print(f"   大小分覆蓋: {over_under_count}/{total_games} ({over_under_count/total_games*100:.1f}%)")
                    print(f"   讓分盤覆蓋: {run_line_count}/{total_games} ({run_line_count/total_games*100:.1f}%)")
                    
                else:
                    print(f"   ❌ 預測失敗: {data.get('error')}")
            else:
                print(f"   ❌ API失敗: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 測試錯誤: {e}")

def generate_enhancement_report():
    """生成增強功能報告"""
    print(f"\n📊 增強功能測試報告")
    print("=" * 80)
    
    tests = [
        ("增強優化預測", test_enhanced_optimized_prediction),
        ("模擬頁面博彩", test_simulation_page_with_betting),
        ("博彩預測準確性", test_betting_prediction_accuracy)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 執行測試: {test_name}")
        try:
            if test_name == "博彩預測準確性":
                test_func()  # 這個測試不返回布爾值
                results[test_name] = True
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 測試異常: {e}")
            results[test_name] = False
    
    # 生成總結
    print(f"\n📋 測試總結:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} 測試通過 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print(f"\n🎉 所有測試通過！增強功能完全正常！")
        print(f"🌐 訪問地址: http://localhost:5500/simulation/date_simulation")
        print(f"🚀 新功能: 博彩盤口預測已整合")
        print(f"📈 包含功能:")
        print(f"   ✅ 大小分預測 (Over/Under)")
        print(f"   ✅ 讓分盤預測 (Run Line)")
        print(f"   ✅ 博彩建議")
        print(f"   ✅ 概率計算")
    else:
        print(f"\n⚠️  部分測試失敗，請檢查相關功能")
    
    return passed == total

def main():
    """主函數"""
    print("🔧 MLB增強優化預測系統測試")
    print("=" * 80)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 檢查Flask是否運行
    try:
        response = requests.get("http://localhost:5500", timeout=5)
        print(f"✅ Flask應用正在運行")
    except:
        print(f"❌ Flask應用未運行，請先啟動應用")
        print(f"   命令: python app.py")
        return
    
    # 執行完整測試
    success = generate_enhancement_report()
    
    if success:
        print(f"\n🎯 測試完成！增強功能狀態良好。")
        print(f"📝 增強總結:")
        print(f"   ✅ 博彩盤口預測已整合")
        print(f"   ✅ 大小分和讓分盤功能完整")
        print(f"   ✅ 模擬界面已更新")
        print(f"   ✅ API響應包含博彩數據")
    else:
        print(f"\n⚠️  測試發現問題，請檢查日誌。")

if __name__ == "__main__":
    main()
