#!/usr/bin/env python3
"""
測試增強預測API - 適配端口5500
"""

import requests
import json

def test_enhanced_prediction_api():
    """測試增強預測API端點"""
    
    print("🎯 測試增強預測API (端口5500)")
    print("=" * 60)
    
    # 正確的API端點
    api_url = "http://localhost:5500/unified/api/predict/enhanced_daily"
    
    # 測試數據
    test_data = {
        'target_date': '2025-08-23'
    }
    
    print(f"📡 API端點: {api_url}")
    print(f"📦 請求數據: {json.dumps(test_data, indent=2)}")
    print()
    
    try:
        print("🚀 發送請求...")
        response = requests.post(
            api_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📊 狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ API請求成功！")
            print()
            
            # 基本信息
            print("📋 基本信息:")
            engine_info = data.get('engine_info', {})
            print(f"   引擎類型: {engine_info.get('engine_type', 'Unknown')}")
            print(f"   總比賽數: {data.get('total_games', 0)}")
            print(f"   成功預測: {data.get('successful_predictions', 0)}")
            print()
            
            # 功能特性
            features = engine_info.get('features', [])
            if features:
                print("🔧 引擎功能:")
                for feature in features:
                    print(f"   {feature}")
                print()
            
            # 預測結果詳情
            predictions = data.get('predictions', [])
            if predictions:
                print(f"🎯 預測結果 (共 {len(predictions)} 場):")
                print()
                
                enhanced_count = 0
                
                for i, pred in enumerate(predictions[:5], 1):  # 顯示前5場
                    print(f"   比賽 {i}: {pred.get('matchup', 'N/A')}")
                    
                    # 基本預測
                    away_score = pred.get('predicted_away_score', 0)
                    home_score = pred.get('predicted_home_score', 0)
                    total_runs = pred.get('predicted_total_runs', 0)
                    print(f"      預測分數: {away_score:.1f} - {home_score:.1f} (總分: {total_runs:.1f})")
                    
                    # 增強功能檢查
                    enhanced_features = pred.get('enhanced_features', {})
                    methodology = enhanced_features.get('methodology', 'Unknown')
                    ballpark_factor = enhanced_features.get('ballpark_factor', 1.0)
                    stadium_name = enhanced_features.get('stadium_name', 'Unknown')
                    
                    print(f"      方法論: {methodology}")
                    print(f"      球場: {stadium_name} (因子: {ballpark_factor:.3f})")
                    
                    # 檢查是否使用增強引擎
                    if 'Enhanced' in methodology:
                        enhanced_count += 1
                        print("      ✅ 使用增強預測引擎")
                    else:
                        print("      ⚠️ 未使用增強預測引擎")
                    print()
                
                # 總結
                print("=" * 60)
                print("📊 測試總結:")
                print(f"   總預測場次: {len(predictions)}")
                print(f"   使用增強引擎: {enhanced_count} 場")
                success_rate = (enhanced_count / len(predictions)) * 100 if predictions else 0
                print(f"   成功率: {success_rate:.1f}%")
                
                if enhanced_count > 0:
                    print()
                    print("🎉 增強預測引擎運作正常！")
                    print("🎯 成功實現用戶需求：")
                    print("   ✓ 球場因子分析")
                    print("   ✓ 投手對戰分析") 
                    print("   ✓ 蒙地卡羅模擬")
                    print("   ✓ 預測校正機制")
                    return True
                else:
                    print()
                    print("⚠️ 增強預測引擎未正常運作")
                    print("可能的原因：")
                    print("   - 數據庫中沒有對應日期的比賽數據")
                    print("   - 增強預測引擎初始化失敗")
                    print("   - API端點配置問題")
                    return False
            else:
                print("📭 沒有預測結果")
                print("可能原因：指定日期沒有比賽數據")
                return False
                
        else:
            print(f"❌ API請求失敗: {response.status_code}")
            try:
                error_data = response.json()
                print(f"錯誤信息: {error_data.get('error', response.text)}")
            except:
                print(f"錯誤內容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 連接失敗！")
        print("請確保：")
        print("   1. Flask應用正在運行 (./start.sh)")
        print("   2. 端口5500可用")
        print("   3. 防火牆設置正確")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ 請求超時！")
        print("API處理時間過長，可能是：")
        print("   1. 數據庫查詢較慢")
        print("   2. 增強預測計算複雜")
        return False
        
    except Exception as e:
        print(f"❌ 測試過程出錯: {e}")
        return False

if __name__ == "__main__":
    print("增強預測引擎API測試")
    print("請確保您已經運行了 ./start.sh 啟動應用")
    print()
    
    success = test_enhanced_prediction_api()
    
    if success:
        print("\\n🎊 測試完成：增強預測引擎正常運作！")
        exit(0)
    else:
        print("\\n🔧 測試失敗：請檢查上述建議")
        exit(1)