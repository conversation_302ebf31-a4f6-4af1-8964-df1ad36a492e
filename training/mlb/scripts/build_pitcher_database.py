#!/usr/bin/env python3
"""
投手資料庫建構腳本
收集和組織投手歷史資料，支持增強預測系統
"""

import sys
import os
import logging
import asyncio
from datetime import date, datetime, timedelta

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models.starting_pitcher_tracker import StartingPitcherTracker
from models.historical_data_optimizer import PitcherBatterHistoryBuilder, HistoricalDataOptimizer
from models.database import db, Game, PlayerStats

logger = logging.getLogger(__name__)

class PitcherDatabaseBuilder:
    """投手資料庫建構器"""
    
    def __init__(self, app):
        self.app = app
        self.pitcher_tracker = StartingPitcherTracker()
        self.data_optimizer = HistoricalDataOptimizer()
        self.history_builder = PitcherBatterHistoryBuilder(self.data_optimizer)
        
    def build_complete_pitcher_database(self, start_date: date = None, end_date: date = None):
        """建立完整的投手資料庫"""
        
        if start_date is None:
            start_date = date(2019, 3, 1)  # MLB 2019賽季開始
        if end_date is None:
            end_date = date.today()
        
        print(f"🚀 開始建構投手資料庫 ({start_date} - {end_date})")
        
        with self.app.app_context():
            # 第一步：收集先發投手記錄
            print("\n📋 第一步：收集歷史先發投手記錄...")
            pitcher_stats = self.pitcher_tracker.batch_collect_historical_pitchers(
                start_date, end_date
            )
            
            print(f"✅ 投手記錄收集完成:")
            for key, value in pitcher_stats.items():
                print(f"   {key}: {value}")
            
            # 第二步：建構投手vs打者對戰資料庫
            print("\n⚾ 第二步：建構投手vs打者對戰資料庫...")
            try:
                matchup_database = self.history_builder.build_comprehensive_matchup_database()
                
                if matchup_database:
                    print(f"✅ 對戰資料庫建構完成:")
                    stats = matchup_database.get('stats', {})
                    for key, value in stats.items():
                        print(f"   {key}: {value}")
                else:
                    print("⚠️  對戰資料庫建構未完成 (可能因為缺少詳細對戰數據)")
                    
            except Exception as e:
                print(f"❌ 對戰資料庫建構失敗: {e}")
            
            # 第三步：驗證和補充數據
            print("\n🔍 第三步：驗證和補充投手數據...")
            validation_stats = self.validate_pitcher_data()
            
            print(f"✅ 數據驗證完成:")
            for key, value in validation_stats.items():
                print(f"   {key}: {value}")
            
            # 第四步：生成總結報告
            print("\n📊 第四步：生成總結報告...")
            summary = self.generate_summary_report()
            
            return summary
    
    def validate_pitcher_data(self):
        """驗證投手數據的完整性"""
        
        validation_stats = {
            'total_games_checked': 0,
            'games_with_pitchers': 0,
            'games_missing_pitchers': 0,
            'verified_records': 0,
            'total_unique_pitchers': 0
        }
        
        try:
            # 檢查有多少比賽有投手記錄
            from models.starting_pitcher_tracker import StartingPitcherRecord
            
            total_games = Game.query.filter(
                Game.date >= date(2019, 3, 1),
                Game.game_status == 'completed'
            ).count()
            
            games_with_pitchers = db.session.query(StartingPitcherRecord).count()
            
            # 檢查有多少投手有詳細統計
            unique_pitchers = PlayerStats.query.filter(
                PlayerStats.season >= 2019,
                PlayerStats.innings_pitched > 0
            ).distinct(PlayerStats.player_id).count()
            
            validation_stats.update({
                'total_games_checked': total_games,
                'games_with_pitchers': games_with_pitchers,
                'games_missing_pitchers': total_games - games_with_pitchers,
                'total_unique_pitchers': unique_pitchers
            })
            
        except Exception as e:
            logger.error(f"數據驗證失敗: {e}")
        
        return validation_stats
    
    def generate_summary_report(self):
        """生成總結報告"""
        
        report = {
            'build_time': datetime.now().isoformat(),
            'data_range': {
                'start_date': '2019-03-01',
                'end_date': date.today().isoformat()
            },
            'database_stats': {},
            'data_quality': {},
            'recommendations': []
        }
        
        try:
            with self.app.app_context():
                # 統計數據庫狀況
                from models.starting_pitcher_tracker import StartingPitcherRecord
                
                total_games = Game.query.count()
                completed_games = Game.query.filter_by(game_status='completed').count()
                pitcher_records = StartingPitcherRecord.query.count()
                
                report['database_stats'] = {
                    'total_games': total_games,
                    'completed_games': completed_games,
                    'pitcher_records': pitcher_records,
                    'coverage_rate': f"{(pitcher_records/completed_games*100):.1f}%" if completed_games > 0 else "0%"
                }
                
                # 數據質量評估
                high_confidence_records = StartingPitcherRecord.query.filter_by(confidence_level='high').count()
                verified_records = StartingPitcherRecord.query.filter_by(is_verified=True).count()
                
                report['data_quality'] = {
                    'high_confidence_records': high_confidence_records,
                    'verified_records': verified_records,
                    'quality_score': f"{(high_confidence_records/pitcher_records*100):.1f}%" if pitcher_records > 0 else "0%"
                }
                
                # 建議
                if pitcher_records < completed_games * 0.8:
                    report['recommendations'].append("建議補充更多歷史投手記錄")
                
                if verified_records < pitcher_records * 0.5:
                    report['recommendations'].append("建議通過box score驗證更多投手記錄")
                
                if high_confidence_records < pitcher_records * 0.7:
                    report['recommendations'].append("建議提升數據來源質量")
                
        except Exception as e:
            logger.error(f"生成報告失敗: {e}")
            report['error'] = str(e)
        
        return report

def main():
    """主函數"""
    print("🎯 MLB投手資料庫建構工具")
    print("=" * 50)
    
    # 創建Flask應用
    app = create_app('development')
    
    # 創建建構器
    builder = PitcherDatabaseBuilder(app)
    
    # 解析命令行參數
    import argparse
    parser = argparse.ArgumentParser(description='建構MLB投手資料庫')
    parser.add_argument('--start-date', type=str, help='開始日期 (YYYY-MM-DD)', default='2019-03-01')
    parser.add_argument('--end-date', type=str, help='結束日期 (YYYY-MM-DD)', default=date.today().isoformat())
    parser.add_argument('--validate-only', action='store_true', help='僅驗證現有數據')
    
    args = parser.parse_args()
    
    try:
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d').date()
    except ValueError as e:
        print(f"❌ 日期格式錯誤: {e}")
        sys.exit(1)
    
    if args.validate_only:
        print("🔍 僅執行數據驗證...")
        with app.app_context():
            validation_stats = builder.validate_pitcher_data()
            print("驗證結果:")
            for key, value in validation_stats.items():
                print(f"   {key}: {value}")
    else:
        # 執行完整建構
        summary = builder.build_complete_pitcher_database(start_date, end_date)
        
        print("\n" + "=" * 50)
        print("🎉 投手資料庫建構完成!")
        print("\n📊 總結報告:")
        
        # 美化輸出報告
        if summary:
            db_stats = summary.get('database_stats', {})
            quality = summary.get('data_quality', {})
            
            print(f"   總比賽數: {db_stats.get('total_games', 'N/A')}")
            print(f"   已完成比賽: {db_stats.get('completed_games', 'N/A')}")
            print(f"   投手記錄: {db_stats.get('pitcher_records', 'N/A')}")
            print(f"   覆蓋率: {db_stats.get('coverage_rate', 'N/A')}")
            print(f"   高信心度記錄: {quality.get('high_confidence_records', 'N/A')}")
            print(f"   已驗證記錄: {quality.get('verified_records', 'N/A')}")
            print(f"   質量評分: {quality.get('quality_score', 'N/A')}")
            
            recommendations = summary.get('recommendations', [])
            if recommendations:
                print("\n💡 建議:")
                for rec in recommendations:
                    print(f"   • {rec}")
        
        print(f"\n✅ 建構完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()