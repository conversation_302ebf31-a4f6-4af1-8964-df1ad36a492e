#!/usr/bin/env python3
"""
更新延期比賽數據腳本
檢測並標記game_id與實際比賽日期不符的延期比賽
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from models.database import db, Game
from datetime import datetime, date
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app():
    """創建Flask應用"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/mlb_data.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

def detect_postponed_games():
    """檢測延期比賽"""
    app = create_app()
    
    with app.app_context():
        logger.info("開始檢測延期比賽...")
        
        # 查詢所有比賽
        games = Game.query.all()
        postponed_count = 0
        updated_count = 0
        
        for game in games:
            try:
                expected_date = None
                
                # 檢測777xxx格式的game_id (應該是2025-07-07)
                if game.game_id.startswith('777'):
                    expected_date = date(2025, 7, 7)
                    
                # 檢測YYYYMMDD格式的game_id
                elif len(game.game_id) >= 8 and game.game_id[:8].isdigit():
                    date_str = game.game_id[:8]
                    expected_date = datetime.strptime(date_str, '%Y%m%d').date()
                
                # 如果預期日期與實際日期不符，標記為延期
                if expected_date and expected_date != game.date:
                    postponed_count += 1
                    
                    # 更新比賽信息
                    if not game.original_date:
                        game.original_date = expected_date
                        game.is_makeup_game = True
                        game.game_status = 'rescheduled'
                        game.postponement_reason = f'從 {expected_date} 延期'
                        updated_count += 1
                        
                        logger.info(f"標記延期比賽: {game.game_id} - {game.away_team} @ {game.home_team}")
                        logger.info(f"  原定: {expected_date} -> 實際: {game.date}")
                        
            except Exception as e:
                logger.warning(f"處理比賽 {game.game_id} 時出錯: {e}")
                continue
        
        # 提交更改
        if updated_count > 0:
            try:
                db.session.commit()
                logger.info(f"✅ 成功更新 {updated_count} 場延期比賽")
            except Exception as e:
                db.session.rollback()
                logger.error(f"❌ 更新失敗: {e}")
        
        logger.info(f"檢測完成: 發現 {postponed_count} 場延期比賽，更新 {updated_count} 場")
        
        return postponed_count, updated_count

def show_postponed_games_summary():
    """顯示延期比賽摘要"""
    app = create_app()
    
    with app.app_context():
        logger.info("\n=== 延期比賽摘要 ===")
        
        # 查詢延期比賽
        postponed_games = Game.query.filter(
            Game.is_makeup_game == True
        ).order_by(Game.date).all()
        
        if not postponed_games:
            logger.info("沒有發現延期比賽")
            return
        
        logger.info(f"總共 {len(postponed_games)} 場延期比賽:")
        
        # 按日期分組
        date_groups = {}
        for game in postponed_games:
            actual_date = game.date.strftime('%Y-%m-%d')
            if actual_date not in date_groups:
                date_groups[actual_date] = []
            date_groups[actual_date].append(game)
        
        for actual_date, games in sorted(date_groups.items()):
            logger.info(f"\n📅 {actual_date} ({len(games)} 場延期比賽):")
            for game in games:
                original_date = game.original_date.strftime('%Y-%m-%d') if game.original_date else 'Unknown'
                logger.info(f"  {game.game_id}: {game.away_team} @ {game.home_team} (原定: {original_date})")

def add_database_columns():
    """添加新的數據庫列"""
    app = create_app()
    
    with app.app_context():
        try:
            # 檢查是否需要添加新列
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('games')]
            
            new_columns = [
                'original_date',
                'is_makeup_game', 
                'postponement_reason',
                'rescheduled_from_game_id',
                'doubleheader_game'
            ]
            
            missing_columns = [col for col in new_columns if col not in columns]
            
            if missing_columns:
                logger.info(f"需要添加的列: {missing_columns}")
                
                # 創建所有表（包括新列）
                db.create_all()
                logger.info("✅ 數據庫結構已更新")
            else:
                logger.info("✅ 數據庫結構已是最新")
                
        except Exception as e:
            logger.error(f"❌ 更新數據庫結構失敗: {e}")

if __name__ == '__main__':
    print("=== MLB 延期比賽數據更新工具 ===")
    
    # 1. 更新數據庫結構
    print("\n1. 檢查數據庫結構...")
    add_database_columns()
    
    # 2. 檢測並更新延期比賽
    print("\n2. 檢測延期比賽...")
    postponed_count, updated_count = detect_postponed_games()
    
    # 3. 顯示摘要
    print("\n3. 延期比賽摘要...")
    show_postponed_games_summary()
    
    print(f"\n✅ 完成! 發現 {postponed_count} 場延期比賽，更新 {updated_count} 場")
