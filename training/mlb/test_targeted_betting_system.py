#!/usr/bin/env python3
"""
測試目標性博彩預測系統
驗證真實API密鑰和預測功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, timedelta
from flask import Flask
from models.database import db, Game
from models.targeted_betting_predictor import TargetedBettingPredictor
from models.real_betting_odds_fetcher import RealBettingOddsFetcher

def create_test_app():
    """創建測試Flask應用"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///mlb_data.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    return app

def test_api_key_status():
    """測試API密鑰狀態"""
    print("=" * 80)
    print("🔑 測試API密鑰狀態")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        print(f"API密鑰狀態: {'✅ 已配置' if fetcher.odds_api_key else '❌ 未配置'}")
        
        if fetcher.odds_api_key:
            # 顯示部分密鑰
            masked_key = fetcher.odds_api_key[:8] + "..." + fetcher.odds_api_key[-4:]
            print(f"API密鑰: {masked_key}")
            
            # 測試API連接
            status = fetcher.check_api_status()
            print(f"API連接狀態: {'✅ 正常' if status['api_accessible'] else '❌ 異常'}")
            print(f"API訊息: {status['message']}")
            
            return True
        else:
            print("❌ 無法進行後續測試，需要API密鑰")
            return False

def test_real_odds_fetching():
    """測試真實博彩盤口獲取"""
    print("\n" + "=" * 80)
    print("📊 測試真實博彩盤口獲取")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        fetcher = RealBettingOddsFetcher(app)
        
        if not fetcher.odds_api_key:
            print("❌ 沒有API密鑰，跳過測試")
            return False
        
        print("正在獲取今日MLB博彩盤口...")
        odds_data = fetcher.get_mlb_odds_today()
        
        print(f"📈 獲取結果:")
        print(f"比賽數量: {odds_data['summary']['total_games']}")
        print(f"可用市場: {', '.join(odds_data['summary']['markets_available'])}")
        
        if odds_data['summary'].get('note'):
            print(f"⚠️  注意: {odds_data['summary']['note']}")
            return False
        else:
            print("✅ 成功獲取真實博彩盤口")
            
            # 顯示前2場比賽的盤口
            if odds_data['games']:
                print(f"\n🏟️  前2場比賽的真實盤口:")
                for i, game in enumerate(odds_data['games'][:2]):
                    print(f"\n比賽 {i+1}: {game['away_team']} @ {game['home_team']}")
                    
                    # 大小分
                    total = game['odds']['total']
                    if total['line']:
                        print(f"  大小分: {total['line']} (大分 {total['over_odds']} | 小分 {total['under_odds']})")
                    
                    # 讓分盤
                    rl = game['odds']['run_line']
                    if rl['home_line']:
                        print(f"  讓分盤: 主隊 {rl['home_line']} ({rl['home_odds']}) | 客隊 {rl['away_line']} ({rl['away_odds']})")
            
            return True

def test_targeted_predictions():
    """測試目標性預測"""
    print("\n" + "=" * 80)
    print("🎯 測試目標性預測系統")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        predictor = TargetedBettingPredictor(app)
        
        # 測試今日預測
        print("正在生成今日目標性預測...")
        today_predictions = predictor.generate_targeted_predictions()
        
        if 'error' in today_predictions:
            print(f"❌ 今日預測失敗: {today_predictions['error']}")
            return False
        
        print(f"✅ 今日預測成功")
        print(f"總預測數: {today_predictions['total_predictions']}")
        print(f"真實盤口預測: {today_predictions['real_odds_predictions']}")
        
        if today_predictions['predictions']:
            print(f"\n📊 預測詳情:")
            for i, pred in enumerate(today_predictions['predictions'][:2]):  # 只顯示前2個
                print(f"\n比賽 {i+1}: {pred['teams']['away']} @ {pred['teams']['home']}")
                print(f"盤口來源: {pred['odds_source']}")
                print(f"預測質量: {pred['prediction_quality']['grade']} ({pred['prediction_quality']['score']:.1f}分)")
                
                # 大小分預測
                if pred['over_under']:
                    ou = pred['over_under']
                    print(f"大小分: {ou['total_line']} (預期 {ou['expected_runs']['total']:.1f}分)")
                    print(f"大分概率: {ou['over_probability']:.1%}")
                    print(f"推薦: {ou.get('recommendation', '無推薦')}")
                
                # 讓分盤預測
                if pred['run_line']:
                    rl = pred['run_line']
                    print(f"讓分盤: 主隊 {rl['run_line_data']['home_line']} | 客隊 {rl['run_line_data']['away_line']}")
                    if rl.get('prediction'):
                        print(f"推薦: {rl['prediction'].get('recommendation', '無推薦')}")
        
        return True

def test_weekly_targets():
    """測試週預測目標"""
    print("\n" + "=" * 80)
    print("📅 測試週預測目標")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        predictor = TargetedBettingPredictor(app)
        
        print("正在獲取本週預測目標...")
        week_targets = predictor.get_prediction_targets_for_week()
        
        if 'error' in week_targets:
            print(f"❌ 週目標獲取失敗: {week_targets['error']}")
            return False
        
        print(f"✅ 週目標獲取成功")
        summary = week_targets['week_summary']
        print(f"總天數: {summary['total_days']}")
        print(f"總比賽: {summary['total_games']}")
        print(f"真實盤口比賽: {summary['real_odds_games']}")
        print(f"建議: {week_targets['recommendation']}")
        
        # 顯示每日目標
        if week_targets['daily_targets']:
            print(f"\n📊 每日目標摘要:")
            for date_str, daily_data in list(week_targets['daily_targets'].items())[:3]:  # 只顯示前3天
                print(f"{date_str}: {daily_data['total_games']}場比賽 ({daily_data['real_odds_count']}場有真實盤口)")
        
        return True

def test_upcoming_games():
    """測試未來比賽獲取"""
    print("\n" + "=" * 80)
    print("🔮 測試未來比賽獲取")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        predictor = TargetedBettingPredictor(app)
        
        print("正在獲取未來3天的比賽...")
        upcoming_games = predictor.get_upcoming_games_with_odds(3)
        
        if 'error' in upcoming_games:
            print(f"❌ 未來比賽獲取失敗: {upcoming_games['error']}")
            return False
        
        print(f"✅ 未來比賽獲取成功")
        print(f"總比賽: {upcoming_games['total_games']}")
        print(f"有真實盤口: {upcoming_games['games_with_real_odds']}")
        
        if upcoming_games['games']:
            print(f"\n📊 未來比賽摘要:")
            date_groups = {}
            for game_data in upcoming_games['games']:
                date_str = game_data['date'].isoformat()
                if date_str not in date_groups:
                    date_groups[date_str] = {'total': 0, 'real_odds': 0}
                date_groups[date_str]['total'] += 1
                if game_data['has_real_odds']:
                    date_groups[date_str]['real_odds'] += 1
            
            for date_str, counts in list(date_groups.items())[:3]:  # 只顯示前3天
                print(f"{date_str}: {counts['total']}場比賽 ({counts['real_odds']}場有真實盤口)")
        
        return True

def test_prediction_quality():
    """測試預測質量評估"""
    print("\n" + "=" * 80)
    print("⭐ 測試預測質量評估")
    print("=" * 80)
    
    app = create_test_app()
    
    with app.app_context():
        predictor = TargetedBettingPredictor(app)
        
        # 獲取今日預測
        predictions = predictor.generate_targeted_predictions()
        
        if 'error' in predictions or not predictions['predictions']:
            print("❌ 無法獲取預測數據進行質量測試")
            return False
        
        print("✅ 預測質量分析:")
        
        quality_stats = {
            'A級': 0, 'B級': 0, 'C級': 0, 'D級': 0,
            'real_odds': 0, 'simulated_odds': 0
        }
        
        for pred in predictions['predictions']:
            grade = pred['prediction_quality']['grade'].split('級')[0] + '級'
            quality_stats[grade] += 1
            
            if pred['has_real_odds']:
                quality_stats['real_odds'] += 1
            else:
                quality_stats['simulated_odds'] += 1
        
        print(f"質量分布:")
        for grade in ['A級', 'B級', 'C級', 'D級']:
            count = quality_stats[grade]
            if count > 0:
                print(f"  {grade}: {count}個預測")
        
        print(f"盤口來源:")
        print(f"  真實盤口: {quality_stats['real_odds']}個預測")
        print(f"  模擬盤口: {quality_stats['simulated_odds']}個預測")
        
        return True

def main():
    """主測試函數"""
    print("🎯 MLB目標性博彩預測系統完整測試")
    print("測試時間:", date.today().strftime('%Y-%m-%d'))
    print("目標日期: 6月28日後的比賽")
    
    test_results = []
    
    try:
        # 1. 測試API密鑰
        result1 = test_api_key_status()
        test_results.append(("API密鑰配置", result1))
        
        if not result1:
            print("\n❌ API密鑰未配置，無法進行後續測試")
            return
        
        # 2. 測試真實盤口獲取
        result2 = test_real_odds_fetching()
        test_results.append(("真實盤口獲取", result2))
        
        # 3. 測試目標性預測
        result3 = test_targeted_predictions()
        test_results.append(("目標性預測", result3))
        
        # 4. 測試週預測目標
        result4 = test_weekly_targets()
        test_results.append(("週預測目標", result4))
        
        # 5. 測試未來比賽
        result5 = test_upcoming_games()
        test_results.append(("未來比賽獲取", result5))
        
        # 6. 測試預測質量
        result6 = test_prediction_quality()
        test_results.append(("預測質量評估", result6))
        
        # 總結
        print("\n" + "=" * 80)
        print("📊 測試結果總結")
        print("=" * 80)
        
        passed = sum(1 for _, result in test_results if result)
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"{test_name}: {status}")
        
        print(f"\n總體結果: {passed}/{total} 測試通過")
        
        if passed == total:
            print("🎉 所有測試通過！目標性博彩預測系統已準備就緒")
            print("\n🚀 下一步:")
            print("1. 啟動Flask應用: python app.py")
            print("2. 訪問: http://127.0.0.1:5500/predictions/targeted_betting")
            print("3. 開始使用真實博彩盤口進行預測")
        else:
            print("⚠️  部分測試失敗，請檢查相關配置")
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
