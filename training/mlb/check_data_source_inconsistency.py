#!/usr/bin/env python3
"""
檢查不同頁面數據來源的不一致問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from datetime import datetime, date

def check_data_sources():
    """檢查不同數據來源的一致性"""
    print("🔍 檢查不同頁面數據來源的一致性")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        target_date = '2025-07-09'
        
        print(f"📅 檢查日期: {target_date}")
        print()
        
        # 1. 檢查predictions表的數據 (predictions/over_under頁面使用)
        print("1️⃣  predictions表數據 (predictions/over_under頁面):")
        cursor.execute("""
            SELECT 
                g.away_team || '@' || g.home_team as matchup,
                p.over_under_line,
                p.predicted_total_runs,
                p.model_version
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date = ?
            ORDER BY g.game_id
        """, (target_date,))
        
        predictions_data = cursor.fetchall()
        
        if predictions_data:
            print(f"   找到 {len(predictions_data)} 個預測記錄")
            for matchup, line, total, model_version in predictions_data[:5]:
                print(f"   {matchup}: 盤口 {line}, 總分 {total:.1f}, 模型 {model_version}")
            if len(predictions_data) > 5:
                print(f"   ... 還有 {len(predictions_data) - 5} 個記錄")
        else:
            print("   ❌ 沒有找到預測記錄")
        
        print()
        
        # 2. 檢查betting_odds表的數據 (unified頁面使用)
        print("2️⃣  betting_odds表數據 (unified頁面):")
        cursor.execute("""
            SELECT 
                g.away_team || '@' || g.home_team as matchup,
                bo.total_point,
                bo.bookmaker,
                COUNT(*) as count
            FROM betting_odds bo
            JOIN games g ON bo.game_id = g.game_id
            WHERE g.date = ? AND bo.market_type = 'totals'
            GROUP BY g.game_id, bo.bookmaker
            ORDER BY g.game_id, bo.bookmaker
        """, (target_date,))
        
        betting_odds_data = cursor.fetchall()
        
        if betting_odds_data:
            print(f"   找到 {len(betting_odds_data)} 個博彩盤口記錄")
            for matchup, total_point, bookmaker, count in betting_odds_data[:10]:
                print(f"   {matchup}: 盤口 {total_point}, 博彩商 {bookmaker}")
            if len(betting_odds_data) > 10:
                print(f"   ... 還有 {len(betting_odds_data) - 10} 個記錄")
        else:
            print("   ❌ 沒有找到博彩盤口記錄")
        
        print()
        
        # 3. 檢查數據一致性
        print("3️⃣  數據一致性檢查:")
        cursor.execute("""
            SELECT 
                g.away_team || '@' || g.home_team as matchup,
                p.over_under_line as pred_line,
                bo.total_point as betting_line,
                bo.bookmaker,
                CASE 
                    WHEN ABS(p.over_under_line - bo.total_point) < 0.1 THEN '✅ 一致'
                    WHEN ABS(p.over_under_line - bo.total_point) < 1.0 THEN '⚠️ 接近'
                    ELSE '❌ 不一致'
                END as status
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            LEFT JOIN betting_odds bo ON p.game_id = bo.game_id 
                AND bo.market_type = 'totals'
                AND bo.bookmaker = 'bet365'
            WHERE g.date = ?
            ORDER BY g.game_id
        """, (target_date,))
        
        consistency_data = cursor.fetchall()
        
        if consistency_data:
            print("   比賽對戰        | 預測盤口 | 博彩盤口 | 博彩商 | 狀態")
            print("   " + "-" * 60)
            
            consistent_count = 0
            total_count = 0
            
            for matchup, pred_line, betting_line, bookmaker, status in consistency_data:
                total_count += 1
                if '✅' in status:
                    consistent_count += 1
                
                pred_str = f"{pred_line:.1f}" if pred_line else "N/A"
                betting_str = f"{betting_line:.1f}" if betting_line else "N/A"
                bookmaker_str = bookmaker if bookmaker else "N/A"
                
                print(f"   {matchup:15s} | {pred_str:8s} | {betting_str:8s} | {bookmaker_str:7s} | {status}")
            
            consistency_rate = (consistent_count / total_count * 100) if total_count > 0 else 0
            print(f"\n   📊 一致性統計: {consistent_count}/{total_count} ({consistency_rate:.1f}%)")
        
        print()
        
        # 4. 檢查unified系統的model_version
        print("4️⃣  模型版本檢查:")
        cursor.execute("""
            SELECT DISTINCT model_version, COUNT(*) as count
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date = ?
            GROUP BY model_version
            ORDER BY count DESC
        """, (target_date,))
        
        model_versions = cursor.fetchall()
        
        if model_versions:
            for version, count in model_versions:
                print(f"   模型版本 '{version}': {count} 個預測")
        else:
            print("   ❌ 沒有找到模型版本信息")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()

def identify_root_cause():
    """識別根本原因"""
    print("\n🎯 根本原因分析")
    print("=" * 70)
    
    print("根據檢查結果，可能的原因:")
    print("1. 不同頁面使用不同的數據表:")
    print("   - predictions/over_under: 使用 predictions.over_under_line")
    print("   - unified頁面: 使用 betting_odds.total_point")
    print()
    print("2. 數據同步問題:")
    print("   - predictions表可能沒有及時更新")
    print("   - betting_odds表可能有重複記錄")
    print()
    print("3. 模型版本不一致:")
    print("   - unified系統可能使用特定的model_version")
    print("   - predictions/over_under可能使用所有版本")

def fix_data_consistency():
    """修復數據一致性問題"""
    print("\n🔧 修復數據一致性")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        target_date = '2025-07-09'
        
        # 1. 確保predictions表使用正確的盤口
        print("1️⃣  同步predictions表的盤口數據...")
        
        cursor.execute("""
            UPDATE predictions 
            SET over_under_line = (
                SELECT bo.total_point 
                FROM betting_odds bo 
                WHERE bo.game_id = predictions.game_id 
                AND bo.market_type = 'totals' 
                AND bo.bookmaker = 'bet365'
                LIMIT 1
            ),
            updated_at = ?
            WHERE game_id IN (
                SELECT g.game_id 
                FROM games g 
                WHERE g.date = ?
            )
            AND EXISTS (
                SELECT 1 
                FROM betting_odds bo 
                WHERE bo.game_id = predictions.game_id 
                AND bo.market_type = 'totals' 
                AND bo.bookmaker = 'bet365'
            )
        """, (datetime.now().isoformat(), target_date))
        
        updated_count = cursor.rowcount
        print(f"   ✅ 更新了 {updated_count} 個預測記錄的盤口")
        
        # 2. 清理重複的betting_odds記錄
        print("\n2️⃣  清理重複的博彩盤口記錄...")
        
        cursor.execute("""
            DELETE FROM betting_odds 
            WHERE rowid NOT IN (
                SELECT MIN(rowid) 
                FROM betting_odds 
                GROUP BY game_id, bookmaker, market_type
            )
        """)
        
        deleted_count = cursor.rowcount
        print(f"   ✅ 刪除了 {deleted_count} 個重複記錄")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 數據一致性修復完成！")
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_data_sources()
    identify_root_cause()
    fix_data_consistency()
    
    print("\n" + "=" * 70)
    print("🔄 建議刷新網頁查看修復效果")
    print("=" * 70)
