#!/usr/bin/env python3
"""
簡化的投手載入測試
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath('.')))

from app import create_app
from models.enhanced_feature_engineer import EnhancedFeatureEngineer
from datetime import date

def test_pitcher_mapping():
    """測試投手載入和球隊名稱映射"""
    app = create_app()
    
    with app.app_context():
        print("🧪 測試投手載入功能")
        
        # 初始化特徵工程器
        feature_engineer = EnhancedFeatureEngineer()
        
        # 測試球隊名稱映射
        test_teams = ['CHC', 'NYY', 'SEA', 'DET']
        print("\n📋 球隊名稱映射測試:")
        for team in test_teams:
            mapped_name = feature_engineer._get_team_name_mapping(team)
            print(f"  {team} -> {mapped_name}")
        
        # 測試投手獲取
        test_date = date(2025, 7, 12)
        print(f"\n🎯 測試日期: {test_date}")
        
        for team in test_teams[:2]:  # 只測試前兩個球隊
            print(f"\n🔍 測試球隊: {team}")
            try:
                pitcher = feature_engineer._get_probable_starter(team, test_date)
                print(f"  先發投手: {pitcher or '未找到'}")
            except Exception as e:
                print(f"  錯誤: {e}")

if __name__ == "__main__":
    test_pitcher_mapping()
