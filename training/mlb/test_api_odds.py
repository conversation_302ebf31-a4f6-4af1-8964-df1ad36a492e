#!/usr/bin/env python3
"""
測試現有API系統獲取歷史賠率數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.odds_data_fetcher import OddsDataFetcher
from datetime import date, timedelta
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)

def test_api_odds():
    """測試API賠率獲取功能"""
    
    # 創建Flask應用
    app = create_app()
    
    with app.app_context():
        # 創建抓取器
        fetcher = OddsDataFetcher()
        
        print('=== API狀態檢查 ===')
        status = fetcher.check_api_status()
        print(f'API密鑰配置: {status["api_key_configured"]}')
        print(f'API可訪問: {status["api_accessible"]}')
        print(f'剩餘請求數: {status["remaining_requests"]}')
        if 'message' in status:
            print(f'訊息: {status["message"]}')
        
        # 測試不同日期的歷史數據
        test_dates = [
            date(2025, 7, 7),   # 最近日期
            date(2025, 7, 6),   # 前一天
            date(2025, 6, 29),  # 更早的日期
            date.today()        # 今天
        ]
        
        for test_date in test_dates:
            print(f'\n=== 測試日期: {test_date} ===')
            
            result = fetcher.fetch_odds_for_games(test_date)
            
            if result['success']:
                print(f'✅ 成功獲取數據')
                print(f'   總比賽數: {result["total_games"]}')
                print(f'   匹配比賽數: {result["matched_games"]}')
                if result.get('games'):
                    print(f'   第一場比賽示例: {result["games"][0]["game_info"]["away_team"]} vs {result["games"][0]["game_info"]["home_team"]}')
            else:
                print(f'❌ 獲取失敗: {result["error"]}')
        
        # 檢查API是否支持歷史數據
        print(f'\n=== API歷史數據支持檢查 ===')
        print('The Odds API 通常只提供未來比賽的賠率數據')
        print('對於已完成的比賽，API可能不再提供賠率數據')
        print('這就是為什麼我們需要替代數據源的原因')

if __name__ == '__main__':
    test_api_odds()
