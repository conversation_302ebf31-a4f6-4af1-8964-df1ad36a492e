#!/usr/bin/env python3
"""
測試網頁抓取優先級配置
驗證系統優先使用網頁抓取而不是模擬API數據
"""

import os
import sys
from datetime import date, timedelta
import logging

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.real_betting_odds_fetcher import RealBettingOddsFetcher

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_web_scraping_priority():
    """測試網頁抓取優先級"""
    
    print("🧪 測試網頁抓取優先級配置")
    print("=" * 50)
    
    # 創建Flask應用
    app = create_app()
    
    with app.app_context():
        # 初始化博彩盤口獲取器
        fetcher = RealBettingOddsFetcher(app)
        
        # 測試日期
        test_dates = [
            date.today(),
            date.today() - timedelta(days=1),
            date(2025, 7, 7)  # 已知有數據的日期
        ]
        
        for test_date in test_dates:
            print(f"\n📅 測試日期: {test_date}")
            print("-" * 30)
            
            try:
                # 獲取博彩盤口數據
                result = fetcher.get_mlb_odds_today(test_date)
                
                if result.get('success'):
                    games = result.get('games', [])
                    summary = result.get('summary', {})
                    
                    print(f"✅ 成功獲取數據")
                    print(f"   比賽數量: {len(games)}")
                    print(f"   數據來源: {summary.get('data_source', 'unknown')}")
                    print(f"   真實數據: {summary.get('is_real_data', False)}")
                    
                    if summary.get('note'):
                        print(f"   備註: {summary.get('note')}")
                    
                    # 顯示前幾場比賽的詳細信息
                    for i, game in enumerate(games[:2]):
                        print(f"   比賽 {i+1}: {game.get('away_team', 'N/A')} @ {game.get('home_team', 'N/A')}")
                        bookmakers = game.get('bookmakers', [])
                        if bookmakers:
                            bookmaker = bookmakers[0]
                            print(f"     博彩商: {bookmaker.get('title', 'N/A')}")
                            markets = bookmaker.get('markets', [])
                            for market in markets:
                                market_key = market.get('key', 'unknown')
                                outcomes = market.get('outcomes', [])
                                print(f"     {market_key}: {len(outcomes)} 個選項")
                
                else:
                    print(f"❌ 獲取失敗")
                    print(f"   錯誤信息: {result.get('message', 'unknown error')}")
                    summary = result.get('summary', {})
                    if summary:
                        print(f"   數據來源: {summary.get('data_source', 'unknown')}")
                        print(f"   真實數據: {summary.get('is_real_data', False)}")
                        if summary.get('note'):
                            print(f"   備註: {summary.get('note')}")
                
            except Exception as e:
                print(f"❌ 測試出錯: {e}")
                import traceback
                traceback.print_exc()

def test_individual_scrapers():
    """測試各個抓取器"""
    
    print("\n🔍 測試各個網頁抓取器")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        test_date = date(2025, 7, 7)
        
        # 測試Covers抓取器
        print(f"\n📊 測試 Covers.com 抓取器")
        try:
            from models.covers_scraper import CoversMLBScraper
            scraper = CoversMLBScraper()
            result = scraper.fetch_mlb_games_for_date(test_date)
            
            if result.get('success'):
                games = result.get('games', [])
                print(f"✅ Covers.com: {len(games)} 場比賽")
                if games:
                    game = games[0]
                    print(f"   示例: {game.get('away_team', 'N/A')} @ {game.get('home_team', 'N/A')}")
                    odds = game.get('odds', {})
                    if odds:
                        print(f"   盤口: spread={odds.get('spread_line')}, total={odds.get('total_line')}")
            else:
                print(f"❌ Covers.com 失敗: {result.get('error', 'unknown')}")
                
        except Exception as e:
            print(f"❌ Covers.com 出錯: {e}")
        
        # 測試SportsBookReview抓取器
        print(f"\n📊 測試 SportsBookReview 抓取器")
        try:
            from models.sportsbookreview_scraper import SportsBookReviewScraper
            scraper = SportsBookReviewScraper()
            result = scraper.fetch_historical_odds(test_date, bookmaker='bet365')
            
            if result.get('success'):
                games = result.get('games', [])
                print(f"✅ SportsBookReview: {len(games)} 場比賽")
                if games:
                    game = games[0]
                    print(f"   示例: {game.get('away_team', 'N/A')} @ {game.get('home_team', 'N/A')}")
            else:
                print(f"❌ SportsBookReview 失敗: {result.get('error', 'unknown')}")
                
        except Exception as e:
            print(f"❌ SportsBookReview 出錯: {e}")

def main():
    """主函數"""
    print("🚀 開始測試網頁抓取優先級配置")
    
    # 測試主要功能
    test_web_scraping_priority()
    
    # 測試各個抓取器
    test_individual_scrapers()
    
    print("\n✅ 測試完成！")
    print("\n💡 配置說明:")
    print("   - 系統現在優先使用網頁抓取獲取真實博彩盤口")
    print("   - 拒絕使用模擬API數據")
    print("   - 只有在獲取到真實數據時才會返回結果")
    print("   - 如果沒有真實數據，會返回空結果而不是模擬數據")

if __name__ == "__main__":
    main()
