#!/usr/bin/env python3
"""
分析極端比賽和隊伍穩定性
識別低分比賽、高分比賽、主客場穩定性等
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction, BettingOdds
import numpy as np
import pandas as pd
from collections import defaultdict
import json

def analyze_extreme_games():
    """分析極端比賽模式"""
    app = create_app()
    
    with app.app_context():
        print("🔍 分析極端比賽和隊伍穩定性...")
        
        # 獲取歷史數據 (最近90天)
        query = """
        SELECT 
            g.game_id,
            g.date,
            g.home_team,
            g.away_team,
            g.home_score,
            g.away_score,
            (g.home_score + g.away_score) as total_score,
            g.game_status,
            p.predicted_home_score,
            p.predicted_away_score,
            (p.predicted_home_score + p.predicted_away_score) as predicted_total,
            p.confidence,
            bo.total_point as betting_line
        FROM games g
        LEFT JOIN predictions p ON g.game_id = p.game_id
        LEFT JOIN betting_odds bo ON g.game_id = bo.game_id
        WHERE g.home_score IS NOT NULL 
        AND g.away_score IS NOT NULL
        AND g.date >= DATE('now', '-90 days')
        ORDER BY g.date DESC
        """
        
        results = db.session.execute(db.text(query)).fetchall()
        
        columns = ['game_id', 'date', 'home_team', 'away_team', 'home_score', 'away_score', 
                  'total_score', 'game_status', 'predicted_home_score', 'predicted_away_score', 
                  'predicted_total', 'confidence', 'betting_line']
        
        df = pd.DataFrame(results, columns=columns)
        
        print(f"📊 分析 {len(df)} 場比賽的數據")
        
        # 1. 識別極端比賽
        identify_extreme_games(df)
        
        # 2. 分析隊伍穩定性
        analyze_team_stability(df)
        
        # 3. 主客場表現分析
        analyze_home_away_patterns(df)
        
        # 4. 低分/高分比賽模式
        analyze_scoring_patterns(df)
        
        # 5. 預測風險評估
        create_risk_assessment_model(df)

def identify_extreme_games(df):
    """識別極端比賽"""
    print(f"\n🚨 極端比賽識別:")
    
    # 定義極端比賽標準
    total_mean = df['total_score'].mean()
    total_std = df['total_score'].std()
    
    # 極低分比賽 (< 平均 - 1.5 標準差)
    low_score_threshold = total_mean - 1.5 * total_std
    low_score_games = df[df['total_score'] < low_score_threshold]
    
    # 極高分比賽 (> 平均 + 1.5 標準差)
    high_score_threshold = total_mean + 1.5 * total_std
    high_score_games = df[df['total_score'] > high_score_threshold]
    
    # 一邊倒比賽 (分差 > 10分)
    df['score_diff'] = abs(df['home_score'] - df['away_score'])
    blowout_games = df[df['score_diff'] > 10]
    
    print(f"  總分統計: 平均 {total_mean:.1f} ± {total_std:.1f}")
    print(f"  極低分比賽 (< {low_score_threshold:.1f}): {len(low_score_games)} 場 ({len(low_score_games)/len(df)*100:.1f}%)")
    print(f"  極高分比賽 (> {high_score_threshold:.1f}): {len(high_score_games)} 場 ({len(high_score_games)/len(df)*100:.1f}%)")
    print(f"  一邊倒比賽 (分差>10): {len(blowout_games)} 場 ({len(blowout_games)/len(df)*100:.1f}%)")
    
    # 顯示極端比賽案例
    print(f"\n  極低分比賽案例:")
    for _, game in low_score_games.head(5).iterrows():
        print(f"    {game['date']} {game['away_team']}@{game['home_team']}: {game['away_score']}-{game['home_score']} (總分 {game['total_score']})")
    
    print(f"\n  極高分比賽案例:")
    for _, game in high_score_games.head(5).iterrows():
        print(f"    {game['date']} {game['away_team']}@{game['home_team']}: {game['away_score']}-{game['home_score']} (總分 {game['total_score']})")
    
    # 分析極端比賽的預測表現
    if 'predicted_total' in df.columns:
        extreme_games = pd.concat([low_score_games, high_score_games])
        extreme_with_pred = extreme_games[extreme_games['predicted_total'].notna()]
        
        if len(extreme_with_pred) > 0:
            pred_error = abs(extreme_with_pred['predicted_total'] - extreme_with_pred['total_score']).mean()
            normal_games = df[~df.index.isin(extreme_games.index)]
            normal_with_pred = normal_games[normal_games['predicted_total'].notna()]
            normal_error = abs(normal_with_pred['predicted_total'] - normal_with_pred['total_score']).mean()
            
            print(f"\n  預測表現對比:")
            print(f"    極端比賽平均誤差: {pred_error:.1f} 分")
            print(f"    正常比賽平均誤差: {normal_error:.1f} 分")
            print(f"    極端比賽誤差增加: {pred_error - normal_error:+.1f} 分")

def analyze_team_stability(df):
    """分析隊伍穩定性"""
    print(f"\n🏟️ 隊伍穩定性分析:")
    
    team_stats = {}
    
    # 分析每個隊伍的得分穩定性
    all_teams = pd.concat([df['home_team'], df['away_team']]).unique()
    
    for team in all_teams:
        if pd.isna(team):
            continue
            
        # 主場表現
        home_games = df[df['home_team'] == team]
        home_scores = home_games['home_score']
        
        # 客場表現
        away_games = df[df['away_team'] == team]
        away_scores = away_games['away_score']
        
        # 總體表現
        all_scores = pd.concat([home_scores, away_scores])
        
        if len(all_scores) >= 5:  # 至少5場比賽
            team_stats[team] = {
                'games_count': len(all_scores),
                'avg_score': all_scores.mean(),
                'score_std': all_scores.std(),
                'score_cv': all_scores.std() / all_scores.mean() if all_scores.mean() > 0 else 0,  # 變異係數
                'home_games': len(home_games),
                'away_games': len(away_games),
                'home_avg': home_scores.mean() if len(home_scores) > 0 else 0,
                'away_avg': away_scores.mean() if len(away_scores) > 0 else 0,
                'home_std': home_scores.std() if len(home_scores) > 0 else 0,
                'away_std': away_scores.std() if len(away_scores) > 0 else 0,
            }
    
    # 按穩定性排序 (變異係數越小越穩定)
    stable_teams = sorted(team_stats.items(), key=lambda x: x[1]['score_cv'])
    unstable_teams = sorted(team_stats.items(), key=lambda x: x[1]['score_cv'], reverse=True)
    
    print(f"  最穩定的隊伍 (變異係數最小):")
    for team, stats in stable_teams[:5]:
        print(f"    {team}: 平均 {stats['avg_score']:.1f}±{stats['score_std']:.1f} 分, CV={stats['score_cv']:.3f} ({stats['games_count']} 場)")
    
    print(f"\n  最不穩定的隊伍 (變異係數最大):")
    for team, stats in unstable_teams[:5]:
        print(f"    {team}: 平均 {stats['avg_score']:.1f}±{stats['score_std']:.1f} 分, CV={stats['score_cv']:.3f} ({stats['games_count']} 場)")
    
    return team_stats

def analyze_home_away_patterns(df):
    """分析主客場表現模式"""
    print(f"\n🏠 主客場表現分析:")
    
    team_home_away = {}
    
    all_teams = pd.concat([df['home_team'], df['away_team']]).unique()
    
    for team in all_teams:
        if pd.isna(team):
            continue
            
        home_games = df[df['home_team'] == team]
        away_games = df[df['away_team'] == team]
        
        if len(home_games) >= 3 and len(away_games) >= 3:
            home_avg = home_games['home_score'].mean()
            away_avg = away_games['away_score'].mean()
            home_std = home_games['home_score'].std()
            away_std = away_games['away_score'].std()
            
            team_home_away[team] = {
                'home_avg': home_avg,
                'away_avg': away_avg,
                'home_std': home_std,
                'away_std': away_std,
                'home_advantage': home_avg - away_avg,
                'home_games': len(home_games),
                'away_games': len(away_games)
            }
    
    # 主場優勢最大的隊伍
    home_advantage = sorted(team_home_away.items(), key=lambda x: x[1]['home_advantage'], reverse=True)
    
    print(f"  主場優勢最大的隊伍:")
    for team, stats in home_advantage[:5]:
        print(f"    {team}: 主場 {stats['home_avg']:.1f}, 客場 {stats['away_avg']:.1f}, 優勢 +{stats['home_advantage']:.1f}")
    
    print(f"\n  主場劣勢最大的隊伍:")
    for team, stats in home_advantage[-5:]:
        print(f"    {team}: 主場 {stats['home_avg']:.1f}, 客場 {stats['away_avg']:.1f}, 劣勢 {stats['home_advantage']:.1f}")
    
    # 主場最穩定的隊伍
    home_stable = sorted(team_home_away.items(), key=lambda x: x[1]['home_std'])
    
    print(f"\n  主場得分最穩定的隊伍:")
    for team, stats in home_stable[:5]:
        print(f"    {team}: 主場 {stats['home_avg']:.1f}±{stats['home_std']:.1f} ({stats['home_games']} 場)")
    
    # 客場最穩定的隊伍
    away_stable = sorted(team_home_away.items(), key=lambda x: x[1]['away_std'])
    
    print(f"\n  客場得分最穩定的隊伍:")
    for team, stats in away_stable[:5]:
        print(f"    {team}: 客場 {stats['away_avg']:.1f}±{stats['away_std']:.1f} ({stats['away_games']} 場)")

def analyze_scoring_patterns(df):
    """分析得分模式"""
    print(f"\n⚾ 得分模式分析:")
    
    # 低分比賽模式 (總分 < 7)
    low_scoring = df[df['total_score'] < 7]
    medium_scoring = df[(df['total_score'] >= 7) & (df['total_score'] <= 12)]
    high_scoring = df[df['total_score'] > 12]
    
    print(f"  得分分布:")
    print(f"    低分比賽 (<7分): {len(low_scoring)} 場 ({len(low_scoring)/len(df)*100:.1f}%)")
    print(f"    中分比賽 (7-12分): {len(medium_scoring)} 場 ({len(medium_scoring)/len(df)*100:.1f}%)")
    print(f"    高分比賽 (>12分): {len(high_scoring)} 場 ({len(high_scoring)/len(df)*100:.1f}%)")
    
    # 分析低分比賽的隊伍
    if len(low_scoring) > 0:
        print(f"\n  低分比賽最常見的隊伍:")
        low_score_teams = defaultdict(int)
        for _, game in low_scoring.iterrows():
            low_score_teams[game['home_team']] += 1
            low_score_teams[game['away_team']] += 1
        
        for team, count in sorted(low_score_teams.items(), key=lambda x: x[1], reverse=True)[:5]:
            total_games = len(df[(df['home_team'] == team) | (df['away_team'] == team)])
            percentage = count / total_games * 100 if total_games > 0 else 0
            print(f"    {team}: {count} 次低分比賽 / {total_games} 場總比賽 ({percentage:.1f}%)")
    
    # 分析高分比賽的隊伍
    if len(high_scoring) > 0:
        print(f"\n  高分比賽最常見的隊伍:")
        high_score_teams = defaultdict(int)
        for _, game in high_scoring.iterrows():
            high_score_teams[game['home_team']] += 1
            high_score_teams[game['away_team']] += 1
        
        for team, count in sorted(high_score_teams.items(), key=lambda x: x[1], reverse=True)[:5]:
            total_games = len(df[(df['home_team'] == team) | (df['away_team'] == team)])
            percentage = count / total_games * 100 if total_games > 0 else 0
            print(f"    {team}: {count} 次高分比賽 / {total_games} 場總比賽 ({percentage:.1f}%)")

def create_risk_assessment_model(df):
    """創建風險評估模型"""
    print(f"\n⚠️ 預測風險評估模型:")
    
    # 計算每場比賽的風險因子
    risk_factors = []
    
    for _, game in df.iterrows():
        if pd.isna(game['predicted_total']):
            continue
            
        risk_score = 0
        risk_reasons = []
        
        # 1. 極端總分風險
        if game['total_score'] < 5 or game['total_score'] > 20:
            risk_score += 3
            risk_reasons.append("極端總分")
        
        # 2. 一邊倒比賽風險
        score_diff = abs(game['home_score'] - game['away_score'])
        if score_diff > 10:
            risk_score += 2
            risk_reasons.append("一邊倒比賽")
        
        # 3. 預測誤差風險
        pred_error = abs(game['predicted_total'] - game['total_score'])
        if pred_error > 8:
            risk_score += 3
            risk_reasons.append("預測誤差大")
        elif pred_error > 5:
            risk_score += 1
            risk_reasons.append("預測誤差中等")
        
        # 4. 博彩盤口偏差風險
        if not pd.isna(game['betting_line']):
            line_diff = abs(game['total_score'] - game['betting_line'])
            if line_diff > 5:
                risk_score += 2
                risk_reasons.append("偏離盤口大")
        
        risk_factors.append({
            'game_id': game['game_id'],
            'date': game['date'],
            'teams': f"{game['away_team']}@{game['home_team']}",
            'risk_score': risk_score,
            'risk_reasons': risk_reasons,
            'actual_score': f"{game['away_score']}-{game['home_score']}",
            'predicted_total': game['predicted_total'],
            'actual_total': game['total_score']
        })
    
    # 按風險分數排序
    high_risk_games = sorted([r for r in risk_factors if r['risk_score'] >= 5], 
                           key=lambda x: x['risk_score'], reverse=True)
    
    print(f"  高風險比賽 (風險分數 ≥ 5): {len(high_risk_games)} 場")
    
    if high_risk_games:
        print(f"\n  最高風險比賽案例:")
        for game in high_risk_games[:5]:
            print(f"    {game['date']} {game['teams']}: {game['actual_score']} (風險分數: {game['risk_score']})")
            print(f"      風險因子: {', '.join(game['risk_reasons'])}")
            print(f"      預測總分: {game['predicted_total']:.1f}, 實際總分: {game['actual_total']}")
    
    # 保存風險評估規則
    risk_rules = {
        'extreme_score_threshold': {'low': 5, 'high': 20},
        'blowout_threshold': 10,
        'prediction_error_thresholds': {'medium': 5, 'high': 8},
        'betting_line_deviation_threshold': 5,
        'high_risk_score_threshold': 5
    }
    
    with open('models/risk_assessment_rules.json', 'w') as f:
        json.dump(risk_rules, f, indent=2)
    
    print(f"\n  ✅ 風險評估規則已保存到 models/risk_assessment_rules.json")

if __name__ == "__main__":
    print("🚀 開始分析極端比賽和隊伍穩定性...")
    
    analyze_extreme_games()
    
    print("\n✅ 分析完成！")
