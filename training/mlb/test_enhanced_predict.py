#!/usr/bin/env python3
"""
測試增強預測系統
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from enhanced_custom_predict import EnhancedCustomPredictor
import j<PERSON>

def test_enhanced_predict():
    """測試增強預測系統"""
    predictor = EnhancedCustomPredictor()
    
    target_date = date(2025, 7, 12)
    
    print("🔍 檢查現有預測...")
    status = predictor.check_existing_predictions(target_date)
    
    print(f"\n📊 {target_date} 預測狀況:")
    print(f"  總比賽數: {status['total_games']}")
    print(f"  已有預測: {len(status['existing_predictions'])}")
    print(f"  缺少預測: {len(status['missing_predictions'])}")
    
    # 測試強制重新生成模式
    print(f"\n🚀 測試強制重新生成模式...")
    options = {
        'force_regenerate': True,
        'update_existing': True,
        'skip_existing': False,
        'backup_existing': True,
        'model_version_suffix': 'test_v1',
        'exclude_postponed': True,
        'confidence_threshold': 0.0
    }
    
    results = predictor.predict_with_options(target_date, options)
    
    print(f"\n✅ 測試結果:")
    print(f"  成功: {results['success']}")
    print(f"  處理比賽: {results['processed_games']}/{results['total_games']}")
    print(f"  更新預測: {results['updated_games']}")
    print(f"  新建預測: {results['new_predictions']}")
    print(f"  備份預測: {results['backed_up_predictions']}")
    
    if results['predictions']:
        print(f"\n📋 預測詳情:")
        for i, pred in enumerate(results['predictions'][:3]):  # 只顯示前3個
            print(f"  {i+1}. {pred['matchup']}")
            print(f"     動作: {pred['action']}")
            if pred['prediction']:
                print(f"     預測: {pred['prediction']['away_score']:.1f} - {pred['prediction']['home_score']:.1f}")
                print(f"     信心度: {pred['prediction']['confidence']:.3f}")
            print(f"     實際: {pred['actual_score']}")
            print()
    
    # 測試 API 格式轉換
    print(f"\n🔧 測試 API 格式轉換...")
    api_format = []
    for pred in results['predictions'][:2]:
        if pred['action'] not in ['error', 'skipped'] and pred['prediction']:
            formatted_pred = {
                'game_id': pred['game_id'],
                'matchup': pred['matchup'],
                'away_team': pred.get('away_team', 'undefined'),
                'home_team': pred.get('home_team', 'undefined'),
                'predicted_away_score': pred['prediction']['away_score'],
                'predicted_home_score': pred['prediction']['home_score'],
                'predicted_score': f"{pred['prediction']['away_score']:.1f} - {pred['prediction']['home_score']:.1f}",
                'confidence': pred['prediction']['confidence'],
                'model_used': 'test_v1'
            }
            api_format.append(formatted_pred)
    
    print(f"📤 API 格式範例:")
    print(json.dumps(api_format, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    test_enhanced_predict()
