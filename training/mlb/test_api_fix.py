#!/usr/bin/env python3
"""
快速測試API端點修復
"""

import requests
import json
import time

def test_api_endpoints():
    """測試所有批次系統API端點"""
    
    print("🔧 測試API端點修復")
    print("=" * 40)
    
    base_url = "http://localhost:5500"
    
    # 測試1: 批次預測API
    print("1️⃣ 測試批次預測API...")
    try:
        response = requests.post(
            f"{base_url}/batch_system/api/batch/prediction",
            json={
                'startDate': '2025-08-20',
                'endDate': '2025-08-21',
                'engine': 'enhanced',
                'strategy': 'skip_existing',
                'excludeWeekends': False,
                'saveResults': True
            },
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 批次預測API修復成功")
                task_id = data.get('task_id')
                print(f"   任務ID: {task_id}")
                
                # 快速檢查狀態
                time.sleep(1)
                status_response = requests.get(
                    f"{base_url}/batch_system/api/batch/status/{task_id}",
                    timeout=5
                )
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print(f"   狀態: {status_data.get('status')}")
                    print(f"   進度: {status_data.get('progress')}%")
            else:
                print(f"❌ API返回錯誤: {data.get('error')}")
        else:
            print(f"❌ HTTP錯誤: {response.status_code}")
    except Exception as e:
        print(f"❌ 請求失敗: {e}")
    
    # 測試2: 回測API  
    print("\n2️⃣ 測試歷史回測API...")
    try:
        response = requests.post(
            f"{base_url}/batch_system/api/batch/backtest",
            json={
                'startDate': '2025-08-15',
                'endDate': '2025-08-18',
                'metrics': ['accuracy'],
                'scope': 'all'
            },
            timeout=5
        )
        
        if response.status_code == 200 and response.json().get('success'):
            print("✅ 歷史回測API修復成功")
            print(f"   任務ID: {response.json().get('task_id')}")
        else:
            print("❌ 歷史回測API有問題")
    except Exception as e:
        print(f"❌ 請求失敗: {e}")
    
    # 測試3: 算式測試API
    print("\n3️⃣ 測試算式測試API...")
    try:
        response = requests.post(
            f"{base_url}/batch_system/api/batch/algorithm_test",
            json={
                'startDate': '2025-08-15',
                'endDate': '2025-08-18',
                'algorithms': ['enhanced'],
                'testConfidenceThresholds': True,
                'testFeatureWeights': False,
                'testSeasonalAdjustments': False
            },
            timeout=5
        )
        
        if response.status_code == 200 and response.json().get('success'):
            print("✅ 算式測試API修復成功")
            print(f"   任務ID: {response.json().get('task_id')}")
        else:
            print("❌ 算式測試API有問題")
    except Exception as e:
        print(f"❌ 請求失敗: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 API端點修復測試完成")
    print("✅ 批次系統現在可以正常使用了！")
    
if __name__ == "__main__":
    test_api_endpoints()