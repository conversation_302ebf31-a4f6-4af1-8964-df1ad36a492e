#!/usr/bin/env python3
"""
測試管理介面的數據查詢
"""

from app import create_app
from models.database import BettingOdds, db
from sqlalchemy import func, distinct

def test_admin_queries():
    """測試管理介面的查詢邏輯"""
    app = create_app()
    
    with app.app_context():
        print("🔍 測試管理介面查詢邏輯")
        print("=" * 50)
        
        try:
            # 總盤口記錄
            total_odds = BettingOdds.query.count()
            print(f"📊 總盤口記錄: {total_odds}")
            
            # 讓分盤記錄數量
            spreads_count = BettingOdds.query.filter(
                (BettingOdds.market_type == 'spreads') | 
                ((BettingOdds.market_type == 'both') & 
                 ((BettingOdds.home_spread_point.isnot(None)) | (BettingOdds.away_spread_point.isnot(None))))
            ).count()
            print(f"🎯 讓分盤記錄: {spreads_count}")
            
            # 大小分記錄數量
            totals_count = BettingOdds.query.filter(
                (BettingOdds.market_type == 'totals') | 
                ((BettingOdds.market_type == 'both') & (BettingOdds.total_point.isnot(None)))
            ).count()
            print(f"📈 大小分記錄: {totals_count}")
            
            # 日期範圍
            date_range = db.session.query(
                func.min(func.date(BettingOdds.odds_time)).label('earliest_date'),
                func.max(func.date(BettingOdds.odds_time)).label('latest_date')
            ).first()
            
            print(f"📅 最早日期: {date_range.earliest_date}")
            print(f"📅 最晚日期: {date_range.latest_date}")
            
            # 檢查是否有資料表鎖定或其他問題
            sample_records = BettingOdds.query.limit(5).all()
            print(f"\n📋 樣本記錄 ({len(sample_records)} 條):")
            for record in sample_records:
                print(f"   - {record.bookmaker} | {record.market_type} | {record.odds_time}")
            
            # 檢查不同的market_type分佈
            market_type_stats = db.session.query(
                BettingOdds.market_type,
                func.count(BettingOdds.id).label('count')
            ).group_by(BettingOdds.market_type).all()
            
            print(f"\n📊 市場類型分佈:")
            for market_type, count in market_type_stats:
                print(f"   {market_type}: {count} 條")
                
        except Exception as e:
            print(f"❌ 查詢失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_admin_queries()