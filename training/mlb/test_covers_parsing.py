#!/usr/bin/env python3
"""
測試covers.com解析
"""

import requests
from bs4 import BeautifulSoup
import re

def test_covers_parsing():
    """測試covers.com解析"""
    print("=== 測試 Covers.com 解析 ===")
    
    url = "https://www.covers.com/sports/mlb/matchups?selectedDate=2024-07-07"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'identity',
        'Connection': 'keep-alive',
    }
    
    try:
        print(f"請求URL: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"狀態碼: {response.status_code}")
        print(f"內容長度: {len(response.text)}")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找比賽相關的內容
        print("\n=== 查找比賽內容 ===")
        
        # 查找包含 "@" 的文本（表示客隊 @ 主隊）
        at_texts = []
        for text in soup.stripped_strings:
            if '@' in text and len(text.split()) >= 3:
                at_texts.append(text)
                
        print(f"找到 {len(at_texts)} 個包含'@'的文本:")
        for i, text in enumerate(at_texts[:10]):
            print(f"  {i+1}: {text}")
            
        # 查找包含 "Final" 的文本
        final_texts = []
        for text in soup.stripped_strings:
            if 'Final' in text:
                final_texts.append(text)
                
        print(f"\n找到 {len(final_texts)} 個包含'Final'的文本:")
        for i, text in enumerate(final_texts[:10]):
            print(f"  {i+1}: {text}")
            
        # 查找包含球隊名稱的文本
        team_names = ['Yankees', 'Dodgers', 'Red Sox', 'Cubs', 'Angels', 'Phillies', 'Braves']
        team_texts = []
        
        for text in soup.stripped_strings:
            for team in team_names:
                if team.lower() in text.lower():
                    team_texts.append(text)
                    break
                    
        print(f"\n找到 {len(team_texts)} 個包含球隊名稱的文本:")
        for i, text in enumerate(team_texts[:10]):
            print(f"  {i+1}: {text}")
            
        # 查找包含分數的文本
        score_texts = []
        for text in soup.stripped_strings:
            # 查找包含數字的文本，可能是分數
            if re.search(r'\d+', text) and len(text.split()) <= 10:
                score_texts.append(text)
                
        print(f"\n找到 {len(score_texts)} 個可能包含分數的文本:")
        for i, text in enumerate(score_texts[:20]):
            print(f"  {i+1}: {text}")
            
        # 嘗試查找特定的HTML結構
        print("\n=== 查找HTML結構 ===")
        
        # 查找可能包含比賽信息的div
        game_divs = soup.find_all('div', class_=re.compile(r'game|match|score', re.I))
        print(f"找到 {len(game_divs)} 個可能的比賽div")
        
        # 查找表格
        tables = soup.find_all('table')
        print(f"找到 {len(tables)} 個表格")
        
        # 查找包含比賽數據的script標籤
        scripts = soup.find_all('script')
        print(f"找到 {len(scripts)} 個script標籤")
        
        for script in scripts:
            if script.string and ('game' in script.string.lower() or 'match' in script.string.lower()):
                print(f"發現可能包含比賽數據的script: {script.string[:200]}...")
                break
                
    except Exception as e:
        print(f"測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_covers_parsing()
