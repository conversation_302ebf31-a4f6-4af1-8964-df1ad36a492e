#!/usr/bin/env python3
"""
更新特定日期的比賽結果
專門用於更新2025-08-29等需要更新結果的比賽
"""

import sys
sys.path.append('.')

import os
import json
import requests
from datetime import datetime, timedelta
from app import app

def update_games_for_date(target_date):
    """更新指定日期的所有比賽結果"""
    
    print(f"🔄 更新 {target_date} 的比賽結果")
    print("=" * 60)
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    with app.app_context():
        try:
            from models.database import db, Game
            from boxscore_data_collector import BoxscoreDataCollector
            
            # 初始化數據收集器
            collector = BoxscoreDataCollector()
            
            # 查詢該日期的所有比賽
            games = db.session.query(Game).filter(
                Game.date == target_date
            ).all()
            
            print(f"📊 找到 {len(games)} 場比賽需要檢查")
            
            if not games:
                print(f"❌ 未找到 {target_date} 的比賽記錄")
                return False
            
            # 從MLB API獲取該日期的比賽數據
            print(f"\n🌐 從 MLB API 獲取 {target_date} 的比賽數據...")
            
            try:
                # MLB API URL
                api_url = f"https://statsapi.mlb.com/api/v1/schedule?sportId=1&date={target_date}&hydrate=game(content(editorial(preview,recap)),decisions),linescore,person"
                
                print(f"   📡 請求URL: {api_url}")
                response = requests.get(api_url, timeout=30)
                response.raise_for_status()
                
                api_data = response.json()
                
                if not api_data.get('dates'):
                    print(f"   ⚠️ API 未返回 {target_date} 的比賽數據")
                    return False
                
                api_games = api_data['dates'][0].get('games', [])
                print(f"   ✅ API 返回 {len(api_games)} 場比賽")
                
                # 創建API比賽的查找字典
                api_games_dict = {}
                for api_game in api_games:
                    # 構建比賽匹配鍵
                    away_team_data = api_game['teams']['away']['team']
                    home_team_data = api_game['teams']['home']['team']
                    
                    # 使用name作為匹配，因為abbreviation可能為None
                    away_name = away_team_data.get('name', '')
                    home_name = home_team_data.get('name', '')
                    away_id = away_team_data.get('id')
                    home_id = home_team_data.get('id')
                    
                    # 建立隊伍名稱到縮寫的映射
                    team_name_to_abbr = {
                        'St. Louis Cardinals': 'STL',
                        'Cincinnati Reds': 'CIN',
                        'Tampa Bay Rays': 'TB',
                        'Washington Nationals': 'WSH',
                        'Atlanta Braves': 'ATL',
                        'Philadelphia Phillies': 'PHI',
                        'Milwaukee Brewers': 'MIL',
                        'Toronto Blue Jays': 'TOR',
                        'Pittsburgh Pirates': 'PIT',
                        'Boston Red Sox': 'BOS',
                        'Seattle Mariners': 'SEA',
                        'Cleveland Guardians': 'CLE',
                        'Miami Marlins': 'MIA',
                        'New York Mets': 'NYM',
                        'New York Yankees': 'NYY',
                        'Chicago White Sox': 'CWS',
                        'Los Angeles Angels': 'LAA',
                        'Houston Astros': 'HOU',
                        'Detroit Tigers': 'DET',
                        'Kansas City Royals': 'KC',
                        'Washington Nationals': 'WAS',  # 別名
                        'San Diego Padres': 'SD',
                        'Minnesota Twins': 'MIN',
                        'Chicago Cubs': 'CHC',
                        'Colorado Rockies': 'COL',
                        'Texas Rangers': 'TEX',
                        'Oakland Athletics': 'OAK',
                        'Arizona Diamondbacks': 'AZ',
                        'Los Angeles Dodgers': 'LAD',
                        'Baltimore Orioles': 'BAL',
                        'San Francisco Giants': 'SF'
                    }
                    
                    away_abbr = team_name_to_abbr.get(away_name, away_name)
                    home_abbr = team_name_to_abbr.get(home_name, home_name)
                    
                    # 多種匹配方式，包括各種可能的縮寫格式
                    match_keys = [
                        f"{away_abbr}_{home_abbr}",
                        f"{away_abbr}@{home_abbr}",
                        str(api_game['gamePk']),
                        f"{away_id}_{home_id}",
                    ]
                    
                    # 添加別名匹配（處理WSH vs WAS的問題）
                    abbr_aliases = {
                        'WSH': 'WAS',
                        'WAS': 'WSH',
                        'CHW': 'CWS',
                        'CWS': 'CHW',
                        'ATH': 'OAK',  # 如果Oakland用ATH
                        'OAK': 'ATH'
                    }
                    
                    # 添加別名匹配鍵
                    away_alt = abbr_aliases.get(away_abbr, away_abbr)
                    home_alt = abbr_aliases.get(home_abbr, home_abbr)
                    
                    if away_alt != away_abbr or home_alt != home_abbr:
                        match_keys.extend([
                            f"{away_alt}_{home_alt}",
                            f"{away_alt}@{home_alt}",
                        ])
                    
                    for key in match_keys:
                        api_games_dict[key] = api_game
                
                print(f"\n🔍 匹配和更新比賽:")
                updated_count = 0
                failed_count = 0
                
                for i, game in enumerate(games, 1):
                    print(f"\n   {i:2d}. {game.away_team} @ {game.home_team} (ID: {game.game_id})")
                    
                    # 嘗試多種匹配方式
                    api_game = None
                    match_keys = [
                        f"{game.away_team}_{game.home_team}",
                        f"{game.away_team}@{game.home_team}",
                        game.game_id,
                        str(game.game_id)
                    ]
                    
                    for key in match_keys:
                        if key in api_games_dict:
                            api_game = api_games_dict[key]
                            print(f"       ✅ 匹配成功 (使用鍵: {key})")
                            break
                    
                    if not api_game:
                        print(f"       ❌ 無法在API數據中找到匹配的比賽")
                        failed_count += 1
                        continue
                    
                    # 更新比賽信息
                    try:
                        # 獲取比賽狀態
                        game_status = api_game.get('status', {}).get('statusCode', 'S')
                        detailed_state = api_game.get('status', {}).get('detailedState', 'Scheduled')
                        
                        print(f"       📊 API狀態: {detailed_state} ({game_status})")
                        
                        # 根據狀態映射
                        status_mapping = {
                            'F': 'completed',   # Final
                            'O': 'completed',   # Game Over  
                            'I': 'in_progress', # In Progress
                            'S': 'scheduled',   # Scheduled
                            'P': 'postponed',   # Postponed
                            'D': 'postponed',   # Delayed
                            'T': 'postponed',   # Cancelled/Postponed
                            'R': 'postponed',   # Rescheduled
                        }
                        
                        new_status = status_mapping.get(game_status, 'scheduled')
                        
                        # 更新比賽狀態
                        if game.game_status != new_status:
                            game.game_status = new_status
                            print(f"       🔄 狀態更新: {game.game_status} → {new_status}")
                        
                        # 更新比分 (如果比賽已完成)
                        if game_status in ['F', 'O']:
                            away_score = api_game['teams']['away'].get('score')
                            home_score = api_game['teams']['home'].get('score')
                            
                            if away_score is not None and home_score is not None:
                                game.away_score = away_score
                                game.home_score = home_score
                                print(f"       ⚽ 比分更新: {away_score} - {home_score}")
                            
                            # 更新局數信息
                            if 'linescore' in api_game:
                                linescore = api_game['linescore']
                                if 'currentInning' in linescore:
                                    game.inning = linescore.get('currentInning')
                                if 'inningState' in linescore:
                                    game.inning_state = linescore.get('inningState')
                        
                        # 更新場地信息
                        if 'venue' in api_game:
                            venue_name = api_game['venue'].get('name')
                            if venue_name and venue_name != game.venue:
                                game.venue = venue_name
                                print(f"       🏟️ 場地更新: {venue_name}")
                        
                        # 記錄official game ID但不更新數據庫中的game_id（避免重複）
                        api_game_id = str(api_game['gamePk'])
                        if game.game_id != api_game_id:
                            print(f"       🆔 Official ID: {api_game_id} (保持當前ID: {game.game_id})")
                        
                        # 更新時間戳
                        game.updated_at = datetime.utcnow()
                        
                        updated_count += 1
                        print(f"       ✅ 更新成功")
                        
                    except Exception as e:
                        print(f"       ❌ 更新失敗: {e}")
                        failed_count += 1
                
                # 提交所有更改
                try:
                    db.session.commit()
                    print(f"\n💾 數據庫更新完成")
                    print(f"   ✅ 成功更新: {updated_count} 場")
                    print(f"   ❌ 更新失敗: {failed_count} 場")
                    print(f"   📊 總計處理: {len(games)} 場")
                    
                    return updated_count > 0
                    
                except Exception as e:
                    db.session.rollback()
                    print(f"   ❌ 數據庫提交失敗: {e}")
                    return False
                
            except requests.RequestException as e:
                print(f"   ❌ API 請求失敗: {e}")
                return False
            except json.JSONDecodeError as e:
                print(f"   ❌ API 數據解析失敗: {e}")
                return False
                
        except Exception as e:
            print(f"❌ 更新過程失敗: {e}")
            import traceback
            traceback.print_exc()
            return False

def verify_update_results(target_date):
    """驗證更新結果"""
    
    print(f"\n🔍 驗證 {target_date} 更新結果")
    print("-" * 40)
    
    with app.app_context():
        try:
            from models.database import db, Game
            
            games = db.session.query(Game).filter(
                Game.date == target_date
            ).all()
            
            status_counts = {}
            completed_with_scores = 0
            
            for game in games:
                status = game.game_status
                status_counts[status] = status_counts.get(status, 0) + 1
                
                if (status == 'completed' and 
                    game.away_score is not None and 
                    game.home_score is not None):
                    completed_with_scores += 1
            
            print(f"📈 更新後狀態統計:")
            for status, count in status_counts.items():
                print(f"   {status}: {count} 場")
            
            print(f"\n✅ 有完整比分的已完成比賽: {completed_with_scores} 場")
            
            if completed_with_scores > 0:
                print(f"🎉 更新成功！現在有比賽結果了")
                return True
            else:
                print(f"⚠️ 還沒有完成的比賽結果，可能比賽還未開始或數據延遲")
                return False
                
        except Exception as e:
            print(f"❌ 驗證失敗: {e}")
            return False

def main():
    """主函數"""
    
    print("🏟️ MLB 特定日期比賽結果更新工具")
    print("專門用於更新需要結果的比賽")
    print()
    
    # 更新8/29的比賽
    target_date = '2025-08-29'
    
    print(f"🎯 目標日期: {target_date}")
    print(f"📅 今日: {datetime.now().strftime('%Y-%m-%d')}")
    print()
    
    # 執行更新
    success = update_games_for_date(target_date)
    
    if success:
        # 驗證結果
        verify_update_results(target_date)
        
        print(f"\n🚀 完成！您現在可以:")
        print(f"   🌐 刷新網頁查看更新後的比賽結果")
        print(f"   📊 檢查比分是否正確顯示")
        print(f"   🔄 如需要可以再次運行此工具")
    else:
        print(f"\n⚠️ 更新未完全成功")
        print(f"   🔍 請檢查網絡連接和API狀態")
        print(f"   🔄 可以稍後再試")

if __name__ == "__main__":
    main()