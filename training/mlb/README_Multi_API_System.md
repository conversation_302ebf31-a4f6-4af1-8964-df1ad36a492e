# MLB 多 API 數據獲取系統

## 概述

這是一個為 MLB 預測系統設計的可靠多 API 數據獲取解決方案。系統能夠自動在付費 API 和免費 API 之間切換，確保數據獲取的連續性和可靠性。

## 🚀 核心特性

### 1. 智能 API 切換
- **自動故障轉移**: 當付費 API 失敗時自動切換到免費 API
- **優先級管理**: 按優先級順序嘗試不同的 API 端點
- **速率限制管理**: 自動監控和管理 API 使用限制

### 2. 多數據源支持
- **付費 API**: The Odds API, RapidAPI MLB
- **免費 API**: ESPN API, MLB Stats API
- **統一格式**: 所有數據源返回統一的數據格式

### 3. 可靠性保證
- **錯誤處理**: 完善的錯誤處理和恢復機制
- **重試邏輯**: 自動重試失敗的請求
- **狀態監控**: 實時監控各 API 的健康狀況

## 📁 文件結構

```
models/
├── reliable_odds_fetcher.py    # 主要的多 API 管理器
├── free_api_fetcher.py         # 免費 API 專用獲取器
├── multi_api_manager.py        # 原始多 API 管理器（已被整合）
└── enhanced_odds_fetcher.py    # 增強版獲取器（已被整合）

api_key_manager.py              # API 密鑰管理工具
test_reliable_api.py            # 可靠 API 系統測試
test_integrated_api.py          # 整合系統測試
demo_multi_api_integration.py   # 完整演示腳本
```

## 🔧 安裝和配置

### 1. 環境準備
```bash
# 激活虛擬環境
source .venv/bin/activate

# 安裝依賴（如果需要）
pip install requests
```

### 2. API 密鑰配置
```bash
# 運行 API 密鑰管理工具
python api_key_manager.py
```

創建以下文件並添加您的 API 密鑰：
- `models/odds-api.txt` - The Odds API 密鑰
- `models/rapidapi-key.txt` - RapidAPI 密鑰

### 3. 測試系統
```bash
# 測試可靠 API 系統
python test_reliable_api.py

# 測試整合系統
python test_integrated_api.py

# 運行完整演示
python demo_multi_api_integration.py
```

## 💻 使用方法

### 基本使用
```python
from models.reliable_odds_fetcher import ReliableOddsFetcher
from datetime import date

# 創建獲取器
fetcher = ReliableOddsFetcher()

# 獲取今天的數據
result = fetcher.fetch_mlb_odds()

if result.get('success'):
    games = result.get('games', [])
    print(f"找到 {len(games)} 場比賽")
    
    if result.get('is_free_api'):
        print("數據來自免費 API")
    else:
        print(f"數據來自付費 API: {result.get('source')}")
else:
    print(f"獲取失敗: {result.get('error')}")
```

### 獲取歷史數據
```python
from datetime import date, timedelta

# 獲取昨天的數據
yesterday = date.today() - timedelta(days=1)
result = fetcher.fetch_mlb_odds(yesterday)
```

### 系統狀態監控
```python
# 獲取狀態報告
status = fetcher.get_status_report()

print(f"活躍端點: {status['active_endpoints']}/{status['total_endpoints']}")
print(f"可用端點: {status['available_endpoints']}")

# 檢查免費 API 狀態
for api in status.get('free_apis', []):
    print(f"{api['name']}: {api['status']}")
```

## 📊 數據格式

### 統一的比賽數據格式
```python
{
    "success": True,
    "source": "ESPN API",
    "is_free_api": True,
    "games": [
        {
            "id": "game_id",
            "date": "2025-07-05T19:00:00Z",
            "status": "Scheduled",
            "home_team": {
                "name": "Minnesota Twins",
                "abbreviation": "MIN",
                "score": "0",
                "record": "42-46"
            },
            "away_team": {
                "name": "Tampa Bay Rays",
                "abbreviation": "TB",
                "score": "0",
                "record": "48-40"
            },
            "venue": "Target Field",
            "odds": {
                "provider": "ESPN BET",
                "home_odds": -110,
                "away_odds": +105
            },
            "probable_pitchers": {
                "home": {"name": "投手姓名", "era": 3.45},
                "away": {"name": "投手姓名", "era": 2.98}
            }
        }
    ],
    "timestamp": "2025-07-05T23:30:00Z"
}
```

## 🔄 API 切換邏輯

### 優先級順序
1. **The Odds API** (優先級 1) - 提供博彩賠率
2. **RapidAPI MLB** (優先級 2) - 備用付費 API
3. **免費 API 組合** (自動切換) - ESPN API + MLB Stats API

### 切換條件
- API 密鑰無效或過期 (401/403 錯誤)
- 達到速率限制 (429 錯誤)
- 網絡連接問題
- API 服務不可用

## 📈 監控和維護

### 狀態檢查
```python
# 檢查所有 API 狀態
status = fetcher.get_status_report()

# 重置失敗的端點
fetcher.reset_endpoint("The Odds API")

# 動態添加 API 密鑰
fetcher.add_api_key("odds", "new_api_key")
```

### 日誌監控
系統會記錄詳細的日誌信息：
- API 請求成功/失敗
- 自動切換事件
- 速率限制警告
- 錯誤詳情

## 🎯 實際應用場景

### 1. MLB 預測系統整合
```python
# 在預測系統中使用
def get_daily_games_for_prediction():
    fetcher = ReliableOddsFetcher()
    result = fetcher.fetch_mlb_odds()
    
    if result.get('success'):
        # 過濾出待預測的比賽
        scheduled_games = [
            g for g in result.get('games', [])
            if 'Scheduled' in g.get('status', '')
        ]
        return scheduled_games
    return []
```

### 2. 歷史數據收集
```python
# 批量下載歷史數據
def download_historical_data(start_date, end_date):
    fetcher = ReliableOddsFetcher()
    current_date = start_date
    
    while current_date <= end_date:
        result = fetcher.fetch_mlb_odds(current_date)
        if result.get('success'):
            # 保存數據到數據庫
            save_games_to_database(result.get('games', []))
        
        current_date += timedelta(days=1)
```

### 3. 實時監控
```python
# 定期檢查系統健康狀況
def monitor_api_health():
    fetcher = ReliableOddsFetcher()
    status = fetcher.get_status_report()
    
    # 檢查是否有 API 失敗
    failed_apis = [
        e for e in status['endpoints']
        if not e['is_active']
    ]
    
    if failed_apis:
        # 發送告警
        send_alert(f"API 失敗: {[api['name'] for api in failed_apis]}")
```

## 🔧 故障排除

### 常見問題

1. **所有付費 API 都失敗**
   - 檢查 API 密鑰是否有效
   - 確認是否達到使用限制
   - 系統會自動切換到免費 API

2. **免費 API 數據不完整**
   - 免費 API 不提供博彩賠率
   - 某些詳細統計可能缺失
   - 這是正常現象

3. **速率限制問題**
   - 系統會自動管理速率限制
   - 可以添加更多 API 密鑰分散負載
   - 考慮升級到更高級的 API 方案

### 調試技巧
```python
# 啟用詳細日誌
import logging
logging.basicConfig(level=logging.INFO)

# 檢查具體錯誤
result = fetcher.fetch_mlb_odds()
if not result.get('success'):
    print(f"錯誤詳情: {result.get('error')}")
```

## 📝 開發建議

1. **定期更新 API 密鑰**: 設置提醒定期檢查和更新 API 密鑰
2. **監控使用量**: 定期檢查 API 使用統計，避免超出限制
3. **備用方案**: 始終保持多個數據源可用
4. **數據驗證**: 對獲取的數據進行基本驗證和清理
5. **錯誤處理**: 在應用層面添加額外的錯誤處理邏輯

## 🎉 總結

這個多 API 系統為 MLB 預測應用提供了：
- **高可用性**: 多重備用確保數據獲取不中斷
- **成本效益**: 智能切換降低 API 使用成本
- **易於維護**: 統一接口和詳細監控
- **擴展性**: 易於添加新的數據源
- **生產就緒**: 完善的錯誤處理和恢復機制

系統已經過全面測試，可以直接整合到現有的 MLB 預測流程中使用。
