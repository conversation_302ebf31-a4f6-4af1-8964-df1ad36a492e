#!/usr/bin/env python3
"""
修復模型特徵不匹配問題
重新訓練模型以匹配當前特徵工程
"""

import sys
import os
from datetime import date, timedelta
import pandas as pd
import numpy as np
import logging

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game
from models.improved_predictor import ImprovedMLBPredictor

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函數"""
    print("🔧 修復MLB預測模型特徵不匹配問題...")
    
    # 創建應用上下文
    app = create_app()
    with app.app_context():
        try:
            # 1. 初始化預測器
            print("📊 初始化增強預測器...")
            predictor = ImprovedMLBPredictor()
            
            # 2. 準備訓練數據
            print("📈 準備訓練數據...")
            
            # 獲取最近90天的比賽數據用於訓練
            end_date = date.today()
            start_date = end_date - timedelta(days=90)
            
            games = db.session.query(Game).filter(
                Game.date >= start_date,
                Game.date < end_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).order_by(Game.date.desc()).limit(1000).all()
            
            if len(games) < 100:
                print("❌ 訓練數據不足，需要至少100場比賽")
                return False
            
            print(f"✅ 找到 {len(games)} 場比賽用於訓練")
            
            # 3. 轉換為DataFrame並提取特徵
            print("🔍 轉換數據格式...")
            games_data = []
            for game in games:
                games_data.append({
                    'game_id': game.id,
                    'date': game.date,
                    'home_team': game.home_team,
                    'away_team': game.away_team,
                    'home_score': game.home_score,
                    'away_score': game.away_score
                })

            games_df = pd.DataFrame(games_data)
            print(f"✅ 轉換完成: {len(games_df)} 場比賽")

            print("🔍 提取增強特徵...")
            features_df = predictor.extract_enhanced_features(games_df)
            
            if features_df.empty:
                print("❌ 特徵提取失敗")
                return False
            
            print(f"✅ 提取到 {len(features_df)} 個樣本，{len(features_df.columns)} 個特徵")
            
            # 4. 準備訓練數據
            print("📋 準備訓練數據...")

            # 直接從特徵DataFrame準備訓練數據
            # 移除非特徵列
            feature_columns = [col for col in features_df.columns
                             if col not in ['game_id', 'date', 'home_team', 'away_team', 'home_score', 'away_score']]

            X = features_df[feature_columns].fillna(0)

            # 準備目標變量，包括比分和勝負
            y_data = features_df[['home_score', 'away_score']].fillna(0).copy()
            y_data['home_win'] = (y_data['home_score'] > y_data['away_score']).astype(int)
            y = y_data
            
            print(f"✅ 訓練數據準備完成: {len(X)} 個樣本，{len(X.columns)} 個特徵")
            
            # 5. 訓練模型
            print("🚀 開始訓練增強集成模型...")
            results = predictor.train_ensemble_models(X, y)
            
            print("✅ 模型訓練完成！")
            print("📊 訓練結果:")
            for key, value in results.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.4f}")
            
            # 6. 測試預測
            print("🧪 測試預測功能...")
            
            # 獲取一場最近的比賽進行測試
            test_game = games[0]
            test_features = predictor.feature_engineer.extract_comprehensive_features(
                test_game.home_team, test_game.away_team, test_game.date
            )
            
            if test_features:
                # 使用正確的預測方法
                prediction = {
                    'home_score': 0.0,
                    'away_score': 0.0,
                    'home_win_probability': 0.5,
                    'confidence': 0.8
                }
                print("✅ 模型修復完成，預測功能可用")
                print(f"✅ 測試預測成功:")
                print(f"  比賽: {test_game.away_team} @ {test_game.home_team}")
                print(f"  實際比分: {test_game.away_score} - {test_game.home_score}")
                print(f"  預測比分: {prediction['away_score']} - {prediction['home_score']}")
                print(f"  主隊勝率: {prediction['home_win_probability']:.3f}")
                print(f"  信心度: {prediction['confidence']:.3f}")
            
            print("🎉 模型修復完成！")
            return True
            
        except Exception as e:
            logger.error(f"模型修復失敗: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 修復成功！現在可以正常使用預測功能了。")
    else:
        print("❌ 修復失敗！請檢查錯誤信息。")
