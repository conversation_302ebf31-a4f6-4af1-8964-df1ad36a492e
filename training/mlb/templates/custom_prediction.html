{% extends "base.html" %}

{% block title %}自定義預測 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-crystal-ball"></i> 自定義比賽預測
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 預測表單 -->
                        <div class="col-md-6">
                            <form id="predictionForm">
                                <div class="form-group">
                                    <label for="homeTeam">主隊</label>
                                    <select class="form-control" id="homeTeam" required>
                                        <option value="">選擇主隊</option>
                                        <option value="LAA">洛杉磯天使 (LAA)</option>
                                        <option value="OAK">奧克蘭運動家 (OAK)</option>
                                        <option value="NYY">紐約洋基 (NYY)</option>
                                        <option value="BOS">波士頓紅襪 (BOS)</option>
                                        <option value="LAD">洛杉磯道奇 (LAD)</option>
                                        <option value="SF">舊金山巨人 (SF)</option>
                                        <option value="HOU">休士頓太空人 (HOU)</option>
                                        <option value="TEX">德州遊騎兵 (TEX)</option>
                                        <option value="SEA">西雅圖水手 (SEA)</option>
                                        <option value="MIN">明尼蘇達雙城 (MIN)</option>
                                        <option value="CLE">克里夫蘭守護者 (CLE)</option>
                                        <option value="DET">底特律老虎 (DET)</option>
                                        <option value="KC">堪薩斯城皇家 (KC)</option>
                                        <option value="CWS">芝加哥白襪 (CWS)</option>
                                        <option value="TB">坦帕灣光芒 (TB)</option>
                                        <option value="TOR">多倫多藍鳥 (TOR)</option>
                                        <option value="BAL">巴爾的摩金鶯 (BAL)</option>
                                        <option value="ATL">亞特蘭大勇士 (ATL)</option>
                                        <option value="NYM">紐約大都會 (NYM)</option>
                                        <option value="PHI">費城費城人 (PHI)</option>
                                        <option value="WSH">華盛頓國民 (WSH)</option>
                                        <option value="MIA">邁阿密馬林魚 (MIA)</option>
                                        <option value="MIL">密爾瓦基釀酒人 (MIL)</option>
                                        <option value="CHC">芝加哥小熊 (CHC)</option>
                                        <option value="STL">聖路易紅雀 (STL)</option>
                                        <option value="CIN">辛辛那提紅人 (CIN)</option>
                                        <option value="PIT">匹茲堡海盜 (PIT)</option>
                                        <option value="COL">科羅拉多洛磯 (COL)</option>
                                        <option value="AZ">亞利桑那響尾蛇 (AZ)</option>
                                        <option value="SD">聖地牙哥教士 (SD)</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="awayTeam">客隊</label>
                                    <select class="form-control" id="awayTeam" required>
                                        <option value="">選擇客隊</option>
                                        <option value="LAA">洛杉磯天使 (LAA)</option>
                                        <option value="OAK">奧克蘭運動家 (OAK)</option>
                                        <option value="NYY">紐約洋基 (NYY)</option>
                                        <option value="BOS">波士頓紅襪 (BOS)</option>
                                        <option value="LAD">洛杉磯道奇 (LAD)</option>
                                        <option value="SF">舊金山巨人 (SF)</option>
                                        <option value="HOU">休士頓太空人 (HOU)</option>
                                        <option value="TEX">德州遊騎兵 (TEX)</option>
                                        <option value="SEA">西雅圖水手 (SEA)</option>
                                        <option value="MIN">明尼蘇達雙城 (MIN)</option>
                                        <option value="CLE">克里夫蘭守護者 (CLE)</option>
                                        <option value="DET">底特律老虎 (DET)</option>
                                        <option value="KC">堪薩斯城皇家 (KC)</option>
                                        <option value="CWS">芝加哥白襪 (CWS)</option>
                                        <option value="TB">坦帕灣光芒 (TB)</option>
                                        <option value="TOR">多倫多藍鳥 (TOR)</option>
                                        <option value="BAL">巴爾的摩金鶯 (BAL)</option>
                                        <option value="ATL">亞特蘭大勇士 (ATL)</option>
                                        <option value="NYM">紐約大都會 (NYM)</option>
                                        <option value="PHI">費城費城人 (PHI)</option>
                                        <option value="WSH">華盛頓國民 (WSH)</option>
                                        <option value="MIA">邁阿密馬林魚 (MIA)</option>
                                        <option value="MIL">密爾瓦基釀酒人 (MIL)</option>
                                        <option value="CHC">芝加哥小熊 (CHC)</option>
                                        <option value="STL">聖路易紅雀 (STL)</option>
                                        <option value="CIN">辛辛那提紅人 (CIN)</option>
                                        <option value="PIT">匹茲堡海盜 (PIT)</option>
                                        <option value="COL">科羅拉多洛磯 (COL)</option>
                                        <option value="AZ">亞利桑那響尾蛇 (AZ)</option>
                                        <option value="SD">聖地牙哥教士 (SD)</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="gameDate">比賽日期</label>
                                    <input type="date" class="form-control" id="gameDate" required>
                                </div>

                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-magic"></i> 開始預測
                                </button>
                            </form>
                        </div>

                        <!-- 預測結果 -->
                        <div class="col-md-6">
                            <div id="loadingSpinner" class="text-center" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">預測中...</span>
                                </div>
                                <p class="mt-2">正在分析比賽數據...</p>
                            </div>

                            <div id="predictionResult" style="display: none;">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-chart-line"></i> 預測結果
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="resultContent">
                                            <!-- 結果將通過JavaScript填充 -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="errorMessage" class="alert alert-danger" style="display: none;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span id="errorText"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 設置默認日期為今天
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('gameDate').value = today;

    // 表單提交處理
    document.getElementById('predictionForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const homeTeam = document.getElementById('homeTeam').value;
        const awayTeam = document.getElementById('awayTeam').value;
        const gameDate = document.getElementById('gameDate').value;
        
        // 驗證表單
        if (!homeTeam || !awayTeam || !gameDate) {
            showError('請填寫所有必要欄位');
            return;
        }
        
        if (homeTeam === awayTeam) {
            showError('主隊和客隊不能相同');
            return;
        }
        
        // 開始預測
        makePrediction(homeTeam, awayTeam, gameDate);
    });
});

function makePrediction(homeTeam, awayTeam, gameDate) {
    // 顯示載入動畫
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('predictionResult').style.display = 'none';
    document.getElementById('errorMessage').style.display = 'none';
    
    // 調用API
    const params = new URLSearchParams({
        home_team: homeTeam,
        away_team: awayTeam,
        date: gameDate
    });
    
    fetch(`/predictions/api/custom_predict?${params}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('loadingSpinner').style.display = 'none';
            
            if (data.success) {
                showPredictionResult(data.prediction, data.request);
            } else {
                showError(data.error || '預測失敗');
            }
        })
        .catch(error => {
            document.getElementById('loadingSpinner').style.display = 'none';
            showError('網路錯誤：' + error.message);
        });
}

function showPredictionResult(prediction, request) {
    const resultContent = document.getElementById('resultContent');
    
    resultContent.innerHTML = `
        <div class="row">
            <div class="col-12 mb-3">
                <h6 class="text-center">
                    <strong>${request.away_team} @ ${request.home_team}</strong>
                    <br><small class="text-muted">${request.date}</small>
                </h6>
            </div>
        </div>
        
        <div class="row text-center mb-3">
            <div class="col-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h4 class="text-primary">${prediction.predicted_away_score}</h4>
                        <small>${request.away_team} (客隊)</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h4 class="text-success">${prediction.predicted_home_score}</h4>
                        <small>${request.home_team} (主隊)</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-6">
                <strong>勝率預測:</strong><br>
                ${request.home_team}: <span class="text-success">${(prediction.home_win_probability * 100).toFixed(1)}%</span><br>
                ${request.away_team}: <span class="text-primary">${(prediction.away_win_probability * 100).toFixed(1)}%</span>
            </div>
            <div class="col-6">
                <strong>總得分:</strong> ${prediction.total_runs}<br>
                <strong>信心度:</strong> <span class="text-info">${(prediction.confidence * 100).toFixed(1)}%</span><br>
                <strong>模型:</strong> ${prediction.model_type}
            </div>
        </div>
        
        <div class="progress mb-2">
            <div class="progress-bar bg-success" style="width: ${prediction.home_win_probability * 100}%"></div>
            <div class="progress-bar bg-primary" style="width: ${prediction.away_win_probability * 100}%"></div>
        </div>
        <small class="text-muted">勝率分布: 綠色=${request.home_team}, 藍色=${request.away_team}</small>
    `;
    
    document.getElementById('predictionResult').style.display = 'block';
}

function showError(message) {
    document.getElementById('errorText').textContent = message;
    document.getElementById('errorMessage').style.display = 'block';
}
</script>
{% endblock %}
