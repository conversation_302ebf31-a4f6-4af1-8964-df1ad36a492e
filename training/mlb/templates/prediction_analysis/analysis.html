{% extends "base.html" %}

{% block title %}預測效果分析 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line"></i> 預測效果分析</h2>
                <div>
                    <a href="{{ url_for('unified_predictions.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回主頁
                    </a>
                </div>
            </div>

            <!-- 快速統計卡片 -->
            <div class="row mb-4" id="quickStats">
                <div class="col-md-3">
                    <div class="card text-white bg-primary">
                        <div class="card-body text-center">
                            <h4 id="weekGames">-</h4>
                            <p class="mb-0">本週預測</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success">
                        <div class="card-body text-center">
                            <h4 id="weekAccuracy">-</h4>
                            <p class="mb-0">本週準確率</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body text-center">
                            <h4 id="monthGames">-</h4>
                            <p class="mb-0">本月預測</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body text-center">
                            <h4 id="monthAccuracy">-</h4>
                            <p class="mb-0">本月準確率</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析配置 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-cogs"></i> 分析配置</h5>
                </div>
                <div class="card-body">
                    <form id="analysisForm">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="startDate" class="form-label">開始日期</label>
                                <input type="date" class="form-control" id="startDate" 
                                       value="{{ default_start_date }}" required>
                            </div>
                            <div class="col-md-4">
                                <label for="endDate" class="form-label">結束日期</label>
                                <input type="date" class="form-control" id="endDate" 
                                       value="{{ default_end_date }}" required>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary btn-lg w-100" id="analyzeBtn">
                                    <i class="fas fa-chart-bar"></i> 開始分析
                                </button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickRange('week')">過去一週</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickRange('month')">過去一月</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickRange('3months')">過去三月</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 載入指示器 -->
            <div id="loadingArea" class="text-center" style="display: none;">
                <div class="card">
                    <div class="card-body">
                        <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                            <span class="visually-hidden">分析中...</span>
                        </div>
                        <h5>正在分析預測效果...</h5>
                        <p class="text-muted">請稍候，正在計算準確率和效果指標</p>
                    </div>
                </div>
            </div>

            <!-- 分析結果 -->
            <div id="analysisResults" style="display: none;">
                <!-- 整體統計 -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-chart-pie"></i> 整體統計</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="overallStats">
                            <!-- 統計數據將在這裡顯示 -->
                        </div>
                    </div>
                </div>

                <!-- 品質評估 -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-star"></i> 品質評估</h5>
                    </div>
                    <div class="card-body" id="qualityAssessment">
                        <!-- 品質評估將在這裡顯示 -->
                    </div>
                </div>

                <!-- 改進建議 -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-lightbulb"></i> 改進建議</h5>
                    </div>
                    <div class="card-body">
                        <ul id="suggestions" class="list-group list-group-flush">
                            <!-- 建議將在這裡顯示 -->
                        </ul>
                    </div>
                </div>

                <!-- 詳細結果 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> 詳細結果</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>比賽</th>
                                        <th>預測比分</th>
                                        <th>實際比分</th>
                                        <th>勝負</th>
                                        <th>分差</th>
                                        <th>盤口</th>
                                        <th>大小分</th>
                                        <th>信心度</th>
                                    </tr>
                                </thead>
                                <tbody id="detailResults">
                                    <!-- 詳細結果將在這裡顯示 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 錯誤提示 -->
            <div id="errorAlert" class="alert alert-danger" style="display: none;">
                <h5><i class="fas fa-exclamation-triangle"></i> 錯誤</h5>
                <p id="errorMessage"></p>
                <button type="button" class="btn btn-outline-danger" onclick="hideError()">
                    <i class="fas fa-times"></i> 關閉
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 頁面載入時獲取快速統計
document.addEventListener('DOMContentLoaded', function() {
    loadQuickStats();
});

// 載入快速統計
function loadQuickStats() {
    fetch('/prediction_analysis/api/summary')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateQuickStats(data.data);
            }
        })
        .catch(error => {
            console.error('載入快速統計失敗:', error);
        });
}

// 更新快速統計顯示
function updateQuickStats(data) {
    document.getElementById('weekGames').textContent = data.week.total_games + '場';
    document.getElementById('weekAccuracy').textContent = data.week.winner_accuracy.toFixed(1) + '%';
    document.getElementById('monthGames').textContent = data.month.total_games + '場';
    document.getElementById('monthAccuracy').textContent = data.month.winner_accuracy.toFixed(1) + '%';
}

// 快速日期範圍設置
function setQuickRange(range) {
    const today = new Date();
    let startDate, endDate;
    
    endDate = new Date(today.getTime() - 24 * 60 * 60 * 1000); // 昨天
    
    switch(range) {
        case 'week':
            startDate = new Date(endDate.getTime() - 6 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            startDate = new Date(endDate.getTime() - 29 * 24 * 60 * 60 * 1000);
            break;
        case '3months':
            startDate = new Date(endDate.getTime() - 89 * 24 * 60 * 60 * 1000);
            break;
        default:
            return;
    }
    
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
}

// 分析表單提交
document.getElementById('analysisForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (!startDate || !endDate) {
        showError('請選擇開始和結束日期');
        return;
    }
    
    startAnalysis(startDate, endDate);
});

// 開始分析
function startAnalysis(startDate, endDate) {
    hideAllResults();
    showLoading();
    
    document.getElementById('analyzeBtn').disabled = true;
    
    fetch('/prediction_analysis/api/analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        document.getElementById('analyzeBtn').disabled = false;
        
        if (data.success) {
            displayAnalysisResults(data.data);
        } else {
            showError(data.error || '分析失敗');
        }
    })
    .catch(error => {
        hideLoading();
        document.getElementById('analyzeBtn').disabled = false;
        showError('網絡錯誤: ' + error.message);
    });
}

// 顯示載入狀態
function showLoading() {
    document.getElementById('loadingArea').style.display = 'block';
}

// 隱藏載入狀態
function hideLoading() {
    document.getElementById('loadingArea').style.display = 'none';
}

// 顯示分析結果
function displayAnalysisResults(data) {
    if (data.overall_stats.total_games === 0) {
        showError('該日期範圍內沒有找到可分析的預測結果');
        return;
    }
    
    // 顯示整體統計
    displayOverallStats(data.overall_stats);
    
    // 顯示品質評估
    displayQualityAssessment(data.quality_assessment);
    
    // 顯示改進建議
    displaySuggestions(data.suggestions);
    
    // 顯示詳細結果
    displayDetailResults(data.games);
    
    document.getElementById('analysisResults').style.display = 'block';
}

// 顯示整體統計
function displayOverallStats(stats) {
    const container = document.getElementById('overallStats');
    container.innerHTML = `
        <div class="col-md-2">
            <div class="text-center">
                <h4 class="text-primary">${stats.total_games}</h4>
                <p class="mb-0">總預測場數</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h4 class="text-${stats.winner_accuracy >= 55 ? 'success' : stats.winner_accuracy >= 50 ? 'warning' : 'danger'}">${stats.winner_accuracy}%</h4>
                <p class="mb-0">勝負準確率</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h4 class="text-${stats.over_under_accuracy >= 55 ? 'success' : stats.over_under_accuracy >= 50 ? 'warning' : 'danger'}">${stats.over_under_accuracy}%</h4>
                <p class="mb-0">大小分準確率</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h4 class="text-${stats.avg_score_diff <= 2.0 ? 'success' : stats.avg_score_diff <= 3.0 ? 'warning' : 'danger'}">${stats.avg_score_diff}</h4>
                <p class="mb-0">平均分差</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h4 class="text-${stats.avg_total_diff <= 3.0 ? 'success' : stats.avg_total_diff <= 5.0 ? 'warning' : 'danger'}">${stats.avg_total_diff}</h4>
                <p class="mb-0">平均總分差</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h4 class="text-info">${stats.avg_confidence}%</h4>
                <p class="mb-0">平均信心度</p>
            </div>
        </div>
    `;
}

// 顯示品質評估
function displayQualityAssessment(assessments) {
    const container = document.getElementById('qualityAssessment');
    let html = '<div class="row">';
    
    assessments.forEach(assessment => {
        const badgeClass = {
            'excellent': 'bg-success',
            'good': 'bg-primary',
            'fair': 'bg-warning',
            'poor': 'bg-danger'
        }[assessment.status] || 'bg-secondary';
        
        html += `
            <div class="col-md-4 mb-2">
                <span class="badge ${badgeClass} w-100 p-2">
                    <strong>${assessment.category}:</strong> ${assessment.message}
                </span>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// 顯示改進建議
function displaySuggestions(suggestions) {
    const container = document.getElementById('suggestions');
    if (suggestions.length === 0) {
        container.innerHTML = '<li class="list-group-item text-success">目前表現良好，暫無改進建議</li>';
        return;
    }
    
    container.innerHTML = suggestions.map(suggestion => 
        `<li class="list-group-item"><i class="fas fa-arrow-right text-primary"></i> ${suggestion}</li>`
    ).join('');
}

// 顯示詳細結果
function displayDetailResults(games) {
    const tbody = document.getElementById('detailResults');
    tbody.innerHTML = games.map(game => {
        const winnerIcon = game.winner_correct ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="fas fa-times-circle text-danger"></i>';
        const overUnderIcon = game.over_under_correct ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="fas fa-times-circle text-danger"></i>';
        
        return `
            <tr>
                <td>${game.date}</td>
                <td>${game.matchup}</td>
                <td>${game.predicted_score}</td>
                <td>${game.actual_score}</td>
                <td>${winnerIcon}</td>
                <td>${game.score_diff.toFixed(2)}</td>
                <td>${game.spread_info || 'N/A'}</td>
                <td>${overUnderIcon}</td>
                <td>${game.confidence.toFixed(0)}%</td>
            </tr>
        `;
    }).join('');
}

// 隱藏所有結果
function hideAllResults() {
    document.getElementById('analysisResults').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
}

// 顯示錯誤
function showError(message) {
    hideAllResults();
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorAlert').style.display = 'block';
}

// 隱藏錯誤
function hideError() {
    document.getElementById('errorAlert').style.display = 'none';
}
</script>
{% endblock %}