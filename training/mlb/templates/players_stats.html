{% extends "base.html" %}

{% block title %}球員統計排行榜{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line"></i> 球員統計排行榜</h2>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('players.players_list') }}" class="btn btn-outline-primary">所有球員</a>
                    <a href="{{ url_for('players.players_stats') }}" class="btn btn-primary">統計排行</a>
                </div>
            </div>

            <!-- 篩選器 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="stat" class="form-label">統計類型</label>
                            <select class="form-select" id="stat" name="stat">
                                <option value="batting_avg" {% if stat_type == 'batting_avg' %}selected{% endif %}>打擊率</option>
                                <option value="home_runs" {% if stat_type == 'home_runs' %}selected{% endif %}>全壘打</option>
                                <option value="rbi" {% if stat_type == 'rbi' %}selected{% endif %}>打點</option>
                                <option value="era" {% if stat_type == 'era' %}selected{% endif %}>ERA</option>
                                <option value="strikeouts" {% if stat_type == 'strikeouts' %}selected{% endif %}>三振</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="season" class="form-label">賽季</label>
                            <select class="form-select" id="season" name="season">
                                {% for year in range(2025, 2019, -1) %}
                                <option value="{{ year }}" {% if season == year %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="position" class="form-label">位置</label>
                            <select class="form-select" id="position" name="position">
                                <option value="">所有位置</option>
                                {% for pos in positions %}
                                <option value="{{ pos }}" {% if position == pos %}selected{% endif %}>{{ pos }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">查看排行</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 統計排行榜 -->
            {% if stats %}
            <div class="card">
                <div class="card-header">
                    <h5>
                        {{ season }} 賽季 
                        {% if stat_type == 'batting_avg' %}打擊率
                        {% elif stat_type == 'home_runs' %}全壘打
                        {% elif stat_type == 'rbi' %}打點
                        {% elif stat_type == 'era' %}ERA
                        {% elif stat_type == 'strikeouts' %}三振
                        {% endif %}
                        排行榜
                        {% if position %} - {{ position }}{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>排名</th>
                                    <th>球員</th>
                                    <th>球隊</th>
                                    <th>位置</th>
                                    {% if stat_type in ['batting_avg', 'home_runs', 'rbi'] %}
                                    <th>比賽</th>
                                    <th>打席</th>
                                    <th>打擊率</th>
                                    <th>全壘打</th>
                                    <th>打點</th>
                                    <th>OPS</th>
                                    {% elif stat_type in ['era', 'strikeouts'] %}
                                    <th>比賽</th>
                                    <th>先發</th>
                                    <th>ERA</th>
                                    <th>勝-敗</th>
                                    <th>三振</th>
                                    <th>WHIP</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for player_stats, player in stats %}
                                <tr>
                                    <td>
                                        <span class="badge 
                                            {% if loop.index <= 3 %}bg-warning
                                            {% elif loop.index <= 10 %}bg-info
                                            {% else %}bg-secondary{% endif %}">
                                            {{ loop.index }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('players.player_detail', player_id=player.player_id) }}" 
                                           class="text-decoration-none fw-bold">
                                            {{ player.full_name }}
                                        </a>
                                        {% if player.jersey_number %}
                                            <small class="text-muted">#{{ player.jersey_number }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ player_stats.team_abbreviation or 'N/A' }}</td>
                                    <td>{{ player.primary_position or 'N/A' }}</td>
                                    
                                    {% if stat_type in ['batting_avg', 'home_runs', 'rbi'] %}
                                    <td>{{ player_stats.games_played }}</td>
                                    <td>{{ player_stats.at_bats }}</td>
                                    <td>
                                        <span class="fw-bold 
                                            {% if stat_type == 'batting_avg' and loop.index <= 3 %}text-warning
                                            {% elif stat_type == 'batting_avg' and loop.index <= 10 %}text-info
                                            {% endif %}">
                                            {{ "%.3f"|format(player_stats.batting_avg) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-bold 
                                            {% if stat_type == 'home_runs' and loop.index <= 3 %}text-warning
                                            {% elif stat_type == 'home_runs' and loop.index <= 10 %}text-info
                                            {% endif %}">
                                            {{ player_stats.home_runs }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-bold 
                                            {% if stat_type == 'rbi' and loop.index <= 3 %}text-warning
                                            {% elif stat_type == 'rbi' and loop.index <= 10 %}text-info
                                            {% endif %}">
                                            {{ player_stats.rbi }}
                                        </span>
                                    </td>
                                    <td>{{ "%.3f"|format(player_stats.ops) }}</td>
                                    
                                    {% elif stat_type in ['era', 'strikeouts'] %}
                                    <td>{{ player_stats.games_played }}</td>
                                    <td>{{ player_stats.games_started }}</td>
                                    <td>
                                        <span class="fw-bold 
                                            {% if stat_type == 'era' and loop.index <= 3 %}text-success
                                            {% elif stat_type == 'era' and loop.index <= 10 %}text-info
                                            {% endif %}">
                                            {{ "%.2f"|format(player_stats.era) }}
                                        </span>
                                    </td>
                                    <td>{{ player_stats.wins }}-{{ player_stats.losses }}</td>
                                    <td>
                                        <span class="fw-bold 
                                            {% if stat_type == 'strikeouts' and loop.index <= 3 %}text-warning
                                            {% elif stat_type == 'strikeouts' and loop.index <= 10 %}text-info
                                            {% endif %}">
                                            {{ player_stats.strikeouts }}
                                        </span>
                                    </td>
                                    <td>{{ "%.2f"|format(player_stats.whip) }}</td>
                                    {% endif %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 統計說明 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6>統計說明:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <li><strong>打擊率 (AVG):</strong> 安打數 ÷ 打數</li>
                                <li><strong>全壘打 (HR):</strong> 全壘打總數</li>
                                <li><strong>打點 (RBI):</strong> 打點總數</li>
                                <li><strong>OPS:</strong> 上壘率 + 長打率</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <li><strong>ERA:</strong> 自責分率 (9局平均自責分)</li>
                                <li><strong>三振 (K):</strong> 三振總數</li>
                                <li><strong>WHIP:</strong> (被安打 + 保送) ÷ 投球局數</li>
                                <li><strong>勝-敗:</strong> 勝投數-敗投數</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            * 打擊率排行需至少100個打數 | ERA排行需至少50局投球
                        </small>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">沒有找到統計數據</h4>
                <p class="text-muted">請嘗試調整篩選條件</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 自動提交表單當選擇改變時
document.addEventListener('DOMContentLoaded', function() {
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
{% endblock %}
