{% extends "base.html" %}

{% block title %}{{ player.full_name }} - 球員詳情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 球員標題 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-user-baseball"></i> 
                        {{ player.full_name }}
                        {% if player.jersey_number %}
                            <span class="badge bg-primary">#{{ player.jersey_number }}</span>
                        {% endif %}
                    </h2>
                    <div class="text-muted">
                        {% if player.primary_position %}{{ player.primary_position }}{% endif %}
                        {% if team %} - {{ team.team_name }} ({{ team.team_code }}){% endif %}
                    </div>
                </div>
                <div>
                    {% if player.active %}
                        <span class="badge bg-success fs-6">現役球員</span>
                    {% else %}
                        <span class="badge bg-secondary fs-6">退役球員</span>
                    {% endif %}
                    <a href="{{ url_for('players.players_list') }}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- 基本信息 -->
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-id-card"></i> 基本信息</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>全名:</strong></td>
                                    <td>{{ player.full_name }}</td>
                                </tr>
                                {% if player.birth_date %}
                                <tr>
                                    <td><strong>出生日期:</strong></td>
                                    <td>
                                        {{ player.birth_date.strftime('%Y-%m-%d') }}
                                        ({{ ((datetime.now().date() - player.birth_date).days / 365.25)|int }} 歲)
                                    </td>
                                </tr>
                                {% endif %}
                                {% if player.birth_city or player.birth_country %}
                                <tr>
                                    <td><strong>出生地:</strong></td>
                                    <td>{{ player.birth_city }}{% if player.birth_city and player.birth_country %}, {% endif %}{{ player.birth_country }}</td>
                                </tr>
                                {% endif %}
                                {% if player.height %}
                                <tr>
                                    <td><strong>身高:</strong></td>
                                    <td>{{ player.height }}</td>
                                </tr>
                                {% endif %}
                                {% if player.weight %}
                                <tr>
                                    <td><strong>體重:</strong></td>
                                    <td>{{ player.weight }}</td>
                                </tr>
                                {% endif %}
                                {% if player.bats %}
                                <tr>
                                    <td><strong>打擊:</strong></td>
                                    <td>{{ player.bats }}</td>
                                </tr>
                                {% endif %}
                                {% if player.throws %}
                                <tr>
                                    <td><strong>投球:</strong></td>
                                    <td>{{ player.throws }}</td>
                                </tr>
                                {% endif %}
                                {% if player.mlb_debut %}
                                <tr>
                                    <td><strong>MLB首秀:</strong></td>
                                    <td>{{ player.mlb_debut.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <!-- 生涯統計摘要 -->
                    {% if career_stats %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-trophy"></i> 生涯統計</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ career_stats.seasons }}</div>
                                        <div class="stat-label">賽季</div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ career_stats.total_games }}</div>
                                        <div class="stat-label">比賽</div>
                                    </div>
                                </div>
                                {% if career_stats.total_at_bats > 0 %}
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ "%.3f"|format(career_stats.career_batting_avg) }}</div>
                                        <div class="stat-label">打擊率</div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ career_stats.total_home_runs }}</div>
                                        <div class="stat-label">全壘打</div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ career_stats.total_rbi }}</div>
                                        <div class="stat-label">打點</div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ career_stats.total_hits }}</div>
                                        <div class="stat-label">安打</div>
                                    </div>
                                </div>
                                {% endif %}
                                {% if career_stats.total_innings_pitched > 0 %}
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ career_stats.total_wins }}</div>
                                        <div class="stat-label">勝投</div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ career_stats.total_losses }}</div>
                                        <div class="stat-label">敗投</div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ "%.1f"|format(career_stats.total_innings_pitched) }}</div>
                                        <div class="stat-label">投球局數</div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ career_stats.total_strikeouts }}</div>
                                        <div class="stat-label">三振</div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- 當前賽季統計 -->
                <div class="col-lg-8">
                    {% if current_stats %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar"></i> {{ current_stats.season }} 賽季統計</h5>
                        </div>
                        <div class="card-body">
                            <!-- 打擊統計 -->
                            {% if current_stats.at_bats > 0 %}
                            <h6 class="mb-3">打擊統計</h6>
                            <div class="row mb-4">
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ "%.3f"|format(current_stats.batting_avg) }}</div>
                                        <div class="stat-label">打擊率</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.home_runs }}</div>
                                        <div class="stat-label">全壘打</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.rbi }}</div>
                                        <div class="stat-label">打點</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.hits }}</div>
                                        <div class="stat-label">安打</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.runs }}</div>
                                        <div class="stat-label">得分</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.stolen_bases }}</div>
                                        <div class="stat-label">盜壘</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ "%.3f"|format(current_stats.on_base_percentage) }}</div>
                                        <div class="stat-label">上壘率</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ "%.3f"|format(current_stats.slugging_percentage) }}</div>
                                        <div class="stat-label">長打率</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ "%.3f"|format(current_stats.ops) }}</div>
                                        <div class="stat-label">OPS</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.doubles }}</div>
                                        <div class="stat-label">二壘打</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.triples }}</div>
                                        <div class="stat-label">三壘打</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.walks }}</div>
                                        <div class="stat-label">保送</div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            <!-- 投球統計 -->
                            {% if current_stats.innings_pitched > 0 %}
                            <h6 class="mb-3">投球統計</h6>
                            <div class="row mb-4">
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ "%.2f"|format(current_stats.era) }}</div>
                                        <div class="stat-label">ERA</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.wins }}</div>
                                        <div class="stat-label">勝投</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.losses }}</div>
                                        <div class="stat-label">敗投</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.saves }}</div>
                                        <div class="stat-label">救援</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ "%.1f"|format(current_stats.innings_pitched) }}</div>
                                        <div class="stat-label">投球局數</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.strikeouts }}</div>
                                        <div class="stat-label">三振</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ "%.2f"|format(current_stats.whip) }}</div>
                                        <div class="stat-label">WHIP</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.hits_allowed }}</div>
                                        <div class="stat-label">被安打</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.walks_allowed }}</div>
                                        <div class="stat-label">保送</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.home_runs_allowed }}</div>
                                        <div class="stat-label">被全壘打</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.games_started }}</div>
                                        <div class="stat-label">先發</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-value">{{ current_stats.complete_games }}</div>
                                        <div class="stat-label">完投</div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- 歷史統計 -->
                    {% if historical_stats %}
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-history"></i> 歷史統計</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>賽季</th>
                                            <th>球隊</th>
                                            <th>比賽</th>
                                            {% if historical_stats[0].at_bats > 0 %}
                                            <th>打擊率</th>
                                            <th>全壘打</th>
                                            <th>打點</th>
                                            <th>OPS</th>
                                            {% endif %}
                                            {% if historical_stats[0].innings_pitched > 0 %}
                                            <th>ERA</th>
                                            <th>勝-敗</th>
                                            <th>三振</th>
                                            <th>WHIP</th>
                                            {% endif %}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for stats in historical_stats %}
                                        <tr>
                                            <td>{{ stats.season }}</td>
                                            <td>{{ stats.team_abbreviation or 'N/A' }}</td>
                                            <td>{{ stats.games_played }}</td>
                                            {% if stats.at_bats > 0 %}
                                            <td>{{ "%.3f"|format(stats.batting_avg) }}</td>
                                            <td>{{ stats.home_runs }}</td>
                                            <td>{{ stats.rbi }}</td>
                                            <td>{{ "%.3f"|format(stats.ops) }}</td>
                                            {% endif %}
                                            {% if stats.innings_pitched > 0 %}
                                            <td>{{ "%.2f"|format(stats.era) }}</td>
                                            <td>{{ stats.wins }}-{{ stats.losses }}</td>
                                            <td>{{ stats.strikeouts }}</td>
                                            <td>{{ "%.2f"|format(stats.whip) }}</td>
                                            {% endif %}
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.stat-item {
    text-align: center;
    padding: 10px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

.stat-card {
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stat-card .stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #495057;
}

.stat-card .stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 5px;
}
</style>
{% endblock %}
