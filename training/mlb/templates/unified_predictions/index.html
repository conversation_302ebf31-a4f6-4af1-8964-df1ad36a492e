{% extends "base.html" %}

{% block title %}統一目標性博彩預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-chart-line"></i> 統一目標性博彩預測系統
                </h1>
                <div class="btn-group" role="group">
                    {% if not data_loaded %}
                    <a href="{{ url_for('unified_predictions.index', load_data='true') }}" class="btn btn-primary">
                        <i class="fas fa-download"></i> 載入數據
                    </a>
                    {% endif %}
                    <button type="button" class="btn btn-success" onclick="generateTodayPredictions()">
                        <i class="fas fa-magic"></i> 生成今日預測
                    </button>
                    <a href="{{ url_for('unified_predictions.custom_predict_page') }}" class="btn btn-primary">
                        <i class="fas fa-calendar-alt"></i> 自定義日期預測
                    </a>
                    <a href="{{ url_for('unified_predictions.custom_game_predict_page') }}" class="btn btn-info">
                        <i class="fas fa-magic"></i> 自定義單場預測
                    </a>
                    <a href="{{ url_for('unified_predictions.query_page') }}" class="btn btn-warning">
                        <i class="fas fa-search"></i> 查詢結果
                    </a>
                    <a href="{{ url_for('unified_predictions.history_page') }}" class="btn btn-info">
                        <i class="fas fa-history"></i> 歷史記錄
                    </a>
                    <a href="{{ url_for('unified_predictions.analytics_page') }}" class="btn btn-success">
                        <i class="fas fa-chart-bar"></i> 性能分析
                    </a>
                    <a href="{{ url_for('admin.prediction_methodology') }}" class="btn btn-secondary">
                        <i class="fas fa-book"></i> 預測方法論
                    </a>
                </div>
            </div>

            {% if error_message %}
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle"></i> {{ error_message }}
            </div>
            {% endif %}

            <!-- 系統狀態卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        今日預測
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {% if daily_summary and daily_summary.success %}
                                            {{ daily_summary.successful_predictions }}/{{ daily_summary.total_games }}
                                        {% else %}
                                            0/0
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        成功率
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {% if daily_summary and daily_summary.success %}
                                            {{ daily_summary.success_rate }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-percentage fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        歷史準確率
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {% if analytics and analytics.success and analytics.analytics and analytics.analytics.overall_performance %}
                                            {{ analytics.analytics.overall_performance.accuracy_rate }}%
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        總預測數
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {% if analytics and analytics.success and analytics.analytics and analytics.analytics.overall_performance %}
                                            {{ analytics.analytics.overall_performance.total_predictions }}
                                        {% else %}
                                            0
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-list-ol fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作區 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-primary btn-block" onclick="generateTodayPredictions()">
                                        <i class="fas fa-play"></i> 生成今日預測
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-info btn-block" onclick="updateData()">
                                        <i class="fas fa-sync"></i> 更新數據
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-success btn-block" onclick="updateResults()">
                                        <i class="fas fa-check"></i> 更新結果
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-warning btn-block" onclick="trainModels()">
                                        <i class="fas fa-cogs"></i> 訓練模型
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 今日預測結果 -->
            {% if daily_summary and daily_summary.success and daily_summary.predictions %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">今日預測結果</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>比賽</th>
                                            <th>預測比分</th>
                                            <th>大小分</th>
                                            <th>讓分盤</th>
                                            <th>質量等級</th>
                                            <th>信心度</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for prediction in daily_summary.predictions %}
                                        {% if prediction.success %}
                                        <tr>
                                            <td>
                                                <strong>{{ prediction.game_info.away_team }}</strong> @ 
                                                <strong>{{ prediction.game_info.home_team }}</strong>
                                            </td>
                                            <td>
                                                {% if prediction.predictions.score and 'error' not in prediction.predictions.score %}
                                                    {{ "%.1f"|format(prediction.predictions.score.away_score) }} - 
                                                    {{ "%.1f"|format(prediction.predictions.score.home_score) }}
                                                {% else %}
                                                    <span class="text-muted">預測失敗</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if prediction.over_under_analysis %}
                                                    {% set analysis = prediction.over_under_analysis %}
                                                    <div class="small">
                                                        <!-- 盘口信息 -->
                                                        <div class="mb-1">
                                                            <strong>線:</strong>
                                                            <span class="badge badge-secondary">{{ analysis.get('line', 'N/A') }}</span>
                                                            {% if analysis.get('is_real_line', False) %}
                                                                <span class="badge badge-success" title="{{ analysis.get('data_source_text', '') }}">真實</span>
                                                            {% else %}
                                                                <span class="badge badge-warning" title="{{ analysis.get('data_source_text', '') }}">模擬</span>
                                                            {% endif %}
                                                        </div>

                                                        <!-- 数据来源 -->
                                                        <div class="mb-1">
                                                            <small class="text-muted">{{ analysis.get('data_source_text', '未知來源') }}</small>
                                                        </div>

                                                        <!-- 预测总分 -->
                                                        <div class="mb-1">
                                                            <strong>預測總分:</strong>
                                                            <span class="badge badge-info">{{ analysis.get('predicted_total', 'N/A')|round(1) }}</span>
                                                        </div>

                                                        <!-- 推荐 -->
                                                        <div class="mb-1">
                                                            {% if analysis.get('recommendation') == 'over' %}
                                                                <span class="badge badge-danger">{{ analysis.get('recommendation_text', '大分') }}</span>
                                                            {% else %}
                                                                <span class="badge badge-primary">{{ analysis.get('recommendation_text', '小分') }}</span>
                                                            {% endif %}
                                                        </div>

                                                        <!-- 信心度 -->
                                                        <div>
                                                            <strong>信心度:</strong>
                                                            <span class="badge badge-dark">{{ (analysis.get('confidence', 0) * 100)|round(1) }}%</span>
                                                        </div>
                                                    </div>
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if prediction.predictions.run_line and 'error' not in prediction.predictions.run_line %}
                                                    <span class="badge badge-secondary">
                                                        {{ prediction.predictions.run_line.get('建議', 'N/A') }}
                                                    </span>
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% set quality = prediction.quality_assessment %}
                                                {% if quality.grade == 'A' %}
                                                    <span class="badge badge-success">{{ quality.grade }} - {{ quality.description }}</span>
                                                {% elif quality.grade == 'B' %}
                                                    <span class="badge badge-info">{{ quality.grade }} - {{ quality.description }}</span>
                                                {% elif quality.grade == 'C' %}
                                                    <span class="badge badge-warning">{{ quality.grade }} - {{ quality.description }}</span>
                                                {% else %}
                                                    <span class="badge badge-danger">{{ quality.grade }} - {{ quality.description }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if prediction.predictions.score and 'error' not in prediction.predictions.score %}
                                                    {{ "%.1f"|format(prediction.predictions.score.confidence * 100) }}%
                                                {% else %}
                                                    N/A
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 最近預測歷史 -->
            {% if history and history.success and history.predictions %}
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">最近預測歷史 (7天)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>比賽ID</th>
                                            <th>預測比分</th>
                                            <th>實際比分</th>
                                            <th>準確性</th>
                                            <th>信心度</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for pred in history.predictions[:10] %}
                                        <tr>
                                            <td>{{ pred.prediction_date[:10] }}</td>
                                            <td>{{ pred.game_id }}</td>
                                            <td>
                                                {{ "%.1f"|format(pred.predicted_away_score) }} - 
                                                {{ "%.1f"|format(pred.predicted_home_score) }}
                                            </td>
                                            <td>
                                                {% if pred.actual_away_score is not none and pred.actual_home_score is not none %}
                                                    {{ pred.actual_away_score }} - {{ pred.actual_home_score }}
                                                {% else %}
                                                    <span class="text-muted">待更新</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if pred.is_correct is not none %}
                                                    {% if pred.is_correct %}
                                                        <span class="badge badge-success">正確</span>
                                                    {% else %}
                                                        <span class="badge badge-danger">錯誤</span>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge badge-secondary">待驗證</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ "%.1f"|format(pred.confidence * 100) }}%</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="{{ url_for('unified_predictions.history_page') }}" class="btn btn-primary">
                                    查看完整歷史
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 操作結果模態框 -->
<div class="modal fade" id="operationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="operationModalTitle">操作結果</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="operationModalBody">
                <!-- 操作結果內容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">關閉</button>
            </div>
        </div>
    </div>
</div>

<script>
function showOperationResult(title, message, isSuccess = true) {
    document.getElementById('operationModalTitle').textContent = title;
    document.getElementById('operationModalBody').innerHTML = 
        `<div class="alert alert-${isSuccess ? 'success' : 'danger'}" role="alert">
            <i class="fas fa-${isSuccess ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}
        </div>`;
    $('#operationModal').modal('show');
}

function generateTodayPredictions() {
    const today = new Date().toISOString().split('T')[0];
    
    fetch(`{{ url_for('unified_predictions.api_predict_daily') }}?date=${today}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOperationResult('預測生成成功', 
                    `成功生成 ${data.successful_predictions}/${data.total_games} 場比賽的預測`);
                setTimeout(() => location.reload(), 2000);
            } else {
                showOperationResult('預測生成失敗', data.error, false);
            }
        })
        .catch(error => {
            showOperationResult('預測生成失敗', '網絡錯誤: ' + error.message, false);
        });
}

function updateData() {
    const today = new Date().toISOString().split('T')[0];
    
    fetch(`{{ url_for('unified_predictions.api_update_data') }}?date=${today}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOperationResult('數據更新成功', '所有數據已更新完成');
            } else {
                showOperationResult('數據更新失敗', data.error, false);
            }
        })
        .catch(error => {
            showOperationResult('數據更新失敗', '網絡錯誤: ' + error.message, false);
        });
}

function updateResults() {
    fetch(`{{ url_for('unified_predictions.api_update_results') }}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOperationResult('結果更新成功', 
                    `已更新 ${data.updated_count} 個預測結果`);
                setTimeout(() => location.reload(), 2000);
            } else {
                showOperationResult('結果更新失敗', data.error, false);
            }
        })
        .catch(error => {
            showOperationResult('結果更新失敗', '網絡錯誤: ' + error.message, false);
        });
}

function generateTodayPredictions() {
    // 顯示載入狀態
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    button.disabled = true;

    const today = new Date().toISOString().split('T')[0];

    fetch(`{{ url_for('unified_predictions.api_predict_daily') }}?date=${today}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOperationResult('預測生成成功', `成功生成 ${data.successful_predictions || 0} 個預測`);
                // 重新載入頁面顯示結果
                setTimeout(() => {
                    window.location.href = `{{ url_for('unified_predictions.index', load_data='true') }}`;
                }, 2000);
            } else {
                showOperationResult('預測生成失敗', data.error || '未知錯誤', false);
            }
        })
        .catch(error => {
            showOperationResult('預測生成失敗', '網絡錯誤: ' + error.message, false);
        })
        .finally(() => {
            // 恢復按鈕狀態
            button.innerHTML = originalText;
            button.disabled = false;
        });
}

function trainModels() {
    fetch(`{{ url_for('unified_predictions.api_train_models') }}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOperationResult('模型訓練成功', data.message);
            } else {
                showOperationResult('模型訓練失敗', data.error, false);
            }
        })
        .catch(error => {
            showOperationResult('模型訓練失敗', '網絡錯誤: ' + error.message, false);
        });
}
</script>
{% endblock %}
