{% extends "base.html" %}

{% block title %}查詢預測結果 - MLB統一預測系統{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-search"></i> 查詢預測結果</h4>
                </div>
                <div class="card-body">
                    <!-- 查詢表單 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="date" class="form-label">選擇日期:</label>
                                <input type="date" 
                                       class="form-control" 
                                       id="date" 
                                       name="date" 
                                       value="{{ query_date or '' }}"
                                       max="{{ (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d') }}">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> 查詢
                                </button>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <div class="btn-group" role="group">
                                    <a href="?date={{ (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d') }}" 
                                       class="btn btn-outline-secondary">昨天</a>
                                    <a href="?date={{ (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d') }}" 
                                       class="btn btn-outline-secondary">前天</a>
                                    <a href="?date={{ (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d') }}" 
                                       class="btn btn-outline-secondary">一週前</a>
                                </div>
                            </div>
                        </div>
                    </form>

                    {% if error_message %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> {{ error_message }}
                        </div>
                    {% endif %}

                    {% if predictions_data %}
                        <!-- 統計摘要 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5>{{ predictions_data.date }}</h5>
                                        <p class="mb-0">查詢日期</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5>{{ predictions_data.total_predictions }}</h5>
                                        <p class="mb-0">總預測數</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5>{{ predictions_data.correct_predictions }}</h5>
                                        <p class="mb-0">正確預測</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h5>{{ "%.1f"|format(predictions_data.accuracy_rate) }}%</h5>
                                        <p class="mb-0">準確率</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 預測詳細結果 -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>比賽</th>
                                        <th>預測得分</th>
                                        <th>實際得分</th>
                                        <th>勝負預測</th>
                                        <th>大小分</th>
                                        <th>讓分盤</th>
                                        <th>信心度</th>
                                        <th>品質分數</th>
                                        <th>預測時間</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for pred in predictions_data.predictions %}
                                    <tr>
                                        <td>
                                            <strong>{{ pred.away_team }} @ {{ pred.home_team }}</strong><br>
                                            <small class="text-muted">{{ pred.game_id }}</small>
                                        </td>
                                        <td>
                                            {{ pred.away_team }}: {{ pred.predicted_away_score or 0 }}<br>
                                            {{ pred.home_team }}: {{ pred.predicted_home_score or 0 }}<br>
                                            <small class="text-muted">總分: {{ pred.predicted_total_runs or 0 }}</small>
                                        </td>
                                        <td>
                                            {% if pred.actual_away_score is not none and pred.actual_home_score is not none %}
                                                {{ pred.away_team }}: {{ pred.actual_away_score }}<br>
                                                {{ pred.home_team }}: {{ pred.actual_home_score }}<br>
                                                <small class="text-muted">總分: {{ pred.actual_total_runs or 0 }}</small>
                                            {% else %}
                                                <span class="text-muted">未完成</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if pred.predicted_home_score and pred.predicted_away_score %}
                                                {% set predicted_winner = pred.home_team if pred.predicted_home_score > pred.predicted_away_score else pred.away_team %}
                                                <span class="badge bg-primary">{{ predicted_winner }}</span><br>
                                                {% if pred.is_correct is not none %}
                                                    {% if pred.is_correct %}
                                                        <span class="badge bg-success">✓ 正確</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">✗ 錯誤</span>
                                                    {% endif %}
                                                {% endif %}
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if pred.over_under_analysis %}
                                                <div class="small">
                                                    <!-- 盤口信息 (只顯示真實數據) -->
                                                    {% if pred.over_under_analysis.is_real_line %}
                                                    <div class="mb-1">
                                                        <strong>盤口:</strong>
                                                        <span class="badge bg-secondary">{{ pred.over_under_analysis.line }}</span>
                                                    </div>
                                                    {% endif %}

                                                    <!-- 預測總分 -->
                                                    <div class="mb-1">
                                                        <strong>預測總分:</strong>
                                                        <span class="badge bg-info">{{ pred.over_under_analysis.predicted_total or 'N/A' }}</span>
                                                    </div>

                                                    <!-- 實際總分 (如果有) -->
                                                    {% if pred.actual_total_runs %}
                                                        <div class="mb-1">
                                                            <strong>實際總分:</strong>
                                                            <span class="badge bg-success">{{ pred.actual_total_runs }}</span>
                                                        </div>
                                                    {% endif %}

                                                    <!-- 推薦 -->
                                                    <div class="mb-1">
                                                        {% if pred.over_under_analysis.recommendation == 'over' %}
                                                            <span class="badge bg-warning">{{ pred.over_under_analysis.recommendation_text }}</span>
                                                        {% else %}
                                                            <span class="badge bg-primary">{{ pred.over_under_analysis.recommendation_text }}</span>
                                                        {% endif %}
                                                    </div>

                                                    <!-- 分析結果 -->
                                                    <div class="mb-1">
                                                        {% if pred.over_under_analysis.difference %}
                                                            {% if pred.over_under_analysis.difference > 0 %}
                                                                <span class="badge bg-danger">預測>盤口 (+{{ pred.over_under_analysis.difference|round(1) }})</span>
                                                            {% else %}
                                                                <span class="badge bg-info">預測<盤口 ({{ pred.over_under_analysis.difference|round(1) }})</span>
                                                            {% endif %}
                                                        {% endif %}
                                                    </div>

                                                    <!-- 信心度 -->
                                                    {% if pred.over_under_analysis.strength %}
                                                        <div class="mb-1">
                                                            <span class="badge
                                                                {% if pred.over_under_analysis.strength == 'strong' %}bg-success
                                                                {% elif pred.over_under_analysis.strength == 'moderate' %}bg-warning
                                                                {% else %}bg-secondary{% endif %}">
                                                                {{ pred.over_under_analysis.strength }}
                                                            </span>
                                                        </div>
                                                    {% endif %}

                                                    <!-- 準確性標記 -->
                                                    {% if pred.over_under_correct is not none %}
                                                        <div>
                                                            {% if pred.over_under_correct %}
                                                                <span class="badge bg-success">✓ 預測正確</span>
                                                            {% else %}
                                                                <span class="badge bg-danger">✗ 預測錯誤</span>
                                                            {% endif %}
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if pred.run_line_prediction %}
                                                <div class="small">
                                                    <!-- 讓分推薦 -->
                                                    <div class="mb-1">
                                                        <strong>推薦:</strong>
                                                        <span class="badge bg-primary">{{ pred.run_line_prediction }}</span>
                                                    </div>

                                                    <!-- 盤口資料 (如果有) -->
                                                    {% if pred.betting_odds and pred.betting_odds.spreads %}
                                                        <div class="mb-1">
                                                            <strong>盤口:</strong>
                                                            <span class="badge bg-secondary">{{ pred.betting_odds.spreads.home_spread_point }}</span>
                                                        </div>
                                                    {% endif %}

                                                    <!-- 預測分差 -->
                                                    {% if pred.predicted_home_score and pred.predicted_away_score %}
                                                        {% set score_diff = pred.predicted_home_score - pred.predicted_away_score %}
                                                        <div class="mb-1">
                                                            <strong>預測分差:</strong>
                                                            <span class="badge bg-info">{{ score_diff|round(1) }}</span>
                                                        </div>
                                                    {% endif %}

                                                    <!-- 盤口分析 -->
                                                    {% if pred.betting_analysis and pred.betting_analysis.spread_analysis %}
                                                        <div class="mb-1">
                                                            <span class="badge bg-success">{{ pred.betting_analysis.spread_analysis.recommendation }}</span>
                                                            <span class="badge bg-warning">{{ pred.betting_analysis.spread_analysis.confidence }}信心</span>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if pred.confidence %}
                                                <div class="progress" style="height: 20px;">
                                                    {% set confidence_pct = (pred.confidence * 100)|round(1) %}
                                                    <div class="progress-bar 
                                                        {% if confidence_pct >= 80 %}bg-success
                                                        {% elif confidence_pct >= 60 %}bg-warning
                                                        {% else %}bg-danger{% endif %}" 
                                                         style="width: {{ confidence_pct }}%">
                                                        {{ confidence_pct }}%
                                                    </div>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if pred.quality_score %}
                                                {% set quality_grade = 'A' if pred.quality_score >= 80 else 'B' if pred.quality_score >= 60 else 'C' if pred.quality_score >= 40 else 'D' %}
                                                <span class="badge 
                                                    {% if quality_grade == 'A' %}bg-success
                                                    {% elif quality_grade == 'B' %}bg-primary
                                                    {% elif quality_grade == 'C' %}bg-warning
                                                    {% else %}bg-danger{% endif %}">
                                                    {{ quality_grade }} ({{ pred.quality_score }})
                                                </span>
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if pred.prediction_time %}
                                                {% if pred.prediction_time is string %}
                                                    <small class="text-muted">{{ pred.prediction_time }}</small>
                                                {% else %}
                                                    {{ pred.prediction_time.strftime('%H:%M') }}<br>
                                                    <small class="text-muted">{{ pred.prediction_time.strftime('%m/%d') }}</small>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% elif query_date %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> {{ query_date }} 沒有找到預測記錄
                        </div>
                    {% else %}
                        <div class="alert alert-secondary">
                            <i class="fas fa-calendar-alt"></i> 請選擇日期來查詢預測結果
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 設置日期輸入的默認值為昨天
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('date');
    if (!dateInput.value) {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        dateInput.value = yesterday.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
