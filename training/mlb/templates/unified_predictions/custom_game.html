{% extends "base.html" %}

{% block title %}自定義單場預測 - 統一預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-magic"></i> 自定義單場比賽預測
                </h1>
                <a href="{{ url_for('unified_predictions.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回主頁
                </a>
            </div>

            <!-- 預測表單 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> 比賽設置</h5>
                </div>
                <div class="card-body">
                    <form id="customGameForm">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="homeTeam" class="form-label">主隊</label>
                                <select class="form-control" id="homeTeam" required>
                                    <option value="">選擇主隊</option>
                                    <option value="LAA">洛杉磯天使 (LAA)</option>
                                    <option value="OAK">奧克蘭運動家 (OAK)</option>
                                    <option value="NYY">紐約洋基 (NYY)</option>
                                    <option value="BOS">波士頓紅襪 (BOS)</option>
                                    <option value="LAD">洛杉磯道奇 (LAD)</option>
                                    <option value="SF">舊金山巨人 (SF)</option>
                                    <option value="HOU">休士頓太空人 (HOU)</option>
                                    <option value="TEX">德州遊騎兵 (TEX)</option>
                                    <option value="SEA">西雅圖水手 (SEA)</option>
                                    <option value="MIN">明尼蘇達雙城 (MIN)</option>
                                    <option value="CLE">克里夫蘭守護者 (CLE)</option>
                                    <option value="DET">底特律老虎 (DET)</option>
                                    <option value="KC">堪薩斯城皇家 (KC)</option>
                                    <option value="CWS">芝加哥白襪 (CWS)</option>
                                    <option value="TB">坦帕灣光芒 (TB)</option>
                                    <option value="TOR">多倫多藍鳥 (TOR)</option>
                                    <option value="BAL">巴爾的摩金鶯 (BAL)</option>
                                    <option value="ATL">亞特蘭大勇士 (ATL)</option>
                                    <option value="NYM">紐約大都會 (NYM)</option>
                                    <option value="PHI">費城費城人 (PHI)</option>
                                    <option value="WSH">華盛頓國民 (WSH)</option>
                                    <option value="MIA">邁阿密馬林魚 (MIA)</option>
                                    <option value="MIL">密爾瓦基釀酒人 (MIL)</option>
                                    <option value="CHC">芝加哥小熊 (CHC)</option>
                                    <option value="STL">聖路易紅雀 (STL)</option>
                                    <option value="CIN">辛辛那提紅人 (CIN)</option>
                                    <option value="PIT">匹茲堡海盜 (PIT)</option>
                                    <option value="COL">科羅拉多洛磯 (COL)</option>
                                    <option value="AZ">亞利桑那響尾蛇 (AZ)</option>
                                    <option value="SD">聖地牙哥教士 (SD)</option>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <label for="awayTeam" class="form-label">客隊</label>
                                <select class="form-control" id="awayTeam" required>
                                    <option value="">選擇客隊</option>
                                    <option value="LAA">洛杉磯天使 (LAA)</option>
                                    <option value="OAK">奧克蘭運動家 (OAK)</option>
                                    <option value="NYY">紐約洋基 (NYY)</option>
                                    <option value="BOS">波士頓紅襪 (BOS)</option>
                                    <option value="LAD">洛杉磯道奇 (LAD)</option>
                                    <option value="SF">舊金山巨人 (SF)</option>
                                    <option value="HOU">休士頓太空人 (HOU)</option>
                                    <option value="TEX">德州遊騎兵 (TEX)</option>
                                    <option value="SEA">西雅圖水手 (SEA)</option>
                                    <option value="MIN">明尼蘇達雙城 (MIN)</option>
                                    <option value="CLE">克里夫蘭守護者 (CLE)</option>
                                    <option value="DET">底特律老虎 (DET)</option>
                                    <option value="KC">堪薩斯城皇家 (KC)</option>
                                    <option value="CWS">芝加哥白襪 (CWS)</option>
                                    <option value="TB">坦帕灣光芒 (TB)</option>
                                    <option value="TOR">多倫多藍鳥 (TOR)</option>
                                    <option value="BAL">巴爾的摩金鶯 (BAL)</option>
                                    <option value="ATL">亞特蘭大勇士 (ATL)</option>
                                    <option value="NYM">紐約大都會 (NYM)</option>
                                    <option value="PHI">費城費城人 (PHI)</option>
                                    <option value="WSH">華盛頓國民 (WSH)</option>
                                    <option value="MIA">邁阿密馬林魚 (MIA)</option>
                                    <option value="MIL">密爾瓦基釀酒人 (MIL)</option>
                                    <option value="CHC">芝加哥小熊 (CHC)</option>
                                    <option value="STL">聖路易紅雀 (STL)</option>
                                    <option value="CIN">辛辛那提紅人 (CIN)</option>
                                    <option value="PIT">匹茲堡海盜 (PIT)</option>
                                    <option value="COL">科羅拉多洛磯 (COL)</option>
                                    <option value="AZ">亞利桑那響尾蛇 (AZ)</option>
                                    <option value="SD">聖地牙哥教士 (SD)</option>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <label for="gameDate" class="form-label">比賽日期</label>
                                <input type="date" 
                                       class="form-control" 
                                       id="gameDate" 
                                       name="gameDate" 
                                       value="{{ datetime.now().strftime('%Y-%m-%d') }}"
                                       required>
                                <small class="form-text text-muted">
                                    預測將基於該日期之前的歷史數據
                                </small>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-lg" id="predictBtn">
                                    <i class="fas fa-magic"></i> 開始預測
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 載入指示器 -->
            <div id="loadingIndicator" class="text-center" style="display: none;">
                <div class="card">
                    <div class="card-body">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">預測中...</span>
                        </div>
                        <p class="mt-2">正在分析比賽數據並生成預測...</p>
                    </div>
                </div>
            </div>

            <!-- 錯誤提示 -->
            <div id="errorAlert" class="alert alert-danger" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="errorMessage"></span>
            </div>

            <!-- 預測結果 -->
            <div id="predictionResults" style="display: none;">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line"></i> 預測結果
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="resultContent">
                            <!-- 結果將通過JavaScript填充 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 表單提交處理
    document.getElementById('customGameForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const homeTeam = document.getElementById('homeTeam').value;
        const awayTeam = document.getElementById('awayTeam').value;
        const gameDate = document.getElementById('gameDate').value;
        
        // 驗證表單
        if (!homeTeam || !awayTeam || !gameDate) {
            showError('請填寫所有必要欄位');
            return;
        }
        
        if (homeTeam === awayTeam) {
            showError('主隊和客隊不能相同');
            return;
        }
        
        // 開始預測
        makePrediction(homeTeam, awayTeam, gameDate);
    });
});

function makePrediction(homeTeam, awayTeam, gameDate) {
    // 顯示載入動畫
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('predictionResults').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
    document.getElementById('predictBtn').disabled = true;
    
    // 調用API
    const params = new URLSearchParams({
        home_team: homeTeam,
        away_team: awayTeam,
        date: gameDate
    });
    
    fetch(`/unified/api/predict/custom_game?${params}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('predictBtn').disabled = false;
            
            if (data.success) {
                showPredictionResult(data.prediction, data.request);
            } else {
                showError(data.error || '預測失敗');
            }
        })
        .catch(error => {
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('predictBtn').disabled = false;
            showError('網路錯誤：' + error.message);
        });
}

function showPredictionResult(prediction, request) {
    const resultContent = document.getElementById('resultContent');
    const betting = prediction.betting_recommendations || {};
    
    resultContent.innerHTML = `
        <div class="row">
            <div class="col-12 mb-3">
                <h6 class="text-center">
                    <strong>${request.away_team} @ ${request.home_team}</strong>
                    <br><small class="text-muted">${request.date}</small>
                </h6>
            </div>
        </div>
        
        <!-- 得分預測 -->
        <div class="row text-center mb-4">
            <div class="col-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h3 class="text-primary">${prediction.predicted_away_score}</h3>
                        <strong>${request.away_team} (客隊)</strong>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h3 class="text-success">${prediction.predicted_home_score}</h3>
                        <strong>${request.home_team} (主隊)</strong>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 勝率分析 -->
        <div class="row mb-4">
            <div class="col-12">
                <h6>勝率分析</h6>
                <div class="progress mb-2" style="height: 30px;">
                    <div class="progress-bar bg-success" style="width: ${prediction.home_win_probability * 100}%">
                        ${request.home_team}: ${(prediction.home_win_probability * 100).toFixed(1)}%
                    </div>
                    <div class="progress-bar bg-primary" style="width: ${prediction.away_win_probability * 100}%">
                        ${request.away_team}: ${(prediction.away_win_probability * 100).toFixed(1)}%
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 博彩推薦 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fas fa-bullseye"></i> 博彩推薦</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>大小分:</strong> <span class="badge bg-info">${betting.over_under || 'N/A'}</span></p>
                        <p><strong>讓分盤:</strong> <span class="badge bg-warning text-dark">${betting.run_line || 'N/A'}</span></p>
                        <p><strong>勝負盤:</strong> <span class="badge bg-success">${betting.moneyline || 'N/A'}</span></p>
                        <p><strong>信心等級:</strong> <span class="badge bg-secondary">${betting.confidence_level || 'N/A'}</span></p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 預測詳情</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>總得分:</strong> ${prediction.total_runs}</p>
                        <p><strong>信心度:</strong> ${(prediction.confidence * 100).toFixed(1)}%</p>
                        <p><strong>模型類型:</strong> ${prediction.model_type}</p>
                        <p><strong>特徵數量:</strong> ${prediction.features_extracted}</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('predictionResults').style.display = 'block';
}

function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorAlert').style.display = 'block';
}
</script>
{% endblock %}
