{% extends "base.html" %}

{% block title %}預測歷史記錄{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-history"></i> 預測歷史記錄
                </h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('unified_predictions.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回主頁
                    </a>
                    <a href="{{ url_for('unified_predictions.query_page') }}" class="btn btn-warning">
                        <i class="fas fa-search"></i> 查詢結果
                    </a>
                    <a href="{{ url_for('unified_predictions.analytics_page') }}" class="btn btn-success">
                        <i class="fas fa-chart-bar"></i> 性能分析
                    </a>
                </div>
            </div>

            {% if error_message %}
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle"></i> {{ error_message }}
            </div>
            {% endif %}

            <!-- 時間範圍選擇 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">時間範圍選擇</h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="form-inline">
                                <div class="form-group mr-3">
                                    <label for="days" class="mr-2">查看過去：</label>
                                    <select name="days" id="days" class="form-control" onchange="this.form.submit()">
                                        <option value="7" {% if days_back == 7 %}selected{% endif %}>7天</option>
                                        <option value="15" {% if days_back == 15 %}selected{% endif %}>15天</option>
                                        <option value="30" {% if days_back == 30 %}selected{% endif %}>30天</option>
                                        <option value="60" {% if days_back == 60 %}selected{% endif %}>60天</option>
                                        <option value="90" {% if days_back == 90 %}selected{% endif %}>90天</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            {% if history and history.success %}
            <!-- 統計摘要 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        總預測數
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ history.total_predictions }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        準確率
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ "%.1f"|format(history.summary.accuracy_rate) }}%
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-bullseye fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        平均信心度
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ "%.1f"|format(history.summary.average_confidence) }}%
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-percentage fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        已驗證預測
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ history.summary.verified_games }}/{{ history.summary.total_games }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 預測歷史表格 -->
            {% if history.predictions %}
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                預測歷史詳情 ({{ history.total_predictions }} 筆記錄)
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="historyTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>預測日期</th>
                                            <th>比賽ID</th>
                                            <th>預測比分</th>
                                            <th>實際比分</th>
                                            <th>準確性</th>
                                            <th>信心度</th>
                                            <th>分差</th>
                                            <th>模型版本</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for pred in history.predictions %}
                                        <tr>
                                            <td>{{ pred.prediction_date[:10] }}</td>
                                            <td>
                                                <a href="{{ url_for('games.game_detail', game_id=pred.game_id) }}" 
                                                   class="text-primary">
                                                    {{ pred.game_id }}
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge badge-primary">
                                                    {{ "%.1f"|format(pred.predicted_away_score) }} - 
                                                    {{ "%.1f"|format(pred.predicted_home_score) }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if pred.actual_home_score is not none and pred.actual_away_score is not none %}
                                                <span class="badge badge-secondary">
                                                    {{ pred.actual_away_score|int }} - {{ pred.actual_home_score|int }}
                                                </span>
                                                {% else %}
                                                <span class="text-muted">載入中...</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if pred.is_correct is not none %}
                                                    {% if pred.is_correct %}
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-check"></i> 正確
                                                    </span>
                                                    {% else %}
                                                    <span class="badge badge-danger">
                                                        <i class="fas fa-times"></i> 錯誤
                                                    </span>
                                                    {% endif %}
                                                {% else %}
                                                <span class="badge badge-warning">
                                                    <i class="fas fa-clock"></i> 待驗證
                                                </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    {% set confidence_pct = (pred.confidence * 100) if pred.confidence else 0 %}
                                                    <div class="progress-bar 
                                                        {% if confidence_pct >= 80 %}bg-success
                                                        {% elif confidence_pct >= 60 %}bg-warning
                                                        {% else %}bg-danger{% endif %}" 
                                                        role="progressbar" 
                                                        style="width: {{ confidence_pct }}%">
                                                        {{ "%.1f"|format(confidence_pct) }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                {% if pred.score_difference is not none %}
                                                <span class="badge 
                                                    {% if pred.score_difference <= 2 %}badge-success
                                                    {% elif pred.score_difference <= 4 %}badge-warning
                                                    {% else %}badge-danger{% endif %}">
                                                    {{ "%.1f"|format(pred.score_difference) }}
                                                </span>
                                                {% else %}
                                                <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if pred.model_version and pred.model_version.startswith('Core_') %}<span class="badge bg-success">{{ pred.model_version }}</span>{% elif pred.model_version == 'legacy' %}<span class="badge bg-secondary">歷史模型 (Legacy)</span>{% else %}<span class="badge bg-warning">舊版模型 ({{ pred.model_version or '未知' }})</span>{% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-body text-center">
                            <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-gray-600">沒有找到預測記錄</h5>
                            <p class="text-gray-500">在過去 {{ days_back }} 天內沒有找到任何預測記錄。</p>
                            <a href="{{ url_for('unified_predictions.index') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 開始預測
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% else %}
            <!-- 錯誤狀態 -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h5 class="text-gray-600">載入歷史記錄失敗</h5>
                            <p class="text-gray-500">
                                {% if history and history.error %}
                                    錯誤信息: {{ history.error }}
                                {% else %}
                                    無法載入預測歷史記錄，請稍後再試。
                                {% endif %}
                            </p>
                            <a href="{{ url_for('unified_predictions.index') }}" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> 返回主頁
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- DataTables JavaScript -->
<script>
$(document).ready(function() {
    $('#historyTable').DataTable({
        "order": [[ 0, "desc" ]], // 按日期降序排列
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Chinese-traditional.json"
        },
        "columnDefs": [
            { "orderable": false, "targets": [2, 3, 4, 6] } // 某些列不可排序
        ]
    });
});
</script>
{% endblock %}
