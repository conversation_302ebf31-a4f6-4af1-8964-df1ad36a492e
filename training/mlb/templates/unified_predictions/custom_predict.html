{% extends "base.html" %}

{% block title %}自定義日期預測 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-calendar-alt"></i> 自定義日期預測</h2>
                <div>
                    <a href="{{ url_for('unified_predictions.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回主頁
                    </a>
                </div>
            </div>

            <!-- 日期選擇和預測生成 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> 預測設置</h5>
                </div>
                <div class="card-body">
                    <form id="customPredictForm">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="targetDate" class="form-label">選擇預測日期</label>
                                <input type="date" 
                                       class="form-control" 
                                       id="targetDate" 
                                       name="targetDate" 
                                       value="{{ datetime.now().strftime('%Y-%m-%d') }}"
                                       min="2020-01-01"
                                       max="{{ datetime.now().strftime('%Y-%m-%d') }}">
                                <small class="form-text text-muted">
                                    訓練數據將使用選擇日期的前一天及之前的數據
                                </small>
                            </div>
                            <div class="col-md-12 mt-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="excludePostponed" checked>
                                    <label class="form-check-label" for="excludePostponed">
                                        <i class="fas fa-calendar-times text-warning"></i> 排除延期/取消比賽
                                    </label>
                                    <small class="form-text text-muted d-block">
                                        排除狀態為postponed、rescheduled、cancelled的比賽，避免影響預測準確性
                                    </small>
                                </div>
                            </div>

                            <!-- 預測處理選項 -->
                            <div class="col-md-12 mt-4">
                                <h6><i class="fas fa-cog"></i> 預測處理選項</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="predictionMode" id="updateExisting" value="update" checked>
                                            <label class="form-check-label" for="updateExisting">
                                                <i class="fas fa-sync text-primary"></i> <strong>更新現有預測</strong>
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                更新已存在的預測，備份原預測結果
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="predictionMode" id="skipExisting" value="skip">
                                            <label class="form-check-label" for="skipExisting">
                                                <i class="fas fa-forward text-warning"></i> <strong>跳過已有預測</strong>
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                只為沒有預測的比賽生成新預測
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mt-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="predictionMode" id="forceRegenerate" value="force">
                                            <label class="form-check-label" for="forceRegenerate">
                                                <i class="fas fa-redo text-danger"></i> <strong>強制重新生成</strong>
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                強制重新生成所有預測（用於測試新邏輯）
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mt-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="predictionMode" id="lowConfidenceOnly" value="low_confidence">
                                            <label class="form-check-label" for="lowConfidenceOnly">
                                                <i class="fas fa-question-circle text-info"></i> <strong>只更新低信心預測</strong>
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                只更新信心度低於 60% 的預測
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 高級選項 -->
                            <div class="col-md-12 mt-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showAdvancedOptions">
                                    <label class="form-check-label" for="showAdvancedOptions">
                                        <i class="fas fa-tools"></i> 顯示高級選項
                                    </label>
                                </div>
                                <div id="advancedOptions" style="display: none;" class="mt-3 p-3 border rounded bg-light">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="modelVersionSuffix" class="form-label">模型版本後綴</label>
                                            <select class="form-control" id="modelVersionSuffix">
                                                <option value="">選擇版本 (可選)</option>
                                                <optgroup label="🏆 核心模型版本">
                                                    <option value="Core_v1.0">Core_v1.0 - 核心預測模型</option>
                                                </optgroup>
                                                <optgroup label="🎯 預測改進版本">
                                                    <option value="improved_v1">improved_v1 - 基礎改進版本</option>
                                                    <option value="improved_v2">improved_v2 - 進階改進版本</option>
                                                    <option value="improved_v3">improved_v3 - 最新改進版本</option>
                                                    <option value="optimized_v1.0">optimized_v1.0 - 優化版本</option>
                                                </optgroup>
                                                <optgroup label="🧠 智能特徵工程版本">
                                                    <option value="intelligent_features">intelligent_features - 智能特徵工程</option>
                                                    <option value="smart_features">smart_features - 智能特徵分析</option>
                                                    <option value="feature_engineering_v1">feature_engineering_v1 - 特徵工程v1</option>
                                                    <option value="advanced_analytics">advanced_analytics - 高級分析</option>
                                                </optgroup>
                                                <optgroup label="⚾ 投手分析版本">
                                                    <option value="pitcher_analysis">pitcher_analysis - 投手深度分析</option>
                                                    <option value="ace_pitcher_v1">ace_pitcher_v1 - 王牌投手特化</option>
                                                    <option value="pitcher_matchup">pitcher_matchup - 投手對戰分析</option>
                                                    <option value="era_calibrated">era_calibrated - ERA校正版本</option>
                                                    <option value="comprehensive_pitcher">comprehensive_pitcher - 全面投手分析</option>
                                                    <option value="fast_intelligent">fast_intelligent - 快速智能分析</option>
                                                </optgroup>
                                                <optgroup label="🛡️ 壓制效應版本">
                                                    <option value="boyd_suppression">boyd_suppression - Boyd壓制分析</option>
                                                    <option value="lineup_suppression">lineup_suppression - 打線壓制分析</option>
                                                    <option value="ace_dominance">ace_dominance - 王牌主宰效應</option>
                                                    <option value="pitcher_dominance">pitcher_dominance - 投手主導分析</option>
                                                </optgroup>
                                                <optgroup label="💥 意外情況版本">
                                                    <option value="outlier_detection">outlier_detection - 意外情況檢測</option>
                                                    <option value="explosion_analysis">explosion_analysis - 爆發分析</option>
                                                    <option value="upset_prediction">upset_prediction - 冷門預測</option>
                                                    <option value="variance_adjusted">variance_adjusted - 變異調整</option>
                                                </optgroup>
                                                <optgroup label="📊 數據源版本">
                                                    <option value="real_odds_only">real_odds_only - 僅真實盤口</option>
                                                    <option value="historical_validation">historical_validation - 歷史驗證</option>
                                                    <option value="recent_form_focus">recent_form_focus - 近期狀態重點</option>
                                                    <option value="comprehensive_data">comprehensive_data - 綜合數據</option>
                                                </optgroup>
                                                <optgroup label="🧪 實驗版本">
                                                    <option value="experimental_v1">experimental_v1 - 實驗版本1</option>
                                                    <option value="experimental_v2">experimental_v2 - 實驗版本2</option>
                                                    <option value="test_algorithm">test_algorithm - 測試算法</option>
                                                    <option value="beta_features">beta_features - Beta功能</option>
                                                    <option value="unified_v1.0">unified_v1.0 - 統一預測v1.0</option>
                                                    <option value="ml_v1.0">ml_v1.0 - 機器學習v1.0</option>
                                                    <option value="Core_2025">Core_2025 - 2025核心版本</option>
                                                </optgroup>
                                                <optgroup label="🎲 自定義版本">
                                                    <option value="custom_logic">custom_logic - 自定義邏輯</option>
                                                    <option value="user_defined">user_defined - 用戶定義</option>
                                                </optgroup>
                                            </select>
                                            <small class="form-text text-muted">選擇或標識改進的預測邏輯版本</small>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="customVersionInput" class="form-label">自定義版本名稱 (可選)</label>
                                            <input type="text" class="form-control" id="customVersionInput" placeholder="輸入自定義版本名稱">
                                            <small class="form-text text-muted">如果需要自定義版本名稱，會覆蓋上方選擇</small>
                                        </div>
                                        <div class="col-md-6 mt-2">
                                            <label for="confidenceThreshold" class="form-label">信心度閾值</label>
                                            <input type="number" class="form-control" id="confidenceThreshold" min="0" max="1" step="0.1" value="0.6">
                                            <small class="form-text text-muted">只更新低於此信心度的預測 (0-1)</small>
                                        </div>
                                        <div class="col-md-12 mt-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="backupExisting" checked>
                                                <label class="form-check-label" for="backupExisting">
                                                    <i class="fas fa-save text-success"></i> 備份現有預測
                                                </label>
                                                <small class="form-text text-muted d-block">
                                                    在更新前備份原預測結果到文件
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 d-flex align-items-end mt-3">
                                <button type="submit" class="btn btn-primary" id="generateBtn">
                                    <i class="fas fa-magic"></i> 生成預測
                                </button>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="setDate(-1)">昨天</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setDate(-7)">一週前</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setDate(-30)">一個月前</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 預測結果顯示 -->
            <div id="predictionResults" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> 預測結果</h5>
                    </div>
                    <div class="card-body">
                        <div id="predictionSummary" class="mb-3"></div>
                        <div id="postponedGamesSummary" class="mb-3"></div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>比賽</th>
                                        <th>比賽狀態</th>
                                        <th>預測比分</th>
                                        <th>投手對戰</th>
                                        <th>信心度</th>
                                        <th>使用模型</th>
                                        <th>大小分</th>
                                        <th>讓分盤</th>
                                        <th>實際結果</th>
                                        <th>準確性</th>
                                        <th>詳細分析</th>
                                    </tr>
                                </thead>
                                <tbody id="predictionTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 載入中提示 -->
            <div id="loadingIndicator" style="display: none;">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">載入中...</span>
                        </div>
                        <p class="mt-3">正在生成預測，請稍候...</p>
                    </div>
                </div>
            </div>

            <!-- 錯誤提示 -->
            <div id="errorAlert" class="alert alert-danger" style="display: none;">
                <h5><i class="fas fa-exclamation-triangle"></i> 錯誤</h5>
                <p id="errorMessage"></p>
            </div>
        </div>
    </div>
</div>

<script>
// 生成投手對戰顯示
function generatePitcherMatchupDisplay(pred) {
    if (!pred || !pred.pitcher_analysis) {
        return '<span class="text-muted">無投手信息</span>';
    }

    const home = pred.pitcher_analysis.home_pitcher || {};
    const away = pred.pitcher_analysis.away_pitcher || {};
    const matchupType = pred.pitcher_analysis.matchup_type || '普通對戰';

    // 根據投手等級設置顏色
    function getStrengthColor(strength) {
        switch(strength) {
            case '王牌': return 'success';
            case '優秀': return 'info';
            case '普通': return 'secondary';
            case '弱勢': return 'danger';
            default: return 'secondary';
        }
    }

    const homeColor = getStrengthColor(home.strength);
    const awayColor = getStrengthColor(away.strength);

    // 對戰類型顏色
    function getMatchupColor(type) {
        switch(type) {
            case '王牌對決': return 'success';
            case '打擊戰': return 'danger';
            case '強弱對戰': return 'warning';
            default: return 'secondary';
        }
    }

    const matchupColor = getMatchupColor(matchupType);

    return `
        <div class="d-flex flex-column">
            <div class="mb-1">
                <span class="badge bg-${awayColor}">${away.name || '未知'}</span>
                <small class="text-muted">(ERA: ${away.era ? away.era.toFixed(2) : 'N/A'})</small>
            </div>
            <div class="mb-1">
                <span class="badge bg-${homeColor}">${home.name || '未知'}</span>
                <small class="text-muted">(ERA: ${home.era ? home.era.toFixed(2) : 'N/A'})</small>
            </div>
            <div>
                <span class="badge bg-${matchupColor}">${matchupType}</span>
            </div>
        </div>
    `;
}

// 設置日期快捷按鈕
function setDate(daysOffset) {
    const today = new Date();
    const targetDate = new Date(today.getTime() + daysOffset * 24 * 60 * 60 * 1000);
    const dateString = targetDate.toISOString().split('T')[0];
    document.getElementById('targetDate').value = dateString;
}

// 高級選項顯示/隱藏
document.getElementById('showAdvancedOptions').addEventListener('change', function() {
    const advancedOptions = document.getElementById('advancedOptions');
    if (this.checked) {
        advancedOptions.style.display = 'block';
    } else {
        advancedOptions.style.display = 'none';
    }
});

// 模型版本選擇邏輯
document.getElementById('modelVersionSuffix').addEventListener('change', function() {
    if (this.value) {
        // 當選擇下拉選單時，清空自定義輸入
        document.getElementById('customVersionInput').value = '';
    }
});

document.getElementById('customVersionInput').addEventListener('input', function() {
    if (this.value.trim()) {
        // 當輸入自定義版本時，清空下拉選單選擇
        document.getElementById('modelVersionSuffix').value = '';
    }
});

// 表單提交處理
document.getElementById('customPredictForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const targetDate = document.getElementById('targetDate').value;
    if (!targetDate) {
        alert('請選擇預測日期');
        return;
    }
    
    generatePredictions(targetDate);
});

// 生成預測
function generatePredictions(targetDate) {
    // 顯示載入指示器
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('predictionResults').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
    document.getElementById('generateBtn').disabled = true;

    // 獲取預測處理選項
    const predictionMode = document.querySelector('input[name="predictionMode"]:checked').value;

    // 獲取模型版本後綴 (優先使用自定義輸入)
    const customVersionInput = document.getElementById('customVersionInput').value.trim();
    const selectedVersion = document.getElementById('modelVersionSuffix').value;
    const modelVersionSuffix = customVersionInput || selectedVersion;

    const confidenceThreshold = parseFloat(document.getElementById('confidenceThreshold').value) || 0.6;
    const backupExisting = document.getElementById('backupExisting').checked;

    // 構建請求選項
    const options = {
        date: targetDate,
        exclude_postponed: document.getElementById('excludePostponed').checked,
        prediction_options: {
            force_regenerate: predictionMode === 'force',
            update_existing: predictionMode === 'update',
            skip_existing: predictionMode === 'skip',
            low_confidence_only: predictionMode === 'low_confidence',
            backup_existing: backupExisting,
            model_version_suffix: modelVersionSuffix,
            confidence_threshold: predictionMode === 'low_confidence' ? confidenceThreshold : 0.0
        }
    };

    // 發送預測請求
    fetch('/unified/api/predict/custom_date_enhanced', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(options)
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loadingIndicator').style.display = 'none';
        document.getElementById('generateBtn').disabled = false;
        
        if (data.success) {
            displayPredictions(data);
            // 載入實際結果進行比對（不覆蓋盤口數據）
            loadActualResultsOnly(targetDate);
        } else {
            showError(data.error || '預測生成失敗');
        }
    })
    .catch(error => {
        document.getElementById('loadingIndicator').style.display = 'none';
        document.getElementById('generateBtn').disabled = false;
        showError('網絡錯誤: ' + error.message);
    });
}

// 顯示預測結果
function displayPredictions(data) {
    const summaryDiv = document.getElementById('predictionSummary');
    summaryDiv.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h6>預測日期</h6>
                        <h4>${data.target_date}</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h6>成功預測</h6>
                        <h4>${data.successful_predictions}/${data.total_games}</h4>
                    </div>
                </div>
            </div>
            ${data.postponed_games_excluded > 0 ? `
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h6>排除延期</h6>
                        <h4>${data.postponed_games_excluded} 場</h4>
                    </div>
                </div>
            </div>
            ` : ''}
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h6>訓練截止</h6>
                        <h4>${data.training_end_date}</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h6>平均信心度</h6>
                        <h4>${data.predictions.length > 0 ? (data.predictions.reduce((sum, p) => sum + (p.confidence || 0), 0) / data.predictions.length * 100).toFixed(1) : 0}%</h4>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    const tableBody = document.getElementById('predictionTableBody');
    tableBody.innerHTML = '';
    
    data.predictions.forEach(pred => {
        const row = document.createElement('tr');

        // 檢查是否為延期比賽
        let gameStatusHtml = '<span class="badge bg-success">正常</span>';
        let gameInfoHtml = `<strong>${pred.away_team} @ ${pred.home_team}</strong><br><small class="text-muted">${pred.game_id}</small>`;

        // 檢測延期比賽 (使用後端傳遞的is_postponed標記)
        if (pred.is_postponed) {
            gameStatusHtml = `
                <span class="badge bg-warning">延期</span><br>
                <small class="text-muted">狀態: ${pred.game_status}</small>
            `;
            gameInfoHtml = `
                <strong>${pred.away_team} @ ${pred.home_team}</strong><br>
                <small class="text-muted">${pred.game_id}</small><br>
                <small class="text-warning"><i class="fas fa-calendar-times"></i> ${pred.game_status}比賽</small>
            `;
        }

        row.innerHTML = `
            <td>${gameInfoHtml}</td>
            <td>${gameStatusHtml}</td>
            <td>
                <span class="badge bg-primary">${pred.predicted_score}</span>
            </td>
            <td>
                ${generatePitcherMatchupDisplay(pred)}
            </td>
            <td>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar" role="progressbar"
                         style="width: ${(pred.confidence || 0) * 100}%"
                         aria-valuenow="${(pred.confidence || 0) * 100}"
                         aria-valuemin="0" aria-valuemax="100">
                        ${((pred.confidence || 0) * 100).toFixed(1)}%
                    </div>
                </div>
            </td>
            <td id="model_version_${pred.game_id}"></td> <!-- Placeholder for model version -->
            <td id="ou_${pred.game_id}">
                ${generateOverUnderDisplay(pred)}
            </td>
            <td id="rl_${pred.game_id}">
                ${generateRunLineDisplay(pred)}
            </td>
            <td id="actual_${pred.game_id}">
                <span class="text-muted">載入中...</span>
            </td>
            <td id="accuracy_${pred.game_id}">
                <span class="text-muted">載入中...</span>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-info" onclick="showDetailedAnalysis('${pred.game_id}', ${JSON.stringify(pred).replace(/"/g, '&quot;')})">
                    <i class="fas fa-chart-line"></i> 詳細
                </button>
            </td>
        `;
        tableBody.appendChild(row);

        // 在 JavaScript 中動態設置模型版本顯示
        const modelVersionCell = document.getElementById(`model_version_${pred.game_id}`);
        if (modelVersionCell) {
            let modelVersionText = '';
            let badgeClass = 'bg-secondary';

            if (pred.model_used && pred.model_used.startsWith('Core_')) {
                modelVersionText = pred.model_used;
                badgeClass = 'bg-success';
            } else if (pred.model_used === 'legacy') {
                modelVersionText = '歷史模型 (Legacy)';
                badgeClass = 'bg-secondary';
            } else {
                modelVersionText = `舊版模型 (${pred.model_used || '未知'})`;
                badgeClass = 'bg-warning';
            }
            modelVersionCell.innerHTML = `<span class="badge ${badgeClass}">${modelVersionText}</span>`;
        }
    });
    
    document.getElementById('predictionResults').style.display = 'block';
}

// 生成大小分顯示
function generateOverUnderDisplay(pred) {
    let html = '';

    // 檢查新的博彩建議數據結構
    if (pred.betting_recommendations && pred.betting_recommendations.over_under) {
        const ou = pred.betting_recommendations.over_under;
        const line = ou.line;
        const predicted = ou.predicted_total;
        const recommendation = ou.recommendation;
        const edge = ou.edge;

        html += `<div><strong>盤口:</strong> ${line}</div>`;
        html += `<div><strong>預測:</strong> ${predicted.toFixed(1)}</div>`;

        const recClass = recommendation === 'Over' ? 'success' : 'info';
        html += `<div><span class="badge bg-${recClass}">${recommendation}</span></div>`;

        if (edge > 0.5) {
            html += `<div><small class="text-warning">優勢: ${edge.toFixed(1)}</small></div>`;
        }
    }
    // 檢查博彩盤口數據
    else if (pred.betting_lines && pred.betting_lines.over_under) {
        const line = pred.betting_lines.over_under;
        const predicted = pred.predicted_total_runs || 0;

        html += `<div><strong>盤口:</strong> ${line}</div>`;
        html += `<div><strong>預測:</strong> ${predicted.toFixed(1)}</div>`;

        const recommendation = predicted > line ? 'Over' : 'Under';
        const recClass = recommendation === 'Over' ? 'success' : 'info';
        html += `<div><span class="badge bg-${recClass}">${recommendation}</span></div>`;
    }
    // 舊數據結構兼容
    else {
        const line = pred.over_under_line;
        const recommendation = pred.over_under_recommendation;

        if (line && line !== 'N/A') {
            html += `<div><strong>盤口:</strong> ${line}</div>`;
            if (recommendation && recommendation !== 'N/A') {
                const recClass = recommendation.includes('大分') || recommendation.includes('Over') ? 'primary' : 'info';
                html += `<div><span class="badge bg-${recClass}">${recommendation}</span></div>`;
            }
        } else {
            html = '<span class="text-muted">N/A</span>';
        }
    }

    return html || '<span class="text-muted">N/A</span>';
}

// 生成讓分盤顯示
function generateRunLineDisplay(pred) {
    let html = '';

    // 檢查新的博彩建議數據結構
    if (pred.betting_recommendations && pred.betting_recommendations.run_line) {
        const rl = pred.betting_recommendations.run_line;
        const spread = rl.spread;
        const predicted_margin = rl.predicted_margin;
        const recommendation = rl.recommendation;
        const covers = rl.covers;

        html += `<div><strong>讓分:</strong> ${spread}</div>`;
        html += `<div><strong>預測差距:</strong> ${predicted_margin.toFixed(1)}</div>`;

        const recClass = covers ? 'success' : 'warning';
        html += `<div><span class="badge bg-${recClass}">${recommendation}</span></div>`;
    }
    // 檢查博彩盤口數據
    else if (pred.betting_lines && pred.betting_lines.run_line_spread) {
        const spread = pred.betting_lines.run_line_spread;
        const predicted_home = pred.predicted_home_score || 0;
        const predicted_away = pred.predicted_away_score || 0;
        const predicted_margin = predicted_home - predicted_away;

        html += `<div><strong>讓分:</strong> ${spread}</div>`;
        html += `<div><strong>預測差距:</strong> ${predicted_margin.toFixed(1)}</div>`;

        const covers = predicted_margin > spread;
        const recClass = covers ? 'success' : 'warning';
        const recommendation = covers ? `${pred.home_team} -${spread}` : `${pred.away_team} +${spread}`;
        html += `<div><span class="badge bg-${recClass}">${recommendation}</span></div>`;
    }
    // 舊數據結構兼容
    else {
        const line = pred.run_line;
        const recommendation = pred.run_line_recommendation;

        if (line && line !== 'N/A') {
            html += `<div><strong>盤口:</strong> ${line}</div>`;

            if (recommendation && recommendation !== 'N/A') {
                const recClass = recommendation.includes('主隊') ? 'success' : 'warning';
                html += `<div><span class="badge bg-${recClass}">${recommendation}</span></div>`;
            }
        } else {
            html = '<span class="text-muted">N/A</span>';
        }
    }

    return html || '<span class="text-muted">N/A</span>';
}

// 載入實際結果（只更新實際結果和準確性，不覆蓋盤口數據）
function loadActualResultsOnly(targetDate) {
    fetch(`/unified/api/predictions/date/${targetDate}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.predictions) {
            data.predictions.forEach(pred => {
                updateActualResultsOnly(pred);
            });
        }
    })
    .catch(error => {
        console.error('載入實際結果失敗:', error);
    });
}

// 載入實際結果（舊版本，會覆蓋盤口數據）
function loadActualResults(targetDate) {
    fetch(`/unified/api/predictions/date/${targetDate}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.predictions) {
            data.predictions.forEach(pred => {
                updateActualResults(pred);
            });
        }
    })
    .catch(error => {
        console.error('載入實際結果失敗:', error);
    });
}

// 只更新實際結果和準確性（不覆蓋盤口數據）
function updateActualResultsOnly(pred) {
    // 更新實際結果欄位
    const actualCell = document.getElementById(`actual_${pred.game_id}`);
    if (actualCell && pred.actual_home_score !== undefined && pred.actual_away_score !== undefined &&
        pred.actual_home_score !== null && pred.actual_away_score !== null) {
        actualCell.innerHTML = `
            <div class="small">
                <div><strong>${pred.actual_away_score} - ${pred.actual_home_score}</strong></div>
                <div class="text-muted">實際比分</div>
            </div>
        `;
    }

    // 更新準確性欄位
    const accuracyCell = document.getElementById(`accuracy_${pred.game_id}`);
    if (accuracyCell) {
        let accuracyHtml = '<span class="text-muted">計算中...</span>';

        if (pred.is_correct !== undefined) {
            const isCorrect = pred.is_correct;
            const badgeClass = isCorrect ? 'bg-success' : 'bg-danger';
            const text = isCorrect ? '✓ 正確' : '✗ 錯誤';

            accuracyHtml = `<span class="badge ${badgeClass}">${text}</span>`;

            // 如果有得分差異信息
            if (pred.score_difference !== undefined && pred.score_difference !== null) {
                accuracyHtml += `<br><small class="text-muted">差異: ${pred.score_difference.toFixed(1)}</small>`;
            }
        }

        accuracyCell.innerHTML = accuracyHtml;
    }
}

// 更新實際結果顯示（舊版本，會覆蓋盤口數據）
function updateActualResults(pred) {
    // 更新大小分預測 (優先顯示真實博彩盤口)
    const ouCell = document.getElementById(`ou_${pred.game_id}`);
    if (ouCell) {
        let ouHtml = '<span class="text-muted">N/A</span>';

        // 只使用真實博彩盤口數據
        if (pred.betting_odds && pred.betting_odds.totals) {
            const totals = pred.betting_odds.totals;
            const line = totals.total_point;
            const bookmaker = totals.bookmaker;

            // 只顯示真實博彩數據
            if (line && totals.is_real !== false) {
                const predTotal = pred.predicted_total_runs || 0;
                const recommendation = predTotal > line ? 'Over' : 'Under';
                const confidence = pred.over_under_confidence || 0;

                ouHtml = `
                    <div class="small">
                        <div><strong>線: ${line}</strong></div>
                        <div class="text-muted">預測: ${predTotal ? predTotal.toFixed(1) : 'N/A'}</div>
                        <div class="badge bg-info">${recommendation} (${confidence ? confidence.toFixed(0) : 0}%)</div>
                        ${bookmaker ? `<div class="text-muted">${bookmaker}</div>` : ''}
                    </div>
                `;
            }
        }

        ouCell.innerHTML = ouHtml;
    }

    // 更新讓分盤預測
    const rlCell = document.getElementById(`rl_${pred.game_id}`);
    if (rlCell) {
        let rlHtml = '<span class="text-muted">N/A</span>';

        if (pred.betting_odds && pred.betting_odds.spreads) {
            const spreads = pred.betting_odds.spreads;
            const homeSpread = spreads.home_spread_point;
            const awaySpread = spreads.away_spread_point;

            if (homeSpread !== null || awaySpread !== null) {
                const spreadText = homeSpread !== null ? `主隊 ${homeSpread > 0 ? '+' : ''}${homeSpread}` :
                                 awaySpread !== null ? `客隊 ${awaySpread > 0 ? '+' : ''}${awaySpread}` : '';

                rlHtml = `
                    <div class="small">
                        ${spreadText ? `<div><strong>${spreadText}</strong></div>` : ''}
                        ${pred.run_line_prediction ? `<div class="badge bg-warning">${pred.run_line_prediction}</div>` : ''}
                    </div>
                `;
            }
        } else if (pred.run_line_prediction) {
            rlHtml = pred.run_line_prediction;
        }

        rlCell.innerHTML = rlHtml;
    }
    
    // 更新實際結果
    const actualCell = document.getElementById(`actual_${pred.game_id}`);
    if (actualCell && pred.actual_home_score !== null && pred.actual_away_score !== null) {
        actualCell.innerHTML = `<span class="badge bg-success">${pred.actual_away_score} - ${pred.actual_home_score}</span>`;
    } else {
        actualCell.innerHTML = '<span class="text-muted">未完成</span>';
    }
    
    // 更新準確性
    const accuracyCell = document.getElementById(`accuracy_${pred.game_id}`);
    if (accuracyCell) {
        if (pred.is_correct === true) {
            accuracyCell.innerHTML = '<span class="badge bg-success"><i class="fas fa-check"></i> 正確</span>';
        } else if (pred.is_correct === false) {
            accuracyCell.innerHTML = '<span class="badge bg-danger"><i class="fas fa-times"></i> 錯誤</span>';
        } else {
            accuracyCell.innerHTML = '<span class="text-muted">待驗證</span>';
        }
    }
}

// 顯示錯誤
function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorAlert').style.display = 'block';
}
</script>
{% endblock %}
