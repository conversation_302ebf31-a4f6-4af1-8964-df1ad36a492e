{% extends "base.html" %}

{% block title %}統一博彩預測 - 分析報告{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-chart-line"></i> 統一博彩預測分析報告
            </h1>
        </div>
    </div>

    <!-- 性能概覽 -->
    {% if analytics and analytics.success and analytics.analytics %}
    {% set perf = analytics.analytics.overall_performance %}
    {% set over_under = analytics.analytics.over_under_performance %}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ perf.total_predictions or 0 }}</h4>
                            <p class="mb-0">總預測數</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-bar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ perf.accuracy_rate or 0 }}%</h4>
                            <p class="mb-0">勝率</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-trophy fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ over_under.accuracy_rate or 0 }}%</h4>
                            <p class="mb-0">大小分準確率</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullseye fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ "%.2f"|format(perf.average_score_difference or 0) }}</h4>
                            <p class="mb-0">平均分差</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-percentage fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                {% if analytics.message %}
                {{ analytics.message }}
                {% elif error_message %}
                {{ error_message }}
                {% else %}
                暫無分析數據可顯示
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 詳細分析 -->
    {% if analytics and analytics.success and analytics.analytics %}
    <div class="row">
        <!-- 預測性能統計 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 預測性能統計</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h6>勝負預測</h6>
                            <p class="mb-1">準確率: <strong>{{ analytics.analytics.overall_performance.accuracy_rate }}%</strong></p>
                            <p class="mb-1">正確預測: {{ analytics.analytics.overall_performance.correct_predictions }}</p>
                            <p class="mb-0">總預測數: {{ analytics.analytics.overall_performance.total_predictions }}</p>
                        </div>
                        <div class="col-6">
                            <h6>大小分預測</h6>
                            <p class="mb-1">準確率: <strong>{{ analytics.analytics.over_under_performance.accuracy_rate }}%</strong></p>
                            <p class="mb-1">正確預測: {{ analytics.analytics.over_under_performance.correct_predictions }}</p>
                            <p class="mb-0">總預測數: {{ analytics.analytics.over_under_performance.total_predictions }}</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6>分差分析</h6>
                            <p class="mb-0">平均分差: <strong>{{ "%.2f"|format(analytics.analytics.overall_performance.average_score_difference) }}</strong> 分</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析期間信息 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar"></i> 分析期間</h5>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>開始日期:</strong> {{ analytics.analytics.analysis_period.start_date }}</p>
                    <p class="mb-2"><strong>結束日期:</strong> {{ analytics.analytics.analysis_period.end_date }}</p>
                    <p class="mb-2"><strong>分析天數:</strong> {{ analytics.analytics.analysis_period.days_analyzed }} 天</p>
                    <p class="mb-0"><strong>分析時間:</strong> {{ analytics.analysis_time[:19] if analytics.analysis_time else 'N/A' }}</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 改進建議和系統狀態 -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> 系統建議與狀態</h5>
                </div>
                <div class="card-body">
                    {% if analytics and analytics.success and analytics.analytics %}
                    {% set perf = analytics.analytics.overall_performance %}
                    {% set over_under = analytics.analytics.over_under_performance %}

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> 基於當前數據的建議：</h6>
                        <ul class="mb-0">
                            {% if perf.accuracy_rate < 50 %}
                            <li><strong>勝負預測準確率偏低 ({{ perf.accuracy_rate }}%)</strong> - 建議檢查模型參數或增加訓練數據</li>
                            {% elif perf.accuracy_rate >= 60 %}
                            <li><strong>勝負預測表現良好 ({{ perf.accuracy_rate }}%)</strong> - 繼續保持當前策略</li>
                            {% else %}
                            <li><strong>勝負預測表現中等 ({{ perf.accuracy_rate }}%)</strong> - 可考慮優化特徵工程</li>
                            {% endif %}

                            {% if over_under.accuracy_rate < 50 %}
                            <li><strong>大小分預測需要改進 ({{ over_under.accuracy_rate }}%)</strong> - 建議分析總分預測邏輯</li>
                            {% elif over_under.accuracy_rate >= 55 %}
                            <li><strong>大小分預測表現優秀 ({{ over_under.accuracy_rate }}%)</strong> - 可作為主要投注策略</li>
                            {% else %}
                            <li><strong>大小分預測表現穩定 ({{ over_under.accuracy_rate }}%)</strong> - 持續監控表現</li>
                            {% endif %}

                            {% if perf.average_score_difference > 3.0 %}
                            <li><strong>平均分差較大 ({{ "%.1f"|format(perf.average_score_difference) }})</strong> - 建議調整比分預測模型</li>
                            {% else %}
                            <li><strong>比分預測精度良好 (平均分差: {{ "%.1f"|format(perf.average_score_difference) }})</strong></li>
                            {% endif %}

                            <li>建議定期更新模型訓練數據以保持預測準確性</li>
                            <li>持續監控不同球隊和投手的預測表現差異</li>
                        </ul>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6>數據完整性</h6>
                                {% if perf.total_predictions >= 20 %}
                                <span class="badge badge-success badge-lg">良好</span>
                                <p class="small text-muted mt-1">{{ perf.total_predictions }} 筆預測</p>
                                {% elif perf.total_predictions >= 10 %}
                                <span class="badge badge-warning badge-lg">中等</span>
                                <p class="small text-muted mt-1">{{ perf.total_predictions }} 筆預測</p>
                                {% else %}
                                <span class="badge badge-danger badge-lg">不足</span>
                                <p class="small text-muted mt-1">{{ perf.total_predictions }} 筆預測</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6>預測質量</h6>
                                {% if perf.accuracy_rate >= 55 %}
                                <span class="badge badge-success badge-lg">優秀</span>
                                {% elif perf.accuracy_rate >= 50 %}
                                <span class="badge badge-warning badge-lg">良好</span>
                                {% else %}
                                <span class="badge badge-danger badge-lg">需改進</span>
                                {% endif %}
                                <p class="small text-muted mt-1">{{ perf.accuracy_rate }}% 準確率</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6>系統狀態</h6>
                                <span class="badge badge-success badge-lg">正常運行</span>
                                <p class="small text-muted mt-1">{{ days_back }} 天分析</p>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <h6>系統建議：</h6>
                        <ul class="mb-0">
                            <li>持續收集更多預測數據以提高分析準確性</li>
                            <li>確保預測結果驗證功能正常運行</li>
                            <li>定期檢視和調整預測模型參數</li>
                            <li>監控系統日誌以發現潛在問題</li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 返回按鈕 -->
    <div class="row">
        <div class="col-12">
            <a href="{{ url_for('unified_predictions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回統一預測
            </a>
            <a href="{{ url_for('unified_predictions.analytics_page', days=7) }}" class="btn btn-outline-primary ml-2">
                <i class="fas fa-calendar-week"></i> 7天分析
            </a>
            <a href="{{ url_for('unified_predictions.analytics_page', days=30) }}" class="btn btn-outline-primary ml-2">
                <i class="fas fa-calendar-alt"></i> 30天分析
            </a>
            <a href="{{ url_for('unified_predictions.analytics_page', days=90) }}" class="btn btn-outline-primary ml-2">
                <i class="fas fa-calendar"></i> 90天分析
            </a>
        </div>
    </div>
</div>

<script>
// 頁面載入完成後的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 可以在這裡添加任何需要的JavaScript初始化代碼
    console.log('統一預測分析頁面載入完成');
});
</script>
{% endblock %}
