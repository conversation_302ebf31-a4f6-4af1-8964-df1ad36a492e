<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>目標性博彩預測 - MLB預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .prediction-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .prediction-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .real-odds-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        .simulated-odds-badge {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        .quality-grade-a { border-left-color: #28a745; }
        .quality-grade-b { border-left-color: #17a2b8; }
        .quality-grade-c { border-left-color: #ffc107; }
        .quality-grade-d { border-left-color: #dc3545; }
        .recommendation-over { background-color: #d4edda; border-color: #c3e6cb; }
        .recommendation-under { background-color: #f8d7da; border-color: #f5c6cb; }
        .recommendation-home { background-color: #cce5ff; border-color: #b3d9ff; }
        .recommendation-away { background-color: #fff3cd; border-color: #ffeaa7; }
        .odds-display {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        .probability-bar {
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
        }
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body>
    {% extends "base.html" %}
    
    {% block content %}
    <div class="container-fluid mt-4">
        <!-- 頁面標題 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-0">
                            <i class="fas fa-bullseye text-primary"></i>
                            目標性博彩預測
                        </h1>
                        <p class="text-muted mb-0">基於真實博彩盤口的精準預測</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="refreshPredictions()">
                            <i class="fas fa-sync-alt"></i> 刷新預測
                        </button>
                        <button class="btn btn-outline-secondary" onclick="showWeeklyTargets()">
                            <i class="fas fa-calendar-week"></i> 週預測目標
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 預測摘要 -->
        {% if prediction_data and prediction_data.summary %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card summary-card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <h3 class="mb-0">{{ prediction_data.total_predictions }}</h3>
                                <small>總預測數</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="mb-0">{{ prediction_data.real_odds_predictions }}</h3>
                                <small>真實盤口預測</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="mb-0">{{ prediction_data.summary.recommendations.over_under.over_count + prediction_data.summary.recommendations.over_under.under_count }}</h3>
                                <small>大小分推薦</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="mb-0">{{ prediction_data.summary.recommendations.run_line.home_cover_count + prediction_data.summary.recommendations.run_line.away_cover_count }}</h3>
                                <small>讓分盤推薦</small>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-center">
                                <span class="badge bg-light text-dark">
                                    真實盤口覆蓋率: {{ prediction_data.summary.real_odds_coverage }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 預測結果 -->
        {% if prediction_data and prediction_data.predictions %}
        <div class="row">
            {% for prediction in prediction_data.predictions %}
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card prediction-card quality-grade-{{ prediction.prediction_quality.grade.split('級')[0].lower() }}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-baseball-ball text-primary"></i>
                            {{ prediction.teams.away }} @ {{ prediction.teams.home }}
                        </h6>
                        <span class="badge {{ 'real-odds-badge' if prediction.has_real_odds else 'simulated-odds-badge' }}">
                            {{ prediction.odds_source }}
                        </span>
                    </div>
                    
                    <div class="card-body">
                        <!-- 預測質量 -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">預測質量</small>
                                <span class="badge bg-secondary">{{ prediction.prediction_quality.grade }}</span>
                            </div>
                            <div class="progress mt-1" style="height: 6px;">
                                <div class="progress-bar" style="width: {{ prediction.prediction_quality.score }}%"></div>
                            </div>
                        </div>

                        <!-- 大小分預測 -->
                        {% if prediction.over_under %}
                        <div class="mb-3">
                            <h6 class="text-primary">
                                <i class="fas fa-chart-line"></i> 大小分預測
                            </h6>
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="odds-display">{{ prediction.over_under.total_line }}</div>
                                        <small class="text-muted">盤口</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="odds-display">{{ "%.1f"|format(prediction.over_under.expected_runs.total) }}</div>
                                        <small class="text-muted">預期得分</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 大小分概率 -->
                            <div class="mt-2">
                                <div class="d-flex justify-content-between">
                                    <small>大分: {{ "%.1f"|format(prediction.over_under.over_probability * 100) }}%</small>
                                    <small>小分: {{ "%.1f"|format((1 - prediction.over_under.over_probability) * 100) }}%</small>
                                </div>
                                <div class="probability-bar bg-light">
                                    <div class="bg-success h-100" style="width: {{ prediction.over_under.over_probability * 100 }}%"></div>
                                </div>
                            </div>
                            
                            <!-- 大小分推薦 -->
                            {% if prediction.over_under.recommendation %}
                            <div class="mt-2">
                                <div class="alert alert-sm py-1 px-2 mb-0 {{ 'recommendation-over' if '大分' in prediction.over_under.recommendation else 'recommendation-under' }}">
                                    <small><i class="fas fa-lightbulb"></i> {{ prediction.over_under.recommendation }}</small>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- 讓分盤預測 -->
                        {% if prediction.run_line %}
                        <div class="mb-3">
                            <h6 class="text-info">
                                <i class="fas fa-exchange-alt"></i> 讓分盤預測
                            </h6>
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="odds-display">{{ prediction.run_line.run_line_data.home_line }}</div>
                                        <small class="text-muted">主隊讓分</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="odds-display">{{ prediction.run_line.run_line_data.away_line }}</div>
                                        <small class="text-muted">客隊讓分</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 讓分盤推薦 -->
                            {% if prediction.run_line.prediction and prediction.run_line.prediction.recommendation %}
                            <div class="mt-2">
                                <div class="alert alert-sm py-1 px-2 mb-0 {{ 'recommendation-home' if '主隊' in prediction.run_line.prediction.recommendation else 'recommendation-away' }}">
                                    <small><i class="fas fa-target"></i> {{ prediction.run_line.prediction.recommendation }}</small>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- 預測因素 -->
                        <div class="mt-3">
                            <small class="text-muted">預測因素:</small>
                            <ul class="list-unstyled mt-1">
                                {% for factor in prediction.prediction_quality.factors %}
                                <li><small><i class="fas fa-check-circle text-success"></i> {{ factor }}</small></li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> {{ prediction.date }}
                            </small>
                            <button class="btn btn-sm btn-outline-primary" onclick="showGameDetails('{{ prediction.game_id }}')">
                                <i class="fas fa-info-circle"></i> 詳情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <!-- 無預測數據 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h4>暫無預測數據</h4>
                        <p class="text-muted">
                            {% if error_message %}
                            {{ error_message }}
                            {% else %}
                            今日沒有可預測的比賽，或者博彩盤口尚未公布
                            {% endif %}
                        </p>
                        <button class="btn btn-primary" onclick="refreshPredictions()">
                            <i class="fas fa-sync-alt"></i> 重新載入
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 推薦摘要 -->
        {% if prediction_data and prediction_data.summary %}
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-line text-success"></i> 大小分推薦摘要</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 text-center">
                                <h4 class="text-success">{{ prediction_data.summary.recommendations.over_under.over_count }}</h4>
                                <small>推薦大分</small>
                            </div>
                            <div class="col-6 text-center">
                                <h4 class="text-danger">{{ prediction_data.summary.recommendations.over_under.under_count }}</h4>
                                <small>推薦小分</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-exchange-alt text-info"></i> 讓分盤推薦摘要</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 text-center">
                                <h4 class="text-primary">{{ prediction_data.summary.recommendations.run_line.home_cover_count }}</h4>
                                <small>推薦主隊</small>
                            </div>
                            <div class="col-6 text-center">
                                <h4 class="text-warning">{{ prediction_data.summary.recommendations.run_line.away_cover_count }}</h4>
                                <small>推薦客隊</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshPredictions() {
            window.location.reload();
        }
        
        function showWeeklyTargets() {
            window.open('/targeted_betting/weekly_targets', '_blank');
        }
        
        function showGameDetails(gameId) {
            window.open(`/games/${gameId}`, '_blank');
        }
        
        // 自動刷新 (每5分鐘)
        setInterval(function() {
            const lastUpdate = new Date(document.querySelector('meta[name="last-update"]')?.content || Date.now());
            const now = new Date();
            const diffMinutes = (now - lastUpdate) / (1000 * 60);
            
            if (diffMinutes > 5) {
                console.log('自動刷新預測數據...');
                refreshPredictions();
            }
        }, 300000); // 5分鐘
    </script>
    {% endblock %}
</body>
</html>
