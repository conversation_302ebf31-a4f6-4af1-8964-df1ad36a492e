{% extends "base.html" %}

{% block title %}比賽分析 - {{ away_team.team_name if away_team else game.away_team }} @ {{ home_team.team_name if home_team else game.home_team }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 比賽基本信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-chart-line"></i> 比賽分析</h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-5">
                            <h3>{{ away_team.team_name if away_team else game.away_team }}</h3>
                            <small class="text-muted">客隊</small>
                        </div>
                        <div class="col-2 d-flex align-items-center justify-content-center">
                            <span class="text-muted fs-3">VS</span>
                        </div>
                        <div class="col-5">
                            <h3>{{ home_team.team_name if home_team else game.home_team }}</h3>
                            <small class="text-muted">主隊</small>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <p class="mb-1"><strong>比賽日期:</strong> {{ game.date.strftime('%Y年%m月%d日') }}</p>
                        <p class="mb-1"><strong>比賽狀態:</strong> 
                            <span class="badge {% if game.game_status == 'completed' %}bg-success{% elif game.game_status == 'scheduled' %}bg-primary{% else %}bg-warning{% endif %}">
                                {% if game.game_status == 'completed' %}已完成{% elif game.game_status == 'scheduled' %}已排程{% else %}{{ game.game_status }}{% endif %}
                            </span>
                        </p>
                        {% if game.venue %}
                        <p class="mb-0"><strong>比賽場地:</strong> {{ game.venue }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析結果 -->
    <div class="row">
        <!-- 主隊優勢 -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-home"></i> {{ home_team.team_name if home_team else game.home_team }} 優勢</h5>
                </div>
                <div class="card-body">
                    {% if analysis.home_advantages %}
                        <ul class="list-unstyled">
                            {% for advantage in analysis.home_advantages %}
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                {{ advantage }}
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted">暫無明顯優勢</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 客隊優勢 -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-plane"></i> {{ away_team.team_name if away_team else game.away_team }} 優勢</h5>
                </div>
                <div class="card-body">
                    {% if analysis.away_advantages %}
                        <ul class="list-unstyled">
                            {% for advantage in analysis.away_advantages %}
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-primary me-2"></i>
                                {{ advantage }}
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted">暫無明顯優勢</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 關鍵因素詳細分析 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-key"></i> 關鍵分析因素</h5>
                </div>
                <div class="card-body">
                    {% if analysis.key_factors %}
                        <div class="accordion" id="keyFactorsAccordion">
                            {% for factor_name, factor_data in analysis.key_factors.items() %}
                            <div class="accordion-item mb-3">
                                <h2 class="accordion-header" id="heading{{ loop.index }}">
                                    <button class="accordion-button {% if not loop.first %}collapsed{% endif %}" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse{{ loop.index }}"
                                            aria-expanded="{% if loop.first %}true{% else %}false{% endif %}"
                                            aria-controls="collapse{{ loop.index }}">
                                        <i class="{{ factor_data.icon }} me-2"></i>
                                        <span class="badge bg-primary me-2">{{ loop.index }}</span>
                                        {{ factor_data.title }}
                                    </button>
                                </h2>
                                <div id="collapse{{ loop.index }}"
                                     class="accordion-collapse collapse {% if loop.first %}show{% endif %}"
                                     aria-labelledby="heading{{ loop.index }}"
                                     data-bs-parent="#keyFactorsAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6><i class="fas fa-list"></i> 分析要點</h6>
                                                <ul class="list-unstyled">
                                                    {% for detail in factor_data.details %}
                                                    <li class="mb-1">
                                                        <i class="fas fa-check-circle text-success me-2"></i>
                                                        {{ detail }}
                                                    </li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <h6><i class="fas fa-chart-bar"></i> 詳細分析</h6>
                                                {% if factor_data.analysis %}
                                                    {% if factor_name == '投手對戰' %}
                                                        <div class="mb-2">
                                                            <strong>對戰優勢:</strong> {{ factor_data.analysis.matchup_advantage }}
                                                        </div>
                                                        <div class="small text-muted">
                                                            {% for point in factor_data.analysis.key_points %}
                                                            <div>• {{ point }}</div>
                                                            {% endfor %}
                                                        </div>
                                                    {% elif factor_name == '打線深度' %}
                                                        <div class="mb-2">
                                                            <strong>打擊優勢:</strong> {{ factor_data.analysis.advantage }}
                                                        </div>
                                                        <div class="small text-muted">
                                                            {% for point in factor_data.analysis.key_points %}
                                                            <div>• {{ point }}</div>
                                                            {% endfor %}
                                                        </div>
                                                    {% elif factor_name == '牛棚實力' %}
                                                        <div class="mb-2">
                                                            <strong>牛棚對比:</strong> {{ factor_data.analysis.advantage }}
                                                        </div>
                                                        <div class="small text-muted">
                                                            {% for point in factor_data.analysis.key_points %}
                                                            <div>• {{ point }}</div>
                                                            {% endfor %}
                                                        </div>
                                                    {% elif factor_name == '傷兵情況' %}
                                                        <div class="mb-2">
                                                            <strong>傷兵影響:</strong> {{ factor_data.analysis.advantage }}
                                                        </div>
                                                        <div class="small text-muted">
                                                            {% for point in factor_data.analysis.key_points %}
                                                            <div>• {{ point }}</div>
                                                            {% endfor %}
                                                        </div>
                                                    {% elif factor_name == '天氣條件' %}
                                                        <div class="mb-2">
                                                            <strong>天氣影響:</strong> {{ factor_data.analysis.impact }}
                                                        </div>
                                                        <div class="small text-muted">
                                                            {% for point in factor_data.analysis.key_points %}
                                                            <div>• {{ point }}</div>
                                                            {% endfor %}
                                                        </div>
                                                    {% endif %}
                                                {% else %}
                                                    <p class="text-muted small">詳細分析數據準備中...</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">暫無關鍵因素分析</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 預測因素 -->
    {% if analysis.prediction_factors %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-crystal-ball"></i> 預測分析</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for key, value in analysis.prediction_factors.items() %}
                        <div class="col-md-6 mb-3">
                            <strong>{{ key }}:</strong> {{ value }}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 操作按鈕 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <a href="/games/{{ game.game_id }}" class="btn btn-primary me-2">
                        <i class="fas fa-arrow-left"></i> 返回比賽詳情
                    </a>
                    {% if game.game_status == 'scheduled' %}
                    <a href="/predictions/{{ game.game_id }}" class="btn btn-success me-2">
                        <i class="fas fa-crystal-ball"></i> 查看預測
                    </a>
                    {% endif %}
                    <a href="/games/" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> 比賽列表
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.875em;
}

.list-unstyled li {
    padding: 0.25rem 0;
}

.text-success {
    color: #198754 !important;
}

.text-primary {
    color: #0d6efd !important;
}

.accordion-item {
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.375rem;
}

.accordion-button {
    background-color: #f8f9fa;
    border: none;
    font-weight: 500;
}

.accordion-button:not(.collapsed) {
    background-color: #e7f3ff;
    color: #0d6efd;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.accordion-body {
    background-color: #ffffff;
}

.accordion-body h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.accordion-body .small {
    font-size: 0.875rem;
    line-height: 1.4;
}
</style>
{% endblock %}
