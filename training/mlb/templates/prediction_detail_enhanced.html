{% extends "base.html" %}

{% block title %}預測詳情 - {{ prediction.away_team }} @ {{ prediction.home_team }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line"></i>
                        預測詳情 - {{ prediction.away_team }} @ {{ prediction.home_team }}
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-info">{{ prediction.game_date }}</span>
                        <span class="badge badge-secondary">{{ prediction.model_version }}</span>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 預測結果概覽 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-box bg-gradient-primary">
                                <span class="info-box-icon"><i class="fas fa-baseball-ball"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">預測比分</span>
                                    <span class="info-box-number">
                                        {{ "%.1f"|format(prediction.predicted_away_score) }} - {{ "%.1f"|format(prediction.predicted_home_score) }}
                                    </span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: {{ (prediction.confidence * 100)|round }}%"></div>
                                    </div>
                                    <span class="progress-description">信心度: {{ "%.1f"|format(prediction.confidence * 100) }}%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-box bg-gradient-success">
                                <span class="info-box-icon"><i class="fas fa-trophy"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">實際比分</span>
                                    <span class="info-box-number">
                                        {% if prediction.game.away_score is not none and prediction.game.home_score is not none %}
                                            {{ prediction.game.away_score }} - {{ prediction.game.home_score }}
                                        {% else %}
                                            待定
                                        {% endif %}
                                    </span>
                                    <span class="progress-description">
                                        {% if prediction.game.game_status %}
                                            狀態: {{ prediction.game.game_status }}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 投手信息狀態 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h4><i class="fas fa-user-tie"></i> 投手信息狀態</h4>
                        </div>
                        
                        <!-- 主隊投手 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{{ prediction.home_team }} 主場投手</h5>
                                </div>
                                <div class="card-body">
                                    {% if features and features.get('home_probable_starter') %}
                                        <div class="pitcher-info">
                                            <h6><i class="fas fa-user"></i> {{ features.home_probable_starter }}</h6>
                                            
                                            <!-- 投手狀態標示 -->
                                            <div class="status-badges mb-2">
                                                {% set pitcher_status = features.get('home_pitcher_status', 'unknown') %}
                                                {% if pitcher_status == 'found' %}
                                                    <span class="badge badge-success"><i class="fas fa-check"></i> 找到投手</span>
                                                {% elif pitcher_status == 'not_found' %}
                                                    <span class="badge badge-danger"><i class="fas fa-times"></i> 找不到投手</span>
                                                {% else %}
                                                    <span class="badge badge-warning"><i class="fas fa-question"></i> 狀態未知</span>
                                                {% endif %}
                                                
                                                {% set data_status = features.get('home_pitcher_data_status', 'unknown') %}
                                                {% if data_status == 'found' %}
                                                    <span class="badge badge-success"><i class="fas fa-database"></i> 有統計數據</span>
                                                {% elif data_status == 'missing_stats' %}
                                                    <span class="badge badge-warning"><i class="fas fa-exclamation-triangle"></i> 找不到投手數據</span>
                                                {% elif data_status == 'error' %}
                                                    <span class="badge badge-danger"><i class="fas fa-bug"></i> 數據錯誤</span>
                                                {% else %}
                                                    <span class="badge badge-secondary"><i class="fas fa-question"></i> 數據狀態未知</span>
                                                {% endif %}
                                            </div>
                                            
                                            <!-- 數據來源 -->
                                            <div class="data-source mb-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle"></i>
                                                    投手來源: {{ features.get('home_pitcher_source', '未知') }} | 
                                                    數據來源: {{ features.get('home_pitcher_data_source', '未知') }}
                                                </small>
                                            </div>
                                            
                                            <!-- 投手統計 -->
                                            {% if features.get('home_pitcher_era') %}
                                            <div class="pitcher-stats">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <strong>ERA:</strong> {{ "%.2f"|format(features.home_pitcher_era) }}
                                                    </div>
                                                    <div class="col-6">
                                                        <strong>WHIP:</strong> {{ "%.2f"|format(features.get('home_pitcher_whip', 0)) }}
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <strong>❌ 找不到投手信息</strong>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- 客隊投手 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{{ prediction.away_team }} 客場投手</h5>
                                </div>
                                <div class="card-body">
                                    {% if features and features.get('away_probable_starter') %}
                                        <div class="pitcher-info">
                                            <h6><i class="fas fa-user"></i> {{ features.away_probable_starter }}</h6>
                                            
                                            <!-- 投手狀態標示 -->
                                            <div class="status-badges mb-2">
                                                {% set pitcher_status = features.get('away_pitcher_status', 'unknown') %}
                                                {% if pitcher_status == 'found' %}
                                                    <span class="badge badge-success"><i class="fas fa-check"></i> 找到投手</span>
                                                {% elif pitcher_status == 'not_found' %}
                                                    <span class="badge badge-danger"><i class="fas fa-times"></i> 找不到投手</span>
                                                {% else %}
                                                    <span class="badge badge-warning"><i class="fas fa-question"></i> 狀態未知</span>
                                                {% endif %}
                                                
                                                {% set data_status = features.get('away_pitcher_data_status', 'unknown') %}
                                                {% if data_status == 'found' %}
                                                    <span class="badge badge-success"><i class="fas fa-database"></i> 有統計數據</span>
                                                {% elif data_status == 'missing_stats' %}
                                                    <span class="badge badge-warning"><i class="fas fa-exclamation-triangle"></i> 找不到投手數據</span>
                                                {% elif data_status == 'error' %}
                                                    <span class="badge badge-danger"><i class="fas fa-bug"></i> 數據錯誤</span>
                                                {% else %}
                                                    <span class="badge badge-secondary"><i class="fas fa-question"></i> 數據狀態未知</span>
                                                {% endif %}
                                            </div>
                                            
                                            <!-- 數據來源 -->
                                            <div class="data-source mb-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle"></i>
                                                    投手來源: {{ features.get('away_pitcher_source', '未知') }} | 
                                                    數據來源: {{ features.get('away_pitcher_data_source', '未知') }}
                                                </small>
                                            </div>
                                            
                                            <!-- 投手統計 -->
                                            {% if features.get('away_pitcher_era') %}
                                            <div class="pitcher-stats">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <strong>ERA:</strong> {{ "%.2f"|format(features.away_pitcher_era) }}
                                                    </div>
                                                    <div class="col-6">
                                                        <strong>WHIP:</strong> {{ "%.2f"|format(features.get('away_pitcher_whip', 0)) }}
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <strong>❌ 找不到投手信息</strong>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 預測準確性分析 -->
                    {% if prediction.game.away_score is not none and prediction.game.home_score is not none %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h4><i class="fas fa-chart-bar"></i> 預測準確性分析</h4>
                            <div class="card">
                                <div class="card-body">
                                    {% set actual_total = prediction.game.away_score + prediction.game.home_score %}
                                    {% set predicted_total = prediction.predicted_away_score + prediction.predicted_home_score %}
                                    {% set total_diff = (predicted_total - actual_total)|abs %}
                                    
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-info"><i class="fas fa-calculator"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">總分差異</span>
                                                    <span class="info-box-number">{{ "%.1f"|format(total_diff) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-warning"><i class="fas fa-home"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">主隊分差</span>
                                                    <span class="info-box-number">{{ "%.1f"|format((prediction.predicted_home_score - prediction.game.home_score)|abs) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-success"><i class="fas fa-plane"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">客隊分差</span>
                                                    <span class="info-box-number">{{ "%.1f"|format((prediction.predicted_away_score - prediction.game.away_score)|abs) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 返回按鈕 -->
                    <div class="row">
                        <div class="col-12">
                            <a href="{{ url_for('predictions.predictions') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回預測列表
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.status-badges .badge {
    margin-right: 5px;
    margin-bottom: 5px;
}

.pitcher-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}

.data-source {
    border-top: 1px solid #dee2e6;
    padding-top: 10px;
}

.pitcher-stats {
    background-color: #ffffff;
    padding: 10px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}
</style>
{% endblock %}
