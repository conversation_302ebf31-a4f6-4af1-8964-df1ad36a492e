{% extends "base.html" %}

{% block title %}預測準確率分析{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-chart-line"></i> 預測準確率分析
            </h1>
        </div>
    </div>

    <!-- 總體統計 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">總預測數</h4>
                            <h2>{{ stats.get('total_predictions', 0) }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">勝負準確率</h4>
                            <h2>{{ "%.1f%%" | format(stats.get('overall_accuracy', 0)) }}%</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullseye fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">總分誤差</h4>
                            <h2>{{ "%.2f" | format(stats.get('average_score_error', 0)) }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-bar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">平均信心度</h4>
                            <h2>{{ "%.1f" | format(stats.get('high_confidence_accuracy', 0)) }}%</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-heart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 詳細分析 -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-trophy"></i> 勝負預測分析</h5>
                </div>
                <div class="card-body">
                    {% if stats and stats.get('total_predictions', 0) > 0 %}
                        <div class="mb-3">
                            <label>主隊勝利預測準確率</label>
                            <div class="progress">
                                <div class="progress-bar bg-success" 
                                     style="width: {{ stats.get('overall_accuracy', 0) }}%">
                                    {{ "%.1f%%" | format(stats.get('overall_accuracy', 0)) }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-6">
                                <strong>正確預測:</strong> {{ stats.get('correct_predictions', 0) }}
                            </div>
                            <div class="col-6">
                                <strong>錯誤預測:</strong> {{ stats.get('total_predictions', 0) - stats.get('correct_predictions', 0) }}
                            </div>
                        </div>
                    {% else %}
                        <p class="text-muted">暫無足夠的預測數據進行分析</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> 總分預測分析</h5>
                </div>
                <div class="card-body">
                    {% if stats and stats.get('total_predictions', 0) > 0 %}
                        <div class="mb-3">
                            <label>平均絕對誤差 (MAE)</label>
                            <h4 class="text-info">{{ "%.2f" | format(stats.get('average_score_error', 0)) }} 分</h4>
                        </div>
                        
                        <div class="mb-3">
                            <label>誤差分布</label>
                            <div class="small">
                                <div>• 誤差 ≤ 1分: 約 {{ "%.0f%%" | format(30) }}</div>
                                <div>• 誤差 ≤ 2分: 約 {{ "%.0f%%" | format(60) }}</div>
                                <div>• 誤差 ≤ 3分: 約 {{ "%.0f%%" | format(80) }}</div>
                            </div>
                        </div>
                    {% else %}
                        <p class="text-muted">暫無足夠的預測數據進行分析</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 最近預測記錄 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> 最近預測記錄</h5>
                </div>
                <div class="card-body">
                    {% if recent_predictions %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>比賽</th>
                                        <th>預測結果</th>
                                        <th>實際結果</th>
                                        <th>主隊勝率</th>
                                        <th>總分預測</th>
                                        <th>信心度</th>
                                        <th>狀態</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prediction in recent_predictions %}
                                    <tr>
                                        <td>{{ prediction.game.date.strftime('%m-%d') }}</td>
                                        <td>
                                            <small>{{ prediction.game.away_team }} @ {{ prediction.game.home_team }}</small>
                                        </td>
                                        <td>
                                            {% if prediction.home_win_probability > 0.5 %}
                                                <span class="badge bg-primary">主隊勝</span>
                                            {% else %}
                                                <span class="badge bg-secondary">客隊勝</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if prediction.game.game_status == 'completed' %}
                                                {% if prediction.game.home_score is not none and prediction.game.away_score is not none and prediction.game.home_score > prediction.game.away_score %}
                                                    <span class="badge bg-success">主隊勝</span>
                                                {% else %}
                                                    <span class="badge bg-danger">客隊勝</span>
                                                {% endif %}
                                                <small>({{ prediction.game.away_score }}-{{ prediction.game.home_score }})</small>
                                            {% else %}
                                                <span class="badge bg-warning">未完成</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ "%.3f" | format(prediction.home_win_probability) }}</td>
                                        <td>{{ "%.1f" | format(prediction.predicted_total_runs) }}</td>
                                        <td>{{ "%.3f" | format(prediction.confidence) }}</td>
                                        <td>
                                            {% if prediction.game.game_status == 'completed' %}
                                                {% set predicted_home_win = prediction.home_win_probability > 0.5 %}
                                                {% set actual_home_win = prediction.game.home_score is not none and prediction.game.away_score is not none and prediction.game.home_score > prediction.game.away_score %}
                                                {% if predicted_home_win == actual_home_win %}
                                                    <i class="fas fa-check text-success"></i>
                                                {% else %}
                                                    <i class="fas fa-times text-danger"></i>
                                                {% endif %}
                                            {% else %}
                                                <i class="fas fa-clock text-warning"></i>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">暫無預測記錄</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 返回按鈕 -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('predictions.predictions_list') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回預測列表
            </a>
        </div>
    </div>
</div>
{% endblock %}
