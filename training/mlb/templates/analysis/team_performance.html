{% extends "base.html" %}

{% block title %}球隊表現分析{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users"></i> 球隊表現分析</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard.dashboard') }}">首頁</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('analysis.analysis_dashboard') }}">分析中心</a></li>
                        <li class="breadcrumb-item active">球隊表現</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    {% if team_stats %}
    <div class="row">
        <!-- 球隊表現排行榜 -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-trophy"></i> 球隊表現排行榜</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>球隊</th>
                                    <th>總比賽</th>
                                    <th>主場戰績</th>
                                    <th>客場戰績</th>
                                    <th>勝率</th>
                                    <th>平均得分</th>
                                    <th>平均失分</th>
                                    <th>得失分差</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for team in team_stats %}
                                <tr>
                                    <td>
                                        <span class="badge bg-{{ 'success' if loop.index <= 5 else 'warning' if loop.index <= 15 else 'secondary' }}">
                                            {{ loop.index }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>{{ team.team_code }}</strong>
                                        <br><small class="text-muted">{{ team.team_name }}</small>
                                    </td>
                                    <td>{{ team.total_games }}</td>
                                    <td>
                                        <span class="text-{{ 'success' if team.home_win_rate > 0.5 else 'danger' }}">
                                            {{ team.home_wins }}-{{ team.home_losses }}
                                        </span>
                                        <br><small class="text-muted">{{ "%.1f%%" | format(team.home_win_rate * 100) }}</small>
                                    </td>
                                    <td>
                                        <span class="text-{{ 'success' if team.away_win_rate > 0.5 else 'danger' }}">
                                            {{ team.away_wins }}-{{ team.away_losses }}
                                        </span>
                                        <br><small class="text-muted">{{ "%.1f%%" | format(team.away_win_rate * 100) }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if team.overall_win_rate > 0.6 else 'warning' if team.overall_win_rate > 0.4 else 'danger' }}">
                                            {{ "%.1f%%" | format(team.overall_win_rate * 100) }}
                                        </span>
                                    </td>
                                    <td>{{ "%.1f" | format(team.avg_runs_scored) }}</td>
                                    <td>{{ "%.1f" | format(team.avg_runs_allowed) }}</td>
                                    <td>
                                        <span class="text-{{ 'success' if team.run_differential > 0 else 'danger' }}">
                                            {{ "%+.1f" | format(team.run_differential) }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 主客場表現對比 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 主客場表現對比</h5>
                </div>
                <div class="card-body">
                    <canvas id="homeAwayChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- 得失分分佈 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-scatter"></i> 得失分分佈</h5>
                </div>
                <div class="card-body">
                    <canvas id="runDifferentialChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 聯盟排名分佈 -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 聯盟表現分佈</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>美國聯盟 (AL)</h6>
                            <div class="list-group">
                                {% for team in team_stats if team.league == 'American' %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>{{ team.team_code }}</span>
                                    <span class="badge bg-{{ 'success' if team.overall_win_rate > 0.5 else 'danger' }}">
                                        {{ "%.1f%%" | format(team.overall_win_rate * 100) }}
                                    </span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>國家聯盟 (NL)</h6>
                            <div class="list-group">
                                {% for team in team_stats if team.league == 'National' %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>{{ team.team_code }}</span>
                                    <span class="badge bg-{{ 'success' if team.overall_win_rate > 0.5 else 'danger' }}">
                                        {{ "%.1f%%" | format(team.overall_win_rate * 100) }}
                                    </span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>表現統計</h6>
                            <div class="card">
                                <div class="card-body">
                                    <p><strong>最佳球隊:</strong> {{ team_stats[0].team_code if team_stats else 'N/A' }}</p>
                                    <p><strong>平均勝率:</strong> {{ "%.1f%%" | format((team_stats | sum(attribute='overall_win_rate') / team_stats | length) * 100) if team_stats else 'N/A' }}</p>
                                    <p><strong>競爭激烈度:</strong> 
                                        {% set win_rates = team_stats | map(attribute='overall_win_rate') | list %}
                                        {% if win_rates %}
                                            {{ "%.1f%%" | format(((win_rates | max) - (win_rates | min)) * 100) }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> 暫無球隊表現數據
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if team_stats %}
// 準備數據
const teamNames = {{ team_stats | map(attribute='team_code') | list | tojson }};
const homeWinRates = [];
const awayWinRates = [];
{% for team in team_stats %}
homeWinRates.push({{ team.home_win_rate * 100 }});
awayWinRates.push({{ team.away_win_rate * 100 }});
{% endfor %}
const runsScored = {{ team_stats | map(attribute='avg_runs_scored') | list | tojson }};
const runsAllowed = {{ team_stats | map(attribute='avg_runs_allowed') | list | tojson }};

// 主客場表現對比圖表
const homeAwayCtx = document.getElementById('homeAwayChart').getContext('2d');
new Chart(homeAwayCtx, {
    type: 'bar',
    data: {
        labels: teamNames,
        datasets: [{
            label: '主場勝率 (%)',
            data: homeWinRates,
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }, {
            label: '客場勝率 (%)',
            data: awayWinRates,
            backgroundColor: 'rgba(255, 99, 132, 0.6)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

// 得失分分佈圖表
const runDifferentialCtx = document.getElementById('runDifferentialChart').getContext('2d');
new Chart(runDifferentialCtx, {
    type: 'scatter',
    data: {
        datasets: [{
            label: '球隊表現',
            data: teamNames.map((team, index) => ({
                x: runsScored[index],
                y: runsAllowed[index],
                label: team
            })),
            backgroundColor: 'rgba(75, 192, 192, 0.6)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            x: {
                title: {
                    display: true,
                    text: '平均得分'
                }
            },
            y: {
                title: {
                    display: true,
                    text: '平均失分'
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.raw.label + ': 得分 ' + context.raw.x.toFixed(1) + ', 失分 ' + context.raw.y.toFixed(1);
                    }
                }
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}
