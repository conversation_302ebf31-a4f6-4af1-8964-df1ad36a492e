{% extends "base.html" %}

{% block title %}預測趨勢分析{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line"></i> 預測趨勢分析</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard.dashboard') }}">首頁</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('analysis.analysis_dashboard') }}">分析中心</a></li>
                        <li class="breadcrumb-item active">預測趨勢</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    {% if daily_stats %}
    <div class="row">
        <!-- 每日預測數量趨勢 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 每日預測數量</h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyPredictionsChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- 每日準確率趨勢 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> 每日準確率</h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyAccuracyChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 信心度分佈 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 信心度分佈</h5>
                </div>
                <div class="card-body">
                    <canvas id="confidenceDistributionChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- 每日統計表格 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> 每日統計詳情</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>預測數</th>
                                    <th>準確率</th>
                                    <th>平均信心度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for date, stats in daily_stats.items() %}
                                <tr>
                                    <td>{{ date }}</td>
                                    <td>{{ stats.total }}</td>
                                    <td>
                                        {% if stats.accuracy is not none %}
                                            <span class="badge bg-{{ 'success' if stats.accuracy > 0.6 else 'warning' if stats.accuracy > 0.4 else 'danger' }}">
                                                {{ "%.1f%%" | format(stats.accuracy * 100) }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if stats.avg_confidence is not none %}
                                            {{ "%.2f" | format(stats.avg_confidence) }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> 暫無預測趨勢數據
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if daily_stats %}
// 準備數據
const dates = [];
const predictions = [];
const accuracies = [];
const confidences = [];

{% for date, stats in daily_stats.items() %}
dates.push('{{ date }}');
predictions.push({{ stats.total }});
accuracies.push({{ stats.accuracy * 100 if stats.accuracy is not none else 'null' }});
confidences.push({{ stats.avg_confidence if stats.avg_confidence is not none else 'null' }});
{% endfor %}

// 每日預測數量圖表
const dailyPredictionsCtx = document.getElementById('dailyPredictionsChart').getContext('2d');
new Chart(dailyPredictionsCtx, {
    type: 'bar',
    data: {
        labels: dates,
        datasets: [{
            label: '預測數量',
            data: predictions,
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// 每日準確率圖表
const dailyAccuracyCtx = document.getElementById('dailyAccuracyChart').getContext('2d');
new Chart(dailyAccuracyCtx, {
    type: 'line',
    data: {
        labels: dates,
        datasets: [{
            label: '準確率 (%)',
            data: accuracies,
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

// 信心度分佈圖表
const confidenceDistributionCtx = document.getElementById('confidenceDistributionChart').getContext('2d');
new Chart(confidenceDistributionCtx, {
    type: 'line',
    data: {
        labels: dates,
        datasets: [{
            label: '平均信心度',
            data: confidences,
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 1,
                ticks: {
                    callback: function(value) {
                        return (value * 100).toFixed(0) + '%';
                    }
                }
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}
