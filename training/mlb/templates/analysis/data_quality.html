{% extends "base.html" %}

{% block title %}數據質量分析{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-database"></i> 數據質量分析</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard.dashboard') }}">首頁</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('analysis.analysis_dashboard') }}">分析中心</a></li>
                        <li class="breadcrumb-item active">數據質量</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 數據完整性概覽 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 數據完整性概覽</h5>
                </div>
                <div class="card-body">
                    {% if data_quality %}
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">{{ "{:,}".format(data_quality.total_games) }}</h4>
                                <small class="text-muted">總比賽數</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success">{{ "{:,}".format(data_quality.completed_games) }}</h4>
                                <small class="text-muted">已完成比賽</small>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-info">{{ "{:,}".format(data_quality.total_predictions) }}</h4>
                                <small class="text-muted">總預測數</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-warning">{{ "{:,}".format(data_quality.total_boxscores) }}</h4>
                                <small class="text-muted">Box Score數</small>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 正在載入數據質量信息...
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 數據覆蓋率 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-percentage"></i> 數據覆蓋率</h5>
                </div>
                <div class="card-body">
                    {% if data_quality %}
                    <div class="mb-3">
                        <label>Box Score覆蓋率</label>
                        <div class="progress">
                            <div class="progress-bar bg-success" 
                                 style="width: {{ data_quality.boxscore_coverage }}%">
                                {{ "%.1f%%" | format(data_quality.boxscore_coverage) }}
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>預測覆蓋率</label>
                        <div class="progress">
                            <div class="progress-bar bg-info" 
                                 style="width: {{ data_quality.prediction_coverage }}%">
                                {{ "%.1f%%" | format(data_quality.prediction_coverage) }}
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>球員統計覆蓋率</label>
                        <div class="progress">
                            <div class="progress-bar bg-warning" 
                                 style="width: {{ data_quality.player_stats_coverage }}%">
                                {{ "%.1f%%" | format(data_quality.player_stats_coverage) }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 數據質量問題 -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> 數據質量問題</h5>
                </div>
                <div class="card-body">
                    {% if data_quality and data_quality.issues %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>問題類型</th>
                                    <th>影響範圍</th>
                                    <th>嚴重程度</th>
                                    <th>建議處理</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for issue in data_quality.issues %}
                                <tr>
                                    <td>{{ issue.type }}</td>
                                    <td>{{ issue.scope }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if issue.severity == 'high' else 'warning' if issue.severity == 'medium' else 'info' }}">
                                            {{ issue.severity }}
                                        </span>
                                    </td>
                                    <td>{{ issue.recommendation }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> 未發現明顯的數據質量問題
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 最近更新狀態 -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clock"></i> 最近更新狀態</h5>
                </div>
                <div class="card-body">
                    {% if data_quality and data_quality.recent_updates %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>數據類型</th>
                                    <th>最後更新時間</th>
                                    <th>更新狀態</th>
                                    <th>記錄數量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for update in data_quality.recent_updates %}
                                <tr>
                                    <td>{{ update.data_type }}</td>
                                    <td>{{ update.last_update }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if update.status == 'success' else 'danger' if update.status == 'failed' else 'warning' }}">
                                            {{ update.status }}
                                        </span>
                                    </td>
                                    <td>{{ "{:,}".format(update.record_count) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 暫無最近更新記錄
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
