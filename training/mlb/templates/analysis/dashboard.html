{% extends "base.html" %}

{% block title %}數據分析儀表板{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-chart-bar"></i> 數據分析儀表板
            </h1>
        </div>
    </div>

    <!-- 數據統計卡片 -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>{{ "{:,}".format(total_games) }}</h4>
                    <p class="mb-0">總比賽數</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>{{ total_teams }}</h4>
                    <p class="mb-0">球隊數</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4>{{ "{:,}".format(total_players) }}</h4>
                    <p class="mb-0">球員數</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4>{{ "{:,}".format(total_boxscores) }}</h4>
                    <p class="mb-0">Box Score</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h4>{{ "{:,}".format(total_predictions) }}</h4>
                    <p class="mb-0">預測數</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h4>{{ "%.1f%%" | format((accuracy_stats.home_win_accuracy or 0) * 100) }}</h4>
                    <p class="mb-0">預測準確率</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析功能導航 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools"></i> 分析工具</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('analysis.team_performance') }}" class="btn btn-outline-primary btn-block mb-2">
                                <i class="fas fa-users"></i> 球隊表現分析
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('analysis.prediction_trends') }}" class="btn btn-outline-success btn-block mb-2">
                                <i class="fas fa-chart-line"></i> 預測趨勢分析
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('analysis.data_quality') }}" class="btn btn-outline-info btn-block mb-2">
                                <i class="fas fa-database"></i> 數據質量分析
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('predictions.prediction_accuracy') }}" class="btn btn-outline-warning btn-block mb-2">
                                <i class="fas fa-bullseye"></i> 預測準確率
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近比賽和預測 -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-baseball-ball"></i> 最近比賽</h5>
                </div>
                <div class="card-body">
                    {% if recent_games %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>比賽</th>
                                        <th>比分</th>
                                        <th>狀態</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for game in recent_games %}
                                    <tr>
                                        <td>{{ game.date.strftime('%m-%d') }}</td>
                                        <td>{{ game.away_team }} @ {{ game.home_team }}</td>
                                        <td>
                                            {% if game.game_status == 'completed' %}
                                                {{ game.away_score if game.away_score is not none else 0 }}-{{ game.home_score if game.home_score is not none else 0 }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if game.game_status == 'completed' %}
                                                <span class="badge bg-success">已完成</span>
                                            {% elif game.game_status == 'scheduled' %}
                                                <span class="badge bg-primary">已排程</span>
                                            {% else %}
                                                <span class="badge bg-warning">{{ game.game_status }}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">暫無最近比賽數據</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-crystal-ball"></i> 最近預測</h5>
                </div>
                <div class="card-body">
                    {% if recent_predictions %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>比賽</th>
                                        <th>預測</th>
                                        <th>信心度</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prediction in recent_predictions %}
                                    <tr>
                                        <td>{{ prediction.game.date.strftime('%m-%d') }}</td>
                                        <td>{{ prediction.game.away_team }} @ {{ prediction.game.home_team }}</td>
                                        <td>
                                            {% if prediction.home_win_probability > 0.5 %}
                                                <span class="badge bg-primary">主隊勝</span>
                                            {% else %}
                                                <span class="badge bg-secondary">客隊勝</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ "%.3f" | format(prediction.confidence) }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">暫無預測數據</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
