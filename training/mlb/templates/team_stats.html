{% extends "base.html" %}

{% block title %}{{ team.team_name }} - 統計數據{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 頁面標題 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-chart-bar"></i> {{ team.team_name }} - 統計數據</h1>
                    <p class="text-muted mb-0">{{ team.league }} - {{ team.division }}</p>
                </div>
                <div>
                    <a href="{{ url_for('teams.team_detail', team_id=team.team_id) }}" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回球隊詳情
                    </a>
                </div>
            </div>

            {% if stats_by_season %}
            <!-- 賽季統計 -->
            {% for stat in stats_by_season %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-calendar"></i> {{ stat.season }} 賽季統計</h5>
                        </div>
                        <div class="card-body">
                            <!-- 基本戰績 -->
                            <div class="row mb-4">
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h4 class="text-success">{{ stat.wins or 0 }}</h4>
                                        <small class="text-muted">勝場</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h4 class="text-danger">{{ stat.losses or 0 }}</h4>
                                        <small class="text-muted">敗場</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h4 class="text-primary">{{ "%.3f"|format(stat.win_percentage or 0) }}</h4>
                                        <small class="text-muted">勝率</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h4>{{ stat.runs_scored or 0 }}</h4>
                                        <small class="text-muted">總得分</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h4>{{ stat.runs_allowed or 0 }}</h4>
                                        <small class="text-muted">總失分</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h4>{{ (stat.runs_scored or 0) - (stat.runs_allowed or 0) }}</h4>
                                        <small class="text-muted">得失分差</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 打擊統計 -->
                            <h6 class="mb-3"><i class="fas fa-baseball-ball"></i> 打擊統計</h6>
                            <div class="row mb-4">
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ "%.3f"|format(stat.batting_avg or 0) }}</h5>
                                        <small class="text-muted">打擊率</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ "%.3f"|format(stat.on_base_pct or 0) }}</h5>
                                        <small class="text-muted">上壘率</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ "%.3f"|format(stat.slugging_pct or 0) }}</h5>
                                        <small class="text-muted">長打率</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ stat.home_runs or 0 }}</h5>
                                        <small class="text-muted">全壘打</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ stat.stolen_bases or 0 }}</h5>
                                        <small class="text-muted">盜壘</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ stat.errors or 0 }}</h5>
                                        <small class="text-muted">失誤</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 投手統計 -->
                            <h6 class="mb-3"><i class="fas fa-hand-paper"></i> 投手統計</h6>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ "%.2f"|format(stat.team_era or 0) }}</h5>
                                        <small class="text-muted">防禦率</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ "%.3f"|format(stat.whip or 0) }}</h5>
                                        <small class="text-muted">WHIP</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ stat.strikeouts or 0 }}</h5>
                                        <small class="text-muted">三振</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ stat.walks or 0 }}</h5>
                                        <small class="text-muted">四壞球</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ stat.saves or 0 }}</h5>
                                        <small class="text-muted">救援成功</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="stat-card text-center">
                                        <h5>{{ "%.1f"|format(stat.innings_pitched or 0) }}</h5>
                                        <small class="text-muted">投球局數</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% else %}
            <!-- 無數據提示 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暫無統計數據</h5>
                            <p class="text-muted">該球隊目前沒有可用的統計數據</p>
                            <a href="{{ url_for('teams.team_detail', team_id=team.team_id) }}" 
                               class="btn btn-primary">返回球隊詳情</a>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.stat-card {
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-bottom: 10px;
    background-color: #f8f9fa;
}

.stat-card h4, .stat-card h5 {
    margin-bottom: 5px;
    font-weight: bold;
}

.stat-card small {
    font-size: 0.8rem;
}
</style>
{% endblock %}
