{% extends "base.html" %}

{% block title %}比賽預測{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-crystal-ball"></i> 比賽預測</h2>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('predictions.predictions_list') }}" class="btn btn-outline-primary">所有預測</a>
                    <a href="{{ url_for('predictions.prediction_accuracy') }}" class="btn btn-outline-info">準確率統計</a>
                </div>
            </div>

            <!-- 篩選器 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="date" class="form-label">日期</label>
                            <input type="date" class="form-control" id="date" name="date" value="{{ selected_date }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">篩選</button>
                            <a href="{{ url_for('predictions.predictions_list') }}" class="btn btn-outline-secondary">清除</a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 預測列表 -->
            {% if predictions %}
                <div class="row">
                    {% for game, prediction in predictions %}
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ game.date.strftime('%Y-%m-%d') }}</small>
                                <span class="badge bg-{{ 'success' if prediction.confidence_level > 0.7 else 'warning' if prediction.confidence_level > 0.5 else 'secondary' }}">
                                    信心度: {{ (prediction.confidence_level * 100)|round(1) }}%
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="row text-center mb-3">
                                    <div class="col-5">
                                        <h6 class="mb-1">{{ game.away_team }}</h6>
                                        <div class="fs-4 fw-bold text-primary">
                                            {{ prediction.predicted_away_score|round(1) }}
                                        </div>
                                        <small class="text-muted">
                                            勝率: {{ (prediction.away_win_probability * 100)|round(1) }}%
                                        </small>
                                    </div>
                                    <div class="col-2 d-flex align-items-center justify-content-center">
                                        <span class="text-muted">VS</span>
                                    </div>
                                    <div class="col-5">
                                        <h6 class="mb-1">{{ game.home_team }}</h6>
                                        <div class="fs-4 fw-bold text-success">
                                            {{ prediction.predicted_home_score|round(1) }}
                                        </div>
                                        <small class="text-muted">
                                            勝率: {{ (prediction.home_win_probability * 100)|round(1) }}%
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="text-center mb-3">
                                    {% if prediction.home_win_probability > prediction.away_win_probability %}
                                        <span class="badge bg-success">預測主隊獲勝</span>
                                    {% else %}
                                        <span class="badge bg-primary">預測客隊獲勝</span>
                                    {% endif %}
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        模型: {{ prediction.prediction_model or 'basic' }}
                                    </small>
                                    <a href="{{ url_for('predictions.prediction_detail', game_id=game.game_id) }}" 
                                       class="btn btn-sm btn-outline-primary">詳細分析</a>
                                </div>
                            </div>
                            {% if game.game_status == 'completed' %}
                            <div class="card-footer">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">實際比分</small><br>
                                        <strong>{{ game.away_score or 0 }} - {{ game.home_score or 0 }}</strong>
                                    </div>
                                    <div class="col-6">
                                        {% set actual_home_wins = game.home_score is not none and game.away_score is not none and game.home_score > game.away_score %}
                                        {% set predicted_home_wins = prediction.home_win_probability > prediction.away_win_probability %}
                                        {% if actual_home_wins == predicted_home_wins %}
                                            <span class="badge bg-success">預測正確</span>
                                        {% else %}
                                            <span class="badge bg-danger">預測錯誤</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- 分頁 -->
                {% if pagination.pages > 1 %}
                <nav aria-label="預測分頁">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('predictions.predictions_list', page=pagination.prev_num, date=selected_date) }}">上一頁</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('predictions.predictions_list', page=page_num, date=selected_date) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('predictions.predictions_list', page=pagination.next_num, date=selected_date) }}">下一頁</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">沒有找到預測</h4>
                    <p class="text-muted">請嘗試調整篩選條件或選擇其他日期</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 自動設置今天的日期
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('date');
    if (!dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
});
</script>
{% endblock %}
