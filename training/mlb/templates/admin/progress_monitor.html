{% extends "admin/layout.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-tasks"></i> 任務進度監控</h5>
                <div>
                    <button id="refreshBtn" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button id="autoRefreshToggle" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-play"></i> 自動刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 整體統計 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5 id="totalTasksCount">-</h5>
                                <small>總任務數</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5 id="runningTasksCount">-</h5>
                                <small>正在運行</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5 id="completedTasksCount">-</h5>
                                <small>已完成</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h5 id="failedTasksCount">-</h5>
                                <small>失敗</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 運行中的任務 -->
                <div id="runningTasks">
                    <h6 class="text-primary"><i class="fas fa-spinner fa-spin"></i> 正在運行的任務</h6>
                    <div id="runningTasksList" class="mb-4">
                        <div class="text-center text-muted">
                            <i class="fas fa-hourglass-half"></i> 正在加載任務狀態...
                        </div>
                    </div>
                </div>

                <!-- 已完成的任務 -->
                <div id="completedTasks">
                    <h6 class="text-success"><i class="fas fa-check-circle"></i> 已完成的任務</h6>
                    <div id="completedTasksList" class="mb-4">
                        <!-- 動態填充 -->
                    </div>
                </div>

                <!-- 失敗的任務 -->
                <div id="failedTasks">
                    <h6 class="text-danger"><i class="fas fa-exclamation-triangle"></i> 失敗的任務</h6>
                    <div id="failedTasksList" class="mb-4">
                        <!-- 動態填充 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任務詳情模態框 -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任務詳情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="taskDetailsBody">
                <!-- 動態填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
            </div>
        </div>
    </div>
</div>

<style>
.task-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
}
.task-card.running {
    border-left: 4px solid #007bff;
    background: #e3f2fd;
}
.task-card.completed {
    border-left: 4px solid #28a745;
    background: #e8f5e8;
}
.task-card.failed {
    border-left: 4px solid #dc3545;
    background: #ffeaea;
}
.progress-bar-container {
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    position: relative;
}
.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
    position: relative;
}
.progress-bar.bg-success {
    background: linear-gradient(90deg, #28a745, #1e7e34) !important;
}
.progress-bar.bg-danger {
    background: linear-gradient(90deg, #dc3545, #a71d2a) !important;
}
.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}
.task-meta {
    font-size: 0.85em;
    color: #6c757d;
}
.task-message {
    font-style: italic;
    color: #495057;
    margin-top: 5px;
}
.error-message {
    color: #dc3545;
    font-weight: bold;
    margin-top: 5px;
}
</style>

<script>
let autoRefreshInterval = null;
let autoRefreshEnabled = false;

// 頁面加載時初始化
$(document).ready(function() {
    loadTaskStatus();
    
    // 刷新按鈕
    $('#refreshBtn').click(function() {
        loadTaskStatus();
    });
    
    // 自動刷新切換
    $('#autoRefreshToggle').click(function() {
        toggleAutoRefresh();
    });
});

function loadTaskStatus() {
    $.get('/admin/api/progress-status')
        .done(function(response) {
            if (response.success) {
                renderTasks(response);
            } else {
                showError('獲取任務狀態失敗: ' + response.message);
            }
        })
        .fail(function(xhr) {
            showError('請求失敗: ' + xhr.responseText);
        });
}

function renderTasks(data) {
    const runningList = $('#runningTasksList');
    const completedList = $('#completedTasksList');
    const failedList = $('#failedTasksList');
    
    // 清空現有內容
    runningList.empty();
    completedList.empty();
    failedList.empty();
    
    let hasRunning = false, hasCompleted = false, hasFailed = false;
    let runningCount = 0, completedCount = 0, failedCount = 0;
    
    // 遍歷所有任務並分類顯示
    Object.values(data.all_tasks).forEach(task => {
        const taskHtml = createTaskCard(task);
        
        switch(task.status) {
            case 'running':
                runningList.append(taskHtml);
                hasRunning = true;
                runningCount++;
                break;
            case 'completed':
                completedList.append(taskHtml);
                hasCompleted = true;
                completedCount++;
                break;
            case 'failed':
                failedList.append(taskHtml);
                hasFailed = true;
                failedCount++;
                break;
        }
    });
    
    // 顯示空狀態
    if (!hasRunning) {
        runningList.html('<div class="text-center text-muted"><i class="fas fa-check"></i> 沒有正在運行的任務</div>');
    }
    if (!hasCompleted) {
        completedList.html('<div class="text-center text-muted">沒有已完成的任務</div>');
    }
    if (!hasFailed) {
        failedList.html('<div class="text-center text-muted">沒有失敗的任務</div>');
    }
    
    // 更新統計卡片
    const totalCount = Object.keys(data.all_tasks).length;
    $('#totalTasksCount').text(totalCount);
    $('#runningTasksCount').text(runningCount);
    $('#completedTasksCount').text(completedCount);
    $('#failedTasksCount').text(failedCount);
    
    // 更新頁面標題顯示運行中任務數
    if (runningCount > 0) {
        document.title = `(${runningCount}) MLB 任務進度監控`;
    } else {
        document.title = 'MLB 任務進度監控';
    }
}

function createTaskCard(task) {
    const statusClass = task.status;
    const progress = Math.round(task.progress || 0);
    const progressWidth = Math.max(5, progress); // 最小寬度5%以顯示文字
    
    // 格式化時間
    const startTime = task.start_time ? new Date(task.start_time).toLocaleString('zh-TW') : 'N/A';
    const endTime = task.end_time ? new Date(task.end_time).toLocaleString('zh-TW') : '';
    const duration = task.duration_seconds ? `${task.duration_seconds.toFixed(1)}秒` : '';
    
    // 最新消息
    const latestMessage = task.messages && task.messages.length > 0 ? 
        task.messages[task.messages.length - 1].message : '';
    
    // 錯誤消息
    const latestError = task.errors && task.errors.length > 0 ? 
        task.errors[task.errors.length - 1].error : '';
    
    return `
        <div class="task-card ${statusClass}">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">${task.task_name}</h6>
                <small class="task-meta">${task.task_id}</small>
            </div>
            
            ${task.status === 'running' ? `
            <div class="progress-bar-container mb-2">
                <div class="progress-bar" style="width: ${progressWidth}%"></div>
                <div class="progress-text">${progress}% (${task.current_step}/${task.total_steps})</div>
            </div>
            ` : task.status === 'completed' ? `
            <div class="progress-bar-container mb-2">
                <div class="progress-bar bg-success" style="width: 100%"></div>
                <div class="progress-text">✅ 100% 完成</div>
            </div>
            ` : task.status === 'failed' ? `
            <div class="progress-bar-container mb-2">
                <div class="progress-bar bg-danger" style="width: ${progressWidth}%"></div>
                <div class="progress-text">❌ ${progress}% 失敗</div>
            </div>
            ` : ''}
            
            <div class="task-meta">
                <i class="fas fa-clock"></i> 開始: ${startTime}
                ${endTime ? `<br><i class="fas fa-flag-checkered"></i> 結束: ${endTime}` : ''}
                ${duration ? `<br><i class="fas fa-stopwatch"></i> 耗時: ${duration}` : ''}
            </div>
            
            ${latestMessage ? `
            <div class="task-message">
                <i class="fas fa-comment"></i> ${latestMessage}
            </div>
            ` : ''}
            
            ${task.status === 'completed' && task.current_message ? `
            <div class="alert alert-success alert-sm mt-2 mb-2">
                <i class="fas fa-check-circle"></i> <strong>完成摘要:</strong><br>
                ${task.current_message}
            </div>
            ` : ''}
            
            ${latestError ? `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i> ${latestError}
            </div>
            ` : ''}
            
            <div class="mt-2">
                <button class="btn btn-sm btn-outline-info" onclick="showTaskDetails('${task.task_id}')">
                    <i class="fas fa-info-circle"></i> 詳情
                </button>
                ${task.messages && task.messages.length > 0 ? `
                <small class="text-muted ms-2">${task.messages.length} 條訊息</small>
                ` : ''}
                ${task.errors && task.errors.length > 0 ? `
                <small class="text-danger ms-2">${task.errors.length} 個錯誤</small>
                ` : ''}
            </div>
        </div>
    `;
}

function showTaskDetails(taskId) {
    $.get(`/admin/api/task-details/${taskId}`)
        .done(function(response) {
            if (response.success) {
                renderTaskDetails(response.task);
                $('#taskDetailsModal').modal('show');
            } else {
                showError('獲取任務詳情失敗: ' + response.message);
            }
        })
        .fail(function(xhr) {
            showError('請求失敗: ' + xhr.responseText);
        });
}

function renderTaskDetails(task) {
    let detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>任務名稱:</td><td>${task.task_name}</td></tr>
                    <tr><td>任務ID:</td><td><code>${task.task_id}</code></td></tr>
                    <tr><td>狀態:</td><td><span class="badge bg-${task.status === 'running' ? 'primary' : task.status === 'completed' ? 'success' : 'danger'}">${task.status}</span></td></tr>
                    <tr><td>進度:</td><td>${Math.round(task.progress || 0)}% (${task.current_step}/${task.total_steps})</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>時間信息</h6>
                <table class="table table-sm">
                    <tr><td>開始時間:</td><td>${task.start_time ? new Date(task.start_time).toLocaleString('zh-TW') : 'N/A'}</td></tr>
                    ${task.end_time ? `<tr><td>結束時間:</td><td>${new Date(task.end_time).toLocaleString('zh-TW')}</td></tr>` : ''}
                    ${task.duration_seconds ? `<tr><td>執行時間:</td><td>${task.duration_seconds.toFixed(1)}秒</td></tr>` : ''}
                    ${task.last_update ? `<tr><td>最後更新:</td><td>${new Date(task.last_update).toLocaleString('zh-TW')}</td></tr>` : ''}
                </table>
            </div>
        </div>
    `;
    
    // 添加訊息歷史
    if (task.messages && task.messages.length > 0) {
        detailsHtml += `
            <h6 class="mt-3">執行日誌</h6>
            <div style="max-height: 300px; overflow-y: auto;">
        `;
        task.messages.forEach(msg => {
            detailsHtml += `
                <div class="border-start border-info ps-3 mb-2">
                    <small class="text-muted">${new Date(msg.time).toLocaleString('zh-TW')}</small>
                    <div>${msg.message}</div>
                </div>
            `;
        });
        detailsHtml += '</div>';
    }
    
    // 添加錯誤信息
    if (task.errors && task.errors.length > 0) {
        detailsHtml += `
            <h6 class="mt-3 text-danger">錯誤記錄</h6>
            <div style="max-height: 200px; overflow-y: auto;">
        `;
        task.errors.forEach(err => {
            detailsHtml += `
                <div class="border-start border-danger ps-3 mb-2">
                    <small class="text-muted">${new Date(err.time).toLocaleString('zh-TW')}</small>
                    <div class="text-danger">${err.error}</div>
                </div>
            `;
        });
        detailsHtml += '</div>';
    }
    
    $('#taskDetailsBody').html(detailsHtml);
}

function toggleAutoRefresh() {
    if (autoRefreshEnabled) {
        // 停止自動刷新
        clearInterval(autoRefreshInterval);
        autoRefreshEnabled = false;
        $('#autoRefreshToggle').html('<i class="fas fa-play"></i> 自動刷新').removeClass('btn-outline-danger').addClass('btn-outline-success');
    } else {
        // 開始自動刷新
        autoRefreshInterval = setInterval(loadTaskStatus, 3000); // 每3秒刷新
        autoRefreshEnabled = true;
        $('#autoRefreshToggle').html('<i class="fas fa-pause"></i> 停止刷新').removeClass('btn-outline-success').addClass('btn-outline-danger');
    }
}

function showError(message) {
    // 簡單的錯誤顯示，可以使用更好的通知組件
    alert('錯誤: ' + message);
}
</script>
{% endblock %}