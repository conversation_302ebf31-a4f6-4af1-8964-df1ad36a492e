{% extends "admin/layout.html" %}

{% block admin_content %}
<div class="row">
    <!-- 左側：控制面板 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calendar-alt"></i> 選擇回測日期</h5>
            </div>
            <div class="card-body">
                <div class="input-group mb-3">
                    <input type="date" id="date-selector" class="form-control" value="{{ today }}">
                    <button class="btn btn-primary" type="button" id="fetch-games-btn">載入比賽</button>
                </div>
                <hr>
                <h6>當日比賽列表</h6>
                <div id="games-list-container" style="max-height: 600px; overflow-y: auto;">
                    <p class="text-muted">請先選擇日期並載入比賽。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 右側：分析報告 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> 回測分析報告</h5>
            </div>
            <div class="card-body" id="report-container">
                <div class="text-center p-5">
                    <i class="fas fa-search fa-3x text-muted"></i>
                    <p class="mt-3 text-muted">請從左側選擇一場比賽以開始分析。</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fetchBtn = document.getElementById('fetch-games-btn');
    const dateSelector = document.getElementById('date-selector');
    const gamesListContainer = document.getElementById('games-list-container');
    const reportContainer = document.getElementById('report-container');

    fetchBtn.addEventListener('click', fetchGamesForDate);

    function fetchGamesForDate() {
        const selectedDate = dateSelector.value;
        if (!selectedDate) {
            alert('請選擇一個日期。');
            return;
        }

        gamesListContainer.innerHTML = '<div class="d-flex justify-content-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>';

        fetch(`/admin/api/games-for-date?date=${selectedDate}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    gamesListContainer.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
                    return;
                }
                if (data.length === 0) {
                    gamesListContainer.innerHTML = '<p class="text-muted">該日期沒有比賽。</p>';
                    return;
                }

                let listHtml = '<ul class="list-group">';
                data.forEach(game => {
                    listHtml += `
                        <li class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" style="cursor: pointer;" data-game-id="${game.game_id}">
                            <div>
                                <i class="fas fa-baseball-ball"></i>
                                <strong>${game.away_team}</strong> @ <strong>${game.home_team}</strong>
                            </div>
                            <span class="badge bg-secondary">${game.score}</span>
                        </li>`;
                });
                listHtml += '</ul>';
                gamesListContainer.innerHTML = listHtml;

                // 為每個比賽項目添加點擊事件
                document.querySelectorAll('#games-list-container li').forEach(item => {
                    item.addEventListener('click', runBacktest);
                });
            })
            .catch(err => {
                gamesListContainer.innerHTML = `<div class="alert alert-danger">載入比賽失敗: ${err}</div>`;
            });
    }

    function runBacktest(event) {
        const gameId = event.currentTarget.dataset.gameId;
        reportContainer.innerHTML = '<div class="text-center p-5"><div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status"></div><p class="mt-3">正在執行回測分析...</p></div>';

        // 移除其他項目的 active class
        document.querySelectorAll('#games-list-container li').forEach(item => item.classList.remove('active'));
        // 為當前點擊的項目添加 active class
        event.currentTarget.classList.add('active');

        fetch('/admin/api/run-backtest', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ game_id: gameId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                reportContainer.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
                return;
            }
            renderReport(data);
        })
        .catch(err => {
            reportContainer.innerHTML = `<div class="alert alert-danger">執行回測失敗: ${err}</div>`;
        });
    }

    function renderReport(data) {
        const analysis = data.matchup_analysis;
        const prediction = data.prediction_result;
        const gameInfo = data.game_info;

        const homePitcher = analysis.pitcher_scores.home;
        const awayPitcher = analysis.pitcher_scores.away;

        let reportHtml = `
            <h4>${gameInfo.away_team} @ ${gameInfo.home_team}</h4>
            <p class="text-muted">日期: ${gameInfo.date} | 實際比分: ${gameInfo.actual_score}</p>
            <hr>

            <div class="row">
                <!-- 智能對戰分析 -->
                <div class="col-md-6">
                    <h5><i class="fas fa-brain text-primary"></i> 智能對戰分析</h5>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">${analysis.summary}</h6>
                        <p><strong>比賽類型:</strong> <span class="badge bg-primary">${analysis.matchup_type}</span></p>
                        <p><strong>預期總分環境:</strong> <span class="badge bg-info">${analysis.run_environment}</span></p>
                    </div>
                    <h6>投手強度評分</h6>
                    <div class="d-flex justify-content-around">
                        <div class="text-center">
                            <p>${awayPitcher.name}</p>
                            <h4>${awayPitcher.score}</h4>
                        </div>
                        <div class="text-center">
                            <p>${homePitcher.name}</p>
                            <h4>${homePitcher.score}</h4>
                        </div>
                    </div>
                </div>

                <!-- 模型預測結果 -->
                <div class="col-md-6">
                    <h5><i class="fas fa-chart-line text-success"></i> 模型預測結果</h5>
                    <div class="text-center bg-light p-3 rounded">
                        <h4>${prediction.predicted_away_score} - ${prediction.predicted_home_score}</h4>
                        <p class="mb-0">預測總分: <strong>${prediction.total_runs_predicted}</strong></p>
                    </div>
                    <h6 class="mt-3">勝率預測</h6>
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar bg-info" role="progressbar" style="width: ${prediction.away_win_probability * 100}%">${(prediction.away_win_probability * 100).toFixed(1)}%</div>
                        <div class="progress-bar bg-success" role="progressbar" style="width: ${prediction.home_win_probability * 100}%">${(prediction.home_win_probability * 100).toFixed(1)}%</div>
                    </div>
                    <p class="mt-2"><strong>信心度:</strong> ${(prediction.confidence * 100).toFixed(1)}%</p>
                </div>
            </div>
            <hr>
            <h5><i class="fas fa-cogs"></i> 使用的特徵 (部分)</h5>
            <pre style="max-height: 200px; background-color: #f8f9fa; padding: 10px; border-radius: 5px;"><code>${JSON.stringify(prediction.features_used, null, 2)}</code></pre>
        `;
        reportContainer.innerHTML = reportHtml;
    }
});
</script>
{% endblock %}
