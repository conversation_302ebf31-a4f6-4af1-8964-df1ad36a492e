<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>賠率數據改善系統 - 管理後台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .dashboard-card {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e3e6f0;
            margin-bottom: 1.5rem;
        }
        .quality-excellent { color: #28a745; font-weight: bold; }
        .quality-good { color: #17a2b8; font-weight: bold; }
        .quality-average { color: #ffc107; font-weight: bold; }
        .quality-poor { color: #fd7e14; font-weight: bold; }
        .quality-critical { color: #dc3545; font-weight: bold; }
        .coverage-meter {
            height: 20px;
            background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        .coverage-indicator {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: rgba(0,0,0,0.1);
            border-right: 2px solid #fff;
            transition: width 0.3s ease;
        }
        .coverage-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
        }
        .missing-game-item {
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            border-left: 3px solid #dc3545;
            background-color: #f8f9fa;
            border-radius: 0 0.25rem 0.25rem 0;
        }
        .stats-card {
            text-align: center;
            padding: 1.5rem;
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            line-height: 1;
        }
        .stats-icon {
            font-size: 3rem;
            opacity: 0.7;
            margin-bottom: 1rem;
        }
        .enhancement-action {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 0.375rem;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-info">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">
                <i class="bi bi-graph-up-arrow me-2"></i>賠率數據改善系統
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/admin/data-management">
                    <i class="bi bi-arrow-left me-1"></i>返回數據管理
                </a>
                <a class="nav-link" href="/admin/progress-monitor">
                    <i class="bi bi-activity me-1"></i>進度監控
                </a>
                <a class="nav-link" href="/admin/data-repair">
                    <i class="bi bi-tools me-1"></i>數據修復
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- 覆蓋率概覽 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>賠率覆蓋率概覽
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <i class="bi bi-percent stats-icon text-primary"></i>
                                    <div class="stats-number text-primary" id="coverage-rate">-</div>
                                    <div class="text-muted">覆蓋率</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <i class="bi bi-trophy stats-icon text-success"></i>
                                    <div class="stats-number text-success" id="quality-grade">-</div>
                                    <div class="text-muted">質量等級</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <i class="bi bi-calendar-x stats-icon text-warning"></i>
                                    <div class="stats-number text-warning" id="missing-games">-</div>
                                    <div class="text-muted">缺失場次</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <i class="bi bi-arrow-up-circle stats-icon text-info"></i>
                                    <div class="stats-number text-info" id="improvement-potential">-</div>
                                    <div class="text-muted">改善潛力</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <h6>覆蓋率進度條</h6>
                                <div class="coverage-meter" id="coverage-meter">
                                    <div class="coverage-indicator" id="coverage-indicator"></div>
                                    <div class="coverage-text" id="coverage-text">分析中...</div>
                                </div>
                                <div class="d-flex justify-content-between mt-1 small text-muted">
                                    <span>0%</span>
                                    <span>50%</span>
                                    <span>100%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 改善控制面板 -->
            <div class="col-lg-4">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-sliders me-2"></i>改善控制面板
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="enhancement-settings">
                            <div class="mb-3">
                                <label for="target-year" class="form-label">年份</label>
                                <select class="form-select" id="target-year">
                                    <option value="2025">2025</option>
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="target-month" class="form-label">月份</label>
                                <select class="form-select" id="target-month">
                                    <option value="1">1月</option>
                                    <option value="2">2月</option>
                                    <option value="3">3月</option>
                                    <option value="4">4月</option>
                                    <option value="5">5月</option>
                                    <option value="6">6月</option>
                                    <option value="7">7月</option>
                                    <option value="8" selected>8月</option>
                                    <option value="9">9月</option>
                                    <option value="10">10月</option>
                                    <option value="11">11月</option>
                                    <option value="12">12月</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="target-coverage" class="form-label">目標覆蓋率</label>
                                <select class="form-select" id="target-coverage">
                                    <option value="0.8">80% (最低標準)</option>
                                    <option value="0.9" selected>90% (目標標準)</option>
                                    <option value="0.95">95% (優秀標準)</option>
                                </select>
                            </div>
                            <hr>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" id="analyze-btn">
                                    <i class="bi bi-search me-1"></i>分析覆蓋率
                                </button>
                                <button type="button" class="btn btn-success" id="enhance-btn" disabled>
                                    <i class="bi bi-graph-up-arrow me-1"></i>開始改善
                                </button>
                            </div>
                        </form>

                        <hr>
                        
                        <div class="mb-3">
                            <h6>快速修復</h6>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="quickFix('missing')">
                                    <i class="bi bi-lightning me-1"></i>缺失賠率
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="quickFix('low_quality')">
                                    <i class="bi bi-shield-exclamation me-1"></i>低質量數據
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="quickFix('outdated')">
                                    <i class="bi bi-clock-history me-1"></i>過期數據
                                </button>
                            </div>
                        </div>

                        <div class="alert alert-info small">
                            <i class="bi bi-info-circle me-1"></i>
                            <strong>改善策略</strong><br>
                            1. 重試主要數據源<br>
                            2. 啟用備用API<br>
                            3. 網頁抓取補充<br>
                            4. 歷史數據估算
                        </div>
                    </div>
                </div>
            </div>

            <!-- 覆蓋率分析結果 -->
            <div class="col-lg-8">
                <div class="card dashboard-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="bi bi-bar-chart me-2"></i>覆蓋率分析結果
                        </h6>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="showAnalysis('overview')">概覽</button>
                            <button type="button" class="btn btn-outline-success" onclick="showAnalysis('bookmakers')">博彩商</button>
                            <button type="button" class="btn btn-outline-info" onclick="showAnalysis('markets')">市場類型</button>
                            <button type="button" class="btn btn-outline-warning" onclick="showAnalysis('missing')">缺失詳情</button>
                        </div>
                    </div>
                    <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                        <div id="analysis-content">
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-bar-chart-line display-6 mb-3"></i>
                                <p>點擊"分析覆蓋率"開始分析當前數據狀況</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentAnalysis = null;
        let currentView = 'overview';

        // 設置默認日期為當前月份
        document.addEventListener('DOMContentLoaded', function() {
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;
            
            document.getElementById('target-year').value = currentYear;
            document.getElementById('target-month').value = currentMonth;
        });

        // 分析覆蓋率
        document.getElementById('analyze-btn').addEventListener('click', async function() {
            const year = parseInt(document.getElementById('target-year').value);
            const month = parseInt(document.getElementById('target-month').value);
            
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>分析中...';
            
            try {
                const response = await fetch('/admin/api/analyze-odds-coverage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ year, month })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentAnalysis = result.analysis;
                    displayAnalysis(result.analysis);
                    document.getElementById('enhance-btn').disabled = false;
                } else {
                    showError('分析失敗: ' + result.message);
                }
            } catch (error) {
                showError('分析請求失敗: ' + error.message);
            }
            
            this.disabled = false;
            this.innerHTML = '<i class="bi bi-search me-1"></i>分析覆蓋率';
        });

        // 開始改善
        document.getElementById('enhance-btn').addEventListener('click', async function() {
            if (!currentAnalysis) {
                alert('請先進行覆蓋率分析');
                return;
            }
            
            const year = parseInt(document.getElementById('target-year').value);
            const month = parseInt(document.getElementById('target-month').value);
            const targetCoverage = parseFloat(document.getElementById('target-coverage').value);
            
            if (currentAnalysis.coverage_rate >= targetCoverage * 100) {
                if (!confirm('當前覆蓋率已達到目標，仍要執行改善嗎？')) {
                    return;
                }
            } else {
                if (!confirm(`即將改善 ${year}年${month}月 的賠率覆蓋率至 ${targetCoverage*100}%，是否繼續？`)) {
                    return;
                }
            }
            
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>改善中...';
            
            try {
                const response = await fetch('/admin/api/enhance-odds-collection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        year, 
                        month, 
                        target_coverage: targetCoverage 
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess(result.message);
                } else {
                    showError('改善啟動失敗: ' + result.message);
                }
            } catch (error) {
                showError('改善請求失敗: ' + error.message);
            }
            
            this.disabled = false;
            this.innerHTML = '<i class="bi bi-graph-up-arrow me-1"></i>開始改善';
        });

        // 快速修復
        async function quickFix(type) {
            const year = parseInt(document.getElementById('target-year').value);
            const month = parseInt(document.getElementById('target-month').value);
            
            const typeNames = {
                'missing': '缺失賠率',
                'low_quality': '低質量數據',
                'outdated': '過期數據'
            };
            
            if (!confirm(`即將執行快速修復: ${typeNames[type]}，是否繼續？`)) {
                return;
            }
            
            try {
                const response = await fetch('/admin/api/quick-odds-fix', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ year, month, type })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess(result.message);
                } else {
                    showError('快速修復失敗: ' + result.message);
                }
            } catch (error) {
                showError('快速修復請求失敗: ' + error.message);
            }
        }

        // 顯示分析結果
        function displayAnalysis(analysis) {
            // 更新概覽統計
            document.getElementById('coverage-rate').textContent = analysis.coverage_rate.toFixed(1) + '%';
            document.getElementById('quality-grade').textContent = analysis.quality_grade;
            document.getElementById('missing-games').textContent = analysis.missing_games;
            document.getElementById('improvement-potential').textContent = '+' + analysis.improvement_potential.toFixed(1) + '%';
            
            // 更新覆蓋率進度條
            const coverageIndicator = document.getElementById('coverage-indicator');
            const coverageText = document.getElementById('coverage-text');
            
            coverageIndicator.style.width = analysis.coverage_rate + '%';
            coverageText.textContent = analysis.coverage_rate.toFixed(1) + '%';
            
            // 設置質量等級顏色
            const gradeElement = document.getElementById('quality-grade');
            gradeElement.className = `stats-number ${getQualityClass(analysis.quality_grade)}`;
            
            // 顯示詳細分析內容
            showAnalysis('overview');
        }

        // 顯示不同視圖的分析內容
        function showAnalysis(view) {
            if (!currentAnalysis) return;
            
            currentView = view;
            const content = document.getElementById('analysis-content');
            
            switch (view) {
                case 'overview':
                    content.innerHTML = generateOverviewHTML(currentAnalysis);
                    break;
                case 'bookmakers':
                    content.innerHTML = generateBookmakersHTML(currentAnalysis);
                    break;
                case 'markets':
                    content.innerHTML = generateMarketsHTML(currentAnalysis);
                    break;
                case 'missing':
                    content.innerHTML = generateMissingHTML(currentAnalysis);
                    break;
            }
        }

        // 生成概覽HTML
        function generateOverviewHTML(analysis) {
            return `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本統計</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-calendar me-2 text-primary"></i>目標月份: ${analysis.month}</li>
                            <li><i class="bi bi-trophy me-2 text-success"></i>總比賽數: ${analysis.total_games}</li>
                            <li><i class="bi bi-check-circle me-2 text-info"></i>有賠率比賽: ${analysis.covered_games}</li>
                            <li><i class="bi bi-x-circle me-2 text-warning"></i>缺失比賽: ${analysis.missing_games}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>質量評估</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-percent me-2 text-primary"></i>覆蓋率: ${analysis.coverage_rate.toFixed(1)}%</li>
                            <li><i class="bi bi-star me-2 text-success"></i>質量等級: ${analysis.quality_grade}</li>
                            <li><i class="bi bi-shield-check me-2 ${analysis.meets_minimum_standard ? 'text-success' : 'text-danger'}"></i>最低標準: ${analysis.meets_minimum_standard ? '✅ 達成' : '❌ 未達成'}</li>
                            <li><i class="bi bi-target me-2 ${analysis.meets_target_standard ? 'text-success' : 'text-warning'}"></i>目標標準: ${analysis.meets_target_standard ? '✅ 達成' : '❌ 未達成'}</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        // 生成博彩商HTML
        function generateBookmakersHTML(analysis) {
            const bookmakers = analysis.bookmaker_breakdown || {};
            let html = '<h6>博彩商分佈</h6>';
            
            if (Object.keys(bookmakers).length === 0) {
                html += '<p class="text-muted">暫無博彩商數據</p>';
            } else {
                html += '<div class="row">';
                for (const [bookmaker, count] of Object.entries(bookmakers)) {
                    const percentage = ((count / analysis.total_games) * 100).toFixed(1);
                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-building me-2"></i>${bookmaker}</span>
                                <span class="badge bg-primary">${count} (${percentage}%)</span>
                            </div>
                        </div>
                    `;
                }
                html += '</div>';
            }
            
            return html;
        }

        // 生成市場類型HTML
        function generateMarketsHTML(analysis) {
            const markets = analysis.market_breakdown || {};
            let html = '<h6>市場類型分佈</h6>';
            
            if (Object.keys(markets).length === 0) {
                html += '<p class="text-muted">暫無市場類型數據</p>';
            } else {
                html += '<div class="row">';
                for (const [market, count] of Object.entries(markets)) {
                    const percentage = ((count / analysis.total_games) * 100).toFixed(1);
                    const marketName = {
                        'spreads': '讓分盤',
                        'totals': '大小分',
                        'both': '綜合'
                    }[market] || market;
                    
                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-graph-up me-2"></i>${marketName}</span>
                                <span class="badge bg-info">${count} (${percentage}%)</span>
                            </div>
                        </div>
                    `;
                }
                html += '</div>';
            }
            
            return html;
        }

        // 生成缺失詳情HTML
        function generateMissingHTML(analysis) {
            const missingGames = analysis.missing_odds_games || [];
            let html = '<h6>缺失賠率比賽詳情</h6>';
            
            if (missingGames.length === 0) {
                html += '<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>沒有缺失賠率的比賽</div>';
            } else {
                html += `<p class="text-muted mb-3">顯示前 ${Math.min(missingGames.length, 20)} 場缺失賠率的比賽</p>`;
                missingGames.forEach(game => {
                    html += `
                        <div class="missing-game-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-calendar me-2"></i>${game.date}
                                    <i class="bi bi-people-fill ms-3 me-2"></i>${game.matchup}
                                    <span class="badge bg-secondary ms-2">${game.status}</span>
                                </div>
                                <button class="btn btn-sm btn-outline-primary" onclick="fixSingleGame('${game.game_id}')">
                                    <i class="bi bi-tools"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });
            }
            
            return html;
        }

        // 修復單個比賽
        async function fixSingleGame(gameId) {
            // 這裡可以實現單個比賽的修復邏輯
            showInfo(`正在修復比賽 ${gameId} 的賠率數據...`);
        }

        // 獲取質量等級樣式類
        function getQualityClass(grade) {
            const classMap = {
                '優秀': 'quality-excellent',
                '良好': 'quality-good',
                '一般': 'quality-average',
                '需改善': 'quality-poor',
                '嚴重不足': 'quality-critical'
            };
            return classMap[grade] || 'text-muted';
        }

        // 消息顯示函數
        function showSuccess(message) {
            showNotification(message, 'success');
        }

        function showError(message) {
            showNotification(message, 'danger');
        }

        function showInfo(message) {
            showNotification(message, 'info');
        }

        function showNotification(message, type) {
            // 創建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // 自動移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }
    </script>
</body>
</html>