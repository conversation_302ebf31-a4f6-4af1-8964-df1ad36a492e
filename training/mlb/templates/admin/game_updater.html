<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比賽結果更新器 - MLB管理系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .update-card {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 1.5rem;
        }
        .game-item {
            padding: 0.75rem;
            border-bottom: 1px solid #e3e6f0;
        }
        .game-item:last-child {
            border-bottom: none;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        .status-completed { background-color: #d1f2eb; color: #0f5132; }
        .status-scheduled { background-color: #fff3cd; color: #664d03; }
        .status-in_progress { background-color: #cfe2ff; color: #084298; }
        .needs-update {
            background-color: #ffebee;
        }
        .update-success {
            animation: pulse 1s;
            background-color: #e8f5e9;
        }
        @keyframes pulse {
            0% { background-color: #a5d6a7; }
            100% { background-color: #e8f5e9; }
        }
        .spinner-container {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .spinner-container.active {
            display: block;
        }
        .summary-stat {
            text-align: center;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">
                <i class="bi bi-arrow-repeat me-2"></i>比賽結果更新器
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/admin">
                    <i class="bi bi-house me-1"></i>管理首頁
                </a>
                <a class="nav-link" href="/admin/data-management">
                    <i class="bi bi-database me-1"></i>數據管理
                </a>
                <a class="nav-link" href="/admin/progress-monitor">
                    <i class="bi bi-activity me-1"></i>進度監控
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- 快速更新區 -->
        <div class="row">
            <div class="col-md-4">
                <div class="card update-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-check me-2"></i>單日更新與數據下載
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="single-date" class="form-label">選擇日期</label>
                            <input type="date" class="form-control" id="single-date" value="{{ yesterday }}">
                        </div>
                        <div class="d-grid gap-2">
                            <button class="btn btn-info" onclick="checkGameStatus()">
                                <i class="bi bi-search me-2"></i>檢查狀態
                            </button>
                            <button class="btn btn-primary" onclick="updateSingleDate()">
                                <i class="bi bi-arrow-clockwise me-2"></i>更新比賽結果
                            </button>
                            <button class="btn btn-success" onclick="downloadBoxscores()">
                                <i class="bi bi-download me-2"></i>下載Box Score
                            </button>
                        </div>
                        
                        <!-- 快速選擇按鈕 -->
                        <div class="mt-3">
                            <small class="text-muted">快速選擇：</small>
                            <div class="btn-group btn-group-sm w-100 mt-1" role="group">
                                <button type="button" class="btn btn-outline-secondary" onclick="setDate('today')">今天</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setDate('yesterday')">昨天</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setDate('2days')">前天</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量更新區 -->
                <div class="card update-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-range me-2"></i>批量更新
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="batch-start" class="form-label">開始日期</label>
                            <input type="date" class="form-control" id="batch-start">
                        </div>
                        <div class="mb-3">
                            <label for="batch-end" class="form-label">結束日期</label>
                            <input type="date" class="form-control" id="batch-end">
                            <small class="text-muted">最多支持7天</small>
                        </div>
                        <div class="d-grid">
                            <button class="btn btn-success" onclick="batchUpdate()">
                                <i class="bi bi-arrow-repeat me-2"></i>批量更新
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 狀態顯示區 -->
            <div class="col-md-8">
                <div class="card update-card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-list-check me-2"></i>比賽狀態詳情
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 統計摘要 -->
                        <div id="summary-container" style="display: none;">
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <div class="summary-stat bg-light">
                                        <div class="h3 mb-0" id="total-games">0</div>
                                        <small class="text-muted">總比賽數</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="summary-stat bg-light">
                                        <div class="h3 mb-0 text-success" id="completed-games">0</div>
                                        <small class="text-muted">已完成</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="summary-stat bg-light">
                                        <div class="h3 mb-0 text-warning" id="scheduled-games">0</div>
                                        <small class="text-muted">預定中</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="summary-stat bg-light">
                                        <div class="h3 mb-0 text-danger" id="no-score-games">0</div>
                                        <small class="text-muted">缺少比分</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 載入動畫 -->
                        <div class="spinner-container" id="loading-spinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">載入中...</span>
                            </div>
                            <div class="mt-2">正在處理...</div>
                        </div>

                        <!-- 比賽列表 -->
                        <div id="games-container">
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-info-circle" style="font-size: 3rem;"></i>
                                <p class="mt-2">選擇日期並點擊「檢查狀態」查看比賽詳情</p>
                            </div>
                        </div>

                        <!-- 操作結果 -->
                        <div id="result-message" class="alert d-none" role="alert"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 設置日期
        function setDate(type) {
            const input = document.getElementById('single-date');
            const today = new Date();
            
            if (type === 'today') {
                input.value = formatDate(today);
            } else if (type === 'yesterday') {
                today.setDate(today.getDate() - 1);
                input.value = formatDate(today);
            } else if (type === '2days') {
                today.setDate(today.getDate() - 2);
                input.value = formatDate(today);
            }
        }

        // 格式化日期
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // 檢查比賽狀態
        async function checkGameStatus() {
            const date = document.getElementById('single-date').value;
            if (!date) {
                showMessage('請選擇日期', 'warning');
                return;
            }

            showLoading(true);
            hideMessage();

            try {
                const response = await fetch('/admin/game-updater/api/check-games', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ date })
                });

                const data = await response.json();
                
                if (data.success) {
                    displayGameStatus(data);
                } else {
                    showMessage(data.message || '檢查失敗', 'danger');
                }
            } catch (error) {
                showMessage('網絡錯誤：' + error.message, 'danger');
            } finally {
                showLoading(false);
            }
        }

        // 顯示比賽狀態
        function displayGameStatus(data) {
            // 更新統計
            document.getElementById('summary-container').style.display = 'block';
            document.getElementById('total-games').textContent = data.summary.total;
            document.getElementById('completed-games').textContent = data.summary.completed;
            document.getElementById('scheduled-games').textContent = data.summary.scheduled;
            document.getElementById('no-score-games').textContent = data.summary.without_scores;

            // 顯示比賽列表
            const container = document.getElementById('games-container');
            container.innerHTML = '';

            if (data.games && data.games.length > 0) {
                const list = document.createElement('div');
                list.className = 'list-group';

                data.games.forEach(game => {
                    const item = document.createElement('div');
                    item.className = `game-item ${game.needs_update ? 'needs-update' : ''}`;
                    
                    item.innerHTML = `
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <strong>${game.teams}</strong>
                            </div>
                            <div class="col-md-2">
                                <span class="status-badge status-${game.status}">${game.status}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="text-monospace">${game.score}</span>
                            </div>
                            <div class="col-md-4 text-muted small">
                                更新: ${game.updated_at}
                            </div>
                        </div>
                    `;
                    
                    list.appendChild(item);
                });

                container.appendChild(list);

                // 顯示建議
                if (data.summary.needs_update) {
                    showMessage(`發現 ${data.summary.without_scores} 場比賽需要更新結果`, 'info');
                } else {
                    showMessage('所有比賽都已有最新結果', 'success');
                }
            } else {
                container.innerHTML = '<div class="text-center text-muted py-3">沒有找到比賽記錄</div>';
            }
        }

        // 下載Box Score數據
        async function downloadBoxscores() {
            const date = document.getElementById('single-date').value;
            if (!date) {
                showMessage('請選擇日期', 'warning');
                return;
            }

            if (!confirm(`確定要下載 ${date} 的Box Score詳細數據嗎？\n\n這將包含每位球員的詳細統計數據，對預測準確性很重要。`)) {
                return;
            }

            showLoading(true);
            hideMessage();

            try {
                const response = await fetch('/admin/game-updater/api/download-boxscores', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ date })
                });

                const data = await response.json();
                
                if (data.success) {
                    showMessage(data.message + '\n' + data.note, 'success');
                    // 建議用戶稍後檢查狀態
                    setTimeout(() => {
                        showMessage('Box Score下載完成！建議重新運行預測以獲得更好準確性。', 'info');
                    }, 10000);
                } else {
                    showMessage(data.message || 'Box Score下載失敗', 'danger');
                }
            } catch (error) {
                showMessage('網絡錯誤：' + error.message, 'danger');
            } finally {
                showLoading(false);
            }
        }

        // 更新單日比賽
        async function updateSingleDate() {
            const date = document.getElementById('single-date').value;
            if (!date) {
                showMessage('請選擇日期', 'warning');
                return;
            }

            if (!confirm(`確定要更新 ${date} 的比賽結果嗎？`)) {
                return;
            }

            showLoading(true);
            hideMessage();

            try {
                const response = await fetch('/admin/game-updater/api/update-games', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ date })
                });

                const data = await response.json();
                
                if (data.success) {
                    showMessage(data.message, 'success');
                    // 5秒後重新檢查狀態
                    setTimeout(() => checkGameStatus(), 5000);
                } else {
                    showMessage(data.message || '更新失敗', 'danger');
                }
            } catch (error) {
                showMessage('網絡錯誤：' + error.message, 'danger');
            } finally {
                showLoading(false);
            }
        }

        // 批量更新
        async function batchUpdate() {
            const startDate = document.getElementById('batch-start').value;
            const endDate = document.getElementById('batch-end').value;
            
            if (!startDate || !endDate) {
                showMessage('請選擇開始和結束日期', 'warning');
                return;
            }

            if (!confirm(`確定要批量更新 ${startDate} 到 ${endDate} 的比賽結果嗎？`)) {
                return;
            }

            showLoading(true);
            hideMessage();

            try {
                const response = await fetch('/admin/game-updater/api/batch-update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        start_date: startDate,
                        end_date: endDate 
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showMessage(data.message + '\n' + data.note, 'success');
                } else {
                    showMessage(data.message || '批量更新失敗', 'danger');
                }
            } catch (error) {
                showMessage('網絡錯誤：' + error.message, 'danger');
            } finally {
                showLoading(false);
            }
        }

        // 顯示載入動畫
        function showLoading(show) {
            const spinner = document.getElementById('loading-spinner');
            if (show) {
                spinner.classList.add('active');
            } else {
                spinner.classList.remove('active');
            }
        }

        // 顯示消息
        function showMessage(message, type) {
            const alertDiv = document.getElementById('result-message');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.classList.remove('d-none');
        }

        // 隱藏消息
        function hideMessage() {
            const alertDiv = document.getElementById('result-message');
            alertDiv.classList.add('d-none');
        }

        // 頁面載入時設置默認日期
        window.onload = function() {
            // 設置批量更新的默認日期範圍
            const today = new Date();
            const threeDaysAgo = new Date();
            threeDaysAgo.setDate(today.getDate() - 3);
            
            document.getElementById('batch-start').value = formatDate(threeDaysAgo);
            document.getElementById('batch-end').value = formatDate(today);
        };
    </script>
</body>
</html>