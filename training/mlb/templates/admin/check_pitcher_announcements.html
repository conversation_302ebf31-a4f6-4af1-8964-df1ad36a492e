{% extends "admin/layout.html" %}

{% block title %}投手公告檢查 - 管理面板{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1><i class="fas fa-bullhorn"></i> 投手公告檢查</h1>
            <p class="text-muted">檢查今日比賽的先發投手公告狀態</p>
        </div>
    </div>

    <!-- 控制面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-search"></i> 投手公告查詢</h5>
                </div>
                <div class="card-body">
                    <form id="pitcher-check-form" class="form-inline">
                        <div class="form-group mr-3">
                            <label for="check-date" class="mr-2">檢查日期:</label>
                            <input type="date" class="form-control" id="check-date" value="{{ moment().format('YYYY-MM-DD') }}">
                        </div>
                        
                        <div class="form-group mr-3">
                            <label for="data-source" class="mr-2">資料來源:</label>
                            <select class="form-control" id="data-source">
                                <option value="mlb">MLB.com</option>
                                <option value="rotogrinders">RotoGrinders</option>
                                <option value="espn">ESPN</option>
                                <option value="all">全部來源</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 檢查公告
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 投手公告狀態 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clipboard-list"></i> 投手公告狀態</h5>
                </div>
                <div class="card-body">
                    <div id="pitcher-status-loading" style="display: none;">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">檢查中...</span>
                            </div>
                            <p class="mt-2">正在檢查投手公告...</p>
                        </div>
                    </div>
                    
                    <div id="pitcher-status-results" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>比賽</th>
                                        <th>主場投手</th>
                                        <th>客場投手</th>
                                        <th>公告狀態</th>
                                        <th>資料來源</th>
                                        <th>最後更新</th>
                                    </tr>
                                </thead>
                                <tbody id="pitcher-status-table-body">
                                    <!-- 動態載入內容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div id="pitcher-status-empty" style="display: none;">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            尚未執行檢查，請選擇日期並點擊「檢查公告」
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 統計資訊 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x"></i>
                    <h4 class="card-title mt-2">已公告</h4>
                    <h3 id="announced-count">0</h3>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x"></i>
                    <h4 class="card-title mt-2">待公告</h4>
                    <h3 id="pending-count">0</h3>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-white bg-danger">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                    <h4 class="card-title mt-2">資訊缺失</h4>
                    <h3 id="missing-count">0</h3>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body text-center">
                    <i class="fas fa-baseball-ball fa-2x"></i>
                    <h4 class="card-title mt-2">總比賽數</h4>
                    <h3 id="total-games">0</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作區域 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools"></i> 管理操作</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-success" id="update-pitcher-info">
                        <i class="fas fa-sync-alt"></i> 更新投手資訊
                    </button>
                    
                    <button class="btn btn-info" id="manual-pitcher-input">
                        <i class="fas fa-edit"></i> 手動輸入投手
                    </button>
                    
                    <a href="{{ url_for('admin.prediction_readiness') }}" class="btn btn-warning">
                        <i class="fas fa-clipboard-check"></i> 預測準備檢查
                    </a>
                    
                    <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回儀表板
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 顯示初始狀態
    $('#pitcher-status-empty').show();
    
    // 投手公告檢查表單提交
    $('#pitcher-check-form').on('submit', function(e) {
        e.preventDefault();
        
        const checkDate = $('#check-date').val();
        const dataSource = $('#data-source').val();
        
        // 顯示載入中
        $('#pitcher-status-empty').hide();
        $('#pitcher-status-results').hide();
        $('#pitcher-status-loading').show();
        
        // 模擬 API 調用
        setTimeout(() => {
            // 隱藏載入中，顯示結果
            $('#pitcher-status-loading').hide();
            
            // 模擬結果資料
            const mockData = [
                {
                    game: 'WSH @ CHC',
                    home_pitcher: 'Kyle Hendricks',
                    away_pitcher: 'Josiah Gray',
                    status: 'confirmed',
                    source: 'MLB.com',
                    updated: '2025-09-05 08:30'
                },
                {
                    game: 'TEX @ HOU',
                    home_pitcher: '待公告',
                    away_pitcher: 'Nathan Eovaldi',
                    status: 'pending',
                    source: 'ESPN',
                    updated: '2025-09-05 07:45'
                }
            ];
            
            // 更新表格
            updatePitcherStatusTable(mockData);
            $('#pitcher-status-results').show();
        }, 2000);
    });
    
    // 更新投手狀態表格
    function updatePitcherStatusTable(data) {
        let tbody = $('#pitcher-status-table-body');
        tbody.empty();
        
        let announced = 0, pending = 0, missing = 0;
        
        data.forEach(game => {
            let statusBadge = '';
            let statusClass = '';
            
            if (game.status === 'confirmed') {
                statusBadge = '<span class="badge badge-success">已確認</span>';
                announced++;
            } else if (game.status === 'pending') {
                statusBadge = '<span class="badge badge-warning">待公告</span>';
                pending++;
            } else {
                statusBadge = '<span class="badge badge-danger">缺失</span>';
                missing++;
            }
            
            const row = `
                <tr>
                    <td>${game.game}</td>
                    <td>${game.home_pitcher}</td>
                    <td>${game.away_pitcher}</td>
                    <td>${statusBadge}</td>
                    <td>${game.source}</td>
                    <td>${game.updated}</td>
                </tr>
            `;
            
            tbody.append(row);
        });
        
        // 更新統計
        $('#announced-count').text(announced);
        $('#pending-count').text(pending);
        $('#missing-count').text(missing);
        $('#total-games').text(data.length);
    }
    
    // 更新投手資訊按鈕
    $('#update-pitcher-info').on('click', function() {
        const btn = $(this);
        btn.prop('disabled', true);
        btn.html('<i class="fas fa-spinner fa-spin"></i> 更新中...');
        
        // 模擬更新過程
        setTimeout(() => {
            btn.prop('disabled', false);
            btn.html('<i class="fas fa-sync-alt"></i> 更新投手資訊');
            
            // 顯示成功訊息
            showAlert('success', '投手資訊更新完成！');
        }, 3000);
    });
    
    // 顯示警告訊息
    function showAlert(type, message) {
        const alert = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        
        $('.container-fluid').prepend(alert);
        
        // 3秒後自動關閉
        setTimeout(() => {
            $('.alert').alert('close');
        }, 3000);
    }
});
</script>
{% endblock %}