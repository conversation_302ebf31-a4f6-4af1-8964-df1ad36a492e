{% extends "admin/layout.html" %}

{% block title %}系統日誌 - 管理面板{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1><i class="fas fa-file-alt"></i> 系統日誌</h1>
            <p class="text-muted">查看系統運行日誌和錯誤記錄</p>
        </div>
    </div>

    <!-- 日誌過濾控制 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-filter"></i> 日誌過濾</h5>
                </div>
                <div class="card-body">
                    <form id="log-filter-form" class="form-inline">
                        <div class="form-group mr-3">
                            <label for="log-level" class="mr-2">日誌級別:</label>
                            <select class="form-control" id="log-level">
                                <option value="all">全部</option>
                                <option value="error">錯誤 (ERROR)</option>
                                <option value="warning">警告 (WARNING)</option>
                                <option value="info">資訊 (INFO)</option>
                                <option value="debug">調試 (DEBUG)</option>
                            </select>
                        </div>
                        
                        <div class="form-group mr-3">
                            <label for="log-module" class="mr-2">模組:</label>
                            <select class="form-control" id="log-module">
                                <option value="all">全部模組</option>
                                <option value="models">模型 (models)</option>
                                <option value="views">視圖 (views)</option>
                                <option value="werkzeug">伺服器 (werkzeug)</option>
                                <option value="predictor">預測器</option>
                                <option value="data_fetcher">數據抓取</option>
                            </select>
                        </div>
                        
                        <div class="form-group mr-3">
                            <label for="log-date" class="mr-2">日期:</label>
                            <input type="date" class="form-control" id="log-date" value="{{ moment().format('YYYY-MM-DD') }}">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 過濾
                        </button>
                        
                        <button type="button" class="btn btn-secondary ml-2" id="refresh-logs">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 日誌統計 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-danger">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-circle fa-2x"></i>
                    <h4 class="card-title mt-2">錯誤</h4>
                    <h3 id="error-count">0</h3>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                    <h4 class="card-title mt-2">警告</h4>
                    <h3 id="warning-count">0</h3>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body text-center">
                    <i class="fas fa-info-circle fa-2x"></i>
                    <h4 class="card-title mt-2">資訊</h4>
                    <h3 id="info-count">0</h3>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-white bg-secondary">
                <div class="card-body text-center">
                    <i class="fas fa-list fa-2x"></i>
                    <h4 class="card-title mt-2">總計</h4>
                    <h3 id="total-count">0</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- 日誌內容 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list-ul"></i> 日誌內容</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary" id="scroll-to-top">
                            <i class="fas fa-arrow-up"></i> 頂部
                        </button>
                        <button class="btn btn-outline-secondary" id="scroll-to-bottom">
                            <i class="fas fa-arrow-down"></i> 底部
                        </button>
                        <button class="btn btn-outline-secondary" id="export-logs">
                            <i class="fas fa-download"></i> 導出
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="log-container" style="height: 600px; overflow-y: auto; background-color: #1e1e1e; color: #d4d4d4; font-family: 'Courier New', monospace; padding: 15px;">
                        <div id="log-loading" class="text-center" style="color: #888;">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="sr-only">載入中...</span>
                            </div>
                            <span class="ml-2">載入日誌中...</span>
                        </div>
                        <div id="log-content" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作區域 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools"></i> 日誌管理</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-warning" id="clear-logs">
                        <i class="fas fa-trash"></i> 清除日誌
                    </button>
                    
                    <button class="btn btn-info" id="archive-logs">
                        <i class="fas fa-archive"></i> 歸檔日誌
                    </button>
                    
                    <button class="btn btn-success" id="download-logs">
                        <i class="fas fa-download"></i> 下載完整日誌
                    </button>
                    
                    <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回儀表板
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 載入初始日誌
    loadLogs();
    
    // 自動刷新（每30秒）
    setInterval(loadLogs, 30000);
    
    // 過濾表單提交
    $('#log-filter-form').on('submit', function(e) {
        e.preventDefault();
        loadLogs();
    });
    
    // 刷新按鈕
    $('#refresh-logs').on('click', loadLogs);
    
    // 載入日誌函數
    function loadLogs() {
        const level = $('#log-level').val();
        const module = $('#log-module').val();
        const date = $('#log-date').val();
        
        $('#log-loading').show();
        $('#log-content').hide();
        
        // 模擬載入過程
        setTimeout(() => {
            const mockLogs = generateMockLogs(level, module, date);
            displayLogs(mockLogs);
            updateLogStats(mockLogs);
        }, 1000);
    }
    
    // 生成模擬日誌資料
    function generateMockLogs(level, module, date) {
        const logs = [
            { level: 'INFO', module: 'models.automated_predictor', time: '10:52:09', message: '自動化預測調度器已啟動' },
            { level: 'INFO', module: 'werkzeug', time: '10:52:09', message: ' * Debugger is active!' },
            { level: 'WARNING', module: 'models.automated_predictor', time: '10:52:00', message: '調度器已在運行中' },
            { level: 'ERROR', module: 'models.data_fetcher', time: '10:06:05', message: '獲取比賽 TB_WAS_20250829 詳細內容失敗: 400 Client Error' },
            { level: 'INFO', module: 'models.data_fetcher', time: '10:05:31', message: '2025-09-12 的比賽信息更新完成' },
            { level: 'INFO', module: 'views.admin', time: '10:05:31', message: '更新 2025-09-12: 15 → 15 場比賽' },
            { level: 'ERROR', module: 'models.calibrated_predictor_v2', time: '10:52:00', message: '載入校正模型失敗: No module named numpy._core' },
            { level: 'INFO', module: 'models.ml_predictor', time: '10:52:09', message: '模型載入完成: models/saved (3/3 models)' }
        ];
        
        return logs.filter(log => {
            if (level !== 'all' && log.level.toLowerCase() !== level) return false;
            if (module !== 'all' && !log.module.includes(module)) return false;
            return true;
        });
    }
    
    // 顯示日誌
    function displayLogs(logs) {
        const container = $('#log-content');
        container.empty();
        
        logs.forEach(log => {
            const levelClass = getLevelClass(log.level);
            const logLine = `
                <div class="log-line mb-1">
                    <span class="text-muted">[${log.time}]</span>
                    <span class="badge badge-${levelClass} badge-sm">${log.level}</span>
                    <span class="text-info">${log.module}</span>
                    <span class="text-light">- ${log.message}</span>
                </div>
            `;
            container.append(logLine);
        });
        
        $('#log-loading').hide();
        $('#log-content').show();
    }
    
    // 獲取日誌級別對應的樣式類
    function getLevelClass(level) {
        switch(level) {
            case 'ERROR': return 'danger';
            case 'WARNING': return 'warning';
            case 'INFO': return 'info';
            case 'DEBUG': return 'secondary';
            default: return 'light';
        }
    }
    
    // 更新日誌統計
    function updateLogStats(logs) {
        const stats = { ERROR: 0, WARNING: 0, INFO: 0, DEBUG: 0 };
        
        logs.forEach(log => {
            if (stats.hasOwnProperty(log.level)) {
                stats[log.level]++;
            }
        });
        
        $('#error-count').text(stats.ERROR);
        $('#warning-count').text(stats.WARNING);
        $('#info-count').text(stats.INFO);
        $('#total-count').text(logs.length);
    }
    
    // 捲動控制
    $('#scroll-to-top').on('click', function() {
        $('#log-container').scrollTop(0);
    });
    
    $('#scroll-to-bottom').on('click', function() {
        const container = $('#log-container')[0];
        container.scrollTop = container.scrollHeight;
    });
    
    // 清除日誌
    $('#clear-logs').on('click', function() {
        if (confirm('確定要清除所有日誌嗎？此操作無法復原。')) {
            showAlert('success', '日誌已清除');
            loadLogs();
        }
    });
    
    // 歸檔日誌
    $('#archive-logs').on('click', function() {
        const btn = $(this);
        btn.prop('disabled', true);
        btn.html('<i class="fas fa-spinner fa-spin"></i> 歸檔中...');
        
        setTimeout(() => {
            btn.prop('disabled', false);
            btn.html('<i class="fas fa-archive"></i> 歸檔日誌');
            showAlert('success', '日誌歸檔完成');
        }, 2000);
    });
    
    // 下載日誌
    $('#download-logs, #export-logs').on('click', function() {
        showAlert('info', '日誌下載功能開發中...');
    });
    
    // 顯示警告訊息
    function showAlert(type, message) {
        const alert = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        
        $('.container-fluid').prepend(alert);
        
        setTimeout(() => {
            $('.alert').alert('close');
        }, 3000);
    }
});
</script>

<style>
.log-line {
    font-size: 12px;
    line-height: 1.4;
}

.badge-sm {
    font-size: 0.7em;
    padding: 0.2em 0.4em;
}

#log-container {
    border: 1px solid #444;
    border-radius: 4px;
}

#log-container::-webkit-scrollbar {
    width: 8px;
}

#log-container::-webkit-scrollbar-track {
    background: #2d2d2d;
}

#log-container::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

#log-container::-webkit-scrollbar-thumb:hover {
    background: #777;
}
</style>
{% endblock %}