{% extends "admin/layout.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-database"></i> 數據管理</h5>
            </div>
            <div class="card-body">
                <!-- 全面數據更新 -->
                <div class="mb-3">
                    <h6><i class="fas fa-sync-alt"></i> 全面數據更新</h6>
                    <p class="text-muted">更新所有預測相關數據：比賽分數、BoxScore、賠率、投手統計、投打對戰等。</p>
                    <form method="POST" action="{{ url_for('admin.comprehensive_update') }}" onsubmit="return confirmAction('全面更新將會更新大量數據，包括比賽分數、BoxScore、賠率等，這可能需要10-30分鐘時間。確定要繼續嗎？')">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">回顧天數</label>
                                <input type="number" name="days_back" class="form-control" value="7" min="1" max="30">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">未來天數</label>
                                <input type="number" name="days_forward" class="form-control" value="7" min="1" max="30">
                            </div>
                        </div>
                        <div class="mb-3">
                            <h6 class="small">數據類型選擇：</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="update_boxscore" value="true" checked id="updateBoxscore">
                                <label class="form-check-label" for="updateBoxscore">
                                    📊 BoxScore 詳細數據 (打擊、投球、守備統計)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="update_odds" value="true" checked id="updateOdds">
                                <label class="form-check-label" for="updateOdds">
                                    💰 博彩賠率數據 (勝負、大小分、讓分)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="update_pitcher_stats" value="true" checked id="updatePitcher">
                                <label class="form-check-label" for="updatePitcher">
                                    ⚾ 投手統計數據 (ERA、WHIP、K/9等)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="true" checked disabled>
                                <label class="form-check-label text-muted">
                                    ⚔️ 投打對戰數據 (必選 - 預測核心數據)
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-warning btn-lg">
                            <i class="fas fa-rocket"></i> 執行全面更新
                        </button>
                        <div class="mt-2">
                            <small class="text-info">
                                <i class="fas fa-info-circle"></i> 
                                此功能會更新所有與預測相關的數據，建議在系統空閒時執行。
                            </small>
                        </div>
                    </form>
                </div>
                <hr>
                <!-- 比賽結果更新器 -->
                <div class="mb-3">
                    <h6><i class="fas fa-refresh"></i> 比賽結果更新器</h6>
                    <p class="text-muted">更新特定日期的比賽結果，解決"預定"狀態未更新的問題。</p>
                    <a href="/admin/game-updater/" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-repeat"></i> 進入比賽結果更新器
                    </a>
                </div>
                <hr>
                <!-- 歷史盤口 -->
                <div class="mb-3">
                    <h6><i class="fas fa-chart-bar"></i> 歷史盤口管理</h6>
                    <p class="text-muted">下載和管理歷史博彩盤口數據。</p>
                    <a href="{{ url_for('admin.historical_odds_dashboard') }}" class="btn btn-outline-info">進入歷史盤口管理</a>
                </div>
                <hr>
                <!-- 整月數據下載 -->
                <div class="mb-3">
                    <h6><i class="fas fa-calendar"></i> 整月數據下載</h6>
                    <p class="text-muted">下載整個月的完整數據，包括比賽、boxscore、投手數據，並檢查遺漏場次。</p>
                    <form method="POST" action="{{ url_for('admin.download_monthly_data') }}" onsubmit="return confirmAction('整月數據下載將獲取大量數據，可能需要10-30分鐘。確定要繼續嗎？')">
                        <div class="row">
                            <div class="col-md-3">
                                <select name="year" class="form-control">
                                    <option value="2024">2024年</option>
                                    <option value="2025" selected>2025年</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="month" class="form-control">
                                    <option value="1">1月</option>
                                    <option value="2">2月</option>
                                    <option value="3">3月</option>
                                    <option value="4">4月</option>
                                    <option value="5">5月</option>
                                    <option value="6">6月</option>
                                    <option value="7">7月</option>
                                    <option value="8" selected>8月</option>
                                    <option value="9">9月</option>
                                    <option value="10">10月</option>
                                    <option value="11">11月</option>
                                    <option value="12">12月</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary">下載整月數據</button>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ url_for('admin.progress_monitor_page') }}" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-tasks"></i> 查看進度
                                </a>
                            </div>
                        </div>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" name="include_boxscore" value="true" checked id="includeBoxscore">
                            <label class="form-check-label" for="includeBoxscore">
                                包含詳細boxscore數據
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="auto_fix_missing" value="true" checked id="autoFixMissing">
                            <label class="form-check-label" for="autoFixMissing">
                                自動修復缺失場次
                            </label>
                        </div>
                    </form>
                    <div class="mt-2">
                        <small class="text-info">
                            <i class="fas fa-info-circle"></i> 
                            提示：點擊下載後，可在新窗口中查看實時進度。任務將在後台執行，您可以關閉此頁面。
                        </small>
                    </div>
                </div>
                <hr>
                <!-- 數據完整性檢查 -->
                <div class="mb-3">
                    <h6><i class="fas fa-search"></i> 數據完整性檢查</h6>
                    <p class="text-muted">檢查指定月份的數據完整性，找出遺漏的比賽場次。</p>
                    <form method="POST" action="{{ url_for('admin.check_data_completeness') }}">
                        <div class="row">
                            <div class="col-md-4">
                                <select name="year" class="form-control">
                                    <option value="2024">2024年</option>
                                    <option value="2025" selected>2025年</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select name="month" class="form-control">
                                    <option value="1">1月</option>
                                    <option value="2">2月</option>
                                    <option value="3">3月</option>
                                    <option value="4">4月</option>
                                    <option value="5">5月</option>
                                    <option value="6">6月</option>
                                    <option value="7">7月</option>
                                    <option value="8" selected>8月</option>
                                    <option value="9">9月</option>
                                    <option value="10">10月</option>
                                    <option value="11">11月</option>
                                    <option value="12">12月</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-outline-info">檢查完整性</button>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">或查看現有報告:</small>
                            <a href="{{ url_for('admin.data_completeness_report', year=2025, month=8) }}" class="btn btn-link btn-sm">2025年8月報告</a>
                            <a href="{{ url_for('admin.data_completeness_report', year=2025, month=9) }}" class="btn btn-link btn-sm">2025年9月報告</a>
                        </div>
                    </form>
                </div>
                <hr>
                <!-- 其他數據操作 -->
                <h6><i class="fas fa-cogs"></i> 其他數據操作</h6>
                <div class="row">
                    <div class="col-md-6">
                        <form method="POST" action="{{ url_for('admin.update_data') }}" class="mb-2">
                            <div class="input-group">
                                <input type="number" class="form-control" name="days_back" value="3" min="1" max="30" placeholder="更新天數">
                                <button type="submit" class="btn btn-info">更新基本數據</button>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <form method="POST" action="{{ url_for('admin.fetch_detailed_data') }}" class="mb-2">
                            <div class="input-group">
                                <input type="date" class="form-control" name="date" value="{{ yesterday.isoformat() }}">
                                <button type="submit" class="btn btn-warning">獲取詳細數據</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 說明</h6>
            </div>
            <div class="card-body">
                <p>此頁面提供更深入、耗時更長的數據管理功能。</p>
                <ul>
                    <li><strong>全面數據更新:</strong> 用於大規模回補或修復數據。</li>
                    <li><strong>歷史盤口管理:</strong> 專門處理博彩賠率數據。</li>
                    <li><strong>其他數據操作:</strong> 提供更細粒度的單項數據更新功能。</li>
                </ul>
                 <a href="{{ url_for('admin.database_stats') }}" class="btn btn-outline-secondary mt-3">查看資料庫統計</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
