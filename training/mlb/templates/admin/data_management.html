{% extends "admin/layout.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-database"></i> 數據管理</h5>
            </div>
            <div class="card-body">
                <!-- 全面數據更新 -->
                <div class="mb-3">
                    <h6><i class="fas fa-sync-alt"></i> 全面數據更新</h6>
                    <p class="text-muted">更新最近所有資料（過去7天到未來7天）並刷新未來行程。此操作可能需要幾分鐘。</p>
                    <form method="POST" action="{{ url_for('admin.comprehensive_update') }}" onsubmit="return confirmAction('全面更新將會更新過去7天到未來7天的所有數據，這可能需要幾分鐘時間。確定要繼續嗎？')">
                        <button type="submit" class="btn btn-warning">執行全面更新</button>
                    </form>
                </div>
                <hr>
                <!-- 歷史盤口 -->
                <div class="mb-3">
                    <h6><i class="fas fa-chart-bar"></i> 歷史盤口管理</h6>
                    <p class="text-muted">下載和管理歷史博彩盤口數據。</p>
                    <a href="{{ url_for('admin.historical_odds_dashboard') }}" class="btn btn-outline-info">進入歷史盤口管理</a>
                </div>
                <hr>
                <!-- 其他數據操作 -->
                <h6><i class="fas fa-cogs"></i> 其他數據操作</h6>
                <div class="row">
                    <div class="col-md-6">
                        <form method="POST" action="{{ url_for('admin.update_data') }}" class="mb-2">
                            <div class="input-group">
                                <input type="number" class="form-control" name="days_back" value="3" min="1" max="30" placeholder="更新天數">
                                <button type="submit" class="btn btn-info">更新基本數據</button>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <form method="POST" action="{{ url_for('admin.fetch_detailed_data') }}" class="mb-2">
                            <div class="input-group">
                                <input type="date" class="form-control" name="date" value="{{ yesterday.isoformat() }}">
                                <button type="submit" class="btn btn-warning">獲取詳細數據</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 說明</h6>
            </div>
            <div class="card-body">
                <p>此頁面提供更深入、耗時更長的數據管理功能。</p>
                <ul>
                    <li><strong>全面數據更新:</strong> 用於大規模回補或修復數據。</li>
                    <li><strong>歷史盤口管理:</strong> 專門處理博彩賠率數據。</li>
                    <li><strong>其他數據操作:</strong> 提供更細粒度的單項數據更新功能。</li>
                </ul>
                 <a href="{{ url_for('admin.database_stats') }}" class="btn btn-outline-secondary mt-3">查看資料庫統計</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
