{% extends "admin/layout.html" %}

{% block title %}資料庫統計 - 管理面板{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1><i class="fas fa-chart-bar"></i> 資料庫統計</h1>
            <p class="text-muted">檢視系統資料庫的詳細統計資訊</p>
        </div>
    </div>

    <!-- 基本統計 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <div class="card-title">
                        <i class="fas fa-baseball-ball text-primary fa-2x"></i>
                        <h4 class="mt-2">總比賽數</h4>
                    </div>
                    <h3 class="text-primary">{{ stats.total_games or 0 }}</h3>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <div class="card-title">
                        <i class="fas fa-users text-success fa-2x"></i>
                        <h4 class="mt-2">總球隊數</h4>
                    </div>
                    <h3 class="text-success">{{ stats.total_teams or 0 }}</h3>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <div class="card-title">
                        <i class="fas fa-crystal-ball text-info fa-2x"></i>
                        <h4 class="mt-2">總預測數</h4>
                    </div>
                    <h3 class="text-info">{{ stats.total_predictions or 0 }}</h3>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <div class="card-title">
                        <i class="fas fa-user text-warning fa-2x"></i>
                        <h4 class="mt-2">總球員數</h4>
                    </div>
                    <h3 class="text-warning">{{ stats.total_players or 0 }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- 詳細分析 -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 資料分佈概況</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>資料類型</th>
                                    <th>數量</th>
                                    <th>狀態</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>比賽記錄</td>
                                    <td>{{ stats.total_games or 0 }}</td>
                                    <td><span class="badge badge-success">正常</span></td>
                                </tr>
                                <tr>
                                    <td>球隊資料</td>
                                    <td>{{ stats.total_teams or 0 }}</td>
                                    <td><span class="badge badge-success">正常</span></td>
                                </tr>
                                <tr>
                                    <td>預測結果</td>
                                    <td>{{ stats.total_predictions or 0 }}</td>
                                    <td><span class="badge badge-info">活躍</span></td>
                                </tr>
                                <tr>
                                    <td>球員統計</td>
                                    <td>{{ stats.total_players or 0 }}</td>
                                    <td><span class="badge badge-warning">更新中</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> 系統資訊</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">資料庫類型</dt>
                        <dd class="col-sm-8">SQLite</dd>
                        
                        <dt class="col-sm-4">更新頻率</dt>
                        <dd class="col-sm-8">每小時自動更新</dd>
                        
                        <dt class="col-sm-4">最後更新</dt>
                        <dd class="col-sm-8">{{ moment().format('YYYY-MM-DD HH:mm') }}</dd>
                        
                        <dt class="col-sm-4">資料完整性</dt>
                        <dd class="col-sm-8">
                            {% if stats.total_games > 0 and stats.total_teams > 0 %}
                                <span class="badge badge-success">良好</span>
                            {% else %}
                                <span class="badge badge-warning">需要檢查</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作區域 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools"></i> 管理操作</h5>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('admin.data_management') }}" class="btn btn-primary">
                        <i class="fas fa-database"></i> 數據管理
                    </a>
                    <a href="{{ url_for('admin.data_completeness_report') }}" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> 完整性報告
                    </a>
                    <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回儀表板
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 自動刷新統計資料 (每 30 秒)
setInterval(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}