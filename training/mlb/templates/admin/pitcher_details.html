{% extends "base.html" %}

{% block title %}投手詳細統計 - {{ pitcher_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-baseball-ball"></i> 投手詳細統計</h1>
                <a href="{{ url_for('admin.check_pitcher_announcements') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回投手公告
                </a>
            </div>
        </div>
    </div>

    {% if pitcher_stats.error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> 
        獲取投手統計時發生錯誤: {{ pitcher_stats.error }}
    </div>
    {% else %}

    <!-- 投手基本信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4><i class="fas fa-user"></i> {{ pitcher_stats.basic_info.name }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <strong>球隊:</strong><br>
                            <span class="badge bg-info">{{ pitcher_stats.basic_info.team }}</span>
                        </div>
                        <div class="col-md-2">
                            <strong>位置:</strong><br>
                            {{ pitcher_stats.basic_info.position }}
                        </div>
                        <div class="col-md-2">
                            <strong>投球手:</strong><br>
                            {{ pitcher_stats.basic_info.throws }}
                        </div>
                        <div class="col-md-2">
                            <strong>年齡:</strong><br>
                            {{ pitcher_stats.basic_info.age }}
                        </div>
                        <div class="col-md-2">
                            <strong>身高:</strong><br>
                            {{ pitcher_stats.basic_info.height }}
                        </div>
                        <div class="col-md-2">
                            <strong>體重:</strong><br>
                            {{ pitcher_stats.basic_info.weight }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 生涯統計 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> 生涯統計</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3>{{ pitcher_stats.career_stats.games }}</h3>
                                <p class="text-muted">出賽場次</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3>{{ "%.2f"|format(pitcher_stats.career_stats.era) }}</h3>
                                <p class="text-muted">防禦率 (ERA)</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3>{{ "%.2f"|format(pitcher_stats.career_stats.whip) }}</h3>
                                <p class="text-muted">WHIP</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3>{{ pitcher_stats.career_stats.strikeouts }}</h3>
                                <p class="text-muted">三振數</p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-2">
                            <strong>勝-敗:</strong><br>
                            {{ pitcher_stats.career_stats.wins }}-{{ pitcher_stats.career_stats.losses }}
                        </div>
                        <div class="col-md-2">
                            <strong>救援成功:</strong><br>
                            {{ pitcher_stats.career_stats.saves }}
                        </div>
                        <div class="col-md-2">
                            <strong>投球局數:</strong><br>
                            {{ "%.1f"|format(pitcher_stats.career_stats.innings_pitched) }}
                        </div>
                        <div class="col-md-2">
                            <strong>K/9:</strong><br>
                            {{ "%.2f"|format(pitcher_stats.career_stats.k_per_9) }}
                        </div>
                        <div class="col-md-2">
                            <strong>BB/9:</strong><br>
                            {{ "%.2f"|format(pitcher_stats.career_stats.bb_per_9) }}
                        </div>
                        <div class="col-md-2">
                            <strong>被全壘打:</strong><br>
                            {{ pitcher_stats.career_stats.home_runs_allowed }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近表現 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-alt"></i> 最近5場比賽</h5>
                </div>
                <div class="card-body">
                    {% if pitcher_stats.recent_performance.last_5_games %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>對手</th>
                                    <th>局數</th>
                                    <th>ERA</th>
                                    <th>K</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for game in pitcher_stats.recent_performance.last_5_games %}
                                <tr>
                                    <td>{{ game.date }}</td>
                                    <td><small>{{ game.opponent }}</small></td>
                                    <td>{{ "%.1f"|format(game.innings_pitched) }}</td>
                                    <td>{{ "%.2f"|format(game.era) }}</td>
                                    <td>{{ game.strikeouts }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">暫無最近比賽數據</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 逐年統計</h5>
                </div>
                <div class="card-body">
                    {% if pitcher_stats.season_by_season %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>年份</th>
                                    <th>場次</th>
                                    <th>勝-敗</th>
                                    <th>ERA</th>
                                    <th>K</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for season in pitcher_stats.season_by_season %}
                                <tr>
                                    <td>{{ season.season }}</td>
                                    <td>{{ season.games }}</td>
                                    <td>{{ season.wins }}-{{ season.losses }}</td>
                                    <td>{{ "%.2f"|format(season.era) }}</td>
                                    <td>{{ season.strikeouts }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">暫無逐年統計數據</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 對各隊統計 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> 對各隊表現</h5>
                </div>
                <div class="card-body">
                    {% if pitcher_stats.vs_teams_stats %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>對手球隊</th>
                                    <th>出賽場次</th>
                                    <th>防禦率</th>
                                    <th>三振數</th>
                                    <th>投球局數</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for team, stats in pitcher_stats.vs_teams_stats.items() %}
                                <tr>
                                    <td><strong>{{ team }}</strong></td>
                                    <td>{{ stats.games }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if stats.era <= 3.00 %}bg-success
                                            {% elif stats.era <= 4.50 %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ "%.2f"|format(stats.era) }}
                                        </span>
                                    </td>
                                    <td>{{ stats.strikeouts }}</td>
                                    <td>{{ "%.1f"|format(stats.innings_pitched) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">暫無對各隊統計數據</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 對打者統計 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-crosshairs"></i> 對打者統計</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4>{{ pitcher_stats.vs_batters_stats.total_batters_faced }}</h4>
                                <p class="text-muted">面對打者數</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4>{{ "%.3f"|format(pitcher_stats.vs_batters_stats.avg_against) }}</h4>
                                <p class="text-muted">被打擊率</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4>{{ "%.1f"|format(pitcher_stats.vs_batters_stats.strikeout_rate) }}%</h4>
                                <p class="text-muted">三振率</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4>{{ "%.1f"|format(pitcher_stats.vs_batters_stats.walk_rate) }}%</h4>
                                <p class="text-muted">保送率</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i> 
                        詳細的投手對打者歷史對戰數據正在開發中，將包含具體的對戰記錄和表現分析。
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% endif %}
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
    border-top: none;
    font-weight: 600;
}

.badge {
    font-size: 0.875em;
}

.text-center h3, .text-center h4 {
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.text-muted {
    font-size: 0.875em;
}
</style>
{% endblock %}
