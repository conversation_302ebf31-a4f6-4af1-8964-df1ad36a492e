{% extends "base.html" %}

{% block title %}2025年預測啟動器 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-rocket"></i> 2025年MLB預測啟動器</h2>
                <div>
                    <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回管理面板
                    </a>
                    <a href="{{ url_for('admin.check_pitcher_announcements') }}" class="btn btn-info">
                        <i class="fas fa-bullhorn"></i> 投手公告檢查
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 預測可行性分析 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> 預測可行性分析 ({{ report.date }})</h5>
                    <small class="text-muted">生成時間: {{ report.generated_at }}</small>
                </div>
                <div class="card-body">
                    {% set feasibility = report.readiness_analysis.prediction_feasibility %}
                    
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="display-4 
                                    {% if feasibility.status == 'excellent' %}text-success
                                    {% elif feasibility.status == 'good' %}text-primary
                                    {% elif feasibility.status == 'fair' %}text-warning
                                    {% else %}text-danger{% endif %}">
                                    {{ feasibility.overall_score }}
                                </div>
                                <p class="text-muted">總體評分</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3>
                                    {% if feasibility.status == 'excellent' %}
                                        <span class="badge bg-success">優秀</span>
                                    {% elif feasibility.status == 'good' %}
                                        <span class="badge bg-primary">良好</span>
                                    {% elif feasibility.status == 'fair' %}
                                        <span class="badge bg-warning">一般</span>
                                    {% else %}
                                        <span class="badge bg-danger">不足</span>
                                    {% endif %}
                                </h3>
                                <p class="text-muted">預測狀態</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3>{{ feasibility.ready_games }}</h3>
                                <p class="text-muted">準備好的比賽</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3>{{ feasibility.total_games }}</h3>
                                <p class="text-muted">總比賽數</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 詳細評分 -->
                    <div class="row">
                        <div class="col-md-4">
                            <h6>數據質量 (40分)</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-info" style="width: {{ (feasibility.data_quality / 40 * 100) }}%">
                                    {{ feasibility.data_quality }}/40
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>時機 (30分)</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-warning" style="width: {{ (feasibility.timing / 30 * 100) }}%">
                                    {{ feasibility.timing }}/30
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>覆蓋率 (30分)</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-success" style="width: {{ (feasibility.coverage / 30 * 100) }}%">
                                    {{ feasibility.coverage }}/30
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 行動計劃 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tasks"></i> 行動計劃</h5>
                </div>
                <div class="card-body">
                    {% set action_plan = report.action_plan %}
                    
                    <div class="alert 
                        {% if feasibility.status == 'excellent' %}alert-success
                        {% elif feasibility.status == 'good' %}alert-primary
                        {% elif feasibility.status == 'fair' %}alert-warning
                        {% else %}alert-danger{% endif %}">
                        <h6><i class="fas fa-exclamation-circle"></i> 立即行動:</h6>
                        <p class="mb-0">{{ action_plan.immediate_action }}</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-clock"></i> 最佳預測時間:</h6>
                            <p class="text-primary">{{ action_plan.optimal_prediction_time }}</p>
                            
                            <h6><i class="fas fa-shield-alt"></i> 風險評估:</h6>
                            <p class="text-muted">{{ action_plan.risk_assessment }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-list-ol"></i> 下一步驟:</h6>
                            <ol>
                                {% for step in action_plan.next_steps %}
                                <li>{{ step }}</li>
                                {% endfor %}
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 預測控制面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-control"></i> 預測控制面板</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="prediction_date">預測日期:</label>
                                <input type="date" id="prediction_date" class="form-control" value="{{ report.date }}">
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="force_prediction">
                                <label class="form-check-label" for="force_prediction">
                                    強制執行預測 (忽略準備度檢查)
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                {% if feasibility.status in ['excellent', 'good'] %}
                                <button id="launch_prediction_btn" class="btn btn-success btn-lg">
                                    <i class="fas fa-rocket"></i> 啟動預測
                                </button>
                                {% else %}
                                <button id="launch_prediction_btn" class="btn btn-warning btn-lg">
                                    <i class="fas fa-exclamation-triangle"></i> 條件預測
                                </button>
                                {% endif %}
                                
                                <button id="refresh_analysis_btn" class="btn btn-outline-primary">
                                    <i class="fas fa-sync-alt"></i> 重新分析
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 預測結果顯示區域 -->
    <div id="prediction_results" class="row" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 預測結果</h5>
                </div>
                <div class="card-body" id="prediction_results_content">
                    <!-- 預測結果將在這裡顯示 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 投手公告詳情 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> 投手公告詳情</h5>
                </div>
                <div class="card-body">
                    {% set announcement_data = report.readiness_analysis.announcement_data %}
                    
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4>{{ "%.1f"|format(announcement_data.announcement_coverage) }}%</h4>
                                <p class="text-muted">公告覆蓋率</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4>{{ announcement_data.summary.fully_announced }}</h4>
                                <p class="text-muted">完全公告</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4>{{ announcement_data.summary.partially_announced }}</h4>
                                <p class="text-muted">部分公告</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4>{{ announcement_data.summary.no_announcements }}</h4>
                                <p class="text-muted">未公告</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ url_for('admin.check_pitcher_announcements') }}" class="btn btn-outline-primary">
                            <i class="fas fa-eye"></i> 查看詳細投手公告
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const launchBtn = document.getElementById('launch_prediction_btn');
    const refreshBtn = document.getElementById('refresh_analysis_btn');
    const resultsDiv = document.getElementById('prediction_results');
    const resultsContent = document.getElementById('prediction_results_content');
    
    // 啟動預測
    launchBtn.addEventListener('click', function() {
        const date = document.getElementById('prediction_date').value;
        const force = document.getElementById('force_prediction').checked;
        
        launchBtn.disabled = true;
        launchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 執行中...';
        
        fetch('{{ url_for("admin.launch_predictions") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                date: date,
                force: force
            })
        })
        .then(response => response.json())
        .then(data => {
            displayPredictionResults(data);
            launchBtn.disabled = false;
            launchBtn.innerHTML = '<i class="fas fa-rocket"></i> 啟動預測';
        })
        .catch(error => {
            console.error('Error:', error);
            alert('預測執行失敗: ' + error);
            launchBtn.disabled = false;
            launchBtn.innerHTML = '<i class="fas fa-rocket"></i> 啟動預測';
        });
    });
    
    // 重新分析
    refreshBtn.addEventListener('click', function() {
        location.reload();
    });
    
    function displayPredictionResults(data) {
        let html = '';
        
        if (data.success) {
            const predictions = data.predictions;
            html = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 預測執行成功!</h6>
                    <p>成功預測 ${predictions.successful_predictions}/${predictions.total_games} 場比賽</p>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>${predictions.successful_predictions}</h4>
                            <p class="text-muted">成功預測</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>${predictions.failed_predictions}</h4>
                            <p class="text-muted">失敗預測</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>${predictions.summary ? predictions.summary.average_confidence.toFixed(2) : 'N/A'}</h4>
                            <p class="text-muted">平均信心度</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>${predictions.summary ? predictions.summary.high_confidence_predictions : 0}</h4>
                            <p class="text-muted">高信心預測</p>
                        </div>
                    </div>
                </div>
            `;
            
            if (predictions.predictions && predictions.predictions.length > 0) {
                html += `
                    <h6>預測詳情:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>比賽</th>
                                    <th>投手對戰</th>
                                    <th>預測結果</th>
                                    <th>信心度</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                predictions.predictions.forEach(pred => {
                    html += `
                        <tr>
                            <td>${pred.matchup}</td>
                            <td>
                                <small>
                                    主: ${pred.pitcher_info.home_pitcher || '未知'}<br>
                                    客: ${pred.pitcher_info.away_pitcher || '未知'}
                                </small>
                            </td>
                            <td>
                                <small>
                                    主隊: ${pred.prediction.home_score || 'N/A'}<br>
                                    客隊: ${pred.prediction.away_score || 'N/A'}
                                </small>
                            </td>
                            <td>
                                <span class="badge ${pred.confidence >= 0.75 ? 'bg-success' : pred.confidence >= 0.6 ? 'bg-warning' : 'bg-danger'}">
                                    ${(pred.confidence * 100).toFixed(1)}%
                                </span>
                            </td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
            }
        } else {
            html = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> 預測執行失敗</h6>
                    <p>${data.reason || data.error}</p>
                    ${data.recommendation ? `<p><strong>建議:</strong> ${data.recommendation}</p>` : ''}
                </div>
            `;
        }
        
        resultsContent.innerHTML = html;
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    }
});
</script>

<style>
.display-4 {
    font-size: 2.5rem;
    font-weight: 300;
}

.progress {
    height: 20px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}
</style>
{% endblock %}
