{% extends "base.html" %}

{% block title %}管理員後台{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4"><i class="fas fa-cogs"></i> 管理員後台</h2>

            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin.admin_dashboard' %}active{% endif %}" href="{{ url_for('admin.admin_dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i> 主儀表板
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin.daily_operations' %}active{% endif %}" href="{{ url_for('admin.daily_operations') }}">
                        <i class="fas fa-calendar-day"></i> 日常操作
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin.data_management' %}active{% endif %}" href="{{ url_for('admin.data_management') }}">
                        <i class="fas fa-database"></i> 數據管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin.model_predictions' %}active{% endif %}" href="{{ url_for('admin.model_predictions') }}">
                        <i class="fas fa-brain"></i> 模型與預測
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin.system_management' %}active{% endif %}" href="{{ url_for('admin.system_management') }}">
                        <i class="fas fa-server"></i> 系統設定
                    </a>
                </li>
            </ul>

            {% block admin_content %}{% endblock %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function confirmAction(message) {
    return confirm(message || '您確定要執行此操作嗎？');
}
</script>
{% endblock %}
