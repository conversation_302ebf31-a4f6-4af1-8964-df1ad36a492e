{% extends "admin/layout.html" %}

{% block admin_content %}
<div class="row">
    <!-- 模型管理 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="fas fa-brain"></i> 模型管理</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">管理機器學習模型，包括重新訓練和性能評估。</p>
                <form method="POST" action="{{ url_for('admin.retrain_models') }}" class="mb-3">
                    <div class="input-group">
                        <input type="number" class="form-control" name="days_back" value="60" min="30" max="365" placeholder="訓練天數">
                        <button type="submit" class="btn btn-warning">重新訓練模型</button>
                    </div>
                </form>
                <a href="{{ url_for('admin.model_performance') }}" class="btn btn-outline-info">查看模型性能</a>
                <a href="{{ url_for('admin.prediction_methodology') }}" class="btn btn-outline-secondary">預測方法論</a>
            </div>
        </div>
    </div>

    <!-- 2025年預測 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="fas fa-rocket"></i> 2025年預測中心</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">專為2025賽季設計的智能預測系統，包含賽前檢查與啟動功能。</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin.check_pitcher_announcements') }}" class="btn btn-outline-warning">
                        <i class="fas fa-bullhorn"></i> 投手公告檢查
                    </a>
                    <a href="{{ url_for('admin.prediction_readiness') }}" class="btn btn-outline-info">
                        <i class="fas fa-clipboard-check"></i> 預測準備度檢查
                    </a>
                    <a href="{{ url_for('admin.prediction_launcher') }}" class="btn btn-success">
                        <i class="fas fa-rocket"></i> 預測啟動器
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
