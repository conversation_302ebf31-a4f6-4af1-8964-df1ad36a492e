{% extends "base.html" %}

{% block title %}模型性能分析{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line"></i> 模型性能分析</h2>
                <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回管理面板
                </a>
            </div>
        </div>
    </div>

    <!-- 性能概覽 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">7天準確率</h5>
                    <h2 class="text-primary">{{ "%.1f" | format(performance_data['7_days'].accuracy * 100) }}%</h2>
                    <p class="text-muted">{{ performance_data['7_days'].total_predictions }} 場預測</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">30天準確率</h5>
                    <h2 class="text-success">{{ "%.1f" | format(performance_data['30_days'].accuracy * 100) }}%</h2>
                    <p class="text-muted">{{ performance_data['30_days'].total_predictions }} 場預測</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">90天準確率</h5>
                    <h2 class="text-info">{{ "%.1f" | format(performance_data['90_days'].accuracy * 100) }}%</h2>
                    <p class="text-muted">{{ performance_data['90_days'].total_predictions }} 場預測</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 詳細統計 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> 詳細性能統計</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>時間段</th>
                                    <th>總預測數</th>
                                    <th>正確預測</th>
                                    <th>準確率</th>
                                    <th>平均信心度</th>
                                    <th>平均分差誤差</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-primary">最近7天</span></td>
                                    <td>{{ performance_data['7_days'].total_predictions }}</td>
                                    <td>{{ performance_data['7_days'].correct_predictions }}</td>
                                    <td>
                                        <strong>{{ "%.1f" | format(performance_data['7_days'].accuracy * 100) }}%</strong>
                                        {% if performance_data['7_days'].accuracy >= 0.6 %}
                                            <i class="fas fa-arrow-up text-success"></i>
                                        {% elif performance_data['7_days'].accuracy >= 0.5 %}
                                            <i class="fas fa-minus text-warning"></i>
                                        {% else %}
                                            <i class="fas fa-arrow-down text-danger"></i>
                                        {% endif %}
                                    </td>
                                    <td>{{ "%.3f" | format(performance_data['7_days'].get('average_confidence', 0)) }}</td>
                                    <td>{{ "%.2f" | format(performance_data['7_days'].get('average_score_error', 0)) }}</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">最近30天</span></td>
                                    <td>{{ performance_data['30_days'].total_predictions }}</td>
                                    <td>{{ performance_data['30_days'].correct_predictions }}</td>
                                    <td>
                                        <strong>{{ "%.1f" | format(performance_data['30_days'].accuracy * 100) }}%</strong>
                                        {% if performance_data['30_days'].accuracy >= 0.6 %}
                                            <i class="fas fa-arrow-up text-success"></i>
                                        {% elif performance_data['30_days'].accuracy >= 0.5 %}
                                            <i class="fas fa-minus text-warning"></i>
                                        {% else %}
                                            <i class="fas fa-arrow-down text-danger"></i>
                                        {% endif %}
                                    </td>
                                    <td>{{ "%.3f" | format(performance_data['30_days'].get('average_confidence', 0)) }}</td>
                                    <td>{{ "%.2f" | format(performance_data['30_days'].get('average_score_error', 0)) }}</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-info">最近90天</span></td>
                                    <td>{{ performance_data['90_days'].total_predictions }}</td>
                                    <td>{{ performance_data['90_days'].correct_predictions }}</td>
                                    <td>
                                        <strong>{{ "%.1f" | format(performance_data['90_days'].accuracy * 100) }}%</strong>
                                        {% if performance_data['90_days'].accuracy >= 0.6 %}
                                            <i class="fas fa-arrow-up text-success"></i>
                                        {% elif performance_data['90_days'].accuracy >= 0.5 %}
                                            <i class="fas fa-minus text-warning"></i>
                                        {% else %}
                                            <i class="fas fa-arrow-down text-danger"></i>
                                        {% endif %}
                                    </td>
                                    <td>{{ "%.3f" | format(performance_data['90_days'].get('average_confidence', 0)) }}</td>
                                    <td>{{ "%.2f" | format(performance_data['90_days'].get('average_score_error', 0)) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 性能趨勢圖 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-area"></i> 性能趨勢</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>準確率趨勢</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-primary" role="progressbar"
                                     style="width: {{ performance_data['7_days'].accuracy * 100 }}%">
                                    7天: {{ "%.1f" | format(performance_data['7_days'].accuracy * 100) }}%
                                </div>
                            </div>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-success" role="progressbar"
                                     style="width: {{ performance_data['30_days'].accuracy * 100 }}%">
                                    30天: {{ "%.1f" | format(performance_data['30_days'].accuracy * 100) }}%
                                </div>
                            </div>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-info" role="progressbar"
                                     style="width: {{ performance_data['90_days'].accuracy * 100 }}%">
                                    90天: {{ "%.1f" | format(performance_data['90_days'].accuracy * 100) }}%
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>預測量趨勢</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>7天</span>
                                <span class="badge bg-primary">{{ performance_data['7_days'].total_predictions }} 場</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>30天</span>
                                <span class="badge bg-success">{{ performance_data['30_days'].total_predictions }} 場</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>90天</span>
                                <span class="badge bg-info">{{ performance_data['90_days'].total_predictions }} 場</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模型建議 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> 模型優化建議</h5>
                </div>
                <div class="card-body">
                    {% set recent_accuracy = performance_data['7_days'].accuracy %}
                    {% set long_term_accuracy = performance_data['90_days'].accuracy %}
                    
                    {% if recent_accuracy >= 0.65 %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <strong>模型表現優秀！</strong> 最近7天準確率達到 {{ "%.1f" | format(recent_accuracy * 100) }}%，建議保持當前配置。
                        </div>
                    {% elif recent_accuracy >= 0.55 %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>模型表現良好。</strong> 最近7天準確率為 {{ "%.1f" | format(recent_accuracy * 100) }}%，可考慮微調參數以提升性能。
                        </div>
                    {% else %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i>
                            <strong>模型需要優化！</strong> 最近7天準確率僅 {{ "%.1f" | format(recent_accuracy * 100) }}%，建議重新訓練模型或檢查數據質量。
                        </div>
                    {% endif %}
                    
                    {% if recent_accuracy < long_term_accuracy - 0.05 %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>性能下降趨勢。</strong> 短期準確率低於長期平均，建議檢查最近的數據變化或模型漂移。
                        </div>
                    {% endif %}
                    
                    <div class="mt-3">
                        <h6>建議操作：</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-sync text-primary"></i> 定期重新訓練模型（建議每月一次）</li>
                            <li><i class="fas fa-database text-success"></i> 確保數據質量和完整性</li>
                            <li><i class="fas fa-chart-line text-info"></i> 監控特徵重要性變化</li>
                            <li><i class="fas fa-cog text-warning"></i> 根據季節性調整模型參數</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 自動刷新頁面（每5分鐘）
setTimeout(function() {
    location.reload();
}, 300000);

// 添加工具提示
$(document).ready(function() {
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %}
