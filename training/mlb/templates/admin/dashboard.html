{% extends "admin/layout.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4>主儀表板</h4>
            <button class="btn btn-outline-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> 刷新狀態
            </button>
        </div>
    </div>
</div>

<!-- 系統狀態卡片 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-robot fa-2x mb-2 {% if system_status.get('scheduler_running') %}text-success{% else %}text-danger{% endif %}"></i>
                <h6>調度器狀態</h6>
                <span class="badge {% if system_status.get('scheduler_running') %}bg-success{% else %}bg-danger{% endif %}">
                    {% if system_status.get('scheduler_running') %}運行中{% else %}已停止{% endif %}
                </span>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-brain fa-2x mb-2 {% if system_status.get('models_ready') %}text-success{% else %}text-warning{% endif %}"></i>
                <h6>模型狀態</h6>
                <span class="badge {% if system_status.get('models_ready') %}bg-success{% else %}bg-warning{% endif %}">
                    {% if system_status.get('models_ready') %}已就緒{% else %}未就緒{% endif %}
                </span>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-chart-line fa-2x mb-2 text-info"></i>
                <h6>24小時預測</h6>
                <span class="fs-4 fw-bold">{{ system_status.get('recent_predictions_24h', 0) }}</span>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-bullseye fa-2x mb-2 text-primary"></i>
                <h6>7天準確率</h6>
                <span class="fs-4 fw-bold">
                    {% if system_status.get('accuracy_stats') and system_status.accuracy_stats.get('overall_accuracy') %}
                        {{ system_status.accuracy_stats.overall_accuracy }}%
                    {% else %}
                        N/A
                    {% endif %}
                </span>
            </div>
        </div>
    </div>
</div>

<!-- 系統信息 -->
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 系統詳細資訊</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td>最後檢查時間:</td>
                                <td><small>{% if system_status.get('last_check') %}{{ system_status.last_check[:19] }}{% else %}未知{% endif %}</small></td>
                            </tr>
                            {% if system_status.get('training_summary') %}
                            <tr>
                                <td>最後訓練日期:</td>
                                <td><small>{% if system_status.training_summary.get('latest_training_date') %}{{ system_status.training_summary.latest_training_date[:10] }}{% else %}未訓練{% endif %}</small></td>
                            </tr>
                            <tr>
                                <td>訓練樣本數:</td>
                                <td><small>{{ system_status.training_summary.get('training_samples', 0) }}</small></td>
                            </tr>
                            <tr>
                                <td>模型特徵數量:</td>
                                <td><small>{{ system_status.training_summary.get('features_count', 0) }}</small></td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        {% if system_status.get('accuracy_stats') %}
                        <h6>準確率統計</h6>
                        <div class="progress mb-2" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ system_status.accuracy_stats.get('overall_accuracy', 0) }}%">
                                {{ system_status.accuracy_stats.get('overall_accuracy', 0) }}%
                            </div>
                        </div>
                        <small class="text-muted">
                            總預測: {{ system_status.accuracy_stats.get('total_predictions', 0) }} | 
                            正確預測: {{ system_status.accuracy_stats.get('correct_predictions', 0) }} | 
                            平均誤差: {{ system_status.accuracy_stats.get('average_score_error', 0) }}
                        </small>
                        {% else %}
                        <p>暫無準確率統計數據。</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
