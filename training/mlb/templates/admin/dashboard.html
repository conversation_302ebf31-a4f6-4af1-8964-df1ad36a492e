{% extends "base.html" %}

{% block title %}管理員儀表板{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cogs"></i> 管理員儀表板</h2>
                <div class="btn-group">
                    <button class="btn btn-outline-primary" onclick="refreshStatus()">
                        <i class="fas fa-sync-alt"></i> 刷新狀態
                    </button>
                </div>
            </div>

            <!-- 系統狀態卡片 -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-robot fa-2x mb-2 
                                {% if system_status.get('scheduler_running') %}text-success
                                {% else %}text-danger{% endif %}"></i>
                            <h6>調度器狀態</h6>
                            <span class="badge 
                                {% if system_status.get('scheduler_running') %}bg-success
                                {% else %}bg-danger{% endif %}">
                                {% if system_status.get('scheduler_running') %}運行中{% else %}已停止{% endif %}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-brain fa-2x mb-2 
                                {% if system_status.get('models_ready') %}text-success
                                {% else %}text-warning{% endif %}"></i>
                            <h6>模型狀態</h6>
                            <span class="badge 
                                {% if system_status.get('models_ready') %}bg-success
                                {% else %}bg-warning{% endif %}">
                                {% if system_status.get('models_ready') %}已就緒{% else %}未就緒{% endif %}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-chart-line fa-2x mb-2 text-info"></i>
                            <h6>24小時預測</h6>
                            <span class="fs-4 fw-bold">{{ system_status.get('recent_predictions_24h', 0) }}</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-bullseye fa-2x mb-2 text-primary"></i>
                            <h6>7天準確率</h6>
                            <span class="fs-4 fw-bold">
                                {% if system_status.get('accuracy_stats') and system_status.accuracy_stats.get('overall_accuracy') %}
                                    {{ system_status.accuracy_stats.overall_accuracy }}%
                                {% else %}
                                    N/A
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作面板 -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tools"></i> 系統操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- 預測管理 -->
                                <div class="col-md-6 mb-3">
                                    <h6><i class="fas fa-crystal-ball"></i> 預測管理</h6>
                                    <form method="POST" action="{{ url_for('admin.generate_predictions') }}" class="mb-2">
                                        <div class="input-group">
                                            <input type="date" class="form-control" name="date" 
                                                   value="{{ date.today().isoformat() }}">
                                            <button type="submit" class="btn btn-primary">生成預測</button>
                                        </div>
                                    </form>
                                    <button class="btn btn-outline-info btn-sm" onclick="testPrediction()">
                                        測試預測功能
                                    </button>
                                </div>

                                <!-- 模型管理 -->
                                <div class="col-md-6 mb-3">
                                    <h6><i class="fas fa-brain"></i> 模型管理</h6>
                                    <form method="POST" action="{{ url_for('admin.retrain_models') }}" class="mb-2">
                                        <div class="input-group">
                                            <input type="number" class="form-control" name="days_back" 
                                                   value="60" min="30" max="365" placeholder="訓練天數">
                                            <button type="submit" class="btn btn-warning">重新訓練</button>
                                        </div>
                                    </form>
                                    <a href="{{ url_for('admin.model_performance') }}" class="btn btn-outline-info btn-sm">
                                        查看性能
                                    </a>
                                </div>

                                <!-- 數據管理 -->
                                <div class="col-md-12 mb-3">
                                    <h6><i class="fas fa-database"></i> 數據管理</h6>

                                    <!-- 全面數據管理鏈接 -->
                                    <div class="mb-3">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <a href="{{ url_for('admin.comprehensive_data_dashboard') }}" class="btn btn-outline-primary w-100">
                                                    <i class="fas fa-chart-line"></i> 全面數據管理
                                                </a>
                                                <small class="text-muted d-block">管理影響賽果的所有球隊和球員資料</small>
                                            </div>
                                            <div class="col-md-6">
                                                <a href="{{ url_for('admin.historical_odds_dashboard') }}" class="btn btn-outline-info w-100">
                                                    <i class="fas fa-chart-bar"></i> 歷史盤口管理
                                                </a>
                                                <small class="text-muted d-block">下載和管理歷史博彩盤口數據</small>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <a href="{{ url_for('admin.prediction_methodology') }}" class="btn btn-outline-secondary w-100">
                                                    <i class="fas fa-book"></i> 預測方法論說明
                                                </a>
                                                <small class="text-muted d-block">詳細說明預測計算邏輯和數據來源標準</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 2025年預測功能 -->
                                    <div class="mb-3">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <a href="{{ url_for('admin.check_pitcher_announcements') }}" class="btn btn-outline-warning w-100 mb-2">
                                                    <i class="fas fa-bullhorn"></i> 投手公告檢查
                                                </a>
                                                <small class="text-muted d-block">檢查每日先發投手公告狀況</small>
                                            </div>
                                            <div class="col-md-4">
                                                <a href="{{ url_for('admin.prediction_readiness') }}" class="btn btn-outline-info w-100 mb-2">
                                                    <i class="fas fa-clipboard-check"></i> 預測準備檢查
                                                </a>
                                                <small class="text-muted d-block">檢查先發陣容確認狀況</small>
                                            </div>
                                            <div class="col-md-4">
                                                <a href="{{ url_for('admin.prediction_launcher') }}" class="btn btn-outline-success w-100 mb-2">
                                                    <i class="fas fa-rocket"></i> 2025年預測啟動器
                                                </a>
                                                <small class="text-muted d-block">智能預測時機判斷與啟動</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 每日更新區域 -->
                                    <div class="row mb-3">
                                        <div class="col-md-3">
                                            <form method="POST" action="{{ url_for('admin.daily_update') }}" class="mb-2">
                                                <div class="input-group">
                                                    <input type="date" class="form-control" name="date"
                                                           value="{{ today.isoformat() }}">
                                                    <button type="submit" class="btn btn-success">
                                                        <i class="fas fa-sync"></i> 每日更新
                                                    </button>
                                                </div>
                                                <small class="text-muted">更新比賽行程 + 下載Box Score</small>
                                            </form>
                                        </div>

                                        <div class="col-md-3">
                                            <form method="POST" action="{{ url_for('admin.download_today_boxscores') }}" class="mb-2">
                                                <button type="submit" class="btn btn-primary w-100">
                                                    <i class="fas fa-download"></i> 下載今天Box Score
                                                </button>
                                                <small class="text-muted">下載今天已完成比賽的詳細數據</small>
                                            </form>
                                        </div>

                                        <div class="col-md-3">
                                            <form method="POST" action="{{ url_for('admin.comprehensive_update') }}" class="mb-2">
                                                <button type="submit" class="btn btn-warning w-100" onclick="return confirmComprehensiveUpdate()">
                                                    <i class="fas fa-sync-alt"></i> 全面更新
                                                </button>
                                                <small class="text-muted">更新最近所有資料並刷新未來七天行程</small>
                                            </form>
                                        </div>

                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-info w-100" onclick="refreshSchedule()">
                                                <i class="fas fa-calendar-alt"></i> 刷新行程
                                            </button>
                                            <small class="text-muted">刷新未來七天比賽行程</small>
                                        </div>

                                        <div class="col-md-4">
                                            <form method="POST" action="{{ url_for('admin.refresh_schedule') }}" class="mb-2">
                                                <div class="input-group">
                                                    <input type="date" class="form-control" name="start_date"
                                                           value="{{ today.isoformat() }}" placeholder="開始日期">
                                                    <input type="date" class="form-control" name="end_date"
                                                           value="{{ next_week.isoformat() }}" placeholder="結束日期">
                                                    <button type="submit" class="btn btn-warning">
                                                        <i class="fas fa-calendar-alt"></i> 刷新行程
                                                    </button>
                                                </div>
                                                <small class="text-muted">刷新指定日期範圍的比賽行程</small>
                                            </form>
                                        </div>
                                    </div>

                                    <!-- 原有功能 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <form method="POST" action="{{ url_for('admin.update_data') }}" class="mb-2">
                                                <div class="input-group">
                                                    <input type="number" class="form-control" name="days_back"
                                                           value="3" min="1" max="30" placeholder="更新天數">
                                                    <button type="submit" class="btn btn-info">更新數據</button>
                                                </div>
                                            </form>
                                        </div>

                                        <div class="col-md-6">
                                            <form method="POST" action="{{ url_for('admin.fetch_detailed_data') }}" class="mb-2">
                                                <div class="input-group">
                                                    <input type="date" class="form-control" name="date"
                                                           value="{{ yesterday.isoformat() }}">
                                                    <button type="submit" class="btn btn-warning">獲取詳細數據</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>

                                    <a href="{{ url_for('admin.database_stats') }}" class="btn btn-outline-info btn-sm">
                                        數據庫統計
                                    </a>
                                </div>

                                <!-- 調度器管理 -->
                                <div class="col-md-6 mb-3">
                                    <h6><i class="fas fa-clock"></i> 調度器管理</h6>
                                    <div class="btn-group w-100 mb-2">
                                        <form method="POST" action="{{ url_for('admin.start_scheduler') }}" class="flex-fill">
                                            <button type="submit" class="btn btn-success w-100"
                                                    {% if system_status.get('scheduler_running') %}disabled{% endif %}>
                                                啟動
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ url_for('admin.stop_scheduler') }}" class="flex-fill">
                                            <button type="submit" class="btn btn-danger w-100"
                                                    {% if not system_status.get('scheduler_running') %}disabled{% endif %}>
                                                停止
                                            </button>
                                        </form>
                                    </div>
                                    <a href="{{ url_for('admin.view_logs') }}" class="btn btn-outline-info btn-sm">
                                        查看日誌
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系統信息 -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-info-circle"></i> 系統信息</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td>最後檢查:</td>
                                    <td>
                                        <small>
                                            {% if system_status.get('last_check') %}
                                                {{ system_status.last_check[:19] }}
                                            {% else %}
                                                未知
                                            {% endif %}
                                        </small>
                                    </td>
                                </tr>
                                {% if system_status.get('training_summary') %}
                                <tr>
                                    <td>最後訓練:</td>
                                    <td>
                                        <small>
                                            {% if system_status.training_summary.get('latest_training_date') %}
                                                {{ system_status.training_summary.latest_training_date[:10] }}
                                            {% else %}
                                                未訓練
                                            {% endif %}
                                        </small>
                                    </td>
                                </tr>
                                <tr>
                                    <td>訓練樣本:</td>
                                    <td>
                                        <small>{{ system_status.training_summary.get('training_samples', 0) }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td>特徵數量:</td>
                                    <td>
                                        <small>{{ system_status.training_summary.get('features_count', 0) }}</small>
                                    </td>
                                </tr>
                                {% endif %}
                            </table>

                            {% if system_status.get('accuracy_stats') %}
                            <h6 class="mt-3">準確率統計</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ system_status.accuracy_stats.get('overall_accuracy', 0) }}%">
                                    {{ system_status.accuracy_stats.get('overall_accuracy', 0) }}%
                                </div>
                            </div>
                            <small class="text-muted">
                                總預測: {{ system_status.accuracy_stats.get('total_predictions', 0) }}<br>
                                正確預測: {{ system_status.accuracy_stats.get('correct_predictions', 0) }}<br>
                                平均誤差: {{ system_status.accuracy_stats.get('average_score_error', 0) }}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshStatus() {
    fetch('/admin/api/status')
        .then(response => response.json())
        .then(data => {
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('刷新狀態失敗');
        });
}

function testPrediction() {
    window.open('/admin/test/prediction', '_blank');
}

// 自動刷新狀態（每30秒）
setInterval(function() {
    fetch('/admin/api/status')
        .then(response => response.json())
        .then(data => {
            // 更新狀態指示器
            updateStatusIndicators(data);
        })
        .catch(error => {
            console.error('Auto refresh error:', error);
        });
}, 30000);

function updateStatusIndicators(data) {
    // 這裡可以實現動態更新狀態指示器的邏輯
    // 目前簡化處理
}

function confirmComprehensiveUpdate() {
    return confirm('全面更新將會更新過去7天到未來7天的所有數據，這可能需要幾分鐘時間。確定要繼續嗎？');
}

function refreshSchedule() {
    if (confirm('確定要刷新未來七天的比賽行程嗎？')) {
        // 顯示載入指示器
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
        button.disabled = true;

        // 調用刷新行程API
        fetch('/admin/api/refresh-schedule', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ 未來七天行程刷新成功！');
                location.reload();
            } else {
                alert('❌ 行程刷新失敗: ' + (data.error || '未知錯誤'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('❌ 行程刷新失敗: 網路錯誤');
        })
        .finally(() => {
            // 恢復按鈕狀態
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}
</script>
{% endblock %}
