{% extends "base.html" %}

{% block title %}全面數據管理 - MLB 預測系統{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-database"></i> 全面數據管理</h2>
                <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回管理面板
                </a>
            </div>
        </div>
    </div>

    {% if not comprehensive_available %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                全面數據功能不可用。請確保相關模組已正確安裝。
            </div>
        </div>
    </div>
    {% else %}

    <!-- 數據狀態概覽 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 數據狀態概覽</h5>
                </div>
                <div class="card-body">
                    {% if stats %}
                    <div class="row">
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-primary">{{ stats.teams }}</h4>
                                <small class="text-muted">活躍球隊</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-success">{{ stats.team_advanced_stats }}</h4>
                                <small class="text-muted">進階統計</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-info">{{ stats.player_trends }}</h4>
                                <small class="text-muted">球員趨勢</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-warning">{{ stats.injury_reports }}</h4>
                                <small class="text-muted">傷兵報告</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-secondary">{{ stats.team_chemistry }}</h4>
                                <small class="text-muted">團隊化學</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-dark">{{ stats.weather_conditions }}</h4>
                                <small class="text-muted">天氣記錄</small>
                            </div>
                        </div>
                    </div>

                    <!-- 完整度指標 -->
                    {% if stats.teams > 0 %}
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <label>進階統計完整度</label>
                            {% set advanced_completeness = (stats.team_advanced_stats / stats.teams * 100) %}
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: {{ advanced_completeness }}%">
                                    {{ "%.1f"|format(advanced_completeness) }}%
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label>團隊化學完整度</label>
                            {% set chemistry_completeness = (stats.team_chemistry / stats.teams * 100) %}
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: {{ chemistry_completeness }}%">
                                    {{ "%.1f"|format(chemistry_completeness) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% else %}
                    <p class="text-muted">無法獲取數據狀態</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 數據管理操作 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-download"></i> 全面數據抓取</h5>
                </div>
                <div class="card-body">
                    <form id="fetchAllForm">
                        <div class="mb-3">
                            <label for="season" class="form-label">賽季年份</label>
                            <input type="number" class="form-control" id="season" name="season"
                                   value="2025" min="2020" max="2026">
                        </div>
                        <button type="submit" class="btn btn-primary" id="fetchAllBtn">
                            <i class="fas fa-download"></i> 獲取所有球隊數據
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-team"></i> 單個球隊數據</h5>
                </div>
                <div class="card-body">
                    <form id="fetchTeamForm">
                        <div class="mb-3">
                            <label for="team_code" class="form-label">選擇球隊</label>
                            <select class="form-select" id="team_code" name="team_code" required>
                                <option value="">請選擇球隊</option>
                                {% for team in teams %}
                                <option value="{{ team.team_code }}">{{ team.team_code }} - {{ team.team_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="team_season" class="form-label">賽季年份</label>
                            <input type="number" class="form-control" id="team_season" name="season"
                                   value="2025" min="2020" max="2026">
                        </div>
                        <button type="submit" class="btn btn-success" id="fetchTeamBtn">
                            <i class="fas fa-download"></i> 獲取球隊數據
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 球員趨勢和維護操作 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-trending-up"></i> 球員趨勢更新</h5>
                </div>
                <div class="card-body">
                    <form id="updateTrendsForm">
                        <div class="mb-3">
                            <label for="trends_season" class="form-label">賽季年份</label>
                            <input type="number" class="form-control" id="trends_season" name="season"
                                   value="2025" min="2020" max="2026">
                        </div>
                        <button type="submit" class="btn btn-info" id="updateTrendsBtn">
                            <i class="fas fa-sync"></i> 更新球員趨勢
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-broom"></i> 數據維護</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-primary" id="refreshStatusBtn">
                            <i class="fas fa-refresh"></i> 刷新狀態
                        </button>
                    </div>
                    <form id="cleanupForm">
                        <div class="mb-3">
                            <label for="cleanup_days" class="form-label">清理天數</label>
                            <input type="number" class="form-control" id="cleanup_days" name="days" 
                                   value="30" min="1" max="365">
                            <small class="form-text text-muted">清理指定天數前的舊數據</small>
                        </div>
                        <button type="submit" class="btn btn-warning" id="cleanupBtn">
                            <i class="fas fa-trash"></i> 清理舊數據
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作日誌 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> 操作日誌</h5>
                </div>
                <div class="card-body">
                    <div id="operationLog" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
                        <p class="text-muted">等待操作...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const logContainer = document.getElementById('operationLog');
    
    function addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `mb-2 text-${type}`;
        logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    function setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 處理中...';
        } else {
            button.disabled = false;
            button.innerHTML = button.getAttribute('data-original-text');
        }
    }
    
    // 獲取所有球隊數據
    document.getElementById('fetchAllForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const btn = document.getElementById('fetchAllBtn');
        const formData = new FormData(this);
        
        setButtonLoading(btn, true);
        addLog('開始獲取所有球隊的全面數據...', 'info');
        
        fetch('{{ url_for("admin.fetch_all_comprehensive_data") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLog(data.message, 'success');
                if (data.results && data.results.failed_teams.length > 0) {
                    addLog(`失敗球隊: ${data.results.failed_teams.join(', ')}`, 'warning');
                }
            } else {
                addLog(data.message, 'danger');
            }
        })
        .catch(error => {
            addLog(`錯誤: ${error.message}`, 'danger');
        })
        .finally(() => {
            setButtonLoading(btn, false);
        });
    });
    
    // 獲取單個球隊數據
    document.getElementById('fetchTeamForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const btn = document.getElementById('fetchTeamBtn');
        const formData = new FormData(this);
        
        setButtonLoading(btn, true);
        addLog(`開始獲取球隊 ${formData.get('team_code')} 的全面數據...`, 'info');
        
        fetch('{{ url_for("admin.fetch_team_comprehensive_data") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLog(data.message, 'success');
            } else {
                addLog(data.message, 'danger');
            }
        })
        .catch(error => {
            addLog(`錯誤: ${error.message}`, 'danger');
        })
        .finally(() => {
            setButtonLoading(btn, false);
        });
    });
    
    // 更新球員趨勢
    document.getElementById('updateTrendsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const btn = document.getElementById('updateTrendsBtn');
        const formData = new FormData(this);
        
        setButtonLoading(btn, true);
        addLog('開始更新球員表現趨勢...', 'info');
        
        fetch('{{ url_for("admin.update_player_trends") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLog(data.message, 'success');
            } else {
                addLog(data.message, 'danger');
            }
        })
        .catch(error => {
            addLog(`錯誤: ${error.message}`, 'danger');
        })
        .finally(() => {
            setButtonLoading(btn, false);
        });
    });
    
    // 刷新狀態
    document.getElementById('refreshStatusBtn').addEventListener('click', function() {
        addLog('刷新數據狀態...', 'info');
        location.reload();
    });
    
    // 清理舊數據
    document.getElementById('cleanupForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const btn = document.getElementById('cleanupBtn');
        const formData = new FormData(this);
        
        if (!confirm(`確定要清理 ${formData.get('days')} 天前的舊數據嗎？`)) {
            return;
        }
        
        setButtonLoading(btn, true);
        addLog(`開始清理 ${formData.get('days')} 天前的舊數據...`, 'info');
        
        fetch('{{ url_for("admin.cleanup_old_comprehensive_data") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLog(data.message, 'success');
            } else {
                addLog(data.message, 'danger');
            }
        })
        .catch(error => {
            addLog(`錯誤: ${error.message}`, 'danger');
        })
        .finally(() => {
            setButtonLoading(btn, false);
        });
    });
});
</script>
{% endblock %}
