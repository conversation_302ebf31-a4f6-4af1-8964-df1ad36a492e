<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MLB 數據修復系統 - 管理後台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .dashboard-card {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e3e6f0;
            margin-bottom: 1.5rem;
        }
        .priority-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .priority-critical { background-color: #dc3545; color: white; }
        .priority-high { background-color: #fd7e14; color: white; }
        .priority-medium { background-color: #ffc107; color: black; }
        .priority-low { background-color: #28a745; color: white; }
        .repair-status {
            min-height: 300px;
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
        }
        .repair-progress {
            background: linear-gradient(90deg, #4e73df 0%, #224abe 100%);
            min-height: 4px;
            border-radius: 2px;
        }
        .problem-item {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #6c757d;
            background-color: #f8f9fa;
            border-radius: 0 0.25rem 0.25rem 0;
        }
        .problem-item.critical { border-left-color: #dc3545; }
        .problem-item.high { border-left-color: #fd7e14; }
        .problem-item.medium { border-left-color: #ffc107; }
        .problem-item.low { border-left-color: #28a745; }
        .stats-icon {
            font-size: 2rem;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">
                <i class="bi bi-tools me-2"></i>MLB 數據修復系統
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/admin/data-management">
                    <i class="bi bi-arrow-left me-1"></i>返回數據管理
                </a>
                <a class="nav-link" href="/admin/progress-monitor">
                    <i class="bi bi-activity me-1"></i>進度監控
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- 系統狀態概覽 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-shield-exclamation me-2"></i>數據修復系統概覽
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-bug stats-icon text-danger me-3"></i>
                                    <div>
                                        <div class="h4 mb-0" id="total-issues">-</div>
                                        <div class="text-muted">待修復問題</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-exclamation-triangle stats-icon text-warning me-3"></i>
                                    <div>
                                        <div class="h4 mb-0" id="critical-issues">-</div>
                                        <div class="text-muted">關鍵問題</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-clock-history stats-icon text-info me-3"></i>
                                    <div>
                                        <div class="h4 mb-0" id="estimated-time">-</div>
                                        <div class="text-muted">預估修復時間</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-calendar-check stats-icon text-success me-3"></i>
                                    <div>
                                        <div class="h4 mb-0" id="target-month">-</div>
                                        <div class="text-muted">目標月份</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 修復控制面板 -->
            <div class="col-lg-4">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-gear me-2"></i>修復控制面板
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="repair-settings">
                            <div class="mb-3">
                                <label for="repair-year" class="form-label">年份</label>
                                <select class="form-select" id="repair-year">
                                    <option value="2025">2025</option>
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="repair-month" class="form-label">月份</label>
                                <select class="form-select" id="repair-month">
                                    <option value="1">1月</option>
                                    <option value="2">2月</option>
                                    <option value="3">3月</option>
                                    <option value="4">4月</option>
                                    <option value="5">5月</option>
                                    <option value="6">6月</option>
                                    <option value="7">7月</option>
                                    <option value="8" selected>8月</option>
                                    <option value="9">9月</option>
                                    <option value="10">10月</option>
                                    <option value="11">11月</option>
                                    <option value="12">12月</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="max-concurrent" class="form-label">並發數量</label>
                                <select class="form-select" id="max-concurrent">
                                    <option value="1">1 個任務</option>
                                    <option value="3" selected>3 個任務</option>
                                    <option value="5">5 個任務</option>
                                    <option value="8">8 個任務</option>
                                </select>
                            </div>
                            <hr>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-info" id="create-plan-btn">
                                    <i class="bi bi-clipboard-check me-1"></i>分析問題
                                </button>
                                <button type="button" class="btn btn-success" id="execute-repair-btn" disabled>
                                    <i class="bi bi-tools me-1"></i>執行修復
                                </button>
                                <button type="button" class="btn btn-warning" id="status-sync-btn">
                                    <i class="bi bi-arrow-clockwise me-1"></i>同步狀態
                                </button>
                            </div>
                        </form>

                        <hr>
                        
                        <div class="mb-3">
                            <h6>緊急修復</h6>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="emergencyRepair('critical')">
                                    <i class="bi bi-exclamation-triangle me-1"></i>關鍵問題
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="emergencyRepair('boxscore')">
                                    <i class="bi bi-bar-chart me-1"></i>Boxscore缺失
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="emergencyRepair('predictions')">
                                    <i class="bi bi-bullseye me-1"></i>預測缺失
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 修復狀態 -->
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-activity me-2"></i>修復狀態
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="repair-status" class="repair-status">
                            <div class="text-center text-muted">
                                <i class="bi bi-info-circle display-6 mb-3"></i>
                                <p>點擊"分析問題"開始檢查數據完整性</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 問題詳情 -->
            <div class="col-lg-8">
                <div class="card dashboard-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="bi bi-list-check me-2"></i>問題詳情
                        </h6>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="filterProblems('all')">全部</button>
                            <button type="button" class="btn btn-outline-danger" onclick="filterProblems('critical')">關鍵</button>
                            <button type="button" class="btn btn-outline-warning" onclick="filterProblems('high')">高</button>
                            <button type="button" class="btn btn-outline-info" onclick="filterProblems('medium')">中</button>
                            <button type="button" class="btn btn-outline-success" onclick="filterProblems('low')">低</button>
                        </div>
                    </div>
                    <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                        <div id="problems-list">
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-search display-6 mb-3"></i>
                                <p>暫無數據，請先進行問題分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 修復進度模態框 -->
    <div class="modal fade" id="repairProgressModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-gear-fill me-2"></i>修復進度
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="progress mb-3">
                        <div class="progress-bar repair-progress" id="modal-progress-bar" role="progressbar" style="width: 0%">
                            0%
                        </div>
                    </div>
                    <div id="modal-progress-details">
                        <p class="text-muted">準備開始修復...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                    <a href="/admin/progress-monitor" class="btn btn-primary" target="_blank">
                        <i class="bi bi-activity me-1"></i>詳細進度
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentRepairPlan = null;
        let currentFilter = 'all';

        // 設置默認日期為當前月份
        document.addEventListener('DOMContentLoaded', function() {
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;
            
            document.getElementById('repair-year').value = currentYear;
            document.getElementById('repair-month').value = currentMonth;
            document.getElementById('target-month').textContent = `${currentYear}年${currentMonth}月`;
        });

        // 創建修復計劃
        document.getElementById('create-plan-btn').addEventListener('click', async function() {
            const year = parseInt(document.getElementById('repair-year').value);
            const month = parseInt(document.getElementById('repair-month').value);
            
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>分析中...';
            
            updateRepairStatus('分析數據完整性...', 'info');
            
            try {
                const response = await fetch('/admin/api/create-repair-plan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ year, month })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentRepairPlan = result.repair_plan;
                    displayRepairPlan(result.repair_plan);
                    document.getElementById('execute-repair-btn').disabled = false;
                    updateRepairStatus('分析完成，可以開始修復', 'success');
                } else {
                    updateRepairStatus('分析失敗: ' + result.message, 'danger');
                }
            } catch (error) {
                updateRepairStatus('分析請求失敗: ' + error.message, 'danger');
            }
            
            this.disabled = false;
            this.innerHTML = '<i class="bi bi-clipboard-check me-1"></i>分析問題';
        });

        // 執行修復
        document.getElementById('execute-repair-btn').addEventListener('click', async function() {
            if (!currentRepairPlan) {
                alert('請先進行問題分析');
                return;
            }
            
            const maxConcurrent = parseInt(document.getElementById('max-concurrent').value);
            
            if (!confirm(`即將開始修復 ${currentRepairPlan.total_issues} 個問題，是否繼續？`)) {
                return;
            }
            
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>修復中...';
            
            updateRepairStatus('開始批量修復...', 'warning');
            
            try {
                const response = await fetch('/admin/api/execute-data-repair', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        repair_plan: currentRepairPlan,
                        max_concurrent: maxConcurrent
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateRepairStatus('修復已在後台啟動，請查看進度監控', 'success');
                } else {
                    updateRepairStatus('修復啟動失敗: ' + result.message, 'danger');
                }
            } catch (error) {
                updateRepairStatus('修復請求失敗: ' + error.message, 'danger');
            }
            
            this.disabled = false;
            this.innerHTML = '<i class="bi bi-tools me-1"></i>執行修復';
        });

        // 狀態同步
        document.getElementById('status-sync-btn').addEventListener('click', async function() {
            const year = parseInt(document.getElementById('repair-year').value);
            const month = parseInt(document.getElementById('repair-month').value);
            
            if (!confirm(`即將同步 ${year}年${month}月 的比賽狀態，是否繼續？`)) {
                return;
            }
            
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>同步中...';
            
            try {
                const response = await fetch('/admin/api/status-sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ year, month })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateRepairStatus('狀態同步已啟動', 'success');
                } else {
                    updateRepairStatus('狀態同步失敗: ' + result.message, 'danger');
                }
            } catch (error) {
                updateRepairStatus('狀態同步請求失敗: ' + error.message, 'danger');
            }
            
            this.disabled = false;
            this.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>同步狀態';
        });

        // 緊急修復
        async function emergencyRepair(type) {
            const year = parseInt(document.getElementById('repair-year').value);
            const month = parseInt(document.getElementById('repair-month').value);
            
            const typeNames = {
                'critical': '關鍵問題',
                'boxscore': 'Boxscore缺失',
                'predictions': '預測缺失'
            };
            
            if (!confirm(`即將進行緊急修復: ${typeNames[type]}，是否繼續？`)) {
                return;
            }
            
            updateRepairStatus(`啟動緊急修復: ${typeNames[type]}`, 'warning');
            
            try {
                const response = await fetch('/admin/api/emergency-repair', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ year, month, type })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateRepairStatus('緊急修復已啟動', 'success');
                } else {
                    updateRepairStatus('緊急修復失敗: ' + result.message, 'danger');
                }
            } catch (error) {
                updateRepairStatus('緊急修復請求失敗: ' + error.message, 'danger');
            }
        }

        // 顯示修復計劃
        function displayRepairPlan(plan) {
            // 更新統計信息
            document.getElementById('total-issues').textContent = plan.total_issues;
            document.getElementById('critical-issues').textContent = plan.priority_summary.critical;
            document.getElementById('estimated-time').textContent = Math.ceil(plan.estimated_time / 60) + '分鐘';
            document.getElementById('target-month').textContent = plan.month;
            
            // 顯示問題列表
            const problemsList = document.getElementById('problems-list');
            problemsList.innerHTML = '';
            
            if (plan.repair_tasks.length === 0) {
                problemsList.innerHTML = `
                    <div class="text-center text-success py-5">
                        <i class="bi bi-check-circle display-6 mb-3"></i>
                        <p>恭喜！沒有發現需要修復的問題</p>
                    </div>
                `;
                return;
            }
            
            plan.repair_tasks.forEach(task => {
                const problemDiv = document.createElement('div');
                problemDiv.className = `problem-item ${task.priority}`;
                problemDiv.dataset.priority = task.priority;
                
                const typeIcon = {
                    'missing_game': 'bi-calendar-x',
                    'missing_boxscore': 'bi-bar-chart',
                    'missing_prediction': 'bi-bullseye',
                    'missing_odds': 'bi-currency-dollar'
                };
                
                const typeText = {
                    'missing_game': '缺失比賽',
                    'missing_boxscore': '缺失Boxscore',
                    'missing_prediction': '缺失預測',
                    'missing_odds': '缺失賠率'
                };
                
                problemDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <i class="${typeIcon[task.type]} me-2"></i>
                                <span class="badge priority-badge priority-${task.priority}">${task.priority.toUpperCase()}</span>
                                <span class="ms-2 fw-bold">${typeText[task.type]}</span>
                            </div>
                            <div class="text-muted mb-2">
                                <i class="bi bi-calendar me-1"></i>${task.date}
                                <i class="bi bi-people-fill ms-3 me-1"></i>${task.matchup}
                            </div>
                            <div class="small text-secondary">
                                修復動作: ${task.repair_actions.join(', ')}
                            </div>
                        </div>
                        <div class="text-end">
                            <button class="btn btn-sm btn-outline-primary" onclick="repairSingleIssue(this, '${task.game_id}', '${task.type}')">
                                <i class="bi bi-tools"></i>
                            </button>
                        </div>
                    </div>
                `;
                
                problemsList.appendChild(problemDiv);
            });
        }

        // 修復單個問題
        async function repairSingleIssue(button, gameId, taskType) {
            const task = currentRepairPlan.repair_tasks.find(t => t.game_id === gameId && t.type === taskType);
            if (!task) return;
            
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm"></span>';
            
            try {
                const response = await fetch('/admin/api/repair-single-issue', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ task })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    button.innerHTML = '<i class="bi bi-check-circle text-success"></i>';
                    button.className = 'btn btn-sm btn-success';
                } else {
                    button.innerHTML = '<i class="bi bi-x-circle text-danger"></i>';
                    button.className = 'btn btn-sm btn-danger';
                }
            } catch (error) {
                button.innerHTML = '<i class="bi bi-exclamation-triangle text-warning"></i>';
                button.className = 'btn btn-sm btn-warning';
            }
        }

        // 篩選問題
        function filterProblems(priority) {
            currentFilter = priority;
            const items = document.querySelectorAll('.problem-item');
            
            items.forEach(item => {
                if (priority === 'all' || item.dataset.priority === priority) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 更新修復狀態
        function updateRepairStatus(message, type = 'info') {
            const statusDiv = document.getElementById('repair-status');
            const typeColors = {
                'info': 'text-info',
                'success': 'text-success',
                'warning': 'text-warning',
                'danger': 'text-danger'
            };
            
            const typeIcons = {
                'info': 'bi-info-circle',
                'success': 'bi-check-circle',
                'warning': 'bi-exclamation-triangle',
                'danger': 'bi-x-circle'
            };
            
            statusDiv.innerHTML = `
                <div class="text-center ${typeColors[type]}">
                    <i class="${typeIcons[type]} display-6 mb-3"></i>
                    <p>${message}</p>
                    <small class="text-muted">${new Date().toLocaleString('zh-TW')}</small>
                </div>
            `;
        }

        // 更新目標月份顯示
        document.getElementById('repair-year').addEventListener('change', updateTargetMonth);
        document.getElementById('repair-month').addEventListener('change', updateTargetMonth);

        function updateTargetMonth() {
            const year = document.getElementById('repair-year').value;
            const month = document.getElementById('repair-month').value;
            document.getElementById('target-month').textContent = `${year}年${month}月`;
        }
    </script>
</body>
</html>