{% extends "admin/layout.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calendar-day"></i> 日常操作</h5>
            </div>
            <div class="card-body">
                <!-- 每日更新 -->
                <div class="mb-4 p-3 border rounded bg-light">
                    <h5><i class="fas fa-sync"></i> 核心每日更新</h5>
                    <p class="text-muted">執行每日例行任務：更新今日比賽行程並下載昨日已完成比賽的Box Score。</p>
                    <form method="POST" action="{{ url_for('admin.daily_update') }}">
                        <div class="input-group">
                            <input type="date" class="form-control" name="date" value="{{ today.isoformat() }}">
                            <button type="submit" class="btn btn-primary btn-lg">執行每日更新</button>
                        </div>
                    </form>
                </div>

                <!-- 手動預測 -->
                <div class="mb-3">
                    <h6><i class="fas fa-crystal-ball"></i> 手動生成預測</h6>
                    <form method="POST" action="{{ url_for('admin.generate_predictions') }}">
                        <div class="input-group">
                            <input type="date" class="form-control" name="date" value="{{ today.isoformat() }}">
                            <button type="submit" class="btn btn-outline-primary">生成預測</button>
                        </div>
                    </form>
                </div>

                <!-- 刷新賽程 -->
                <div class="mb-3">
                    <h6><i class="fas fa-calendar-alt"></i> 刷新未來賽程</h6>
                     <button type="button" class="btn btn-outline-info" onclick="refreshSchedule()">
                        <i class="fas fa-sync-alt"></i> 刷新未來七天行程
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 說明</h6>
            </div>
            <div class="card-body">
                <p>此頁面整合了最常用的日常維護指令，方便您快速啟動每日的數據更新與預測流程。</p>
                <ul>
                    <li><strong>核心每日更新:</strong> 這是每天最主要的操作。</li>
                    <li><strong>手動生成預測:</strong> 用於補跑特定日期的預測。</li>
                    <li><strong>刷新未來賽程:</strong> 當比賽時間有變動時使用。</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function refreshSchedule() {
    if (confirm('確定要刷新未來七天的比賽行程嗎？')) {
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
        button.disabled = true;

        fetch("{{ url_for('admin.api_refresh_schedule') }}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ 未來七天行程刷新成功！');
                location.reload();
            } else {
                alert('❌ 行程刷新失敗: ' + (data.error || '未知錯誤'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('❌ 行程刷新失敗: 網路錯誤');
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}
</script>
{% endblock %}
