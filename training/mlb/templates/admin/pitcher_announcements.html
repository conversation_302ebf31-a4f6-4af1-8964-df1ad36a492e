{% extends "base.html" %}

{% block title %}投手公告檢查 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bullhorn"></i> 每日投手公告檢查</h2>
                <div>
                    <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回管理面板
                    </a>
                    <button onclick="location.reload()" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 總體狀況卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ announcement_status.total_games }}</h4>
                            <p class="mb-0">總比賽數</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-baseball-ball fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ announcement_status.summary.fully_announced }}</h4>
                            <p class="mb-0">完全公告</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-double fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ announcement_status.summary.partially_announced }}</h4>
                            <p class="mb-0">部分公告</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ announcement_status.summary.no_announcements }}</h4>
                            <p class="mb-0">未公告</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 公告覆蓋率 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 公告覆蓋率</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="progress mb-3" style="height: 30px;">
                                <div class="progress-bar 
                                    {% if announcement_status.announcement_coverage >= 80 %}bg-success
                                    {% elif announcement_status.announcement_coverage >= 60 %}bg-warning
                                    {% else %}bg-danger{% endif %}" 
                                    role="progressbar" 
                                    style="width: {{ announcement_status.announcement_coverage }}%">
                                    {{ "%.1f"|format(announcement_status.announcement_coverage) }}%
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4>
                                {% if announcement_status.announcement_coverage >= 80 %}
                                    <span class="badge bg-success">優秀</span>
                                {% elif announcement_status.announcement_coverage >= 60 %}
                                    <span class="badge bg-warning">良好</span>
                                {% elif announcement_status.announcement_coverage >= 40 %}
                                    <span class="badge bg-warning">一般</span>
                                {% else %}
                                    <span class="badge bg-danger">不足</span>
                                {% endif %}
                            </h4>
                            <p class="text-muted">投手公告完整度</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 預測時機建議 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clock"></i> 預測時機建議</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3>{{ "%.1f"|format(timing_recommendation.overall_readiness) }}%</h3>
                                <p class="text-muted">整體準備度</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3>{{ timing_recommendation.ready_for_prediction }}</h3>
                                <p class="text-muted">可預測比賽</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3>{{ timing_recommendation.waiting_for_info }}</h3>
                                <p class="text-muted">等待信息</p>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> 時機建議:</h6>
                        <p class="mb-2">{{ timing_recommendation.timing_advice }}</p>
                        <small class="text-muted">下次檢查時間: {{ timing_recommendation.next_check_time }}</small>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ url_for('admin.prediction_launcher') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-rocket"></i> 前往預測啟動器
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 詳細比賽狀況 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> 詳細比賽狀況 ({{ announcement_status.date }})</h5>
                </div>
                <div class="card-body">
                    {% if announcement_status.games %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>比賽</th>
                                    <th>狀態</th>
                                    <th>主隊投手</th>
                                    <th>客隊投手</th>
                                    <th>預測準備度</th>
                                    <th>建議</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for game in announcement_status.games %}
                                <tr>
                                    <td>
                                        <strong>{{ game.matchup }}</strong>
                                        {% if game.game_time %}
                                        <br><small class="text-muted">{{ game.game_time }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if game.status == 'Scheduled' %}
                                            <span class="badge bg-primary">{{ game.status }}</span>
                                        {% elif game.status in ['In Progress', 'Live'] %}
                                            <span class="badge bg-success">{{ game.status }}</span>
                                        {% elif game.status in ['Final', 'Completed'] %}
                                            <span class="badge bg-secondary">{{ game.status }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ game.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if game.home_pitcher %}
                                            <i class="fas fa-check text-success"></i>
                                            <a href="{{ url_for('admin.pitcher_details', pitcher_name=game.home_pitcher) }}"
                                               class="pitcher-link text-decoration-none">
                                                {{ game.home_pitcher }}
                                            </a>
                                        {% else %}
                                            <i class="fas fa-times text-danger"></i> 未公告
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if game.away_pitcher %}
                                            <i class="fas fa-check text-success"></i>
                                            <a href="{{ url_for('admin.pitcher_details', pitcher_name=game.away_pitcher) }}"
                                               class="pitcher-link text-decoration-none">
                                                {{ game.away_pitcher }}
                                            </a>
                                        {% else %}
                                            <i class="fas fa-times text-danger"></i> 未公告
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar 
                                                {% if game.prediction_readiness.overall_score >= 80 %}bg-success
                                                {% elif game.prediction_readiness.overall_score >= 60 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ game.prediction_readiness.overall_score }}%">
                                                {{ game.prediction_readiness.overall_score }}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <small>{{ game.prediction_readiness.recommendation }}</small>
                                        {% if game.prediction_readiness.missing_elements %}
                                        <br><small class="text-muted">
                                            缺少: {{ game.prediction_readiness.missing_elements|join(', ') }}
                                        </small>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5>今天沒有比賽</h5>
                        <p class="text-muted">請檢查其他日期或等待比賽安排</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.progress {
    background-color: #e9ecef;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
    border-top: none;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}

{% block extra_css %}
<style>
.pitcher-link {
    color: #0d6efd;
    font-weight: 500;
    transition: color 0.2s ease;
}

.pitcher-link:hover {
    color: #0a58ca;
    text-decoration: underline !important;
}

.table td {
    vertical-align: middle;
}

.progress {
    background-color: #e9ecef;
}

.badge {
    font-size: 0.875em;
}
</style>
{% endblock %}
