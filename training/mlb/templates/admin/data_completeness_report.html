{% extends "admin/layout.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-search"></i> 數據完整性報告</h5>
                <a href="{{ url_for('admin.data_management') }}" class="btn btn-outline-secondary btn-sm">返回數據管理</a>
            </div>
            <div class="card-body">
                {% if report %}
                <!-- 總覽統計 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3>{{ report.official_games_count }}</h3>
                                <p class="mb-0">官方賽程場次</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3>{{ report.db_games_count }}</h3>
                                <p class="mb-0">數據庫記錄</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3>{{ report.predictions_count }}</h3>
                                <p class="mb-0">預測記錄</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3>{{ "%.1f"|format(report.data_quality_score) }}%</h3>
                                <p class="mb-0">數據質量分數</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 覆蓋率詳情 -->
                {% if report.coverage_breakdown %}
                <div class="row mb-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>數據覆蓋率分析</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <label>比賽數據覆蓋率</label>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: {{ report.coverage_breakdown.games_coverage }}%">
                                            {{ "%.1f"|format(report.coverage_breakdown.games_coverage) }}%
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label>預測數據覆蓋率</label>
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" style="width: {{ report.coverage_breakdown.predictions_coverage }}%">
                                            {{ "%.1f"|format(report.coverage_breakdown.predictions_coverage) }}%
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label>賠率數據覆蓋率</label>
                                    <div class="progress">
                                        <div class="progress-bar bg-info" style="width: {{ report.coverage_breakdown.odds_coverage }}%">
                                            {{ "%.1f"|format(report.coverage_breakdown.odds_coverage) }}%
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label>Boxscore覆蓋率</label>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" style="width: {{ report.coverage_breakdown.boxscore_coverage }}%">
                                            {{ "%.1f"|format(report.coverage_breakdown.boxscore_coverage) }}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>數據質量指標</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <td>賠率記錄總數</td>
                                        <td><strong>{{ report.odds_count }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>數據覆蓋日期</td>
                                        <td>{{ report.earliest_date or 'N/A' }} 至 {{ report.latest_date or 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td>覆蓋天數</td>
                                        <td>{{ report.covered_dates or 0 }} 天</td>
                                    </tr>
                                    <tr>
                                        <td>數據完整度</td>
                                        <td>{{ "%.1f"|format(report.data_completeness or 0) }}%</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- 缺失數據詳情 -->
                {% if report.missing_games %}
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-exclamation-triangle text-danger"></i> 缺失比賽 ({{ report.missing_games|length }} 場)</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>對戰</th>
                                        <th>球場</th>
                                        <th>狀態</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for missing in report.missing_games %}
                                    <tr>
                                        <td>{{ missing.date }}</td>
                                        <td>{{ missing.matchup }}</td>
                                        <td>{{ missing.venue }}</td>
                                        <td>
                                            <span class="badge badge-{{ 'success' if missing.status == 'Final' else 'warning' }}">
                                                {{ missing.status }}
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="fixMissingGame('{{ missing.game_id }}')">
                                                修復
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if report.missing_predictions %}
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-line text-warning"></i> 缺失預測 ({{ report.missing_predictions|length }} 場)</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>對戰</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for missing in report.missing_predictions[:10] %}
                                    <tr>
                                        <td>{{ missing.date }}</td>
                                        <td>{{ missing.matchup }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-warning" onclick="generatePrediction('{{ missing.game_id }}')">
                                                生成預測
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if report.missing_predictions|length > 10 %}
                        <p class="text-muted">... 以及其他 {{ report.missing_predictions|length - 10 }} 場比賽</p>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                {% if report.games_without_boxscore %}
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-area text-info"></i> 缺失Boxscore數據 ({{ report.games_without_boxscore|length }} 場)</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>對戰</th>
                                        <th>官方比分</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for missing in report.games_without_boxscore[:10] %}
                                    <tr>
                                        <td>{{ missing.date }}</td>
                                        <td>{{ missing.matchup }}</td>
                                        <td>{{ missing.official_score }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-info" onclick="fetchBoxscore('{{ missing.game_id }}')">
                                                獲取Boxscore
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if report.games_without_boxscore|length > 10 %}
                        <p class="text-muted">... 以及其他 {{ report.games_without_boxscore|length - 10 }} 場比賽</p>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 操作建議 -->
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-lightbulb"></i> 建議操作</h6>
                    </div>
                    <div class="card-body">
                        {% if report.data_quality_score < 80 %}
                        <div class="alert alert-warning">
                            <strong>數據質量較低 ({{ "%.1f"|format(report.data_quality_score) }}%)</strong><br>
                            建議執行整月數據下載來補充缺失的數據。
                        </div>
                        {% elif report.data_quality_score < 95 %}
                        <div class="alert alert-info">
                            <strong>數據基本完整 ({{ "%.1f"|format(report.data_quality_score) }}%)</strong><br>
                            有少量缺失數據，可考慮進行局部修復。
                        </div>
                        {% else %}
                        <div class="alert alert-success">
                            <strong>數據質量優秀 ({{ "%.1f"|format(report.data_quality_score) }}%)</strong><br>
                            數據完整性良好，系統運行正常。
                        </div>
                        {% endif %}
                        
                        <div class="mt-3">
                            <a href="{{ url_for('admin.data_management') }}" class="btn btn-primary">返回數據管理</a>
                            <button onclick="window.print()" class="btn btn-outline-secondary">列印報告</button>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <h6>無報告數據</h6>
                    <p>請先執行數據完整性檢查來生成報告。</p>
                    <a href="{{ url_for('admin.data_management') }}" class="btn btn-primary">返回數據管理</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function confirmAction(message) {
    return confirm(message);
}

function fixMissingGame(gameId) {
    if (confirm(`確定要修復比賽 ${gameId} 的數據嗎？`)) {
        fetch('/admin/api/fix-missing-game', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({game_id: gameId})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('修復請求已提交，請查看日誌了解進度。');
                location.reload();
            } else {
                alert('修復失敗: ' + data.message);
            }
        })
        .catch(error => {
            alert('請求失敗: ' + error);
        });
    }
}

function generatePrediction(gameId) {
    if (confirm(`確定要為比賽 ${gameId} 生成預測嗎？`)) {
        fetch('/admin/api/generate-single-prediction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({game_id: gameId})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('預測生成請求已提交。');
                location.reload();
            } else {
                alert('預測生成失敗: ' + data.message);
            }
        })
        .catch(error => {
            alert('請求失敗: ' + error);
        });
    }
}

function fetchBoxscore(gameId) {
    if (confirm(`確定要獲取比賽 ${gameId} 的boxscore數據嗎？`)) {
        fetch('/admin/api/fetch-single-boxscore', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({game_id: gameId})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Boxscore獲取請求已提交。');
                location.reload();
            } else {
                alert('Boxscore獲取失敗: ' + data.message);
            }
        })
        .catch(error => {
            alert('請求失敗: ' + error);
        });
    }
}
</script>
{% endblock %}