{% extends "base.html" %}

{% block title %}預測準備狀況檢查{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-clipboard-check"></i> 預測準備狀況檢查</h2>
                <div>
                    <button id="refreshBtn" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt"></i> 刷新狀況
                    </button>
                    <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回管理面板
                    </a>
                </div>
            </div>

            <!-- 整體狀況概覽 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">總比賽數</h5>
                            <h2 class="text-primary">{{ readiness.total_games }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">完全準備</h5>
                            <h2 class="text-success">{{ readiness.ready_games }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">部分準備</h5>
                            <h2 class="text-warning">{{ readiness.partial_ready_games }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger">未準備</h5>
                            <h2 class="text-danger">{{ readiness.not_ready_games }}</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 預測建議 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4><i class="fas fa-lightbulb"></i> 預測建議</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="alert alert-{% if readiness.prediction_recommendation.confidence == 'high' %}success{% elif readiness.prediction_recommendation.confidence == 'medium' %}warning{% else %}danger{% endif %}">
                                <h5>
                                    {% if readiness.prediction_recommendation.action == 'proceed' %}
                                        <i class="fas fa-check-circle"></i> 建議進行預測
                                    {% elif readiness.prediction_recommendation.action == 'wait_or_partial' %}
                                        <i class="fas fa-clock"></i> 可進行部分預測
                                    {% else %}
                                        <i class="fas fa-pause-circle"></i> 建議等待
                                    {% endif %}
                                </h5>
                                <p class="mb-0">{{ readiness.prediction_recommendation.message }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="progress mb-2" style="height: 25px;">
                                    <div class="progress-bar 
                                        {% if readiness.overall_readiness_percentage >= 70 %}bg-success
                                        {% elif readiness.overall_readiness_percentage >= 40 %}bg-warning
                                        {% else %}bg-danger{% endif %}" 
                                        style="width: {{ readiness.overall_readiness_percentage }}%">
                                        {{ readiness.overall_readiness_percentage }}%
                                    </div>
                                </div>
                                <small class="text-muted">整體準備度</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 陣容更新時間表 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4><i class="fas fa-clock"></i> 陣容公告時間表</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for announcement in schedule.typical_announcement_times %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h5 class="text-info">{{ announcement.time }}</h5>
                                    <p class="mb-1">{{ announcement.description }}</p>
                                    <small class="text-muted">{{ announcement.games_affected }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i> 
                        <strong>下次檢查建議時間:</strong> {{ schedule.next_check_recommendation }}
                        <br>
                        <strong>自動刷新間隔:</strong> {{ schedule.auto_refresh_interval }}
                    </div>
                </div>
            </div>

            <!-- 詳細比賽狀況 -->
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-list"></i> 詳細比賽狀況 ({{ readiness.date }})</h4>
                    <small class="text-muted">最後更新: {{ readiness.checked_at }}</small>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>比賽</th>
                                    <th>時間</th>
                                    <th>狀態</th>
                                    <th>先發投手</th>
                                    <th>打線確認</th>
                                    <th>準備度</th>
                                    <th>建議</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for game in readiness.games %}
                                <tr>
                                    <td>
                                        <strong>{{ game.matchup }}</strong>
                                    </td>
                                    <td>
                                        <small>{{ game.game_time or 'TBD' }}</small>
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if game.status == 'Scheduled' %}badge-primary
                                            {% elif game.status == 'Pre-Game' %}badge-info
                                            {% elif 'In Progress' in game.status %}badge-warning
                                            {% else %}badge-secondary{% endif %}">
                                            {{ game.status }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <div>
                                                <i class="fas fa-home"></i> 
                                                {{ game.starting_pitchers.home or '未確定' }}
                                            </div>
                                            <div>
                                                <i class="fas fa-plane"></i> 
                                                {{ game.starting_pitchers.away or '未確定' }}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <div>
                                                <i class="fas fa-home"></i> 
                                                {% if game.lineups_confirmed.home %}
                                                    <span class="text-success">✓</span>
                                                {% else %}
                                                    <span class="text-danger">✗</span>
                                                {% endif %}
                                            </div>
                                            <div>
                                                <i class="fas fa-plane"></i> 
                                                {% if game.lineups_confirmed.away %}
                                                    <span class="text-success">✓</span>
                                                {% else %}
                                                    <span class="text-danger">✗</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <div class="progress mb-1" style="height: 20px;">
                                                <div class="progress-bar 
                                                    {% if game.readiness_score >= 80 %}bg-success
                                                    {% elif game.readiness_score >= 60 %}bg-info
                                                    {% elif game.readiness_score >= 40 %}bg-warning
                                                    {% else %}bg-danger{% endif %}" 
                                                    style="width: {{ game.readiness_score }}%">
                                                    {{ game.readiness_score }}
                                                </div>
                                            </div>
                                            <small class="badge 
                                                {% if game.readiness_level == '完全準備' %}badge-success
                                                {% elif game.readiness_level == '基本準備' %}badge-info
                                                {% elif game.readiness_level == '部分準備' %}badge-warning
                                                {% else %}badge-danger{% endif %}">
                                                {{ game.readiness_level }}
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="small">
                                            {% for suggestion in game.suggestions %}
                                            <div class="mb-1">
                                                {% if '可以進行預測' in suggestion %}
                                                    <i class="fas fa-check text-success"></i>
                                                {% else %}
                                                    <i class="fas fa-clock text-warning"></i>
                                                {% endif %}
                                                {{ suggestion }}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refreshBtn');
    
    refreshBtn.addEventListener('click', function() {
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
        
        fetch('/admin/api/refresh_lineup_status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('刷新失敗: ' + data.error);
                }
            })
            .catch(error => {
                alert('刷新失敗: ' + error);
            })
            .finally(() => {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新狀況';
            });
    });
    
    // 自動刷新 (每30分鐘)
    setInterval(function() {
        fetch('/admin/api/refresh_lineup_status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 靜默更新，不重新載入頁面
                    console.log('陣容狀況已自動更新:', data.updated_at);
                }
            })
            .catch(error => {
                console.log('自動刷新失敗:', error);
            });
    }, 30 * 60 * 1000); // 30分鐘
});
</script>
{% endblock %}
