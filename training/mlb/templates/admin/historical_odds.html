{% extends "base.html" %}

{% block title %}歷史盤口數據管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">📊 歷史盤口數據管理</h1>
            
            <!-- 數據統計卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ stats.total_odds }}</h4>
                                    <p class="card-text">總盤口記錄</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ stats.spreads_count }}</h4>
                                    <p class="card-text">讓分盤記錄</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exchange-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ stats.totals_count }}</h4>
                                    <p class="card-text">大小分記錄</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calculator fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ stats.covered_dates }}</h4>
                                    <p class="card-text">覆蓋天數</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 數據範圍信息 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">📅 數據覆蓋範圍</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>最早數據:</strong> 
                                        {% if stats.earliest_date %}
                                            {{ stats.earliest_date.strftime('%Y-%m-%d') }}
                                        {% else %}
                                            無數據
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>最新數據:</strong> 
                                        {% if stats.latest_date %}
                                            {{ stats.latest_date.strftime('%Y-%m-%d') }}
                                        {% else %}
                                            無數據
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 歷史數據下載工具 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">🔄 歷史盤口數據管理（從今天往回下載）</h5>
                        </div>
                        <div class="card-body">
                            <form id="historical-odds-form">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="mode">操作模式:</label>
                                            <select class="form-control" id="mode" name="mode">
                                                <option value="download">下載模式</option>
                                                <option value="verify">驗證模式</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="days_back">往回天數:</label>
                                            <input type="number" class="form-control" id="days_back" name="days_back"
                                                   value="7" min="1" max="60" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="scraper_type">數據源:</label>
                                            <select class="form-control" id="scraper_type" name="scraper_type">
                                                <option value="modern_sbr">SportsBookReview (推薦)</option>
                                                <option value="covers">Covers.com (備用)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <div class="text-muted small">
                                                從今天往回 <span id="days-display">7</span> 天
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <button type="submit" class="btn btn-primary btn-block" id="download-btn">
                                                <i class="fas fa-play"></i> 開始執行
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>

                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle"></i>
                                <strong>功能說明:</strong>
                                <ul class="mb-0 mt-2">
                                    <li><strong>下載模式:</strong> 下載缺失的盤口數據，跳過已有數據</li>
                                    <li><strong>驗證模式:</strong> 檢查現有數據一致性，自動重新下載不一致的數據</li>
                                    <li>系統從今天開始往回處理指定天數</li>
                                    <li>會顯示下載數量、不一致數量、重新下載數量等統計</li>
                                    <li>API有使用限制，建議分批處理</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 下載進度和結果 -->
            <div class="row mt-4" id="download-results" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">📋 下載結果</h5>
                        </div>
                        <div class="card-body">
                            <div id="download-progress" class="mb-3">
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                            
                            <div id="download-summary" class="alert alert-info" style="display: none;">
                                <!-- 下載摘要將在這裡顯示 -->
                            </div>
                            
                            <div id="download-details">
                                <!-- 詳細結果將在這裡顯示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('historical-odds-form');
    const downloadBtn = document.getElementById('download-btn');
    const resultsDiv = document.getElementById('download-results');
    const progressBar = document.querySelector('.progress-bar');
    const summaryDiv = document.getElementById('download-summary');
    const detailsDiv = document.getElementById('download-details');
    
    // 更新天數顯示
    const daysBackInput = document.getElementById('days_back');
    const daysDisplay = document.getElementById('days-display');

    daysBackInput.addEventListener('input', function() {
        daysDisplay.textContent = this.value;
    });
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const mode = document.getElementById('mode').value;
        const daysBack = parseInt(document.getElementById('days_back').value);
        const scraperType = document.getElementById('scraper_type').value;

        if (!daysBack || daysBack < 1 || daysBack > 60) {
            alert('請輸入有效的天數（1-60）');
            return;
        }

        // 開始處理
        startProcess(mode, daysBack, scraperType);
    });
    
    function startProcess(mode, daysBack, scraperType) {
        downloadBtn.disabled = true;
        const modeText = mode === 'download' ? '下載' : '驗證';
        const scraperText = scraperType === 'modern_sbr' ? 'SportsBookReview' : 'Covers.com';
        downloadBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${modeText}中...`;

        resultsDiv.style.display = 'block';
        progressBar.style.width = '0%';
        summaryDiv.style.display = 'none';
        detailsDiv.innerHTML = `<p>正在準備${modeText}（使用${scraperText}）...</p>`;

        fetch('/admin/api/fetch_historical_odds', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                mode: mode,
                days_back: daysBack,
                scraper_type: scraperType
            })
        })
        .then(response => response.json())
        .then(data => {
            progressBar.style.width = '100%';

            if (data.success) {
                showResults(data);
            } else {
                showError(data.error);
            }
        })
        .catch(error => {
            showError(`${modeText}失敗: ` + error.message);
        })
        .finally(() => {
            downloadBtn.disabled = false;
            downloadBtn.innerHTML = '<i class="fas fa-play"></i> 開始執行';
        });
    }
    
    function showResults(data) {
        const summary = data.summary;
        const mode = data.mode;

        let summaryHtml = `<h6>${mode === 'download' ? '下載' : '驗證'}摘要</h6>`;
        summaryHtml += `<p><strong>日期範圍:</strong> ${data.date_range.start} 至 ${data.date_range.end} (${data.date_range.days}天)</p>`;
        summaryHtml += `<p><strong>總處理日期:</strong> ${summary.total_dates}</p>`;

        if (mode === 'download') {
            summaryHtml += `<p><strong>下載數量:</strong> ${summary.download_count}</p>`;
        } else {
            summaryHtml += `<p><strong>驗證數量:</strong> ${summary.verify_count}</p>`;
            summaryHtml += `<p><strong>不一致數量:</strong> ${summary.inconsistent_count}</p>`;
            summaryHtml += `<p><strong>重新下載數量:</strong> ${summary.redownload_count}</p>`;
        }

        summaryDiv.innerHTML = summaryHtml;
        summaryDiv.className = summary.download_count > 0 || summary.redownload_count > 0 ? 'alert alert-success' : 'alert alert-info';
        summaryDiv.style.display = 'block';

        let detailsHtml = '<h6>詳細結果</h6><div class="table-responsive"><table class="table table-sm">';
        detailsHtml += '<thead><tr><th>日期</th><th>狀態</th><th>比賽數</th><th>詳情</th></tr></thead><tbody>';

        data.results.forEach(result => {
            let statusBadge = '';
            let details = '';

            switch(result.status) {
                case 'downloaded':
                    statusBadge = '<span class="badge badge-success">已下載</span>';
                    details = `匹配 ${result.matched_games} 場，保存 ${result.saved_records} 筆`;
                    break;
                case 'redownloaded':
                    statusBadge = '<span class="badge badge-warning">重新下載</span>';
                    details = `發現 ${result.inconsistencies.length} 個不一致，重新下載 ${result.redownload_records} 筆`;
                    break;
                case 'consistent':
                    statusBadge = '<span class="badge badge-success">一致</span>';
                    details = `${result.odds_count} 筆盤口數據一致`;
                    break;
                case 'inconsistent':
                    statusBadge = '<span class="badge badge-danger">不一致</span>';
                    details = `發現 ${result.inconsistencies.length} 個不一致`;
                    break;
                case 'exists':
                    statusBadge = '<span class="badge badge-info">已存在</span>';
                    details = `${result.odds_count} 筆盤口記錄`;
                    break;
                case 'no_games':
                    statusBadge = '<span class="badge badge-secondary">無比賽</span>';
                    details = result.message;
                    break;
                case 'no_odds':
                    statusBadge = '<span class="badge badge-warning">無盤口</span>';
                    details = result.error;
                    break;
                case 'error':
                    statusBadge = '<span class="badge badge-danger">錯誤</span>';
                    details = result.error;
                    break;
            }

            detailsHtml += `<tr><td>${result.date}</td><td>${statusBadge}</td><td>${result.games_count || 0}</td><td>${details}</td></tr>`;
        });

        detailsHtml += '</tbody></table></div>';
        detailsDiv.innerHTML = detailsHtml;
    }
    
    function showError(error) {
        summaryDiv.innerHTML = `<h6>下載失敗</h6><p>${error}</p>`;
        summaryDiv.className = 'alert alert-danger';
        summaryDiv.style.display = 'block';
        detailsDiv.innerHTML = '';
    }
});
</script>
{% endblock %}
