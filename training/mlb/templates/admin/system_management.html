{% extends "admin/layout.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> 調度器管理</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">啟動或停止系統的自動化任務排程。</p>
                <div class="btn-group w-100">
                    <form method="POST" action="{{ url_for('admin.start_scheduler') }}" class="flex-fill">
                        <button type="submit" class="btn btn-success w-100" {% if system_status.get('scheduler_running') %}disabled{% endif %}>
                            <i class="fas fa-play"></i> 啟動
                        </button>
                    </form>
                    <form method="POST" action="{{ url_for('admin.stop_scheduler') }}" class="flex-fill">
                        <button type="submit" class="btn btn-danger w-100" {% if not system_status.get('scheduler_running') %}disabled{% endif %}>
                            <i class="fas fa-stop"></i> 停止
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-file-alt"></i> 系統日誌</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">查看系統運行日誌以進行故障排除。</p>
                <a href="{{ url_for('admin.view_logs') }}" class="btn btn-outline-info">
                    <i class="fas fa-search"></i> 查看日誌
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
