{% extends "base.html" %}

{% block title %}{{ team.team_name }} - 比賽記錄{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 頁面標題 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-calendar"></i> {{ team.team_name }} - 比賽記錄</h1>
                    <p class="text-muted mb-0">{{ season }} 賽季 - {{ team.league }} {{ team.division }}</p>
                </div>
                <div>
                    <a href="{{ url_for('teams.team_detail', team_id=team.team_id) }}" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回球隊詳情
                    </a>
                </div>
            </div>

            <!-- 賽季選擇 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="row align-items-center">
                                <div class="col-auto">
                                    <label for="season" class="form-label">選擇賽季:</label>
                                </div>
                                <div class="col-auto">
                                    <select name="season" id="season" class="form-select" onchange="this.form.submit()">
                                        {% for year in range(2024, 2020, -1) %}
                                        <option value="{{ year }}" {% if year == season %}selected{% endif %}>
                                            {{ year }} 賽季
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-auto">
                                    <span class="text-muted">
                                        共 {{ games.total }} 場比賽
                                    </span>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 比賽列表 -->
            {% if games.items %}
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> 比賽記錄</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>對手</th>
                                            <th>主/客</th>
                                            <th>比分</th>
                                            <th>結果</th>
                                            <th>狀態</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for game in games.items %}
                                        <tr>
                                            <td>
                                                <strong>{{ game.date.strftime('%m-%d') }}</strong>
                                                <br><small class="text-muted">{{ game.date.strftime('%Y') }}</small>
                                            </td>
                                            <td>
                                                {% if game.home_team == team.team_code %}
                                                    <strong>vs {{ game.away_team }}</strong>
                                                {% else %}
                                                    <strong>@ {{ game.home_team }}</strong>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if game.home_team == team.team_code %}
                                                    <span class="badge bg-success">主場</span>
                                                {% else %}
                                                    <span class="badge bg-primary">客場</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if game.game_status == 'completed' %}
                                                    {% if game.home_team == team.team_code %}
                                                        <strong>{{ game.home_score or 0 }}-{{ game.away_score or 0 }}</strong>
                                                    {% else %}
                                                        <strong>{{ game.away_score or 0 }}-{{ game.home_score or 0 }}</strong>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if game.game_status == 'completed' %}
                                                    {% set team_won = (game.home_team == team.team_code and game.home_score is not none and game.away_score is not none and game.home_score > game.away_score) or (game.away_team == team.team_code and game.away_score is not none and game.home_score is not none and game.away_score > game.home_score) %}
                                                    {% if team_won %}
                                                        <span class="badge bg-success fs-6">勝</span>
                                                    {% else %}
                                                        <span class="badge bg-danger fs-6">敗</span>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ game.game_status }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if game.game_status == 'completed' %}
                                                    <span class="badge bg-success">已完成</span>
                                                {% elif game.game_status == 'scheduled' %}
                                                    <span class="badge bg-info">已排程</span>
                                                {% elif game.game_status == 'postponed' %}
                                                    <span class="badge bg-warning">延期</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ game.game_status }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('games.game_detail', game_id=game.game_id) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> 詳情
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分頁 -->
                            {% if games.pages > 1 %}
                            <nav aria-label="比賽記錄分頁">
                                <ul class="pagination justify-content-center">
                                    {% if games.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('teams.team_games', team_id=team.team_id, page=games.prev_num, season=season) }}">
                                            <i class="fas fa-chevron-left"></i> 上一頁
                                        </a>
                                    </li>
                                    {% endif %}
                                    
                                    {% for page_num in games.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != games.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('teams.team_games', team_id=team.team_id, page=page_num, season=season) }}">
                                                    {{ page_num }}
                                                </a>
                                            </li>
                                            {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                            {% endif %}
                                        {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if games.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('teams.team_games', team_id=team.team_id, page=games.next_num, season=season) }}">
                                            下一頁 <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <!-- 無比賽記錄提示 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暫無比賽記錄</h5>
                            <p class="text-muted">{{ season }} 賽季暫無該球隊的比賽記錄</p>
                            <a href="{{ url_for('teams.team_detail', team_id=team.team_id) }}" 
                               class="btn btn-primary">返回球隊詳情</a>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 統計摘要 -->
            {% if games.items %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-pie"></i> {{ season }} 賽季統計摘要</h6>
                        </div>
                        <div class="card-body">
                            {% set completed_games = games.items | selectattr('game_status', 'equalto', 'completed') | list %}
                            {% set wins = 0 %}
                            {% for game in completed_games %}
                                {% if (game.home_team == team.team_code and game.home_score is not none and game.away_score is not none and game.home_score > game.away_score) or (game.away_team == team.team_code and game.away_score is not none and game.home_score is not none and game.away_score > game.home_score) %}
                                    {% set wins = wins + 1 %}
                                {% endif %}
                            {% endfor %}
                            {% set losses = completed_games | length - wins %}
                            
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <h4 class="text-success">{{ wins }}</h4>
                                        <small class="text-muted">勝場</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <h4 class="text-danger">{{ losses }}</h4>
                                        <small class="text-muted">敗場</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <h4 class="text-primary">
                                            {% if completed_games | length > 0 %}
                                                {{ "%.3f"|format(wins / completed_games | length) }}
                                            {% else %}
                                                .000
                                            {% endif %}
                                        </h4>
                                        <small class="text-muted">勝率</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <h4>{{ completed_games | length }}</h4>
                                        <small class="text-muted">已完成比賽</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.stat-item {
    padding: 15px;
}

.stat-item h4 {
    margin-bottom: 5px;
    font-weight: bold;
}

.table th {
    font-size: 0.9rem;
    font-weight: 600;
}

.table td {
    font-size: 0.9rem;
}
</style>
{% endblock %}
