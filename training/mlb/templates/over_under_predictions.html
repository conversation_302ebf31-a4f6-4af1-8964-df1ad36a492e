{% extends "base.html" %}

{% block title %}大小分預測 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    大小分預測
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshPredictions()">
                        <i class="fas fa-sync-alt me-1"></i>刷新預測
                    </button>
                    <a href="{{ url_for('predictions.predictions_list') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回預測頁面
                    </a>
                </div>
            </div>

            <!-- 統計概覽 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ predictions|length }}</h4>
                                    <p class="card-text">今日比賽</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-baseball-ball fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ over_recommendations }}</h4>
                                    <p class="card-text">推薦大分</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-up fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ under_recommendations }}</h4>
                                    <p class="card-text">推薦小分</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-down fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ average_confidence|round(1) }}%</h4>
                                    <p class="card-text">平均信心度</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-percentage fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 預測列表 -->
            <div class="row">
                {% for prediction in predictions %}
                <div class="col-lg-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <span class="badge bg-secondary me-2">{{ prediction.game.away_team }}</span>
                                    @
                                    <span class="badge bg-primary ms-2">{{ prediction.game.home_team }}</span>
                                </h5>
                                <small class="text-muted">{{ prediction.game.date.strftime('%m/%d %H:%M') }}</small>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 大小分預測 -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1">{{ prediction.predicted_total_runs|round(1) }}</h4>
                                        <small class="text-muted">預測總分</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-secondary mb-1">{{ prediction.over_under_line }}</h4>
                                        <small class="text-muted">大小分盤口</small>
                                        <br>
                                        <small class="text-warning">⚠️ 可能為模擬數據</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 大小分概率 -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>大分 (Over {{ prediction.over_under_line }})</span>
                                    <span class="fw-bold">{{ (prediction.over_probability * 100)|round(1) }}%</span>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-success" style="width: {{ (prediction.over_probability * 100)|round(1) }}%"></div>
                                </div>
                                
                                <div class="d-flex justify-content-between mb-2">
                                    <span>小分 (Under {{ prediction.over_under_line }})</span>
                                    <span class="fw-bold">{{ (prediction.under_probability * 100)|round(1) }}%</span>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-warning" style="width: {{ (prediction.under_probability * 100)|round(1) }}%"></div>
                                </div>
                            </div>

                            <!-- 推薦 -->
                            <div class="alert {% if prediction.over_probability > 0.6 %}alert-success{% elif prediction.under_probability > 0.6 %}alert-warning{% else %}alert-secondary{% endif %} mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        {% if prediction.over_probability > 0.6 %}
                                            <i class="fas fa-arrow-up me-2"></i>推薦大分 (Over {{ prediction.over_under_line }})
                                        {% elif prediction.under_probability > 0.6 %}
                                            <i class="fas fa-arrow-down me-2"></i>推薦小分 (Under {{ prediction.over_under_line }})
                                        {% else %}
                                            <i class="fas fa-minus me-2"></i>中性，不推薦投注
                                        {% endif %}
                                    </div>
                                    <span class="badge {% if prediction.over_under_confidence > 0.7 %}bg-success{% elif prediction.over_under_confidence > 0.5 %}bg-warning{% else %}bg-secondary{% endif %}">
                                        信心度 {{ (prediction.over_under_confidence * 100)|round(1) }}%
                                    </span>
                                </div>
                            </div>

                            <!-- 先發投手信息 -->
                            {% if prediction.starting_pitcher_home or prediction.starting_pitcher_away %}
                            <div class="mb-3">
                                <h6 class="text-muted mb-2">先發投手</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="text-center">
                                            <div class="fw-bold">{{ prediction.starting_pitcher_away or '待確認' }}</div>
                                            <small class="text-muted">客隊投手</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center">
                                            <div class="fw-bold">{{ prediction.starting_pitcher_home or '待確認' }}</div>
                                            <small class="text-muted">主隊投手</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            <!-- 投手對戰優勢 -->
                            {% if prediction.pitcher_matchup_advantage and prediction.pitcher_matchup_advantage != 'neutral' %}
                            <div class="mb-3">
                                <div class="alert alert-info py-2">
                                    <small>
                                        <i class="fas fa-info-circle me-1"></i>
                                        投手對戰優勢: 
                                        {% if prediction.pitcher_matchup_advantage == 'home' %}
                                            主隊佔優
                                        {% elif prediction.pitcher_matchup_advantage == 'away' %}
                                            客隊佔優
                                        {% else %}
                                            勢均力敵
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        <div class="card-footer bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    預測時間: {{ prediction.prediction_date.strftime('%m/%d %H:%M') }}
                                </small>
                                <div>
                                    <a href="{{ url_for('games.game_detail', game_id=prediction.game_id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>詳細
                                    </a>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="showAnalysisModal('{{ prediction.game_id }}')">
                                        <i class="fas fa-chart-bar me-1"></i>分析
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            {% if not predictions %}
            <div class="text-center py-5">
                <i class="fas fa-baseball-ball fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">今日暫無比賽預測</h4>
                <p class="text-muted">請稍後再試或檢查比賽安排</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 分析詳情模態框 -->
<div class="modal fade" id="analysisModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">投手對戰分析</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="analysisContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">載入中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshPredictions() {
    location.reload();
}

function showAnalysisModal(gameId) {
    const modal = new bootstrap.Modal(document.getElementById('analysisModal'));
    const content = document.getElementById('analysisContent');
    
    // 顯示載入中
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">載入中...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // 載入分析數據
    fetch(`/api/over_under_analysis/${gameId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                content.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
            } else {
                content.innerHTML = generateAnalysisHTML(data);
            }
        })
        .catch(error => {
            content.innerHTML = `<div class="alert alert-danger">載入分析數據失敗: ${error.message}</div>`;
        });
}

function generateAnalysisHTML(data) {
    // 這裡可以根據分析數據生成詳細的HTML
    return `
        <div class="row">
            <div class="col-12">
                <h6>關鍵影響因素</h6>
                <ul class="list-group list-group-flush">
                    ${data.key_factors ? data.key_factors.map(factor => `<li class="list-group-item">${factor}</li>`).join('') : '<li class="list-group-item">暫無數據</li>'}
                </ul>
            </div>
        </div>
    `;
}
</script>
{% endblock %}
