{% extends "base.html" %}

{% block title %}批次預測與回測系統 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line"></i> 批次預測與回測系統</h2>
                <div>
                    <a href="{{ url_for('unified_predictions.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回主頁
                    </a>
                </div>
            </div>

            <!-- 功能選擇卡片 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card border-primary h-100">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-calendar-plus"></i> 批次預測</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">批次生成多個日期的預測，支援自定義日期範圍和預測策略。</p>
                            <button class="btn btn-primary" onclick="showBatchPrediction()">
                                <i class="fas fa-play"></i> 開始批次預測
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-success h-100">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-chart-bar"></i> 歷史回測</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">分析歷史預測準確性，評估模型表現和改進方向。</p>
                            <button class="btn btn-success" onclick="showBacktest()">
                                <i class="fas fa-search"></i> 開始回測
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-info h-100">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-cog"></i> 算式優化</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">測試和比較不同預測算式的表現，找出最佳配置。</p>
                            <button class="btn btn-info" onclick="showAlgorithmTest()">
                                <i class="fas fa-flask"></i> 算式測試
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批次預測區域 -->
            <div id="batchPredictionArea" class="card mb-4" style="display: none;">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-calendar-plus"></i> 批次預測設定</h5>
                </div>
                <div class="card-body">
                    <form id="batchPredictionForm">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="startDate" class="form-label">開始日期</label>
                                <input type="date" class="form-control" id="startDate" required>
                            </div>
                            <div class="col-md-3">
                                <label for="endDate" class="form-label">結束日期</label>
                                <input type="date" class="form-control" id="endDate" required>
                            </div>
                            <div class="col-md-3">
                                <label for="predictionEngine" class="form-label">預測引擎</label>
                                <select class="form-control" id="predictionEngine">
                                    <option value="enhanced">增強預測引擎</option>
                                    <option value="standard">標準預測引擎</option>
                                    <option value="experimental">實驗性引擎</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="batchStrategy" class="form-label">處理策略</label>
                                <select class="form-control" id="batchStrategy">
                                    <option value="skip_existing">跳過已有預測</option>
                                    <option value="update_all">更新所有預測</option>
                                    <option value="force_regenerate">強制重新生成</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="excludeWeekends">
                                    <label class="form-check-label" for="excludeWeekends">
                                        排除週末比賽
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="saveResults" checked>
                                    <label class="form-check-label" for="saveResults">
                                        保存預測結果
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                                    <label class="form-check-label" for="enableNotifications">
                                        啟用進度通知
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="generateReport" checked>
                                    <label class="form-check-label" for="generateReport">
                                        生成摘要報告
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary btn-lg" id="startBatchBtn">
                                    <i class="fas fa-rocket"></i> 開始批次預測
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hideBatchPrediction()">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 歷史回測區域 -->
            <div id="backtestArea" class="card mb-4" style="display: none;">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-chart-bar"></i> 歷史回測設定</h5>
                </div>
                <div class="card-body">
                    <form id="backtestForm">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="backtestStartDate" class="form-label">回測開始日期</label>
                                <input type="date" class="form-control" id="backtestStartDate" required>
                            </div>
                            <div class="col-md-4">
                                <label for="backtestEndDate" class="form-label">回測結束日期</label>
                                <input type="date" class="form-control" id="backtestEndDate" required>
                            </div>
                            <div class="col-md-4">
                                <label for="backtestMetrics" class="form-label">評估指標</label>
                                <select class="form-control" id="backtestMetrics" multiple>
                                    <option value="accuracy" selected>預測準確度</option>
                                    <option value="score_difference" selected>分數差異</option>
                                    <option value="over_under" selected>大小分準確度</option>
                                    <option value="spread" selected>讓分盤準確度</option>
                                    <option value="confidence" selected>信心度分析</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label class="form-label">回測範圍</label>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="backtestScope" id="allGames" value="all" checked>
                                    <label class="form-check-label" for="allGames">所有比賽</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="backtestScope" id="highConfidence" value="high_confidence">
                                    <label class="form-check-label" for="highConfidence">高信心度預測</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="backtestScope" id="specificTeams" value="teams">
                                    <label class="form-check-label" for="specificTeams">特定球隊</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-success btn-lg" id="startBacktestBtn">
                                    <i class="fas fa-chart-line"></i> 開始回測分析
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hideBacktest()">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 算式測試區域 -->
            <div id="algorithmTestArea" class="card mb-4" style="display: none;">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-flask"></i> 算式測試設定</h5>
                </div>
                <div class="card-body">
                    <form id="algorithmTestForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="testDateRange" class="form-label">測試日期範圍</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="testStartDate" required>
                                    <span class="input-group-text">至</span>
                                    <input type="date" class="form-control" id="testEndDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="algorithmsToTest" class="form-label">測試算式</label>
                                <select class="form-control" id="algorithmsToTest" multiple>
                                    <option value="enhanced" selected>增強預測引擎</option>
                                    <option value="machine_learning" selected>機器學習模型</option>
                                    <option value="statistical">統計學模型</option>
                                    <option value="ensemble">集成學習</option>
                                    <option value="deep_learning">深度學習</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label class="form-label">測試參數</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="testConfidenceThresholds" checked>
                                    <label class="form-check-label" for="testConfidenceThresholds">
                                        測試不同信心度閾值
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="testFeatureWeights" checked>
                                    <label class="form-check-label" for="testFeatureWeights">
                                        測試特徵權重組合
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="testSeasonalAdjustments">
                                    <label class="form-check-label" for="testSeasonalAdjustments">
                                        測試季節性調整
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-info btn-lg" id="startAlgorithmTestBtn">
                                    <i class="fas fa-play"></i> 開始算式測試
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hideAlgorithmTest()">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 進度顯示區域 -->
            <div id="progressArea" class="card mb-4" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-clock"></i> 處理進度</h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" 
                             id="progressBar" 
                             style="width: 0%" 
                             aria-valuenow="0" 
                             aria-valuemin="0" 
                             aria-valuemax="100">0%</div>
                    </div>
                    <div id="progressStatus" class="text-muted">準備中...</div>
                    <div id="progressDetails" class="mt-2"></div>
                    <div class="mt-3">
                        <button class="btn btn-warning" onclick="pauseProcess()" id="pauseBtn" style="display: none;">
                            <i class="fas fa-pause"></i> 暫停
                        </button>
                        <button class="btn btn-danger" onclick="stopProcess()" id="stopBtn" style="display: none;">
                            <i class="fas fa-stop"></i> 停止
                        </button>
                    </div>
                </div>
            </div>

            <!-- 結果顯示區域 -->
            <div id="resultsArea" class="card" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 處理結果</h5>
                </div>
                <div class="card-body">
                    <div id="resultsSummary"></div>
                    <div id="resultsDetails" class="mt-4"></div>
                    <div class="mt-4">
                        <button class="btn btn-primary" onclick="downloadResults()">
                            <i class="fas fa-download"></i> 下載報告
                        </button>
                        <button class="btn btn-secondary" onclick="resetSystem()">
                            <i class="fas fa-refresh"></i> 重置系統
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 批次預測功能
function showBatchPrediction() {
    hideAllAreas();
    document.getElementById('batchPredictionArea').style.display = 'block';
    
    // 設置默認日期
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('startDate').value = lastWeek.toISOString().split('T')[0];
    document.getElementById('endDate').value = today.toISOString().split('T')[0];
}

function hideBatchPrediction() {
    document.getElementById('batchPredictionArea').style.display = 'none';
}

// 歷史回測功能
function showBacktest() {
    hideAllAreas();
    document.getElementById('backtestArea').style.display = 'block';
    
    // 設置默認回測日期範圍（過去30天）
    const today = new Date();
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    document.getElementById('backtestStartDate').value = lastMonth.toISOString().split('T')[0];
    document.getElementById('backtestEndDate').value = today.toISOString().split('T')[0];
}

function hideBacktest() {
    document.getElementById('backtestArea').style.display = 'none';
}

// 算式測試功能
function showAlgorithmTest() {
    hideAllAreas();
    document.getElementById('algorithmTestArea').style.display = 'block';
    
    // 設置默認測試日期範圍
    const today = new Date();
    const twoWeeksAgo = new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000);
    
    document.getElementById('testStartDate').value = twoWeeksAgo.toISOString().split('T')[0];
    document.getElementById('testEndDate').value = today.toISOString().split('T')[0];
}

function hideAlgorithmTest() {
    document.getElementById('algorithmTestArea').style.display = 'none';
}

function hideAllAreas() {
    document.getElementById('batchPredictionArea').style.display = 'none';
    document.getElementById('backtestArea').style.display = 'none';
    document.getElementById('algorithmTestArea').style.display = 'none';
    document.getElementById('progressArea').style.display = 'none';
    document.getElementById('resultsArea').style.display = 'none';
}

// 批次預測表單提交
document.getElementById('batchPredictionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value,
        engine: document.getElementById('predictionEngine').value,
        strategy: document.getElementById('batchStrategy').value,
        excludeWeekends: document.getElementById('excludeWeekends').checked,
        saveResults: document.getElementById('saveResults').checked,
        enableNotifications: document.getElementById('enableNotifications').checked,
        generateReport: document.getElementById('generateReport').checked
    };
    
    startBatchPrediction(formData);
});

// 歷史回測表單提交
document.getElementById('backtestForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const metrics = Array.from(document.getElementById('backtestMetrics').selectedOptions).map(option => option.value);
    const scope = document.querySelector('input[name="backtestScope"]:checked').value;
    
    const formData = {
        startDate: document.getElementById('backtestStartDate').value,
        endDate: document.getElementById('backtestEndDate').value,
        metrics: metrics,
        scope: scope
    };
    
    startBacktest(formData);
});

// 算式測試表單提交
document.getElementById('algorithmTestForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const algorithms = Array.from(document.getElementById('algorithmsToTest').selectedOptions).map(option => option.value);
    
    const formData = {
        startDate: document.getElementById('testStartDate').value,
        endDate: document.getElementById('testEndDate').value,
        algorithms: algorithms,
        testConfidenceThresholds: document.getElementById('testConfidenceThresholds').checked,
        testFeatureWeights: document.getElementById('testFeatureWeights').checked,
        testSeasonalAdjustments: document.getElementById('testSeasonalAdjustments').checked
    };
    
    startAlgorithmTest(formData);
});

// 開始批次預測
function startBatchPrediction(formData) {
    hideAllAreas();
    showProgress('批次預測');
    
    // 發送批次預測請求
    fetch('/batch_system/api/batch/prediction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            trackProgress(data.task_id, 'batch_prediction');
        } else {
            showError('批次預測啟動失敗: ' + data.error);
        }
    })
    .catch(error => {
        showError('網絡錯誤: ' + error.message);
    });
}

// 開始歷史回測
function startBacktest(formData) {
    hideAllAreas();
    showProgress('歷史回測');
    
    // 發送回測請求
    fetch('/batch_system/api/batch/backtest', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            trackProgress(data.task_id, 'backtest');
        } else {
            showError('歷史回測啟動失敗: ' + data.error);
        }
    })
    .catch(error => {
        showError('網絡錯誤: ' + error.message);
    });
}

// 開始算式測試
function startAlgorithmTest(formData) {
    hideAllAreas();
    showProgress('算式測試');
    
    // 發送算式測試請求
    fetch('/batch_system/api/batch/algorithm_test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            trackProgress(data.task_id, 'algorithm_test');
        } else {
            showError('算式測試啟動失敗: ' + data.error);
        }
    })
    .catch(error => {
        showError('網絡錯誤: ' + error.message);
    });
}

// 顯示進度
function showProgress(taskType) {
    document.getElementById('progressArea').style.display = 'block';
    document.getElementById('progressStatus').textContent = taskType + ' 處理中...';
    document.getElementById('pauseBtn').style.display = 'inline-block';
    document.getElementById('stopBtn').style.display = 'inline-block';
}

// 追蹤處理進度
function trackProgress(taskId, taskType) {
    const interval = setInterval(() => {
        fetch(`/batch_system/api/batch/status/${taskId}`)
        .then(response => response.json())
        .then(data => {
            updateProgressBar(data.progress);
            updateProgressStatus(data.status, data.details);
            
            if (data.status === 'completed') {
                clearInterval(interval);
                showResults(data.results, taskType);
            } else if (data.status === 'failed') {
                clearInterval(interval);
                showError('處理失敗: ' + data.error);
            }
        })
        .catch(error => {
            console.error('進度追蹤錯誤:', error);
        });
    }, 2000); // 每2秒檢查一次進度
}

// 更新進度條
function updateProgressBar(progress) {
    const progressBar = document.getElementById('progressBar');
    progressBar.style.width = progress + '%';
    progressBar.textContent = progress + '%';
    progressBar.setAttribute('aria-valuenow', progress);
}

// 更新進度狀態
function updateProgressStatus(status, details) {
    document.getElementById('progressStatus').textContent = status;
    if (details) {
        document.getElementById('progressDetails').innerHTML = details;
    }
}

// 顯示結果
function showResults(results, taskType) {
    document.getElementById('progressArea').style.display = 'none';
    document.getElementById('resultsArea').style.display = 'block';
    
    // 根據任務類型顯示不同的結果格式
    const summaryDiv = document.getElementById('resultsSummary');
    const detailsDiv = document.getElementById('resultsDetails');
    
    if (taskType === 'batch_prediction') {
        displayBatchPredictionResults(results, summaryDiv, detailsDiv);
    } else if (taskType === 'backtest') {
        displayBacktestResults(results, summaryDiv, detailsDiv);
    } else if (taskType === 'algorithm_test') {
        displayAlgorithmTestResults(results, summaryDiv, detailsDiv);
    }
}

// 顯示批次預測結果
function displayBatchPredictionResults(results, summaryDiv, detailsDiv) {
    summaryDiv.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${results.total_games}</h4>
                        <p>處理比賽數</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${results.successful_predictions}</h4>
                        <p>成功預測</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>${results.avg_confidence ? results.avg_confidence.toFixed(1) : '0.0'}%</h4>
                        <p>平均信心度</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>${results.processing_time}s</h4>
                        <p>處理時間</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 獲取預測詳細數據並顯示表格
    detailsDiv.innerHTML = `
        <div class="mb-3">
            <h6>處理詳情</h6>
            <ul class="list-group mb-4">
                <li class="list-group-item">日期範圍: ${results.date_range.start} 至 ${results.date_range.end}</li>
                <li class="list-group-item">使用引擎: ${results.engine_used}</li>
                <li class="list-group-item">處理策略: ${results.strategy_used}</li>
                <li class="list-group-item">跳過比賽: ${results.skipped_games} 場</li>
            </ul>
        </div>
        <div class="mb-3">
            <h6>預測結果詳情</h6>
            <div id="loadingPredictions" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">載入中...</span>
                </div>
                <p class="mt-2">載入預測結果...</p>
            </div>
            <div id="predictionsTable" style="display: none;"></div>
        </div>
    `;
    
    // 載入預測詳細數據
    loadPredictionDetails(results.date_range.start, results.date_range.end);
}

// 載入預測詳細數據
function loadPredictionDetails(startDate, endDate) {
    // 調用查詢API獲取詳細預測數據
    const loadingDiv = document.getElementById('loadingPredictions');
    const tableDiv = document.getElementById('predictionsTable');
    
    // 生成日期範圍內的所有日期
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dates = [];
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        dates.push(d.toISOString().split('T')[0]);
    }
    
    // 並行獲取所有日期的預測數據
    const promises = dates.map(date => 
        fetch(`/unified/api/predictions/date/${date}`)
            .then(response => response.json())
            .catch(error => {
                console.warn(`載入 ${date} 的數據失敗:`, error);
                return { success: false, predictions: [] };
            })
    );
    
    Promise.all(promises)
        .then(results => {
            // 合併所有預測數據
            let allPredictions = [];
            results.forEach(result => {
                if (result.success && result.predictions) {
                    allPredictions = allPredictions.concat(result.predictions);
                }
            });
            
            if (allPredictions.length > 0) {
                // 按日期排序
                allPredictions.sort((a, b) => {
                    const dateA = new Date(a.game_date || a.date || '1970-01-01');
                    const dateB = new Date(b.game_date || b.date || '1970-01-01');
                    return dateB - dateA;
                });
                
                // 顯示預測結果表格
                displayPredictionsTable(allPredictions, tableDiv);
                loadingDiv.style.display = 'none';
                tableDiv.style.display = 'block';
            } else {
                loadingDiv.innerHTML = '<p class="text-muted">沒有找到預測數據</p>';
            }
        })
        .catch(error => {
            console.error('載入預測詳情失敗:', error);
            loadingDiv.innerHTML = 
                '<p class="text-danger">載入失敗，請稍後再試</p>';
        });
}

// 顯示預測結果表格
function displayPredictionsTable(predictions, container) {
    const tableHTML = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>比賽</th>
                        <th>預測得分</th>
                        <th>實際得分</th>
                        <th>勝負預測</th>
                        <th>大小分</th>
                        <th>讓分盤</th>
                        <th>信心度</th>
                        <th>預測時間</th>
                    </tr>
                </thead>
                <tbody>
                    ${predictions.map(pred => {
                        // 從API響應中提取數據
                        const awayTeam = pred.away_team || pred.team_away || 'Away';
                        const homeTeam = pred.home_team || pred.team_home || 'Home';
                        const predictedAwayScore = pred.predicted_away_score || 0;
                        const predictedHomeScore = pred.predicted_home_score || 0;
                        const predictedTotal = predictedAwayScore + predictedHomeScore;
                        
                        // 處理大小分數據
                        const overUnderLine = pred.over_under_line || pred.total_point || 
                                             (pred.betting_odds && pred.betting_odds.totals && pred.betting_odds.totals.total_point);
                        
                        // 處理讓分數據 
                        const homeSpread = pred.home_spread_point !== undefined ? pred.home_spread_point :
                                          (pred.betting_odds && pred.betting_odds.spreads && pred.betting_odds.spreads.home_spread_point);
                        
                        return `
                            <tr>
                                <td>
                                    <strong>${awayTeam} @ ${homeTeam}</strong><br>
                                    <small class="text-muted">${pred.game_id || ''}</small>
                                </td>
                                <td>
                                    ${awayTeam}: ${predictedAwayScore.toFixed(1)}<br>
                                    ${homeTeam}: ${predictedHomeScore.toFixed(1)}<br>
                                    <small class="text-muted">總分: ${predictedTotal.toFixed(1)}</small>
                                </td>
                                <td>
                                    ${pred.actual_away_score !== null && pred.actual_home_score !== null ? 
                                        `${awayTeam}: ${pred.actual_away_score}<br>
                                         ${homeTeam}: ${pred.actual_home_score}<br>
                                         <small class="text-muted">總分: ${(pred.actual_away_score + pred.actual_home_score)}</small>` :
                                        '<span class="text-muted">未完成</span>'
                                    }
                                </td>
                                <td>
                                    ${predictedHomeScore > predictedAwayScore ? 
                                        `<span class="badge bg-success">主隊</span>` :
                                        `<span class="badge bg-primary">客隊</span>`
                                    }
                                </td>
                                <td>
                                    ${overUnderLine ? `
                                        <div class="text-center">
                                            <span class="badge bg-info">${parseFloat(overUnderLine).toFixed(1)}</span><br>
                                            ${predictedTotal > parseFloat(overUnderLine) ? 
                                                '<span class="badge bg-warning text-dark">大</span>' :
                                                '<span class="badge bg-secondary">小</span>'
                                            }
                                        </div>
                                    ` : '<span class="text-muted">無數據</span>'}
                                </td>
                                <td>
                                    ${homeSpread !== undefined && homeSpread !== null ? `
                                        <div class="text-center">
                                            ${parseFloat(homeSpread) > 0 ? 
                                                `<span class="badge bg-danger">受讓 ${Math.abs(parseFloat(homeSpread)).toFixed(1)}</span>` :
                                                `<span class="badge bg-success">讓 ${Math.abs(parseFloat(homeSpread)).toFixed(1)}</span>`
                                            }<br>
                                            ${((predictedHomeScore - predictedAwayScore) + parseFloat(homeSpread)) > 0 ? 
                                                '<span class="badge bg-primary">主</span>' :
                                                '<span class="badge bg-info">客</span>'
                                            }
                                        </div>
                                    ` : '<span class="text-muted">無數據</span>'}
                                </td>
                                <td>
                                    <span class="badge bg-${(pred.confidence || 0) > 0.7 ? 'success' : (pred.confidence || 0) > 0.5 ? 'warning' : 'secondary'}">
                                        ${((pred.confidence || 0) * 100).toFixed(1)}%
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">${pred.prediction_date ? new Date(pred.prediction_date).toLocaleString('zh-TW') : 'N/A'}</small>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHTML;
}

// 顯示回測結果
function displayBacktestResults(results, summaryDiv, detailsDiv) {
    summaryDiv.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${results.total_predictions}</h4>
                        <p>分析預測數</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${results.accuracy.toFixed(1)}%</h4>
                        <p>總體準確度</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>${results.over_under_accuracy.toFixed(1)}%</h4>
                        <p>大小分準確度</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>${results.avg_score_diff.toFixed(1)}</h4>
                        <p>平均分數差異</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 顯示詳細回測分析
    detailsDiv.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>表現指標</h6>
                <canvas id="performanceChart" width="400" height="200"></canvas>
            </div>
            <div class="col-md-6">
                <h6>改進建議</h6>
                <ul class="list-group">
                    ${results.improvement_suggestions.map(suggestion => `<li class="list-group-item">${suggestion}</li>`).join('')}
                </ul>
            </div>
        </div>
    `;
}

// 顯示算式測試結果
function displayAlgorithmTestResults(results, summaryDiv, detailsDiv) {
    summaryDiv.innerHTML = `
        <div class="row">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${results.algorithms_tested}</h4>
                        <p>測試算式數</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${results.best_algorithm}</h4>
                        <p>最佳算式</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>${results.improvement_percent.toFixed(1)}%</h4>
                        <p>性能提升</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    detailsDiv.innerHTML = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>算式名稱</th>
                        <th>準確度</th>
                        <th>信心度</th>
                        <th>處理速度</th>
                        <th>推薦度</th>
                    </tr>
                </thead>
                <tbody>
                    ${results.algorithm_comparison.map(alg => `
                        <tr>
                            <td>${alg.name}</td>
                            <td>${alg.accuracy.toFixed(1)}%</td>
                            <td>${alg.confidence.toFixed(1)}%</td>
                            <td>${alg.speed}</td>
                            <td>
                                ${Array.from({length: 5}, (_, i) => 
                                    i < alg.recommendation ? '<i class="fas fa-star text-warning"></i>' : '<i class="far fa-star"></i>'
                                ).join('')}
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// 錯誤處理
function showError(message) {
    alert('錯誤: ' + message);
    document.getElementById('progressArea').style.display = 'none';
}

// 暫停處理
function pauseProcess() {
    // 實現暫停邏輯
    console.log('暫停處理');
}

// 停止處理
function stopProcess() {
    // 實現停止邏輯
    console.log('停止處理');
}

// 下載報告
function downloadResults() {
    // 實現下載邏輯
    console.log('下載報告');
}

// 重置系統
function resetSystem() {
    hideAllAreas();
    location.reload();
}

// 頁面載入時設置
document.addEventListener('DOMContentLoaded', function() {
    // 初始化多選框
    const selectElements = document.querySelectorAll('select[multiple]');
    selectElements.forEach(select => {
        select.setAttribute('size', '4');
    });
});
</script>
{% endblock %}