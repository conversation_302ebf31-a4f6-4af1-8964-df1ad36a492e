{% extends "base.html" %}

{% block title %}{{ team.team_name }} - 球員名單{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 頁面標題 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-users"></i> {{ team.team_name }} - 球員名單</h1>
                    <p class="text-muted mb-0">{{ team.league }} - {{ team.division }}</p>
                </div>
                <div>
                    <a href="{{ url_for('teams.team_detail', team_id=team.team_id) }}" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回球隊詳情
                    </a>
                </div>
            </div>

            <!-- 打者統計 -->
            {% if batters %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-baseball-ball"></i> 打者統計 ({{ batters|length }} 人)</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>球員</th>
                                            <th>打擊率</th>
                                            <th>上壘率</th>
                                            <th>長打率</th>
                                            <th>全壘打</th>
                                            <th>打點</th>
                                            <th>得分</th>
                                            <th>安打</th>
                                            <th>打數</th>
                                            <th>盜壘</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for player in batters %}
                                        <tr>
                                            <td>
                                                <strong>{{ player.player_name or '未知' }}</strong>
                                                <br><small class="text-muted">ID: {{ player.player_id }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if player.batting_avg > 0.300 else 'warning' if player.batting_avg > 0.250 else 'secondary' }}">
                                                    {{ "%.3f"|format(player.batting_avg or 0) }}
                                                </span>
                                            </td>
                                            <td>{{ "%.3f"|format(player.on_base_pct or 0) }}</td>
                                            <td>{{ "%.3f"|format(player.slugging_pct or 0) }}</td>
                                            <td>{{ player.home_runs or 0 }}</td>
                                            <td>{{ player.rbi or 0 }}</td>
                                            <td>{{ player.runs or 0 }}</td>
                                            <td>{{ player.hits or 0 }}</td>
                                            <td>{{ player.at_bats or 0 }}</td>
                                            <td>{{ player.stolen_bases or 0 }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 投手統計 -->
            {% if pitchers %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-hand-paper"></i> 投手統計 ({{ pitchers|length }} 人)</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>球員</th>
                                            <th>防禦率</th>
                                            <th>WHIP</th>
                                            <th>勝場</th>
                                            <th>敗場</th>
                                            <th>救援</th>
                                            <th>三振</th>
                                            <th>四壞球</th>
                                            <th>投球局數</th>
                                            <th>被安打</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for player in pitchers %}
                                        <tr>
                                            <td>
                                                <strong>{{ player.player_name or '未知' }}</strong>
                                                <br><small class="text-muted">ID: {{ player.player_id }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if player.era < 3.00 else 'warning' if player.era < 4.00 else 'secondary' }}">
                                                    {{ "%.2f"|format(player.era or 0) }}
                                                </span>
                                            </td>
                                            <td>{{ "%.3f"|format(player.whip or 0) }}</td>
                                            <td>{{ player.wins or 0 }}</td>
                                            <td>{{ player.losses or 0 }}</td>
                                            <td>{{ player.saves or 0 }}</td>
                                            <td>{{ player.strikeouts or 0 }}</td>
                                            <td>{{ player.walks or 0 }}</td>
                                            <td>{{ "%.1f"|format(player.innings_pitched or 0) }}</td>
                                            <td>{{ player.hits_allowed or 0 }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 無球員數據提示 -->
            {% if not batters and not pitchers %}
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暫無球員數據</h5>
                            <p class="text-muted">該球隊目前沒有可用的球員統計數據</p>
                            <a href="{{ url_for('teams.team_detail', team_id=team.team_id) }}" 
                               class="btn btn-primary">返回球隊詳情</a>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 統計摘要 -->
            {% if batters or pitchers %}
            <div class="row">
                <div class="col-md-6">
                    {% if batters %}
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-bar"></i> 打者統計摘要</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h6>{{ batters|length }}</h6>
                                        <small class="text-muted">打者人數</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h6>{{ "%.3f"|format((batters|sum(attribute='batting_avg') / batters|length) if batters else 0) }}</h6>
                                        <small class="text-muted">平均打擊率</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h6>{{ batters|sum(attribute='home_runs') }}</h6>
                                        <small class="text-muted">總全壘打</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    {% if pitchers %}
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-line"></i> 投手統計摘要</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h6>{{ pitchers|length }}</h6>
                                        <small class="text-muted">投手人數</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h6>{{ "%.2f"|format((pitchers|sum(attribute='era') / pitchers|length) if pitchers else 0) }}</h6>
                                        <small class="text-muted">平均防禦率</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h6>{{ pitchers|sum(attribute='strikeouts') }}</h6>
                                        <small class="text-muted">總三振</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.stat-item {
    padding: 10px;
}

.stat-item h6 {
    margin-bottom: 5px;
    font-weight: bold;
}

.table th {
    font-size: 0.9rem;
    font-weight: 600;
}

.table td {
    font-size: 0.9rem;
}
</style>
{% endblock %}
