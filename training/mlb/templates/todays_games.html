{% extends "base.html" %}

{% block title %}今日比賽 - {{ date.strftime('%Y年%m月%d日') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-calendar-day"></i> 
                    今日比賽 - {{ date.strftime('%Y年%m月%d日') }}
                    <small class="text-muted">({{ date.strftime('%A') }})</small>
                </h2>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('games.games_list') }}" class="btn btn-outline-primary">所有比賽</a>
                    <button type="button" class="btn btn-outline-success" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>

            {% if games %}
                <!-- 比賽狀態統計 -->
                <div class="row mb-4">
                    {% set completed_count = games|selectattr('game_status', 'equalto', 'completed')|list|length %}
                    {% set in_progress_count = games|selectattr('game_status', 'equalto', 'in_progress')|list|length %}
                    {% set scheduled_count = games|selectattr('game_status', 'equalto', 'scheduled')|list|length %}
                    
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary">{{ games|length }}</h3>
                                <p class="card-text">總比賽數</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success">{{ completed_count }}</h3>
                                <p class="card-text">已完成</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-warning">{{ in_progress_count }}</h3>
                                <p class="card-text">進行中</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info">{{ scheduled_count }}</h3>
                                <p class="card-text">未開始</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 比賽列表 -->
                <div class="row">
                    {% for game in games %}
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 game-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    {% if game.game_time %}
                                        {{ game.game_time.strftime('%H:%M') }}
                                    {% else %}
                                        時間待定
                                    {% endif %}
                                </small>
                                <span class="badge bg-{{ 'success' if game.game_status == 'completed' else 'warning' if game.game_status == 'in_progress' else 'info' if game.game_status == 'scheduled' else 'secondary' }}">
                                    {% if game.game_status == 'completed' %}
                                        已結束
                                    {% elif game.game_status == 'in_progress' %}
                                        進行中
                                    {% elif game.game_status == 'scheduled' %}
                                        未開始
                                    {% elif game.game_status == 'postponed' %}
                                        延期
                                    {% else %}
                                        {{ game.game_status }}
                                    {% endif %}
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="row text-center mb-3">
                                    <div class="col-5">
                                        <h6 class="mb-1">{{ game.away_team }}</h6>
                                        <div class="fs-3 fw-bold text-primary">
                                            {% if game.game_status == 'completed' %}
                                                {{ game.away_score or 0 }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">客隊</small>
                                    </div>
                                    <div class="col-2 d-flex align-items-center justify-content-center">
                                        <span class="text-muted fs-4">VS</span>
                                    </div>
                                    <div class="col-5">
                                        <h6 class="mb-1">{{ game.home_team }}</h6>
                                        <div class="fs-3 fw-bold text-success">
                                            {% if game.game_status == 'completed' %}
                                                {{ game.home_score or 0 }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">主隊</small>
                                    </div>
                                </div>

                                {% if game.game_status == 'completed' %}
                                    <div class="text-center mb-3">
                                        {% if game.home_score is not none and game.away_score is not none and game.home_score > game.away_score %}
                                            <span class="badge bg-success">{{ game.home_team }} 獲勝</span>
                                        {% elif game.away_score is not none and game.home_score is not none and game.away_score > game.home_score %}
                                            <span class="badge bg-primary">{{ game.away_team }} 獲勝</span>
                                        {% else %}
                                            <span class="badge bg-secondary">平局</span>
                                        {% endif %}
                                    </div>
                                {% elif game.game_status == 'in_progress' %}
                                    <div class="text-center mb-3">
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-play-circle"></i> 比賽進行中
                                        </span>
                                    </div>
                                {% elif game.game_status == 'scheduled' %}
                                    <div class="text-center mb-3">
                                        <span class="badge bg-info text-dark">
                                            <i class="fas fa-clock"></i> 等待開始
                                        </span>
                                    </div>
                                {% endif %}

                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        ID: {{ game.game_id }}
                                    </small>
                                    <div>
                                        <a href="{{ url_for('games.game_detail', game_id=game.game_id) }}" 
                                           class="btn btn-sm btn-outline-primary me-1">詳情</a>
                                        {% if game.game_status == 'scheduled' %}
                                            <a href="{{ url_for('predictions.prediction_detail', game_id=game.game_id) }}" 
                                               class="btn btn-sm btn-outline-info">預測</a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- 快速導航 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <h6 class="card-title">快速導航</h6>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('games.games_list', date=(date - timedelta(days=1)).strftime('%Y-%m-%d')) }}" 
                                       class="btn btn-outline-secondary">
                                        <i class="fas fa-chevron-left"></i> 昨天
                                    </a>
                                    <a href="{{ url_for('games.todays_games') }}" 
                                       class="btn btn-primary">
                                        <i class="fas fa-calendar-day"></i> 今天
                                    </a>
                                    <a href="{{ url_for('games.games_list', date=(date + timedelta(days=1)).strftime('%Y-%m-%d')) }}" 
                                       class="btn btn-outline-secondary">
                                        明天 <i class="fas fa-chevron-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">今天沒有比賽</h4>
                    <p class="text-muted">今天是休息日，沒有安排MLB比賽</p>
                    <div class="mt-4">
                        <a href="{{ url_for('games.games_list') }}" class="btn btn-primary">
                            <i class="fas fa-calendar-alt"></i> 查看其他日期的比賽
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 自動刷新進行中的比賽
document.addEventListener('DOMContentLoaded', function() {
    const inProgressGames = document.querySelectorAll('.badge:contains("進行中")');
    
    if (inProgressGames.length > 0) {
        // 每30秒刷新一次頁面（如果有進行中的比賽）
        setTimeout(function() {
            location.reload();
        }, 30000);
    }
});

// 添加實時時間顯示
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-TW', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    // 如果頁面上有時間顯示元素，更新它
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// 每秒更新時間
setInterval(updateCurrentTime, 1000);
updateCurrentTime();
</script>
{% endblock %}
