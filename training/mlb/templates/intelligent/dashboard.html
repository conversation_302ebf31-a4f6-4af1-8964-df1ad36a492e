{% extends "base.html" %}

{% block title %}智能預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-brain"></i> 智能預測系統
                    </h3>
                    <p class="mb-0 mt-2">根據投手質量自動選擇預測策略，無需手動選擇模型</p>
                </div>
                <div class="card-body">
                    <!-- 系統特點 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-search-plus fa-2x text-success mb-2"></i>
                                    <h6>自動分析投手</h6>
                                    <small class="text-muted">王牌/優秀/普通/弱勢</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-chess fa-2x text-info mb-2"></i>
                                    <h6>智能選擇策略</h6>
                                    <small class="text-muted">王牌對決/打擊戰/強弱對戰</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-adjust fa-2x text-warning mb-2"></i>
                                    <h6>自動調整預測</h6>
                                    <small class="text-muted">低分/高分/平衡調整</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-magic fa-2x text-primary mb-2"></i>
                                    <h6>一鍵預測</h6>
                                    <small class="text-muted">無需手動選擇模型</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速預測 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-calendar-day"></i> 今天的比賽 ({{ today }})</h5>
                                </div>
                                <div class="card-body">
                                    {% if today_games %}
                                        <div class="list-group">
                                            {% for game in today_games %}
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>{{ game.away_team }} @ {{ game.home_team }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        狀態: {{ game.game_status }}
                                                        {% if game.home_score is not none and game.away_score is not none %}
                                                        | 比分: {{ game.away_score }}-{{ game.home_score }}
                                                        {% endif %}
                                                    </small>
                                                </div>
                                                <button class="btn btn-primary btn-sm predict-btn" 
                                                        data-game-id="{{ game.game_id }}"
                                                        data-home-team="{{ game.home_team }}"
                                                        data-away-team="{{ game.away_team }}"
                                                        data-date="{{ game.date }}">
                                                    <i class="fas fa-brain"></i> 智能預測
                                                </button>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <p class="text-muted">今天沒有比賽</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-calendar-plus"></i> 明天的比賽 ({{ tomorrow }})</h5>
                                </div>
                                <div class="card-body">
                                    {% if tomorrow_games %}
                                        <div class="list-group">
                                            {% for game in tomorrow_games %}
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>{{ game.away_team }} @ {{ game.home_team }}</strong>
                                                    <br>
                                                    <small class="text-muted">狀態: {{ game.game_status }}</small>
                                                </div>
                                                <button class="btn btn-outline-primary btn-sm predict-btn" 
                                                        data-game-id="{{ game.game_id }}"
                                                        data-home-team="{{ game.home_team }}"
                                                        data-away-team="{{ game.away_team }}"
                                                        data-date="{{ game.date }}">
                                                    <i class="fas fa-brain"></i> 智能預測
                                                </button>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <p class="text-muted">明天沒有比賽</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批量預測 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-layer-group"></i> 批量預測</h5>
                                </div>
                                <div class="card-body">
                                    <form id="batchPredictForm" class="row g-3">
                                        <div class="col-md-4">
                                            <label for="batchDate" class="form-label">選擇日期</label>
                                            <input type="date" class="form-control" id="batchDate" value="{{ today }}">
                                        </div>
                                        <div class="col-md-4 d-flex align-items-end">
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-magic"></i> 批量智能預測
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速鏈接 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-link"></i> 快速鏈接</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="{{ url_for('intelligent.demo_page') }}" class="btn btn-outline-info btn-block">
                                                <i class="fas fa-play-circle"></i> 系統演示
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ url_for('intelligent.analysis_page') }}" class="btn btn-outline-warning btn-block">
                                                <i class="fas fa-chart-line"></i> 預測分析
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-secondary btn-block" onclick="showHelp()">
                                                <i class="fas fa-question-circle"></i> 使用說明
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-dark btn-block" onclick="showMethodology()">
                                                <i class="fas fa-book"></i> 預測原理
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 預測結果模態框 -->
<div class="modal fade" id="predictionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-brain"></i> 智能預測結果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="predictionResult">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">預測中...</span>
                    </div>
                    <p class="mt-2">正在進行智能預測...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量預測結果模態框 -->
<div class="modal fade" id="batchResultModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-layer-group"></i> 批量預測結果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="batchResult">
                <!-- 批量結果將在這裡顯示 -->
            </div>
        </div>
    </div>
</div>

<script>
// 單場預測
document.querySelectorAll('.predict-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const gameId = this.dataset.gameId;
        const homeTeam = this.dataset.homeTeam;
        const awayTeam = this.dataset.awayTeam;
        const date = this.dataset.date;
        
        // 顯示模態框
        const modal = new bootstrap.Modal(document.getElementById('predictionModal'));
        modal.show();
        
        // 執行預測
        fetch(`/intelligent/api/predict/${gameId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayPredictionResult(data.prediction);
                } else {
                    document.getElementById('predictionResult').innerHTML = 
                        `<div class="alert alert-danger">預測失敗: ${data.error}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('predictionResult').innerHTML = 
                    `<div class="alert alert-danger">網絡錯誤: ${error}</div>`;
            });
    });
});

// 批量預測
document.getElementById('batchPredictForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const date = document.getElementById('batchDate').value;
    const formData = new FormData();
    formData.append('date', date);
    
    // 顯示模態框
    const modal = new bootstrap.Modal(document.getElementById('batchResultModal'));
    modal.show();
    
    document.getElementById('batchResult').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">批量預測中...</span>
            </div>
            <p class="mt-2">正在進行批量智能預測...</p>
        </div>
    `;
    
    // 執行批量預測
    fetch('/intelligent/batch_predict', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayBatchResult(data);
        } else {
            document.getElementById('batchResult').innerHTML = 
                `<div class="alert alert-danger">批量預測失敗: ${data.error}</div>`;
        }
    })
    .catch(error => {
        document.getElementById('batchResult').innerHTML = 
            `<div class="alert alert-danger">網絡錯誤: ${error}</div>`;
    });
});

function displayPredictionResult(prediction) {
    const pitcher = prediction.pitcher_analysis;
    const strategy = prediction.strategy;
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-baseball-ball"></i> 投手分析</h6>
                <div class="card">
                    <div class="card-body">
                        <p><strong>主隊投手:</strong> ${pitcher.home_pitcher.name}</p>
                        <p><strong>ERA:</strong> ${pitcher.home_pitcher.era.toFixed(2)} | <strong>等級:</strong> 
                           <span class="badge bg-${getPitcherBadgeColor(pitcher.home_pitcher.strength)}">${pitcher.home_pitcher.strength}</span></p>
                        
                        <p><strong>客隊投手:</strong> ${pitcher.away_pitcher.name}</p>
                        <p><strong>ERA:</strong> ${pitcher.away_pitcher.era.toFixed(2)} | <strong>等級:</strong> 
                           <span class="badge bg-${getPitcherBadgeColor(pitcher.away_pitcher.strength)}">${pitcher.away_pitcher.strength}</span></p>
                        
                        <p><strong>對戰類型:</strong> <span class="badge bg-info">${pitcher.matchup_type}</span></p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-chess"></i> 智能策略</h6>
                <div class="card">
                    <div class="card-body">
                        <p><strong>策略:</strong> ${strategy.name}</p>
                        <p><strong>類型:</strong> <span class="badge bg-${getStrategyBadgeColor(strategy.type)}">${strategy.type}</span></p>
                        <p><strong>目標總分:</strong> ${strategy.target_total}分</p>
                        <p><strong>說明:</strong> ${strategy.description}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h6><i class="fas fa-chart-line"></i> 預測結果</h6>
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h4 class="text-primary">
                            ${prediction.predicted_away_score.toFixed(1)} - ${prediction.predicted_home_score.toFixed(1)}
                        </h4>
                        <p><strong>總分:</strong> ${prediction.total_runs.toFixed(1)}分 | 
                           <strong>信心度:</strong> ${(prediction.confidence * 100).toFixed(1)}%</p>
                    </div>
                </div>
            </div>
        </div>
        
        ${prediction.game_info && prediction.game_info.actual_home_score !== null ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6><i class="fas fa-check-circle"></i> 實際結果比較</h6>
                <div class="card">
                    <div class="card-body">
                        <p><strong>實際比分:</strong> ${prediction.game_info.actual_away_score} - ${prediction.game_info.actual_home_score}</p>
                        <p><strong>實際總分:</strong> ${prediction.game_info.actual_away_score + prediction.game_info.actual_home_score}分</p>
                        <p><strong>預測差異:</strong> ${Math.abs(prediction.total_runs - (prediction.game_info.actual_away_score + prediction.game_info.actual_home_score)).toFixed(1)}分</p>
                    </div>
                </div>
            </div>
        </div>
        ` : ''}
    `;
    
    document.getElementById('predictionResult').innerHTML = html;
}

function displayBatchResult(data) {
    let html = `
        <div class="row mb-3">
            <div class="col-12">
                <h6>批量預測統計 - ${data.date}</h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-primary">${data.total_games}</h5>
                                <small>總比賽數</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-success">${data.successful_predictions}</h5>
                                <small>成功預測</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-info">${((data.successful_predictions / data.total_games) * 100).toFixed(1)}%</h5>
                                <small>成功率</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>比賽</th>
                        <th>對戰類型</th>
                        <th>策略</th>
                        <th>預測比分</th>
                        <th>實際比分</th>
                        <th>狀態</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.predictions.forEach(prediction => {
        const game = prediction.game_info;
        if (game.error) {
            html += `
                <tr>
                    <td>${game.away_team} @ ${game.home_team}</td>
                    <td colspan="5" class="text-danger">錯誤: ${game.error}</td>
                </tr>
            `;
        } else {
            const actualScore = game.actual_home_score !== null ? 
                `${game.actual_away_score}-${game.actual_home_score}` : '未完成';
            
            html += `
                <tr>
                    <td>${game.away_team} @ ${game.home_team}</td>
                    <td><span class="badge bg-info">${prediction.pitcher_analysis.matchup_type}</span></td>
                    <td>${prediction.strategy.name}</td>
                    <td>${prediction.predicted_away_score.toFixed(1)}-${prediction.predicted_home_score.toFixed(1)}</td>
                    <td>${actualScore}</td>
                    <td><span class="badge bg-success">成功</span></td>
                </tr>
            `;
        }
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    document.getElementById('batchResult').innerHTML = html;
}

function getPitcherBadgeColor(strength) {
    switch(strength) {
        case '王牌': return 'danger';
        case '優秀': return 'warning';
        case '普通': return 'secondary';
        case '弱勢': return 'dark';
        default: return 'secondary';
    }
}

function getStrategyBadgeColor(type) {
    switch(type) {
        case 'low_scoring': return 'primary';
        case 'high_scoring': return 'danger';
        case 'unbalanced': return 'warning';
        case 'standard': return 'secondary';
        default: return 'secondary';
    }
}

function showHelp() {
    alert('智能預測系統使用說明:\n\n1. 系統會自動分析投手質量\n2. 根據投手組合選擇預測策略\n3. 自動調整預測結果\n4. 無需手動選擇模型\n\n投手等級:\n• 王牌: ERA ≤ 2.50\n• 優秀: ERA ≤ 3.50\n• 普通: ERA ≤ 4.50\n• 弱勢: ERA > 4.50');
}

function showMethodology() {
    alert('智能預測原理:\n\n1. 投手質量分析:\n   - 基於ERA和WHIP計算質量分數\n   - 自動分類投手等級\n\n2. 策略選擇:\n   - 王牌對決 → 低分策略\n   - 打擊戰 → 高分策略\n   - 強弱對戰 → 不平衡策略\n   - 普通對戰 → 標準策略\n\n3. 預測調整:\n   - 根據策略調整總分目標\n   - 考慮主場優勢\n   - 結合球隊歷史表現');
}
</script>
{% endblock %}
