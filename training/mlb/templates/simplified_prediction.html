{% extends "base.html" %}

{% block title %}簡化預測系統 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-baseball-ball"></i> 簡化預測系統</h2>
                <div>
                    <a href="{{ url_for('unified_predictions.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回主頁
                    </a>
                    <a href="{{ url_for('batch_system.batch_system_dashboard') }}" class="btn btn-primary">
                        <i class="fas fa-layer-group"></i> 批次系統
                    </a>
                </div>
            </div>

            <!-- 快速預測區域 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-zap"></i> 快速預測</h5>
                </div>
                <div class="card-body">
                    <form id="quickPredictionForm">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="predictionDate" class="form-label">預測日期</label>
                                <input type="date" class="form-control" id="predictionDate" required>
                                <small class="form-text text-muted">選擇要預測的比賽日期</small>
                            </div>
                            <div class="col-md-4">
                                <label for="predictionMode" class="form-label">預測模式</label>
                                <select class="form-control" id="predictionMode">
                                    <option value="enhanced">增強模式 (推薦)</option>
                                    <option value="standard">標準模式</option>
                                    <option value="fast">快速模式</option>
                                </select>
                                <small class="form-text text-muted">增強模式提供最準確的預測</small>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary btn-lg w-100" id="predictBtn">
                                    <i class="fas fa-magic"></i> 開始預測
                                </button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickDate('today')">今天</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickDate('tomorrow')">明天</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickDate('yesterday')">昨天</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 預測結果摘要 -->
            <div id="predictionSummary" class="row mb-4" style="display: none;">
                <div class="col-md-2">
                    <div class="card text-white bg-success">
                        <div class="card-body text-center">
                            <h4 id="summaryDate">2025-08-23</h4>
                            <p class="mb-0">預測日期</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-primary">
                        <div class="card-body text-center">
                            <h4 id="summarySuccess">14/14</h4>
                            <p class="mb-0">成功預測</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-info">
                        <div class="card-body text-center">
                            <h4 id="summaryPitchers">85%</h4>
                            <p class="mb-0">投手資料覆蓋</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-warning">
                        <div class="card-body text-center">
                            <h4 id="summaryOdds">0%</h4>
                            <p class="mb-0">盤口資料覆蓋</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-secondary">
                        <div class="card-body text-center">
                            <h4 id="summaryScore">85</h4>
                            <p class="mb-0">整體質量分數</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-dark">
                        <div class="card-body text-center">
                            <h4 id="summaryTime">1s</h4>
                            <p class="mb-0">處理時間</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 載入指示器 -->
            <div id="loadingArea" class="text-center" style="display: none;">
                <div class="card">
                    <div class="card-body">
                        <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                            <span class="visually-hidden">處理中...</span>
                        </div>
                        <h5 id="loadingText">正在生成預測...</h5>
                        <p id="loadingDetails" class="text-muted">請稍候，這通常需要幾秒鐘</p>
                        <div class="progress mt-3" style="height: 8px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 id="loadingProgress" 
                                 style="width: 0%">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 預測結果卡片 -->
            <div id="predictionResults" style="display: none;">
                <div class="row" id="gameCards">
                    <!-- 預測結果將在這裡顯示 -->
                </div>
            </div>

            <!-- 錯誤提示 -->
            <div id="errorAlert" class="alert alert-danger" style="display: none;">
                <h5><i class="fas fa-exclamation-triangle"></i> 錯誤</h5>
                <p id="errorMessage"></p>
                <button type="button" class="btn btn-outline-danger" onclick="hideError()">
                    <i class="fas fa-times"></i> 關閉
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 頁面載入時設置
document.addEventListener('DOMContentLoaded', function() {
    // 設置默認日期為今天
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('predictionDate').value = today;
});

// 快速日期設置
function setQuickDate(type) {
    const today = new Date();
    let targetDate;
    
    switch(type) {
        case 'today':
            targetDate = today;
            break;
        case 'tomorrow':
            targetDate = new Date(today.getTime() + 24 * 60 * 60 * 1000);
            break;
        case 'yesterday':
            targetDate = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            break;
        default:
            targetDate = today;
    }
    
    document.getElementById('predictionDate').value = targetDate.toISOString().split('T')[0];
}

// 快速預測表單提交
document.getElementById('quickPredictionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const predictionDate = document.getElementById('predictionDate').value;
    const predictionMode = document.getElementById('predictionMode').value;
    
    if (!predictionDate) {
        showError('請選擇預測日期');
        return;
    }
    
    startPrediction(predictionDate, predictionMode);
});

// 開始預測
function startPrediction(targetDate, mode) {
    // 隱藏之前的結果和錯誤
    hideAllResults();
    
    // 顯示載入指示器
    showLoading('正在生成預測...', '分析比賽數據和投手資訊');
    
    // 禁用預測按鈕
    document.getElementById('predictBtn').disabled = true;
    
    // 選擇API端點
    let apiEndpoint;
    let requestData = {
        target_date: targetDate
    };
    
    if (mode === 'enhanced') {
        apiEndpoint = '/unified/api/predict/enhanced_daily';
    } else {
        apiEndpoint = '/unified/api/predict/daily';
        requestData.prediction_mode = mode;
    }
    
    // 發送預測請求
    fetch(apiEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        document.getElementById('predictBtn').disabled = false;
        
        if (data.success) {
            displayPredictionResults(data, targetDate);
        } else {
            showError(data.error || '預測生成失敗');
        }
    })
    .catch(error => {
        hideLoading();
        document.getElementById('predictBtn').disabled = false;
        showError('網絡錯誤: ' + error.message);
    });
}

// 顯示載入狀態
function showLoading(text, details) {
    document.getElementById('loadingArea').style.display = 'block';
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingDetails').textContent = details;
    
    // 模擬進度條
    let progress = 0;
    const progressBar = document.getElementById('loadingProgress');
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 200);
    
    // 存儲interval ID以便清理
    progressBar.dataset.intervalId = interval;
}

// 隱藏載入狀態
function hideLoading() {
    document.getElementById('loadingArea').style.display = 'none';
    const progressBar = document.getElementById('loadingProgress');
    if (progressBar.dataset.intervalId) {
        clearInterval(progressBar.dataset.intervalId);
    }
    progressBar.style.width = '100%';
}

// 顯示預測結果
function displayPredictionResults(data, targetDate) {
    // 更新摘要信息
    updateSummary(data, targetDate);
    
    // 顯示摘要
    document.getElementById('predictionSummary').style.display = 'flex';
    
    // 生成預測卡片
    const gameCardsContainer = document.getElementById('gameCards');
    gameCardsContainer.innerHTML = '';
    
    if (data.predictions && data.predictions.length > 0) {
        data.predictions.forEach((prediction, index) => {
            const card = createPredictionCard(prediction, index);
            gameCardsContainer.appendChild(card);
        });
        
        document.getElementById('predictionResults').style.display = 'block';
    } else {
        showError('沒有找到該日期的比賽數據');
    }
}

// 更新摘要信息
function updateSummary(data, targetDate) {
    document.getElementById('summaryDate').textContent = targetDate;
    document.getElementById('summarySuccess').textContent = `${data.successful_predictions || 0}/${data.total_games || 0}`;
    
    // 計算投手資料覆蓋率
    const pitcherCoverage = data.predictions ? 
        Math.round((data.predictions.filter(p => 
            p.pitcher_info && (p.pitcher_info.home_pitcher !== '未確認' || p.pitcher_info.away_pitcher !== '未確認')
        ).length / data.predictions.length) * 100) : 0;
    
    document.getElementById('summaryPitchers').textContent = pitcherCoverage + '%';
    
    // 盤口資料覆蓋率
    const oddsCoverage = data.predictions ? 
        Math.round((data.predictions.filter(p => 
            p.betting_odds || (p.over_under_line && p.over_under_line !== 'N/A')
        ).length / data.predictions.length) * 100) : 0;
    
    document.getElementById('summaryOdds').textContent = oddsCoverage + '%';
    
    // 整體質量分數
    const qualityScore = data.data_quality ? 
        Math.round(data.data_quality.overall_quality_score * 100) : 85;
    
    document.getElementById('summaryScore').textContent = qualityScore;
    
    // 處理時間
    document.getElementById('summaryTime').textContent = (data.processing_time || 1) + 's';
}

// 創建預測卡片
function createPredictionCard(prediction, index) {
    const card = document.createElement('div');
    card.className = 'col-md-6 col-lg-4 mb-3';
    
    // 決定卡片邊框顏色
    const confidenceLevel = prediction.confidence || 0;
    let borderColor = 'border-secondary';
    if (confidenceLevel >= 0.7) borderColor = 'border-success';
    else if (confidenceLevel >= 0.5) borderColor = 'border-warning';
    else borderColor = 'border-danger';
    
    card.innerHTML = `
        <div class="card h-100 ${borderColor}">
            <div class="card-header">
                <h6 class="mb-0">
                    <strong>${prediction.matchup || prediction.away_team + ' @ ' + prediction.home_team}</strong>
                    <span class="badge bg-primary float-end">#${index + 1}</span>
                </h6>
            </div>
            <div class="card-body">
                <!-- 比分預測 -->
                <div class="text-center mb-3">
                    <div class="row">
                        <div class="col-5">
                            <h3 class="text-primary mb-0">${(prediction.predicted_away_score || 0).toFixed(1)}</h3>
                            <small class="text-muted">${prediction.away_team}</small>
                        </div>
                        <div class="col-2">
                            <h4 class="text-muted mb-0">-</h4>
                        </div>
                        <div class="col-5">
                            <h3 class="text-success mb-0">${(prediction.predicted_home_score || 0).toFixed(1)}</h3>
                            <small class="text-muted">${prediction.home_team}</small>
                        </div>
                    </div>
                </div>
                
                <!-- 信心度和總分 -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span><strong>總分:</strong> ${(prediction.predicted_total_runs || 0).toFixed(1)}</span>
                        <span><strong>信心度:</strong> ${(confidenceLevel * 100).toFixed(0)}%</span>
                    </div>
                    <div class="progress mt-1" style="height: 8px;">
                        <div class="progress-bar ${confidenceLevel >= 0.7 ? 'bg-success' : confidenceLevel >= 0.5 ? 'bg-warning' : 'bg-danger'}" 
                             style="width: ${confidenceLevel * 100}%"></div>
                    </div>
                </div>
                
                <!-- 投手信息 -->
                <div class="mb-2">
                    <small class="text-muted">
                        <strong>投手:</strong><br>
                        客: ${prediction.pitcher_info?.away_pitcher || '未確認'}<br>
                        主: ${prediction.pitcher_info?.home_pitcher || '未確認'}
                    </small>
                </div>
                
                <!-- 增強功能標記 -->
                ${prediction.enhanced_features ? `
                <div class="mb-2">
                    <span class="badge bg-success">
                        <i class="fas fa-rocket"></i> ${prediction.enhanced_features.methodology || '增強預測'}
                    </span>
                    ${prediction.enhanced_features.ballpark_factor && prediction.enhanced_features.ballpark_factor !== 1.0 ? `
                    <span class="badge bg-info">
                        球場因子: ${prediction.enhanced_features.ballpark_factor.toFixed(3)}
                    </span>
                    ` : ''}
                </div>
                ` : ''}
            </div>
            <div class="card-footer">
                <small class="text-muted">
                    Game ID: ${prediction.game_id || 'N/A'} | 
                    模式: ${prediction.model_used || '標準'}
                </small>
            </div>
        </div>
    `;
    
    return card;
}

// 隱藏所有結果
function hideAllResults() {
    document.getElementById('predictionSummary').style.display = 'none';
    document.getElementById('predictionResults').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
}

// 顯示錯誤
function showError(message) {
    hideAllResults();
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorAlert').style.display = 'block';
}

// 隱藏錯誤
function hideError() {
    document.getElementById('errorAlert').style.display = 'none';
}
</script>
{% endblock %}