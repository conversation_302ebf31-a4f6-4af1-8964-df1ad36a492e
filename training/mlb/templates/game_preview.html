{% extends "base.html" %}

{% block title %}比賽預覽 - {{ preview.teams.away.name }} @ {{ preview.teams.home.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 比賽標題 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-eye"></i>
                        比賽預覽
                    </h2>
                    <div class="text-muted">
                        {{ preview.game.date }} | {{ preview.game.venue }}
                    </div>
                </div>
                <div>
                    <a href="{{ url_for('games.game_detail', game_id=preview.game.game_id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line"></i> 比賽詳情
                    </a>
                </div>
            </div>

            <!-- 球隊對戰卡片 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-5">
                                    <div class="team-info">
                                        <h3>{{ preview.teams.away.name }}</h3>
                                        <div class="team-record">{{ preview.teams.away.record }}</div>
                                        <small class="text-muted">客隊</small>
                                    </div>
                                </div>
                                <div class="col-2 d-flex align-items-center justify-content-center">
                                    <div class="vs-indicator">
                                        <span class="text-muted fs-3">@</span>
                                    </div>
                                </div>
                                <div class="col-5">
                                    <div class="team-info">
                                        <h3>{{ preview.teams.home.name }}</h3>
                                        <div class="team-record">{{ preview.teams.home.record }}</div>
                                        <small class="text-muted">主隊</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 先發投手對比 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-baseball-ball"></i> Probable Pitchers</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- 客隊投手 -->
                                <div class="col-md-5">
                                    {% if preview.probable_pitchers.away %}
                                    <div class="pitcher-card text-center">
                                        <div class="pitcher-avatar mb-3">
                                            <div class="rounded-circle bg-primary text-white d-inline-flex align-items-center justify-content-center" 
                                                 style="width: 80px; height: 80px; font-size: 24px;">
                                                {{ preview.probable_pitchers.away.name[0] if preview.probable_pitchers.away.name else '?' }}
                                            </div>
                                        </div>
                                        <h5>{{ preview.probable_pitchers.away.name }}</h5>
                                        <div class="pitcher-stats">
                                            <div class="stat-item">
                                                <strong>{{ preview.probable_pitchers.away.record }}</strong>
                                                <small class="text-muted d-block">W-L</small>
                                            </div>
                                            <div class="stat-item mt-2">
                                                <strong>{{ preview.probable_pitchers.away.era }}</strong>
                                                <small class="text-muted d-block">ERA</small>
                                            </div>
                                            <div class="stat-item mt-2">
                                                <strong>{{ preview.probable_pitchers.away.strikeouts }}</strong>
                                                <small class="text-muted d-block">K</small>
                                            </div>
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="text-center text-muted">
                                        <i class="fas fa-question-circle fa-3x mb-3"></i>
                                        <p>先發投手待定</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- VS 分隔符 -->
                                <div class="col-md-2 d-flex align-items-center justify-content-center">
                                    <div class="vs-indicator">
                                        <span class="text-muted fs-2">VS</span>
                                    </div>
                                </div>

                                <!-- 主隊投手 -->
                                <div class="col-md-5">
                                    {% if preview.probable_pitchers.home %}
                                    <div class="pitcher-card text-center">
                                        <div class="pitcher-avatar mb-3">
                                            <div class="rounded-circle bg-success text-white d-inline-flex align-items-center justify-content-center" 
                                                 style="width: 80px; height: 80px; font-size: 24px;">
                                                {{ preview.probable_pitchers.home.name[0] if preview.probable_pitchers.home.name else '?' }}
                                            </div>
                                        </div>
                                        <h5>{{ preview.probable_pitchers.home.name }}</h5>
                                        <div class="pitcher-stats">
                                            <div class="stat-item">
                                                <strong>{{ preview.probable_pitchers.home.record }}</strong>
                                                <small class="text-muted d-block">W-L</small>
                                            </div>
                                            <div class="stat-item mt-2">
                                                <strong>{{ preview.probable_pitchers.home.era }}</strong>
                                                <small class="text-muted d-block">ERA</small>
                                            </div>
                                            <div class="stat-item mt-2">
                                                <strong>{{ preview.probable_pitchers.home.strikeouts }}</strong>
                                                <small class="text-muted d-block">K</small>
                                            </div>
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="text-center text-muted">
                                        <i class="fas fa-question-circle fa-3x mb-3"></i>
                                        <p>先發投手待定</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 打線對戰統計 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-chart-bar"></i> Matchups</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- 客隊打線 vs 主隊投手 -->
                                <div class="col-md-6">
                                    {% if preview.probable_pitchers.home %}
                                    <h6>{{ preview.teams.away.code }} vs {{ preview.probable_pitchers.home.name }}</h6>
                                    {% if preview.matchups.away_vs_home_pitcher %}
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>打者</th>
                                                    <th>AB</th>
                                                    <th>H</th>
                                                    <th>AVG</th>
                                                    <th>OPS</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for batter in preview.matchups.away_vs_home_pitcher %}
                                                <tr>
                                                    <td>{{ batter.player_name }}</td>
                                                    <td>{{ batter.vs_pitcher_ab or '-' }}</td>
                                                    <td>{{ batter.vs_pitcher_hits or '-' }}</td>
                                                    <td>{{ "%.3f"|format(batter.vs_pitcher_avg) if batter.vs_pitcher_avg else '-' }}</td>
                                                    <td>-</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <p class="text-muted">暫無對戰數據</p>
                                    {% endif %}
                                    {% else %}
                                    <p class="text-muted">先發投手待定</p>
                                    {% endif %}
                                </div>

                                <!-- 主隊打線 vs 客隊投手 -->
                                <div class="col-md-6">
                                    {% if preview.probable_pitchers.away %}
                                    <h6>{{ preview.teams.home.code }} vs {{ preview.probable_pitchers.away.name }}</h6>
                                    {% if preview.matchups.home_vs_away_pitcher %}
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>打者</th>
                                                    <th>AB</th>
                                                    <th>H</th>
                                                    <th>AVG</th>
                                                    <th>OPS</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for batter in preview.matchups.home_vs_away_pitcher %}
                                                <tr>
                                                    <td>{{ batter.player_name }}</td>
                                                    <td>{{ batter.vs_pitcher_ab or '-' }}</td>
                                                    <td>{{ batter.vs_pitcher_hits or '-' }}</td>
                                                    <td>{{ "%.3f"|format(batter.vs_pitcher_avg) if batter.vs_pitcher_avg else '-' }}</td>
                                                    <td>-</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <p class="text-muted">暫無對戰數據</p>
                                    {% endif %}
                                    {% else %}
                                    <p class="text-muted">先發投手待定</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.team-info h3 {
    margin-bottom: 0.5rem;
}

.team-record {
    font-size: 1.2rem;
    font-weight: bold;
    color: #6c757d;
}

.pitcher-card {
    padding: 1rem;
}

.pitcher-stats .stat-item {
    display: inline-block;
    margin: 0 1rem;
}

.vs-indicator {
    font-weight: bold;
}

@media (max-width: 768px) {
    .pitcher-stats .stat-item {
        display: block;
        margin: 0.5rem 0;
    }
}
</style>
{% endblock %}
