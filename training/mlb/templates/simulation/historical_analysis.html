{% extends "base.html" %}

{% block title %}歷史分析{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('simulation.simulation_index') }}">模擬測試</a>
                            </li>
                            <li class="breadcrumb-item active">歷史分析</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-line text-info"></i> 歷史分析
                    </h1>
                    <p class="text-muted mb-0">分析歷史預測表現，識別模型改進機會</p>
                </div>
                <div>
                    <a href="{{ url_for('simulation.simulation_index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析控制面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter"></i> 分析參數
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="startDate" class="form-label">開始日期</label>
                            <input type="date" class="form-control" id="startDate" 
                                   value="2025-06-28" max="{{ date.today().isoformat() }}">
                        </div>
                        <div class="col-md-3">
                            <label for="endDate" class="form-label">結束日期</label>
                            <input type="date" class="form-control" id="endDate" 
                                   value="2025-06-30" max="{{ date.today().isoformat() }}">
                        </div>
                        <div class="col-md-3">
                            <label for="analysisType" class="form-label">分析類型</label>
                            <select class="form-select" id="analysisType">
                                <option value="trend">趨勢分析</option>
                                <option value="accuracy">準確率分析</option>
                                <option value="model_performance">模型表現</option>
                                <option value="team_analysis">球隊分析</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-info" onclick="runHistoricalAnalysis()">
                                    <i class="fas fa-chart-line"></i> 開始分析
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速分析選項 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightning-bolt text-warning"></i> 快速分析
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-primary w-100" 
                                    onclick="quickAnalysis('last_week')">
                                <i class="fas fa-calendar-week"></i> 最近一週
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-success w-100" 
                                    onclick="quickAnalysis('accuracy_trend')">
                                <i class="fas fa-trending-up"></i> 準確率趨勢
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-info w-100" 
                                    onclick="quickAnalysis('model_comparison')">
                                <i class="fas fa-balance-scale"></i> 模型比較
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-warning w-100" 
                                    onclick="quickAnalysis('team_performance')">
                                <i class="fas fa-users"></i> 球隊表現
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析結果區域 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar text-primary"></i> 分析結果
                    </h5>
                </div>
                <div class="card-body">
                    <div id="analysisResults">
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">等待分析執行</h4>
                            <p class="text-muted">選擇分析參數並點擊開始分析按鈕</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 圖表區域 -->
    <div class="row mt-4" id="chartSection" style="display: none;">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">準確率趨勢</h6>
                </div>
                <div class="card-body">
                    <canvas id="accuracyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">模型表現比較</h6>
                </div>
                <div class="card-body">
                    <canvas id="modelChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析進度模態框 -->
<div class="modal fade" id="analysisProgressModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line"></i> 執行分析中
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-info mb-3" role="status">
                    <span class="visually-hidden">分析中...</span>
                </div>
                <p id="analysisProgressText">正在初始化分析...</p>
                <div class="progress">
                    <div class="progress-bar bg-info" id="analysisProgressBar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let analysisProgressModal;
let accuracyChart, modelChart;

document.addEventListener('DOMContentLoaded', function() {
    analysisProgressModal = new bootstrap.Modal(document.getElementById('analysisProgressModal'));
});

function runHistoricalAnalysis() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const analysisType = document.getElementById('analysisType').value;
    
    if (!startDate || !endDate) {
        alert('請選擇開始和結束日期');
        return;
    }
    
    if (new Date(startDate) > new Date(endDate)) {
        alert('開始日期不能晚於結束日期');
        return;
    }
    
    executeAnalysis(startDate, endDate, analysisType);
}

function quickAnalysis(type) {
    const today = new Date();
    let startDate, endDate, analysisType;
    
    switch (type) {
        case 'last_week':
            endDate = today.toISOString().split('T')[0];
            startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            analysisType = 'trend';
            break;
        case 'accuracy_trend':
            endDate = '2025-06-30';
            startDate = '2025-06-28';
            analysisType = 'accuracy';
            break;
        case 'model_comparison':
            endDate = '2025-06-30';
            startDate = '2025-06-28';
            analysisType = 'model_performance';
            break;
        case 'team_performance':
            endDate = '2025-06-30';
            startDate = '2025-06-28';
            analysisType = 'team_analysis';
            break;
        default:
            return;
    }
    
    executeAnalysis(startDate, endDate, analysisType);
}

function executeAnalysis(startDate, endDate, analysisType) {
    showAnalysisProgress('歷史分析', `正在分析 ${startDate} 到 ${endDate} 的${analysisType}...`);
    
    const requestData = {
        start_date: startDate,
        end_date: endDate,
        analysis_type: analysisType
    };
    
    fetch('/simulation/api/historical_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        hideAnalysisProgress();
        if (data.success) {
            displayAnalysisResults(data, analysisType, startDate, endDate);
        } else {
            displayAnalysisError(data.error);
        }
    })
    .catch(error => {
        hideAnalysisProgress();
        displayAnalysisError('網絡錯誤: ' + error.message);
    });
}

function showAnalysisProgress(title, message) {
    document.querySelector('#analysisProgressModal .modal-title').innerHTML = 
        `<i class="fas fa-chart-line"></i> ${title}`;
    updateAnalysisProgress(message, 0);
    analysisProgressModal.show();
}

function updateAnalysisProgress(message, percentage) {
    document.getElementById('analysisProgressText').textContent = message;
    document.getElementById('analysisProgressBar').style.width = percentage + '%';
}

function hideAnalysisProgress() {
    analysisProgressModal.hide();
}

function displayAnalysisResults(data, analysisType, startDate, endDate) {
    let html = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <strong>分析完成</strong> - ${startDate} 到 ${endDate} 的 ${analysisType} 分析
        </div>
    `;
    
    if (data.analysis_result) {
        html += generateAnalysisContent(data.analysis_result, analysisType);
        
        // 顯示圖表區域
        document.getElementById('chartSection').style.display = 'block';
        
        // 生成圖表
        setTimeout(() => {
            generateCharts(data.analysis_result, analysisType);
        }, 100);
    }
    
    document.getElementById('analysisResults').innerHTML = html;
}

function generateAnalysisContent(result, analysisType) {
    let html = '';
    
    switch (analysisType) {
        case 'accuracy':
            html += generateAccuracyAnalysis(result);
            break;
        case 'model_performance':
            html += generateModelPerformanceAnalysis(result);
            break;
        case 'team_analysis':
            html += generateTeamAnalysis(result);
            break;
        default:
            html += generateTrendAnalysis(result);
    }
    
    return html;
}

function generateAccuracyAnalysis(result) {
    return `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">準確率分析結果</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <h4 class="text-primary">${result.total_predictions || 0}</h4>
                        <small class="text-muted">總預測數</small>
                    </div>
                    <div class="col-md-4">
                        <h4 class="text-success">${result.correct_predictions || 0}</h4>
                        <small class="text-muted">正確預測</small>
                    </div>
                    <div class="col-md-4">
                        <h4 class="text-info">${((result.correct_predictions || 0) / (result.total_predictions || 1) * 100).toFixed(1)}%</h4>
                        <small class="text-muted">整體準確率</small>
                    </div>
                </div>
                
                <hr>
                
                <h6>每日準確率變化</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>預測數</th>
                                <th>正確數</th>
                                <th>準確率</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${generateDailyAccuracyRows(result.daily_accuracy || [])}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

function generateModelPerformanceAnalysis(result) {
    return `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">模型表現分析</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>模型版本</th>
                                <th>預測數</th>
                                <th>正確數</th>
                                <th>準確率</th>
                                <th>表現評級</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${generateModelPerformanceRows(result.model_performance || {})}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

function generateTeamAnalysis(result) {
    return `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">球隊分析結果</h6>
            </div>
            <div class="card-body">
                <p class="text-muted">球隊相關的預測表現分析</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    球隊分析功能正在開發中，將包含各球隊的預測準確率、得分預測偏差等詳細分析。
                </div>
            </div>
        </div>
    `;
}

function generateTrendAnalysis(result) {
    return `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">趨勢分析結果</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>總體統計</h6>
                        <ul class="list-unstyled">
                            <li><strong>分析期間:</strong> ${result.period || 'N/A'}</li>
                            <li><strong>總比賽數:</strong> ${result.total_games || 0}</li>
                            <li><strong>預測覆蓋率:</strong> ${((result.predictions_count || 0) / (result.total_games || 1) * 100).toFixed(1)}%</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>趨勢指標</h6>
                        <ul class="list-unstyled">
                            <li><strong>準確率趨勢:</strong> <span class="text-success">穩定</span></li>
                            <li><strong>預測質量:</strong> <span class="text-warning">需改進</span></li>
                            <li><strong>模型穩定性:</strong> <span class="text-info">良好</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateDailyAccuracyRows(dailyData) {
    if (!dailyData.length) {
        return '<tr><td colspan="4" class="text-center text-muted">暫無數據</td></tr>';
    }
    
    return dailyData.map(day => `
        <tr>
            <td>${day.date}</td>
            <td>${day.predictions}</td>
            <td>${day.correct}</td>
            <td>
                <span class="badge bg-${day.accuracy >= 60 ? 'success' : day.accuracy >= 50 ? 'warning' : 'danger'}">
                    ${day.accuracy.toFixed(1)}%
                </span>
            </td>
        </tr>
    `).join('');
}

function generateModelPerformanceRows(modelData) {
    if (!Object.keys(modelData).length) {
        return '<tr><td colspan="5" class="text-center text-muted">暫無數據</td></tr>';
    }
    
    return Object.entries(modelData).map(([version, performance]) => `
        <tr>
            <td>${version}</td>
            <td>${performance.predictions}</td>
            <td>${performance.correct}</td>
            <td>
                <span class="badge bg-${performance.accuracy >= 60 ? 'success' : performance.accuracy >= 50 ? 'warning' : 'danger'}">
                    ${performance.accuracy.toFixed(1)}%
                </span>
            </td>
            <td>
                <span class="badge bg-${performance.accuracy >= 70 ? 'success' : performance.accuracy >= 60 ? 'primary' : performance.accuracy >= 50 ? 'warning' : 'danger'}">
                    ${performance.accuracy >= 70 ? '優秀' : performance.accuracy >= 60 ? '良好' : performance.accuracy >= 50 ? '一般' : '需改進'}
                </span>
            </td>
        </tr>
    `).join('');
}

function generateCharts(result, analysisType) {
    // 準確率趨勢圖
    if (result.daily_accuracy && result.daily_accuracy.length > 0) {
        const ctx1 = document.getElementById('accuracyChart').getContext('2d');
        
        if (accuracyChart) {
            accuracyChart.destroy();
        }
        
        accuracyChart = new Chart(ctx1, {
            type: 'line',
            data: {
                labels: result.daily_accuracy.map(d => d.date),
                datasets: [{
                    label: '準確率 (%)',
                    data: result.daily_accuracy.map(d => d.accuracy),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }
    
    // 模型表現比較圖
    if (result.model_performance && Object.keys(result.model_performance).length > 0) {
        const ctx2 = document.getElementById('modelChart').getContext('2d');
        
        if (modelChart) {
            modelChart.destroy();
        }
        
        const modelData = Object.entries(result.model_performance);
        
        modelChart = new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: modelData.map(([version, _]) => version),
                datasets: [{
                    label: '準確率 (%)',
                    data: modelData.map(([_, performance]) => performance.accuracy),
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 205, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)'
                    ],
                    borderColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 205, 86)',
                        'rgb(75, 192, 192)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }
}

function displayAnalysisError(error) {
    document.getElementById('analysisResults').innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>分析失敗</strong><br>
            ${error}
        </div>
    `;
}
</script>
{% endblock %}
