{% extends "base.html" %}

{% block title %}模型測試{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('simulation.simulation_index') }}">模擬測試</a>
                            </li>
                            <li class="breadcrumb-item active">模型測試</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-cogs text-success"></i> 模型測試
                    </h1>
                    <p class="text-muted mb-0">比較不同模型版本的表現，測試新功能和算法改進</p>
                </div>
                <div>
                    <a href="{{ url_for('simulation.simulation_index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 測試控制面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-play"></i> 測試控制面板
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="testDate" class="form-label">測試日期</label>
                            <input type="date" class="form-control" id="testDate" 
                                   value="2025-06-30" max="{{ date.today().isoformat() }}">
                        </div>
                        <div class="col-md-3">
                            <label for="modelVersion" class="form-label">模型版本</label>
                            <select class="form-select" id="modelVersion">
                                <option value="all">所有版本</option>
                                <option value="unified_v1.0">統一預測 v1.0</option>
                                <option value="ml_v1.0">機器學習 v1.0</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="testType" class="form-label">測試類型</label>
                            <select class="form-select" id="testType">
                                <option value="accuracy">準確率測試</option>
                                <option value="performance">性能測試</option>
                                <option value="comparison">版本比較</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-success" onclick="runModelTest()">
                                    <i class="fas fa-play"></i> 執行測試
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速測試按鈕 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt text-warning"></i> 快速測試
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-primary w-100" 
                                    onclick="quickTest('2025-06-30', 'accuracy')">
                                <i class="fas fa-target"></i> 06/30 準確率
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-success w-100" 
                                    onclick="quickTest('2025-06-29', 'comparison')">
                                <i class="fas fa-balance-scale"></i> 06/29 版本比較
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-info w-100" 
                                    onclick="quickTest('2025-06-28', 'performance')">
                                <i class="fas fa-tachometer-alt"></i> 06/28 性能測試
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-warning w-100" 
                                    onclick="runBatchTest()">
                                <i class="fas fa-layer-group"></i> 批量測試
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 測試結果區域 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar text-primary"></i> 測試結果
                    </h5>
                </div>
                <div class="card-body">
                    <div id="testResults">
                        <div class="text-center py-5">
                            <i class="fas fa-vial fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">等待測試執行</h4>
                            <p class="text-muted">選擇測試參數並點擊執行測試按鈕</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 測試進度模態框 -->
<div class="modal fade" id="testProgressModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cogs"></i> 執行測試中
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">測試中...</span>
                </div>
                <p id="testProgressText">正在初始化測試...</p>
                <div class="progress">
                    <div class="progress-bar" id="testProgressBar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let testProgressModal;

document.addEventListener('DOMContentLoaded', function() {
    testProgressModal = new bootstrap.Modal(document.getElementById('testProgressModal'));
});

function runModelTest() {
    const testDate = document.getElementById('testDate').value;
    const modelVersion = document.getElementById('modelVersion').value;
    const testType = document.getElementById('testType').value;
    
    if (!testDate) {
        alert('請選擇測試日期');
        return;
    }
    
    executeTest(testDate, testType, modelVersion);
}

function quickTest(date, type) {
    executeTest(date, type, 'all');
}

function runBatchTest() {
    const dates = ['2025-06-28', '2025-06-29', '2025-06-30'];
    
    showTestProgress('批量測試', '正在執行多日期測試...');
    
    let results = [];
    let completed = 0;
    
    dates.forEach((date, index) => {
        setTimeout(() => {
            updateTestProgress(`測試 ${date}...`, (index + 1) / dates.length * 100);
            
            fetch('/simulation/api/compare_models', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ date: date })
            })
            .then(response => response.json())
            .then(data => {
                results.push({ date: date, result: data });
                completed++;
                
                if (completed === dates.length) {
                    hideTestProgress();
                    displayBatchResults(results);
                }
            })
            .catch(error => {
                console.error('測試失敗:', error);
                completed++;
                
                if (completed === dates.length) {
                    hideTestProgress();
                    displayBatchResults(results);
                }
            });
        }, index * 1000);
    });
}

function executeTest(date, type, version) {
    showTestProgress('模型測試', `正在測試 ${date} 的${type}...`);
    
    let apiUrl;
    let requestData = { date: date };
    
    switch (type) {
        case 'accuracy':
        case 'performance':
            apiUrl = '/simulation/api/simulate_date';
            break;
        case 'comparison':
            apiUrl = '/simulation/api/compare_models';
            break;
        default:
            apiUrl = '/simulation/api/simulate_date';
    }
    
    if (version !== 'all') {
        requestData.model_version = version;
    }
    
    fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        hideTestProgress();
        if (data.success) {
            displayTestResults(data, type, date);
        } else {
            displayTestError(data.error);
        }
    })
    .catch(error => {
        hideTestProgress();
        displayTestError('網絡錯誤: ' + error.message);
    });
}

function showTestProgress(title, message) {
    document.querySelector('#testProgressModal .modal-title').innerHTML = 
        `<i class="fas fa-cogs"></i> ${title}`;
    updateTestProgress(message, 0);
    testProgressModal.show();
}

function updateTestProgress(message, percentage) {
    document.getElementById('testProgressText').textContent = message;
    document.getElementById('testProgressBar').style.width = percentage + '%';
}

function hideTestProgress() {
    testProgressModal.hide();
}

function displayTestResults(data, type, date) {
    let html = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <strong>測試完成</strong> - ${date} ${type}測試
        </div>
    `;
    
    if (type === 'comparison' && data.comparison) {
        html += generateComparisonResults(data.comparison);
    } else if (data.simulation_result) {
        html += generateSimulationResults(data.simulation_result);
    }
    
    document.getElementById('testResults').innerHTML = html;
}

function displayBatchResults(results) {
    let html = `
        <div class="alert alert-info">
            <i class="fas fa-layer-group"></i>
            <strong>批量測試完成</strong> - 共測試 ${results.length} 個日期
        </div>
        <div class="row">
    `;
    
    results.forEach(item => {
        if (item.result && item.result.success) {
            const comparison = item.result.comparison;
            html += `
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">${item.date}</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-1">總比賽: ${comparison.total_games}</p>
                            <p class="mb-1">模型版本: ${comparison.model_versions.length}</p>
                            <small class="text-muted">詳細結果請查看單日測試</small>
                        </div>
                    </div>
                </div>
            `;
        }
    });
    
    html += '</div>';
    document.getElementById('testResults').innerHTML = html;
}

function generateComparisonResults(comparison) {
    let html = `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">模型版本比較 - ${comparison.date}</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>總比賽數:</strong> ${comparison.total_games}</p>
                        <p><strong>模型版本:</strong> ${comparison.model_versions.join(', ')}</p>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>模型版本</th>
                                <th>總預測</th>
                                <th>評估比賽</th>
                                <th>正確預測</th>
                                <th>準確率</th>
                            </tr>
                        </thead>
                        <tbody>
    `;
    
    for (const [version, performance] of Object.entries(comparison.performance)) {
        html += `
            <tr>
                <td>${version}</td>
                <td>${performance.total_predictions}</td>
                <td>${performance.evaluated_games}</td>
                <td>${performance.correct_predictions}</td>
                <td>
                    <span class="badge bg-${performance.accuracy >= 60 ? 'success' : performance.accuracy >= 50 ? 'warning' : 'danger'}">
                        ${performance.accuracy.toFixed(1)}%
                    </span>
                </td>
            </tr>
        `;
    }
    
    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
    
    return html;
}

function generateSimulationResults(simulation) {
    const summary = simulation.summary;
    
    return `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">模擬結果摘要</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">${summary.total_games}</h4>
                        <small class="text-muted">總比賽</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">${summary.games_with_predictions}</h4>
                        <small class="text-muted">有預測</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">${summary.successful_simulations}</h4>
                        <small class="text-muted">成功模擬</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">${simulation.games_count}</h4>
                        <small class="text-muted">處理比賽</small>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function displayTestError(error) {
    document.getElementById('testResults').innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>測試失敗</strong><br>
            ${error}
        </div>
    `;
}
</script>
{% endblock %}
