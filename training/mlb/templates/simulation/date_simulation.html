{% extends "base.html" %}

{% block title %}日期模擬 - {{ selected_date }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('simulation.simulation_index') }}">模擬測試</a>
                            </li>
                            <li class="breadcrumb-item active">日期模擬</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-calendar-day text-primary"></i> 
                        日期模擬 - {{ selected_date }}
                    </h1>
                </div>
                <div>
                    <a href="{{ url_for('simulation.simulation_index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 日期選擇器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="date" class="form-label">選擇模擬日期</label>
                            <input type="date" class="form-control" id="date" name="date" 
                                   value="{{ selected_date }}" max="{{ date.today().isoformat() }}">
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-success" id="optimized-simulate-btn">
                                <i class="fas fa-rocket"></i> 優化預測
                            </button>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 模擬
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-info btn-sm" 
                                        onclick="setDate('2025-06-30')">06/30</button>
                                <button type="button" class="btn btn-outline-info btn-sm" 
                                        onclick="setDate('2025-06-29')">06/29</button>
                                <button type="button" class="btn btn-outline-info btn-sm" 
                                        onclick="setDate('2025-06-28')">06/28</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if error %}
    <!-- 錯誤信息 -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                {{ error }}
            </div>
        </div>
    </div>
    {% elif target_date %}
    
    <!-- 統計摘要 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-baseball-ball fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.total_games }}</h4>
                    <p class="text-muted mb-0">總比賽數</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-crystal-ball fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.games_with_predictions }}</h4>
                    <p class="text-muted mb-0">有預測的比賽</p>
                    <small class="text-success">{{ "%.1f"|format(stats.prediction_coverage) }}% 覆蓋率</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.completed_games }}</h4>
                    <p class="text-muted mb-0">已完成比賽</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="{% if stats.accuracy %}text-{{ 'success' if stats.accuracy >= 60 else 'warning' if stats.accuracy >= 50 else 'danger' }}{% else %}text-muted{% endif %} mb-2">
                        <i class="fas fa-target fa-2x"></i>
                    </div>
                    <h4 class="mb-1">
                        {% if stats.accuracy %}
                            {{ "%.1f"|format(stats.accuracy) }}%
                        {% else %}
                            N/A
                        {% endif %}
                    </h4>
                    <p class="text-muted mb-0">預測準確率</p>
                    {% if stats.evaluated_games %}
                        <small class="text-muted">基於 {{ stats.evaluated_games }} 場比賽</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 比賽狀態分布 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie text-primary"></i> 比賽狀態分布
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="border-end">
                                <h3 class="text-success mb-1">{{ stats.completed_games }}</h3>
                                <p class="text-muted mb-0">已完成</p>
                                <div class="progress mt-2" style="height: 6px;">
                                    <div class="progress-bar bg-success" style="width: {{ (stats.completed_games / stats.total_games * 100) if stats.total_games > 0 else 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border-end">
                                <h3 class="text-primary mb-1">{{ stats.scheduled_games }}</h3>
                                <p class="text-muted mb-0">已安排</p>
                                <div class="progress mt-2" style="height: 6px;">
                                    <div class="progress-bar bg-primary" style="width: {{ (stats.scheduled_games / stats.total_games * 100) if stats.total_games > 0 else 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h3 class="text-warning mb-1">{{ stats.postponed_games }}</h3>
                            <p class="text-muted mb-0">延期</p>
                            <div class="progress mt-2" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: {{ (stats.postponed_games / stats.total_games * 100) if stats.total_games > 0 else 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 比賽列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list text-primary"></i> 比賽詳情
                    </h5>
                </div>
                <div class="card-body">
                    {% if predictions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>比賽</th>
                                    <th>狀態</th>
                                    <th>實際比分</th>
                                    <th>預測比分</th>
                                    <th>勝負預測</th>
                                    <th>準確性</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for pred_data in predictions %}
                                {% set game = pred_data.game %}
                                {% set pred = pred_data.prediction %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <strong>{{ pred_data.away_team_name }}</strong> @ 
                                                <strong>{{ pred_data.home_team_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ game.game_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if game.game_status == 'completed' else 'primary' if game.game_status == 'scheduled' else 'warning' }}">
                                            {{ game.game_status }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if game.home_score is not none and game.away_score is not none %}
                                            <strong>{{ game.away_score }} - {{ game.home_score }}</strong>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if pred.predicted_away_score and pred.predicted_home_score %}
                                            {{ "%.1f"|format(pred.predicted_away_score) }} - {{ "%.1f"|format(pred.predicted_home_score) }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if pred.predicted_home_score and pred.predicted_away_score %}
                                            {% if pred.predicted_home_score > pred.predicted_away_score %}
                                                <span class="badge bg-primary">主隊勝</span>
                                            {% else %}
                                                <span class="badge bg-info">客隊勝</span>
                                            {% endif %}
                                            <br>
                                            <small class="text-muted">
                                                勝率: {{ "%.1f"|format((pred.home_win_probability or 0.5) * 100) }}%
                                            </small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if game.game_status == 'completed' and game.home_score is not none and game.away_score is not none and pred.predicted_home_score and pred.predicted_away_score %}
                                            {% set actual_home_win = game.home_score > game.away_score %}
                                            {% set predicted_home_win = pred.predicted_home_score > pred.predicted_away_score %}
                                            {% if actual_home_win == predicted_home_win %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check"></i> 正確
                                                </span>
                                            {% else %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times"></i> 錯誤
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">待評估</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('games.game_detail', game_id=game.game_id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> 詳情
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">沒有找到預測數據</h4>
                        <p class="text-muted">{{ target_date.strftime('%Y-%m-%d') }} 沒有預測記錄</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
function setDate(dateStr) {
    document.getElementById('date').value = dateStr;
    document.querySelector('form').submit();
}

// 初始化 DataTables（如果有數據）
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('table');
    if (table && table.rows.length > 1) {
        // 簡單的表格排序功能
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            if (index < headers.length - 1) { // 排除操作列
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => sortTable(table, index));
            }
        });
    }

    // 優化預測按鈕事件
    const optimizedBtn = document.getElementById('optimized-simulate-btn');
    if (optimizedBtn) {
        optimizedBtn.addEventListener('click', function() {
            const dateInput = document.getElementById('date');
            const selectedDate = dateInput.value;

            if (!selectedDate) {
                alert('請選擇日期');
                return;
            }

            // 顯示載入狀態
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 處理中...';
            this.disabled = true;

            // 發送請求
            fetch('/simulation/api/optimized_simulate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    date: selectedDate
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 顯示結果
                    showOptimizedResults(data);
                } else {
                    alert('優化預測失敗: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('請求失敗: ' + error.message);
            })
            .finally(() => {
                // 恢復按鈕狀態
                this.innerHTML = '<i class="fas fa-rocket"></i> 優化預測';
                this.disabled = false;
            });
        });
    }
});

function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aText = a.cells[column].textContent.trim();
        const bText = b.cells[column].textContent.trim();
        
        // 嘗試數字排序
        const aNum = parseFloat(aText);
        const bNum = parseFloat(bText);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return aNum - bNum;
        }
        
        // 文字排序
        return aText.localeCompare(bText);
    });
    
    // 重新插入排序後的行
    rows.forEach(row => tbody.appendChild(row));
}

function showOptimizedResults(data) {
    // 創建結果顯示區域
    const resultsHtml = `
        <div class="alert alert-success mt-4">
            <h5><i class="fas fa-check-circle"></i> 優化預測完成</h5>
            <p>日期: ${data.date}</p>
            <p>總比賽: ${data.total_games} 場</p>
            <p>訓練統計: 勝負模型 ${data.training_stats.win_training_games} 場, 得分模型 ${data.training_stats.score_training_games} 場</p>
        </div>
        <div class="table-responsive mt-3">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>比賽</th>
                        <th>預測得分</th>
                        <th>勝率</th>
                        <th>大小分</th>
                        <th>讓分盤</th>
                        <th>信心</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.predictions.map(pred => {
                        const betting = pred.prediction.betting_predictions || {};
                        const overUnder = betting.over_under || {};
                        const runLine = betting.run_line || {};

                        return `
                        <tr>
                            <td>${pred.teams}</td>
                            <td>${pred.prediction.away_score} - ${pred.prediction.home_score} (總分: ${pred.prediction.total_runs})</td>
                            <td>${pred.prediction.predicted_winner} (${(pred.prediction.home_win_probability * 100).toFixed(1)}%)</td>
                            <td>
                                ${overUnder.total_line ? `盤口: ${overUnder.total_line}<br>` : ''}
                                ${overUnder.recommendation ? `建議: ${overUnder.recommendation}` : '無數據'}
                            </td>
                            <td>
                                ${runLine.home_line ? `主隊: ${runLine.home_line}<br>` : ''}
                                ${runLine.recommendation ? `建議: ${runLine.recommendation}` : '無數據'}
                            </td>
                            <td>${pred.prediction.confidence_level}</td>
                        </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;

    // 顯示結果
    let resultsContainer = document.getElementById('optimized-results');
    if (!resultsContainer) {
        // 創建結果容器
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'optimized-results';
        resultsContainer.className = 'mt-4';
        document.querySelector('.container-fluid').appendChild(resultsContainer);
    }
    resultsContainer.innerHTML = resultsHtml;
}
</script>
{% endblock %}
