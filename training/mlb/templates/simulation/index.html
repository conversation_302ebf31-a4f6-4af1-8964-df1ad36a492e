{% extends "base.html" %}

{% block title %}模擬測試中心{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-flask text-primary"></i> 模擬測試中心
                    </h1>
                    <p class="text-muted mb-0">測試和驗證預測模型在特定日期的表現</p>
                </div>
                <div>
                    <span class="badge bg-info">
                        <i class="fas fa-clock"></i> {{ datetime.now().strftime('%Y-%m-%d %H:%M') }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能卡片 -->
    <div class="row g-4">
        <!-- 日期模擬 -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-calendar-day fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">日期模擬</h5>
                    <p class="card-text text-muted">
                        模擬特定日期的預測情況，查看模型在該日的表現和準確率
                    </p>
                    <div class="mt-auto">
                        <a href="{{ url_for('simulation.date_simulation') }}" class="btn btn-primary">
                            <i class="fas fa-play"></i> 開始模擬
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模型測試 -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-cogs fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">模型測試</h5>
                    <p class="card-text text-muted">
                        比較不同模型版本的表現，測試新功能和算法改進
                    </p>
                    <div class="mt-auto">
                        <a href="{{ url_for('simulation.model_testing') }}" class="btn btn-success">
                            <i class="fas fa-vial"></i> 模型測試
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 歷史分析 -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-line fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">歷史分析</h5>
                    <p class="card-text text-muted">
                        分析歷史預測表現，識別模型強項和改進空間
                    </p>
                    <div class="mt-auto">
                        <a href="{{ url_for('simulation.historical_analysis') }}" class="btn btn-warning">
                            <i class="fas fa-chart-bar"></i> 歷史分析
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速模擬區域 -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-rocket text-primary"></i> 快速模擬
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 06/30 模擬 -->
                        <div class="col-lg-6">
                            <div class="border rounded p-3 mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-target"></i> 06/30 情況模擬
                                </h6>
                                <p class="text-muted small mb-3">
                                    模擬 2025-06-30 當天的預測情況，測試模型在該日的表現
                                </p>
                                <div class="d-flex gap-2">
                                    <a href="{{ url_for('simulation.date_simulation', date='2025-06-30') }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> 查看詳情
                                    </a>
                                    <button type="button" class="btn btn-primary btn-sm" 
                                            onclick="simulateDate('2025-06-30')">
                                        <i class="fas fa-play"></i> 執行模擬
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 自定義日期模擬 -->
                        <div class="col-lg-6">
                            <div class="border rounded p-3 mb-3">
                                <h6 class="text-success">
                                    <i class="fas fa-calendar-alt"></i> 自定義日期模擬
                                </h6>
                                <div class="input-group mb-3">
                                    <input type="date" class="form-control" id="customDate" 
                                           value="2025-06-30" max="{{ date.today().isoformat() }}">
                                    <button class="btn btn-success" type="button" 
                                            onclick="simulateCustomDate()">
                                        <i class="fas fa-play"></i> 模擬
                                    </button>
                                </div>
                                <small class="text-muted">
                                    選擇任意歷史日期進行模擬測試
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近模擬記錄 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history text-info"></i> 最近模擬記錄
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>模擬日期</th>
                                    <th>模擬類型</th>
                                    <th>比賽數量</th>
                                    <th>準確率</th>
                                    <th>狀態</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2025-06-30</td>
                                    <td>
                                        <span class="badge bg-primary">日期模擬</span>
                                    </td>
                                    <td>15</td>
                                    <td>
                                        <span class="text-success">
                                            <i class="fas fa-arrow-up"></i> 60.0%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">已完成</span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('simulation.date_simulation', date='2025-06-30') }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2025-06-29</td>
                                    <td>
                                        <span class="badge bg-success">模型測試</span>
                                    </td>
                                    <td>12</td>
                                    <td>
                                        <span class="text-warning">
                                            <i class="fas fa-minus"></i> 50.0%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">已完成</span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('simulation.date_simulation', date='2025-06-29') }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模擬結果模態框 -->
<div class="modal fade" id="simulationResultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-pie"></i> 模擬結果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="simulationResultContent">
                <!-- 動態載入模擬結果 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                <button type="button" class="btn btn-primary" onclick="viewDetailedResults()">
                    <i class="fas fa-external-link-alt"></i> 查看詳細結果
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentSimulationDate = null;

function simulateDate(date) {
    currentSimulationDate = date;
    
    // 顯示載入狀態
    showSimulationLoading();
    
    fetch('/simulation/api/simulate_date', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            date: date
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSimulationResult(data);
        } else {
            showSimulationError(data.error);
        }
    })
    .catch(error => {
        showSimulationError('網絡錯誤: ' + error.message);
    });
}

function simulateCustomDate() {
    const dateInput = document.getElementById('customDate');
    const selectedDate = dateInput.value;
    
    if (!selectedDate) {
        alert('請選擇日期');
        return;
    }
    
    simulateDate(selectedDate);
}

function showSimulationLoading() {
    const modal = new bootstrap.Modal(document.getElementById('simulationResultModal'));
    document.getElementById('simulationResultContent').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">載入中...</span>
            </div>
            <p class="mt-3 text-muted">正在執行模擬...</p>
        </div>
    `;
    modal.show();
}

function showSimulationResult(data) {
    const result = data.simulation_result;
    const summary = result.summary || {
        total_games: result.games_count || 0,
        games_with_predictions: 0,
        successful_simulations: 0,
        prediction_coverage: 0
    };
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">模擬摘要</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h4 class="text-primary mb-0">${summary.total_games}</h4>
                                    <small class="text-muted">總比賽數</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h4 class="text-success mb-0">${summary.games_with_predictions}</h4>
                                    <small class="text-muted">有預測</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h4 class="text-info mb-0">${summary.successful_simulations}</h4>
                                <small class="text-muted">成功模擬</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">模擬狀態</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                            <div>
                                <h5 class="mb-1">模擬完成</h5>
                                <p class="mb-0 text-muted">日期: ${data.date}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <h6>比賽詳情</h6>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>比賽</th>
                            <th>狀態</th>
                            <th>預測狀態</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${result.simulation_result && result.simulation_result.results ?
                            result.simulation_result.results.map(game => `
                                <tr>
                                    <td>${game.away_team} @ ${game.home_team}</td>
                                    <td>
                                        <span class="badge bg-${game.game_status === 'completed' ? 'success' : 'secondary'}">
                                            ${game.game_status}
                                        </span>
                                    </td>
                                    <td>
                                        ${game.has_existing_prediction ?
                                            '<span class="badge bg-success">有預測</span>' :
                                            '<span class="badge bg-warning">無預測</span>'
                                        }
                                    </td>
                                </tr>
                            `).join('') :
                            `<tr><td colspan="3" class="text-center">
                                ${result.simulation_result ? result.simulation_result.message || '無詳細數據' : '無模擬結果'}
                            </td></tr>`
                        }
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    document.getElementById('simulationResultContent').innerHTML = content;
}

function showSimulationError(error) {
    document.getElementById('simulationResultContent').innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>模擬失敗</strong><br>
            ${error}
        </div>
    `;
}

function viewDetailedResults() {
    if (currentSimulationDate) {
        window.open(`/simulation/date_simulation?date=${currentSimulationDate}`, '_blank');
    }
}
</script>
{% endblock %}
