{% extends "base.html" %}

{% block title %}預測詳情 - {{ away_team.team_name if away_team else game.away_team }} @ {{ home_team.team_name if home_team else game.home_team }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 比賽標題 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-crystal-ball"></i> 
                    {{ away_team.team_name if away_team else game.away_team }} @ {{ home_team.team_name if home_team else game.home_team }}
                </h2>
                <div>
                    <span class="badge bg-primary">{{ game.date.strftime('%Y-%m-%d') }}</span>
                    <a href="{{ url_for('predictions.predictions_list') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- 預測結果卡片 -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> 預測結果</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center mb-4">
                                <div class="col-5">
                                    <div class="team-prediction">
                                        <h4>{{ away_team.team_name if away_team else game.away_team }}</h4>
                                        <div class="predicted-score">
                                            {{ prediction.predicted_away_score|round(1) }}
                                        </div>
                                        <div class="win-probability">
                                            勝率: {{ (prediction.away_win_probability * 100)|round(1) }}%
                                        </div>
                                        <div class="progress mt-2">
                                            <div class="progress-bar bg-primary" role="progressbar" 
                                                 style="width: {{ (prediction.away_win_probability * 100)|round(1) }}%">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-2 d-flex align-items-center justify-content-center">
                                    <div class="vs-indicator">
                                        <span class="text-muted fs-3">VS</span>
                                    </div>
                                </div>
                                <div class="col-5">
                                    <div class="team-prediction">
                                        <h4>{{ home_team.team_name if home_team else game.home_team }}</h4>
                                        <div class="predicted-score home-score">
                                            {{ prediction.predicted_home_score|round(1) }}
                                        </div>
                                        <div class="win-probability">
                                            勝率: {{ (prediction.home_win_probability * 100)|round(1) }}%
                                        </div>
                                        <div class="progress mt-2">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: {{ (prediction.home_win_probability * 100)|round(1) }}%">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 預測摘要 -->
                            <div class="row">
                                <div class="col-md-4 text-center">
                                    <div class="prediction-stat">
                                        <h6>預測總分</h6>
                                        <span class="fs-4 fw-bold">{{ (prediction.predicted_home_score + prediction.predicted_away_score)|round(1) }}</span>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="prediction-stat">
                                        <h6>信心度</h6>
                                        <span class="fs-4 fw-bold 
                                            {% if prediction.confidence > 0.7 %}text-success
                                            {% elif prediction.confidence > 0.5 %}text-warning
                                            {% else %}text-danger{% endif %}">
                                            {{ (prediction.confidence * 100)|round(1) }}%
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="prediction-stat">
                                        <h6>預測勝隊</h6>
                                        <span class="fs-5 fw-bold">
                                            {% if prediction.home_win_probability > prediction.away_win_probability %}
                                                <span class="badge bg-success">{{ home_team.team_name if home_team else game.home_team }}</span>
                                            {% else %}
                                                <span class="badge bg-primary">{{ away_team.team_name if away_team else game.away_team }}</span>
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析詳情 -->
                    {% if analysis %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-analytics"></i> 預測分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% if analysis.key_factors %}
                                <div class="col-md-4">
                                    <h6><i class="fas fa-star text-warning"></i> 關鍵因素</h6>
                                    <ul class="list-unstyled">
                                        {% for factor in analysis.key_factors %}
                                        <li><i class="fas fa-check-circle text-success"></i> {{ factor }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}

                                {% if analysis.risk_factors %}
                                <div class="col-md-4">
                                    <h6><i class="fas fa-exclamation-triangle text-warning"></i> 風險因素</h6>
                                    <ul class="list-unstyled">
                                        {% for risk in analysis.risk_factors %}
                                        <li><i class="fas fa-exclamation-circle text-warning"></i> {{ risk }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}

                                {% if analysis.confidence_indicators %}
                                <div class="col-md-4">
                                    <h6><i class="fas fa-shield-alt text-info"></i> 信心指標</h6>
                                    <ul class="list-unstyled">
                                        {% for indicator in analysis.confidence_indicators %}
                                        <li><i class="fas fa-info-circle text-info"></i> {{ indicator }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                            </div>

                            <div class="mt-3">
                                <h6>建議</h6>
                                <p class="text-muted">{{ analysis.recommendation or '基於當前分析，建議謹慎參考預測結果。' }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 實際結果（如果比賽已完成） -->
                    {% if game.game_status == 'completed' %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-flag-checkered"></i> 實際結果</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-5">
                                    <h6>{{ away_team.team_name if away_team else game.away_team }}</h6>
                                    <div class="fs-2 fw-bold">{{ game.away_score or 0 }}</div>
                                </div>
                                <div class="col-2 d-flex align-items-center justify-content-center">
                                    <span class="text-muted">-</span>
                                </div>
                                <div class="col-5">
                                    <h6>{{ home_team.team_name if home_team else game.home_team }}</h6>
                                    <div class="fs-2 fw-bold">{{ game.home_score or 0 }}</div>
                                </div>
                            </div>
                            
                            <div class="mt-3 text-center">
                                {% set actual_home_wins = game.home_score is not none and game.away_score is not none and game.home_score > game.away_score %}
                                {% set predicted_home_wins = prediction.home_win_probability > prediction.away_win_probability %}
                                {% if actual_home_wins == predicted_home_wins %}
                                    <span class="badge bg-success fs-6">預測正確</span>
                                {% else %}
                                    <span class="badge bg-danger fs-6">預測錯誤</span>
                                {% endif %}
                                
                                <div class="mt-2">
                                    <small class="text-muted">
                                        得分誤差: 主隊 {{ (prediction.predicted_home_score - game.home_score)|abs|round(1) }}, 
                                        客隊 {{ (prediction.predicted_away_score - game.away_score)|abs|round(1) }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- 側邊欄信息 -->
                <div class="col-lg-4">
                    <!-- 預測信息 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6><i class="fas fa-info-circle"></i> 預測信息</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td>模型版本:</td>
                                    <td>{{ prediction.model_version or 'basic' }}</td>
                                </tr>
                                <tr>
                                    <td>預測時間:</td>
                                    <td>{{ prediction.prediction_date.strftime('%Y-%m-%d %H:%M') if prediction.prediction_date else '未知' }}</td>
                                </tr>
                                <tr>
                                    <td>比賽狀態:</td>
                                    <td>
                                        <span class="badge 
                                            {% if game.game_status == 'completed' %}bg-success
                                            {% elif game.game_status == 'in_progress' %}bg-warning
                                            {% else %}bg-secondary{% endif %}">
                                            {{ game.game_status }}
                                        </span>
                                    </td>
                                </tr>
                                {% if game.venue %}
                                <tr>
                                    <td>比賽場地:</td>
                                    <td>{{ game.venue }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-tools"></i> 操作</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" onclick="refreshPrediction()">
                                    <i class="fas fa-sync-alt"></i> 重新預測
                                </button>
                                <button class="btn btn-outline-info" onclick="showFeatureDetails()">
                                    <i class="fas fa-chart-bar"></i> 特徵詳情
                                </button>
                                <a href="{{ url_for('games.game_detail', game_id=game.game_id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-baseball-ball"></i> 比賽詳情
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.predicted-score {
    font-size: 3rem;
    font-weight: bold;
    margin: 10px 0;
}

.home-score {
    color: #28a745;
}

.team-prediction {
    padding: 20px;
    border-radius: 10px;
    background-color: #f8f9fa;
}

.prediction-stat {
    padding: 15px;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.vs-indicator {
    font-size: 2rem;
    font-weight: bold;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function refreshPrediction() {
    // 重新生成預測
    fetch(`/predictions/api/ml_prediction/{{ game.game_id }}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('重新預測失敗: ' + data.error);
            } else {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('重新預測失敗');
        });
}

function showFeatureDetails() {
    // 顯示特徵詳情（可以實現為模態框）
    alert('特徵詳情功能開發中...');
}
</script>
{% endblock %}
