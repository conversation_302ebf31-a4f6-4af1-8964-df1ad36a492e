{% extends "base.html" %}

{% block title %}{{ team.team_name }} - 球隊詳情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 球隊標題 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-users"></i> {{ team.team_name }}</h1>
                    <p class="text-muted mb-0">{{ team.league }} - {{ team.division }}</p>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary fs-6">{{ team.team_code }}</span>
                </div>
            </div>

            <!-- 球隊基本信息 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle"></i> 基本信息</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>球隊代碼:</strong></td>
                                    <td>{{ team.team_code }}</td>
                                </tr>
                                <tr>
                                    <td><strong>聯盟:</strong></td>
                                    <td>{{ team.league }}</td>
                                </tr>
                                <tr>
                                    <td><strong>分區:</strong></td>
                                    <td>{{ team.division }}</td>
                                </tr>
                                <tr>
                                    <td><strong>主場:</strong></td>
                                    <td>{{ team.venue_name or '未知' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar"></i> 戰績統計</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h4 class="text-success">{{ record.total_wins }}</h4>
                                        <small class="text-muted">勝場</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h4 class="text-danger">{{ record.total_losses }}</h4>
                                        <small class="text-muted">敗場</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <h4 class="text-primary">
                                            {% if record.total_wins + record.total_losses > 0 %}
                                                {{ "%.3f"|format(record.total_wins / (record.total_wins + record.total_losses)) }}
                                            {% else %}
                                                .000
                                            {% endif %}
                                        </h4>
                                        <small class="text-muted">勝率</small>
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="stat-item">
                                        <h6>主場戰績</h6>
                                        <span class="badge bg-success">{{ record.home_wins }}勝</span>
                                        <span class="badge bg-danger">{{ record.home_losses }}敗</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-item">
                                        <h6>客場戰績</h6>
                                        <span class="badge bg-success">{{ record.away_wins }}勝</span>
                                        <span class="badge bg-danger">{{ record.away_losses }}敗</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 賽季統計 -->
            {% if team_stats %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> {{ team_stats.season }} 賽季統計</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="stat-card text-center">
                                        <h4>{{ team_stats.runs_scored or 0 }}</h4>
                                        <small class="text-muted">總得分</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card text-center">
                                        <h4>{{ team_stats.runs_allowed or 0 }}</h4>
                                        <small class="text-muted">總失分</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card text-center">
                                        <h4>{{ team_stats.home_runs or 0 }}</h4>
                                        <small class="text-muted">全壘打</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card text-center">
                                        <h4>{{ "%.3f"|format(team_stats.team_era or 0) }}</h4>
                                        <small class="text-muted">防禦率</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 最近比賽 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-calendar"></i> 最近比賽</h5>
                            <a href="{{ url_for('teams.team_games', team_id=team.team_id) }}" class="btn btn-sm btn-outline-primary">
                                查看全部比賽
                            </a>
                        </div>
                        <div class="card-body">
                            {% if recent_games %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>對手</th>
                                            <th>主/客</th>
                                            <th>比分</th>
                                            <th>結果</th>
                                            <th>狀態</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for game in recent_games %}
                                        <tr>
                                            <td>{{ game.date.strftime('%m-%d') }}</td>
                                            <td>
                                                {% if game.home_team == team.team_code %}
                                                    vs {{ game.away_team }}
                                                {% else %}
                                                    @ {{ game.home_team }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if game.home_team == team.team_code %}
                                                    <span class="badge bg-success">主場</span>
                                                {% else %}
                                                    <span class="badge bg-primary">客場</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if game.game_status == 'completed' %}
                                                    {% if game.home_team == team.team_code %}
                                                        {{ game.home_score or 0 }}-{{ game.away_score or 0 }}
                                                    {% else %}
                                                        {{ game.away_score or 0 }}-{{ game.home_score or 0 }}
                                                    {% endif %}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if game.game_status == 'completed' %}
                                                    {% set team_won = (game.home_team == team.team_code and game.home_score is not none and game.away_score is not none and game.home_score > game.away_score) or (game.away_team == team.team_code and game.away_score is not none and game.home_score is not none and game.away_score > game.home_score) %}
                                                    {% if team_won %}
                                                        <span class="badge bg-success">勝</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">敗</span>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ game.game_status }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('games.game_detail', game_id=game.game_id) }}" 
                                                   class="btn btn-sm btn-outline-info">詳情</a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted text-center">暫無比賽記錄</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速導航 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-link"></i> 快速導航</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('teams.team_stats', team_id=team.team_id) }}" 
                                       class="btn btn-outline-primary w-100">
                                        <i class="fas fa-chart-bar"></i> 詳細統計
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('teams.team_players', team_id=team.team_id) }}" 
                                       class="btn btn-outline-success w-100">
                                        <i class="fas fa-users"></i> 球員名單
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('teams.team_games', team_id=team.team_id) }}" 
                                       class="btn btn-outline-info w-100">
                                        <i class="fas fa-calendar"></i> 比賽記錄
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('teams.teams_list') }}" 
                                       class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-arrow-left"></i> 返回列表
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-item {
    padding: 10px;
}

.stat-card {
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-bottom: 10px;
}

.stat-card h4 {
    margin-bottom: 5px;
    font-weight: bold;
}
</style>
{% endblock %}
