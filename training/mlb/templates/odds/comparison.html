{% extends "base.html" %}

{% block title %}盤口比較 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>⚖️ 多平台盤口比較</h4>
                    <p class="text-muted mb-0">日期: {{ selected_date }}</p>
                </div>
                <div>
                    <div class="btn-group" role="group">
                        <input type="date" class="form-control" id="comparisonDate" 
                               value="{{ selected_date }}" onchange="changeDate(this.value)">
                        <a href="{{ url_for('odds_viewer.odds_dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> 錯誤: {{ error }}
    </div>
    {% endif %}

    {% if comparison_data %}
    {% for game_id, game_data in comparison_data.items() %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-baseball-ball"></i> 
                        {% if game_data.game_info %}
                            {{ game_data.game_info.away_team }} @ {{ game_data.game_info.home_team }}
                            <small class="text-muted">(比賽日期: {{ game_data.game_info.date.strftime('%Y-%m-%d') }})</small>
                        {% else %}
                            {{ game_id }}
                        {% endif %}
                        <span class="badge bg-info ms-2">{{ game_data.bookmakers|length }} 平台</span>
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 讓分盤比較 -->
                    <div class="mb-4">
                        <h6><i class="fas fa-exchange-alt text-success"></i> 讓分盤比較</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>博彩商</th>
                                        <th>主隊讓分</th>
                                        <th>主隊賠率</th>
                                        <th>客隊讓分</th>
                                        <th>客隊賠率</th>
                                        <th>優勢</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% set spread_data = [] %}
                                    {% for bookmaker, data in game_data.bookmakers.items() %}
                                        {% if data.spreads %}
                                            {% set _ = spread_data.append((bookmaker, data.spreads)) %}
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if spread_data %}
                                        {% for bookmaker, spreads in spread_data %}
                                        <tr>
                                            <td><span class="badge bg-secondary">{{ bookmaker }}</span></td>
                                            <td>
                                                <span class="fw-bold">{{ "%.1f" | format(spreads.home_spread) }}</span>
                                            </td>
                                            <td>
                                                <span class="{% if spreads.home_price > 0 %}text-success{% else %}text-danger{% endif %}">
                                                    {{ spreads.home_price }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="fw-bold">{{ "%.1f" | format(spreads.away_spread) }}</span>
                                            </td>
                                            <td>
                                                <span class="{% if spreads.away_price > 0 %}text-success{% else %}text-danger{% endif %}">
                                                    {{ spreads.away_price }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if loop.first %}
                                                    <span class="badge bg-warning">基準</span>
                                                {% else %}
                                                    {% set base_spreads = spread_data[0][1] %}
                                                    {% if spreads.home_spread > base_spreads.home_spread %}
                                                        <i class="fas fa-arrow-up text-success" title="讓分更多"></i>
                                                    {% elif spreads.home_spread < base_spreads.home_spread %}
                                                        <i class="fas fa-arrow-down text-danger" title="讓分更少"></i>
                                                    {% else %}
                                                        <i class="fas fa-equals text-muted" title="相同"></i>
                                                    {% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">無讓分盤數據</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 大小分比較 -->
                    <div class="mb-3">
                        <h6><i class="fas fa-calculator text-info"></i> 大小分比較</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>博彩商</th>
                                        <th>總分線</th>
                                        <th>大分賠率</th>
                                        <th>小分賠率</th>
                                        <th>優勢</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% set totals_data = [] %}
                                    {% for bookmaker, data in game_data.bookmakers.items() %}
                                        {% if data.totals %}
                                            {% set _ = totals_data.append((bookmaker, data.totals)) %}
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if totals_data %}
                                        {% for bookmaker, totals in totals_data %}
                                        <tr>
                                            <td><span class="badge bg-secondary">{{ bookmaker }}</span></td>
                                            <td>
                                                <span class="fw-bold">{{ "%.1f" | format(totals.total_line) }}</span>
                                            </td>
                                            <td>
                                                <span class="{% if totals.over_price > 0 %}text-success{% else %}text-danger{% endif %}">
                                                    {{ totals.over_price }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="{% if totals.under_price > 0 %}text-success{% else %}text-danger{% endif %}">
                                                    {{ totals.under_price }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if loop.first %}
                                                    <span class="badge bg-warning">基準</span>
                                                {% else %}
                                                    {% set base_totals = totals_data[0][1] %}
                                                    {% if totals.total_line > base_totals.total_line %}
                                                        <i class="fas fa-arrow-up text-success" title="總分線更高"></i>
                                                    {% elif totals.total_line < base_totals.total_line %}
                                                        <i class="fas fa-arrow-down text-danger" title="總分線更低"></i>
                                                    {% else %}
                                                        <i class="fas fa-equals text-muted" title="相同"></i>
                                                    {% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">無大小分數據</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">該日期沒有盤口數據</h5>
                    <p class="text-muted">請選擇其他日期查看</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function changeDate(newDate) {
    if (newDate) {
        window.location.href = `{{ url_for('odds_viewer.odds_comparison') }}?date=${newDate}`;
    }
}
</script>

<style>
.table-sm th, .table-sm td {
    padding: 0.3rem;
    font-size: 0.9rem;
}

.fw-bold {
    font-weight: 600 !important;
}

.badge {
    font-size: 0.8em;
}
</style>
{% endblock %}