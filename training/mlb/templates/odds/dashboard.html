{% extends "base.html" %}

{% block title %}盤口查看 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h3>📊 MLB盤口查看中心</h3>
                <div>
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i> 刷新數據
                    </button>
                </div>
            </div>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> 錯誤: {{ error }}
    </div>
    {% endif %}

    <!-- 快速搜索區域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">🔍 快速查找盤口</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('odds_viewer.odds_search') }}" method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="date" class="form-label">選擇日期</label>
                            <input type="date" class="form-control" id="date" name="date" 
                                   value="{{ today }}" min="{{ min_date }}" max="{{ max_date }}">
                        </div>
                        <div class="col-md-2">
                            <label for="market_type" class="form-label">市場類型</label>
                            <select class="form-select" id="market_type" name="market_type">
                                <option value="all">全部</option>
                                <option value="spreads">讓分盤</option>
                                <option value="totals">大小分</option>
                                <option value="both">混合盤</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="bookmaker" class="form-label">博彩商</label>
                            <select class="form-select" id="bookmaker" name="bookmaker">
                                <option value="all">全部博彩商</option>
                                {% for bm in bookmakers %}
                                <option value="{{ bm }}">{{ bm }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="per_page" class="form-label">顯示數量</label>
                            <select class="form-select" id="per_page" name="per_page">
                                <option value="20">20條</option>
                                <option value="50">50條</option>
                                <option value="100">100條</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷功能按鈕 -->
    <div class="row mb-4">
        <div class="col-md-4 mb-2">
            <a href="{{ url_for('odds_viewer.odds_search', date=today) }}" class="btn btn-outline-success w-100">
                <i class="fas fa-calendar-day"></i> 今日盤口
            </a>
        </div>
        <div class="col-md-4 mb-2">
            <a href="{{ url_for('odds_viewer.odds_comparison', date=today) }}" class="btn btn-outline-warning w-100">
                <i class="fas fa-balance-scale"></i> 盤口比較
            </a>
        </div>
        <div class="col-md-4 mb-2">
            <button class="btn btn-outline-info w-100" onclick="showOddsAPI()">
                <i class="fas fa-code"></i> API查詢
            </button>
        </div>
    </div>

    <!-- 數據覆蓋信息 -->
    {% if min_date and max_date %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">📅 數據覆蓋範圍</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-6">
                            <div class="border-end">
                                <h6>最早數據</h6>
                                <span class="badge bg-primary fs-6">{{ min_date }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>最新數據</h6>
                            <span class="badge bg-success fs-6">{{ max_date }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 最新盤口數據預覽 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">🆕 最新盤口數據 (前20條)</h6>
                    <a href="{{ url_for('odds_viewer.odds_search') }}" class="btn btn-sm btn-outline-primary">
                        查看全部
                    </a>
                </div>
                <div class="card-body">
                    {% if latest_odds %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>比賽信息</th>
                                    <th>博彩商</th>
                                    <th>市場類型</th>
                                    <th>讓分盤</th>
                                    <th>大小分</th>
                                    <th>更新時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for odd in latest_odds %}
                                <tr>
                                    <td>
                                        {% if odd.game_info %}
                                            <div>
                                                <strong class="text-primary">{{ odd.game_info.away_team }} @ {{ odd.game_info.home_team }}</strong>
                                            </div>
                                            <small class="text-muted">
                                                {{ odd.game_info.date.strftime('%Y-%m-%d') if odd.game_info.date else 'N/A' }}
                                                <br>
                                                <span class="badge bg-light text-dark">{{ odd.game_id }}</span>
                                            </small>
                                        {% else %}
                                            <small class="text-muted">{{ odd.game_id }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ odd.bookmaker }}</span>
                                    </td>
                                    <td>
                                        {% if odd.market_type == 'spreads' %}
                                            <span class="badge bg-success">讓分盤</span>
                                        {% elif odd.market_type == 'totals' %}
                                            <span class="badge bg-info">大小分</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ odd.market_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if odd.home_spread_point is not none %}
                                            <small>
                                                主: {{ odd.home_spread_point }} ({{ odd.home_spread_price }})<br>
                                                客: {{ odd.away_spread_point }} ({{ odd.away_spread_price }})
                                            </small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if odd.total_point is not none %}
                                            <small>
                                                {{ odd.total_point }}<br>
                                                O: {{ odd.over_price }} / U: {{ odd.under_price }}
                                            </small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ odd.created_at.strftime('%m-%d %H:%M') }}
                                        </small>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('odds_viewer.odds_history', game_id=odd.game_id) }}" 
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-history"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暫無盤口數據</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API查詢模態框 -->
<div class="modal fade" id="apiModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">API查詢盤口數據</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="apiDate" class="form-label">選擇日期</label>
                    <input type="date" class="form-control" id="apiDate" value="{{ today }}">
                </div>
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="fetchOddsAPI()">
                        <i class="fas fa-download"></i> 獲取數據
                    </button>
                </div>
                <div id="apiResult" style="display:none;">
                    <h6>查詢結果:</h6>
                    <pre id="apiContent" class="bg-light p-3" style="max-height: 400px; overflow-y: auto;"></pre>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showOddsAPI() {
    $('#apiModal').modal('show');
}

function fetchOddsAPI() {
    const date = document.getElementById('apiDate').value;
    if (!date) {
        alert('請選擇日期');
        return;
    }
    
    fetch(`/odds/api/odds/${date}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('apiContent').textContent = JSON.stringify(data, null, 2);
            document.getElementById('apiResult').style.display = 'block';
        })
        .catch(error => {
            document.getElementById('apiContent').textContent = 'Error: ' + error.message;
            document.getElementById('apiResult').style.display = 'block';
        });
}
</script>
{% endblock %}