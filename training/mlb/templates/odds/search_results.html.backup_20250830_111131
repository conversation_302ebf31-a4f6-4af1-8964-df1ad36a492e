{% extends "base.html" %}

{% block title %}盤口搜索結果 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題和統計 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>🔍 盤口搜索結果</h4>
                    <p class="text-muted mb-0">
                        日期: {{ selected_date }} | 
                        類型: {{ '全部' if market_type == 'all' else market_type }} |
                        博彩商: {{ '全部' if bookmaker == 'all' else bookmaker }}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('odds_viewer.odds_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> 錯誤: {{ error }}
    </div>
    {% endif %}

    <!-- 搜索統計 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h6>總記錄</h6>
                    <span class="fs-4 fw-bold text-primary">{{ total_count }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h6>讓分盤</h6>
                    <span class="fs-4 fw-bold text-success">{{ spreads_count }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h6>大小分</h6>
                    <span class="fs-4 fw-bold text-info">{{ totals_count }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h6>當前頁</h6>
                    <span class="fs-4 fw-bold text-warning">{{ pagination.page if pagination else 1 }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索結果表格 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">📋 搜索結果</h6>
                    {% if pagination %}
                    <div class="text-muted">
                        第 {{ pagination.page }} 頁，共 {{ pagination.pages }} 頁
                        ({{ pagination.total }} 條記錄)
                    </div>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if odds_data %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>比賽信息</th>
                                    <th>博彩商</th>
                                    <th>市場類型</th>
                                    <th>讓分盤數據</th>
                                    <th>大小分數據</th>
                                    <th>時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for odd in odds_data %}
                                <tr>
                                    <td>
                                        {% if odd.game_info %}
                                            <div>
                                                <strong class="text-primary">{{ odd.game_info.away_team }} @ {{ odd.game_info.home_team }}</strong>
                                            </div>
                                            <small class="text-muted">
                                                比賽日期: {{ odd.game_info.date.strftime('%Y-%m-%d') if odd.game_info.date else 'N/A' }}<br>
                                                盤口時間: {{ odd.odds_time.strftime('%Y-%m-%d') if odd.odds_time else 'N/A' }}<br>
                                                <span class="badge bg-light text-dark">ID: {{ odd.game_id }}</span>
                                            </small>
                                        {% else %}
                                            <div>
                                                <strong class="text-primary">{{ odd.game_id }}</strong>
                                            </div>
                                            <small class="text-muted">
                                                {{ odd.odds_time.strftime('%Y-%m-%d') if odd.odds_time else 'N/A' }}
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ odd.bookmaker }}</span>
                                    </td>
                                    <td>
                                        {% if odd.market_type == 'spreads' %}
                                            <span class="badge bg-success">讓分盤</span>
                                        {% elif odd.market_type == 'totals' %}
                                            <span class="badge bg-info">大小分</span>
                                        {% elif odd.market_type == 'both' %}
                                            <span class="badge bg-warning">混合盤</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ odd.market_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if odd.home_spread_point is not none %}
                                        <div class="row">
                                            <div class="col-6">
                                                <small>
                                                    <strong>主隊:</strong><br>
                                                    {{ "%.1f" | format(odd.home_spread_point) }}
                                                    {% if odd.home_spread_price %}
                                                        ({{ odd.home_spread_price }})
                                                    {% endif %}
                                                </small>
                                            </div>
                                            <div class="col-6">
                                                <small>
                                                    <strong>客隊:</strong><br>
                                                    {{ "%.1f" | format(odd.away_spread_point) if odd.away_spread_point is not none else 'N/A' }}
                                                    {% if odd.away_spread_price %}
                                                        ({{ odd.away_spread_price }})
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                        {% else %}
                                            <span class="text-muted">無讓分數據</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if odd.total_point is not none %}
                                        <div>
                                            <small>
                                                <strong>總分線:</strong> 
                                                <span class="badge bg-info">{{ "%.1f" | format(odd.total_point) }}</span><br>
                                                {% if odd.over_price and odd.under_price %}
                                                <strong>大分:</strong> <span class="text-success">{{ odd.over_price }}</span> |
                                                <strong>小分:</strong> <span class="text-warning">{{ odd.under_price }}</span>
                                                {% else %}
                                                <span class="text-muted small">賠率待更新</span>
                                                {% endif %}
                                            </small>
                                        </div>
                                        {% else %}
                                            <span class="text-muted">無大小分數據</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-clock"></i>
                                                {{ odd.created_at.strftime('%m-%d %H:%M:%S') if odd.created_at else 'N/A' }}
                                            </small>
                                        </div>
                                        {% if odd.data_source %}
                                        <div>
                                            <small class="text-info">
                                                <i class="fas fa-source"></i> {{ odd.data_source }}
                                            </small>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical" role="group">
                                            <a href="{{ url_for('odds_viewer.odds_history', game_id=odd.game_id) }}" 
                                               class="btn btn-sm btn-outline-info mb-1" title="查看歷史">
                                                <i class="fas fa-history"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-secondary" 
                                                    onclick="copyGameId('{{ odd.game_id }}')" title="複製ID">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分頁導航 -->
                    {% if pagination and pagination.pages > 1 %}
                    <nav aria-label="搜索結果分頁">
                        <ul class="pagination justify-content-center">
                            <!-- 上一頁 -->
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('odds_viewer.odds_search', 
                                    date=selected_date, 
                                    market_type=market_type, 
                                    bookmaker=bookmaker, 
                                    page=pagination.prev_num) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% endif %}

                            <!-- 頁碼 -->
                            {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('odds_viewer.odds_search', 
                                            date=selected_date, 
                                            market_type=market_type, 
                                            bookmaker=bookmaker, 
                                            page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            <!-- 下一頁 -->
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('odds_viewer.odds_search', 
                                    date=selected_date, 
                                    market_type=market_type, 
                                    bookmaker=bookmaker, 
                                    page=pagination.next_num) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">沒有找到符合條件的盤口數據</h5>
                        <p class="text-muted">請嘗試調整搜索條件</p>
                        <a href="{{ url_for('odds_viewer.odds_dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> 返回主頁
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyGameId(gameId) {
    navigator.clipboard.writeText(gameId).then(function() {
        // 可以添加成功提示
        alert('比賽ID已複製到剪貼板');
    }, function(err) {
        console.error('複製失敗: ', err);
    });
}
</script>
{% endblock %}