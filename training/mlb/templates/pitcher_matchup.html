{% extends "base.html" %}

{% block title %}投手對戰 - {{ game.away_team }} @ {{ game.home_team }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 比賽標題 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h2 class="card-title mb-0">
                        <i class="fas fa-baseball-ball me-2"></i>
                        投手對戰分析
                    </h2>
                    <h4 class="mt-2">
                        {{ matchup.teams.away.name }} @ {{ matchup.teams.home.name }}
                    </h4>
                    <p class="mb-0">
                        <i class="fas fa-calendar me-1"></i>{{ matchup.game_info.date }}
                        <span class="ms-3">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ matchup.game_info.venue }}
                        </span>
                        <span class="ms-3">
                            <span class="badge bg-light text-dark">{{ matchup.game_info.status }}</span>
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 投手對戰主要內容 -->
    <div class="row">
        <!-- 客隊投手 -->
        <div class="col-md-5">
            <div class="card h-100">
                <div class="card-header bg-danger text-white text-center">
                    <h5 class="mb-0">
                        <i class="fas fa-plane me-2"></i>
                        客隊先發投手
                    </h5>
                    <h6 class="mt-1">{{ matchup.teams.away.name }}</h6>
                </div>
                <div class="card-body">
                    {% if matchup.pitchers.away %}
                        <div class="text-center mb-3">
                            <h4 class="text-primary">{{ matchup.pitchers.away.name }}</h4>
                            <span class="badge bg-{{ 'success' if matchup.pitchers.away.strength == '王牌' else 'primary' if matchup.pitchers.away.strength == '優秀' else 'warning' if matchup.pitchers.away.strength == '普通' else 'danger' }} fs-6">
                                {{ matchup.pitchers.away.strength }}
                            </span>
                        </div>
                        
                        <!-- ERA 顯示 -->
                        <div class="row text-center mb-3">
                            <div class="col-12">
                                <div class="bg-light p-3 rounded">
                                    <h2 class="text-{{ 'success' if matchup.pitchers.away.era <= 3.50 else 'warning' if matchup.pitchers.away.era <= 4.50 else 'danger' }} mb-0">
                                        {{ "%.2f"|format(matchup.pitchers.away.era) }}
                                    </h2>
                                    <small class="text-muted">ERA</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 投手統計 -->
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ matchup.pitchers.away.record }}</h5>
                                    <small class="text-muted">勝-敗</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ matchup.pitchers.away.strikeouts }}</h5>
                                    <small class="text-muted">三振</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-2">
                            <div class="col-6">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ matchup.pitchers.away.walks }}</h5>
                                    <small class="text-muted">保送</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ "%.1f"|format(matchup.pitchers.away.innings_pitched) }}</h5>
                                    <small class="text-muted">投球局數</small>
                                </div>
                            </div>
                        </div>
                        
                        {% if matchup.pitchers.away.whip %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ "%.2f"|format(matchup.pitchers.away.whip) }}</h5>
                                    <small class="text-muted">WHIP</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- 數據來源 -->
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                數據來源: {{ matchup.pitchers.away.data_source }}
                                {% if matchup.pitchers.away.season %}
                                ({{ matchup.pitchers.away.season }} 賽季)
                                {% endif %}
                            </small>
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-question-circle fa-3x mb-3"></i>
                            <p>投手信息暫未確定</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 對戰分析 -->
        <div class="col-md-2">
            <div class="card h-100 bg-light">
                <div class="card-body d-flex flex-column justify-content-center text-center">
                    <div class="mb-3">
                        <i class="fas fa-vs fa-2x text-primary"></i>
                    </div>
                    
                    {% if matchup.matchup_analysis %}
                        <h6 class="text-primary">{{ matchup.matchup_analysis.matchup_type }}</h6>
                        <p class="small mb-2">{{ matchup.matchup_analysis.advantage }}</p>
                        <p class="small text-muted">{{ matchup.matchup_analysis.prediction }}</p>
                    {% else %}
                        <p class="small text-muted">對戰分析中...</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 主隊投手 -->
        <div class="col-md-5">
            <div class="card h-100">
                <div class="card-header bg-success text-white text-center">
                    <h5 class="mb-0">
                        <i class="fas fa-home me-2"></i>
                        主隊先發投手
                    </h5>
                    <h6 class="mt-1">{{ matchup.teams.home.name }}</h6>
                </div>
                <div class="card-body">
                    {% if matchup.pitchers.home %}
                        <div class="text-center mb-3">
                            <h4 class="text-primary">{{ matchup.pitchers.home.name }}</h4>
                            <span class="badge bg-{{ 'success' if matchup.pitchers.home.strength == '王牌' else 'primary' if matchup.pitchers.home.strength == '優秀' else 'warning' if matchup.pitchers.home.strength == '普通' else 'danger' }} fs-6">
                                {{ matchup.pitchers.home.strength }}
                            </span>
                        </div>
                        
                        <!-- ERA 顯示 -->
                        <div class="row text-center mb-3">
                            <div class="col-12">
                                <div class="bg-light p-3 rounded">
                                    <h2 class="text-{{ 'success' if matchup.pitchers.home.era <= 3.50 else 'warning' if matchup.pitchers.home.era <= 4.50 else 'danger' }} mb-0">
                                        {{ "%.2f"|format(matchup.pitchers.home.era) }}
                                    </h2>
                                    <small class="text-muted">ERA</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 投手統計 -->
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ matchup.pitchers.home.record }}</h5>
                                    <small class="text-muted">勝-敗</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ matchup.pitchers.home.strikeouts }}</h5>
                                    <small class="text-muted">三振</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-2">
                            <div class="col-6">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ matchup.pitchers.home.walks }}</h5>
                                    <small class="text-muted">保送</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ "%.1f"|format(matchup.pitchers.home.innings_pitched) }}</h5>
                                    <small class="text-muted">投球局數</small>
                                </div>
                            </div>
                        </div>
                        
                        {% if matchup.pitchers.home.whip %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="text-center p-2 border rounded">
                                    <h5 class="mb-0">{{ "%.2f"|format(matchup.pitchers.home.whip) }}</h5>
                                    <small class="text-muted">WHIP</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- 數據來源 -->
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                數據來源: {{ matchup.pitchers.home.data_source }}
                                {% if matchup.pitchers.home.season %}
                                ({{ matchup.pitchers.home.season }} 賽季)
                                {% endif %}
                            </small>
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-question-circle fa-3x mb-3"></i>
                            <p>投手信息暫未確定</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 對戰分析詳情 -->
    {% if matchup.matchup_analysis and matchup.matchup_analysis.key_points %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        對戰分析要點
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        {% for point in matchup.matchup_analysis.key_points %}
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            {{ point }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 操作按鈕 -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="{{ url_for('games.game_detail', game_id=game.game_id) }}" class="btn btn-primary me-2">
                <i class="fas fa-arrow-left me-1"></i>
                返回比賽詳情
            </a>
            <a href="{{ url_for('games.game_preview', game_id=game.game_id) }}" class="btn btn-info me-2">
                <i class="fas fa-eye me-1"></i>
                比賽預覽
            </a>
            <button class="btn btn-success" onclick="refreshPitcherData()">
                <i class="fas fa-sync-alt me-1"></i>
                刷新投手數據
            </button>
        </div>
    </div>
</div>

<script>
function refreshPitcherData() {
    // 顯示載入狀態
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>載入中...';
    btn.disabled = true;
    
    // 調用API刷新數據
    fetch(`{{ url_for('games.api_pitcher_matchup', game_id=game.game_id) }}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('刷新失敗: ' + data.error);
            } else {
                // 重新載入頁面以顯示最新數據
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('刷新失敗: ' + error.message);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}
</script>
{% endblock %}
