{% extends "base.html" %}

{% block title %}{{ away_team.team_name if away_team else game.away_team }} @ {{ home_team.team_name if home_team else game.home_team }} - 比賽詳情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 比賽標題 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-baseball-ball"></i>
                        {{ away_team.team_name if away_team else game.away_team }} @ {{ home_team.team_name if home_team else game.home_team }}
                    </h2>
                    <div class="text-muted">
                        {{ game.date.strftime('%Y年%m月%d日') }}
                        {% if game.venue %}| {{ game.venue }}{% endif %}
                        | 狀態: 
                        <span class="badge 
                            {% if game.game_status == 'completed' %}bg-success
                            {% elif game.game_status == 'in_progress' %}bg-warning
                            {% else %}bg-secondary{% endif %}">
                            {{ game.game_status }}
                        </span>
                    </div>
                </div>
                <div>
                    <a href="{{ url_for('games.games_list') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                    <a href="{{ url_for('games.game_preview', game_id=game.game_id) }}" class="btn btn-outline-info">
                        <i class="fas fa-eye"></i> 比賽預覽
                    </a>
                    <a href="{{ url_for('games.pitcher_matchup', game_id=game.game_id) }}" class="btn btn-outline-warning">
                        <i class="fas fa-baseball-ball"></i> 投手對戰
                    </a>
                    {% if prediction %}
                    <a href="{{ url_for('predictions.prediction_detail', game_id=game.game_id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-crystal-ball"></i> 查看預測
                    </a>
                    {% endif %}
                    {% if game.game_status in ['scheduled', 'postponed'] %}
                    <button type="button" class="btn btn-outline-success" onclick="generateAdvancedPrediction('{{ game.game_id }}')">
                        <i class="fas fa-magic"></i> 生成進階預測
                    </button>
                    {% endif %}
                </div>
            </div>

            <!-- 比分卡片 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-5">
                                    <div class="team-score">
                                        <h3>{{ away_team.team_name if away_team else game.away_team }}</h3>
                                        <div class="score">{{ game.away_score or 0 if game.game_status == 'completed' else '-' }}</div>
                                    </div>
                                </div>
                                <div class="col-2 d-flex align-items-center justify-content-center">
                                    <div class="vs-indicator">
                                        <span class="text-muted fs-3">VS</span>
                                    </div>
                                </div>
                                <div class="col-5">
                                    <div class="team-score home-team">
                                        <h3>{{ home_team.team_name if home_team else game.home_team }}</h3>
                                        <div class="score">{{ game.home_score or 0 if game.game_status == 'completed' else '-' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 進階預測 (如果比賽尚未開始) -->
            {% if advanced_prediction and game.game_status in ['scheduled', 'postponed'] %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-magic"></i> 進階預測分析
                                <span class="badge bg-light text-primary ms-2">{{ advanced_prediction.model_version }}</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 預測比分 -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h6 class="text-muted mb-2">預測比分</h6>
                                        <div class="h4 mb-0">
                                            <span class="text-primary">{{ advanced_prediction.predicted_away_score }}</span>
                                            <span class="mx-2">-</span>
                                            <span class="text-success">{{ advanced_prediction.predicted_home_score }}</span>
                                        </div>
                                        <small class="text-muted">
                                            {{ away_team.team_name if away_team else game.away_team }} @ {{ home_team.team_name if home_team else game.home_team }}
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center p-2 bg-info text-white rounded">
                                                <div class="h6 mb-1">總分</div>
                                                <div class="h5 mb-0">{{ advanced_prediction.total_score }}</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center p-2 bg-success text-white rounded">
                                                <div class="h6 mb-1">主隊勝率</div>
                                                <div class="h5 mb-0">{{ (advanced_prediction.home_win_probability * 100)|round(1) }}%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 預測因素分析 -->
                            {% if advanced_prediction.prediction_factors %}
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-chart-bar"></i> 進攻能力評級
                                    </h6>
                                    <div class="mb-2">
                                        <div class="d-flex justify-content-between">
                                            <span>{{ away_team.team_name if away_team else game.away_team }}</span>
                                            <span class="badge bg-primary">{{ advanced_prediction.prediction_factors.away_offense_rating|round(0) }}</span>
                                        </div>
                                        <div class="progress mt-1" style="height: 8px;">
                                            <div class="progress-bar bg-primary" style="width: {{ advanced_prediction.prediction_factors.away_offense_rating }}%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>{{ home_team.team_name if home_team else game.home_team }}</span>
                                            <span class="badge bg-success">{{ advanced_prediction.prediction_factors.home_offense_rating|round(0) }}</span>
                                        </div>
                                        <div class="progress mt-1" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: {{ advanced_prediction.prediction_factors.home_offense_rating }}%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-shield-alt"></i> 投手能力評級
                                    </h6>
                                    <div class="mb-2">
                                        <div class="d-flex justify-content-between">
                                            <span>{{ away_team.team_name if away_team else game.away_team }}</span>
                                            <span class="badge bg-warning">{{ advanced_prediction.prediction_factors.away_pitching_rating|round(0) }}</span>
                                        </div>
                                        <div class="progress mt-1" style="height: 8px;">
                                            <div class="progress-bar bg-warning" style="width: {{ advanced_prediction.prediction_factors.away_pitching_rating }}%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>{{ home_team.team_name if home_team else game.home_team }}</span>
                                            <span class="badge bg-info">{{ advanced_prediction.prediction_factors.home_pitching_rating|round(0) }}</span>
                                        </div>
                                        <div class="progress mt-1" style="height: 8px;">
                                            <div class="progress-bar bg-info" style="width: {{ advanced_prediction.prediction_factors.home_pitching_rating }}%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 其他因素 -->
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <div class="text-center p-2 border rounded">
                                        <div class="text-muted small">主場優勢</div>
                                        <div class="h6 mb-0 text-success">+{{ advanced_prediction.prediction_factors.home_field_advantage|round(1) }}</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center p-2 border rounded">
                                        <div class="text-muted small">近況影響</div>
                                        <div class="h6 mb-0 {% if advanced_prediction.prediction_factors.recent_form_impact > 0 %}text-success{% elif advanced_prediction.prediction_factors.recent_form_impact < 0 %}text-danger{% else %}text-muted{% endif %}">
                                            {{ advanced_prediction.prediction_factors.recent_form_impact|round(1) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center p-2 border rounded">
                                        <div class="text-muted small">預測方法</div>
                                        <div class="h6 mb-0 text-primary">進階統計</div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            <!-- 預測說明 -->
                            <div class="mt-4 p-3 bg-light rounded">
                                <h6 class="text-primary mb-2">
                                    <i class="fas fa-info-circle"></i> 預測說明
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled small mb-0">
                                            <li><i class="fas fa-check text-success"></i> 球隊平均得分能力</li>
                                            <li><i class="fas fa-check text-success"></i> 殘壘率 (LOB%)</li>
                                            <li><i class="fas fa-check text-success"></i> 得點圈打擊率 (RISP)</li>
                                            <li><i class="fas fa-check text-success"></i> 打點能力 (RBI)</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled small mb-0">
                                            <li><i class="fas fa-check text-success"></i> 投手防禦率 (ERA)</li>
                                            <li><i class="fas fa-check text-success"></i> 主客場差異</li>
                                            <li><i class="fas fa-check text-success"></i> 最近10場表現</li>
                                            <li><i class="fas fa-check text-success"></i> 王牌投手效應</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Box Score (如果比賽已完成) -->
            {% if game.game_status == 'completed' and box_scores %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar"></i> Box Score</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>球隊</th>
                                            <th>得分</th>
                                            <th>安打</th>
                                            <th>失誤</th>
                                            <th>殘壘</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if box_scores.away %}
                                        <tr>
                                            <td><strong>{{ away_team.team_name if away_team else game.away_team }}</strong></td>
                                            <td>{{ box_scores.away.runs }}</td>
                                            <td>{{ box_scores.away.hits }}</td>
                                            <td>{{ box_scores.away.errors }}</td>
                                            <td>{{ box_scores.away.left_on_base }}</td>
                                        </tr>
                                        {% endif %}
                                        {% if box_scores.home %}
                                        <tr>
                                            <td><strong>{{ home_team.team_name if home_team else game.home_team }}</strong></td>
                                            <td>{{ box_scores.home.runs }}</td>
                                            <td>{{ box_scores.home.hits }}</td>
                                            <td>{{ box_scores.home.errors }}</td>
                                            <td>{{ box_scores.home.left_on_base }}</td>
                                        </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- 各局得分 -->
                            {% if box_scores.home and box_scores.home.inning_scores %}
                            <h6 class="mt-4 mb-3">各局得分</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>球隊</th>
                                            {% set home_innings = box_scores.home.inning_scores | from_json %}
                                            {% for inning in range(1, (home_innings.keys() | list | length) + 1) %}
                                            <th>{{ inning }}</th>
                                            {% endfor %}
                                            <th>R</th>
                                            <th>H</th>
                                            <th>E</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if box_scores.away and box_scores.away.inning_scores %}
                                        {% set away_innings = box_scores.away.inning_scores | from_json %}
                                        <tr>
                                            <td><strong>{{ away_team.team_code if away_team else game.away_team }}</strong></td>
                                            {% for inning in range(1, (away_innings.keys() | list | length) + 1) %}
                                            <td>{{ away_innings[inning|string] or 0 }}</td>
                                            {% endfor %}
                                            <td><strong>{{ box_scores.away.runs }}</strong></td>
                                            <td>{{ box_scores.away.hits }}</td>
                                            <td>{{ box_scores.away.errors }}</td>
                                        </tr>
                                        {% endif %}
                                        <tr>
                                            <td><strong>{{ home_team.team_code if home_team else game.home_team }}</strong></td>
                                            {% for inning in range(1, (home_innings.keys() | list | length) + 1) %}
                                            <td>{{ home_innings[inning|string] or 0 }}</td>
                                            {% endfor %}
                                            <td><strong>{{ box_scores.home.runs }}</strong></td>
                                            <td>{{ box_scores.home.hits }}</td>
                                            <td>{{ box_scores.home.errors }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 詳細球員統計 (如果有數據) -->
            {% if player_stats and (player_stats.home or player_stats.away) %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-users"></i> 詳細球員表現</h5>
                            <ul class="nav nav-tabs card-header-tabs" id="playerStatsTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="batting-tab" data-bs-toggle="tab" data-bs-target="#batting" type="button" role="tab">
                                        <i class="fas fa-baseball-ball"></i> 打擊統計
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="pitching-tab" data-bs-toggle="tab" data-bs-target="#pitching" type="button" role="tab">
                                        <i class="fas fa-hand-paper"></i> 投手統計
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="fielding-tab" data-bs-toggle="tab" data-bs-target="#fielding" type="button" role="tab">
                                        <i class="fas fa-shield-alt"></i> 守備統計
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="playerStatsTabContent">
                                <!-- 打擊統計標籤 -->
                                <div class="tab-pane fade show active" id="batting" role="tabpanel">
                                    <!-- 客隊打擊統計 -->
                                    {% if player_stats.away %}
                                    <h6 class="mb-3">
                                        <span class="badge bg-primary">客隊</span>
                                        {{ away_team.team_name if away_team else game.away_team }} 打擊統計
                                    </h6>
                                    <div class="table-responsive mb-4">
                                        <table class="table table-sm table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>打序</th>
                                                    <th>球員</th>
                                                    <th>位置</th>
                                                    <th>打數</th>
                                                    <th>得分</th>
                                                    <th>安打</th>
                                                    <th>打點</th>
                                                    <th>2B</th>
                                                    <th>3B</th>
                                                    <th>HR</th>
                                                    <th>BB</th>
                                                    <th>SO</th>
                                                    <th>SB</th>
                                                    <th>AVG</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for stat, player in player_stats.away %}
                                                {% if stat.at_bats > 0 or stat.walks > 0 %}
                                                <tr>
                                                    <td>
                                                        {% if stat.batting_order %}
                                                            <span class="badge bg-secondary">{{ stat.batting_order }}</span>
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if player and player.full_name %}
                                                            <a href="{{ url_for('players.player_detail', player_id=player.player_id) }}"
                                                               class="text-decoration-none fw-bold">{{ player.full_name }}</a>
                                                        {% else %}
                                                            <span class="text-muted">球員 #{{ stat.player_id }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td><span class="badge bg-info">{{ stat.position or 'N/A' }}</span></td>
                                                    <td>{{ stat.at_bats }}</td>
                                                    <td>{{ stat.runs }}</td>
                                                    <td><strong>{{ stat.hits }}</strong></td>
                                                    <td>{{ stat.rbi }}</td>
                                                    <td>{{ stat.doubles or 0 }}</td>
                                                    <td>{{ stat.triples or 0 }}</td>
                                                    <td>{% if stat.home_runs > 0 %}<strong class="text-warning">{{ stat.home_runs }}</strong>{% else %}0{% endif %}</td>
                                                    <td>{{ stat.walks }}</td>
                                                    <td>{{ stat.strikeouts }}</td>
                                                    <td>{{ stat.stolen_bases or 0 }}</td>
                                                    <td>
                                                        {% if stat.at_bats > 0 %}
                                                            {{ "%.3f"|format(stat.hits / stat.at_bats) }}
                                                        {% else %}
                                                            .000
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endif %}
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% endif %}

                                    <!-- 主隊打擊統計 -->
                                    {% if player_stats.home %}
                                    <h6 class="mb-3">
                                        <span class="badge bg-success">主隊</span>
                                        {{ home_team.team_name if home_team else game.home_team }} 打擊統計
                                    </h6>
                                    <div class="table-responsive mb-4">
                                        <table class="table table-sm table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>打序</th>
                                                    <th>球員</th>
                                                    <th>位置</th>
                                                    <th>打數</th>
                                                    <th>得分</th>
                                                    <th>安打</th>
                                                    <th>打點</th>
                                                    <th>2B</th>
                                                    <th>3B</th>
                                                    <th>HR</th>
                                                    <th>BB</th>
                                                    <th>SO</th>
                                                    <th>SB</th>
                                                    <th>AVG</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for stat, player in player_stats.home %}
                                                {% if stat.at_bats > 0 or stat.walks > 0 %}
                                                <tr>
                                                    <td>
                                                        {% if stat.batting_order %}
                                                            <span class="badge bg-secondary">{{ stat.batting_order }}</span>
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if player and player.full_name %}
                                                            <a href="{{ url_for('players.player_detail', player_id=player.player_id) }}"
                                                               class="text-decoration-none fw-bold">{{ player.full_name }}</a>
                                                        {% else %}
                                                            <span class="text-muted">球員 #{{ stat.player_id }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td><span class="badge bg-info">{{ stat.position or 'N/A' }}</span></td>
                                                    <td>{{ stat.at_bats }}</td>
                                                    <td>{{ stat.runs }}</td>
                                                    <td><strong>{{ stat.hits }}</strong></td>
                                                    <td>{{ stat.rbi }}</td>
                                                    <td>{{ stat.doubles or 0 }}</td>
                                                    <td>{{ stat.triples or 0 }}</td>
                                                    <td>{% if stat.home_runs > 0 %}<strong class="text-warning">{{ stat.home_runs }}</strong>{% else %}0{% endif %}</td>
                                                    <td>{{ stat.walks }}</td>
                                                    <td>{{ stat.strikeouts }}</td>
                                                    <td>{{ stat.stolen_bases or 0 }}</td>
                                                    <td>
                                                        {% if stat.at_bats > 0 %}
                                                            {{ "%.3f"|format(stat.hits / stat.at_bats) }}
                                                        {% else %}
                                                            .000
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endif %}
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- 投手統計標籤 -->
                                <div class="tab-pane fade" id="pitching" role="tabpanel">
                                    <h6 class="mb-3"><i class="fas fa-hand-paper"></i> 投手詳細統計</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>球員</th>
                                                    <th>球隊</th>
                                                    <th>投球局數</th>
                                                    <th>投球數</th>
                                                    <th>好球數</th>
                                                    <th>被安打</th>
                                                    <th>失分</th>
                                                    <th>自責分</th>
                                                    <th>三振</th>
                                                    <th>保送</th>
                                                    <th>被HR</th>
                                                    <th>ERA</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for team_name, team_stats in [('away', player_stats.away), ('home', player_stats.home)] %}
                                                {% if team_stats %}
                                                {% for stat, player in team_stats %}
                                                {% if stat.innings_pitched > 0 %}
                                                <tr>
                                                    <td>
                                                        {% if player and player.full_name %}
                                                            <a href="{{ url_for('players.player_detail', player_id=player.player_id) }}"
                                                               class="text-decoration-none fw-bold">{{ player.full_name }}</a>
                                                        {% else %}
                                                            <span class="text-muted">球員 #{{ stat.player_id }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if team_name == 'home' %}
                                                            <span class="badge bg-success">{{ home_team.team_code if home_team else game.home_team }}</span>
                                                        {% else %}
                                                            <span class="badge bg-primary">{{ away_team.team_code if away_team else game.away_team }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td><strong>{{ stat.innings_pitched }}</strong></td>
                                                    <td>{{ stat.pitches_thrown or 0 }}</td>
                                                    <td>{{ stat.strikes_thrown or 0 }}</td>
                                                    <td>{{ stat.hits_allowed }}</td>
                                                    <td>{{ stat.runs_allowed }}</td>
                                                    <td><strong>{{ stat.earned_runs }}</strong></td>
                                                    <td>{{ stat.strikeouts_pitched }}</td>
                                                    <td>{{ stat.walks_allowed }}</td>
                                                    <td>{% if stat.home_runs_allowed > 0 %}<strong class="text-danger">{{ stat.home_runs_allowed }}</strong>{% else %}0{% endif %}</td>
                                                    <td>
                                                        {% if stat.innings_pitched > 0 %}
                                                            {{ "%.2f"|format((stat.earned_runs * 9) / stat.innings_pitched) }}
                                                        {% else %}
                                                            0.00
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endif %}
                                                {% endfor %}
                                                {% endif %}
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- 守備統計標籤 -->
                                <div class="tab-pane fade" id="fielding" role="tabpanel">
                                    <h6 class="mb-3"><i class="fas fa-shield-alt"></i> 守備統計</h6>

                                    <!-- 客隊守備統計 -->
                                    {% if player_stats.away %}
                                    <h6 class="mb-3">
                                        <span class="badge bg-primary">客隊</span>
                                        {{ away_team.team_name if away_team else game.away_team }} 守備統計
                                    </h6>
                                    <div class="table-responsive mb-4">
                                        <table class="table table-sm table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>球員</th>
                                                    <th>位置</th>
                                                    <th>刺殺</th>
                                                    <th>助殺</th>
                                                    <th>失誤</th>
                                                    <th>守備率</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for stat, player in player_stats.away %}
                                                {% if stat.putouts > 0 or stat.assists > 0 or stat.errors > 0 %}
                                                <tr>
                                                    <td>
                                                        {% if player and player.full_name %}
                                                            <a href="{{ url_for('players.player_detail', player_id=player.player_id) }}"
                                                               class="text-decoration-none fw-bold">{{ player.full_name }}</a>
                                                        {% else %}
                                                            <span class="text-muted">球員 #{{ stat.player_id }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td><span class="badge bg-info">{{ stat.position or 'N/A' }}</span></td>
                                                    <td>{{ stat.putouts }}</td>
                                                    <td>{{ stat.assists }}</td>
                                                    <td>{% if stat.errors > 0 %}<strong class="text-danger">{{ stat.errors }}</strong>{% else %}0{% endif %}</td>
                                                    <td>
                                                        {% set total_chances = stat.putouts + stat.assists + stat.errors %}
                                                        {% if total_chances > 0 %}
                                                            {{ "%.3f"|format((stat.putouts + stat.assists) / total_chances) }}
                                                        {% else %}
                                                            1.000
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endif %}
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% endif %}

                                    <!-- 主隊守備統計 -->
                                    {% if player_stats.home %}
                                    <h6 class="mb-3">
                                        <span class="badge bg-success">主隊</span>
                                        {{ home_team.team_name if home_team else game.home_team }} 守備統計
                                    </h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>球員</th>
                                                    <th>位置</th>
                                                    <th>刺殺</th>
                                                    <th>助殺</th>
                                                    <th>失誤</th>
                                                    <th>守備率</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for stat, player in player_stats.home %}
                                                {% if stat.putouts > 0 or stat.assists > 0 or stat.errors > 0 %}
                                                <tr>
                                                    <td>
                                                        {% if player and player.full_name %}
                                                            <a href="{{ url_for('players.player_detail', player_id=player.player_id) }}"
                                                               class="text-decoration-none fw-bold">{{ player.full_name }}</a>
                                                        {% else %}
                                                            <span class="text-muted">球員 #{{ stat.player_id }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td><span class="badge bg-info">{{ stat.position or 'N/A' }}</span></td>
                                                    <td>{{ stat.putouts }}</td>
                                                    <td>{{ stat.assists }}</td>
                                                    <td>{% if stat.errors > 0 %}<strong class="text-danger">{{ stat.errors }}</strong>{% else %}0{% endif %}</td>
                                                    <td>
                                                        {% set total_chances = stat.putouts + stat.assists + stat.errors %}
                                                        {% if total_chances > 0 %}
                                                            {{ "%.3f"|format((stat.putouts + stat.assists) / total_chances) }}
                                                        {% else %}
                                                            1.000
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endif %}
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 對戰記錄 -->
            {% if h2h_games %}
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-history"></i> 最近對戰記錄</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>客隊</th>
                                            <th>比分</th>
                                            <th>主隊</th>
                                            <th>場地</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for h2h_game in h2h_games %}
                                        <tr>
                                            <td>{{ h2h_game.date.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ h2h_game.away_team }}</td>
                                            <td>
                                                <strong>{{ h2h_game.away_score or 0 }} - {{ h2h_game.home_score or 0 }}</strong>
                                            </td>
                                            <td>{{ h2h_game.home_team }}</td>
                                            <td>{{ h2h_game.venue or 'N/A' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.team-score {
    padding: 20px;
}

.team-score h3 {
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.score {
    font-size: 3rem;
    font-weight: bold;
    color: #007bff;
}

.home-team .score {
    color: #28a745;
}

.vs-indicator {
    font-size: 2rem;
    font-weight: bold;
}

.table th {
    font-size: 0.9rem;
}

.table td {
    font-size: 0.85rem;
}

/* 球員統計標籤頁樣式 */
.nav-tabs .nav-link {
    color: #495057;
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    isolation: isolate;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

/* 統計表格增強樣式 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.075);
}

.table-dark th {
    background-color: #343a40;
    border-color: #454d55;
}

/* 徽章樣式 */
.badge {
    font-size: 0.75em;
}

/* 球員姓名連結樣式 */
.fw-bold {
    font-weight: 600 !important;
}

/* 數據高亮 */
.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* 響應式調整 */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.8rem;
    }

    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .badge {
        font-size: 0.7em;
    }
}

/* 標籤頁內容間距 */
.tab-pane {
    padding-top: 1rem;
}

/* 球隊徽章顏色 */
.badge.bg-primary {
    background-color: #0d6efd !important;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}
</style>

<script>
function generateAdvancedPrediction(gameId) {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    button.disabled = true;

    fetch(`/games/api/${gameId}/advanced_prediction`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 顯示成功消息
                showAlert('success', '進階預測生成成功！頁面將重新載入以顯示結果。');
                // 重新載入頁面顯示預測結果
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showAlert('danger', '生成失敗：' + (data.error || '未知錯誤'));
            }
        })
        .catch(error => {
            showAlert('danger', '網絡錯誤：' + error.message);
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 插入到頁面頂部
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // 5秒後自動消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
