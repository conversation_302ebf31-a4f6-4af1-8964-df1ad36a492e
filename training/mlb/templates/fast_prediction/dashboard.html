{% extends "base.html" %}

{% block title %}快速智能預測 - MLB預測系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bolt"></i> 快速智能預測</h2>
                <div>
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回主頁
                    </a>
                </div>
            </div>

            <!-- 日期選擇和預測生成 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-calendar-alt"></i> 選擇日期</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="targetDate" class="form-label">預測日期</label>
                            <input type="date" 
                                   class="form-control" 
                                   id="targetDate" 
                                   name="targetDate" 
                                   value="{{ datetime.now().strftime('%Y-%m-%d') }}">
                        </div>
                        <div class="col-md-8">
                            <label class="form-label">快速選擇</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-secondary" onclick="setDate(-1)">昨天</button>
                                <button class="btn btn-outline-secondary" onclick="setDate(0)">今天</button>
                                <button class="btn btn-outline-secondary" onclick="setDate(1)">明天</button>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="excludePostponed" checked>
                                <label class="form-check-label" for="excludePostponed">
                                    <i class="fas fa-calendar-times text-warning"></i> 排除延期/取消比賽
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button id="generateBtn" class="btn btn-primary" onclick="generatePredictions()">
                                <i class="fas fa-bolt"></i> 快速生成預測
                            </button>
                            <div id="loadingIndicator" class="spinner-border text-primary ms-2" role="status" style="display: none;">
                                <span class="visually-hidden">載入中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 錯誤提示 -->
            <div id="errorAlert" class="alert alert-danger" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i> <span id="errorMessage"></span>
            </div>

            <!-- 預測結果 -->
            <div id="predictionResults" class="card" style="display: none;">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-chart-line"></i> 預測結果</h5>
                        <div>
                            <span id="predictionCount" class="badge bg-light text-dark me-2">0場比賽</span>
                            <span id="predictionTime" class="badge bg-light text-dark">耗時: 0秒</span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>比賽</th>
                                    <th>狀態</th>
                                    <th>預測比分</th>
                                    <th>投手對戰</th>
                                    <th>信心度</th>
                                    <th>策略</th>
                                    <th>實際結果</th>
                                    <th>預測耗時</th>
                                </tr>
                            </thead>
                            <tbody id="predictionsTableBody">
                                <!-- 預測結果將在這裡動態生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 生成投手對戰顯示
function generatePitcherMatchupDisplay(pred) {
    if (!pred || !pred.pitcher_analysis) {
        return '<span class="text-muted">無投手信息</span>';
    }
    
    const home = pred.pitcher_analysis.home_pitcher || {};
    const away = pred.pitcher_analysis.away_pitcher || {};
    const matchupType = pred.pitcher_analysis.matchup_type || '普通對戰';
    
    // 根據投手等級設置顏色
    function getStrengthColor(strength) {
        switch(strength) {
            case '王牌': return 'success';
            case '優秀': return 'info';
            case '普通': return 'secondary';
            case '弱勢': return 'danger';
            default: return 'secondary';
        }
    }
    
    const homeColor = getStrengthColor(home.strength);
    const awayColor = getStrengthColor(away.strength);
    
    // 對戰類型顏色
    function getMatchupColor(type) {
        switch(type) {
            case '王牌對決': return 'success';
            case '打擊戰': return 'danger';
            case '強弱對戰': return 'warning';
            default: return 'secondary';
        }
    }
    
    const matchupColor = getMatchupColor(matchupType);
    
    return `
        <div class="d-flex flex-column">
            <div class="mb-1">
                <span class="badge bg-${awayColor}">${away.name || '未知'}</span>
                <small class="text-muted">(ERA: ${away.era ? away.era.toFixed(2) : 'N/A'})</small>
            </div>
            <div class="mb-1">
                <span class="badge bg-${homeColor}">${home.name || '未知'}</span>
                <small class="text-muted">(ERA: ${home.era ? home.era.toFixed(2) : 'N/A'})</small>
            </div>
            <div>
                <span class="badge bg-${matchupColor}">${matchupType}</span>
            </div>
        </div>
    `;
}

// 設置日期快捷按鈕
function setDate(daysOffset) {
    const today = new Date();
    const targetDate = new Date(today.getTime() + daysOffset * 24 * 60 * 60 * 1000);
    const dateString = targetDate.toISOString().split('T')[0];
    document.getElementById('targetDate').value = dateString;
}

// 生成預測
function generatePredictions() {
    // 獲取選擇的日期
    const targetDate = document.getElementById('targetDate').value;
    if (!targetDate) {
        showError('請選擇日期');
        return;
    }
    
    // 顯示載入指示器
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('predictionResults').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
    document.getElementById('generateBtn').disabled = true;
    
    // 構建請求URL
    const excludePostponed = document.getElementById('excludePostponed').checked;
    const url = `/fast/api/predict/date/${targetDate}?exclude_postponed=${excludePostponed}`;
    
    // 發送請求
    fetch(url)
        .then(response => response.json())
        .then(data => {
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('generateBtn').disabled = false;
            
            if (data.success) {
                displayPredictions(data);
            } else {
                showError(data.error || '預測生成失敗');
            }
        })
        .catch(error => {
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('generateBtn').disabled = false;
            showError('網絡錯誤: ' + error.message);
        });
}

// 顯示預測結果
function displayPredictions(data) {
    const tableBody = document.getElementById('predictionsTableBody');
    tableBody.innerHTML = '';
    
    // 更新計數和耗時
    document.getElementById('predictionCount').textContent = `${data.count}場比賽`;
    document.getElementById('predictionTime').textContent = `耗時: ${data.elapsed_time}秒`;
    
    // 顯示結果區域
    document.getElementById('predictionResults').style.display = 'block';
    
    // 如果沒有預測結果
    if (!data.predictions || data.predictions.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-3">
                    <i class="fas fa-info-circle text-info"></i> 沒有找到可預測的比賽
                </td>
            </tr>
        `;
        return;
    }
    
    // 添加每個預測結果
    data.predictions.forEach(pred => {
        const row = document.createElement('tr');
        
        // 比賽信息
        const gameInfo = `
            <div>
                <strong>${pred.away_team} @ ${pred.home_team}</strong>
            </div>
            <div class="small text-muted">
                ${pred.date} ${pred.time || ''}
            </div>
        `;
        
        // 比賽狀態
        let statusClass = 'secondary';
        if (pred.game_status === 'completed') statusClass = 'success';
        else if (pred.game_status === 'in_progress') statusClass = 'primary';
        else if (['postponed', 'cancelled', 'rescheduled'].includes(pred.game_status)) statusClass = 'warning';
        
        const gameStatus = `<span class="badge bg-${statusClass}">${pred.game_status || 'unknown'}</span>`;
        
        // 預測比分
        const predictedScore = `
            <div>
                <span class="badge bg-primary">
                    ${pred.predicted_away_score.toFixed(1)} - ${pred.predicted_home_score.toFixed(1)}
                </span>
            </div>
            <div class="small text-muted">
                總分: ${pred.total_runs.toFixed(1)}
            </div>
        `;
        
        // 投手對戰
        const pitcherMatchup = generatePitcherMatchupDisplay(pred);
        
        // 信心度
        const confidence = `
            <div class="progress" style="height: 20px;">
                <div class="progress-bar" role="progressbar"
                     style="width: ${(pred.confidence || 0) * 100}%"
                     aria-valuenow="${(pred.confidence || 0) * 100}"
                     aria-valuemin="0" aria-valuemax="100">
                    ${((pred.confidence || 0) * 100).toFixed(1)}%
                </div>
            </div>
        `;
        
        // 策略
        const strategy = pred.strategy ? `
            <div>
                <span class="badge bg-info">${pred.strategy.name}</span>
            </div>
            <div class="small text-muted">
                ${pred.strategy.description}
            </div>
        ` : '<span class="text-muted">無策略信息</span>';
        
        // 實際結果
        const actualResult = (pred.actual_home_score !== undefined && pred.actual_away_score !== undefined) ?
            `<span class="badge bg-success">${pred.actual_away_score} - ${pred.actual_home_score}</span>` :
            '<span class="text-muted">未完成</span>';
        
        // 預測耗時
        const predictionTime = `<span class="badge bg-secondary">${pred.prediction_time}秒</span>`;
        
        // 設置行內容
        row.innerHTML = `
            <td>${gameInfo}</td>
            <td>${gameStatus}</td>
            <td>${predictedScore}</td>
            <td>${pitcherMatchup}</td>
            <td>${confidence}</td>
            <td>${strategy}</td>
            <td>${actualResult}</td>
            <td>${predictionTime}</td>
        `;
        
        tableBody.appendChild(row);
    });
}

// 顯示錯誤
function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorAlert').style.display = 'block';
}

// 頁面載入時設置今天日期
document.addEventListener('DOMContentLoaded', function() {
    setDate(0);
});
</script>
{% endblock %}
