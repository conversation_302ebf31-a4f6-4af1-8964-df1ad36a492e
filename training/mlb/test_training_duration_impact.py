#!/usr/bin/env python3
"""
測試不同訓練時長對預測準確性的影響
"""

import os
import sys
from datetime import date, timedelta
import pandas as pd
import logging
from typing import Dict, List

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db
from models.model_trainer import ModelTrainer
from models.prediction_service import PredictionService

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TrainingDurationTester:
    """訓練時長影響測試器"""
    
    def __init__(self):
        self.app = create_app()
        self.trainer = ModelTrainer()
        self.prediction_service = PredictionService()
        self.test_results = []
    
    def test_duration_impact(self, test_periods: List[int] = None) -> Dict:
        """測試不同訓練時長的影響"""
        if test_periods is None:
            test_periods = [30, 45, 60, 75, 90, 120]
        
        logger.info("🧪 開始測試不同訓練時長的影響...")
        logger.info(f"測試時長: {test_periods} 天")
        
        with self.app.app_context():
            results = {}
            
            for days in test_periods:
                logger.info(f"\n📊 測試 {days} 天訓練時長...")
                
                try:
                    # 訓練模型
                    training_result = self._train_with_duration(days)
                    
                    # 測試預測準確性
                    accuracy_result = self._test_prediction_accuracy(days)
                    
                    # 合併結果
                    results[days] = {
                        'training_samples': training_result.get('training_samples', 0),
                        'features_count': training_result.get('features_count', 0),
                        'training_time': training_result.get('training_time', 0),
                        'accuracy': accuracy_result.get('accuracy', 0),
                        'total_predictions': accuracy_result.get('total_predictions', 0),
                        'correct_predictions': accuracy_result.get('correct_predictions', 0),
                        'average_confidence': accuracy_result.get('average_confidence', 0),
                        'score_mae': accuracy_result.get('score_mae', 0)
                    }
                    
                    logger.info(f"✅ {days}天: {accuracy_result.get('accuracy', 0):.1%} 準確率 "
                              f"({accuracy_result.get('correct_predictions', 0)}/{accuracy_result.get('total_predictions', 0)})")
                    
                except Exception as e:
                    logger.error(f"❌ {days}天測試失敗: {e}")
                    results[days] = {'error': str(e)}
            
            # 分析結果
            analysis = self._analyze_results(results)
            
            return {
                'test_results': results,
                'analysis': analysis,
                'recommendation': self._get_recommendation(results)
            }
    
    def _train_with_duration(self, days: int) -> Dict:
        """使用指定時長訓練模型"""
        try:
            import time
            start_time = time.time()
            
            # 設置訓練日期範圍
            end_date = date.today() - timedelta(days=1)
            start_date = end_date - timedelta(days=days)
            
            logger.info(f"訓練期間: {start_date} 到 {end_date}")
            
            # 執行訓練
            training_result = self.trainer.train_full_pipeline(
                start_date=start_date,
                end_date=end_date,
                save_models=True
            )
            
            training_time = time.time() - start_time
            training_result['training_time'] = training_time
            
            return training_result
            
        except Exception as e:
            logger.error(f"訓練失敗: {e}")
            raise
    
    def _test_prediction_accuracy(self, training_days: int) -> Dict:
        """測試預測準確性"""
        try:
            # 重新初始化預測服務以載入新模型
            self.prediction_service.initialize_service()
            
            # 獲取最近7天的比賽進行測試
            test_start = date.today() - timedelta(days=7)
            test_end = date.today() - timedelta(days=1)
            
            from models.database import Game
            test_games = Game.query.filter(
                Game.date >= test_start,
                Game.date <= test_end,
                Game.game_status == 'completed',
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            if not test_games:
                return {'error': '沒有測試數據'}
            
            logger.info(f"測試 {len(test_games)} 場比賽...")
            
            correct_predictions = 0
            total_predictions = 0
            confidence_sum = 0
            score_errors = []
            
            for game in test_games:
                try:
                    # 生成預測
                    prediction = self.prediction_service.generate_prediction(
                        home_team_id=game.home_team_id,
                        away_team_id=game.away_team_id,
                        game_date=game.date
                    )
                    
                    if prediction and 'prediction' in prediction:
                        pred_data = prediction['prediction']
                        
                        # 檢查勝負預測
                        predicted_home_wins = pred_data.get('home_score', 0) > pred_data.get('away_score', 0)
                        actual_home_wins = game.home_score > game.away_score
                        
                        if predicted_home_wins == actual_home_wins:
                            correct_predictions += 1
                        
                        total_predictions += 1
                        
                        # 累計信心度
                        confidence = pred_data.get('confidence', 0.5)
                        confidence_sum += confidence
                        
                        # 計算得分誤差
                        home_error = abs(pred_data.get('home_score', 0) - game.home_score)
                        away_error = abs(pred_data.get('away_score', 0) - game.away_score)
                        score_errors.extend([home_error, away_error])
                
                except Exception as e:
                    logger.warning(f"預測失敗 {game.id}: {e}")
                    continue
            
            if total_predictions == 0:
                return {'error': '沒有成功的預測'}
            
            accuracy = correct_predictions / total_predictions
            average_confidence = confidence_sum / total_predictions
            score_mae = sum(score_errors) / len(score_errors) if score_errors else 0
            
            return {
                'accuracy': accuracy,
                'total_predictions': total_predictions,
                'correct_predictions': correct_predictions,
                'average_confidence': average_confidence,
                'score_mae': score_mae
            }
            
        except Exception as e:
            logger.error(f"測試預測準確性失敗: {e}")
            return {'error': str(e)}
    
    def _analyze_results(self, results: Dict) -> Dict:
        """分析測試結果"""
        try:
            valid_results = {k: v for k, v in results.items() if 'error' not in v}
            
            if not valid_results:
                return {'error': '沒有有效的測試結果'}
            
            # 找出最佳準確率
            best_accuracy = 0
            best_days = 0
            
            for days, result in valid_results.items():
                accuracy = result.get('accuracy', 0)
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    best_days = days
            
            # 計算平均值
            accuracies = [r.get('accuracy', 0) for r in valid_results.values()]
            training_samples = [r.get('training_samples', 0) for r in valid_results.values()]
            
            analysis = {
                'best_duration': best_days,
                'best_accuracy': best_accuracy,
                'average_accuracy': sum(accuracies) / len(accuracies),
                'accuracy_range': max(accuracies) - min(accuracies),
                'sample_size_correlation': self._calculate_correlation(
                    list(valid_results.keys()), 
                    accuracies
                )
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析結果失敗: {e}")
            return {'error': str(e)}
    
    def _calculate_correlation(self, x_values: List, y_values: List) -> float:
        """計算相關係數"""
        try:
            import numpy as np
            correlation = np.corrcoef(x_values, y_values)[0, 1]
            return correlation if not np.isnan(correlation) else 0
        except:
            return 0
    
    def _get_recommendation(self, results: Dict) -> Dict:
        """獲取建議"""
        try:
            valid_results = {k: v for k, v in results.items() if 'error' not in v}
            
            if not valid_results:
                return {'message': '無法提供建議，沒有有效結果'}
            
            # 找出最佳時長
            best_days = max(valid_results.keys(), 
                          key=lambda k: valid_results[k].get('accuracy', 0))
            best_accuracy = valid_results[best_days].get('accuracy', 0)
            
            # 分析趨勢
            sorted_results = sorted(valid_results.items())
            
            recommendation = {
                'optimal_duration': best_days,
                'optimal_accuracy': best_accuracy,
                'current_vs_optimal': self._compare_with_current(valid_results),
                'trend_analysis': self._analyze_trend(sorted_results),
                'suggestions': self._generate_suggestions(valid_results)
            }
            
            return recommendation
            
        except Exception as e:
            logger.error(f"生成建議失敗: {e}")
            return {'error': str(e)}
    
    def _compare_with_current(self, results: Dict) -> Dict:
        """與當前60天設置比較"""
        current_60 = results.get(60, {})
        if 'error' in current_60:
            return {'message': '無法比較，60天測試失敗'}
        
        best_days = max(results.keys(), key=lambda k: results[k].get('accuracy', 0))
        best_result = results[best_days]
        
        improvement = best_result.get('accuracy', 0) - current_60.get('accuracy', 0)
        
        return {
            'current_60_days_accuracy': current_60.get('accuracy', 0),
            'best_accuracy': best_result.get('accuracy', 0),
            'improvement_potential': improvement,
            'recommendation': f"從60天改為{best_days}天可提升{improvement:.1%}"
        }
    
    def _analyze_trend(self, sorted_results: List) -> str:
        """分析準確率趨勢"""
        accuracies = [result[1].get('accuracy', 0) for result in sorted_results]
        
        if len(accuracies) < 3:
            return "數據不足以分析趨勢"
        
        # 簡單趨勢分析
        early_avg = sum(accuracies[:len(accuracies)//2]) / (len(accuracies)//2)
        late_avg = sum(accuracies[len(accuracies)//2:]) / (len(accuracies) - len(accuracies)//2)
        
        if late_avg > early_avg + 0.02:
            return "準確率隨訓練時長增加而提升"
        elif early_avg > late_avg + 0.02:
            return "準確率隨訓練時長增加而下降"
        else:
            return "準確率與訓練時長無明顯相關性"
    
    def _generate_suggestions(self, results: Dict) -> List[str]:
        """生成具體建議"""
        suggestions = []
        
        # 基於結果生成建議
        best_days = max(results.keys(), key=lambda k: results[k].get('accuracy', 0))
        best_accuracy = results[best_days].get('accuracy', 0)
        
        if best_accuracy > 0.65:
            suggestions.append(f"✅ 使用{best_days}天訓練可達到{best_accuracy:.1%}準確率")
        elif best_accuracy > 0.55:
            suggestions.append(f"⚠️ 最佳{best_days}天訓練準確率為{best_accuracy:.1%}，建議結合其他優化")
        else:
            suggestions.append(f"❌ 所有測試準確率都較低，建議檢查數據質量和特徵工程")
        
        # 季節性建議
        current_month = date.today().month
        if current_month in [4, 5]:  # 賽季初期
            suggestions.append("🌱 賽季初期建議使用90-120天包含更多歷史數據")
        elif current_month in [9, 10]:  # 賽季後期
            suggestions.append("🏁 賽季後期建議使用30-45天重點關注近期狀態")
        
        return suggestions

def main():
    """主函數"""
    print("🧪 MLB預測系統 - 訓練時長影響測試")
    print("=" * 60)
    
    tester = TrainingDurationTester()
    
    # 執行測試
    results = tester.test_duration_impact()
    
    # 顯示結果
    print("\n📊 測試結果摘要:")
    print("-" * 40)
    
    if 'test_results' in results:
        for days, result in results['test_results'].items():
            if 'error' not in result:
                accuracy = result.get('accuracy', 0)
                samples = result.get('training_samples', 0)
                print(f"{days:3d}天: {accuracy:6.1%} 準確率 ({samples:4d} 訓練樣本)")
            else:
                print(f"{days:3d}天: ❌ {result['error']}")
    
    # 顯示分析
    if 'analysis' in results and 'error' not in results['analysis']:
        analysis = results['analysis']
        print(f"\n🎯 最佳設置: {analysis.get('best_duration')}天 "
              f"({analysis.get('best_accuracy', 0):.1%} 準確率)")
        print(f"📈 平均準確率: {analysis.get('average_accuracy', 0):.1%}")
        print(f"📊 準確率範圍: {analysis.get('accuracy_range', 0):.1%}")
    
    # 顯示建議
    if 'recommendation' in results and 'error' not in results['recommendation']:
        rec = results['recommendation']
        print(f"\n💡 建議:")
        for suggestion in rec.get('suggestions', []):
            print(f"   {suggestion}")
    
    print("\n✅ 測試完成！")

if __name__ == "__main__":
    main()
