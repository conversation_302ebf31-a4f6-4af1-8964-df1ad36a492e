#!/usr/bin/env python3
"""
測試優化預測器 - 驗證混合訓練策略
"""

import sys
import os
from datetime import date, timedelta

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game
from models.optimized_predictor import OptimizedMLBPredictor

def test_optimized_predictor():
    """測試優化預測器"""
    print("🚀 測試優化預測器 - 混合訓練策略")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        predictor = OptimizedMLBPredictor()
        
        # 1. 檢查模型狀態
        print("📊 初始模型狀態:")
        status = predictor.get_model_status()
        print(f"   勝負模型已訓練: {'✅' if status['win_model_trained'] else '❌'}")
        print(f"   得分模型已訓練: {'✅' if status['score_models_trained'] else '❌'}")
        print(f"   訓練策略: {status['training_strategies']}")
        
        # 2. 訓練模型 (使用6/21作為目標日期，這樣可以用6/20及之前的數據)
        target_date = date(2025, 6, 21)
        print(f"\n🔧 開始訓練模型 (目標日期: {target_date})")
        print(f"   勝負預測: 使用 {target_date - timedelta(days=13)} 到 {target_date - timedelta(days=1)} (14天)")
        print(f"   得分預測: 使用 {target_date - timedelta(days=89)} 到 {target_date - timedelta(days=1)} (90天)")
        
        training_result = predictor.train_models(target_date)
        
        if training_result['success']:
            print("✅ 模型訓練成功!")
            
            stats = training_result['training_stats']
            print(f"\n📈 訓練統計:")
            print(f"   勝負模型訓練場次: {stats['win_training_games']}")
            print(f"   得分模型訓練場次: {stats['score_training_games']}")
            print(f"   預估勝負準確率: {stats['win_accuracy_estimate']:.1f}%")
            print(f"   預估得分MAE: 主隊 {stats['score_mae_estimate']['home_mae']:.2f}, "
                  f"客隊 {stats['score_mae_estimate']['away_mae']:.2f}")
        else:
            print(f"❌ 模型訓練失敗: {training_result.get('error', '未知錯誤')}")
            return
        
        # 3. 測試預測功能
        print(f"\n🎯 測試預測功能:")
        
        # 獲取6/21的實際比賽進行測試
        test_games = Game.query.filter_by(date=target_date).limit(5).all()
        
        if not test_games:
            print(f"❌ {target_date} 沒有比賽數據")
            return
        
        print(f"   測試 {len(test_games)} 場比賽:")
        
        successful_predictions = 0
        total_predictions = 0
        
        for i, game in enumerate(test_games, 1):
            print(f"\n   [{i}] {game.away_team} @ {game.home_team}")
            
            # 進行預測
            prediction = predictor.predict_game(
                game.home_team, game.away_team, game.date
            )
            
            total_predictions += 1
            
            if prediction['success']:
                successful_predictions += 1
                pred = prediction['predictions']
                
                print(f"       預測得分: {game.away_team} {pred['away_score']} - "
                      f"{pred['home_score']} {game.home_team}")
                print(f"       勝率: {game.home_team} {pred['home_win_probability']:.1%}, "
                      f"{game.away_team} {pred['away_win_probability']:.1%}")
                print(f"       預測勝者: {pred['predicted_winner']}")
                print(f"       信心等級: {pred['confidence_level']}")
                
                # 如果有實際結果，進行比較
                if (game.game_status == 'completed' and
                    game.home_score is not None and
                    game.away_score is not None):
                    actual_winner = game.home_team if game.home_score > game.away_score else game.away_team
                    prediction_correct = pred['predicted_winner'] == actual_winner

                    print(f"       實際得分: {game.away_team} {game.away_score} - "
                          f"{game.home_score} {game.home_team}")
                    print(f"       實際勝者: {actual_winner}")
                    print(f"       預測結果: {'✅ 正確' if prediction_correct else '❌ 錯誤'}")

                    # 計算得分誤差
                    home_error = abs(pred['home_score'] - game.home_score)
                    away_error = abs(pred['away_score'] - game.away_score)
                    print(f"       得分誤差: 主隊 {home_error:.1f}, 客隊 {away_error:.1f}")
                else:
                    print(f"       實際結果: 比賽未完成或數據缺失")
                
            else:
                print(f"       ❌ 預測失敗: {prediction.get('error', '未知錯誤')}")
        
        # 4. 總結
        print(f"\n📊 測試總結:")
        print(f"   成功預測: {successful_predictions}/{total_predictions}")
        print(f"   成功率: {successful_predictions/total_predictions*100:.1f}%")
        
        # 5. 檢查最終模型狀態
        final_status = predictor.get_model_status()
        print(f"\n🔍 最終模型狀態:")
        print(f"   勝負模型: {'✅ 已訓練' if final_status['win_model_trained'] else '❌ 未訓練'}")
        print(f"   得分模型: {'✅ 已訓練' if final_status['score_models_trained'] else '❌ 未訓練'}")
        
        last_training = final_status['last_training_dates']
        if last_training['win_model']:
            print(f"   最後訓練日期: {last_training['win_model']}")

def test_different_dates():
    """測試不同日期的預測"""
    print(f"\n🗓️  測試不同日期的預測能力:")
    
    app = create_app()
    
    with app.app_context():
        predictor = OptimizedMLBPredictor()
        
        # 先訓練模型
        predictor.train_models(date(2025, 6, 25))
        
        # 測試不同日期
        test_dates = [
            date(2025, 6, 25),
            date(2025, 6, 26),
            date(2025, 6, 27)
        ]
        
        for test_date in test_dates:
            print(f"\n   📅 測試日期: {test_date}")
            
            games = Game.query.filter_by(date=test_date).limit(3).all()
            
            for game in games:
                prediction = predictor.predict_game(
                    game.home_team, game.away_team, game.date
                )
                
                if prediction['success']:
                    pred = prediction['predictions']
                    print(f"     {game.away_team} @ {game.home_team}: "
                          f"{pred['away_score']}-{pred['home_score']}, "
                          f"勝者: {pred['predicted_winner']} ({pred['confidence_level']})")

if __name__ == "__main__":
    test_optimized_predictor()
    test_different_dates()
