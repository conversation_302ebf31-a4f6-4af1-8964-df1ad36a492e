#!/usr/bin/env python3
"""
測試預測生成功能
"""

from datetime import date
from app import create_app
from models.automated_predictor import AutomatedPredictor

def test_prediction_generation():
    """測試預測生成功能"""
    print("🎯 測試預測生成功能...")
    
    app = create_app()
    with app.app_context():
        try:
            # 創建自動化預測器
            predictor = AutomatedPredictor()
            
            # 測試手動生成預測
            target_date = date(2025, 6, 25)
            print(f"📅 生成 {target_date} 的預測...")
            
            result = predictor.manual_generate_predictions(target_date)
            
            if result and 'error' not in result:
                print("✅ 預測生成成功！")
                print(f"📊 總比賽數: {result.get('total_games', 0)}")
                print(f"📊 成功預測: {result.get('successful_predictions', 0)}")
                
                # 顯示前幾個預測
                predictions = result.get('predictions', [])
                if predictions:
                    print("\n🏟️ 預測結果樣本:")
                    for i, pred in enumerate(predictions[:3]):
                        print(f"   {i+1}. {pred['away_team']} @ {pred['home_team']}")
                        print(f"      預測比分: {pred['predicted_away_score']}-{pred['predicted_home_score']}")
                        print(f"      主隊勝率: {pred['home_win_probability']:.3f}")
                        print(f"      信心度: {pred['confidence']:.3f}")
                        print()
                else:
                    print("⚠️ 沒有生成預測結果")
                    
            else:
                print(f"❌ 預測生成失敗: {result.get('error', '未知錯誤')}")
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_prediction_generation()
