/**
 * 增強預測系統 JavaScript
 * 整合精細投手分析到現有界面
 */

class EnhancedPredictionSystem {
    constructor() {
        this.apiEndpoint = '/predictions/api/';
        this.enhancedEndpoint = '/predictions/enhanced/api/';
    }

    /**
     * 獲取增強預測結果
     */
    async getEnhancedPrediction(gameId, targetDate = null) {
        try {
            console.log(`🎯 獲取增強預測: ${gameId}`);
            
            const url = targetDate 
                ? `${this.enhancedEndpoint}predict/${gameId}?target_date=${targetDate}`
                : `${this.enhancedEndpoint}predict/${gameId}`;
            
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    target_date: targetDate,
                    prediction_type: 'comprehensive'
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                console.log('✅ 增強預測成功');
                return result;
            } else {
                console.error('❌ 增強預測失敗:', result.error);
                // 回退到原始API
                return await this.getFallbackPrediction(gameId);
            }
        } catch (error) {
            console.error('增強預測請求失敗:', error);
            // 回退到原始API
            return await this.getFallbackPrediction(gameId);
        }
    }

    /**
     * 回退到原始預測API
     */
    async getFallbackPrediction(gameId) {
        try {
            const response = await fetch(`${this.apiEndpoint}${gameId}`);
            const result = await response.json();
            
            // 標記為基礎預測
            result.enhanced = false;
            result.analysis_type = 'basic_prediction';
            
            return result;
        } catch (error) {
            console.error('回退預測也失敗:', error);
            return {
                success: false,
                error: '預測系統暫時無法使用'
            };
        }
    }

    /**
     * 更新界面顯示
     */
    updatePredictionDisplay(result, containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('找不到容器元素:', containerId);
            return;
        }

        if (!result.success) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <strong>預測失敗:</strong> ${result.error}
                </div>
            `;
            return;
        }

        // 根據是否為增強預測顯示不同界面
        if (result.enhanced) {
            this.renderEnhancedPrediction(result, container);
        } else {
            this.renderBasicPrediction(result, container);
        }
    }

    /**
     * 渲染增強預測結果
     */
    renderEnhancedPrediction(result, container) {
        const prediction = result.prediction || {};
        const pitcherAnalysis = result.pitcher_analysis || {};
        const confidence = result.confidence_details || result.confidence || {};
        const formattedDisplay = result.formatted_display || {};

        const html = `
            <div class="enhanced-prediction">
                <!-- 預測結果卡片 -->
                <div class="prediction-card enhanced">
                    <div class="card-header">
                        <h4>🎯 增強預測結果</h4>
                        <span class="badge badge-success">精細投手分析</span>
                    </div>
                    
                    <div class="prediction-score">
                        <div class="score-display">
                            <span class="away-score">${prediction.away_predicted_runs?.toFixed(1) || '0.0'}</span>
                            <span class="vs">-</span>
                            <span class="home-score">${prediction.home_predicted_runs?.toFixed(1) || '0.0'}</span>
                        </div>
                        <div class="total-score">
                            總得分: ${prediction.predicted_total_runs?.toFixed(1) || '0.0'}
                        </div>
                    </div>
                </div>

                <!-- 投手分析 -->
                <div class="pitcher-analysis-card">
                    <h5>⚾ 投手對戰分析</h5>
                    <div class="pitcher-info">
                        <div class="pitcher away-pitcher">
                            <strong>客隊投手:</strong> 
                            <span class="pitcher-name">${pitcherAnalysis.away_pitcher?.name || '未確認'}</span>
                            ${this.renderPitcherAdvantage(pitcherAnalysis, 'away')}
                        </div>
                        <div class="pitcher home-pitcher">
                            <strong>主隊投手:</strong> 
                            <span class="pitcher-name">${pitcherAnalysis.home_pitcher?.name || '未確認'}</span>
                            ${this.renderPitcherAdvantage(pitcherAnalysis, 'home')}
                        </div>
                    </div>
                    
                    <div class="matchup-advantage">
                        <strong>對戰優勢:</strong> 
                        ${this.getMatchupAdvantageText(pitcherAnalysis.matchup_advantage)}
                    </div>
                </div>

                <!-- 信心度指標 -->
                <div class="confidence-indicator">
                    <div class="confidence-level ${confidence.confidence_level || 'medium'}">
                        <strong>預測信心度:</strong> 
                        <span class="level-text">${this.getConfidenceLevelText(confidence.confidence_level)}</span>
                        <span class="confidence-score">${((confidence.overall_confidence || 0.5) * 100).toFixed(0)}%</span>
                    </div>
                    
                    <div class="confidence-factors">
                        ${this.renderConfidenceFactors(confidence)}
                    </div>
                </div>

                <!-- 關鍵洞察 -->
                ${this.renderKeyInsights(result)}

                <!-- 系統信息 -->
                <div class="system-info">
                    <small class="text-muted">
                        預測策略: ${result.strategy || 'enhanced_historical'} | 
                        生成時間: ${new Date(result.generated_at || Date.now()).toLocaleString()}
                        ${result.analysis_type === 'enhanced_with_pitcher_analysis' ? ' | ⚡ 已升級預測' : ''}
                    </small>
                </div>
            </div>

            <style>
                .enhanced-prediction {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }
                
                .prediction-card.enhanced {
                    border: 2px solid #28a745;
                    border-radius: 8px;
                    margin-bottom: 20px;
                }
                
                .card-header {
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    padding: 15px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .prediction-score {
                    padding: 20px;
                    text-align: center;
                }
                
                .score-display {
                    font-size: 2.5em;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
                
                .away-score { color: #007bff; }
                .home-score { color: #dc3545; }
                .vs { margin: 0 15px; color: #6c757d; }
                
                .total-score {
                    font-size: 1.2em;
                    color: #6f42c1;
                    font-weight: 600;
                }
                
                .pitcher-analysis-card {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 15px;
                }
                
                .pitcher-info {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 15px;
                    margin-bottom: 15px;
                }
                
                .pitcher {
                    padding: 10px;
                    border-radius: 5px;
                    background: white;
                }
                
                .pitcher-name {
                    color: #495057;
                    font-weight: 600;
                }
                
                .matchup-advantage {
                    text-align: center;
                    font-weight: 600;
                    padding: 10px;
                    border-radius: 5px;
                    background: white;
                }
                
                .confidence-indicator {
                    background: #e9ecef;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 15px;
                }
                
                .confidence-level.high { color: #28a745; }
                .confidence-level.medium { color: #ffc107; }
                .confidence-level.low { color: #dc3545; }
                
                .confidence-score {
                    font-weight: bold;
                    font-size: 1.1em;
                }
                
                .key-insights {
                    background: #fff3cd;
                    border-left: 4px solid #ffc107;
                    padding: 15px;
                    margin-bottom: 15px;
                }
                
                .insight-item {
                    margin-bottom: 8px;
                }
                
                .system-info {
                    text-align: center;
                    margin-top: 15px;
                    padding-top: 15px;
                    border-top: 1px solid #dee2e6;
                }
            </style>
        `;

        container.innerHTML = html;
    }

    /**
     * 渲染基礎預測結果
     */
    renderBasicPrediction(result, container) {
        const html = `
            <div class="basic-prediction">
                <div class="prediction-card basic">
                    <div class="card-header">
                        <h4>📊 基礎預測</h4>
                        <span class="badge badge-secondary">團隊統計分析</span>
                    </div>
                    
                    <div class="prediction-score">
                        <div class="score-display">
                            <span class="away-score">${result.predicted_away_score?.toFixed(1) || '0.0'}</span>
                            <span class="vs">-</span>
                            <span class="home-score">${result.predicted_home_score?.toFixed(1) || '0.0'}</span>
                        </div>
                    </div>

                    <div class="basic-info">
                        <p><strong>投手信息:</strong> ${result.starting_pitcher_away || '未確認'} vs ${result.starting_pitcher_home || '未確認'}</p>
                        <p><strong>預測方法:</strong> 基於球隊平均表現</p>
                    </div>

                    <div class="upgrade-notice">
                        <div class="alert alert-info">
                            <strong>💡 提示:</strong> 啟用精細投手分析可獲得更準確的預測結果
                            <button onclick="upgradeToEnhanced('${result.game_id}')" class="btn btn-sm btn-primary ml-2">
                                升級預測
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    /**
     * 渲染投手優勢
     */
    renderPitcherAdvantage(pitcherAnalysis, pitcherType) {
        const analysis = pitcherAnalysis[`${pitcherType}_pitcher`]?.analysis;
        if (!analysis) return '';

        const confidence = analysis.confidence_level || 'low';
        const confidenceIcon = {
            'high': '🟢',
            'medium': '🟡', 
            'low': '🔴'
        }[confidence];

        return `
            <div class="pitcher-advantage">
                <span class="confidence-icon">${confidenceIcon}</span>
                <span class="confidence-text">${confidence.toUpperCase()}</span>
            </div>
        `;
    }

    /**
     * 獲取對戰優勢文本
     */
    getMatchupAdvantageText(advantage) {
        const advantageMap = {
            'home_pitcher': '🏠 主隊投手優勢',
            'away_pitcher': '✈️ 客隊投手優勢', 
            'neutral': '⚖️ 勢均力敵',
            'home': '🏠 主隊優勢',
            'away': '✈️ 客隊優勢'
        };
        return advantageMap[advantage] || '⚖️ 勢均力敵';
    }

    /**
     * 獲取信心度級別文本
     */
    getConfidenceLevelText(level) {
        const levelMap = {
            'high': '高 🎯',
            'medium': '中 📊',
            'low': '低 ⚠️'
        };
        return levelMap[level] || '中 📊';
    }

    /**
     * 渲染信心度因子
     */
    renderConfidenceFactors(confidence) {
        const factors = confidence.confidence_factors || {};
        if (Object.keys(factors).length === 0) return '';

        return `
            <div class="confidence-breakdown">
                <strong>信心度組成:</strong>
                <ul>
                    ${Object.entries(factors).map(([key, value]) => 
                        `<li>${this.getFactorName(key)}: ${(value * 100).toFixed(0)}%</li>`
                    ).join('')}
                </ul>
            </div>
        `;
    }

    /**
     * 渲染關鍵洞察
     */
    renderKeyInsights(result) {
        const analysisDetails = result.analysis_details || {};
        const pitchingAnalysis = analysisDetails.pitching_analysis || {};
        const insights = pitchingAnalysis.key_insights || [];

        if (insights.length === 0) return '';

        return `
            <div class="key-insights">
                <h5>💡 關鍵洞察</h5>
                ${insights.map(insight => 
                    `<div class="insight-item">• ${insight}</div>`
                ).join('')}
            </div>
        `;
    }

    /**
     * 獲取因子名稱
     */
    getFactorName(key) {
        const nameMap = {
            'data_completeness': '數據完整性',
            'recent_form_reliability': '近期表現可靠性',
            'prediction_reasonableness': '預測合理性'
        };
        return nameMap[key] || key;
    }
}

// 全局升級函數
async function upgradeToEnhanced(gameId) {
    const enhancedSystem = new EnhancedPredictionSystem();
    const result = await enhancedSystem.getEnhancedPrediction(gameId);
    enhancedSystem.updatePredictionDisplay(result, 'prediction-container');
}

// 全局實例
window.enhancedPrediction = new EnhancedPredictionSystem();

// 自動升級現有預測
document.addEventListener('DOMContentLoaded', function() {
    // 檢查是否有需要升級的預測
    const predictionContainers = document.querySelectorAll('[data-game-id]');
    predictionContainers.forEach(async (container) => {
        const gameId = container.dataset.gameId;
        if (gameId) {
            const result = await window.enhancedPrediction.getEnhancedPrediction(gameId);
            window.enhancedPrediction.updatePredictionDisplay(result, container.id);
        }
    });
});