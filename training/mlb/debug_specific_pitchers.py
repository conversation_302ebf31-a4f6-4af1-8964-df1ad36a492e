#!/usr/bin/env python3
"""
調試特定投手的匹配問題
"""

import logging
from app import create_app
from models.database import db, Game, GameDetail, PlayerStats

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_specific_pitchers():
    """調試特定投手"""
    app = create_app()
    
    with app.app_context():
        print("=" * 80)
        print("🔍 特定投手調試")
        print("=" * 80)
        
        # 測試失敗的投手
        test_pitchers = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        
        for pitcher_name in test_pitchers:
            print(f"\n⚾ 調試投手: '{pitcher_name}'")
            print("-" * 50)
            
            # 1. 檢查是否存在於PlayerStats中
            all_seasons_match = PlayerStats.query.filter(
                PlayerStats.player_name == pitcher_name
            ).all()
            
            if all_seasons_match:
                print(f"✅ 在PlayerStats中找到 {len(all_seasons_match)} 條記錄:")
                for match in all_seasons_match:
                    print(f"  - {match.season}賽季: ERA {match.era:.2f}, IP {match.innings_pitched:.1f}")
            else:
                print(f"❌ 在PlayerStats中未找到精確匹配")
            
            # 2. 模糊搜索
            name_parts = pitcher_name.split()
            if len(name_parts) >= 2:
                first_name = name_parts[0]
                last_name = name_parts[-1]
                
                fuzzy_matches = PlayerStats.query.filter(
                    PlayerStats.player_name.like(f'%{first_name}%'),
                    PlayerStats.player_name.like(f'%{last_name}%')
                ).all()
                
                if fuzzy_matches:
                    print(f"🔍 模糊匹配結果 ({first_name} + {last_name}):")
                    for match in fuzzy_matches:
                        print(f"  - '{match.player_name}' ({match.season}賽季)")
                else:
                    print(f"❌ 模糊匹配也無結果")
            
            # 3. 檢查該投手出現在哪些比賽中
            games_as_home = db.session.query(Game, GameDetail).join(
                GameDetail, Game.game_id == GameDetail.game_id
            ).filter(
                GameDetail.home_starting_pitcher == pitcher_name
            ).all()
            
            games_as_away = db.session.query(Game, GameDetail).join(
                GameDetail, Game.game_id == GameDetail.game_id
            ).filter(
                GameDetail.away_starting_pitcher == pitcher_name
            ).all()
            
            total_games = len(games_as_home) + len(games_as_away)
            
            if total_games > 0:
                print(f"📅 該投手出現在 {total_games} 場比賽中:")
                
                # 顯示比賽年份分布
                years = set()
                for game, detail in games_as_home + games_as_away:
                    years.add(game.date.year)
                
                print(f"  比賽年份: {sorted(years)}")
                
                # 顯示幾場比賽樣本
                sample_games = (games_as_home + games_as_away)[:3]
                for game, detail in sample_games:
                    print(f"  - {game.date}: {game.away_team} @ {game.home_team}")
            else:
                print(f"❌ 該投手未出現在任何比賽中")
        
        # 4. 檢查PlayerStats的整體情況
        print(f"\n📊 PlayerStats整體情況:")
        print("-" * 50)
        
        # 按賽季統計投手數量
        season_counts = db.session.query(
            PlayerStats.season,
            db.func.count(PlayerStats.id)
        ).filter(
            PlayerStats.innings_pitched > 0
        ).group_by(PlayerStats.season).order_by(PlayerStats.season.desc()).all()
        
        print("各賽季投手統計數量:")
        for season, count in season_counts:
            print(f"  {season}賽季: {count}名投手")
        
        # 5. 檢查是否有姓名格式問題
        print(f"\n🔤 姓名格式檢查:")
        print("-" * 50)
        
        # 檢查包含特殊字符的姓名
        special_names = PlayerStats.query.filter(
            db.or_(
                PlayerStats.player_name.like('%.%'),
                PlayerStats.player_name.like('%Jr%'),
                PlayerStats.player_name.like('%Sr%'),
                PlayerStats.player_name.like('%III%'),
                PlayerStats.player_name.like('%II%')
            ),
            PlayerStats.innings_pitched > 0
        ).limit(10).all()
        
        if special_names:
            print("包含特殊格式的投手姓名:")
            for stats in special_names:
                print(f"  '{stats.player_name}'")
        
        # 6. 檢查是否有重複的球員但不同格式
        print(f"\n🔄 重複球員檢查:")
        print("-" * 50)
        
        # 查找可能的重複（相同姓氏）
        common_last_names = ['Pineda', 'Musgrove', 'Pivetta', 'Turnbull']
        
        for last_name in common_last_names:
            similar_names = PlayerStats.query.filter(
                PlayerStats.player_name.like(f'%{last_name}%'),
                PlayerStats.innings_pitched > 0
            ).all()
            
            if similar_names:
                print(f"包含 '{last_name}' 的投手:")
                for stats in similar_names:
                    print(f"  '{stats.player_name}' ({stats.season}賽季)")
        
        print("\n" + "=" * 80)

def main():
    """主函數"""
    debug_specific_pitchers()

if __name__ == "__main__":
    main()
