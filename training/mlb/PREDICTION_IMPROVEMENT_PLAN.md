# MLB預測系統改進計劃
## 目標：將命中率從50%提升至65%+

## 🔍 當前問題分析

### 1. 數據維度不足
- **缺少投手對戰數據**：先發投手vs對手打線的歷史表現
- **缺少天氣因素**：風向、溫度、濕度對比賽的影響
- **缺少傷兵影響**：關鍵球員受傷對球隊實力的影響
- **缺少疲勞因素**：連續比賽、旅行疲勞、休息天數

### 2. 特徵工程不夠深入
- **時間序列特徵**：球隊狀態的動態變化
- **對戰優勢**：特定球隊間的剋制關係
- **情境因素**：季後賽壓力、排名競爭等

### 3. 模型複雜度不足
- 當前使用基礎ML模型，可能無法捕捉複雜的非線性關係

## 🎯 改進方案

### 階段一：增強數據收集 (預期提升5-8%)

#### 1.1 投手對戰數據
```python
# 新增數據表
class PitcherBatterMatchup:
    pitcher_id: int
    batter_id: int
    at_bats: int
    hits: int
    home_runs: int
    strikeouts: int
    walks: int
    avg: float
    ops: float
```

#### 1.2 天氣數據整合
```python
class GameWeather:
    game_id: str
    temperature: float
    humidity: float
    wind_speed: float
    wind_direction: str
    weather_condition: str
    is_dome: bool
```

#### 1.3 傷兵報告影響評估
```python
class InjuryImpact:
    team_id: int
    date: date
    injured_players: List[str]
    impact_score: float  # 0-10評分
    key_positions_affected: List[str]
```

### 階段二：高級特徵工程 (預期提升8-12%)

#### 2.1 動態實力評估
- **ELO評分系統**：根據比賽結果動態調整球隊實力
- **近期狀態權重**：最近5場比賽權重更高
- **主客場分離評估**：分別計算主客場實力

#### 2.2 投手疲勞模型
```python
def calculate_pitcher_fatigue(pitcher_id, game_date):
    """計算投手疲勞度"""
    recent_games = get_pitcher_recent_games(pitcher_id, days=7)
    
    fatigue_score = 0
    for game in recent_games:
        days_ago = (game_date - game.date).days
        innings_pitched = game.innings_pitched
        
        # 疲勞累積公式
        fatigue_score += innings_pitched / (days_ago + 1)
    
    return min(fatigue_score, 10)  # 0-10評分
```

#### 2.3 對戰優勢矩陣
```python
def build_matchup_matrix():
    """建立球隊間對戰優勢矩陣"""
    # 分析過去3年的對戰記錄
    # 考慮投手類型vs打線風格
    # 建立30x30的優勢矩陣
```

### 階段三：模型架構升級 (預期提升5-10%)

#### 3.1 集成學習
```python
class EnsemblePredictor:
    def __init__(self):
        self.models = {
            'xgboost': XGBRegressor(),
            'lightgbm': LGBMRegressor(), 
            'catboost': CatBoostRegressor(),
            'neural_network': MLPRegressor(),
            'random_forest': RandomForestRegressor()
        }
        self.meta_model = LinearRegression()
```

#### 3.2 深度學習模型
```python
class DeepMLBPredictor(nn.Module):
    def __init__(self, input_size):
        super().__init__()
        self.layers = nn.Sequential(
            nn.Linear(input_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 3)  # home_score, away_score, win_prob
        )
```

### 階段四：實時數據整合 (預期提升3-5%)

#### 4.1 賽前最新消息
- **傷兵報告**：比賽日當天的最新傷兵狀況
- **先發陣容**：實際先發打線和投手
- **天氣預報**：比賽時間的精確天氣

#### 4.2 博彩市場數據
```python
class BettingOdds:
    game_id: str
    home_ml_odds: float
    away_ml_odds: float
    total_runs_line: float
    run_line: float
    market_confidence: float
```

## 📊 具體實施步驟

### 第1週：數據收集增強
1. 實施投手對戰數據收集
2. 整合天氣API
3. 建立傷兵影響評估系統

### 第2週：特徵工程升級
1. 實施ELO評分系統
2. 開發疲勞模型
3. 建立對戰優勢矩陣

### 第3週：模型架構改進
1. 實施集成學習
2. 開發深度學習模型
3. 模型融合和調優

### 第4週：實時數據整合
1. 賽前數據更新機制
2. 博彩市場數據整合
3. 預測信心度校準

## 🎯 預期成果

| 改進階段 | 預期命中率 | 主要改進點 |
|---------|-----------|-----------|
| 當前 | 50% | 基礎統計模型 |
| 階段一 | 55-58% | 數據維度擴展 |
| 階段二 | 63-70% | 高級特徵工程 |
| 階段三 | 68-75% | 模型架構升級 |
| 階段四 | 70-78% | 實時數據整合 |

## 🔧 技術實施重點

### 1. 數據質量保證
- 數據驗證和清洗流程
- 缺失值處理策略
- 異常值檢測和處理

### 2. 特徵選擇優化
- 特徵重要性分析
- 相關性檢測
- 維度降低技術

### 3. 模型驗證
- 時間序列交叉驗證
- 滾動窗口驗證
- 預測穩定性測試

## 📈 監控指標

### 核心指標
- **勝負預測準確率**：目標65%+
- **分差預測MAE**：目標<2.5分
- **總分預測MAE**：目標<1.8分
- **高信心預測準確率**：目標75%+

### 輔助指標
- 不同球隊的預測表現
- 不同月份的預測表現
- 主客場預測差異
- 投手類型影響分析
