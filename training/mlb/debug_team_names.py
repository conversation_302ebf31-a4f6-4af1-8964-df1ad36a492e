#!/usr/bin/env python3
"""
調試球隊名稱匹配問題
檢查API返回的球隊名稱格式 vs 數據庫中的格式
"""

import sys
import os
from datetime import date

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game
from models.odds_data_fetcher import OddsDataFetcher

def debug_team_names():
    """調試球隊名稱匹配問題"""
    print("🔍 調試球隊名稱匹配問題")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        fetcher = OddsDataFetcher(app)
        
        # 1. 檢查數據庫中的球隊名稱格式
        print("📊 數據庫中的球隊名稱格式 (最近比賽):")
        recent_games = Game.query.filter(
            Game.date == date(2025, 6, 29)
        ).limit(10).all()
        
        print("   最近比賽的球隊名稱:")
        for game in recent_games:
            print(f"   - {game.away_team} @ {game.home_team}")
        
        # 2. 獲取API返回的球隊名稱格式
        print(f"\n🌐 API返回的球隊名稱格式:")
        
        try:
            # 直接調用API獲取當前賠率
            odds_data = fetcher._fetch_mlb_odds()
            
            if odds_data:
                print(f"   API返回 {len(odds_data)} 場比賽:")
                for i, game_odds in enumerate(odds_data[:10]):  # 只顯示前10場
                    home_team = game_odds.get('home_team', 'N/A')
                    away_team = game_odds.get('away_team', 'N/A')
                    commence_time = game_odds.get('commence_time', 'N/A')
                    print(f"   [{i+1}] {away_team} @ {home_team} ({commence_time})")
            else:
                print("   ❌ 無法獲取API數據")
        
        except Exception as e:
            print(f"   ❌ API調用失敗: {e}")
        
        # 3. 分析名稱差異
        print(f"\n🔍 球隊名稱格式分析:")
        
        # 獲取數據庫中所有唯一的球隊名稱
        db_teams = set()
        all_games = Game.query.limit(100).all()
        for game in all_games:
            db_teams.add(game.home_team)
            db_teams.add(game.away_team)
        
        print(f"   數據庫中的球隊名稱樣本 ({len(db_teams)} 個):")
        for team in sorted(list(db_teams))[:15]:  # 顯示前15個
            print(f"     - {team}")
        
        # 4. 檢查特定日期的比賽
        print(f"\n📅 檢查 2025-06-29 的具體比賽:")
        target_games = Game.query.filter_by(date=date(2025, 6, 29)).all()
        
        for game in target_games:
            print(f"   Game ID: {game.game_id}")
            print(f"   Teams: {game.away_team} @ {game.home_team}")
            print(f"   Status: {game.game_status}")
            print(f"   Time: {game.game_time}")
            print()

def test_team_mapping():
    """測試改進的球隊名稱映射"""
    print("🔧 測試改進的球隊名稱映射")
    print("=" * 60)
    
    # 創建更完整的球隊名稱映射
    team_mapping = {
        # 美國聯盟東區
        'New York Yankees': ['Yankees', 'NY Yankees', 'New York Yankees', 'NYY'],
        'Boston Red Sox': ['Red Sox', 'Boston Red Sox', 'BOS'],
        'Toronto Blue Jays': ['Blue Jays', 'Toronto Blue Jays', 'TOR'],
        'Baltimore Orioles': ['Orioles', 'Baltimore Orioles', 'BAL'],
        'Tampa Bay Rays': ['Rays', 'Tampa Bay Rays', 'TB'],
        
        # 美國聯盟中區
        'Chicago White Sox': ['White Sox', 'Chicago White Sox', 'CWS', 'CHW'],
        'Cleveland Guardians': ['Guardians', 'Cleveland Guardians', 'CLE'],
        'Detroit Tigers': ['Tigers', 'Detroit Tigers', 'DET'],
        'Kansas City Royals': ['Royals', 'Kansas City Royals', 'KC'],
        'Minnesota Twins': ['Twins', 'Minnesota Twins', 'MIN'],
        
        # 美國聯盟西區
        'Houston Astros': ['Astros', 'Houston Astros', 'HOU'],
        'Los Angeles Angels': ['Angels', 'LA Angels', 'Los Angeles Angels', 'LAA'],
        'Oakland Athletics': ['Athletics', 'Oakland Athletics', 'OAK', 'A\'s'],
        'Seattle Mariners': ['Mariners', 'Seattle Mariners', 'SEA'],
        'Texas Rangers': ['Rangers', 'Texas Rangers', 'TEX'],
        
        # 國家聯盟東區
        'Atlanta Braves': ['Braves', 'Atlanta Braves', 'ATL'],
        'Miami Marlins': ['Marlins', 'Miami Marlins', 'MIA'],
        'New York Mets': ['Mets', 'NY Mets', 'New York Mets', 'NYM'],
        'Philadelphia Phillies': ['Phillies', 'Philadelphia Phillies', 'PHI'],
        'Washington Nationals': ['Nationals', 'Washington Nationals', 'WSH', 'WAS'],
        
        # 國家聯盟中區
        'Chicago Cubs': ['Cubs', 'Chicago Cubs', 'CHC'],
        'Cincinnati Reds': ['Reds', 'Cincinnati Reds', 'CIN'],
        'Milwaukee Brewers': ['Brewers', 'Milwaukee Brewers', 'MIL'],
        'Pittsburgh Pirates': ['Pirates', 'Pittsburgh Pirates', 'PIT'],
        'St. Louis Cardinals': ['Cardinals', 'St Louis Cardinals', 'STL'],
        
        # 國家聯盟西區
        'Arizona Diamondbacks': ['Diamondbacks', 'Arizona Diamondbacks', 'ARI'],
        'Colorado Rockies': ['Rockies', 'Colorado Rockies', 'COL'],
        'Los Angeles Dodgers': ['Dodgers', 'LA Dodgers', 'Los Angeles Dodgers', 'LAD'],
        'San Diego Padres': ['Padres', 'San Diego Padres', 'SD'],
        'San Francisco Giants': ['Giants', 'San Francisco Giants', 'SF']
    }
    
    print(f"📋 完整球隊映射表 ({len(team_mapping)} 支球隊):")
    for full_name, variants in team_mapping.items():
        print(f"   {full_name}: {variants}")

if __name__ == "__main__":
    debug_team_names()
    print("\n" + "="*60)
    test_team_mapping()
