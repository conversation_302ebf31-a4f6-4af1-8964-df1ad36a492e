#!/usr/bin/env python3
"""
直接測試增強預測API端點
"""

import requests
import json
from datetime import date, datetime

def test_enhanced_api():
    """直接調用增強預測API"""
    
    print("🎯 直接測試增強預測API端點")
    print("=" * 50)
    
    # API端點 - 使用正確的端口5500
    api_url = "http://localhost:5500/unified/api/predict/enhanced_daily"
    
    # 請求數據
    test_data = {
        'target_date': '2025-08-23',
        'use_enhanced_engine': True
    }
    
    try:
        print(f"📡 調用API: {api_url}")
        print(f"📦 請求數據: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(
            api_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"🔍 響應狀態: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API調用成功！")
            
            # 顯示基本信息
            print(f"   🎲 引擎類型: {data.get('engine_info', {}).get('engine_type', 'Unknown')}")
            print(f"   📊 總比賽數: {data.get('total_games', 0)}")
            print(f"   ✅ 成功預測: {data.get('successful_predictions', 0)}")
            
            # 顯示前3場預測詳情
            predictions = data.get('predictions', [])
            if predictions:
                print(f"\\n🎯 預測結果 (前3場):")
                for i, pred in enumerate(predictions[:3], 1):
                    print(f"   {i}. {pred.get('matchup', 'N/A')}")
                    print(f"      預測: {pred.get('predicted_away_score', 0):.1f} - {pred.get('predicted_home_score', 0):.1f}")
                    print(f"      總分: {pred.get('predicted_total_runs', 0):.1f}")
                    methodology = pred.get('enhanced_features', {}).get('methodology', 'Unknown')
                    print(f"      方法: {methodology}")
                    print(f"      球場因子: {pred.get('enhanced_features', {}).get('ballpark_factor', 1.0):.3f}")
                    
                    if '✅ 使用增強預測引擎' in methodology or 'Enhanced' in methodology:
                        print("      ✅ 確認使用增強預測引擎")
                    else:
                        print("      ⚠️ 可能未使用增強預測引擎")
                    print()
                
                # 檢查是否有使用增強引擎
                enhanced_count = sum(1 for pred in predictions 
                                   if 'Enhanced' in pred.get('enhanced_features', {}).get('methodology', ''))
                
                if enhanced_count > 0:
                    print(f"🎉 成功！{enhanced_count}/{len(predictions)} 場比賽使用了增強預測引擎")
                    return True
                else:
                    print("⚠️ 整合問題：未檢測到增強預測引擎使用")
                    return False
            else:
                print("📭 沒有預測結果")
                return False
        else:
            print(f"❌ API調用失敗: {response.status_code}")
            print(f"錯誤信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 請求失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試出錯: {e}")
        return False

if __name__ == "__main__":
    # 先啟動Flask應用 (需要在另一個終端運行: python app.py)
    print("⚠️ 請確保在另一個終端運行: python app.py")
    print("按 Enter 繼續...")
    input()
    
    success = test_enhanced_api()
    
    if success:
        print("\\n✅ 增強預測API測試通過！")
        exit(0)
    else:
        print("\\n❌ 增強預測API測試失敗！")
        exit(1)