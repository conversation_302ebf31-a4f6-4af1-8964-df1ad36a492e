#!/usr/bin/env python3
"""
檢查BettingOdds表中的is_real字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, BettingOdds, Game

def check_is_real_field():
    """檢查is_real字段的值"""
    app = create_app()
    
    with app.app_context():
        test_date = date(2025, 7, 7)
        
        print(f"🔍 檢查 {test_date} 的is_real字段...")
        
        # 查詢該日期的盤口數據
        odds_records = db.session.query(BettingOdds, Game).join(
            Game, Game.game_id == BettingOdds.game_id
        ).filter(
            Game.date == test_date
        ).all()
        
        print(f"📊 找到 {len(odds_records)} 筆盤口記錄:")
        
        for odds, game in odds_records:
            print(f"\n比賽: {game.away_team} @ {game.home_team}")
            print(f"  博彩商: {odds.bookmaker}")
            print(f"  市場類型: {odds.market_type}")
            print(f"  總分線: {odds.total_point}")
            
            # 檢查是否有is_real字段
            if hasattr(odds, 'is_real'):
                print(f"  is_real: {odds.is_real}")
            else:
                print(f"  ❌ 沒有is_real字段")
            
            # 檢查所有字段
            print(f"  所有字段:")
            for column in odds.__table__.columns:
                value = getattr(odds, column.name)
                print(f"    {column.name}: {value}")

if __name__ == "__main__":
    check_is_real_field()
