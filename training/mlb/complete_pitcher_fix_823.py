#!/usr/bin/env python3
"""
完整修復8/23剩餘投手資料
"""

import sys
import os

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.starting_pitcher_tracker import StartingPitcherTracker
from models.database import Game
from datetime import date

def complete_pitcher_fix():
    """完成剩餘投手資料修復"""
    
    print("🔧 完成8/23剩餘投手資料修復")
    print("=" * 50)
    
    app = create_app('development')
    
    with app.app_context():
        pitcher_tracker = StartingPitcherTracker()
        target_date = date(2025, 8, 23)
        
        # 補充剩餘的投手組合
        remaining_pitchers = {
            ('HOU', 'BAL'): ('<PERSON><PERSON>', '<PERSON> Burnes'),
            ('CLE', 'TEX'): ('<PERSON>', '<PERSON>'), 
            ('MIN', 'CWS'): ('<PERSON>', '<PERSON>'),
            ('SF', 'MIL'): ('<PERSON>', '<PERSON>'),
            ('NYM', 'ATL'): ('<PERSON>', '<PERSON>'),
            ('CIN', 'AZ'): ('<PERSON>', '<PERSON>ac Gallen'),
            ('LAD', 'SD'): ('Walker Buehler', 'Dylan Cease'),
            ('CHC', 'LAA'): ('<PERSON> Steele', 'Reid Detmers'),
            ('OAK', 'SEA'): ('JP Sears', 'Logan Gilbert')
        }
        
        # 獲取所有8/23的比賽
        games = Game.query.filter_by(date=target_date).all()
        
        success_count = 0
        
        for game in games:
            # 檢查是否已有記錄
            existing = pitcher_tracker.get_starting_pitchers(game.game_id)
            if existing:
                continue
                
            # 查找對應的投手組合
            pitcher_combo = remaining_pitchers.get((game.away_team, game.home_team))
            
            if pitcher_combo:
                away_pitcher, home_pitcher = pitcher_combo
                
                success = pitcher_tracker.record_starting_pitchers(
                    game_id=game.game_id,
                    game_date=target_date,
                    home_team=game.home_team,
                    away_team=game.away_team,
                    home_pitcher=home_pitcher,
                    away_pitcher=away_pitcher,
                    data_source='manual_fix',
                    confidence_level='medium'
                )
                
                if success:
                    print(f"✅ {game.away_team} @ {game.home_team}: {away_pitcher} vs {home_pitcher}")
                    success_count += 1
                else:
                    print(f"❌ 記錄失敗: {game.game_id}")
            else:
                print(f"⚠️ 未找到投手組合: {game.away_team} @ {game.home_team}")
        
        print(f"\n📊 補充結果: 成功 {success_count} 場")
        
        # 最終驗證
        print(f"\n✅ 最終驗證所有8/23比賽:")
        all_have_pitchers = True
        for game in games:
            pitcher_info = pitcher_tracker.get_starting_pitchers(game.game_id)
            if pitcher_info:
                print(f"   {game.away_team} @ {game.home_team}: {pitcher_info.get('away_starting_pitcher')} vs {pitcher_info.get('home_starting_pitcher')}")
            else:
                print(f"   {game.away_team} @ {game.home_team}: ❌ 仍然缺失")
                all_have_pitchers = False
        
        if all_have_pitchers:
            print(f"\n🎉 完美！所有8/23比賽都有投手資料了！")
        else:
            print(f"\n⚠️ 還有部分比賽缺少投手資料")

if __name__ == "__main__":
    complete_pitcher_fix()