#!/usr/bin/env python3
"""
測試大小分預測器的校正效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.over_under_predictor import OverUnderPredictor
from models.database import Game
from datetime import date

def test_over_under_calibration():
    """測試大小分預測校正"""
    print("🎯 測試大小分預測校正效果")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        predictor = OverUnderPredictor(app)
        
        # 測試校正函數
        print("🧪 測試校正函數:")
        test_cases = [
            (7.0, 5.0, "正常得分"),
            (9.0, 8.0, "高分比賽"),
            (12.0, 10.0, "極高分比賽")
        ]
        
        for home, away, desc in test_cases:
            calibrated_home, calibrated_away = predictor._apply_score_calibration(home, away)
            original_total = home + away
            calibrated_total = calibrated_home + calibrated_away
            change = calibrated_total - original_total
            
            print(f"  {desc}: {home:.1f}-{away:.1f} ({original_total:.1f}) → {calibrated_home:.1f}-{calibrated_away:.1f} ({calibrated_total:.1f}) [{change:+.1f}]")
        
        # 測試實際比賽預測
        print(f"\n🏟️ 測試實際比賽預測:")
        
        # 獲取今天的比賽
        today_games = Game.query.filter_by(date=date.today()).limit(3).all()
        
        if today_games:
            for game in today_games:
                try:
                    result = predictor.predict_over_under(game.game_id)
                    
                    if 'error' not in result:
                        total_line = result.get('盤口', 'N/A')
                        predicted_total = result.get('預期總得分', 'N/A')
                        over_prob = result.get('大分概率', 0)
                        
                        print(f"  {game.away_team} @ {game.home_team}:")
                        print(f"    盤口: {total_line}")
                        print(f"    預測總分: {predicted_total}")
                        print(f"    大分概率: {over_prob:.1%}")
                    else:
                        print(f"  {game.away_team} @ {game.home_team}: 預測失敗 - {result['error']}")
                        
                except Exception as e:
                    print(f"  {game.away_team} @ {game.home_team}: 錯誤 - {e}")
        else:
            print("  沒有找到今天的比賽")

def main():
    test_over_under_calibration()

if __name__ == "__main__":
    main()
