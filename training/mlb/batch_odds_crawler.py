#!/usr/bin/env python3
"""
批量賠率抓取器
針對大範圍日期進行穩定的批量數據抓取
"""

import logging
import time
import random
from datetime import date, datetime, timedelta
from typing import List, Dict, Optional, Tuple
from integrated_odds_system import IntegratedOddsSystem
from enhanced_sbr_scraper import EnhancedSBRScraper
import json

logger = logging.getLogger(__name__)

class BatchOddsCrawler:
    """批量賠率抓取器"""
    
    def __init__(self):
        self.integrated_system = IntegratedOddsSystem()
        self.direct_scraper = EnhancedSBRScraper()
        
        # 抓取配置
        self.min_delay = 2.0  # 最小延遲秒數
        self.max_delay = 8.0  # 最大延遲秒數
        self.max_retries = 3   # 最大重試次數
        self.batch_size = 5    # 批次大小
        
    def crawl_date_range(self, start_date: date, end_date: date, 
                        market_type: str = 'both') -> Dict:
        """抓取日期範圍內的賠率數據"""
        
        logger.info(f"🚀 開始批量抓取: {start_date} 到 {end_date}")
        logger.info(f"📊 市場類型: {market_type}")
        
        # 生成日期列表
        date_list = self._generate_date_list(start_date, end_date)
        logger.info(f"📅 總計 {len(date_list)} 個日期需要抓取")
        
        # 結果統計
        results = {
            'total_dates': len(date_list),
            'successful_dates': 0,
            'failed_dates': 0,
            'total_games_processed': 0,
            'total_records_saved': 0,
            'date_results': [],
            'failed_dates_list': [],
            'success_rate': 0.0
        }
        
        # 批量處理
        for i, batch_dates in enumerate(self._batch_dates(date_list), 1):
            logger.info(f"🔄 處理批次 {i} - {len(batch_dates)} 個日期")
            
            batch_results = self._process_batch(batch_dates, market_type)
            
            # 更新總體結果
            for result in batch_results:
                results['date_results'].append(result)
                
                if result['success']:
                    results['successful_dates'] += 1
                    results['total_games_processed'] += result.get('games_processed', 0)
                    results['total_records_saved'] += result.get('total_saved', 0)
                else:
                    results['failed_dates'] += 1
                    results['failed_dates_list'].append(result['date'])
            
            # 批次間延遲
            if i < len(list(self._batch_dates(date_list))):
                delay = random.uniform(self.min_delay * 2, self.max_delay * 2)
                logger.info(f"⏳ 批次間延遲 {delay:.1f} 秒")
                time.sleep(delay)
        
        # 計算成功率
        results['success_rate'] = (results['successful_dates'] / results['total_dates']) * 100
        
        logger.info(f"✅ 批量抓取完成!")
        logger.info(f"📊 成功: {results['successful_dates']}/{results['total_dates']} ({results['success_rate']:.1f}%)")
        logger.info(f"🎮 總遊戲: {results['total_games_processed']}")
        logger.info(f"💾 總記錄: {results['total_records_saved']}")
        
        return results
    
    def _generate_date_list(self, start_date: date, end_date: date) -> List[date]:
        """生成日期列表"""
        date_list = []
        current_date = start_date
        
        while current_date <= end_date:
            date_list.append(current_date)
            current_date += timedelta(days=1)
            
        return date_list
    
    def _batch_dates(self, date_list: List[date]) -> List[List[date]]:
        """將日期列表分批"""
        for i in range(0, len(date_list), self.batch_size):
            yield date_list[i:i + self.batch_size]
    
    def _process_batch(self, batch_dates: List[date], market_type: str) -> List[Dict]:
        """處理一批日期"""
        batch_results = []
        
        for target_date in batch_dates:
            logger.info(f"📅 處理日期: {target_date}")
            
            # 嘗試抓取
            result = self._crawl_single_date_with_retry(target_date, market_type)
            
            batch_results.append({
                'date': target_date.isoformat(),
                'success': result.get('success', False),
                'games_processed': result.get('games_processed', 0),
                'total_saved': result.get('total_saved', 0),
                'message': result.get('message', ''),
                'method_used': result.get('method_used', 'unknown')
            })
            
            # 單次抓取間延遲
            delay = random.uniform(self.min_delay, self.max_delay)
            logger.info(f"⏳ 延遲 {delay:.1f} 秒")
            time.sleep(delay)
        
        return batch_results
    
    def _crawl_single_date_with_retry(self, target_date: date, market_type: str) -> Dict:
        """帶重試的單日期抓取"""
        
        # 方法1：嘗試集成系統
        for attempt in range(self.max_retries):
            try:
                logger.info(f"🔄 嘗試 {attempt + 1}/{self.max_retries} - 集成系統")
                
                result = self.integrated_system.fetch_and_save_odds(target_date, market_type)
                
                if result.get('success') and result.get('total_saved', 0) > 0:
                    logger.info(f"✅ 集成系統成功: {result.get('total_saved')} 條記錄")
                    result['method_used'] = 'integrated_system'
                    return result
                
                logger.warning(f"⚠️ 集成系統無數據: {result.get('message')}")
                
            except Exception as e:
                logger.warning(f"❌ 集成系統失敗 (嘗試 {attempt + 1}): {e}")
                
                if attempt < self.max_retries - 1:
                    retry_delay = (attempt + 1) * 2
                    logger.info(f"⏳ 重試延遲 {retry_delay} 秒")
                    time.sleep(retry_delay)
        
        # 方法2：嘗試直接抓取器
        logger.info("🔄 使用直接抓取器作為備用方案")
        try:
            raw_result = self.direct_scraper.fetch_current_odds(target_date)
            games = raw_result.get('games', [])
            
            if games:
                # 嘗試簡單保存（只記錄抓取成功）
                logger.info(f"📊 直接抓取獲得 {len(games)} 場比賽，但未保存到數據庫")
                return {
                    'success': True,
                    'games_processed': len(games),
                    'total_saved': 0,
                    'message': f'直接抓取獲得 {len(games)} 場比賽數據（未保存）',
                    'method_used': 'direct_scraper_only',
                    'raw_data_available': True
                }
            else:
                logger.warning(f"⚠️ 直接抓取器也無數據")
                
        except Exception as e:
            logger.error(f"❌ 直接抓取器失敗: {e}")
        
        # 所有方法都失敗
        return {
            'success': False,
            'games_processed': 0,
            'total_saved': 0,
            'message': f'{target_date} 無可用數據或所有抓取方法失敗',
            'method_used': 'all_failed'
        }
    
    def test_data_availability(self, start_date: date, end_date: date, 
                              sample_size: int = 10) -> Dict:
        """測試日期範圍內的數據可用性"""
        logger.info(f"🔍 測試數據可用性: {start_date} 到 {end_date}")
        
        date_list = self._generate_date_list(start_date, end_date)
        
        # 隨機採樣
        if len(date_list) > sample_size:
            sample_dates = random.sample(date_list, sample_size)
        else:
            sample_dates = date_list
        
        logger.info(f"📊 採樣 {len(sample_dates)} 個日期進行測試")
        
        results = {
            'total_tested': len(sample_dates),
            'data_available': 0,
            'no_data': 0,
            'error': 0,
            'availability_rate': 0.0,
            'sample_results': []
        }
        
        for test_date in sample_dates:
            try:
                logger.info(f"🧪 測試 {test_date}")
                
                raw_result = self.direct_scraper.fetch_current_odds(test_date)
                games_count = len(raw_result.get('games', []))
                
                if games_count > 0:
                    results['data_available'] += 1
                    status = 'available'
                    logger.info(f"✅ {test_date}: {games_count} 場比賽")
                else:
                    results['no_data'] += 1
                    status = 'no_data'
                    logger.info(f"❌ {test_date}: 無數據")
                
                results['sample_results'].append({
                    'date': test_date.isoformat(),
                    'games_count': games_count,
                    'status': status
                })
                
                # 測試間延遲
                time.sleep(1.0)
                
            except Exception as e:
                results['error'] += 1
                results['sample_results'].append({
                    'date': test_date.isoformat(),
                    'games_count': 0,
                    'status': 'error',
                    'error': str(e)
                })
                logger.error(f"❌ {test_date} 測試失敗: {e}")
        
        results['availability_rate'] = (results['data_available'] / results['total_tested']) * 100
        
        logger.info(f"📊 數據可用性測試完成:")
        logger.info(f"   有數據: {results['data_available']}/{results['total_tested']} ({results['availability_rate']:.1f}%)")
        logger.info(f"   無數據: {results['no_data']}")
        logger.info(f"   錯誤: {results['error']}")
        
        return results


def main():
    """主函數 - 批量抓取示例"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    from app import create_app
    
    app = create_app()
    with app.app_context():
        crawler = BatchOddsCrawler()
        
        # 先測試數據可用性
        print("🔍 步驟1: 測試數據可用性")
        availability_test = crawler.test_data_availability(
            date(2025, 6, 1), 
            date(2025, 8, 25), 
            sample_size=15
        )
        
        print(f"\n📊 可用性測試結果:")
        print(f"   數據可用率: {availability_test['availability_rate']:.1f}%")
        print(f"   有效日期: {availability_test['data_available']}/{availability_test['total_tested']}")
        
        # 如果可用性過低，提醒用戶
        if availability_test['availability_rate'] < 30:
            print("\n⚠️ 警告: 數據可用性較低，可能是因為:")
            print("   1. 歷史數據在網站上不可用")
            print("   2. 網站結構在某些日期發生了變化") 
            print("   3. 某些日期沒有MLB比賽")
            
            user_input = input("\n是否仍要繼續批量抓取? (y/n): ")
            if user_input.lower() != 'y':
                print("❌ 用戶取消批量抓取")
                return
        
        # 執行批量抓取
        print(f"\n🚀 步驟2: 開始批量抓取")
        results = crawler.crawl_date_range(
            date(2025, 6, 1),
            date(2025, 8, 25),
            'both'
        )
        
        # 保存結果
        with open('batch_crawl_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📋 最終結果已保存到 batch_crawl_results.json")


if __name__ == "__main__":
    main()