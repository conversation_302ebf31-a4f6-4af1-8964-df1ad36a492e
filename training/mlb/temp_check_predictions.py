#!/usr/bin/env python3
"""
腳本：查詢資料庫中最新的預測記錄，檢查模型版本和預測分數。
"""

import logging
from app import create_app, db
from models.database import Prediction, Game

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_latest_predictions():
    """查詢最新的預測記錄並打印其詳細信息。"""
    app = create_app()
    with app.app_context():
        try:
            # 查詢最新的10筆預測記錄
            latest_predictions = Prediction.query.order_by(Prediction.prediction_date.desc()).limit(10).all()

            if not latest_predictions:
                logger.info("資料庫中沒有找到任何預測記錄。")
                return

            logger.info("最新的預測記錄：")
            for pred in latest_predictions:
                game = Game.query.filter_by(game_id=pred.game_id).first()
                game_info = f"{game.away_team} @ {game.home_team} ({game.date})" if game else pred.game_id

                logger.info(f"  比賽: {game_info}")
                logger.info(f"    預測日期: {pred.prediction_date}")
                logger.info(f"    模型版本: {pred.model_version}")
                logger.info(f"    預測比分: {pred.predicted_away_score:.1f} - {pred.predicted_home_score:.1f}")
                logger.info(f"    信心度: {pred.confidence:.2f}")
                logger.info(f"    實際比分: {pred.actual_away_score} - {pred.actual_home_score} (已驗證: {pred.is_correct})")
                logger.info("----------------------------------------")

        except Exception as e:
            logger.error(f"查詢最新預測記錄失敗: {e}", exc_info=True)

if __name__ == '__main__':
    check_latest_predictions()
