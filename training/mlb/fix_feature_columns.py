#!/usr/bin/env python3
"""
修復特徵列問題並重新訓練模型
"""

from app import create_app
from models.model_trainer import ModelTrainer
from models.ml_predictor import MLBPredictor
from datetime import date, timedelta
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_feature_columns():
    """修復特徵列問題"""
    print("🔧 修復特徵列問題...")
    
    app = create_app()
    with app.app_context():
        try:
            # 創建模型訓練器
            trainer = ModelTrainer()
            
            # 使用最近60天的數據快速重新訓練
            print("🚀 開始快速重新訓練模型...")
            
            result = trainer.quick_retrain(days_back=60)
            
            if result and result.get('training_samples', 0) > 0:
                print("✅ 模型重新訓練成功！")
                print(f"📊 訓練樣本: {result.get('training_samples', 0)}")
                print(f"📊 特徵數量: {result.get('features_count', 0)}")
                
                # 測試模型載入
                predictor = MLBPredictor()
                if predictor.load_models('models/saved'):
                    print(f"✅ 模型載入成功，特徵列數量: {len(predictor.feature_columns)}")
                    print(f"📋 前10個特徵: {predictor.feature_columns[:10]}")
                    
                    # 測試預測功能
                    from models.database import Game
                    target_date = date(2025, 6, 25)
                    games = Game.query.filter(Game.date == target_date).limit(1).all()
                    
                    if games:
                        game = games[0]
                        print(f"🎯 測試預測: {game.away_team} @ {game.home_team}")
                        
                        try:
                            prediction = predictor.predict_game(
                                game.home_team, game.away_team, game.date
                            )
                            print(f"✅ 預測成功!")
                            print(f"   主隊得分: {prediction['predicted_home_score']}")
                            print(f"   客隊得分: {prediction['predicted_away_score']}")
                            print(f"   主隊勝率: {prediction['home_win_probability']:.3f}")
                            
                        except Exception as e:
                            print(f"❌ 預測測試失敗: {e}")
                    
                else:
                    print("❌ 模型載入失敗")
                    
            else:
                print("❌ 模型重新訓練失敗")
                
        except Exception as e:
            print(f"❌ 修復過程失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    fix_feature_columns()
