#!/usr/bin/env python3
"""
測試SportsBookReview歷史博彩數據獲取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.sportsbookreview_scraper import SportsBookReviewScraper
from datetime import datetime, date, timedelta
import logging
import sqlite3

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_sbr_historical_fetch():
    """測試SBR歷史數據獲取"""
    print("🎯 測試SportsBookReview歷史博彩數據獲取")
    print("=" * 60)
    
    try:
        # 初始化爬蟲
        scraper = SportsBookReviewScraper()
        
        # 測試獲取2025-07-01的數據
        test_date = date(2025, 7, 1)
        print(f"📅 獲取日期: {test_date}")
        print(f"🔗 URL: https://www.sportsbookreview.com/betting-odds/mlb-baseball/totals/full-game/?date={test_date}")
        
        # 獲取歷史數據
        historical_data = scraper.fetch_historical_odds(test_date, bookmaker='bet365')
        
        if historical_data and historical_data.get('games'):
            games = historical_data.get('games', [])
            print(f"✅ 成功獲取 {len(games)} 場比賽的歷史博彩數據")
            
            # 顯示前5場比賽的數據
            for i, game in enumerate(games[:5]):
                print(f"\n🏟️  比賽 {i+1}:")
                print(f"   對戰: {game.get('matchup', 'N/A')}")
                print(f"   主隊: {game.get('home_team', 'N/A')}")
                print(f"   客隊: {game.get('away_team', 'N/A')}")
                
                # 顯示總分盤
                totals = game.get('totals', {})
                if totals:
                    line = totals.get('line', 'N/A')
                    over_odds = totals.get('over_odds', 'N/A')
                    under_odds = totals.get('under_odds', 'N/A')
                    print(f"   📊 總分盤: {line} (Over: {over_odds}, Under: {under_odds})")
                
                # 顯示讓分盤
                spreads = game.get('spreads', {})
                if spreads:
                    home_line = spreads.get('home_line', 'N/A')
                    home_odds = spreads.get('home_odds', 'N/A')
                    away_line = spreads.get('away_line', 'N/A')
                    away_odds = spreads.get('away_odds', 'N/A')
                    print(f"   🎯 讓分盤: 主隊 {home_line} ({home_odds}), 客隊 {away_line} ({away_odds})")
        else:
            print("❌ 沒有獲取到歷史博彩數據")
            print("可能原因:")
            print("  - 網站結構已變更")
            print("  - 需要更新爬蟲邏輯")
            print("  - 網站反爬蟲機制")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_dates():
    """測試多個日期的數據獲取"""
    print("\n📅 測試多個日期的歷史數據")
    print("=" * 60)
    
    try:
        scraper = SportsBookReviewScraper()
        
        # 測試最近幾天的數據
        test_dates = [
            date(2025, 7, 1),
            date(2025, 7, 2),
            date(2025, 7, 3)
        ]
        
        for test_date in test_dates:
            print(f"\n📅 測試日期: {test_date}")
            
            historical_data = scraper.fetch_historical_odds(test_date, bookmaker='bet365')
            
            if historical_data and historical_data.get('games'):
                games = historical_data.get('games', [])
                print(f"   ✅ 獲取 {len(games)} 場比賽")
                
                # 統計總分盤數據
                total_lines = []
                for game in games:
                    totals = game.get('totals', {})
                    if totals and totals.get('line'):
                        try:
                            line = float(totals.get('line'))
                            total_lines.append(line)
                        except:
                            pass
                
                if total_lines:
                    avg_line = sum(total_lines) / len(total_lines)
                    min_line = min(total_lines)
                    max_line = max(total_lines)
                    print(f"   📊 總分盤統計: 平均 {avg_line:.1f}, 範圍 {min_line}-{max_line}")
                else:
                    print("   ⚠️  沒有有效的總分盤數據")
            else:
                print(f"   ❌ 沒有數據")
            
            # 避免請求過快
            time.sleep(2)
            
    except Exception as e:
        print(f"❌ 多日期測試失敗: {e}")

def save_historical_odds_to_db():
    """保存歷史博彩數據到數據庫"""
    print("\n💾 保存歷史博彩數據到數據庫")
    print("=" * 60)
    
    try:
        scraper = SportsBookReviewScraper()
        
        # 連接數據庫
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.join(basedir, "instance", "mlb_data.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 測試保存2025-07-01的數據
        test_date = date(2025, 7, 1)
        historical_data = scraper.fetch_historical_odds(test_date, bookmaker='bet365')
        
        if not historical_data or not historical_data.get('games'):
            print("❌ 沒有數據可保存")
            conn.close()
            return
        
        saved_count = 0
        games = historical_data.get('games', [])
        
        for game in games:
            home_team = game.get('home_team', '')
            away_team = game.get('away_team', '')
            
            if not home_team or not away_team:
                continue
            
            # 查找對應的game_id
            cursor.execute("""
                SELECT game_id FROM games 
                WHERE away_team = ? AND home_team = ? AND date = ?
            """, (away_team, home_team, str(test_date)))
            
            result = cursor.fetchone()
            if not result:
                print(f"⚠️  找不到比賽: {away_team} @ {home_team}")
                continue
            
            game_id = result[0]
            
            # 保存總分盤數據
            totals = game.get('totals', {})
            if totals and totals.get('line'):
                try:
                    total_point = float(totals.get('line'))
                    
                    # 保存到數據庫
                    cursor.execute("""
                        INSERT OR REPLACE INTO betting_odds 
                        (game_id, bookmaker, market_type, total_point, odds_time, created_at, updated_at)
                        VALUES (?, ?, 'totals', ?, ?, ?, ?)
                    """, (
                        game_id,
                        'bet365',
                        total_point,
                        datetime.now().isoformat(),
                        datetime.now().isoformat(),
                        datetime.now().isoformat()
                    ))
                    saved_count += 1
                    print(f"✅ 保存: {away_team}@{home_team} - bet365: {total_point}")
                    
                except ValueError:
                    print(f"⚠️  無效的總分盤數據: {totals.get('line')}")
        
        conn.commit()
        conn.close()
        print(f"\n💾 總共保存了 {saved_count} 個真實博彩盤口數據")
        
    except Exception as e:
        print(f"❌ 保存失敗: {e}")
        import traceback
        traceback.print_exc()

def verify_saved_real_odds():
    """驗證保存的真實博彩數據"""
    print("\n🔍 驗證保存的真實博彩數據")
    print("=" * 60)
    
    try:
        # 連接數據庫
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.join(basedir, "instance", "mlb_data.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查詢真實博彩數據 (非estimated和Simulated)
        query = """
        SELECT 
            g.date,
            g.away_team || '@' || g.home_team as matchup,
            bo.bookmaker,
            bo.total_point,
            bo.odds_time
        FROM betting_odds bo
        JOIN games g ON bo.game_id = g.game_id
        WHERE g.date >= '2025-07-01' 
        AND bo.bookmaker NOT IN ('estimated', 'Simulated')
        ORDER BY g.date, g.game_id
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        if results:
            print(f"找到 {len(results)} 個真實博彩數據:")
            
            # 按日期分組顯示
            current_date = None
            for date_str, matchup, bookmaker, total_point, odds_time in results:
                if date_str != current_date:
                    current_date = date_str
                    print(f"\n📅 {date_str}:")
                
                print(f"  {matchup} - {bookmaker}: {total_point}")
        else:
            print("❌ 沒有找到真實博彩數據")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")

if __name__ == '__main__':
    test_sbr_historical_fetch()
    test_multiple_dates()
    save_historical_odds_to_db()
    verify_saved_real_odds()
