#!/usr/bin/env python3
"""
使用 Core_v1.0 模型生成預測
確保系統中有 Core_v1.0 模型的預測記錄
"""

import sys
import os
from datetime import date, timedelta

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction
from models.ml_predictor import MLBPredictor
from models.prediction_service import PredictionService
import json

def generate_core_v1_predictions():
    """使用 Core_v1.0 模型生成預測"""
    print("🎯 使用 Core_v1.0 模型生成預測")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        # 1. 檢查 Core_v1.0 模型是否存在
        print("📂 檢查 Core_v1.0 模型...")
        
        core_model_path = "models/saved/Core_v1.0"
        if not os.path.exists(core_model_path):
            print(f"❌ Core_v1.0 模型不存在: {core_model_path}")
            return False
        
        print(f"✅ Core_v1.0 模型存在: {core_model_path}")
        
        # 2. 載入 Core_v1.0 模型
        print("\n🔧 載入 Core_v1.0 模型...")
        
        try:
            # 創建預測器並指定載入 Core_v1.0 模型
            predictor = MLBPredictor()
            success = predictor.load_models(core_model_path)
            
            if not success:
                print("❌ Core_v1.0 模型載入失敗")
                return False
            
            print("✅ Core_v1.0 模型載入成功")
            
        except Exception as e:
            print(f"❌ 載入模型時發生錯誤: {e}")
            return False
        
        # 3. 選擇要預測的日期（最近幾天的比賽）
        target_dates = [
            date.today() - timedelta(days=1),  # 昨天
            date.today(),                      # 今天
            date.today() + timedelta(days=1),  # 明天
        ]
        
        total_predictions = 0
        
        for target_date in target_dates:
            print(f"\n📅 處理日期: {target_date}")
            
            # 獲取該日期的比賽
            games = Game.query.filter_by(date=target_date).all()
            
            if not games:
                print(f"   ⚠️  沒有找到 {target_date} 的比賽")
                continue
            
            print(f"   📊 找到 {len(games)} 場比賽")
            
            for game in games:
                try:
                    # 檢查是否已有 Core_v1.0 的預測
                    existing_prediction = Prediction.query.filter_by(
                        game_id=game.game_id,
                        model_version='Core_v1.0'
                    ).first()
                    
                    if existing_prediction:
                        print(f"   ⏭️  跳過 {game.away_team} @ {game.home_team} (已有預測)")
                        continue
                    
                    # 使用 Core_v1.0 模型進行預測
                    print(f"   🔮 預測 {game.away_team} @ {game.home_team}...")

                    prediction_result = predictor.predict_game_advanced(
                        home_team=game.home_team,
                        away_team=game.away_team,
                        game_date=game.date
                    )
                    
                    if not prediction_result:
                        print(f"   ❌ 預測失敗")
                        continue
                    
                    # 創建預測記錄
                    new_prediction = Prediction(
                        game_id=game.game_id,
                        predicted_home_score=prediction_result.get('predicted_home_score', 0),
                        predicted_away_score=prediction_result.get('predicted_away_score', 0),
                        home_win_probability=prediction_result.get('home_win_probability', 0.5),
                        away_win_probability=prediction_result.get('away_win_probability', 0.5),
                        confidence=prediction_result.get('confidence', 0.5),
                        model_version='Core_v1.0',  # 明確設置為 Core_v1.0
                        prediction_date=target_date,
                        over_under_line=prediction_result.get('over_under_line'),
                        run_line=prediction_result.get('run_line')
                    )
                    
                    db.session.add(new_prediction)
                    db.session.commit()
                    
                    total_predictions += 1
                    
                    print(f"   ✅ 預測完成: {prediction_result['predicted_away_score']:.1f} - {prediction_result['predicted_home_score']:.1f}")
                    print(f"      主隊勝率: {prediction_result['home_win_probability']:.1%}, 信心度: {prediction_result['confidence']:.1%}")
                    
                except Exception as e:
                    print(f"   ❌ 預測 {game.away_team} @ {game.home_team} 失敗: {e}")
                    continue
        
        print(f"\n🎉 預測生成完成!")
        print(f"   總共生成: {total_predictions} 條 Core_v1.0 預測")
        
        # 4. 驗證結果
        print(f"\n🔍 驗證結果...")
        core_predictions = Prediction.query.filter_by(model_version='Core_v1.0').count()
        print(f"   數據庫中 Core_v1.0 預測總數: {core_predictions}")
        
        if core_predictions > 0:
            # 顯示最新的 Core_v1.0 預測
            latest_predictions = Prediction.query.filter_by(
                model_version='Core_v1.0'
            ).order_by(Prediction.created_at.desc()).limit(5).all()
            
            print(f"\n📋 最新5條 Core_v1.0 預測:")
            for i, pred in enumerate(latest_predictions, 1):
                game = Game.query.filter_by(game_id=pred.game_id).first()
                if game:
                    print(f"   [{i}] {game.away_team} @ {game.home_team}: "
                          f"{pred.predicted_away_score:.1f}-{pred.predicted_home_score:.1f} "
                          f"(勝率: {pred.home_win_probability:.1%}, 信心: {pred.confidence:.1f})")
        
        return True

if __name__ == "__main__":
    success = generate_core_v1_predictions()
    if success:
        print("\n✅ Core_v1.0 預測生成成功!")
    else:
        print("\n❌ Core_v1.0 預測生成失敗!")
