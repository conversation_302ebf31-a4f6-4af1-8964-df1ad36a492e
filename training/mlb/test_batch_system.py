#!/usr/bin/env python3
"""
測試批次預測與回測系統
"""

import requests
import json
import sys
import time

def test_batch_system():
    """測試批次系統功能"""
    
    print("🧪 測試批次預測與回測系統")
    print("=" * 50)
    
    base_url = "http://localhost:5500"
    
    # 測試1: 檢查批次系統頁面
    print("1️⃣ 測試批次系統頁面...")
    try:
        response = requests.get(f"{base_url}/batch_system/batch_system", timeout=10)
        if response.status_code == 200:
            print("✅ 批次系統頁面載入成功")
        else:
            print(f"❌ 批次系統頁面載入失敗: {response.status_code}")
    except Exception as e:
        print(f"❌ 批次系統頁面測試失敗: {e}")
    
    # 測試2: 檢查簡化預測頁面
    print("\n2️⃣ 測試簡化預測頁面...")
    try:
        response = requests.get(f"{base_url}/batch_system/simplified", timeout=10)
        if response.status_code == 200:
            print("✅ 簡化預測頁面載入成功")
        else:
            print(f"❌ 簡化預測頁面載入失敗: {response.status_code}")
    except Exception as e:
        print(f"❌ 簡化預測頁面測試失敗: {e}")
    
    # 測試3: 測試批次預測API
    print("\n3️⃣ 測試批次預測API...")
    batch_data = {
        'startDate': '2025-08-20',
        'endDate': '2025-08-23',
        'engine': 'enhanced',
        'strategy': 'skip_existing',
        'excludeWeekends': False,
        'saveResults': True,
        'enableNotifications': True,
        'generateReport': True
    }
    
    try:
        response = requests.post(
            f"{base_url}/batch_system/api/batch/prediction",
            json=batch_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 批次預測API啟動成功")
                task_id = data.get('task_id')
                
                # 監控任務狀態
                print(f"📋 任務ID: {task_id}")
                print("⏳ 監控任務進度...")
                
                for i in range(10):  # 最多等待20秒
                    time.sleep(2)
                    status_response = requests.get(
                        f"{base_url}/batch_system/api/batch/status/{task_id}",
                        timeout=5
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        progress = status_data.get('progress', 0)
                        status = status_data.get('status', 'unknown')
                        details = status_data.get('details', '')
                        
                        print(f"   進度: {progress}% - {status} - {details}")
                        
                        if status == 'completed':
                            print("✅ 批次預測任務完成")
                            results = status_data.get('results', {})
                            print(f"   處理比賽數: {results.get('total_games', 0)}")
                            print(f"   成功預測: {results.get('successful_predictions', 0)}")
                            print(f"   平均信心度: {results.get('avg_confidence', 0):.1f}%")
                            break
                        elif status == 'failed':
                            print(f"❌ 批次預測任務失敗: {status_data.get('error', 'Unknown error')}")
                            break
                    else:
                        print(f"❌ 無法獲取任務狀態: {status_response.status_code}")
                        break
            else:
                print(f"❌ 批次預測API啟動失敗: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ 批次預測API請求失敗: {response.status_code}")
    except Exception as e:
        print(f"❌ 批次預測API測試失敗: {e}")
    
    # 測試4: 測試回測API
    print("\n4️⃣ 測試回測API...")
    backtest_data = {
        'startDate': '2025-08-15',
        'endDate': '2025-08-25',
        'metrics': ['accuracy', 'score_difference', 'over_under'],
        'scope': 'all'
    }
    
    try:
        response = requests.post(
            f"{base_url}/batch_system/api/batch/backtest",
            json=backtest_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 回測API啟動成功")
                task_id = data.get('task_id')
                print(f"📋 回測任務ID: {task_id}")
            else:
                print(f"❌ 回測API啟動失敗: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ 回測API請求失敗: {response.status_code}")
    except Exception as e:
        print(f"❌ 回測API測試失敗: {e}")
    
    # 測試5: 測試算式測試API
    print("\n5️⃣ 測試算式測試API...")
    algorithm_test_data = {
        'startDate': '2025-08-15',
        'endDate': '2025-08-25',
        'algorithms': ['enhanced', 'machine_learning'],
        'testConfidenceThresholds': True,
        'testFeatureWeights': True,
        'testSeasonalAdjustments': False
    }
    
    try:
        response = requests.post(
            f"{base_url}/batch_system/api/batch/algorithm_test",
            json=algorithm_test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 算式測試API啟動成功")
                task_id = data.get('task_id')
                print(f"📋 算式測試任務ID: {task_id}")
            else:
                print(f"❌ 算式測試API啟動失敗: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ 算式測試API請求失敗: {response.status_code}")
    except Exception as e:
        print(f"❌ 算式測試API測試失敗: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 批次系統測試完成")
    print("\n📋 功能摘要:")
    print("✅ 批次預測系統 - 支援日期範圍批量預測")
    print("✅ 歷史回測系統 - 分析預測準確性")
    print("✅ 算式測試系統 - 比較不同預測算法")
    print("✅ 簡化預測界面 - 快速單日預測")
    print("✅ 進度監控API - 實時追蹤處理狀態")
    
    print("\n🔗 訪問連結:")
    print(f"📊 批次系統: {base_url}/batch_system/batch_system")
    print(f"⚡ 簡化預測: {base_url}/batch_system/simplified")
    
    return True

def test_simplified_prediction():
    """測試簡化預測功能"""
    
    print("\n" + "=" * 50)
    print("🚀 測試簡化預測功能")
    print("=" * 50)
    
    base_url = "http://localhost:5500"
    
    # 測試增強預測API
    print("1️⃣ 測試增強預測API...")
    
    test_data = {
        'target_date': '2025-08-23'
    }
    
    try:
        response = requests.post(
            f"{base_url}/unified/api/predict/enhanced_daily",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 增強預測API測試成功")
                print(f"   總比賽數: {data.get('total_games', 0)}")
                print(f"   成功預測: {data.get('successful_predictions', 0)}")
                print(f"   處理時間: {data.get('processing_time', 0)}s")
                print(f"   引擎類型: {data.get('engine_info', {}).get('engine_type', 'Unknown')}")
                
                # 檢查預測結果格式
                predictions = data.get('predictions', [])
                if predictions:
                    pred = predictions[0]
                    print(f"   範例預測: {pred.get('matchup', 'N/A')}")
                    print(f"   預測分數: {pred.get('predicted_away_score', 0):.1f} - {pred.get('predicted_home_score', 0):.1f}")
                    print(f"   信心度: {(pred.get('confidence', 0) * 100):.1f}%")
                    
                    # 檢查增強功能
                    enhanced_features = pred.get('enhanced_features', {})
                    if enhanced_features:
                        print(f"   增強功能: {enhanced_features.get('methodology', 'N/A')}")
                        print(f"   球場因子: {enhanced_features.get('ballpark_factor', 1.0):.3f}")
                
            else:
                print(f"❌ 增強預測API測試失敗: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ 增強預測API請求失敗: {response.status_code}")
    except Exception as e:
        print(f"❌ 增強預測API測試出錯: {e}")
    
    print("\n🎊 簡化預測測試完成")
    return True

if __name__ == "__main__":
    print("🧪 批次預測與回測系統測試")
    print("⚠️ 請確保Flask應用在另一個終端運行: ./start.sh")
    print("🔄 自動啟動測試...")
    
    try:
        # 測試批次系統
        test_batch_system()
        
        # 測試簡化預測
        test_simplified_prediction()
        
        print("\n" + "🎉" * 20)
        print("✅ 所有測試完成！新功能已準備就緒")
        print("🎯 解決方案摘要:")
        print("   ✓ 批次預測功能 - 支援日期範圍批量處理")
        print("   ✓ 歷史回測功能 - 分析和改善預測準確性")
        print("   ✓ 算式測試功能 - 比較和優化預測算法")
        print("   ✓ 簡化預測界面 - 修復預測日期顯示問題")
        print("   ✓ 進度監控系統 - 實時追蹤處理狀態")
        
        sys.exit(0)
        
    except KeyboardInterrupt:
        print("\n\n❌ 測試被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 測試過程出錯: {e}")
        sys.exit(1)