#!/usr/bin/env python3
"""
測試API返回的數據格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import date

def test_api_response():
    """測試API返回的數據格式"""
    
    # 測試自定義預測API
    url = "http://localhost:5500/unified/api/predict/custom_date"
    data = {
        "date": "2025-07-07",
        "exclude_postponed": True
    }
    
    print(f"🌐 測試API: {url}")
    print(f"📤 請求數據: {data}")
    
    try:
        response = requests.post(url, json=data)
        print(f"📥 響應狀態: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API調用成功")
            
            # 檢查預測數據結構
            predictions = result.get('predictions', [])
            print(f"📊 預測數量: {len(predictions)}")
            
            if predictions:
                # 檢查第一個預測的數據結構
                first_pred = predictions[0]
                print(f"\n🔍 第一個預測的數據結構:")
                print(json.dumps(first_pred, indent=2, ensure_ascii=False))
                
                # 特別檢查betting_odds結構
                betting_odds = first_pred.get('betting_odds')
                if betting_odds:
                    print(f"\n💰 盤口數據結構:")
                    print(json.dumps(betting_odds, indent=2, ensure_ascii=False))
                    
                    # 檢查totals字段
                    totals = betting_odds.get('totals')
                    if totals:
                        print(f"\n📈 總分線數據:")
                        print(f"  total_point: {totals.get('total_point')}")
                        print(f"  is_real: {totals.get('is_real')}")
                        print(f"  data_source: {totals.get('data_source')}")
                else:
                    print(f"❌ 沒有找到betting_odds數據")
                    
        else:
            print(f"❌ API調用失敗: {response.text}")
            
    except Exception as e:
        print(f"❌ 請求失敗: {e}")

if __name__ == "__main__":
    test_api_response()
