#!/usr/bin/env python3
"""
調試實際結果載入問題
檢查數據庫中的實際比賽結果和預測數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction
from datetime import date, timedelta
from sqlalchemy import text

def check_actual_results():
    """檢查實際比賽結果"""
    print("🔍 檢查實際比賽結果...")
    
    app = create_app()
    with app.app_context():
        # 檢查最近幾天的比賽結果
        end_date = date.today()
        start_date = end_date - timedelta(days=3)
        
        print(f"📅 檢查日期範圍: {start_date} 到 {end_date}")
        
        # 查詢有實際結果的比賽
        games_with_results = Game.query.filter(
            Game.date.between(start_date, end_date),
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).order_by(Game.date.desc()).all()
        
        print(f"📊 找到 {len(games_with_results)} 場有實際結果的比賽:")
        print("-" * 80)
        print("日期        比賽                實際比分    game_id")
        print("-" * 80)
        
        for game in games_with_results[:10]:  # 只顯示前10場
            print(f"{game.date}  {game.away_team:3} @ {game.home_team:3}      {game.away_score:2}-{game.home_score:2}      {game.game_id}")
        
        return games_with_results

def check_predictions_with_results():
    """檢查預測數據中的實際結果"""
    print(f"\n🎯 檢查預測數據中的實際結果...")
    
    app = create_app()
    with app.app_context():
        # 查詢最近的預測記錄
        query = text("""
            SELECT p.game_id, p.predicted_home_score, p.predicted_away_score,
                   p.actual_home_score, p.actual_away_score, p.is_correct,
                   g.home_team, g.away_team, g.date, g.home_score, g.away_score
            FROM predictions p
            LEFT JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= :start_date
            AND g.home_score IS NOT NULL
            AND g.away_score IS NOT NULL
            ORDER BY g.date DESC
            LIMIT 10
        """)
        
        start_date = date.today() - timedelta(days=3)
        results = db.session.execute(query, {'start_date': start_date}).fetchall()
        
        print(f"📋 找到 {len(results)} 條預測記錄:")
        print("-" * 100)
        print("比賽                預測得分    實際得分(預測表)  實際得分(比賽表)  準確性")
        print("-" * 100)
        
        for row in results:
            game_id, pred_home, pred_away, actual_home_pred, actual_away_pred, is_correct, home_team, away_team, game_date, home_score, away_score = row
            
            pred_score = f"{pred_away:.1f}-{pred_home:.1f}" if pred_home and pred_away else "N/A"
            actual_pred = f"{actual_away_pred}-{actual_home_pred}" if actual_home_pred is not None and actual_away_pred is not None else "未更新"
            actual_game = f"{away_score}-{home_score}" if home_score is not None and away_score is not None else "N/A"
            accuracy = "✅" if is_correct else "❌" if is_correct is not None else "未計算"
            
            print(f"{away_team:3} @ {home_team:3} {game_date}  {pred_score:8}    {actual_pred:8}      {actual_game:8}      {accuracy}")
        
        return results

def test_api_endpoint():
    """測試API端點"""
    print(f"\n🌐 測試API端點...")
    
    import requests
    
    # 測試日期
    test_date = "2025-07-11"  # 使用有實際結果的日期
    
    try:
        # 測試API端點
        url = f"http://localhost:5500/unified/api/predictions/date/{test_date}"
        print(f"📡 測試URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API響應成功")
            print(f"📊 返回數據:")
            print(f"  success: {data.get('success')}")
            print(f"  predictions數量: {len(data.get('predictions', []))}")
            
            # 檢查前幾個預測的實際結果
            predictions = data.get('predictions', [])[:3]
            for i, pred in enumerate(predictions, 1):
                print(f"  預測{i}:")
                print(f"    game_id: {pred.get('game_id')}")
                print(f"    actual_home_score: {pred.get('actual_home_score')}")
                print(f"    actual_away_score: {pred.get('actual_away_score')}")
                print(f"    is_correct: {pred.get('is_correct')}")
        else:
            print(f"❌ API響應失敗: {response.status_code}")
            print(f"響應內容: {response.text}")
            
    except Exception as e:
        print(f"❌ API測試失敗: {e}")

def update_missing_actual_results():
    """更新缺失的實際結果"""
    print(f"\n🔄 更新缺失的實際結果...")
    
    app = create_app()
    with app.app_context():
        # 查找有實際比賽結果但預測表中未更新的記錄
        query = text("""
            SELECT p.id, p.game_id, g.home_score, g.away_score
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.home_score IS NOT NULL 
            AND g.away_score IS NOT NULL
            AND (p.actual_home_score IS NULL OR p.actual_away_score IS NULL)
            AND g.date >= :start_date
        """)
        
        start_date = date.today() - timedelta(days=7)
        results = db.session.execute(query, {'start_date': start_date}).fetchall()
        
        print(f"📋 找到 {len(results)} 條需要更新的預測記錄")
        
        updated_count = 0
        for row in results:
            pred_id, game_id, home_score, away_score = row
            
            try:
                # 更新預測記錄
                prediction = Prediction.query.get(pred_id)
                if prediction:
                    prediction.actual_home_score = home_score
                    prediction.actual_away_score = away_score
                    prediction.actual_total_runs = home_score + away_score
                    
                    # 計算準確性
                    if prediction.predicted_home_score and prediction.predicted_away_score:
                        predicted_home_wins = prediction.predicted_home_score > prediction.predicted_away_score
                        actual_home_wins = home_score > away_score
                        prediction.is_correct = predicted_home_wins == actual_home_wins
                        
                        # 計算得分差異
                        home_diff = abs(prediction.predicted_home_score - home_score)
                        away_diff = abs(prediction.predicted_away_score - away_score)
                        prediction.score_difference = (home_diff + away_diff) / 2
                    
                    updated_count += 1
                    print(f"✅ 更新 {game_id}: {away_score}-{home_score}")
                    
            except Exception as e:
                print(f"❌ 更新失敗 {game_id}: {e}")
        
        # 提交更改
        try:
            db.session.commit()
            print(f"💾 成功更新 {updated_count} 條記錄")
        except Exception as e:
            db.session.rollback()
            print(f"❌ 提交失敗: {e}")

def main():
    """主要調試流程"""
    print("🔧 實際結果載入問題調試")
    print("=" * 60)
    
    # 1. 檢查實際比賽結果
    games = check_actual_results()
    
    # 2. 檢查預測數據中的實際結果
    predictions = check_predictions_with_results()
    
    # 3. 更新缺失的實際結果
    update_missing_actual_results()
    
    # 4. 測試API端點
    test_api_endpoint()
    
    print(f"\n📋 調試總結:")
    print(f"  有實際結果的比賽: {len(games) if games else 0} 場")
    print(f"  有預測記錄的比賽: {len(predictions) if predictions else 0} 場")
    print(f"  建議: 檢查前端JavaScript是否正確調用API")

if __name__ == "__main__":
    main()
