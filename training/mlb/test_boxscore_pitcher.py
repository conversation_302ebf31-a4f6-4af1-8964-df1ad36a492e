#!/usr/bin/env python3
"""
測試BoxScore投手提取器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game, PlayerGameStats, Team
from models.boxscore_pitcher_extractor import boxscore_pitcher_extractor

def test_boxscore_pitcher_extractor():
    """測試BoxScore投手提取器"""
    app = create_app()
    
    with app.app_context():
        print("🔍 測試BoxScore投手提取器")
        print("=" * 80)
        
        # 測試案例：查找有PlayerGameStats數據的比賽
        test_games = [
            {
                'game_id': '777122',
                'home_team': 'NYY',
                'away_team': 'CHC',
                'description': 'CHC @ NYY - 測試從比賽統計獲取投手'
            },
            {
                'game_id': '777170',
                'home_team': 'BOS', 
                'away_team': 'TB',
                'description': 'TB @ BOS - 測試另一場比賽的投手'
            }
        ]
        
        for i, test_case in enumerate(test_games, 1):
            print(f"\n🎯 測試案例 {i}: {test_case['description']}")
            print("-" * 60)
            
            game_id = test_case['game_id']
            home_team = test_case['home_team']
            away_team = test_case['away_team']
            
            # 檢查比賽是否存在
            game = Game.query.filter_by(game_id=game_id).first()
            if not game:
                print(f"❌ 找不到比賽 ID: {game_id}")
                continue
                
            print(f"📅 比賽: {away_team} @ {home_team} ({game.date})")
            
            # 檢查是否有PlayerGameStats數據
            pitcher_stats = PlayerGameStats.query.filter(
                PlayerGameStats.game_id == game_id,
                PlayerGameStats.position == 'P',
                PlayerGameStats.innings_pitched > 0
            ).all()
            
            print(f"📊 該比賽投手統計數量: {len(pitcher_stats)}")
            
            if pitcher_stats:
                print(f"📋 投手列表:")
                for stat in pitcher_stats:
                    team_obj = Team.query.get(stat.team_id)
                    team_code = team_obj.team_code if team_obj and hasattr(team_obj, 'team_code') else 'Unknown'
                    
                    # 計算ERA
                    if stat.innings_pitched > 0:
                        era = (stat.earned_runs * 9.0) / stat.innings_pitched
                    else:
                        era = 0.0
                    
                    print(f"   - 投手#{stat.player_id} ({team_code}) "
                          f"投球 {stat.innings_pitched} 局, "
                          f"失分 {stat.earned_runs}, "
                          f"ERA: {era:.2f}")
            
            # 測試獲取主隊投手
            print(f"\n🏠 測試獲取主隊投手 ({home_team}):")
            home_pitcher = boxscore_pitcher_extractor.get_starting_pitcher_from_boxscore(
                game_id, home_team, is_home=True
            )
            if home_pitcher:
                print(f"   ✅ 找到主隊投手: {home_pitcher['name']} "
                      f"(ERA: {home_pitcher['era']:.2f}, 質量: {home_pitcher['quality']:.1f})")
            else:
                print(f"   ❌ 找不到主隊投手")
            
            # 測試獲取客隊投手
            print(f"\n✈️  測試獲取客隊投手 ({away_team}):")
            away_pitcher = boxscore_pitcher_extractor.get_starting_pitcher_from_boxscore(
                game_id, away_team, is_home=False
            )
            if away_pitcher:
                print(f"   ✅ 找到客隊投手: {away_pitcher['name']} "
                      f"(ERA: {away_pitcher['era']:.2f}, 質量: {away_pitcher['quality']:.1f})")
            else:
                print(f"   ❌ 找不到客隊投手")
            
            # 測試投手對戰分析
            print(f"\n🧠 測試投手對戰分析:")
            analysis = boxscore_pitcher_extractor.analyze_pitcher_matchup(
                game_id, home_team, away_team
            )
            
            print(f"   主隊: {analysis['home_pitcher']['name']} "
                  f"(ERA: {analysis['home_pitcher']['era']:.2f}, 等級: {analysis['home_pitcher']['strength']})")
            print(f"   客隊: {analysis['away_pitcher']['name']} "
                  f"(ERA: {analysis['away_pitcher']['era']:.2f}, 等級: {analysis['away_pitcher']['strength']})")
            print(f"   對戰類型: {analysis['matchup_type']}")
            print(f"   平均ERA: {analysis['average_era']:.2f}")

def test_pitcher_stats_summary():
    """測試投手統計摘要"""
    print(f"\n\n🔬 測試投手統計摘要")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        # 測試獲取投手統計摘要
        game_id = '777122'  # CHC @ NYY
        
        summary = boxscore_pitcher_extractor.get_pitcher_stats_summary(game_id)
        
        print(f"📊 比賽: {summary.get('away_team', 'Unknown')} @ {summary.get('home_team', 'Unknown')}")
        print(f"📅 日期: {summary.get('date', 'Unknown')}")
        print(f"👥 投手總數: {summary.get('total_pitchers', 0)}")
        
        # 顯示主隊先發投手
        home_starter = summary.get('home_starting_pitcher')
        if home_starter:
            print(f"\n🏠 主隊先發投手:")
            print(f"   名稱: {home_starter['name']}")
            print(f"   ERA: {home_starter['era']:.2f}")
            print(f"   投球局數: {home_starter['innings_pitched']}")
            print(f"   失分: {home_starter['earned_runs']}")
            print(f"   三振: {home_starter.get('strikeouts_pitched', 0)}")
        else:
            print(f"\n❌ 找不到主隊先發投手")
        
        # 顯示客隊先發投手
        away_starter = summary.get('away_starting_pitcher')
        if away_starter:
            print(f"\n✈️  客隊先發投手:")
            print(f"   名稱: {away_starter['name']}")
            print(f"   ERA: {away_starter['era']:.2f}")
            print(f"   投球局數: {away_starter['innings_pitched']}")
            print(f"   失分: {away_starter['earned_runs']}")
            print(f"   三振: {away_starter.get('strikeouts_pitched', 0)}")
        else:
            print(f"\n❌ 找不到客隊先發投手")

def test_intelligent_prediction_with_boxscore():
    """測試使用BoxScore數據的智能預測"""
    print(f"\n\n🧠 測試使用BoxScore數據的智能預測")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        from models.simple_intelligent_predictor import SimpleIntelligentPredictor
        
        predictor = SimpleIntelligentPredictor()
        
        # 測試CHC @ NYY比賽
        try:
            result = predictor.predict_game_intelligent(
                home_team='NYY',
                away_team='CHC',
                game_date=date(2025, 7, 13),
                game_id='777122'
            )
            
            print("✅ 智能預測成功!")
            print(f"📊 投手分析:")
            print(f"   主隊投手: {result['pitcher_analysis']['home_pitcher']['name']} "
                  f"(ERA: {result['pitcher_analysis']['home_pitcher']['era']:.2f}, "
                  f"等級: {result['pitcher_analysis']['home_pitcher']['strength']})")
            print(f"   客隊投手: {result['pitcher_analysis']['away_pitcher']['name']} "
                  f"(ERA: {result['pitcher_analysis']['away_pitcher']['era']:.2f}, "
                  f"等級: {result['pitcher_analysis']['away_pitcher']['strength']})")
            print(f"   對戰類型: {result['pitcher_analysis']['matchup_type']}")
            
            print(f"\n🧠 智能策略: {result['strategy']['name']}")
            print(f"   策略說明: {result['strategy']['description']}")
            
            print(f"\n📈 預測結果:")
            print(f"   預測比分: {result['predicted_away_score']:.1f} - {result['predicted_home_score']:.1f}")
            print(f"   預測總分: {result['total_runs']:.1f}分")
            print(f"   信心度: {result['confidence']:.1%}")
            
            # 檢查實際結果
            game = Game.query.filter_by(game_id='777122').first()
            if game and game.away_score is not None and game.home_score is not None:
                actual_total = game.away_score + game.home_score
                predicted_total = result['total_runs']
                diff = abs(predicted_total - actual_total)
                
                print(f"\n🎯 與實際結果比較:")
                print(f"   實際比分: {game.away_score} - {game.home_score}")
                print(f"   實際總分: {actual_total}分")
                print(f"   預測差異: {diff:.1f}分")
                
                if diff <= 2.0:
                    print(f"   ✅ 預測非常準確!")
                elif diff <= 4.0:
                    print(f"   ⚠️  預測尚可")
                else:
                    print(f"   ❌ 預測偏差較大")
            
        except Exception as e:
            print(f"❌ 智能預測失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_boxscore_pitcher_extractor()
    test_pitcher_stats_summary()
    test_intelligent_prediction_with_boxscore()
