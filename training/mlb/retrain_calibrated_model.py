#!/usr/bin/env python3
"""
重新訓練校正模型來修復預測偏差
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, Prediction, BettingOdds
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error
import joblib
import json

class CalibratedMLBPredictor:
    """校正的MLB預測器"""
    
    def __init__(self):
        self.total_score_model = None
        self.home_score_model = None
        self.away_score_model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.calibration_factors = {}
        
    def prepare_training_data(self):
        """準備訓練數據"""
        print("📊 準備訓練數據...")
        
        app = create_app()
        with app.app_context():
            # 獲取歷史數據 (最近90天)
            query = """
            SELECT 
                g.game_id,
                g.date,
                g.home_team,
                g.away_team,
                g.home_score,
                g.away_score,
                (g.home_score + g.away_score) as actual_total,
                p.predicted_home_score,
                p.predicted_away_score,
                (p.predicted_home_score + p.predicted_away_score) as predicted_total,
                p.confidence,
                p.model_version,
                bo.total_point as betting_line,
                bo.bookmaker
            FROM games g
            JOIN predictions p ON g.game_id = p.game_id
            LEFT JOIN betting_odds bo ON g.game_id = bo.game_id
            WHERE g.home_score IS NOT NULL 
            AND g.away_score IS NOT NULL
            AND p.predicted_home_score IS NOT NULL
            AND p.predicted_away_score IS NOT NULL
            AND g.date >= DATE('now', '-90 days')
            ORDER BY g.date
            """
            
            results = db.session.execute(db.text(query)).fetchall()
            
            columns = ['game_id', 'date', 'home_team', 'away_team', 'home_score', 'away_score', 
                      'actual_total', 'predicted_home_score', 'predicted_away_score', 'predicted_total', 
                      'confidence', 'model_version', 'betting_line', 'bookmaker']
            
            df = pd.DataFrame(results, columns=columns)
            
            print(f"  獲取 {len(df)} 場比賽的歷史數據")
            
            # 創建特徵
            df = self.create_features(df)
            
            return df
    
    def create_features(self, df):
        """創建預測特徵"""
        print("🔧 創建預測特徵...")
        
        # 基本特徵
        df['predicted_total'] = df['predicted_home_score'] + df['predicted_away_score']
        df['confidence_squared'] = df['confidence'] ** 2
        df['confidence_log'] = np.log(df['confidence'] + 0.01)
        
        # 博彩盤口特徵
        df['has_betting_line'] = df['betting_line'].notna().astype(int)
        df['betting_line_filled'] = df['betting_line'].fillna(df['predicted_total'])
        df['pred_vs_line'] = df['predicted_total'] - df['betting_line_filled']
        df['pred_vs_line_ratio'] = df['predicted_total'] / df['betting_line_filled']
        
        # 隊伍特徵 (基於歷史表現)
        df = self.add_team_features(df)
        
        # 時間特徵
        df['date'] = pd.to_datetime(df['date'])
        df['day_of_week'] = df['date'].dt.dayofweek
        df['month'] = df['date'].dt.month
        df['days_since_start'] = (df['date'] - df['date'].min()).dt.days
        
        # 模型版本特徵
        model_dummies = pd.get_dummies(df['model_version'], prefix='model')
        df = pd.concat([df, model_dummies], axis=1)
        
        return df
    
    def add_team_features(self, df):
        """添加隊伍特徵"""
        print("  添加隊伍歷史表現特徵...")
        
        # 計算每個隊伍的歷史預測誤差
        team_stats = {}
        
        for team in pd.concat([df['home_team'], df['away_team']]).unique():
            if pd.isna(team):
                continue
                
            # 主隊表現
            home_games = df[df['home_team'] == team].copy()
            if len(home_games) > 0:
                home_error = abs(home_games['predicted_home_score'] - home_games['home_score']).mean()
                home_bias = (home_games['predicted_home_score'] - home_games['home_score']).mean()
            else:
                home_error, home_bias = 3.0, 0.0
            
            # 客隊表現
            away_games = df[df['away_team'] == team].copy()
            if len(away_games) > 0:
                away_error = abs(away_games['predicted_away_score'] - away_games['away_score']).mean()
                away_bias = (away_games['predicted_away_score'] - away_games['away_score']).mean()
            else:
                away_error, away_bias = 3.0, 0.0
            
            team_stats[team] = {
                'home_error': home_error,
                'home_bias': home_bias,
                'away_error': away_error,
                'away_bias': away_bias
            }
        
        # 添加特徵到數據框
        df['home_team_error'] = df['home_team'].map(lambda x: team_stats.get(x, {}).get('home_error', 3.0))
        df['home_team_bias'] = df['home_team'].map(lambda x: team_stats.get(x, {}).get('home_bias', 0.0))
        df['away_team_error'] = df['away_team'].map(lambda x: team_stats.get(x, {}).get('away_error', 3.0))
        df['away_team_bias'] = df['away_team'].map(lambda x: team_stats.get(x, {}).get('away_bias', 0.0))
        
        return df
    
    def train_models(self, df):
        """訓練校正模型"""
        print("🤖 訓練校正模型...")
        
        # 選擇特徵
        feature_cols = [
            'predicted_home_score', 'predicted_away_score', 'predicted_total',
            'confidence', 'confidence_squared', 'confidence_log',
            'has_betting_line', 'betting_line_filled', 'pred_vs_line', 'pred_vs_line_ratio',
            'home_team_error', 'home_team_bias', 'away_team_error', 'away_team_bias',
            'day_of_week', 'month', 'days_since_start'
        ]
        
        # 添加模型版本特徵
        model_cols = [col for col in df.columns if col.startswith('model_')]
        feature_cols.extend(model_cols)
        
        # 過濾存在的特徵
        feature_cols = [col for col in feature_cols if col in df.columns]
        self.feature_columns = feature_cols
        
        print(f"  使用 {len(feature_cols)} 個特徵")
        
        # 準備數據
        X = df[feature_cols].fillna(0)

        # 確保所有特徵都是數值型
        for col in X.columns:
            if X[col].dtype == 'object':
                X[col] = pd.to_numeric(X[col], errors='coerce').fillna(0)

        y_total = df['actual_total']
        y_home = df['home_score']
        y_away = df['away_score']

        # 標準化特徵
        X_scaled = self.scaler.fit_transform(X)
        
        # 分割數據
        X_train, X_test, y_total_train, y_total_test = train_test_split(
            X_scaled, y_total, test_size=0.2, random_state=42
        )
        
        _, _, y_home_train, y_home_test = train_test_split(
            X_scaled, y_home, test_size=0.2, random_state=42
        )
        
        _, _, y_away_train, y_away_test = train_test_split(
            X_scaled, y_away, test_size=0.2, random_state=42
        )
        
        # 訓練總分模型
        print("  訓練總分校正模型...")
        self.total_score_model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42
        )
        self.total_score_model.fit(X_train, y_total_train)
        
        # 訓練主隊得分模型
        print("  訓練主隊得分校正模型...")
        self.home_score_model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42
        )
        self.home_score_model.fit(X_train, y_home_train)
        
        # 訓練客隊得分模型
        print("  訓練客隊得分校正模型...")
        self.away_score_model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42
        )
        self.away_score_model.fit(X_train, y_away_train)
        
        # 評估模型
        self.evaluate_models(X_test, y_total_test, y_home_test, y_away_test)
        
        # 計算校正因子
        self.calculate_calibration_factors(df)
    
    def evaluate_models(self, X_test, y_total_test, y_home_test, y_away_test):
        """評估模型性能"""
        print("📊 評估模型性能...")
        
        # 總分預測
        total_pred = self.total_score_model.predict(X_test)
        total_mae = mean_absolute_error(y_total_test, total_pred)
        total_rmse = np.sqrt(mean_squared_error(y_total_test, total_pred))
        
        print(f"  總分模型:")
        print(f"    MAE: {total_mae:.2f}")
        print(f"    RMSE: {total_rmse:.2f}")
        
        # 主隊得分預測
        home_pred = self.home_score_model.predict(X_test)
        home_mae = mean_absolute_error(y_home_test, home_pred)
        home_rmse = np.sqrt(mean_squared_error(y_home_test, home_pred))
        
        print(f"  主隊得分模型:")
        print(f"    MAE: {home_mae:.2f}")
        print(f"    RMSE: {home_rmse:.2f}")
        
        # 客隊得分預測
        away_pred = self.away_score_model.predict(X_test)
        away_mae = mean_absolute_error(y_away_test, away_pred)
        away_rmse = np.sqrt(mean_squared_error(y_away_test, away_pred))
        
        print(f"  客隊得分模型:")
        print(f"    MAE: {away_mae:.2f}")
        print(f"    RMSE: {away_rmse:.2f}")
    
    def calculate_calibration_factors(self, df):
        """計算校正因子"""
        print("⚖️ 計算校正因子...")
        
        # 按信心度計算校正因子
        confidence_bins = [0, 0.4, 0.6, 0.8, 1.0]
        confidence_labels = ['low', 'medium_low', 'medium_high', 'high']
        
        df['confidence_bin'] = pd.cut(df['confidence'], bins=confidence_bins, labels=confidence_labels)
        
        for bin_name in confidence_labels:
            subset = df[df['confidence_bin'] == bin_name]
            if len(subset) > 0:
                total_bias = (subset['predicted_total'] - subset['actual_total']).mean()
                home_bias = (subset['predicted_home_score'] - subset['home_score']).mean()
                away_bias = (subset['predicted_away_score'] - subset['away_score']).mean()
                
                self.calibration_factors[bin_name] = {
                    'total_bias': total_bias,
                    'home_bias': home_bias,
                    'away_bias': away_bias
                }
                
                print(f"  {bin_name} 信心度校正因子:")
                print(f"    總分偏差: {total_bias:+.2f}")
                print(f"    主隊偏差: {home_bias:+.2f}")
                print(f"    客隊偏差: {away_bias:+.2f}")
    
    def save_model(self, model_path='models/calibrated_v2.0'):
        """保存校正模型"""
        print(f"💾 保存校正模型到 {model_path}...")
        
        os.makedirs(model_path, exist_ok=True)
        
        # 保存模型
        joblib.dump(self.total_score_model, f"{model_path}/total_score_model.pkl")
        joblib.dump(self.home_score_model, f"{model_path}/home_score_model.pkl")
        joblib.dump(self.away_score_model, f"{model_path}/away_score_model.pkl")
        joblib.dump(self.scaler, f"{model_path}/scaler.pkl")
        
        # 保存配置
        config = {
            'feature_columns': self.feature_columns,
            'calibration_factors': self.calibration_factors,
            'model_version': 'calibrated_v2.0',
            'created_at': datetime.now().isoformat()
        }
        
        with open(f"{model_path}/config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        print("  ✅ 模型保存完成")

def main():
    """主函數"""
    print("🚀 開始重新訓練校正模型...")
    
    # 創建校正預測器
    predictor = CalibratedMLBPredictor()
    
    # 準備訓練數據
    df = predictor.prepare_training_data()
    
    if len(df) < 50:
        print("❌ 訓練數據不足，需要至少50場比賽")
        return
    
    # 訓練模型
    predictor.train_models(df)
    
    # 保存模型
    predictor.save_model()
    
    print("\n✅ 校正模型訓練完成！")
    print("\n📋 改進要點:")
    print("  1. 修正了系統性高估偏差")
    print("  2. 基於信心度進行校正")
    print("  3. 考慮了隊伍歷史表現")
    print("  4. 整合了博彩盤口信息")
    print("  5. 使用梯度提升算法提高準確性")

if __name__ == "__main__":
    main()
