#!/usr/bin/env python3
"""
測試賠率UI功能
檢查賠率頁面的API和前端功能
"""

from app import create_app
import requests
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_odds_ui():
    """測試賠率UI功能"""
    print("🔍 測試賠率UI功能...")
    print("=" * 80)
    
    app = create_app()
    
    with app.test_client() as client:
        
        # 1. 測試賠率頁面是否可以訪問
        print("\n📄 測試賠率頁面可訪問性...")
        response = client.get('/unified/betting_odds')
        print(f"   狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 賠率頁面可以正常訪問")
            print(f"   📄 頁面內容長度: {len(response.data)} bytes")
        else:
            print(f"   ❌ 賠率頁面無法訪問: {response.status_code}")
            return
        
        # 2. 測試賠率摘要API
        print("\n📊 測試賠率摘要API...")
        response = client.get('/unified/api/betting_odds/summary')
        print(f"   狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                if data.get('success'):
                    stats = data.get('stats', {})
                    print("   ✅ 摘要API正常工作")
                    print(f"      總記錄數: {stats.get('total_records', 0)}")
                    print(f"      日期範圍: {stats.get('earliest_date')} 到 {stats.get('latest_date')}")
                    print(f"      博彩商數量: {len(stats.get('bookmakers', []))}")
                    print(f"      市場類型: {stats.get('market_types', [])}")
                else:
                    print(f"   ❌ 摘要API返回錯誤: {data.get('error', '未知錯誤')}")
            except Exception as e:
                print(f"   ❌ 摘要API數據解析失敗: {e}")
        else:
            print(f"   ❌ 摘要API無法訪問: {response.status_code}")
        
        # 3. 測試賠率查詢API
        print("\n🔍 測試賠率查詢API...")
        
        # 測試基本查詢
        query_data = {
            "date_from": "2025-08-25",
            "date_to": "2025-08-30",
            "limit": 10
        }
        
        response = client.post('/unified/api/betting_odds/query',
                             json=query_data,
                             content_type='application/json')
        
        print(f"   狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                if data.get('success'):
                    print("   ✅ 查詢API正常工作")
                    print(f"      查詢結果數量: {len(data.get('data', []))}")
                    
                    # 顯示前幾條記錄
                    records = data.get('data', [])
                    for i, record in enumerate(records[:3]):
                        print(f"      記錄 {i+1}: {record.get('away_team')}@{record.get('home_team')} "
                              f"({record.get('game_date')}) - {record.get('bookmaker')} {record.get('market_type')}")
                        if record.get('market_type') == 'totals':
                            print(f"                大小分: {record.get('total_point')} (O{record.get('over_price')}/U{record.get('under_price')})")
                        elif record.get('market_type') == 'spreads':
                            print(f"                讓分盤: {record.get('home_spread_point')}/{record.get('away_spread_point')}")
                    
                    stats = data.get('stats', {})
                    print(f"      統計: 總記錄 {stats.get('total_records', 0)}")
                else:
                    print(f"   ❌ 查詢API返回錯誤: {data.get('error', '未知錯誤')}")
            except Exception as e:
                print(f"   ❌ 查詢API數據解析失敗: {e}")
        else:
            print(f"   ❌ 查詢API無法訪問: {response.status_code}")
        
        # 4. 測試特定球隊查詢
        print("\n🏟️ 測試特定球隊查詢...")
        
        team_query = {
            "date_from": "2025-08-25",
            "date_to": "2025-08-30",
            "team": "STL",
            "limit": 5
        }
        
        response = client.post('/unified/api/betting_odds/query',
                             json=team_query,
                             content_type='application/json')
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                if data.get('success'):
                    records = data.get('data', [])
                    print(f"   ✅ STL球隊查詢成功，找到 {len(records)} 條記錄")
                    for record in records[:3]:
                        print(f"      - {record.get('away_team')}@{record.get('home_team')} ({record.get('game_date')})")
                else:
                    print(f"   ❌ 球隊查詢失敗: {data.get('error')}")
            except Exception as e:
                print(f"   ❌ 球隊查詢數據解析失敗: {e}")
        
        # 5. 測試自動更新功能
        print("\n🔄 測試自動更新功能...")
        
        update_data = {
            "days": 1,
            "force_update": False
        }
        
        response = client.post('/unified/api/betting_odds/auto_update',
                              json=update_data,
                              content_type='application/json')
        
        print(f"   狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                if data.get('success'):
                    print("   ✅ 自動更新功能正常")
                    print(f"      處理比賽數: {data.get('processed_games', 0)}")
                    print(f"      更新記錄數: {data.get('updated_count', 0)}")
                    print(f"      消息: {data.get('message', '')}")
                else:
                    print(f"   ⚠️ 自動更新完成但有提示: {data.get('error', '')}")
            except Exception as e:
                print(f"   ❌ 自動更新數據解析失敗: {e}")
        else:
            print(f"   ❌ 自動更新API無法訪問: {response.status_code}")

def test_web_interface():
    """測試Web介面"""
    print("\n🌐 測試Web介面可訪問性...")
    
    try:
        # 嘗試訪問實際運行的服務器
        base_url = "http://localhost:5000"
        
        response = requests.get(f"{base_url}/unified/betting_odds", timeout=5)
        
        if response.status_code == 200:
            print("   ✅ Web介面可以正常訪問")
            print(f"      URL: {base_url}/unified/betting_odds")
            print("   建議：在瀏覽器中打開此頁面測試完整功能")
        else:
            print(f"   ❌ Web介面無法訪問: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ⚠️ 無法連接到Web服務器: {e}")
        print("   建議：確認服務器是否正在運行")

if __name__ == "__main__":
    test_odds_ui()
    test_web_interface()