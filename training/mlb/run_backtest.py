#!/usr/bin/env python3
"""
MLB預測系統回測驗證 - 便捷使用腳本
使用方法：
1. 快速回測最近7天：python3 run_backtest.py
2. 指定日期範圍：python3 run_backtest.py --start 2025-07-10 --end 2025-07-20
3. 指定模型：python3 run_backtest.py --models Core_v1.0,calibrated_v2.0
4. 詳細報告：python3 run_backtest.py --detailed
"""

import argparse
import sys
import os
from datetime import date, timedelta
from backtest_validation_system import BacktestValidationSystem

def main():
    parser = argparse.ArgumentParser(description='MLB預測系統回測驗證')
    
    # 日期參數
    parser.add_argument('--start', type=str, help='開始日期 (YYYY-MM-DD)')
    parser.add_argument('--end', type=str, help='結束日期 (YYYY-MM-DD)')
    parser.add_argument('--days', type=int, default=7, help='回測天數 (默認7天)')
    
    # 模型參數
    parser.add_argument('--models', type=str, 
                       default='Core_v1.0,calibrated_v2.0,legacy',
                       help='要分析的模型，用逗號分隔')
    
    # 輸出參數
    parser.add_argument('--detailed', action='store_true', help='生成詳細報告')
    parser.add_argument('--output', type=str, help='輸出文件名')
    parser.add_argument('--quiet', action='store_true', help='靜默模式，只輸出結果')
    
    args = parser.parse_args()
    
    # 設置日期範圍
    if args.start and args.end:
        start_date = args.start
        end_date = args.end
    else:
        end_date = date.today().isoformat()
        start_date = (date.today() - timedelta(days=args.days)).isoformat()
    
    # 解析模型列表
    model_list = [m.strip() for m in args.models.split(',')]
    
    if not args.quiet:
        print("🚀 MLB預測系統回測驗證")
        print("=" * 50)
        print(f"📅 分析期間: {start_date} 到 {end_date}")
        print(f"🤖 分析模型: {', '.join(model_list)}")
        print("=" * 50)
    
    # 創建回測系統
    backtest_system = BacktestValidationSystem()
    
    # 運行分析
    results = backtest_system.run_backtest_analysis(
        start_date=start_date,
        end_date=end_date,
        model_versions=model_list
    )
    
    # 顯示簡要結果
    if not args.quiet:
        print("\n📊 回測結果摘要:")
        print("-" * 50)
    
    for model_name, model_results in results.items():
        if not model_results:
            if not args.quiet:
                print(f"❌ {model_name}: 無數據")
            continue
        
        total = model_results.get('total_predictions', 0)
        win_loss = model_results.get('win_loss_accuracy', {})
        score_acc = model_results.get('score_accuracy', {})
        
        if not args.quiet:
            print(f"\n🎯 {model_name}:")
            print(f"   預測場次: {total}")
            if win_loss:
                print(f"   勝負準確率: {win_loss.get('accuracy', 0)}% ({win_loss.get('correct', 0)}/{win_loss.get('total', 0)})")
            if score_acc:
                print(f"   平均誤差: {score_acc.get('mean_total_error', 0):.2f} 分")
        else:
            # 靜默模式，只輸出關鍵數據
            accuracy = win_loss.get('accuracy', 0) if win_loss else 0
            error = score_acc.get('mean_total_error', 0) if score_acc else 0
            print(f"{model_name},{total},{accuracy},{error:.2f}")
    
    # 生成詳細報告
    if args.detailed or args.output:
        output_file = args.output or f"backtest_report_{start_date}_{end_date}.md"
        report_content = backtest_system.generate_backtest_report(results, output_file)
        
        if not args.quiet:
            print(f"\n📋 詳細報告已保存到: {output_file}")
    
    if not args.quiet:
        print("\n✅ 回測驗證完成！")

if __name__ == "__main__":
    main()
