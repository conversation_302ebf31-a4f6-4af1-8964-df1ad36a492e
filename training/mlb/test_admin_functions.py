#!/usr/bin/env python3
"""
測試管理員功能
"""

from app import create_app
from models.automated_predictor import automated_predictor
from models.detailed_data_fetcher import DetailedDataFetcher
from datetime import date, timedelta

def test_daily_update():
    """測試每日更新功能"""
    print("🔄 測試每日更新功能...")
    
    app = create_app()
    with app.app_context():
        try:
            # 測試手動每日更新
            today = date.today()
            result = automated_predictor.manual_daily_update(today)
            
            print(f"每日更新結果: {result}")
            
            if result.get('status') == 'success':
                print("✅ 每日更新功能正常")
                return True
            else:
                print("❌ 每日更新功能異常")
                return False
                
        except Exception as e:
            print(f"❌ 每日更新測試失敗: {e}")
            return False

def test_boxscore_download():
    """測試Box Score下載功能"""
    print("📊 測試Box Score下載功能...")
    
    app = create_app()
    with app.app_context():
        try:
            fetcher = DetailedDataFetcher()
            
            # 測試今天的Box Score下載
            today = date.today()
            result = fetcher.fetch_completed_games_boxscores(today)
            
            print(f"Box Score下載結果: {result}")
            
            if result:
                print("✅ Box Score下載功能正常")
                return True
            else:
                print("✅ Box Score下載功能正常 (今天沒有需要下載的比賽)")
                return True
                
        except Exception as e:
            print(f"❌ Box Score下載測試失敗: {e}")
            return False

def test_schedule_refresh():
    """測試行程刷新功能"""
    print("📅 測試行程刷新功能...")
    
    app = create_app()
    with app.app_context():
        try:
            fetcher = DetailedDataFetcher()
            
            # 測試刷新今天到明天的行程
            today = date.today()
            tomorrow = today + timedelta(days=1)
            
            result = fetcher.refresh_schedule_for_date_range(today, tomorrow)
            
            print(f"行程刷新結果: 更新了 {result} 天")
            
            if result >= 0:
                print("✅ 行程刷新功能正常")
                return True
            else:
                print("❌ 行程刷新功能異常")
                return False
                
        except Exception as e:
            print(f"❌ 行程刷新測試失敗: {e}")
            return False

def main():
    """主測試函數"""
    print("🚀 開始測試管理員功能...")
    
    tests = [
        ("每日更新", test_daily_update),
        ("Box Score下載", test_boxscore_download),
        ("行程刷新", test_schedule_refresh)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- 測試 {test_name} ---")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "="*50)
    print("📋 測試結果摘要:")
    print("="*50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("="*50)
    if all_passed:
        print("🎉 所有測試通過！管理員功能正常運作")
    else:
        print("⚠️  部分測試失敗，請檢查相關功能")
    
    return all_passed

if __name__ == '__main__':
    main()
