#!/usr/bin/env python3
"""
測試簡化版智能預測系統
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game
from models.simple_intelligent_predictor import SimpleIntelligentPredictor

def test_simple_intelligent_predictions():
    """測試簡化版智能預測系統"""
    app = create_app()
    
    with app.app_context():
        print("🧠 測試簡化版智能預測系統")
        print("=" * 80)
        print("這個系統解決了:")
        print("1. 模型特徵維度不匹配問題")
        print("2. StandardScaler未訓練問題") 
        print("3. 數據庫列缺失問題")
        print("4. 複雜依賴問題")
        print()
        
        predictor = SimpleIntelligentPredictor()
        
        # 測試案例
        test_cases = [
            {
                'home_team': 'NYY',
                'away_team': 'CHC', 
                'game_date': date(2025, 7, 13),
                'game_id': '777122',
                'description': 'CHC @ NYY - 測試實際比賽預測'
            },
            {
                'home_team': 'SD',
                'away_team': 'PHI',
                'game_date': date(2025, 7, 13),
                'game_id': None,
                'description': 'PHI @ SD - 測試Zack Wheeler vs Yu Darvish'
            },
            {
                'home_team': 'BOS',
                'away_team': 'TB',
                'game_date': date(2025, 7, 10),
                'game_id': '777170',
                'description': 'TB @ BOS - 測試已完成比賽'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🎯 測試案例 {i}: {test_case['description']}")
            print("-" * 60)
            
            try:
                # 執行簡化智能預測
                result = predictor.predict_game_intelligent(
                    home_team=test_case['home_team'],
                    away_team=test_case['away_team'],
                    game_date=test_case['game_date'],
                    game_id=test_case['game_id']
                )
                
                # 顯示結果
                print_prediction_result(result, test_case)
                
                # 如果有實際結果，比較準確性
                if test_case['game_id']:
                    compare_with_actual_result(result, test_case['game_id'])
                
            except Exception as e:
                print(f"❌ 簡化智能預測失敗: {e}")
                import traceback
                traceback.print_exc()

def print_prediction_result(result: dict, test_case: dict):
    """打印預測結果"""
    pitcher_analysis = result['pitcher_analysis']
    strategy = result['strategy']
    
    print(f"📊 投手分析:")
    print(f"   主隊投手: {pitcher_analysis['home_pitcher']['name']} "
          f"(ERA: {pitcher_analysis['home_pitcher']['era']:.2f}, "
          f"等級: {pitcher_analysis['home_pitcher']['strength']})")
    print(f"   客隊投手: {pitcher_analysis['away_pitcher']['name']} "
          f"(ERA: {pitcher_analysis['away_pitcher']['era']:.2f}, "
          f"等級: {pitcher_analysis['away_pitcher']['strength']})")
    print(f"   對戰類型: {pitcher_analysis['matchup_type']}")
    
    print(f"\n🧠 智能策略: {strategy['name']}")
    print(f"   策略類型: {strategy['type']}")
    print(f"   目標總分: {strategy['target_total']}分")
    print(f"   策略說明: {strategy['description']}")
    
    print(f"\n📈 預測結果:")
    print(f"   預測比分: {result['predicted_away_score']:.1f} - {result['predicted_home_score']:.1f}")
    print(f"   預測總分: {result['total_runs']:.1f}分")
    print(f"   信心度: {result['confidence']:.1%}")
    
    # 策略合理性分析
    analyze_strategy_effectiveness(pitcher_analysis, strategy, result)

def compare_with_actual_result(result: dict, game_id: str):
    """與實際結果比較"""
    try:
        game = Game.query.filter_by(game_id=game_id).first()
        
        if game and game.away_score is not None and game.home_score is not None:
            actual_total = game.away_score + game.home_score
            predicted_total = result['total_runs']
            total_diff = abs(predicted_total - actual_total)
            
            print(f"\n🎯 與實際結果比較:")
            print(f"   實際比分: {game.away_score} - {game.home_score}")
            print(f"   實際總分: {actual_total}分")
            print(f"   預測總分: {predicted_total:.1f}分")
            print(f"   總分差異: {total_diff:.1f}分")
            
            if total_diff <= 2.0:
                print(f"   ✅ 預測非常準確 (差異 ≤ 2分)")
            elif total_diff <= 4.0:
                print(f"   ⚠️  預測尚可 (差異 ≤ 4分)")
            else:
                print(f"   ❌ 預測偏差較大 (差異 > 4分)")
        else:
            print(f"\n⏳ 比賽尚未完成或數據未更新")
            
    except Exception as e:
        print(f"\n❌ 比較實際結果失敗: {e}")

def analyze_strategy_effectiveness(pitcher_analysis: dict, strategy: dict, result: dict):
    """分析策略有效性"""
    print(f"\n🔍 策略有效性分析:")
    
    matchup_type = pitcher_analysis['matchup_type']
    predicted_total = result['total_runs']
    
    if matchup_type == "王牌對決":
        if predicted_total <= 8.0:
            print(f"   ✅ 策略有效: 王牌對決成功預測低分 ({predicted_total:.1f}分 ≤ 8.0分)")
        else:
            print(f"   ⚠️  策略可疑: 王牌對決但預測高分 ({predicted_total:.1f}分 > 8.0分)")
            
    elif matchup_type == "打擊戰":
        if predicted_total >= 11.0:
            print(f"   ✅ 策略有效: 打擊戰成功預測高分 ({predicted_total:.1f}分 ≥ 11.0分)")
        else:
            print(f"   ⚠️  策略可疑: 打擊戰但預測低分 ({predicted_total:.1f}分 < 11.0分)")
            
    elif matchup_type == "強弱對戰":
        if 8.0 <= predicted_total <= 11.0:
            print(f"   ✅ 策略有效: 強弱對戰預測中等分數 ({predicted_total:.1f}分)")
        else:
            print(f"   ⚠️  策略調整: 強弱對戰預測極端分數 ({predicted_total:.1f}分)")
            
    else:  # 普通對戰
        if 8.0 <= predicted_total <= 11.0:
            print(f"   ✅ 策略有效: 普通對戰預測標準分數 ({predicted_total:.1f}分)")
        else:
            print(f"   ⚠️  策略調整: 普通對戰預測非標準分數 ({predicted_total:.1f}分)")

def test_pitcher_analysis():
    """測試投手分析功能"""
    print(f"\n\n🔬 測試投手分析功能")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        predictor = SimpleIntelligentPredictor()
        
        # 測試不同的投手組合
        test_scenarios = [
            {
                'home_team': 'NYY',
                'away_team': 'CHC',
                'game_date': date(2025, 7, 13),
                'game_id': '777122',
                'expected_home': 'Will Warren',
                'expected_away': 'Shota Imanaga'
            },
            {
                'home_team': 'SD', 
                'away_team': 'PHI',
                'game_date': date(2025, 7, 13),
                'game_id': None,
                'expected_home': 'Yu Darvish',
                'expected_away': 'Zack Wheeler'
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n🎯 分析 {scenario['away_team']} @ {scenario['home_team']}:")
            
            try:
                analysis = predictor._analyze_pitcher_quality_simple(
                    scenario['home_team'],
                    scenario['away_team'], 
                    scenario['game_date'],
                    scenario['game_id']
                )
                
                print(f"   主隊投手: {analysis['home_pitcher']['name']} "
                      f"(ERA: {analysis['home_pitcher']['era']:.2f}, "
                      f"質量: {analysis['home_pitcher']['quality']:.1f}, "
                      f"等級: {analysis['home_pitcher']['strength']})")
                      
                print(f"   客隊投手: {analysis['away_pitcher']['name']} "
                      f"(ERA: {analysis['away_pitcher']['era']:.2f}, "
                      f"質量: {analysis['away_pitcher']['quality']:.1f}, "
                      f"等級: {analysis['away_pitcher']['strength']})")
                      
                print(f"   對戰類型: {analysis['matchup_type']}")
                print(f"   平均ERA: {analysis['average_era']:.2f}")
                
            except Exception as e:
                print(f"   ❌ 分析失敗: {e}")

def test_strategy_selection():
    """測試策略選擇邏輯"""
    print(f"\n\n🧪 測試策略選擇邏輯")
    print("=" * 80)
    
    predictor = SimpleIntelligentPredictor()
    
    # 模擬不同投手組合
    test_scenarios = [
        {
            'name': '王牌對決測試',
            'pitcher_analysis': {
                'home_pitcher': {'era': 2.20, 'quality': 85, 'strength': '王牌'},
                'away_pitcher': {'era': 2.50, 'quality': 82, 'strength': '王牌'},
                'matchup_type': '王牌對決',
                'average_era': 2.35,
                'average_quality': 83.5
            },
            'expected_strategy': 'low_scoring'
        },
        {
            'name': '打擊戰測試',
            'pitcher_analysis': {
                'home_pitcher': {'era': 5.50, 'quality': 30, 'strength': '弱勢'},
                'away_pitcher': {'era': 6.00, 'quality': 25, 'strength': '弱勢'},
                'matchup_type': '打擊戰',
                'average_era': 5.75,
                'average_quality': 27.5
            },
            'expected_strategy': 'high_scoring'
        },
        {
            'name': 'CHC @ NYY 實際案例',
            'pitcher_analysis': {
                'home_pitcher': {'era': 10.32, 'quality': 22, 'strength': '弱勢'},
                'away_pitcher': {'era': 4.50, 'quality': 64, 'strength': '普通'},
                'matchup_type': '普通對戰',
                'average_era': 7.41,
                'average_quality': 43.0
            },
            'expected_strategy': 'standard'
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🎯 {scenario['name']}:")
        
        strategy = predictor._determine_prediction_strategy(scenario['pitcher_analysis'])
        
        print(f"   投手組合: {scenario['pitcher_analysis']['home_pitcher']['strength']} vs {scenario['pitcher_analysis']['away_pitcher']['strength']}")
        print(f"   對戰類型: {scenario['pitcher_analysis']['matchup_type']}")
        print(f"   選擇策略: {strategy['name']} ({strategy['type']})")
        print(f"   目標總分: {strategy['target_total']}分")
        print(f"   策略說明: {strategy['description']}")
        
        if strategy['type'] == scenario['expected_strategy']:
            print(f"   ✅ 策略選擇正確")
        else:
            print(f"   ❌ 策略選擇錯誤 (期望: {scenario['expected_strategy']})")

if __name__ == "__main__":
    test_simple_intelligent_predictions()
    test_pitcher_analysis()
    test_strategy_selection()
