#!/usr/bin/env python3
"""
修復數據庫事務問題的腳本
"""

import os
import sys
import sqlite3
from datetime import datetime

def fix_database_transaction():
    """修復數據庫事務問題"""
    
    db_path = 'instance/mlb_data.db'
    journal_path = 'instance/mlb_data.db-journal'
    
    print("🔧 開始修復數據庫事務問題...")
    
    # 1. 檢查文件狀態
    print(f"數據庫文件存在: {os.path.exists(db_path)}")
    print(f"Journal文件存在: {os.path.exists(journal_path)}")
    
    # 2. 刪除journal文件（如果存在）
    if os.path.exists(journal_path):
        try:
            os.remove(journal_path)
            print("✅ 已刪除journal文件")
        except Exception as e:
            print(f"❌ 刪除journal文件失敗: {e}")
            return False
    
    # 3. 測試數據庫連接
    try:
        conn = sqlite3.connect(db_path, timeout=30)
        cursor = conn.cursor()
        
        # 檢查數據庫完整性
        cursor.execute('PRAGMA integrity_check')
        result = cursor.fetchone()
        print(f"數據庫完整性檢查: {result[0]}")
        
        if result[0] != 'ok':
            print("❌ 數據庫完整性檢查失敗")
            return False
        
        # 檢查predictions表
        cursor.execute('SELECT COUNT(*) FROM predictions')
        count = cursor.fetchone()[0]
        print(f"Predictions表記錄數: {count}")
        
        # 測試事務操作
        cursor.execute('BEGIN TRANSACTION')
        cursor.execute('SELECT 1')
        cursor.execute('ROLLBACK')
        print("✅ 事務操作測試成功")
        
        # 設置WAL模式以避免鎖定問題
        cursor.execute('PRAGMA journal_mode=WAL')
        wal_mode = cursor.fetchone()[0]
        print(f"設置WAL模式: {wal_mode}")
        
        conn.commit()
        conn.close()
        
        print("✅ 數據庫修復完成")
        return True
        
    except Exception as e:
        print(f"❌ 數據庫修復失敗: {e}")
        return False

def test_flask_database_connection():
    """測試Flask應用的數據庫連接"""
    
    print("\n🧪 測試Flask數據庫連接...")
    
    try:
        # 設置Flask應用上下文
        sys.path.append('.')
        from app import create_app
        from models.database import db, Prediction
        
        app = create_app()
        
        with app.app_context():
            # 測試查詢
            prediction_count = Prediction.query.count()
            print(f"✅ Flask查詢成功，預測記錄數: {prediction_count}")
            
            # 測試事務
            from sqlalchemy import text
            db.session.execute(text('SELECT 1'))
            db.session.commit()
            print("✅ Flask事務測試成功")
            
        return True
        
    except Exception as e:
        print(f"❌ Flask數據庫連接測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("數據庫事務修復工具")
    print("=" * 60)
    
    # 修復數據庫
    if fix_database_transaction():
        # 測試Flask連接
        test_flask_database_connection()
        print("\n✅ 數據庫修復完成，可以繼續使用預測系統")
    else:
        print("\n❌ 數據庫修復失敗，請檢查數據庫文件")
