#!/usr/bin/env python3
"""
Boxscore 數據收集器
從 MLB API 獲取詳細的 boxscore 數據，包括投手表現、打者表現等
"""

import sys
sys.path.append('.')

import requests
import json
import time
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional
import logging

# Import removed to avoid circular dependency - using current_app when needed
from models.database import db, Game

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BoxscoreDataCollector:
    """Boxscore 數據收集器"""
    
    def __init__(self):
        self.base_url = "https://statsapi.mlb.com/api/v1"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MLB-Prediction-System/1.0'
        })
        
    def get_game_boxscore(self, game_pk: str) -> Optional[Dict]:
        """獲取比賽的 boxscore 數據"""
        try:
            url = f"{self.base_url}/game/{game_pk}/boxscore"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"獲取 boxscore 失敗 {game_pk}: {e}")
            return None
    
    def extract_pitcher_stats(self, boxscore: Dict, team: str) -> Dict:
        """從 boxscore 提取投手統計數據"""
        pitcher_stats = {
            'starters': [],
            'relievers': [],
            'team_totals': {}
        }
        
        try:
            teams_data = boxscore.get('teams', {})
            team_data = teams_data.get(team, {})  # 'home' or 'away'
            
            if 'players' in team_data:
                players = team_data['players']
                
                for player_id, player_data in players.items():
                    if player_data.get('position', {}).get('code') == '1':  # 投手代碼
                        stats = player_data.get('stats', {}).get('pitching', {})
                        
                        if stats:
                            pitcher_info = {
                                'player_id': player_id,
                                'name': player_data.get('person', {}).get('fullName', ''),
                                'innings_pitched': stats.get('inningsPitched', '0'),
                                'hits': stats.get('hits', 0),
                                'runs': stats.get('runs', 0),
                                'earned_runs': stats.get('earnedRuns', 0),
                                'walks': stats.get('baseOnBalls', 0),
                                'strikeouts': stats.get('strikeOuts', 0),
                                'home_runs': stats.get('homeRuns', 0),
                                'pitches': stats.get('numberOfPitches', 0),
                                'strikes': stats.get('strikes', 0),
                                'era': stats.get('era', '0.00'),
                                'whip': stats.get('whip', '0.00'),
                                'is_starter': stats.get('gamesStarted', 0) > 0
                            }
                            
                            if pitcher_info['is_starter']:
                                pitcher_stats['starters'].append(pitcher_info)
                            else:
                                pitcher_stats['relievers'].append(pitcher_info)
            
            # 團隊投手統計
            team_stats = team_data.get('teamStats', {}).get('pitching', {})
            if team_stats:
                pitcher_stats['team_totals'] = {
                    'innings_pitched': team_stats.get('inningsPitched', '0'),
                    'hits': team_stats.get('hits', 0),
                    'runs': team_stats.get('runs', 0),
                    'earned_runs': team_stats.get('earnedRuns', 0),
                    'walks': team_stats.get('baseOnBalls', 0),
                    'strikeouts': team_stats.get('strikeOuts', 0),
                    'home_runs': team_stats.get('homeRuns', 0),
                    'era': team_stats.get('era', '0.00')
                }
                
        except Exception as e:
            logger.error(f"提取投手數據失敗: {e}")
            
        return pitcher_stats
    
    def extract_batter_stats(self, boxscore: Dict, team: str) -> Dict:
        """從 boxscore 提取打者統計數據"""
        batter_stats = {
            'batters': [],
            'team_totals': {}
        }
        
        try:
            teams_data = boxscore.get('teams', {})
            team_data = teams_data.get(team, {})
            
            if 'players' in team_data:
                players = team_data['players']
                
                for player_id, player_data in players.items():
                    stats = player_data.get('stats', {}).get('batting', {})
                    
                    if stats:
                        batter_info = {
                            'player_id': player_id,
                            'name': player_data.get('person', {}).get('fullName', ''),
                            'position': player_data.get('position', {}).get('abbreviation', ''),
                            'at_bats': stats.get('atBats', 0),
                            'runs': stats.get('runs', 0),
                            'hits': stats.get('hits', 0),
                            'rbi': stats.get('rbi', 0),
                            'doubles': stats.get('doubles', 0),
                            'triples': stats.get('triples', 0),
                            'home_runs': stats.get('homeRuns', 0),
                            'walks': stats.get('baseOnBalls', 0),
                            'strikeouts': stats.get('strikeOuts', 0),
                            'batting_average': stats.get('avg', '0.000'),
                            'obp': stats.get('obp', '0.000'),
                            'slg': stats.get('slg', '0.000')
                        }
                        batter_stats['batters'].append(batter_info)
            
            # 團隊打擊統計
            team_stats = team_data.get('teamStats', {}).get('batting', {})
            if team_stats:
                batter_stats['team_totals'] = {
                    'at_bats': team_stats.get('atBats', 0),
                    'runs': team_stats.get('runs', 0),
                    'hits': team_stats.get('hits', 0),
                    'rbi': team_stats.get('rbi', 0),
                    'doubles': team_stats.get('doubles', 0),
                    'triples': team_stats.get('triples', 0),
                    'home_runs': team_stats.get('homeRuns', 0),
                    'walks': team_stats.get('baseOnBalls', 0),
                    'strikeouts': team_stats.get('strikeOuts', 0),
                    'batting_average': team_stats.get('avg', '0.000'),
                    'obp': team_stats.get('obp', '0.000'),
                    'slg': team_stats.get('slg', '0.000')
                }
                
        except Exception as e:
            logger.error(f"提取打者數據失敗: {e}")
            
        return batter_stats
    
    def get_detailed_game_analysis(self, game_id: str) -> Optional[Dict]:
        """獲取比賽的詳細分析數據"""
        from flask import current_app
        with current_app.app_context():
            game = Game.query.filter_by(game_id=game_id).first()
            if not game:
                logger.error(f"找不到比賽: {game_id}")
                return None
            
            # 嘗試從 game_id 提取 MLB API 的 game_pk
            game_pk = self._extract_game_pk(game_id)
            if not game_pk:
                logger.error(f"無法從 game_id 提取 game_pk: {game_id}")
                return None
            
            # 獲取 boxscore 數據
            boxscore = self.get_game_boxscore(game_pk)
            if not boxscore:
                return None
            
            # 提取詳細統計
            analysis = {
                'game_id': game_id,
                'game_pk': game_pk,
                'date': game.date.isoformat(),
                'home_team': game.home_team,
                'away_team': game.away_team,
                'final_score': {
                    'home': game.home_score,
                    'away': game.away_score
                }
            }
            
            # 主隊投手數據
            analysis['home_pitching'] = self.extract_pitcher_stats(boxscore, 'home')
            
            # 客隊投手數據
            analysis['away_pitching'] = self.extract_pitcher_stats(boxscore, 'away')
            
            # 主隊打擊數據
            analysis['home_batting'] = self.extract_batter_stats(boxscore, 'home')
            
            # 客隊打擊數據
            analysis['away_batting'] = self.extract_batter_stats(boxscore, 'away')
            
            return analysis
    
    def _extract_game_pk(self, game_id: str) -> Optional[str]:
        """從 game_id 提取 MLB API 的 game_pk"""
        # 這裡需要根據你們的 game_id 格式來實作
        # 通常 MLB API 使用數字 ID，而你們可能使用自定義格式
        
        # 如果 game_id 已經是純數字，直接返回
        if game_id.isdigit():
            return game_id
        
        # 如果包含日期信息，可能需要查詢 MLB API 來獲取對應的 game_pk
        # 這需要額外的實作來映射你們的 game_id 到 MLB 的 game_pk
        
        logger.warning(f"需要實作 game_id 到 game_pk 的映射: {game_id}")
        return None
    
    def analyze_pitcher_home_away_from_boxscore(self, pitcher_name: str, days_back: int = 365) -> Dict:
        """基於 boxscore 數據分析投手主客場表現"""
        from flask import current_app
        with current_app.app_context():
            cutoff_date = datetime.now().date() - timedelta(days=days_back)
            
            # 獲取包含該投手的比賽
            games = Game.query.filter(
                Game.date >= cutoff_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None)
            ).all()
            
            home_stats = []
            away_stats = []
            
            for game in games:
                try:
                    # 獲取詳細數據
                    analysis = self.get_detailed_game_analysis(game.game_id)
                    if not analysis:
                        continue
                    
                    # 檢查主隊投手
                    home_starters = analysis.get('home_pitching', {}).get('starters', [])
                    for pitcher in home_starters:
                        if pitcher_name.lower() in pitcher['name'].lower():
                            home_stats.append({
                                'date': game.date,
                                'team': game.home_team,
                                'opponent': game.away_team,
                                'venue': 'home',
                                'innings_pitched': pitcher['innings_pitched'],
                                'earned_runs': pitcher['earned_runs'],
                                'hits': pitcher['hits'],
                                'walks': pitcher['walks'],
                                'strikeouts': pitcher['strikeouts'],
                                'era_game': pitcher['earned_runs'] * 9 / max(float(pitcher['innings_pitched'].replace('.', '').replace('0', '0.1')), 0.1),
                                'whip': (pitcher['hits'] + pitcher['walks']) / max(float(pitcher['innings_pitched'].replace('.', '').replace('0', '0.1')), 0.1)
                            })
                    
                    # 檢查客隊投手
                    away_starters = analysis.get('away_pitching', {}).get('starters', [])
                    for pitcher in away_starters:
                        if pitcher_name.lower() in pitcher['name'].lower():
                            away_stats.append({
                                'date': game.date,
                                'team': game.away_team,
                                'opponent': game.home_team,
                                'venue': 'away',
                                'innings_pitched': pitcher['innings_pitched'],
                                'earned_runs': pitcher['earned_runs'],
                                'hits': pitcher['hits'],
                                'walks': pitcher['walks'],
                                'strikeouts': pitcher['strikeouts'],
                                'era_game': pitcher['earned_runs'] * 9 / max(float(pitcher['innings_pitched'].replace('.', '').replace('0', '0.1')), 0.1),
                                'whip': (pitcher['hits'] + pitcher['walks']) / max(float(pitcher['innings_pitched'].replace('.', '').replace('0', '0.1')), 0.1)
                            })
                    
                    # 添加延遲避免API限制
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"處理比賽 {game.game_id} 時出錯: {e}")
                    continue
            
            # 分析結果
            result = {
                'pitcher': pitcher_name,
                'home_starts': len(home_stats),
                'away_starts': len(away_stats)
            }
            
            if home_stats:
                home_eras = [s['era_game'] for s in home_stats]
                home_whips = [s['whip'] for s in home_stats]
                result['home_performance'] = {
                    'avg_era': np.mean(home_eras),
                    'avg_whip': np.mean(home_whips),
                    'avg_strikeouts': np.mean([s['strikeouts'] for s in home_stats]),
                    'consistency': np.std(home_eras)
                }
            
            if away_stats:
                away_eras = [s['era_game'] for s in away_stats]
                away_whips = [s['whip'] for s in away_stats]
                result['away_performance'] = {
                    'avg_era': np.mean(away_eras),
                    'avg_whip': np.mean(away_whips),
                    'avg_strikeouts': np.mean([s['strikeouts'] for s in away_stats]),
                    'consistency': np.std(away_eras)
                }
            
            if home_stats and away_stats:
                result['home_away_difference'] = {
                    'era_diff': result['away_performance']['avg_era'] - result['home_performance']['avg_era'],
                    'whip_diff': result['away_performance']['avg_whip'] - result['home_performance']['avg_whip'],
                    'better_location': 'home' if result['home_performance']['avg_era'] < result['away_performance']['avg_era'] else 'away'
                }
            
            return result

def test_boxscore_collection():
    """測試 boxscore 數據收集"""
    collector = BoxscoreDataCollector()
    
    # 測試獲取最近的比賽數據
    from flask import current_app
    app = current_app._get_current_object()
    with app.app_context():
        recent_game = Game.query.filter(
            Game.home_score.isnot(None),
            Game.away_score.isnot(None)
        ).order_by(Game.date.desc()).first()
        
        if recent_game:
            print(f"🔍 測試獲取比賽詳細數據: {recent_game.away_team} @ {recent_game.home_team} ({recent_game.date})")
            
            analysis = collector.get_detailed_game_analysis(recent_game.game_id)
            if analysis:
                print("✅ 成功獲取 boxscore 數據")
                print(f"   主隊投手: {len(analysis.get('home_pitching', {}).get('starters', []))} 先發")
                print(f"   客隊投手: {len(analysis.get('away_pitching', {}).get('starters', []))} 先發")
                print(f"   主隊打者: {len(analysis.get('home_batting', {}).get('batters', []))} 打者")
                print(f"   客隊打者: {len(analysis.get('away_batting', {}).get('batters', []))} 打者")
                
                # 顯示先發投手信息
                if analysis['home_pitching']['starters']:
                    starter = analysis['home_pitching']['starters'][0]
                    print(f"   {recent_game.home_team} 先發: {starter['name']} - {starter['innings_pitched']}局, {starter['earned_runs']}自責分")
                
                if analysis['away_pitching']['starters']:
                    starter = analysis['away_pitching']['starters'][0]
                    print(f"   {recent_game.away_team} 先發: {starter['name']} - {starter['innings_pitched']}局, {starter['earned_runs']}自責分")
            else:
                print("❌ 無法獲取 boxscore 數據 (可能需要實作 game_id 映射)")
        else:
            print("❌ 沒有找到完成的比賽記錄")

if __name__ == "__main__":
    test_boxscore_collection()