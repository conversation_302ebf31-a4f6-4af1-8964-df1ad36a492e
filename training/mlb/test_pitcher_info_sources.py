#!/usr/bin/env python3
"""
測試不同來源的投手信息
"""

import requests
import json
from datetime import date, timedelta

def test_pitcher_info_sources():
    """測試不同來源的投手信息"""
    print("=" * 80)
    print("⚾ 投手信息來源測試")
    print("=" * 80)
    
    base_url = "https://statsapi.mlb.com/api/v1"
    yesterday = date.today() - timedelta(days=1)
    date_str = yesterday.strftime('%Y-%m-%d')
    
    # 1. 測試schedule API的probablePitchers
    print(f"\n📡 測試1: Schedule API - probablePitchers")
    print("-" * 50)
    
    try:
        url = f"{base_url}/schedule"
        params = {
            'sportId': 1,
            'date': date_str,
            'hydrate': 'probablePitchers'
        }
        response = requests.get(url, params=params, timeout=30)
        data = response.json()
        
        if data['dates'] and data['dates'][0]['games']:
            first_game = data['dates'][0]['games'][0]
            game_id = first_game['gamePk']
            
            print(f"比賽ID: {game_id}")
            
            if 'probablePitchers' in first_game:
                pitchers = first_game['probablePitchers']
                print(f"probablePitchers結構: {pitchers}")
            else:
                print("❌ 沒有probablePitchers信息")
                
    except Exception as e:
        print(f"❌ Schedule API失敗: {e}")
    
    # 2. 測試boxscore API的投手信息
    print(f"\n📡 測試2: Boxscore API - 投手信息")
    print("-" * 50)
    
    try:
        # 先獲取比賽ID
        url = f"{base_url}/schedule"
        params = {'sportId': 1, 'date': date_str}
        response = requests.get(url, params=params, timeout=30)
        data = response.json()
        
        if data['dates'] and data['dates'][0]['games']:
            game_id = data['dates'][0]['games'][0]['gamePk']
            
            # 獲取boxscore
            boxscore_url = f"{base_url}/game/{game_id}/boxscore"
            boxscore_response = requests.get(boxscore_url, timeout=30)
            boxscore_data = boxscore_response.json()
            
            print(f"比賽ID: {game_id}")
            
            # 檢查投手信息
            if 'teams' in boxscore_data:
                for team_type in ['home', 'away']:
                    team_data = boxscore_data['teams'][team_type]
                    
                    print(f"\n{team_type}隊投手信息:")
                    
                    # 檢查pitchers列表
                    if 'pitchers' in team_data:
                        pitchers = team_data['pitchers']
                        print(f"  投手ID列表: {pitchers}")
                        
                        # 獲取第一個投手的詳細信息
                        if pitchers and 'players' in team_data:
                            first_pitcher_id = f"ID{pitchers[0]}"
                            if first_pitcher_id in team_data['players']:
                                pitcher_info = team_data['players'][first_pitcher_id]
                                pitcher_name = pitcher_info.get('person', {}).get('fullName')
                                print(f"  第一個投手: {pitcher_name}")
                                
                                # 檢查是否為先發投手
                                if 'stats' in pitcher_info:
                                    stats = pitcher_info['stats']
                                    if 'pitching' in stats:
                                        pitching_stats = stats['pitching']
                                        innings = pitching_stats.get('inningsPitched', '0')
                                        print(f"    投球局數: {innings}")
                                        
                                        # 通常先發投手投球局數較多
                                        if float(innings) >= 4.0:
                                            print(f"    ✅ 可能是先發投手")
                                        else:
                                            print(f"    ❓ 可能是救援投手")
                    
                    # 檢查battingOrder（可能包含投手信息）
                    if 'battingOrder' in team_data:
                        batting_order = team_data['battingOrder']
                        print(f"  打擊順序: {batting_order}")
                        
                        # DH規則下，投手可能不在打擊順序中
                        if len(batting_order) == 9:
                            print(f"    使用指定打擊制")
                        else:
                            print(f"    投手可能在打擊順序中")
                            
    except Exception as e:
        print(f"❌ Boxscore API失敗: {e}")
    
    # 3. 測試live feed API
    print(f"\n📡 測試3: Live Feed API")
    print("-" * 50)
    
    try:
        # 獲取比賽ID
        url = f"{base_url}/schedule"
        params = {'sportId': 1, 'date': date_str}
        response = requests.get(url, params=params, timeout=30)
        data = response.json()
        
        if data['dates'] and data['dates'][0]['games']:
            game_id = data['dates'][0]['games'][0]['gamePk']
            
            # 獲取live feed
            live_url = f"{base_url}/game/{game_id}/feed/live"
            live_response = requests.get(live_url, timeout=30)
            live_data = live_response.json()
            
            print(f"比賽ID: {game_id}")
            
            # 檢查gameData中的probablePitchers
            if 'gameData' in live_data:
                game_data = live_data['gameData']
                
                if 'probablePitchers' in game_data:
                    probable_pitchers = game_data['probablePitchers']
                    print(f"GameData probablePitchers: {probable_pitchers}")
                
                # 檢查players信息
                if 'players' in game_data:
                    players = game_data['players']
                    print(f"總球員數: {len(players)}")
                    
                    # 查找投手
                    pitchers_found = []
                    for player_id, player_info in players.items():
                        if player_info.get('primaryPosition', {}).get('code') == '1':  # 投手位置代碼
                            pitchers_found.append(player_info.get('fullName'))
                    
                    print(f"找到投手: {pitchers_found[:5]}...")  # 只顯示前5個
            
            # 檢查liveData中的投手信息
            if 'liveData' in live_data:
                live_data_section = live_data['liveData']
                
                if 'boxscore' in live_data_section:
                    boxscore = live_data_section['boxscore']
                    
                    if 'teams' in boxscore:
                        for team_type in ['home', 'away']:
                            team_info = boxscore['teams'][team_type]
                            
                            if 'pitchers' in team_info:
                                pitchers = team_info['pitchers']
                                print(f"{team_type}隊投手ID: {pitchers}")
                                
    except Exception as e:
        print(f"❌ Live Feed API失敗: {e}")

def main():
    """主函數"""
    test_pitcher_info_sources()

if __name__ == "__main__":
    main()
