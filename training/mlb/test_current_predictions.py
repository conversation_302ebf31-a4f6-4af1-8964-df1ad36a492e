#!/usr/bin/env python3
"""
測試當前預測系統的差異化
"""

import sys
import os
from datetime import date

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction

def test_current_predictions():
    """測試當前預測系統"""
    print("🎯 測試當前預測系統")
    print("=" * 50)
    
    app = create_app()
    with app.app_context():
        # 1. 檢查最近的預測
        print("📊 檢查最近的預測:")
        recent_predictions = Prediction.query.order_by(Prediction.prediction_date.desc()).limit(10).all()
        
        if not recent_predictions:
            print("❌ 沒有找到預測記錄")
            return
        
        print(f"找到 {len(recent_predictions)} 個最近預測:")
        
        home_scores = []
        away_scores = []
        
        for pred in recent_predictions:
            # 獲取比賽信息
            game = Game.query.filter_by(game_id=pred.game_id).first()
            if game:
                home_team = game.home_team
                away_team = game.away_team
            else:
                home_team = "Unknown"
                away_team = "Unknown"
            
            home_score = pred.predicted_home_score
            away_score = pred.predicted_away_score
            
            home_scores.append(home_score)
            away_scores.append(away_score)
            
            print(f"  {away_team} @ {home_team}: {away_score:.1f} - {home_score:.1f} (日期: {pred.prediction_date})")
        
        # 2. 分析預測差異
        print(f"\n📈 預測差異分析:")
        if home_scores and away_scores:
            home_min, home_max = min(home_scores), max(home_scores)
            away_min, away_max = min(away_scores), max(away_scores)
            
            home_diff = home_max - home_min
            away_diff = away_max - away_min
            
            print(f"主隊得分範圍: {home_min:.1f} - {home_max:.1f} (差異: {home_diff:.1f})")
            print(f"客隊得分範圍: {away_min:.1f} - {away_max:.1f} (差異: {away_diff:.1f})")
            
            # 檢查是否所有預測都相同
            unique_home = len(set(home_scores))
            unique_away = len(set(away_scores))
            
            print(f"主隊得分唯一值數量: {unique_home}")
            print(f"客隊得分唯一值數量: {unique_away}")
            
            if unique_home <= 2 and unique_away <= 2:
                print("❌ 問題確認: 預測缺乏差異化!")
                print("   所有球隊得到相似的預測分數")
                
                # 檢查是否是經典的 4.0-5.0 問題
                if all(4.0 <= score <= 5.0 for score in home_scores + away_scores):
                    print("❌ 確認是 4.0-5.0 問題!")
                    print("   需要修復特徵提取和模型訓練")
            else:
                print("✅ 預測有合理差異化")
        
        # 3. 檢查模型版本
        print(f"\n🔧 檢查模型版本:")
        model_versions = set(pred.model_version for pred in recent_predictions if pred.model_version)
        print(f"使用的模型版本: {list(model_versions)}")
        
        # 4. 檢查今日比賽
        print(f"\n🏟️ 檢查今日比賽:")
        today = date.today()
        today_games = Game.query.filter(
            Game.date >= today,
            Game.date < today
        ).all()
        
        print(f"今日比賽數量: {len(today_games)}")
        
        # 5. 檢查今日預測
        today_predictions = Prediction.query.filter(
            Prediction.prediction_date >= today
        ).all()
        
        print(f"今日預測數量: {len(today_predictions)}")

if __name__ == "__main__":
    test_current_predictions()
