#!/usr/bin/env python3
"""
測試MLB傷兵追蹤系統
"""

import sys
import os
from datetime import date, timedelta

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.injury_tracker import InjuryTracker

def test_injury_tracker():
    """測試傷兵追蹤系統"""
    print("=" * 80)
    print("🏥 測試MLB傷兵追蹤系統")
    print("=" * 80)
    
    tracker = InjuryTracker()
    
    # 測試1: 獲取特定球隊的傷兵報告
    print("\n📊 測試1: 獲取波士頓紅襪隊傷兵報告")
    print("-" * 50)
    
    red_sox_injuries = tracker.get_team_injuries(111)  # 波士頓紅襪
    
    print(f"球隊ID: {red_sox_injuries['team_id']}")
    print(f"總傷兵數: {red_sox_injuries['total_injured']}")
    
    if red_sox_injuries['injured_players']:
        print("\n🤕 傷兵名單:")
        for player in red_sox_injuries['injured_players']:
            print(f"  {player['player_name']} ({player['position_abbr']})")
            print(f"    狀態: {player['status_description']}")
            print(f"    嚴重程度: {player['severity']}")
            print(f"    預期天數: {player['expected_days']} 天")
            print()
    else:
        print("✅ 該球隊目前無傷兵")
    
    print(f"\n📈 按嚴重程度統計:")
    for severity, count in red_sox_injuries['by_severity'].items():
        if count > 0:
            print(f"  {severity.title()}: {count} 人")
    
    # 測試2: 獲取多個球隊的傷兵情況
    print(f"\n📊 測試2: 獲取多個球隊傷兵情況")
    print("-" * 50)
    
    test_teams = [
        (111, "Boston Red Sox"),
        (147, "New York Yankees"),
        (119, "Los Angeles Dodgers"),
        (117, "Houston Astros"),
        (112, "Chicago Cubs")
    ]
    
    team_injury_summary = []
    
    for team_id, team_name in test_teams:
        injuries = tracker.get_team_injuries(team_id)
        team_injury_summary.append({
            'team_name': team_name,
            'team_id': team_id,
            'total_injured': injuries['total_injured'],
            'injuries': injuries
        })
        
        print(f"{team_name}: {injuries['total_injured']} 名傷兵")
        
        if injuries['injured_players']:
            # 顯示最嚴重的傷兵
            severe_injuries = [p for p in injuries['injured_players'] 
                             if p['severity'] in ['Major', 'Season-Ending']]
            if severe_injuries:
                print(f"  重大傷兵: {', '.join([p['player_name'] for p in severe_injuries])}")
    
    # 測試3: 分析傷兵對今日比賽的影響
    print(f"\n📊 測試3: 分析傷兵對比賽的影響")
    print("-" * 50)
    
    today = date.today().strftime('%Y-%m-%d')
    yesterday = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
    
    # 測試今日和昨日的影響
    for test_date in [today, yesterday]:
        print(f"\n📅 分析日期: {test_date}")
        
        impact_analysis = tracker.analyze_injury_impact_on_predictions(test_date)
        
        print(f"總影響分數: {impact_analysis['total_impact_score']:.1f}")
        print(f"受影響比賽數: {len(impact_analysis['games_affected'])}")
        
        if impact_analysis['games_affected']:
            print("\n🎯 比賽影響分析:")
            for game in impact_analysis['games_affected'][:5]:  # 只顯示前5場
                print(f"  {game['home_team']} vs {game['away_team']}")
                print(f"    主隊傷兵影響: {game['home_injury_impact']:.1f}")
                print(f"    客隊傷兵影響: {game['away_injury_impact']:.1f}")
                print(f"    優勢方: {game['advantage']}")
                print()
        
        if impact_analysis['recommendations']:
            print("💡 建議:")
            for rec in impact_analysis['recommendations']:
                print(f"  - {rec}")
    
    # 測試4: 獲取全聯盟傷兵報告摘要
    print(f"\n📊 測試4: 全聯盟傷兵報告摘要")
    print("-" * 50)
    
    print("正在獲取全聯盟傷兵數據...")
    
    # 只測試前5個球隊以節省時間
    sample_teams = [111, 112, 113, 114, 115]
    total_injured = 0
    position_summary = {'pitchers': 0, 'catchers': 0, 'infielders': 0, 'outfielders': 0}
    severity_summary = {'minor': 0, 'moderate': 0, 'major': 0, 'season_ending': 0, 'recovering': 0}
    
    for team_id in sample_teams:
        injuries = tracker.get_team_injuries(team_id)
        total_injured += injuries['total_injured']
        
        for player in injuries['injured_players']:
            # 統計位置
            position_type = player['position_type'].lower()
            if 'pitcher' in position_type:
                position_summary['pitchers'] += 1
            elif 'catcher' in position_type:
                position_summary['catchers'] += 1
            elif 'infielder' in position_type:
                position_summary['infielders'] += 1
            elif 'outfielder' in position_type:
                position_summary['outfielders'] += 1
            
            # 統計嚴重程度
            severity = player['severity'].lower().replace('-', '_')
            if severity in severity_summary:
                severity_summary[severity] += 1
    
    print(f"\n📈 樣本統計 (前5個球隊):")
    print(f"總傷兵數: {total_injured}")
    
    print(f"\n按位置分布:")
    for position, count in position_summary.items():
        if count > 0:
            print(f"  {position.title()}: {count}")
    
    print(f"\n按嚴重程度分布:")
    for severity, count in severity_summary.items():
        if count > 0:
            print(f"  {severity.replace('_', ' ').title()}: {count}")
    
    # 測試5: 計算傷兵影響分數
    print(f"\n📊 測試5: 傷兵影響分數計算")
    print("-" * 50)
    
    for team_name, team_id in [("Boston Red Sox", 111), ("New York Yankees", 147)]:
        injuries = tracker.get_team_injuries(team_id)
        impact_score = tracker.calculate_injury_impact_score(injuries)
        
        print(f"{team_name}:")
        print(f"  傷兵數: {injuries['total_injured']}")
        print(f"  影響分數: {impact_score:.1f}")
        
        if impact_score > 5:
            print(f"  ⚠️  高影響球隊")
        elif impact_score > 2:
            print(f"  ⚡ 中等影響")
        else:
            print(f"  ✅ 低影響")
        print()

def main():
    """主函數"""
    test_injury_tracker()

if __name__ == "__main__":
    main()
