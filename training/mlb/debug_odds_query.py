#!/usr/bin/env python3
"""
調試賠率查詢問題
檢查為什麼API查詢返回0結果
"""

from app import create_app
from models.database import db, Game, BettingOdds
from datetime import date, timedelta, datetime
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_odds_query():
    """調試賠率查詢問題"""
    app = create_app()
    
    with app.app_context():
        print("🔍 調試賠率查詢問題...")
        print("=" * 80)
        
        # 1. 檢查數據庫基本狀態
        print("\n📊 檢查數據庫基本狀態...")
        total_odds = BettingOdds.query.count()
        total_games = Game.query.count()
        print(f"   總賠率記錄: {total_odds}")
        print(f"   總比賽記錄: {total_games}")
        
        # 2. 檢查最近的賠率數據
        print("\n📅 檢查最近的賠率數據...")
        recent_odds = BettingOdds.query.order_by(BettingOdds.created_at.desc()).limit(5).all()
        
        for i, odds in enumerate(recent_odds):
            try:
                game = Game.query.filter_by(game_id=odds.game_id).first()
                if game:
                    print(f"   記錄 {i+1}: {game.away_team}@{game.home_team} ({game.date}) - {odds.bookmaker} {odds.market_type}")
                else:
                    print(f"   記錄 {i+1}: 遊戲ID {odds.game_id} 找不到對應比賽")
            except Exception as e:
                print(f"   記錄 {i+1}: 解析失敗 - {e}")
        
        # 3. 檢查特定日期範圍的數據
        print("\n🎯 檢查特定日期範圍的數據...")
        target_dates = [
            date.today() - timedelta(days=1),  # 昨天
            date.today() - timedelta(days=2),  # 前天
            date.today() - timedelta(days=3),  # 3天前
        ]
        
        for target_date in target_dates:
            # 直接查詢該日期的比賽
            games_on_date = Game.query.filter(Game.date == target_date).all()
            print(f"   📅 {target_date}: {len(games_on_date)} 場比賽")
            
            if games_on_date:
                # 查詢這些比賽的賠率
                game_ids = [g.game_id for g in games_on_date]
                odds_for_date = BettingOdds.query.filter(
                    BettingOdds.game_id.in_(game_ids)
                ).count()
                print(f"      對應賠率記錄: {odds_for_date}")
                
                # 顯示前幾場比賽的詳情
                for game in games_on_date[:3]:
                    odds_count = BettingOdds.query.filter_by(game_id=game.game_id).count()
                    print(f"         - {game.away_team}@{game.home_team} (ID: {game.game_id}): {odds_count} 條賠率")
        
        # 4. 測試API查詢邏輯
        print("\n🔧 測試API查詢邏輯...")
        
        # 模擬API查詢參數
        date_from = "2025-08-28"
        date_to = "2025-08-30"
        
        start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        
        print(f"   查詢範圍: {start_date} 到 {end_date}")
        
        # 執行查詢（模擬API邏輯）
        query = db.session.query(BettingOdds, Game).join(Game, BettingOdds.game_id == Game.game_id)
        query = query.filter(Game.date >= start_date)
        query = query.filter(Game.date <= end_date)
        
        results = query.limit(10).all()
        print(f"   查詢結果: {len(results)} 條記錄")
        
        if results:
            print("   📋 查詢結果範例:")
            for i, (odds, game) in enumerate(results[:5]):
                print(f"      {i+1}. {game.away_team}@{game.home_team} ({game.date}) - {odds.bookmaker} {odds.market_type}")
        else:
            print("   ❌ 查詢沒有結果")
            
            # 檢查是否是JOIN的問題
            print("\n🔍 調試JOIN問題...")
            orphaned_odds = BettingOdds.query.outerjoin(Game, BettingOdds.game_id == Game.game_id).filter(
                Game.game_id.is_(None)
            ).count()
            print(f"   孤立的賠率記錄（沒有對應比賽）: {orphaned_odds}")
            
            # 檢查Game表中的日期分佈
            date_stats = db.session.query(
                db.func.min(Game.date).label('min_date'),
                db.func.max(Game.date).label('max_date'),
                db.func.count(Game.id).label('total')
            ).first()
            
            print(f"   比賽日期範圍: {date_stats.min_date} 到 {date_stats.max_date} (總計 {date_stats.total})")
            
            # 檢查是否有指定日期範圍內的比賽
            games_in_range = Game.query.filter(
                Game.date >= start_date,
                Game.date <= end_date
            ).count()
            print(f"   指定日期範圍內的比賽: {games_in_range}")
        
        # 5. 檢查數據完整性
        print("\n🔍 檢查數據完整性...")
        
        # 檢查BettingOdds和Game的關聯
        total_odds = BettingOdds.query.count()
        linked_odds = db.session.query(BettingOdds).join(Game).count()
        
        print(f"   總賠率記錄: {total_odds}")
        print(f"   已關聯比賽的賠率: {linked_odds}")
        print(f"   未關聯比賽的賠率: {total_odds - linked_odds}")
        
        if total_odds - linked_odds > 0:
            print("\n⚠️ 發現未關聯的賠率記錄，檢查前幾條...")
            orphaned = BettingOdds.query.outerjoin(Game).filter(Game.game_id.is_(None)).limit(5).all()
            for odds in orphaned:
                print(f"      - Game ID: {odds.game_id}, Bookmaker: {odds.bookmaker}, Market: {odds.market_type}")

if __name__ == "__main__":
    debug_odds_query()