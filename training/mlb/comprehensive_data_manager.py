#!/usr/bin/env python3
"""
全面數據管理器 - 管理所有影響賽果的球隊和球員資料抓取
"""

import sys
import os
import logging
from datetime import date, datetime, timedelta
import argparse

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 延遲導入app以避免循環導入
def get_app():
    """獲取Flask應用實例"""
    from app import create_app
    return create_app()

from models.database import db, Team, Player
from models.comprehensive_data_fetcher import ComprehensiveDataFetcher
from models.enhanced_data_models import (
    TeamAdvancedStats, PlayerInjuryReport, PlayerPerformanceTrends,
    WeatherConditions, TeamChemistry
)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_data.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ComprehensiveDataManager:
    """全面數據管理器"""
    
    def __init__(self):
        self.app = get_app()
        self.fetcher = ComprehensiveDataFetcher()
    
    def initialize_database(self):
        """初始化數據庫表"""
        logger.info("初始化增強數據模型表...")
        
        with self.app.app_context():
            try:
                # 創建新表
                db.create_all()
                logger.info("✅ 數據庫表初始化完成")
                return True
                
            except Exception as e:
                logger.error(f"❌ 數據庫初始化失敗: {e}")
                return False
    
    def fetch_all_teams_data(self, season: int = None):
        """獲取所有球隊的全面數據"""
        if season is None:
            season = date.today().year
        
        logger.info(f"開始獲取所有球隊的 {season} 賽季全面數據...")
        
        with self.app.app_context():
            try:
                results = self.fetcher.fetch_all_teams_comprehensive_data(season)
                
                # 顯示結果摘要
                self._display_results_summary(results)
                
                return results
                
            except Exception as e:
                logger.error(f"獲取全面數據失敗: {e}")
                return None
    
    def fetch_single_team_data(self, team_code: str, season: int = None):
        """獲取單個球隊的全面數據"""
        if season is None:
            season = date.today().year
        
        with self.app.app_context():
            try:
                # 查找球隊
                team = Team.query.filter_by(team_code=team_code.upper()).first()
                if not team:
                    logger.error(f"找不到球隊: {team_code}")
                    return False
                
                logger.info(f"獲取球隊 {team.team_code} ({team.team_name}) 的全面數據...")
                
                success = self.fetcher.fetch_all_team_data(team.team_id, season)
                
                if success:
                    logger.info(f"✅ 球隊 {team.team_code} 數據獲取成功")
                else:
                    logger.error(f"❌ 球隊 {team.team_code} 數據獲取失敗")
                
                return success
                
            except Exception as e:
                logger.error(f"獲取球隊 {team_code} 數據失敗: {e}")
                return False
    
    def update_player_trends(self, season: int = None):
        """更新所有球員的表現趨勢"""
        if season is None:
            season = date.today().year
        
        logger.info(f"開始更新 {season} 賽季球員表現趨勢...")
        
        with self.app.app_context():
            try:
                # 獲取所有活躍球員
                players = Player.query.filter_by(active=True).all()
                
                success_count = 0
                total_players = len(players)
                
                for i, player in enumerate(players, 1):
                    logger.info(f"處理球員 {i}/{total_players}: {player.full_name}")
                    
                    try:
                        # 獲取球員當前球隊
                        team_id = player.current_team_id if hasattr(player, 'current_team_id') else None
                        
                        if team_id:
                            success = self.fetcher.fetch_player_performance_trends(
                                player.player_id, team_id, season
                            )
                            
                            if success:
                                success_count += 1
                        
                        # 避免API限制
                        if i % 10 == 0:
                            logger.info(f"已處理 {i}/{total_players} 球員...")
                            
                    except Exception as e:
                        logger.error(f"處理球員 {player.full_name} 失敗: {e}")
                        continue
                
                logger.info(f"球員趨勢更新完成: {success_count}/{total_players} 成功")
                return success_count
                
            except Exception as e:
                logger.error(f"更新球員趨勢失敗: {e}")
                return 0
    
    def check_data_status(self, season: int = None):
        """檢查數據狀態"""
        if season is None:
            season = date.today().year
        
        logger.info(f"檢查 {season} 賽季數據狀態...")
        
        with self.app.app_context():
            try:
                # 檢查各類數據的數量
                stats = {
                    'teams': Team.query.filter_by(active=True).count(),
                    'team_advanced_stats': TeamAdvancedStats.query.filter_by(season=season).count(),
                    'player_trends': PlayerPerformanceTrends.query.filter_by(season=season).count(),
                    'injury_reports': PlayerInjuryReport.query.filter_by(is_active=True).count(),
                    'team_chemistry': TeamChemistry.query.filter_by(season=season).count(),
                    'weather_conditions': WeatherConditions.query.count()
                }
                
                # 顯示統計信息
                logger.info("=== 數據狀態統計 ===")
                logger.info(f"活躍球隊數量: {stats['teams']}")
                logger.info(f"球隊進階統計: {stats['team_advanced_stats']}")
                logger.info(f"球員表現趨勢: {stats['player_trends']}")
                logger.info(f"活躍傷兵報告: {stats['injury_reports']}")
                logger.info(f"球隊化學反應: {stats['team_chemistry']}")
                logger.info(f"天氣條件記錄: {stats['weather_conditions']}")
                
                # 計算完整度
                completeness = {
                    'team_advanced_stats': (stats['team_advanced_stats'] / stats['teams'] * 100) if stats['teams'] > 0 else 0,
                    'team_chemistry': (stats['team_chemistry'] / stats['teams'] * 100) if stats['teams'] > 0 else 0
                }
                
                logger.info("=== 數據完整度 ===")
                logger.info(f"球隊進階統計完整度: {completeness['team_advanced_stats']:.1f}%")
                logger.info(f"球隊化學反應完整度: {completeness['team_chemistry']:.1f}%")
                
                return stats
                
            except Exception as e:
                logger.error(f"檢查數據狀態失敗: {e}")
                return None
    
    def _display_results_summary(self, results: dict):
        """顯示結果摘要"""
        if not results:
            return
        
        logger.info("=== 數據獲取結果摘要 ===")
        logger.info(f"總球隊數: {results['total_teams']}")
        logger.info(f"成功球隊數: {results['successful_teams']}")
        logger.info(f"失敗球隊數: {len(results['failed_teams'])}")
        logger.info(f"成功率: {(results['successful_teams'] / results['total_teams'] * 100):.1f}%")
        
        if results['failed_teams']:
            logger.warning(f"失敗球隊: {', '.join(results['failed_teams'])}")
    
    def cleanup_old_data(self, days_old: int = 30):
        """清理舊數據"""
        logger.info(f"清理 {days_old} 天前的舊數據...")
        
        with self.app.app_context():
            try:
                cutoff_date = date.today() - timedelta(days=days_old)
                
                # 清理舊的表現趨勢數據
                old_trends = PlayerPerformanceTrends.query.filter(
                    PlayerPerformanceTrends.date_calculated < cutoff_date
                ).count()
                
                PlayerPerformanceTrends.query.filter(
                    PlayerPerformanceTrends.date_calculated < cutoff_date
                ).delete()
                
                # 清理已恢復的傷兵報告
                recovered_injuries = PlayerInjuryReport.query.filter(
                    PlayerInjuryReport.actual_return.isnot(None),
                    PlayerInjuryReport.actual_return < cutoff_date
                ).count()
                
                PlayerInjuryReport.query.filter(
                    PlayerInjuryReport.actual_return.isnot(None),
                    PlayerInjuryReport.actual_return < cutoff_date
                ).delete()
                
                db.session.commit()
                
                logger.info(f"✅ 清理完成: 刪除 {old_trends} 條趨勢記錄, {recovered_injuries} 條傷兵記錄")
                
            except Exception as e:
                logger.error(f"清理舊數據失敗: {e}")
                db.session.rollback()

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='MLB 全面數據管理器')
    parser.add_argument('action', choices=[
        'init', 'fetch-all', 'fetch-team', 'update-trends', 'status', 'cleanup'
    ], help='執行的操作')
    parser.add_argument('--team', help='球隊代碼 (用於 fetch-team)')
    parser.add_argument('--season', type=int, help='賽季年份')
    parser.add_argument('--days', type=int, default=30, help='清理天數 (用於 cleanup)')
    
    args = parser.parse_args()
    
    manager = ComprehensiveDataManager()
    
    if args.action == 'init':
        manager.initialize_database()
    
    elif args.action == 'fetch-all':
        manager.fetch_all_teams_data(args.season)
    
    elif args.action == 'fetch-team':
        if not args.team:
            logger.error("請指定球隊代碼 --team")
            return
        manager.fetch_single_team_data(args.team, args.season)
    
    elif args.action == 'update-trends':
        manager.update_player_trends(args.season)
    
    elif args.action == 'status':
        manager.check_data_status(args.season)
    
    elif args.action == 'cleanup':
        manager.cleanup_old_data(args.days)

if __name__ == '__main__':
    main()
