#!/usr/bin/env python3
"""
增強預測系統測試腳本
驗證日期邏輯修復和投手資料問題解決方案
"""

import sys
import os
import asyncio
import json
from datetime import date, datetime

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.unified_prediction_engine import predict_game
from models.prediction_context import create_prediction_context
from models.starting_pitcher_tracker import StartingPitcherTracker

def test_date_logic_fix():
    """測試日期邏輯修復"""
    
    print("🗓️  測試日期邏輯修復")
    print("=" * 50)
    
    # 創建Flask應用上下文
    app = create_app('development')
    
    with app.app_context():
        # 測試1: 歷史日期預測 (解決8/23查詢8/27的問題)
        print("測試1: 歷史日期預測")
        
        historical_context = create_prediction_context(
            game_id="20250823_BOS_NYY", 
            target_date="2025-08-23"  # 明確指定8/23
        )
        
        print(f"✅ 預測模式: {historical_context.prediction_mode}")
        print(f"✅ 目標日期: {historical_context.target_date}")
        print(f"✅ 請求日期: {historical_context.request_date}")
        print(f"✅ 是否歷史預測: {historical_context.is_historical_prediction()}")
        
        # 測試2: None日期處理 (舊系統的問題場景)
        print("\n測試2: None日期自動推斷")
        
        context_with_none_date = create_prediction_context(
            game_id="20250823_TOR_MIA",  # 包含日期信息的game_id
            target_date=None  # 模擬舊系統的None傳遞
        )
        
        print(f"✅ 從game_id推斷日期: {context_with_none_date.target_date}")
        print(f"✅ 預測模式: {context_with_none_date.prediction_mode}")
        
        # 測試3: 即時預測
        print("\n測試3: 即時預測")
        
        live_context = create_prediction_context(
            game_id="20250827_LAD_SF",
            target_date=date.today()
        )
        
        print(f"✅ 預測模式: {live_context.prediction_mode}")
        print(f"✅ 是否即時預測: {live_context.is_live_prediction()}")

def test_pitcher_data_integration():
    """測試投手資料整合"""
    
    print("\n⚾ 測試投手資料整合")
    print("=" * 50)
    
    app = create_app('development')
    
    with app.app_context():
        pitcher_tracker = StartingPitcherTracker()
        
        # 測試投手資料查詢
        print("測試1: 投手資料查詢")
        
        # 模擬添加一個測試投手記錄
        test_result = pitcher_tracker.record_starting_pitchers(
            game_id="test_game_20250823",
            game_date=date(2025, 8, 23),
            home_team="BOS",
            away_team="NYY", 
            home_pitcher="Chris Sale",
            away_pitcher="Gerrit Cole",
            data_source="test",
            confidence_level="high"
        )
        
        print(f"✅ 記錄投手資料: {test_result}")
        
        # 查詢投手資料
        pitcher_info = pitcher_tracker.get_starting_pitchers("test_game_20250823")
        
        if pitcher_info:
            print(f"✅ 客隊投手: {pitcher_info['away_starting_pitcher']}")
            print(f"✅ 主隊投手: {pitcher_info['home_starting_pitcher']}")
            print(f"✅ 數據來源: {pitcher_info['data_source']}")
            print(f"✅ 信心度: {pitcher_info['confidence_level']}")
        else:
            print("❌ 無法查詢到投手資料")

async def test_unified_prediction_engine():
    """測試統一預測引擎"""
    
    print("\n🎯 測試統一預測引擎")
    print("=" * 50)
    
    app = create_app('development')
    
    with app.app_context():
        # 測試1: 歷史預測 (解決您界面中的4.0 vs 5.0問題)
        print("測試1: 歷史預測 (使用增強引擎)")
        
        try:
            historical_result = await predict_game(
                game_id="test_game_historical",
                target_date=date(2025, 8, 23)
            )
            
            if historical_result.get('success'):
                prediction = historical_result.get('prediction', {})
                confidence = historical_result.get('confidence', {})
                pitcher_analysis = historical_result.get('pitcher_analysis', {})
                
                print(f"✅ 預測成功")
                print(f"   客隊得分: {prediction.get('away_predicted_runs', 'N/A')}")
                print(f"   主隊得分: {prediction.get('home_predicted_runs', 'N/A')}")
                print(f"   總得分: {prediction.get('predicted_total_runs', 'N/A')}")
                print(f"   主隊勝率: {prediction.get('home_win_probability', 'N/A'):.3f}")
                print(f"   信心度: {confidence.get('confidence_level', 'N/A')}")
                print(f"   客隊投手: {pitcher_analysis.get('away_pitcher', {}).get('name', '未確認')}")
                print(f"   主隊投手: {pitcher_analysis.get('home_pitcher', {}).get('name', '未確認')}")
                
            else:
                print(f"❌ 預測失敗: {historical_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ 預測引擎異常: {e}")
        
        # 測試2: 回退預測 (當數據不足時)
        print("\n測試2: 回退預測機制")
        
        try:
            fallback_result = await predict_game(
                game_id="nonexistent_game",
                target_date=date(2025, 8, 23)
            )
            
            if fallback_result.get('prediction_strategy') == 'fallback':
                print("✅ 回退預測機制正常工作")
                prediction = fallback_result.get('prediction', {})
                print(f"   回退得分: {prediction.get('away_predicted_runs', 0):.1f} vs {prediction.get('home_predicted_runs', 0):.1f}")
            else:
                print("⚠️  未觸發回退機制")
                
        except Exception as e:
            print(f"❌ 回退測試異常: {e}")

def test_covid_adjustments():
    """測試COVID-19調整機制"""
    
    print("\n😷 測試COVID-19調整機制")
    print("=" * 50)
    
    app = create_app('development')
    
    with app.app_context():
        from models.historical_data_optimizer import HistoricalDataOptimizer
        
        optimizer = HistoricalDataOptimizer()
        
        # 測試2020年權重調整
        print("測試1: 2020年權重調整")
        print(f"✅ 2020年基礎權重: {optimizer.seasonal_weights.get(2020, 'N/A')}")
        print(f"✅ COVID調整因子: {optimizer.covid_adjustment_factor}")
        print(f"✅ 2020年質量評分: {optimizer.season_quality_scores.get(2020, 'N/A')}")
        
        # 測試權重計算
        print("\n測試2: 各年度權重分布")
        for year, weight in optimizer.seasonal_weights.items():
            quality = optimizer.season_quality_scores.get(year, 0.5)
            if year == 2020:
                final_weight = weight * optimizer.covid_adjustment_factor * quality
                print(f"   {year}: {weight} × {optimizer.covid_adjustment_factor} × {quality} = {final_weight:.3f} (COVID調整)")
            else:
                final_weight = weight * quality
                print(f"   {year}: {weight} × {quality} = {final_weight:.3f}")

def run_comprehensive_test():
    """運行綜合測試"""
    
    print("🚀 MLB增強預測系統 - 綜合測試")
    print("=" * 80)
    print("此測試將驗證是否解決了您界面中的問題:")
    print("1. 投手資料顯示 '未確認' 的問題")
    print("2. 4.0 vs 5.0 簡化預測的問題") 
    print("3. 8/23查詢顯示8/27數據的問題")
    print("=" * 80)
    
    # 1. 測試日期邏輯修復
    test_date_logic_fix()
    
    # 2. 測試投手資料整合
    test_pitcher_data_integration()
    
    # 3. 測試COVID調整
    test_covid_adjustments()
    
    # 4. 測試統一預測引擎 (異步)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        loop.run_until_complete(test_unified_prediction_engine())
    finally:
        loop.close()
    
    print("\n🎉 綜合測試完成!")
    print("=" * 80)
    print("✅ 主要改進:")
    print("   • 統一預測上下文管理，解決日期傳遞錯誤")
    print("   • 投手資料庫和追蹤系統，解決'未確認'問題")
    print("   • 增強歷史預測引擎，提供更準確的得分預測")
    print("   • COVID-19季節特殊處理，提升歷史數據質量")
    print("   • 多級回退機制，確保系統穩定性")
    print()
    print("🔗 使用方式:")
    print("   • 新API端點: /predictions/enhanced/api/predict/<game_id>")
    print("   • 批量預測: /predictions/enhanced/api/batch_predict") 
    print("   • 投手查詢: /predictions/enhanced/api/pitcher_info/<game_id>")
    print()
    print("📊 數據庫建構:")
    print("   • 運行: python scripts/build_pitcher_database.py")
    print("   • 這將收集2019-2025年的投手歷史資料")

if __name__ == "__main__":
    run_comprehensive_test()