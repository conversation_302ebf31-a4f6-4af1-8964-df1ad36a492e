#!/usr/bin/env python3
"""
測試模板修改後的效果
確保界面不再顯示模擬博彩數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime
from app import create_app
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_custom_predict_template():
    """測試自定義預測模板"""
    print("🧪 測試自定義預測模板...")
    print("=" * 60)
    
    app = create_app()
    
    with app.test_client() as client:
        # 測試自定義預測頁面
        response = client.get('/unified/custom_predict')
        
        print(f"📊 響應狀態: {response.status_code}")
        
        if response.status_code == 200:
            content = response.get_data(as_text=True)
            
            # 檢查是否還有模擬數據相關的顯示邏輯
            simulated_indicators = [
                'badge bg-warning text-dark">模擬',
                'sourceLabel = isReal ? \'真實\' : \'模擬\'',
                'Simulated</span>',
                'estimated</span>'
            ]
            
            found_issues = []
            for indicator in simulated_indicators:
                if indicator in content:
                    found_issues.append(indicator)
            
            if found_issues:
                print("❌ 仍有模擬數據顯示邏輯:")
                for issue in found_issues:
                    print(f"   - {issue}")
                return False
            else:
                print("✅ 模板已正確修改，不再顯示模擬數據")
                return True
        else:
            print(f"❌ 頁面載入失敗: {response.status_code}")
            return False

def test_api_response():
    """測試API響應是否包含模擬數據"""
    print("\n🧪 測試API響應...")
    print("=" * 60)
    
    app = create_app()
    
    with app.test_client() as client:
        # 測試自定義日期預測API
        test_data = {
            'date': '2025-07-04',
            'exclude_postponed': True
        }
        
        response = client.post('/unified/api/predict/custom_date',
                             json=test_data,
                             content_type='application/json')
        
        print(f"📊 API響應狀態: {response.status_code}")
        
        if response.status_code == 200:
            data = response.get_json()
            
            if data.get('success'):
                predictions = data.get('predictions', [])
                print(f"📈 預測數量: {len(predictions)}")
                
                # 檢查是否有模擬博彩數據
                simulated_count = 0
                real_count = 0
                
                for pred in predictions:
                    betting_odds = pred.get('betting_odds')

                    if betting_odds:
                        # 檢查大小分數據
                        totals = betting_odds.get('totals', {})
                        if totals:
                            bookmaker = totals.get('bookmaker', '')
                            if bookmaker in ['Simulated', 'estimated', 'realistic_estimate']:
                                simulated_count += 1
                            elif bookmaker and bookmaker not in ['Simulated', 'estimated', 'realistic_estimate']:
                                real_count += 1

                        # 檢查讓分盤數據
                        spreads = betting_odds.get('spreads', {})
                        if spreads:
                            bookmaker = spreads.get('bookmaker', '')
                            if bookmaker in ['Simulated', 'estimated', 'realistic_estimate']:
                                simulated_count += 1
                            elif bookmaker and bookmaker not in ['Simulated', 'estimated', 'realistic_estimate']:
                                real_count += 1
                
                print(f"📊 博彩數據統計:")
                print(f"   - 真實數據: {real_count}")
                print(f"   - 模擬數據: {simulated_count}")
                
                if simulated_count == 0:
                    print("✅ API不再返回模擬博彩數據")
                    return True
                else:
                    print("❌ API仍返回模擬博彩數據")
                    return False
            else:
                print(f"❌ API返回錯誤: {data.get('error')}")
                return False
        else:
            print(f"❌ API請求失敗: {response.status_code}")
            return False

def test_database_consistency():
    """測試數據庫一致性"""
    print("\n🧪 測試數據庫一致性...")
    print("=" * 60)
    
    import sqlite3
    
    conn = sqlite3.connect('instance/mlb_data.db')
    cursor = conn.cursor()
    
    try:
        # 檢查是否還有模擬數據
        cursor.execute("""
            SELECT COUNT(*) 
            FROM betting_odds 
            WHERE bookmaker IN ('estimated', 'Simulated', 'realistic_estimate')
        """)
        
        simulated_count = cursor.fetchone()[0]
        
        print(f"📊 數據庫模擬數據數量: {simulated_count}")
        
        if simulated_count == 0:
            print("✅ 數據庫中沒有模擬數據")
            
            # 檢查真實數據數量
            cursor.execute("""
                SELECT COUNT(*) 
                FROM betting_odds 
                WHERE bookmaker NOT IN ('estimated', 'Simulated', 'realistic_estimate')
            """)
            
            real_count = cursor.fetchone()[0]
            print(f"📈 真實博彩數據數量: {real_count}")
            
            return True
        else:
            print("❌ 數據庫中仍有模擬數據")
            return False
    
    finally:
        conn.close()

def main():
    """主測試函數"""
    print("🧪 模板修改效果測試")
    print("=" * 80)
    
    tests_passed = 0
    total_tests = 3
    
    # 測試1: 自定義預測模板
    if test_custom_predict_template():
        tests_passed += 1
    
    # 測試2: API響應
    if test_api_response():
        tests_passed += 1
    
    # 測試3: 數據庫一致性
    if test_database_consistency():
        tests_passed += 1
    
    # 總結
    print("\n" + "=" * 80)
    print(f"🎯 測試總結: {tests_passed}/{total_tests} 通過")
    
    if tests_passed == total_tests:
        print("🎉 所有測試通過！")
        print("\n✅ 模板已正確修改")
        print("✅ API不再返回模擬數據")
        print("✅ 數據庫保持一致性")
        print("\n💡 用戶界面現在只會顯示真實博彩數據")
    else:
        print("⚠️  部分測試失敗，請檢查相關配置")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
