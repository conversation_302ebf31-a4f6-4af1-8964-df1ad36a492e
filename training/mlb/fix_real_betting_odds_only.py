#!/usr/bin/env python3
"""
修復預測系統，確保只使用真實的博彩盤口數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from datetime import datetime, date, timedelta

def fix_prediction_odds_source():
    """修復預測系統的盤口數據來源"""
    print("🔧 修復預測系統盤口數據來源")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 檢查現有預測的盤口數據來源
        print("1️⃣  檢查現有預測的盤口數據來源:")
        cursor.execute("""
            SELECT
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN EXISTS (
                    SELECT 1
                    FROM betting_odds bo
                    WHERE bo.game_id = p.game_id
                    AND bo.market_type = 'totals'
                    AND bo.bookmaker = 'bet365'
                    AND ABS(bo.total_point - p.over_under_line) < 0.1
                ) THEN 1 END) as with_real_odds,
                COUNT(CASE WHEN NOT EXISTS (
                    SELECT 1
                    FROM betting_odds bo
                    WHERE bo.game_id = p.game_id
                    AND bo.market_type = 'totals'
                    AND bo.bookmaker = 'bet365'
                    AND ABS(bo.total_point - p.over_under_line) < 0.1
                ) THEN 1 END) as with_simulated_odds
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
            AND p.model_version = 'unified_v1.0'
        """)
        
        total, real, simulated = cursor.fetchone()
        print(f"   總預測數: {total}")
        print(f"   使用真實盤口: {real}")
        print(f"   使用模擬盤口: {simulated}")
        
        # 2. 更新所有預測使用真實博彩盤口
        print("\n2️⃣  更新預測使用真實博彩盤口:")
        cursor.execute("""
            UPDATE predictions 
            SET 
                over_under_line = (
                    SELECT bo.total_point 
                    FROM betting_odds bo 
                    WHERE bo.game_id = predictions.game_id 
                    AND bo.market_type = 'totals' 
                    AND bo.bookmaker = 'bet365'
                    LIMIT 1
                ),
                updated_at = ?
            WHERE game_id IN (
                SELECT DISTINCT bo.game_id 
                FROM betting_odds bo 
                WHERE bo.market_type = 'totals' 
                AND bo.bookmaker = 'bet365'
            )
            AND model_version = 'unified_v1.0'
            AND game_id IN (
                SELECT g.game_id 
                FROM games g 
                WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
            )
        """, (datetime.now().isoformat(),))
        
        updated_count = cursor.rowcount
        print(f"   ✅ 更新了 {updated_count} 個預測的盤口數據")
        
        # 3. 重新計算大小分預測準確性
        print("\n3️⃣  重新計算大小分預測準確性:")
        cursor.execute("""
            UPDATE predictions 
            SET 
                is_correct = CASE 
                    WHEN (
                        SELECT g.home_score + g.away_score 
                        FROM games g 
                        WHERE g.game_id = predictions.game_id
                        AND g.home_score IS NOT NULL 
                        AND g.away_score IS NOT NULL
                    ) > predictions.over_under_line THEN 
                        CASE WHEN predictions.over_probability > 0.5 THEN 1 ELSE 0 END
                    WHEN (
                        SELECT g.home_score + g.away_score 
                        FROM games g 
                        WHERE g.game_id = predictions.game_id
                        AND g.home_score IS NOT NULL 
                        AND g.away_score IS NOT NULL
                    ) <= predictions.over_under_line THEN 
                        CASE WHEN predictions.under_probability > 0.5 THEN 1 ELSE 0 END
                    ELSE NULL
                END,
                actual_total_runs = (
                    SELECT g.home_score + g.away_score 
                    FROM games g 
                    WHERE g.game_id = predictions.game_id
                    AND g.home_score IS NOT NULL 
                    AND g.away_score IS NOT NULL
                ),
                updated_at = ?
            WHERE model_version = 'unified_v1.0'
            AND game_id IN (
                SELECT g.game_id 
                FROM games g 
                WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
                AND g.home_score IS NOT NULL 
                AND g.away_score IS NOT NULL
            )
        """, (datetime.now().isoformat(),))
        
        accuracy_updated = cursor.rowcount
        print(f"   ✅ 重新計算了 {accuracy_updated} 個預測的準確性")
        
        # 4. 刪除沒有真實博彩盤口的預測記錄
        print("\n4️⃣  清理沒有真實博彩盤口的預測:")
        cursor.execute("""
            DELETE FROM predictions 
            WHERE game_id NOT IN (
                SELECT DISTINCT bo.game_id 
                FROM betting_odds bo 
                WHERE bo.market_type = 'totals' 
                AND bo.bookmaker = 'bet365'
            )
            AND model_version = 'unified_v1.0'
            AND game_id IN (
                SELECT g.game_id 
                FROM games g 
                WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
            )
        """)
        
        deleted_count = cursor.rowcount
        print(f"   ✅ 刪除了 {deleted_count} 個沒有真實盤口的預測")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        import traceback
        traceback.print_exc()

def regenerate_predictions_with_real_odds():
    """重新生成6月到7/10的預測，確保使用真實盤口"""
    print("\n🔄 重新生成6月到7/10的預測")
    print("=" * 70)
    
    from models.unified_betting_predictor import UnifiedBettingPredictor
    from datetime import datetime, timedelta
    
    try:
        predictor = UnifiedBettingPredictor()
        
        # 生成日期範圍
        start_date = date(2025, 6, 1)
        end_date = date(2025, 7, 10)
        current_date = start_date
        
        total_generated = 0
        
        while current_date <= end_date:
            print(f"📅 處理日期: {current_date}")
            
            # 獲取該日期的比賽
            basedir = os.path.abspath(os.path.dirname(__file__))
            db_path = os.path.join(basedir, "instance", "mlb_data.db")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 只處理有真實博彩盤口的比賽
            cursor.execute("""
                SELECT DISTINCT g.game_id, g.home_team, g.away_team
                FROM games g
                JOIN betting_odds bo ON g.game_id = bo.game_id
                WHERE g.date = ?
                AND bo.market_type = 'totals'
                AND bo.bookmaker = 'bet365'
                ORDER BY g.game_id
            """, (current_date.strftime('%Y-%m-%d'),))
            
            games = cursor.fetchall()
            conn.close()
            
            if games:
                print(f"   找到 {len(games)} 場有真實盤口的比賽")
                
                # 為每場比賽生成預測
                for game_id, home_team, away_team in games:
                    try:
                        # 計算訓練截止日期（比賽日期-1）
                        training_end_date = current_date - timedelta(days=1)
                        
                        # 生成預測
                        result = predictor.predict_game_comprehensive(
                            game_id,
                            target_date=current_date,
                            training_end_date=training_end_date,
                            force_real_odds=True  # 強制使用真實盤口
                        )
                        
                        if result:
                            total_generated += 1
                            
                    except Exception as e:
                        print(f"   ⚠️  比賽 {away_team}@{home_team} 預測失敗: {e}")
                        continue
            
            current_date += timedelta(days=1)
        
        print(f"\n✅ 總共重新生成了 {total_generated} 個預測")
        
    except Exception as e:
        print(f"❌ 重新生成預測失敗: {e}")
        import traceback
        traceback.print_exc()

def verify_real_odds_only():
    """驗證所有預測都使用真實盤口"""
    print("\n✅ 驗證預測數據質量")
    print("=" * 70)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查最終結果
        cursor.execute("""
            SELECT 
                g.date,
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN bo.bookmaker = 'bet365' THEN 1 END) as with_real_odds,
                COUNT(CASE WHEN p.is_correct = 1 THEN 1 END) as correct_predictions,
                COUNT(CASE WHEN p.is_correct IS NOT NULL THEN 1 END) as with_results
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            LEFT JOIN betting_odds bo ON p.game_id = bo.game_id 
                AND bo.market_type = 'totals' AND bo.bookmaker = 'bet365'
                AND ABS(bo.total_point - p.over_under_line) < 0.1
            WHERE g.date >= '2025-06-01' AND g.date <= '2025-07-10'
            AND p.model_version = 'unified_v1.0'
            GROUP BY g.date
            ORDER BY g.date DESC
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        print("日期       | 總預測 | 真實盤口 | 正確數 | 有結果 | 準確率")
        print("-" * 60)
        
        total_all = 0
        total_real = 0
        total_correct = 0
        total_with_results = 0
        
        for date_str, total, real, correct, with_results in results:
            accuracy = (correct / with_results * 100) if with_results > 0 else 0
            real_rate = (real / total * 100) if total > 0 else 0
            
            print(f"{date_str} | {total:6d} | {real:8d} | {correct:6d} | {with_results:6d} | {accuracy:6.1f}%")
            
            total_all += total
            total_real += real
            total_correct += correct
            total_with_results += with_results
        
        overall_accuracy = (total_correct / total_with_results * 100) if total_with_results > 0 else 0
        overall_real_rate = (total_real / total_all * 100) if total_all > 0 else 0
        
        print("-" * 60)
        print(f"總計       | {total_all:6d} | {total_real:8d} | {total_correct:6d} | {total_with_results:6d} | {overall_accuracy:6.1f}%")
        print(f"真實盤口率: {overall_real_rate:.1f}%")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")

if __name__ == '__main__':
    fix_prediction_odds_source()
    verify_real_odds_only()
    
    print("\n" + "=" * 70)
    print("🎯 修復完成！現在所有預測都使用真實的bet365博彩盤口")
    print("請重新運行預測查看修復效果")
    print("=" * 70)
