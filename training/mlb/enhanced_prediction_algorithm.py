#!/usr/bin/env python3
"""
增強型MLB預測算法
基於準確性分析結果設計的改良版本

主要改進:
1. 解決系統性偏高問題
2. 提高勝負預測準確率  
3. 更好的信心度校準
4. 多模型集成預測
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, accuracy_score
from datetime import datetime, date, timedelta
from app import create_app
from models.database import db, Game, TeamStats, PredictionHistory
import joblib
import logging
from typing import Dict, List, Tuple, Any
import warnings

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedMLBPredictor:
    """增強型MLB預測器"""
    
    def __init__(self):
        self.app = create_app()
        self.models = {}
        self.scalers = {}
        self.is_trained = False
        self.feature_importance = {}
        
        # 偏差校正參數 (基於分析發現預測總分偏高)
        self.bias_correction = {
            'home_score_bias': -0.15,  # 主隊得分偏差校正
            'away_score_bias': -0.25,  # 客隊得分偏差校正 
            'total_score_bias': -0.40   # 總分偏差校正
        }
        
        # 多模型權重
        self.model_weights = {
            'rf_home': 0.30,    # 隨機森林 - 主隊
            'gb_home': 0.25,    # 梯度提升 - 主隊
            'ridge_home': 0.20, # Ridge回歸 - 主隊
            'elastic_home': 0.25, # ElasticNet - 主隊
            'rf_away': 0.30,    # 隨機森林 - 客隊
            'gb_away': 0.25,    # 梯度提升 - 客隊
            'ridge_away': 0.20, # Ridge回歸 - 客隊
            'elastic_away': 0.25  # ElasticNet - 客隊
        }
    
    def prepare_enhanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """準備增強特徵"""
        print("🔧 準備增強特徵...")
        
        # 基礎統計特徵
        feature_cols = []
        
        # 主隊特徵
        home_features = [
            'home_runs_scored_avg', 'home_runs_allowed_avg', 'home_batting_avg',
            'home_era', 'home_win_percentage', 'home_recent_form'
        ]
        feature_cols.extend(home_features)
        
        # 客隊特徵
        away_features = [
            'away_runs_scored_avg', 'away_runs_allowed_avg', 'away_batting_avg', 
            'away_era', 'away_win_percentage', 'away_recent_form'
        ]
        feature_cols.extend(away_features)
        
        # 新增增強特徵
        enhanced_features = []
        
        # 1. 攻擊力 vs 防守力匹配度
        df['home_offense_vs_away_defense'] = df['home_runs_scored_avg'] / (df['away_runs_allowed_avg'] + 0.1)
        df['away_offense_vs_home_defense'] = df['away_runs_scored_avg'] / (df['home_runs_allowed_avg'] + 0.1)
        enhanced_features.extend(['home_offense_vs_away_defense', 'away_offense_vs_home_defense'])
        
        # 2. 綜合實力對比
        df['home_team_strength'] = df['home_win_percentage'] * 0.4 + (1/df['home_era']) * 0.3 + df['home_batting_avg'] * 0.3
        df['away_team_strength'] = df['away_win_percentage'] * 0.4 + (1/df['away_era']) * 0.3 + df['away_batting_avg'] * 0.3
        df['strength_difference'] = df['home_team_strength'] - df['away_team_strength']
        enhanced_features.extend(['home_team_strength', 'away_team_strength', 'strength_difference'])
        
        # 3. 近期表現權重
        df['home_form_weighted'] = df['home_recent_form'] * 1.2  # 增加近期表現權重
        df['away_form_weighted'] = df['away_recent_form'] * 1.2
        enhanced_features.extend(['home_form_weighted', 'away_form_weighted'])
        
        # 4. 得分預期 (基於歷史對戰)
        df['expected_home_runs'] = (df['home_runs_scored_avg'] + (5.0 - df['away_runs_allowed_avg'])) / 2
        df['expected_away_runs'] = (df['away_runs_scored_avg'] + (5.0 - df['home_runs_allowed_avg'])) / 2
        enhanced_features.extend(['expected_home_runs', 'expected_away_runs'])
        
        # 5. 勝負預測特徵
        df['win_probability_base'] = 0.5 + df['strength_difference'] * 0.3
        df['win_probability_base'] = np.clip(df['win_probability_base'], 0.1, 0.9)
        enhanced_features.append('win_probability_base')
        
        feature_cols.extend(enhanced_features)
        
        # 處理缺失值
        df[feature_cols] = df[feature_cols].fillna(df[feature_cols].median())
        
        print(f"   ✅ 特徵準備完成，共 {len(feature_cols)} 個特徵")
        return df[feature_cols]
    
    def train_enhanced_models(self, days_back: int = 365):
        """訓練增強模型"""
        with self.app.app_context():
            print(f"🚀 開始訓練增強預測模型 (過去 {days_back} 天數據)...")
            
            # 獲取訓練數據
            end_date = date.today() - timedelta(days=1)
            start_date = end_date - timedelta(days=days_back)
            
            # 查詢已完成的比賽
            completed_games = db.session.query(Game).filter(
                Game.date >= start_date,
                Game.date <= end_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None),
                Game.game_status == 'completed'
            ).all()
            
            if len(completed_games) < 100:
                raise ValueError(f"訓練數據不足，僅有 {len(completed_games)} 場比賽")
            
            print(f"   找到 {len(completed_games)} 場已完成比賽用於訓練")
            
            # 準備訓練數據
            training_data = []
            for game in completed_games:
                # 獲取球隊統計數據 (這裡簡化處理)
                features = self._get_game_features(game)
                if features is not None:
                    features.update({
                        'home_score': game.home_score,
                        'away_score': game.away_score
                    })
                    training_data.append(features)
            
            if len(training_data) < 50:
                raise ValueError("有效訓練數據不足")
            
            df = pd.DataFrame(training_data)
            print(f"   ✅ 準備了 {len(df)} 條訓練樣本")
            
            # 準備特徵和目標
            X = self.prepare_enhanced_features(df)
            y_home = df['home_score'].values
            y_away = df['away_score'].values
            
            # 標準化特徵
            self.scalers['features'] = StandardScaler()
            X_scaled = self.scalers['features'].fit_transform(X)
            
            # 訓練多個模型
            self._train_ensemble_models(X_scaled, y_home, y_away)
            
            self.is_trained = True
            print("🎉 模型訓練完成！")
    
    def _get_game_features(self, game) -> Dict:
        """獲取比賽特徵 (簡化版本)"""
        try:
            # 這裡應該從TeamStats或其他表獲取實際統計數據
            # 為簡化，使用模擬數據
            return {
                'home_runs_scored_avg': np.random.normal(4.5, 1.0),
                'home_runs_allowed_avg': np.random.normal(4.5, 1.0),
                'home_batting_avg': np.random.normal(0.250, 0.030),
                'home_era': np.random.normal(4.20, 0.50),
                'home_win_percentage': np.random.normal(0.500, 0.100),
                'home_recent_form': np.random.normal(0.500, 0.150),
                'away_runs_scored_avg': np.random.normal(4.5, 1.0),
                'away_runs_allowed_avg': np.random.normal(4.5, 1.0),
                'away_batting_avg': np.random.normal(0.250, 0.030),
                'away_era': np.random.normal(4.20, 0.50),
                'away_win_percentage': np.random.normal(0.500, 0.100),
                'away_recent_form': np.random.normal(0.500, 0.150)
            }
        except:
            return None
    
    def _train_ensemble_models(self, X: np.ndarray, y_home: np.ndarray, y_away: np.ndarray):
        """訓練集成模型"""
        print("🏗️ 訓練集成模型...")
        
        # 定義模型
        models_config = {
            'rf': RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1),
            'gb': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'ridge': Ridge(alpha=1.0, random_state=42),
            'elastic': ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42)
        }
        
        # 時間序列交叉驗證
        tscv = TimeSeriesSplit(n_splits=5)
        
        # 訓練主隊得分模型
        for name, model in models_config.items():
            model_key = f"{name}_home"
            print(f"   訓練 {model_key} 模型...")
            
            # 交叉驗證
            cv_scores = cross_val_score(model, X, y_home, cv=tscv, 
                                      scoring='neg_mean_absolute_error')
            print(f"     CV MAE: {-cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
            
            # 訓練最終模型
            model.fit(X, y_home)
            self.models[model_key] = model
        
        # 訓練客隊得分模型
        for name, model in models_config.items():
            model_key = f"{name}_away"
            print(f"   訓練 {model_key} 模型...")
            
            # 創建新實例避免干擾
            model_copy = type(model)(**model.get_params())
            
            # 交叉驗證
            cv_scores = cross_val_score(model_copy, X, y_away, cv=tscv,
                                      scoring='neg_mean_absolute_error')
            print(f"     CV MAE: {-cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
            
            # 訓練最終模型
            model_copy.fit(X, y_away)
            self.models[model_key] = model_copy
        
        # 計算特徵重要性（來自隨機森林）
        if 'rf_home' in self.models:
            feature_names = [f'feature_{i}' for i in range(X.shape[1])]
            importance = self.models['rf_home'].feature_importances_
            self.feature_importance = dict(zip(feature_names, importance))
        
        print("   ✅ 集成模型訓練完成")
    
    def predict_enhanced(self, game_features: Dict) -> Dict:
        """增強預測"""
        if not self.is_trained:
            raise ValueError("模型尚未訓練，請先調用 train_enhanced_models()")
        
        # 準備特徵
        df = pd.DataFrame([game_features])
        X = self.prepare_enhanced_features(df)
        X_scaled = self.scalers['features'].transform(X)
        
        # 集成預測
        home_predictions = []
        away_predictions = []
        
        # 主隊預測
        for model_name in ['rf_home', 'gb_home', 'ridge_home', 'elastic_home']:
            if model_name in self.models:
                pred = self.models[model_name].predict(X_scaled)[0]
                weight = self.model_weights.get(model_name, 0.25)
                home_predictions.append(pred * weight)
        
        # 客隊預測
        for model_name in ['rf_away', 'gb_away', 'ridge_away', 'elastic_away']:
            if model_name in self.models:
                pred = self.models[model_name].predict(X_scaled)[0]
                weight = self.model_weights.get(model_name, 0.25)
                away_predictions.append(pred * weight)
        
        # 集成結果
        raw_home_score = sum(home_predictions)
        raw_away_score = sum(away_predictions)
        
        # 應用偏差校正
        corrected_home_score = raw_home_score + self.bias_correction['home_score_bias']
        corrected_away_score = raw_away_score + self.bias_correction['away_score_bias']
        
        # 確保得分合理
        corrected_home_score = max(0.5, corrected_home_score)
        corrected_away_score = max(0.5, corrected_away_score)
        
        # 計算信心度
        total_predicted = corrected_home_score + corrected_away_score
        strength_diff = abs(game_features.get('strength_difference', 0))
        
        # 改進信心度計算
        base_confidence = 0.6  # 基礎信心度
        strength_confidence = min(0.3, strength_diff * 0.5)  # 實力差距帶來的信心度
        variance_penalty = 0.1 * abs(total_predicted - 9.0) / 9.0  # 偏離正常得分的懲罰
        
        confidence = base_confidence + strength_confidence - variance_penalty
        confidence = np.clip(confidence, 0.1, 0.95)
        
        # 勝負預測 (改進版)
        score_diff = corrected_home_score - corrected_away_score
        win_prob_base = game_features.get('win_probability_base', 0.5)
        
        # 基於得分差和實力差的勝率調整
        if score_diff > 0.5:
            home_win_prob = min(0.9, win_prob_base + 0.1 + score_diff * 0.05)
        elif score_diff < -0.5:
            home_win_prob = max(0.1, win_prob_base - 0.1 + score_diff * 0.05)
        else:
            home_win_prob = win_prob_base
        
        return {
            'predicted_home_score': round(corrected_home_score, 2),
            'predicted_away_score': round(corrected_away_score, 2),
            'predicted_total_runs': round(corrected_home_score + corrected_away_score, 2),
            'home_win_probability': round(home_win_prob, 3),
            'away_win_probability': round(1 - home_win_prob, 3),
            'confidence': round(confidence, 3),
            'raw_predictions': {
                'raw_home_score': round(raw_home_score, 2),
                'raw_away_score': round(raw_away_score, 2)
            },
            'bias_corrections': self.bias_correction,
            'model_info': {
                'algorithm': 'enhanced_ensemble_v2',
                'models_used': len(self.models),
                'features_count': X.shape[1]
            }
        }
    
    def save_models(self, filepath_prefix: str = "enhanced_mlb_predictor"):
        """保存模型"""
        if not self.is_trained:
            raise ValueError("沒有訓練好的模型可以保存")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存所有模型和標準化器
        model_data = {
            'models': self.models,
            'scalers': self.scalers,
            'bias_correction': self.bias_correction,
            'model_weights': self.model_weights,
            'feature_importance': self.feature_importance,
            'is_trained': self.is_trained,
            'trained_at': timestamp
        }
        
        filepath = f"{filepath_prefix}_{timestamp}.joblib"
        joblib.dump(model_data, filepath)
        print(f"✅ 增強模型已保存至: {filepath}")
        
        return filepath
    
    def load_models(self, filepath: str):
        """加載模型"""
        try:
            model_data = joblib.load(filepath)
            self.models = model_data['models']
            self.scalers = model_data['scalers']
            self.bias_correction = model_data['bias_correction']
            self.model_weights = model_data['model_weights']
            self.feature_importance = model_data['feature_importance']
            self.is_trained = model_data['is_trained']
            
            print(f"✅ 增強模型已加載: {filepath}")
            print(f"   模型數量: {len(self.models)}")
            print(f"   訓練時間: {model_data.get('trained_at', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ 加載模型失敗: {e}")
            raise

def main():
    """主函數 - 示例用法"""
    predictor = EnhancedMLBPredictor()
    
    try:
        # 訓練模型
        print("開始訓練增強預測模型...")
        predictor.train_enhanced_models(days_back=180)  # 使用過去6個月數據
        
        # 保存模型
        saved_path = predictor.save_models()
        
        # 測試預測
        sample_features = {
            'home_runs_scored_avg': 5.2,
            'home_runs_allowed_avg': 4.1,
            'home_batting_avg': 0.265,
            'home_era': 3.80,
            'home_win_percentage': 0.580,
            'home_recent_form': 0.650,
            'away_runs_scored_avg': 4.8,
            'away_runs_allowed_avg': 4.6,
            'away_batting_avg': 0.248,
            'away_era': 4.20,
            'away_win_percentage': 0.520,
            'away_recent_form': 0.480
        }
        
        prediction = predictor.predict_enhanced(sample_features)
        
        print("\n🎯 測試預測結果:")
        print(f"   主隊得分: {prediction['predicted_home_score']}")
        print(f"   客隊得分: {prediction['predicted_away_score']}")
        print(f"   預測總分: {prediction['predicted_total_runs']}")
        print(f"   主隊勝率: {prediction['home_win_probability']}")
        print(f"   信心度: {prediction['confidence']}")
        
        print(f"\n📊 模型信息:")
        print(f"   算法: {prediction['model_info']['algorithm']}")
        print(f"   使用模型數: {prediction['model_info']['models_used']}")
        print(f"   特徵數量: {prediction['model_info']['features_count']}")
        
        print("\n✅ 增強預測系統準備就緒！")
        
    except Exception as e:
        print(f"❌ 訓練失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()