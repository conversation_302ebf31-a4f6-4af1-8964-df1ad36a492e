#!/usr/bin/env python3
"""
測試投手因素增強後的預測準確度
包含模型重新訓練和準確度對比
"""

import sys
import os
from datetime import date, timedelta
import pandas as pd
import numpy as np

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.improved_predictor import ImprovedMLBPredictor
from models.enhanced_feature_engineer import EnhancedFeatureEngineer
from models.database import Game, Prediction
from models.unified_betting_predictor import UnifiedBettingPredictor

def test_model_training_with_pitcher_factors():
    """測試包含投手因素的模型訓練"""
    app = create_app()
    
    with app.app_context():
        print("🚀 測試投手因素增強模型訓練")
        print("=" * 60)
        
        # 初始化改進預測器
        predictor = ImprovedMLBPredictor()
        
        print("📊 準備訓練數據...")
        
        # 獲取訓練數據 (6月1日到6月27日)
        training_games = Game.query.filter(
            Game.date >= date(2025, 6, 1),
            Game.date <= date(2025, 6, 27),
            Game.home_score.isnot(None),
            Game.away_score.isnot(None),
            Game.game_status == 'completed'
        ).all()
        
        print(f"📈 找到 {len(training_games)} 場訓練比賽")
        
        if len(training_games) < 50:
            print("❌ 訓練數據不足，需要至少50場比賽")
            return False
        
        # 訓練模型
        print("\n🎯 開始訓練增強模型...")
        try:
            # 使用正確的訓練方法
            training_result = predictor.train_advanced_ensemble_models(
                start_date=date(2025, 6, 1),
                end_date=date(2025, 6, 27)
            )

            # 檢查訓練是否成功（通過檢查模型是否存在）
            if hasattr(predictor, 'models') and predictor.models:
                print("✅ 模型訓練成功！")

                # 顯示模型性能
                performance = training_result.get('performance', {})
                if performance:
                    print("\n📊 模型性能指標:")
                    for metric, value in performance.items():
                        if isinstance(value, (int, float)):
                            print(f"  {metric}: {value:.3f}")
                        else:
                            print(f"  {metric}: {value}")

                # 顯示訓練摘要
                if hasattr(predictor, 'training_summary'):
                    summary = predictor.training_summary
                    print(f"\n📈 訓練摘要:")
                    print(f"  主隊得分模型 MAE: {summary.get('home_score_mae', 'N/A')}")
                    print(f"  客隊得分模型 MAE: {summary.get('away_score_mae', 'N/A')}")
                    print(f"  勝負預測模型 MAE: {summary.get('home_win_mae', 'N/A')}")

                # 設置訓練狀態
                predictor.is_trained = True
                return True
            else:
                print(f"❌ 模型訓練失敗: 模型未正確初始化")
                return False
                
        except Exception as e:
            print(f"❌ 訓練過程出錯: {e}")
            return False

def test_prediction_accuracy_comparison():
    """測試預測準確度對比"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("📊 預測準確度對比測試")
        print("=" * 60)
        
        # 獲取測試數據 (6月28日)
        test_date = date(2025, 6, 28)
        test_games = Game.query.filter(
            Game.date == test_date,
            Game.home_score.isnot(None),
            Game.away_score.isnot(None),
            Game.game_status == 'completed'
        ).all()
        
        print(f"📅 測試日期: {test_date}")
        print(f"🎯 測試比賽數: {len(test_games)}")
        
        if not test_games:
            print("❌ 找不到測試比賽")
            return
        
        # 初始化預測器
        predictor = ImprovedMLBPredictor()
        feature_engineer = EnhancedFeatureEngineer()
        
        correct_predictions = 0
        total_predictions = 0
        score_errors = []
        pitcher_factor_impacts = []
        
        print(f"\n🔍 逐場分析:")
        
        for i, game in enumerate(test_games, 1):
            try:
                print(f"\n{i}. {game.away_team} @ {game.home_team}")
                print(f"   實際比分: {game.away_score}-{game.home_score}")
                
                # 提取特徵（包含投手因素）
                features = feature_engineer.extract_comprehensive_features(
                    game.home_team, game.away_team, game.date
                )
                
                if not features:
                    print("   ❌ 特徵提取失敗")
                    continue
                
                # 分析投手因素
                ace_duel = features.get('ace_pitcher_duel', 0)
                pitcher_advantage = features.get('pitcher_matchup_advantage', 0)
                home_quality = features.get('home_pitcher_quality', 50)
                away_quality = features.get('away_pitcher_quality', 50)
                
                print(f"   投手分析: 主隊{home_quality:.1f} vs 客隊{away_quality:.1f}")
                if ace_duel:
                    print("   🌟 王牌投手對決")
                
                # 進行預測
                try:
                    # 使用正確的預測方法
                    prediction = predictor.predict_game(game.home_team, game.away_team, game.date)

                    if prediction and 'predicted_home_score' in prediction:
                        pred_home = round(prediction['predicted_home_score'])
                        pred_away = round(prediction['predicted_away_score'])
                        
                        print(f"   預測比分: {pred_away}-{pred_home}")
                        
                        # 計算準確度
                        actual_winner = 'home' if game.home_score > game.away_score else 'away'
                        pred_winner = 'home' if pred_home > pred_away else 'away'
                        
                        if actual_winner == pred_winner:
                            correct_predictions += 1
                            print("   ✅ 勝負預測正確")
                        else:
                            print("   ❌ 勝負預測錯誤")
                        
                        total_predictions += 1
                        
                        # 計算得分誤差
                        home_error = abs(pred_home - game.home_score)
                        away_error = abs(pred_away - game.away_score)
                        total_error = home_error + away_error
                        score_errors.append(total_error)
                        
                        print(f"   得分誤差: {total_error:.1f}")
                        
                        # 記錄投手因素影響
                        pitcher_factor_impacts.append({
                            'ace_duel': ace_duel,
                            'pitcher_advantage': abs(pitcher_advantage),
                            'total_runs': game.home_score + game.away_score,
                            'prediction_error': total_error
                        })
                    
                    else:
                        print("   ❌ 預測失敗")
                
                except Exception as e:
                    print(f"   ❌ 預測錯誤: {e}")
                
            except Exception as e:
                print(f"   ❌ 分析錯誤: {e}")
        
        # 計算總體準確度
        if total_predictions > 0:
            accuracy = (correct_predictions / total_predictions) * 100
            avg_score_error = np.mean(score_errors) if score_errors else 0
            
            print(f"\n📈 總體結果:")
            print(f"  勝負預測準確度: {accuracy:.1f}% ({correct_predictions}/{total_predictions})")
            print(f"  平均得分誤差: {avg_score_error:.1f} 分")
            
            # 分析投手因素影響
            if pitcher_factor_impacts:
                ace_duel_games = [p for p in pitcher_factor_impacts if p['ace_duel'] == 1]
                normal_games = [p for p in pitcher_factor_impacts if p['ace_duel'] == 0]
                
                if ace_duel_games and normal_games:
                    ace_avg_error = np.mean([p['prediction_error'] for p in ace_duel_games])
                    normal_avg_error = np.mean([p['prediction_error'] for p in normal_games])
                    
                    print(f"\n🎯 投手因素影響分析:")
                    print(f"  王牌對決比賽預測誤差: {ace_avg_error:.1f} 分")
                    print(f"  普通比賽預測誤差: {normal_avg_error:.1f} 分")
                    
                    if ace_avg_error < normal_avg_error:
                        print("  ✅ 王牌投手因素提升了預測準確度！")
                    else:
                        print("  📊 王牌投手因素對準確度影響不明顯")

def test_unified_betting_predictions():
    """測試統一博彩預測系統"""
    app = create_app()
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("🎰 統一博彩預測系統測試")
        print("=" * 60)
        
        # 初始化統一預測器
        unified_predictor = UnifiedBettingPredictor(app)
        
        # 初始化預測模型
        try:
            unified_predictor.initialize_prediction_models()
            print("✅ 預測模型初始化成功")
        except Exception as e:
            print(f"❌ 預測模型初始化失敗: {e}")
            return
        
        # 獲取明天的比賽進行預測
        tomorrow = date.today() + timedelta(days=1)
        future_games = Game.query.filter(Game.date == tomorrow).limit(3).all()
        
        if not future_games:
            print("❌ 找不到明天的比賽")
            return
        
        print(f"📅 預測日期: {tomorrow}")
        print(f"🎯 預測比賽數: {len(future_games)}")
        
        for i, game in enumerate(future_games, 1):
            print(f"\n🎲 比賽 {i}: {game.away_team} @ {game.home_team}")
            
            try:
                # 生成統一預測
                prediction_result = unified_predictor.generate_unified_prediction(
                    game.game_id, tomorrow
                )
                
                if prediction_result.get('success'):
                    predictions = prediction_result.get('predictions', {})
                    
                    # 顯示比分預測
                    if 'score' in predictions:
                        score_pred = predictions['score']
                        print(f"  📊 比分預測: {score_pred.get('predicted_away_score', 'N/A')}-"
                              f"{score_pred.get('predicted_home_score', 'N/A')}")
                        print(f"  🏆 主隊勝率: {score_pred.get('home_win_probability', 0):.1%}")
                    
                    # 顯示大小分預測
                    if 'over_under' in predictions:
                        ou_pred = predictions['over_under']
                        print(f"  🎯 大小分: {ou_pred.get('predicted_total', 'N/A')} "
                              f"(建議: {ou_pred.get('recommendation', 'N/A')})")
                    
                    # 顯示讓分預測
                    if 'run_line' in predictions:
                        rl_pred = predictions['run_line']
                        print(f"  📈 讓分: {rl_pred.get('spread', 'N/A')} "
                              f"(建議: {rl_pred.get('recommendation', 'N/A')})")
                    
                    # 顯示質量評估
                    quality = prediction_result.get('quality_assessment', {})
                    if quality:
                        print(f"  ⭐ 預測質量: {quality.get('score', 0)}/100")
                        print(f"  📝 評級: {quality.get('grade', 'N/A')}")
                
                else:
                    print(f"  ❌ 預測失敗: {prediction_result.get('error', 'Unknown error')}")
            
            except Exception as e:
                print(f"  ❌ 預測錯誤: {e}")

if __name__ == "__main__":
    print("⚾ 投手因素增強預測系統完整測試")
    print("=" * 60)
    
    # 1. 測試模型訓練
    training_success = test_model_training_with_pitcher_factors()
    
    if training_success:
        # 2. 測試預測準確度
        test_prediction_accuracy_comparison()
        
        # 3. 測試統一博彩預測
        test_unified_betting_predictions()
    else:
        print("❌ 模型訓練失敗，跳過後續測試")
    
    print("\n✅ 完整測試結束")
