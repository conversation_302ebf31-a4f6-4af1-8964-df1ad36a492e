#!/usr/bin/env python3
"""
測試總分線模式提取
"""

import re

def test_total_pattern():
    """測試總分線模式提取"""
    print("=== 測試總分線模式提取 ===")
    
    # 從debug文件中提取的實際文本
    test_texts = [
        "Atlanta won the game at odds of -160. The total score of 6 was under 8.",
        "NY Mets won the game at odds of -130. The total score of 5 was under 9.",
        "St. Louis won the game at odds of -110. The total score of 11 was over 9.",
        "Detroit won the game at odds of -135. The total score of 6 was under 8.",
        "Cleveland won the game at odds of -135. The total score of 9 was over 8.5.",
        "Miami won the game at odds of -150. The total score of 11 was over 7.5.",
        "Minnesota won the game at odds of -150. The total score of 5 was under 8.5.",
        "Chi. Cubs won the game at odds of -150. The total score of 5 was under 9.",
        "Texas won the game at odds of -135. The total score of 15 was over 7.5.",
        "Kansas City won the game at odds of -170. The total score of 11 was over 10.5.",
        "Baltimore won the game at odds of -185. The total score of 9 was over 8.",
        "Milwaukee won the game at odds of +145. The total score of 11 was over 9.",
        "Arizona won the game at odds of +150. The total score of 10 was over 8.5.",
        "Toronto won the game at odds of +125. The total score of 9 was over 7.",
        "Boston won the game at odds of +110. The total score of 3 was under 8.5."
    ]
    
    # 測試模式
    total_pattern = r'total score of \d+ was (?:under|over) (\d+(?:\.\d+)?)'
    
    print(f"使用模式: {total_pattern}")
    print()
    
    for i, text in enumerate(test_texts):
        print(f"測試 {i+1}: {text}")
        
        total_match = re.search(total_pattern, text, re.IGNORECASE)
        
        if total_match:
            total_line = total_match.group(1)
            print(f"  ✅ 找到總分線: {total_line}")
            print(f"  完整匹配: {total_match.group(0)}")
        else:
            print(f"  ❌ 未找到總分線")
        
        print()

if __name__ == "__main__":
    test_total_pattern()
