#!/usr/bin/env python3
"""
測試全面數據抓取系統
"""

import sys
import os
import logging

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db
from comprehensive_data_manager import ComprehensiveDataManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_comprehensive_data_system():
    """測試全面數據系統"""
    logger.info("=== 開始測試全面數據抓取系統 ===")
    
    try:
        # 創建管理器
        manager = ComprehensiveDataManager()
        
        # 1. 初始化數據庫
        logger.info("1. 初始化數據庫...")
        if manager.initialize_database():
            logger.info("✅ 數據庫初始化成功")
        else:
            logger.error("❌ 數據庫初始化失敗")
            return False
        
        # 2. 檢查當前數據狀態
        logger.info("2. 檢查數據狀態...")
        stats = manager.check_data_status()
        if stats:
            logger.info("✅ 數據狀態檢查完成")
        else:
            logger.error("❌ 數據狀態檢查失敗")
        
        # 3. 測試單個球隊數據抓取
        logger.info("3. 測試單個球隊數據抓取...")
        test_teams = ['LAA', 'NYY', 'LAD']  # 測試幾個球隊
        
        for team_code in test_teams:
            logger.info(f"測試球隊: {team_code}")
            success = manager.fetch_single_team_data(team_code)
            if success:
                logger.info(f"✅ {team_code} 數據抓取成功")
            else:
                logger.warning(f"⚠️ {team_code} 數據抓取失敗")
        
        # 4. 再次檢查數據狀態
        logger.info("4. 檢查更新後的數據狀態...")
        updated_stats = manager.check_data_status()
        
        # 5. 比較前後差異
        if stats and updated_stats:
            logger.info("=== 數據更新對比 ===")
            for key in ['team_advanced_stats', 'team_chemistry', 'player_trends']:
                before = stats.get(key, 0)
                after = updated_stats.get(key, 0)
                diff = after - before
                logger.info(f"{key}: {before} -> {after} (+{diff})")
        
        logger.info("=== 測試完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"測試過程中發生錯誤: {e}")
        return False

def test_data_models():
    """測試數據模型"""
    logger.info("=== 測試數據模型 ===")
    
    try:
        app = create_app()
        
        with app.app_context():
            # 測試導入所有模型
            from models.enhanced_data_models import (
                TeamAdvancedStats, PlayerInjuryReport, PlayerPerformanceTrends,
                WeatherConditions, TeamChemistry
            )
            
            logger.info("✅ 所有增強數據模型導入成功")
            
            # 測試創建表
            db.create_all()
            logger.info("✅ 數據庫表創建成功")
            
            # 測試基本操作
            # 創建一個測試記錄
            test_stats = TeamAdvancedStats(
                team_id=108,  # LAA
                season=2024,
                core_lineup_avg=0.250,
                bullpen_era=4.50
            )
            
            db.session.add(test_stats)
            db.session.commit()
            
            # 查詢測試
            found_stats = TeamAdvancedStats.query.filter_by(
                team_id=108, season=2024
            ).first()
            
            if found_stats:
                logger.info("✅ 數據模型基本操作測試成功")
                logger.info(f"測試數據: team_id={found_stats.team_id}, season={found_stats.season}")
                
                # 清理測試數據
                db.session.delete(found_stats)
                db.session.commit()
                logger.info("✅ 測試數據清理完成")
            else:
                logger.error("❌ 數據模型操作測試失敗")
                return False
            
        return True
        
    except Exception as e:
        logger.error(f"數據模型測試失敗: {e}")
        return False

def main():
    """主函數"""
    logger.info("開始全面數據系統測試...")
    
    # 測試數據模型
    if not test_data_models():
        logger.error("數據模型測試失敗，停止測試")
        return
    
    # 測試全面數據系統
    if test_comprehensive_data_system():
        logger.info("🎉 全面數據系統測試成功！")
    else:
        logger.error("💥 全面數據系統測試失敗！")

if __name__ == '__main__':
    main()
