#!/bin/bash

# MLB 系統端口快速清理腳本
echo "🧹 MLB 系統端口快速清理工具"
echo "=================================="

# 檢查端口管理工具是否存在
if [ ! -f "port_manager.py" ]; then
    echo "❌ 找不到 port_manager.py"
    exit 1
fi

# 顯示當前端口狀態
echo "📊 當前端口狀態:"
python port_manager.py --check

echo ""
read -p "🤔 是否要清理被佔用的端口？ (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 開始清理..."
    python port_manager.py --cleanup
    
    echo ""
    echo "✅ 清理完成！更新後的端口狀態:"
    python port_manager.py --check
    
    echo ""
    echo "💡 現在可以執行以下命令啟動系統："
    echo "   ./start.sh"
else
    echo "❌ 取消清理"
fi

echo ""
echo "🔧 其他可用命令："
echo "   python port_manager.py --check     # 檢查端口狀態"
echo "   python port_manager.py --cleanup   # 清理所有被佔用端口"
echo "   python port_manager.py --find      # 尋找可用端口"
echo "   python port_manager.py --port 5502 --cleanup  # 清理特定端口"