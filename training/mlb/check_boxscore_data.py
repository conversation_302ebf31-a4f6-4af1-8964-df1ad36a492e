#!/usr/bin/env python3
"""
檢查Box Score數據完整性
分析比賽是否有詳細的球員統計數據
"""

import sys
sys.path.append('.')

import os
from datetime import datetime
from app import app

def check_boxscore_data(target_date='2025-08-29'):
    """檢查指定日期的Box Score數據完整性"""
    
    print(f"🔍 檢查 {target_date} Box Score 數據狀況")
    print("=" * 60)
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    with app.app_context():
        try:
            from models.database import db, Game, PlayerStats, PlayerGameStats
            
            # 查詢該日期的比賽
            games = db.session.query(Game).filter(
                Game.date == target_date
            ).all()
            
            print(f"📊 {target_date} 比賽數量: {len(games)}")
            
            if not games:
                print(f"❌ 未找到 {target_date} 的比賽記錄")
                return
            
            # 檢查每場比賽的詳細數據
            total_games = len(games)
            games_with_player_stats = 0
            games_with_pitcher_stats = 0
            games_without_boxscore = 0
            
            print(f"\n🏟️ 比賽Box Score檢查:")
            
            for i, game in enumerate(games, 1):
                print(f"\n   {i:2d}. {game.away_team} @ {game.home_team} (ID: {game.game_id})")
                print(f"       比分: {game.away_score or 'N/A'} - {game.home_score or 'N/A'}")
                print(f"       狀態: {game.game_status}")
                
                # 檢查球員統計數據
                player_stats_count = db.session.query(PlayerStats).filter(
                    PlayerStats.game_id == game.game_id
                ).count()
                
                # 檢查單場比賽統計數據  
                game_stats_count = db.session.query(PlayerGameStats).filter(
                    PlayerGameStats.game_id == game.game_id
                ).count()
                
                print(f"       球員統計: {player_stats_count} 條記錄")
                print(f"       單場統計: {game_stats_count} 條記錄")
                
                # 統計分析
                if player_stats_count > 0:
                    games_with_player_stats += 1
                if game_stats_count > 0:
                    games_with_pitcher_stats += 1
                if player_stats_count == 0 and game_stats_count == 0:
                    games_without_boxscore += 1
                    print(f"       ⚠️ 缺少Box Score數據")
                else:
                    print(f"       ✅ 有Box Score數據")
            
            # 生成總結報告
            print(f"\n📈 Box Score 數據總結:")
            print(f"   總比賽數: {total_games}")
            print(f"   有球員統計: {games_with_player_stats} 場 ({games_with_player_stats/total_games*100:.1f}%)")
            print(f"   有單場統計: {games_with_pitcher_stats} 場 ({games_with_pitcher_stats/total_games*100:.1f}%)")
            print(f"   缺少Box Score: {games_without_boxscore} 場 ({games_without_boxscore/total_games*100:.1f}%)")
            
            # 分析問題嚴重程度
            if games_without_boxscore == 0:
                print(f"\n🎉 所有比賽都有完整的Box Score數據")
            elif games_without_boxscore < total_games * 0.2:
                print(f"\n⚠️ 少數比賽缺少Box Score數據，需要補充下載")
            else:
                print(f"\n🚨 大量比賽缺少Box Score數據，需要批量下載")
            
            # 提供解決建議
            if games_without_boxscore > 0:
                print(f"\n🔧 建議操作:")
                print(f"   1. 使用BoxscoreDataCollector補充下載缺失的數據")
                print(f"   2. 檢查MLB API連接是否正常")
                print(f"   3. 驗證比賽ID格式是否正確")
                print(f"   4. 考慮使用批量Box Score下載工具")
            
            return {
                'total_games': total_games,
                'games_with_player_stats': games_with_player_stats,
                'games_with_pitcher_stats': games_with_pitcher_stats,
                'games_without_boxscore': games_without_boxscore,
                'completeness_percentage': ((total_games - games_without_boxscore) / total_games * 100) if total_games > 0 else 0
            }
            
        except Exception as e:
            print(f"❌ 檢查過程失敗: {e}")
            import traceback
            traceback.print_exc()
            return None

def get_boxscore_sample_data(target_date='2025-08-29'):
    """獲取Box Score樣本數據結構"""
    
    print(f"\n🔬 分析 Box Score 數據結構樣本:")
    
    with app.app_context():
        try:
            from models.database import db, Game, PlayerStats, PlayerGameStats
            
            # 找一場有數據的比賽作為樣本
            sample_game = db.session.query(Game).filter(
                Game.date == target_date,
                Game.game_status == 'completed'
            ).first()
            
            if not sample_game:
                print("   ❌ 找不到樣本比賽")
                return
            
            print(f"   📋 樣本比賽: {sample_game.away_team} @ {sample_game.home_team}")
            
            # 檢查球員統計樣本
            player_sample = db.session.query(PlayerStats).filter(
                PlayerStats.game_id == sample_game.game_id
            ).first()
            
            if player_sample:
                print(f"   ✅ 球員統計樣本存在")
                print(f"      球員: {player_sample.player_name or 'N/A'}")
                print(f"      位置: {player_sample.position or 'N/A'}")
                print(f"      打數: {player_sample.at_bats or 'N/A'}")
                print(f"      安打: {player_sample.hits or 'N/A'}")
            else:
                print(f"   ❌ 無球員統計數據")
            
            # 檢查單場統計樣本
            game_sample = db.session.query(PlayerGameStats).filter(
                PlayerGameStats.game_id == sample_game.game_id
            ).first()
            
            if game_sample:
                print(f"   ✅ 單場統計樣本存在")
                print(f"      球員: {game_sample.player_id or 'N/A'}")
                print(f"      位置: {game_sample.position or 'N/A'}")
                print(f"      球隊: {game_sample.team_id or 'N/A'}")
            else:
                print(f"   ❌ 無單場統計數據")
                
        except Exception as e:
            print(f"   ❌ 樣本數據分析失敗: {e}")

if __name__ == "__main__":
    print("🏟️ MLB Box Score 數據完整性檢查工具")
    print("檢查比賽是否有詳細的球員和投手統計數據")
    print()
    
    # 檢查8/29的Box Score數據
    result = check_boxscore_data('2025-08-29')
    
    # 分析數據結構
    get_boxscore_sample_data('2025-08-29')
    
    if result:
        print(f"\n🎯 總結:")
        print(f"   Box Score完整性: {result['completeness_percentage']:.1f}%")
        if result['games_without_boxscore'] > 0:
            print(f"   需要補充下載 {result['games_without_boxscore']} 場比賽的Box Score")
        else:
            print(f"   所有比賽都有完整的Box Score數據")
    
    print(f"\n📋 下一步:")
    print(f"   1. 如果缺少Box Score，創建補充下載工具")
    print(f"   2. 整合Box Score下載到Web界面")
    print(f"   3. 驗證數據質量和完整性")