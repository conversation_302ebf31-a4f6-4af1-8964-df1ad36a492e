#!/usr/bin/env python3
"""
測試投手狀態標示功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app import create_app
from models.database import db, Game, Prediction
from models.enhanced_feature_engineer import EnhancedFeatureEngineer

def test_pitcher_status_display():
    """測試投手狀態顯示功能"""
    app = create_app()
    
    with app.app_context():
        print("🔍 測試投手狀態標示功能")
        print("=" * 80)
        
        # 測試案例
        test_cases = [
            {
                'game_id': '777165',
                'description': 'NYM @ BAL (Game 1) - 應該找到投手和數據'
            },
            {
                'game_id': '777181', 
                'description': 'NYM @ BAL (Game 2) - 應該找到投手和數據'
            },
            {
                'game_id': '777170',
                'description': 'TB @ BOS - 應該找到投手和數據'
            }
        ]
        
        feature_engineer = EnhancedFeatureEngineer()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🎯 測試案例 {i}: {test_case['description']}")
            print("-" * 60)
            
            # 查找比賽
            game = Game.query.filter_by(game_id=test_case['game_id']).first()
            
            if not game:
                print(f"❌ 找不到比賽 ID: {test_case['game_id']}")
                continue
                
            print(f"📅 比賽: {game.away_team} @ {game.home_team} ({game.date})")
            print(f"🏆 實際比分: {game.away_score} - {game.home_score}")
            
            try:
                # 提取特徵
                features = feature_engineer.extract_comprehensive_features(
                    home_team=game.home_team,
                    away_team=game.away_team,
                    game_date=game.date,
                    game_id=test_case['game_id']
                )
                
                # 顯示投手狀態信息
                print(f"\n🏠 主隊投手 ({game.home_team}):")
                display_pitcher_status(
                    features.get('home_probable_starter'),
                    features.get('home_pitcher_status'),
                    features.get('home_pitcher_data_status'),
                    features.get('home_pitcher_source'),
                    features.get('home_pitcher_data_source'),
                    features.get('home_pitcher_era'),
                    features.get('home_pitcher_whip')
                )
                
                print(f"\n✈️  客隊投手 ({game.away_team}):")
                display_pitcher_status(
                    features.get('away_probable_starter'),
                    features.get('away_pitcher_status'),
                    features.get('away_pitcher_data_status'),
                    features.get('away_pitcher_source'),
                    features.get('away_pitcher_data_source'),
                    features.get('away_pitcher_era'),
                    features.get('away_pitcher_whip')
                )
                
                # 檢查預測
                prediction = Prediction.query.filter_by(game_id=test_case['game_id']).first()
                if prediction:
                    print(f"\n📊 預測結果:")
                    print(f"   預測比分: {prediction.predicted_away_score:.1f} - {prediction.predicted_home_score:.1f}")
                    print(f"   信心度: {prediction.confidence:.1%}")
                    print(f"   模型版本: {prediction.model_version}")
                else:
                    print(f"\n❌ 沒有找到預測結果")
                    
            except Exception as e:
                print(f"❌ 特徵提取失敗: {e}")

def display_pitcher_status(pitcher_name, pitcher_status, data_status, pitcher_source, data_source, era, whip):
    """顯示投手狀態信息"""
    
    # 投手姓名
    if pitcher_name:
        print(f"   👤 投手: {pitcher_name}")
    else:
        print(f"   ❌ 投手: 未知")
    
    # 投手狀態
    if pitcher_status == 'found':
        print(f"   ✅ 投手狀態: 找到投手")
    elif pitcher_status == 'not_found':
        print(f"   ❌ 投手狀態: 找不到投手")
    else:
        print(f"   ⚠️  投手狀態: {pitcher_status or '未知'}")
    
    # 數據狀態
    if data_status == 'found':
        print(f"   ✅ 數據狀態: 有統計數據")
    elif data_status == 'missing_stats':
        print(f"   ⚠️  數據狀態: 找不到投手數據")
    elif data_status == 'error':
        print(f"   ❌ 數據狀態: 數據錯誤")
    else:
        print(f"   ❓ 數據狀態: {data_status or '未知'}")
    
    # 數據來源
    print(f"   📍 投手來源: {pitcher_source or '未知'}")
    print(f"   📊 數據來源: {data_source or '未知'}")
    
    # 統計數據
    if era is not None and whip is not None:
        print(f"   📈 統計: ERA={era:.2f}, WHIP={whip:.2f}")
    else:
        print(f"   📈 統計: 無可用數據")

def test_status_combinations():
    """測試各種狀態組合的顯示"""
    print(f"\n🧪 測試狀態組合顯示")
    print("=" * 80)
    
    test_combinations = [
        {
            'name': 'Clayton Kershaw',
            'pitcher_status': 'found',
            'data_status': 'found',
            'pitcher_source': 'daily_lineup_api',
            'data_source': 'exact_match_current_year',
            'era': 2.85,
            'whip': 1.12,
            'description': '理想情況 - 找到投手和數據'
        },
        {
            'name': 'Unknown Pitcher',
            'pitcher_status': 'found',
            'data_status': 'missing_stats',
            'pitcher_source': 'game_record',
            'data_source': 'default_values',
            'era': 4.50,
            'whip': 1.30,
            'description': '找到投手但沒有統計數據'
        },
        {
            'name': None,
            'pitcher_status': 'not_found',
            'data_status': 'no_stats',
            'pitcher_source': 'none',
            'data_source': 'none',
            'era': None,
            'whip': None,
            'description': '完全找不到投手信息'
        },
        {
            'name': 'Error Pitcher',
            'pitcher_status': 'error',
            'data_status': 'error',
            'pitcher_source': 'none',
            'data_source': 'default_values',
            'era': 4.50,
            'whip': 1.30,
            'description': '獲取投手信息時發生錯誤'
        }
    ]
    
    for i, combo in enumerate(test_combinations, 1):
        print(f"\n🎯 組合 {i}: {combo['description']}")
        print("-" * 40)
        display_pitcher_status(
            combo['name'],
            combo['pitcher_status'],
            combo['data_status'],
            combo['pitcher_source'],
            combo['data_source'],
            combo['era'],
            combo['whip']
        )

def generate_status_summary():
    """生成狀態標示說明"""
    print(f"\n📋 投手狀態標示說明")
    print("=" * 80)
    
    print(f"\n🎯 投手狀態 (pitcher_status):")
    print(f"   ✅ found      - 成功找到投手信息")
    print(f"   ❌ not_found  - 找不到投手信息")
    print(f"   ❌ error      - 獲取投手信息時發生錯誤")
    
    print(f"\n📊 數據狀態 (data_status):")
    print(f"   ✅ found         - 找到投手統計數據")
    print(f"   ⚠️  missing_stats - 找不到投手統計數據 (使用默認值)")
    print(f"   ❌ error         - 獲取統計數據時發生錯誤")
    print(f"   ❓ no_stats      - 沒有嘗試獲取統計數據")
    
    print(f"\n📍 投手來源 (pitcher_source):")
    print(f"   • daily_lineup_api - 從每日打線API獲取")
    print(f"   • game_record      - 從比賽記錄獲取")
    print(f"   • database         - 從數據庫獲取")
    print(f"   • none             - 無來源")
    
    print(f"\n📈 數據來源 (data_source):")
    print(f"   • exact_match_current_year    - 當年精確匹配")
    print(f"   • fuzzy_match_current_year    - 當年模糊匹配")
    print(f"   • fuzzy_match_previous_year   - 去年模糊匹配")
    print(f"   • default_values              - 使用默認值")
    print(f"   • none                        - 無數據來源")

if __name__ == "__main__":
    test_pitcher_status_display()
    test_status_combinations()
    generate_status_summary()
