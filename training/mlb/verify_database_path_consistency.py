#!/usr/bin/env python3
"""
驗證所有程式的資料庫路徑一致性
檢查項目中所有Python文件的資料庫配置是否統一使用instance/mlb_data.db
"""

import os
import re
from collections import defaultdict

def scan_database_references():
    """掃描所有Python文件中的資料庫路徑引用"""
    
    print("🔍 MLB 資料庫路徑一致性檢查")
    print("=" * 70)
    
    # 定義要搜索的資料庫路徑模式
    patterns = {
        'correct_instance': r'instance/mlb_data\.db',
        'correct_full_path': r'/Users/<USER>/python/training/mlb/instance/mlb_data\.db',
        'wrong_data_folder': r'data/mlb_data\.db',
        'wrong_root': r'sqlite:///mlb_data\.db',
        'wrong_direct': r'mlb_data\.db(?!.*instance)',
        'other_db': r'mlb_prediction\.db|betting_results\.db'
    }
    
    results = defaultdict(list)
    
    # 掃描所有Python文件
    for root, dirs, files in os.walk('.'):
        # 跳過一些不需要檢查的目錄
        if any(skip_dir in root for skip_dir in ['.git', '__pycache__', '.pytest_cache', 'node_modules']):
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, '.')
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        # 檢查每個模式
                        for pattern_name, pattern in patterns.items():
                            matches = re.finditer(pattern, content)
                            for match in matches:
                                line_start = content.rfind('\n', 0, match.start()) + 1
                                line_end = content.find('\n', match.start())
                                if line_end == -1:
                                    line_end = len(content)
                                
                                line_content = content[line_start:line_end].strip()
                                line_num = content[:match.start()].count('\n') + 1
                                
                                results[pattern_name].append({
                                    'file': relative_path,
                                    'line': line_num,
                                    'content': line_content,
                                    'match': match.group()
                                })
                
                except Exception as e:
                    print(f"   ⚠️ 無法讀取文件 {relative_path}: {e}")
    
    # 分析結果
    print(f"\n📊 掃描結果統計:")
    
    total_files_scanned = sum(len(files) for files in results.values())
    unique_files = set()
    for files in results.values():
        for item in files:
            unique_files.add(item['file'])
    
    print(f"   📁 掃描的文件數: {len(unique_files)}")
    print(f"   🔍 發現的引用數: {sum(len(refs) for refs in results.values())}")
    
    # 顯示結果
    print(f"\n✅ 正確的路徑使用:")
    correct_count = 0
    
    if results['correct_instance']:
        print(f"   📋 使用 'instance/mlb_data.db': {len(results['correct_instance'])} 個")
        for ref in results['correct_instance'][:5]:  # 顯示前5個
            print(f"      📝 {ref['file']}:{ref['line']}")
        if len(results['correct_instance']) > 5:
            print(f"      ... 還有 {len(results['correct_instance'])-5} 個文件")
        correct_count += len(results['correct_instance'])
    
    if results['correct_full_path']:
        print(f"   📋 使用完整路徑: {len(results['correct_full_path'])} 個")
        for ref in results['correct_full_path'][:3]:
            print(f"      📝 {ref['file']}:{ref['line']}")
        if len(results['correct_full_path']) > 3:
            print(f"      ... 還有 {len(results['correct_full_path'])-3} 個文件")
        correct_count += len(results['correct_full_path'])
    
    # 顯示錯誤的路徑
    print(f"\n❌ 需要修正的路徑:")
    error_count = 0
    
    if results['wrong_data_folder']:
        print(f"   🚫 使用 'data/mlb_data.db': {len(results['wrong_data_folder'])} 個")
        for ref in results['wrong_data_folder']:
            print(f"      ❌ {ref['file']}:{ref['line']} - {ref['content']}")
        error_count += len(results['wrong_data_folder'])
    
    if results['wrong_root']:
        print(f"   🚫 使用根目錄路徑: {len(results['wrong_root'])} 個")
        for ref in results['wrong_root']:
            print(f"      ❌ {ref['file']}:{ref['line']} - {ref['content']}")
        error_count += len(results['wrong_root'])
    
    if results['wrong_direct']:
        print(f"   🚫 直接使用 'mlb_data.db': {len(results['wrong_direct'])} 個")
        for ref in results['wrong_direct']:
            print(f"      ❌ {ref['file']}:{ref['line']} - {ref['content']}")
        error_count += len(results['wrong_direct'])
    
    # 其他資料庫文件
    if results['other_db']:
        print(f"\n📊 其他資料庫文件: {len(results['other_db'])} 個")
        for ref in results['other_db'][:3]:
            print(f"   ℹ️ {ref['file']}:{ref['line']} - {ref['match']}")
    
    # 總結
    print(f"\n🎯 一致性檢查總結:")
    print(f"   ✅ 正確路徑: {correct_count} 個引用")
    print(f"   ❌ 錯誤路徑: {error_count} 個引用")
    
    if error_count == 0:
        print(f"   🎉 所有程式都使用正確的資料庫路徑!")
    else:
        print(f"   ⚠️ 發現 {error_count} 個需要修正的路徑")
    
    # 生成修復建議
    if error_count > 0:
        print(f"\n💡 修復建議:")
        
        if results['wrong_data_folder']:
            print(f"   🔧 修復 data/mlb_data.db → instance/mlb_data.db")
        
        if results['wrong_root']:
            print(f"   🔧 修復 sqlite:///mlb_data.db → sqlite:///instance/mlb_data.db")
        
        if results['wrong_direct']:
            print(f"   🔧 檢查直接引用，確保路徑正確")
    
    return error_count == 0

if __name__ == "__main__":
    print("🔍 MLB 資料庫路徑一致性驗證工具")
    print("檢查所有程式是否使用正確的 instance/mlb_data.db 路徑")
    print("")
    
    success = scan_database_references()
    
    if success:
        print(f"\n✅ 資料庫路徑一致性檢查通過")
        print(f"🎯 所有程式都使用正確的資料庫配置")
    else:
        print(f"\n⚠️ 發現資料庫路徑不一致問題")
        print(f"💡 請根據上述建議修正相關文件")