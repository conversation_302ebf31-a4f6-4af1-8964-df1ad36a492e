#!/usr/bin/env python3
"""
監控Box Score下載進度
"""

import time
import sqlite3
from datetime import datetime

def check_boxscore_progress():
    """檢查Box Score下載進度"""
    try:
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        print(f"\n📊 Box Score下載進度 - {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 60)
        
        # 總Box Score數量
        cursor.execute("SELECT COUNT(*) FROM box_scores")
        total_boxscores = cursor.fetchone()[0]
        print(f"總Box Score數量: {total_boxscores:,}")
        
        # 按年份統計
        years = [2023, 2024, 2025]
        for year in years:
            # 該年份已完成的比賽總數
            cursor.execute("""
                SELECT COUNT(*) FROM games 
                WHERE strftime('%Y', date) = ? AND game_status = 'completed'
            """, (str(year),))
            total_completed = cursor.fetchone()[0]
            
            # 該年份有Box Score的比賽數
            cursor.execute("""
                SELECT COUNT(*) FROM games g
                JOIN box_scores bs ON g.game_id = bs.game_id
                WHERE strftime('%Y', g.date) = ? AND g.game_status = 'completed'
            """, (str(year),))
            with_boxscore = cursor.fetchone()[0]
            
            if total_completed > 0:
                percentage = (with_boxscore / total_completed) * 100
                missing = total_completed - with_boxscore
                status = "✅" if percentage >= 99 else "🔄" if percentage >= 50 else "❌"
                print(f"{year}年: {status} {with_boxscore:,}/{total_completed:,} ({percentage:.1f}%) - 缺失 {missing:,} 場")
            else:
                print(f"{year}年: 沒有已完成的比賽")
        
        # 最近下載的Box Score
        cursor.execute("""
            SELECT g.date, g.away_team || ' @ ' || g.home_team as matchup
            FROM box_scores bs
            JOIN games g ON bs.game_id = g.game_id
            ORDER BY bs.box_score_id DESC
            LIMIT 5
        """)
        recent_boxscores = cursor.fetchall()
        
        if recent_boxscores:
            print("\n最近下載的Box Score:")
            for date, matchup in recent_boxscores:
                print(f"  {date}: {matchup}")
        
        conn.close()
        
    except Exception as e:
        print(f"檢查進度失敗: {e}")

if __name__ == '__main__':
    while True:
        check_boxscore_progress()
        time.sleep(60)  # 每分鐘檢查一次
