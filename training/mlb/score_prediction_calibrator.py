#!/usr/bin/env python3
"""
分數預測校正系統
解決批次預測結果與實際比分差距過大的問題
"""

import sys
sys.path.append('.')

import numpy as np
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from sklearn.isotonic import IsotonicRegression
from sklearn.preprocessing import StandardScaler
from scipy import stats
import sqlite3

from app import app
from models.database import db, Game, Prediction, BettingOdds
from models.prediction_history_manager import PredictionHistoryManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ScorePredictionCalibrator:
    """分數預測校正器"""
    
    def __init__(self):
        self.home_score_calibrator = None
        self.away_score_calibrator = None
        self.total_score_calibrator = None
        self.variance_adjuster = None
        self.is_fitted = False
        
        # 校正參數
        self.min_games_for_calibration = 50
        self.calibration_lookback_days = 90
        
        # 分數範圍統計
        self.actual_score_stats = {}
        self.predicted_score_stats = {}
        
    def analyze_prediction_accuracy(self, days_back: int = 30) -> Dict:
        """分析預測準確性"""
        with app.app_context():
            # 獲取最近的預測和實際結果
            cutoff_date = datetime.now().date() - timedelta(days=days_back)
            
            query = db.session.query(
                Prediction.predicted_home_score,
                Prediction.predicted_away_score,
                Game.home_score,
                Game.away_score,
                Game.home_team,
                Game.away_team,
                Game.date
            ).join(
                Game, Prediction.game_id == Game.game_id
            ).filter(
                Game.date >= cutoff_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None),
                Prediction.predicted_home_score.isnot(None),
                Prediction.predicted_away_score.isnot(None)
            ).all()
            
            if not query:
                logger.warning("沒有找到可分析的預測數據")
                return {}
            
            # 轉換為DataFrame
            data = []
            for pred in query:
                data.append({
                    'predicted_home': float(pred.predicted_home_score),
                    'predicted_away': float(pred.predicted_away_score),
                    'predicted_total': float(pred.predicted_home_score) + float(pred.predicted_away_score),
                    'actual_home': pred.home_score,
                    'actual_away': pred.away_score,
                    'actual_total': pred.home_score + pred.away_score,
                    'home_team': pred.home_team,
                    'away_team': pred.away_team,
                    'date': pred.date
                })
            
            df = pd.DataFrame(data)
            
            # 計算統計數據
            analysis = {
                'total_games': len(df),
                'date_range': f"{df['date'].min()} to {df['date'].max()}",
                
                # 預測分佈統計
                'predicted_stats': {
                    'home_mean': df['predicted_home'].mean(),
                    'home_std': df['predicted_home'].std(),
                    'home_min': df['predicted_home'].min(),
                    'home_max': df['predicted_home'].max(),
                    'away_mean': df['predicted_away'].mean(),
                    'away_std': df['predicted_away'].std(),
                    'away_min': df['predicted_away'].min(),
                    'away_max': df['predicted_away'].max(),
                    'total_mean': df['predicted_total'].mean(),
                    'total_std': df['predicted_total'].std()
                },
                
                # 實際分佈統計
                'actual_stats': {
                    'home_mean': df['actual_home'].mean(),
                    'home_std': df['actual_home'].std(),
                    'home_min': df['actual_home'].min(),
                    'home_max': df['actual_home'].max(),
                    'away_mean': df['actual_away'].mean(),
                    'away_std': df['actual_away'].std(),
                    'away_min': df['actual_away'].min(),
                    'away_max': df['actual_away'].max(),
                    'total_mean': df['actual_total'].mean(),
                    'total_std': df['actual_total'].std()
                },
                
                # 準確性指標
                'accuracy_metrics': {
                    'home_mae': np.mean(np.abs(df['predicted_home'] - df['actual_home'])),
                    'away_mae': np.mean(np.abs(df['predicted_away'] - df['actual_away'])),
                    'total_mae': np.mean(np.abs(df['predicted_total'] - df['actual_total'])),
                    'home_rmse': np.sqrt(np.mean((df['predicted_home'] - df['actual_home']) ** 2)),
                    'away_rmse': np.sqrt(np.mean((df['predicted_away'] - df['actual_away']) ** 2)),
                    'total_rmse': np.sqrt(np.mean((df['predicted_total'] - df['actual_total']) ** 2))
                },
                
                # 偏差分析
                'bias_analysis': {
                    'home_bias': df['predicted_home'].mean() - df['actual_home'].mean(),
                    'away_bias': df['predicted_away'].mean() - df['actual_away'].mean(),
                    'total_bias': df['predicted_total'].mean() - df['actual_total'].mean()
                }
            }
            
            # 分數範圍分佈比較
            predicted_home_bins = np.histogram(df['predicted_home'], bins=range(0, 16))[0]
            actual_home_bins = np.histogram(df['actual_home'], bins=range(0, 16))[0]
            
            analysis['distribution_comparison'] = {
                'predicted_home_distribution': predicted_home_bins.tolist(),
                'actual_home_distribution': actual_home_bins.tolist(),
                'variance_ratio_home': df['actual_home'].var() / df['predicted_home'].var() if df['predicted_home'].var() > 0 else 1,
                'variance_ratio_away': df['actual_away'].var() / df['predicted_away'].var() if df['predicted_away'].var() > 0 else 1
            }
            
            return analysis
    
    def fit_calibration_curves(self, days_back: int = 90):
        """訓練校正曲線"""
        with app.app_context():
            logger.info(f"開始訓練校正曲線，使用過去 {days_back} 天的數據...")
            
            cutoff_date = datetime.now().date() - timedelta(days=days_back)
            
            # 獲取訓練數據
            query = db.session.query(
                Prediction.predicted_home_score,
                Prediction.predicted_away_score,
                Game.home_score,
                Game.away_score,
                BettingOdds.total_point,
                BettingOdds.home_spread_point,
                Game.home_team,
                Game.away_team
            ).outerjoin(
                Game, Prediction.game_id == Game.game_id
            ).outerjoin(
                BettingOdds, Game.game_id == BettingOdds.game_id
            ).filter(
                Game.date >= cutoff_date,
                Game.home_score.isnot(None),
                Game.away_score.isnot(None),
                Prediction.predicted_home_score.isnot(None),
                Prediction.predicted_away_score.isnot(None)
            ).all()
            
            if len(query) < self.min_games_for_calibration:
                logger.warning(f"數據量不足 ({len(query)} < {self.min_games_for_calibration})，無法進行校正")
                return False
            
            # 準備訓練數據
            predicted_home = np.array([float(p.predicted_home_score) for p in query])
            predicted_away = np.array([float(p.predicted_away_score) for p in query])
            predicted_total = predicted_home + predicted_away
            
            actual_home = np.array([p.home_score for p in query])
            actual_away = np.array([p.away_score for p in query])
            actual_total = actual_home + actual_away
            
            # 訓練等距回歸校正器
            self.home_score_calibrator = IsotonicRegression(out_of_bounds='clip')
            self.away_score_calibrator = IsotonicRegression(out_of_bounds='clip')
            self.total_score_calibrator = IsotonicRegression(out_of_bounds='clip')
            
            # 確保有足夠的唯一值進行訓練
            if len(np.unique(predicted_home)) > 1:
                self.home_score_calibrator.fit(predicted_home, actual_home)
            else:
                logger.warning("主場預測分數缺乏變異性，跳過主場校正")
                self.home_score_calibrator = None
                
            if len(np.unique(predicted_away)) > 1:
                self.away_score_calibrator.fit(predicted_away, actual_away)
            else:
                logger.warning("客場預測分數缺乏變異性，跳過客場校正")
                self.away_score_calibrator = None
                
            if len(np.unique(predicted_total)) > 1:
                self.total_score_calibrator.fit(predicted_total, actual_total)
            else:
                logger.warning("總分預測缺乏變異性，跳過總分校正")
                self.total_score_calibrator = None
            
            # 計算變異性調整因子
            self.variance_adjuster = {
                'home_scale': np.std(actual_home) / np.std(predicted_home) if np.std(predicted_home) > 0 else 1.0,
                'away_scale': np.std(actual_away) / np.std(predicted_away) if np.std(predicted_away) > 0 else 1.0,
                'home_mean_actual': np.mean(actual_home),
                'away_mean_actual': np.mean(actual_away),
                'home_mean_predicted': np.mean(predicted_home),
                'away_mean_predicted': np.mean(predicted_away)
            }
            
            self.is_fitted = True
            logger.info(f"校正曲線訓練完成，使用 {len(query)} 場比賽數據")
            logger.info(f"變異性調整因子 - 主場: {self.variance_adjuster['home_scale']:.3f}, 客場: {self.variance_adjuster['away_scale']:.3f}")
            
            return True
    
    def calibrate_prediction(self, predicted_home: float, predicted_away: float, 
                           context: Optional[Dict] = None) -> Tuple[float, float]:
        """校正單個預測結果"""
        if not self.is_fitted:
            logger.warning("校正器尚未訓練，先使用原始預測")
            return predicted_home, predicted_away
        
        # 基礎校正 - 使用等距回歸
        calibrated_home = predicted_home
        calibrated_away = predicted_away
        
        if self.home_score_calibrator:
            calibrated_home = float(self.home_score_calibrator.predict([predicted_home])[0])
        
        if self.away_score_calibrator:
            calibrated_away = float(self.away_score_calibrator.predict([predicted_away])[0])
        
        # 變異性增強 - 基於上下文調整
        if context:
            calibrated_home, calibrated_away = self._apply_contextual_adjustments(
                calibrated_home, calibrated_away, context
            )
        
        # 確保合理範圍
        calibrated_home = max(0, min(25, calibrated_home))
        calibrated_away = max(0, min(25, calibrated_away))
        
        return calibrated_home, calibrated_away
    
    def _apply_contextual_adjustments(self, home_score: float, away_score: float, 
                                    context: Dict) -> Tuple[float, float]:
        """應用上下文調整"""
        
        # 投手效應調整
        if 'pitcher_era_diff' in context:
            # ERA差異影響分數
            era_diff = context['pitcher_era_diff']  # 主場ERA - 客場ERA
            if era_diff > 1.0:  # 主場投手較弱
                home_score *= 0.9
                away_score *= 1.1
            elif era_diff < -1.0:  # 客場投手較弱
                home_score *= 1.1
                away_score *= 0.9
        
        # 球場效應調整
        if 'ballpark_factor' in context:
            factor = context['ballpark_factor']
            home_score *= factor
            away_score *= factor
        
        # 天氣效應調整
        if 'weather_impact' in context:
            weather = context['weather_impact']
            if weather == 'windy':
                # 強風可能降低得分
                home_score *= 0.95
                away_score *= 0.95
            elif weather == 'hot':
                # 炎熱天氣可能增加得分
                home_score *= 1.05
                away_score *= 1.05
        
        # 隊伍近期狀態調整
        if 'team_form' in context:
            home_form = context['team_form'].get('home', 1.0)
            away_form = context['team_form'].get('away', 1.0)
            home_score *= home_form
            away_score *= away_form
        
        return home_score, away_score
    
    def batch_calibrate_predictions(self, start_date: date, end_date: date) -> Dict:
        """批次校正歷史預測"""
        with app.app_context():
            logger.info(f"開始批次校正 {start_date} 到 {end_date} 的預測...")
            
            # 獲取需要校正的預測
            predictions = db.session.query(
                Prediction,
                Game
            ).join(
                Game, Prediction.game_id == Game.game_id
            ).filter(
                Game.date >= start_date,
                Game.date <= end_date,
                Prediction.predicted_home_score.isnot(None),
                Prediction.predicted_away_score.isnot(None)
            ).all()
            
            calibrated_count = 0
            total_improvement = 0
            
            for pred, game in predictions:
                original_home = float(pred.predicted_home_score)
                original_away = float(pred.predicted_away_score)
                
                # 準備上下文信息
                context = {
                    'home_team': game.home_team,
                    'away_team': game.away_team,
                    'date': game.date
                }
                
                # 校正預測
                calibrated_home, calibrated_away = self.calibrate_prediction(
                    original_home, original_away, context
                )
                
                # 記錄校正結果 (暫時不修改數據庫結構，僅用於分析)
                logger.debug(f"校正結果: {game.game_id} - 原始 {original_home:.1f}vs{original_away:.1f} → 校正 {calibrated_home:.1f}vs{calibrated_away:.1f}")
                
                # 如果有實際結果，計算改善程度
                if game.home_score is not None and game.away_score is not None:
                    original_error = abs(original_home - game.home_score) + abs(original_away - game.away_score)
                    calibrated_error = abs(calibrated_home - game.home_score) + abs(calibrated_away - game.away_score)
                    
                    if calibrated_error < original_error:
                        total_improvement += (original_error - calibrated_error)
                
                calibrated_count += 1
            
            logger.info(f"批次校正完成，處理了 {calibrated_count} 個預測")
            
            return {
                'calibrated_count': calibrated_count,
                'total_improvement': total_improvement,
                'avg_improvement': total_improvement / calibrated_count if calibrated_count > 0 else 0
            }

def main():
    """主函數 - 執行校正分析和訓練"""
    calibrator = ScorePredictionCalibrator()
    
    print("🔍 分析當前預測準確性...")
    analysis = calibrator.analyze_prediction_accuracy(days_back=30)
    
    if analysis:
        print(f"\n📊 預測準確性分析 (過去30天, {analysis['total_games']}場比賽):")
        print(f"   預測平均: 主場 {analysis['predicted_stats']['home_mean']:.2f}±{analysis['predicted_stats']['home_std']:.2f}, "
              f"客場 {analysis['predicted_stats']['away_mean']:.2f}±{analysis['predicted_stats']['away_std']:.2f}")
        print(f"   實際平均: 主場 {analysis['actual_stats']['home_mean']:.2f}±{analysis['actual_stats']['home_std']:.2f}, "
              f"客場 {analysis['actual_stats']['away_mean']:.2f}±{analysis['actual_stats']['away_std']:.2f}")
        print(f"   平均絕對誤差: 主場 {analysis['accuracy_metrics']['home_mae']:.2f}, "
              f"客場 {analysis['accuracy_metrics']['away_mae']:.2f}, 總分 {analysis['accuracy_metrics']['total_mae']:.2f}")
        print(f"   變異性比率: 主場 {analysis['distribution_comparison']['variance_ratio_home']:.2f}, "
              f"客場 {analysis['distribution_comparison']['variance_ratio_away']:.2f}")
        print(f"   偏差: 主場 {analysis['bias_analysis']['home_bias']:.2f}, "
              f"客場 {analysis['bias_analysis']['away_bias']:.2f}, 總分 {analysis['bias_analysis']['total_bias']:.2f}")
    
    print(f"\n🛠️ 訓練校正曲線...")
    if calibrator.fit_calibration_curves(days_back=90):
        print("✅ 校正曲線訓練成功")
        
        # 測試校正效果
        print("\n🧪 測試校正效果...")
        test_cases = [
            (4.5, 4.5, "標準預測"),
            (3.0, 6.0, "低客場高主場"),
            (6.0, 3.0, "高客場低主場"),
            (2.0, 2.0, "低分比賽"),
            (7.0, 7.0, "高分比賽")
        ]
        
        for home, away, desc in test_cases:
            cal_home, cal_away = calibrator.calibrate_prediction(home, away)
            print(f"   {desc}: {home:.1f}vs{away:.1f} → {cal_home:.1f}vs{cal_away:.1f}")
        
    else:
        print("❌ 校正曲線訓練失敗")

if __name__ == "__main__":
    main()