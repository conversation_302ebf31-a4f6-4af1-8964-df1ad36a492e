#!/usr/bin/env python3
"""
預測結果分析系統 - 建立完整的預測驗證和效果評估機制
"""

from datetime import datetime, date, timedelta
from flask import Flask
from models.database import db, Game, Prediction
import statistics

def analyze_prediction_accuracy(start_date, end_date=None):
    """分析預測準確性"""
    
    if end_date is None:
        end_date = start_date
    
    print(f"📊 預測準確性分析: {start_date} 到 {end_date}")
    print("=" * 60)
    
    # 查找有實際結果的預測
    predictions_with_results = db.session.query(Prediction, Game).join(
        Game, Prediction.game_id == Game.game_id
    ).filter(
        Game.date >= start_date,
        Game.date <= end_date,
        Game.home_score.isnot(None),  # 有實際比分
        Game.away_score.isnot(None)
    ).all()
    
    if not predictions_with_results:
        print("❌ 該日期範圍內沒有找到有實際結果的預測")
        return
    
    print(f"📈 找到 {len(predictions_with_results)} 場有實際結果的預測")
    print("\n" + "=" * 60)
    
    # 分析統計
    total_games = len(predictions_with_results)
    correct_winners = 0
    score_differences = []
    total_score_differences = []
    over_under_correct = 0
    confidence_scores = []
    
    print("🎯 詳細分析結果:")
    print("-" * 60)
    print(f"{'比賽':<20} {'預測':<15} {'實際':<15} {'勝負':<8} {'分差':<8} {'總分':<10}")
    print("-" * 60)
    
    for prediction, game in predictions_with_results:
        # 預測結果
        pred_home = prediction.predicted_home_score
        pred_away = prediction.predicted_away_score
        pred_total = pred_home + pred_away
        pred_winner = "主隊" if pred_home > pred_away else "客隊"
        
        # 實際結果
        actual_home = game.home_score
        actual_away = game.away_score  
        actual_total = actual_home + actual_away
        actual_winner = "主隊" if actual_home > actual_away else "客隊"
        
        # 勝負預測正確性
        winner_correct = pred_winner == actual_winner
        if winner_correct:
            correct_winners += 1
        
        # 分數差異
        home_diff = abs(pred_home - actual_home)
        away_diff = abs(pred_away - actual_away)
        avg_score_diff = (home_diff + away_diff) / 2
        score_differences.append(avg_score_diff)
        
        # 總分差異
        total_diff = abs(pred_total - actual_total)
        total_score_differences.append(total_diff)
        
        # 大小分預測 (假設線是8.5)
        over_under_line = 8.5
        pred_over = pred_total > over_under_line
        actual_over = actual_total > over_under_line
        if pred_over == actual_over:
            over_under_correct += 1
        
        # 信心度
        confidence_scores.append(prediction.confidence or 0.65)
        
        # 顯示結果
        matchup = f"{game.away_team}@{game.home_team}"
        pred_score = f"{pred_away:.1f}-{pred_home:.1f}"
        actual_score = f"{actual_away}-{actual_home}"
        winner_status = "✅" if winner_correct else "❌"
        
        print(f"{matchup:<20} {pred_score:<15} {actual_score:<15} {winner_status:<8} {avg_score_diff:<8.1f} {total_diff:<10.1f}")
    
    print("-" * 60)
    
    # 計算統計指標
    winner_accuracy = (correct_winners / total_games) * 100
    over_under_accuracy = (over_under_correct / total_games) * 100
    avg_score_diff = statistics.mean(score_differences)
    avg_total_diff = statistics.mean(total_score_differences)
    avg_confidence = statistics.mean(confidence_scores) * 100
    
    print("\n📊 整體表現統計:")
    print("=" * 40)
    print(f"總預測場數: {total_games}")
    print(f"勝負預測準確率: {winner_accuracy:.1f}% ({correct_winners}/{total_games})")
    print(f"大小分預測準確率: {over_under_accuracy:.1f}% ({over_under_correct}/{total_games})")
    print(f"平均分數差異: {avg_score_diff:.2f} 分")
    print(f"平均總分差異: {avg_total_diff:.2f} 分")
    print(f"平均信心度: {avg_confidence:.1f}%")
    
    # 評估預測品質
    print("\n🎯 預測品質評估:")
    print("=" * 40)
    
    if winner_accuracy >= 60:
        print("✅ 勝負預測: 優秀 (≥60%)")
    elif winner_accuracy >= 55:
        print("⚠️ 勝負預測: 良好 (55-60%)")
    elif winner_accuracy >= 50:
        print("⚠️ 勝負預測: 及格 (50-55%)")
    else:
        print("❌ 勝負預測: 需要改善 (<50%)")
    
    if avg_score_diff <= 1.5:
        print("✅ 分數預測: 優秀 (差異≤1.5分)")
    elif avg_score_diff <= 2.5:
        print("⚠️ 分數預測: 良好 (差異1.5-2.5分)")
    else:
        print("❌ 分數預測: 需要改善 (差異>2.5分)")
    
    if over_under_accuracy >= 55:
        print("✅ 大小分預測: 優秀 (≥55%)")
    elif over_under_accuracy >= 50:
        print("⚠️ 大小分預測: 及格 (50-55%)")
    else:
        print("❌ 大小分預測: 需要改善 (<50%)")
    
    # 改進建議
    print("\n💡 改進建議:")
    print("=" * 40)
    
    if winner_accuracy < 55:
        print("• 考慮調整投手分析權重")
        print("• 增加主場優勢係數")
        print("• 檢查球隊近期狀態分析")
    
    if avg_score_diff > 2.0:
        print("• 優化得分預測算法")
        print("• 考慮天氣因素影響")
        print("• 調整攻擊力評估模型")
    
    if over_under_accuracy < 52:
        print("• 改善總分預測模型")
        print("• 考慮球場因子影響")
        print("• 分析投手壓制力評估")
    
    print("\n🎉 分析完成！")
    
    return {
        'total_games': total_games,
        'winner_accuracy': winner_accuracy,
        'over_under_accuracy': over_under_accuracy,
        'avg_score_diff': avg_score_diff,
        'avg_total_diff': avg_total_diff,
        'avg_confidence': avg_confidence
    }

def check_recent_predictions():
    """檢查最近的預測和實際結果"""
    
    print("🔍 檢查最近預測結果...")
    print("=" * 50)
    
    # 檢查過去7天的預測
    end_date = date.today()
    start_date = end_date - timedelta(days=7)
    
    # 找到有預測但實際結果未更新的比賽
    pending_results = db.session.query(Prediction, Game).join(
        Game, Prediction.game_id == Game.game_id
    ).filter(
        Game.date >= start_date,
        Game.date < end_date,  # 不包含今天
        db.or_(Game.home_score.is_(None), Game.away_score.is_(None))
    ).all()
    
    print(f"📅 檢查日期範圍: {start_date} 到 {end_date}")
    print(f"⏳ 找到 {len(pending_results)} 場預測等待實際結果更新")
    
    if pending_results:
        print("\n等待結果更新的比賽:")
        for pred, game in pending_results[:10]:  # 顯示前10場
            print(f"  {game.date} {game.away_team} @ {game.home_team}")
    
    # 找到可以分析的比賽
    analyzable_games = db.session.query(Prediction, Game).join(
        Game, Prediction.game_id == Game.game_id
    ).filter(
        Game.date >= start_date,
        Game.date < end_date,
        Game.home_score.isnot(None),
        Game.away_score.isnot(None)
    ).count()
    
    print(f"✅ 可分析的比賽: {analyzable_games} 場")
    
    if analyzable_games > 0:
        print(f"\n建議運行分析命令:")
        print(f"python create_prediction_analysis_system.py analyze {start_date} {end_date - timedelta(days=1)}")
    
    return analyzable_games

def main():
    """主函數"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python create_prediction_analysis_system.py check  # 檢查最近預測")
        print("  python create_prediction_analysis_system.py analyze YYYY-MM-DD [YYYY-MM-DD]  # 分析準確性")
        print("  python create_prediction_analysis_system.py recent  # 分析最近7天")
        sys.exit(1)
    
    command = sys.argv[1]
    
    # 創建Flask應用上下文
    from app import app
    with app.app_context():
        if command == "check":
            check_recent_predictions()
        
        elif command == "analyze":
            if len(sys.argv) < 3:
                print("❌ 請提供開始日期 YYYY-MM-DD")
                sys.exit(1)
            
            start_date = datetime.strptime(sys.argv[2], '%Y-%m-%d').date()
            end_date = start_date
            
            if len(sys.argv) >= 4:
                end_date = datetime.strptime(sys.argv[3], '%Y-%m-%d').date()
            
            analyze_prediction_accuracy(start_date, end_date)
        
        elif command == "recent":
            end_date = date.today() - timedelta(days=1)  # 昨天
            start_date = end_date - timedelta(days=6)    # 過去7天
            analyze_prediction_accuracy(start_date, end_date)
        
        else:
            print(f"❌ 未知命令: {command}")
            sys.exit(1)

if __name__ == "__main__":
    main()