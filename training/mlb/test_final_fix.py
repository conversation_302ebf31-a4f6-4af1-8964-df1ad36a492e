#!/usr/bin/env python3
"""
測試最終修復結果
"""

from app import create_app
from models.automated_predictor import AutomatedPredictor
from datetime import date

def main():
    app = create_app()
    with app.app_context():
        print('🎯 測試今日比賽預測生成')
        print('=' * 50)
        
        predictor = AutomatedPredictor()
        result = predictor.manual_generate_predictions(date.today())
        
        if 'error' in result:
            print(f'❌ 預測生成失敗: {result["error"]}')
        else:
            print(f'✅ 預測生成成功')
            print(f'總比賽數: {result.get("total_games", "未知")}')
            print(f'成功預測: {result.get("successful_predictions", "未知")}')
            print(f'失敗預測: {result.get("failed_predictions", "未知")}')
            
            if result.get('successful_predictions', 0) > 0:
                print('\n🏆 預測成功！系統已完全修復')
            else:
                print('\n⚠️ 雖然沒有錯誤，但沒有成功的預測')

if __name__ == "__main__":
    main()
