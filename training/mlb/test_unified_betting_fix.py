#!/usr/bin/env python3
"""
測試統一博彩預測器的修復
"""

import sys
import os
from datetime import date

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def test_unified_betting_predictor_fix():
    """測試統一博彩預測器修復"""
    print("🔧 測試統一博彩預測器修復")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 導入統一博彩預測器
            from models.unified_betting_predictor import UnifiedBettingPredictor
            
            print("✅ 統一博彩預測器導入成功")
            
            # 初始化預測器
            predictor = UnifiedBettingPredictor()
            print("✅ 統一博彩預測器初始化成功")
            
            # 測試高級模型訓練
            print("\n🎯 測試高級模型訓練...")
            try:
                result = predictor.train_advanced_models()
                print(f"✅ 高級模型訓練結果: {result}")
                
                if result:
                    print("🎉 高級模型訓練成功！")
                else:
                    print("⚠️ 高級模型訓練返回False")
                    
            except Exception as e:
                print(f"❌ 高級模型訓練失敗: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # 測試基本模型訓練
            print("\n🎯 測試基本模型訓練...")
            try:
                basic_result = predictor.train_models()
                print(f"✅ 基本模型訓練結果: {basic_result}")
                
                if basic_result:
                    print("🎉 基本模型訓練成功！")
                else:
                    print("⚠️ 基本模型訓練返回False")
                    
            except Exception as e:
                print(f"❌ 基本模型訓練失敗: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # 測試預測功能
            print("\n🎯 測試預測功能...")
            try:
                prediction_result = predictor.predict_game_comprehensive(
                    home_team="CLE",
                    away_team="STL", 
                    game_date=date(2025, 6, 29)
                )
                
                print(f"✅ 預測結果: {prediction_result}")
                
                if prediction_result and prediction_result.get('success'):
                    print("🎉 預測功能正常工作！")
                    
                    # 檢查博彩預測
                    betting_predictions = prediction_result.get('betting_predictions', {})
                    if betting_predictions:
                        print("✅ 博彩預測包含在結果中")
                        
                        over_under = betting_predictions.get('over_under')
                        run_line = betting_predictions.get('run_line')
                        
                        if over_under:
                            print(f"   大小分: {over_under.get('recommendation', '無建議')}")
                        if run_line:
                            print(f"   讓分盤: {run_line.get('recommendation', '無建議')}")
                    else:
                        print("⚠️ 博彩預測未包含在結果中")
                        
                else:
                    print("⚠️ 預測功能返回失敗")
                    
            except Exception as e:
                print(f"❌ 預測功能失敗: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            print("\n🎉 所有測試通過！統一博彩預測器修復成功！")
            return True
            
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函數"""
    print("🔧 統一博彩預測器修復測試")
    print("=" * 60)
    
    success = test_unified_betting_predictor_fix()
    
    if success:
        print("\n✅ 修復測試成功！")
        print("📋 修復內容:")
        print("   ✅ 修復了 train_ensemble_models() 缺少參數的問題")
        print("   ✅ 改用 train_advanced_ensemble_models() 方法")
        print("   ✅ 統一博彩預測器現在可以正常工作")
    else:
        print("\n❌ 修復測試失敗！")
        print("⚠️ 需要進一步檢查和修復")
    
    return success

if __name__ == "__main__":
    main()
