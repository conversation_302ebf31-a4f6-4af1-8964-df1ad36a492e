#!/usr/bin/env python3
"""
快速模擬功能測試腳本
測試優化後的模擬API性能
"""

import requests
import time
import json
from datetime import datetime

def test_fast_simulation():
    """測試快速模擬API"""
    print("🚀 測試快速模擬功能")
    print("=" * 50)
    
    # 測試URL
    base_url = "http://127.0.0.1:5500"
    api_url = f"{base_url}/simulation/api/simulate_date"
    
    # 測試日期
    test_dates = [
        "2025-06-30",
        "2025-06-29", 
        "2025-06-28"
    ]
    
    for date_str in test_dates:
        print(f"\n📅 測試日期: {date_str}")
        
        # 記錄開始時間
        start_time = time.time()
        
        try:
            # 發送API請求
            response = requests.post(
                api_url,
                json={"date": date_str},
                headers={"Content-Type": "application/json"},
                timeout=10  # 10秒超時
            )
            
            # 記錄結束時間
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"⏱️  執行時間: {duration:.2f} 秒")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('simulation_result', {})
                    summary = result.get('summary', {})
                    
                    print(f"✅ 模擬成功")
                    print(f"   總比賽: {summary.get('total_games', 0)}")
                    print(f"   有預測: {summary.get('games_with_predictions', 0)}")
                    print(f"   覆蓋率: {summary.get('prediction_coverage', 0):.1f}%")
                    print(f"   狀態: {result.get('status', 'unknown')}")
                else:
                    print(f"❌ 模擬失敗: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP錯誤: {response.status_code}")
                print(f"   響應: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ 請求超時 (>10秒)")
        except requests.exceptions.RequestException as e:
            print(f"❌ 網絡錯誤: {e}")
        except Exception as e:
            print(f"❌ 未知錯誤: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 快速模擬測試完成")

def test_performance_comparison():
    """比較快速模擬與普通模擬的性能"""
    print("\n🏁 性能比較測試")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5500"
    test_date = "2025-06-30"
    
    # 測試快速模擬
    print(f"📊 測試快速模擬 API")
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{base_url}/simulation/api/simulate_date",
            json={"date": test_date},
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        fast_duration = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 快速模擬: {fast_duration:.2f} 秒")
            
            if data.get('success'):
                result = data.get('simulation_result', {})
                summary = result.get('summary', {})
                print(f"   結果: {summary.get('total_games', 0)} 場比賽")
            else:
                print(f"   錯誤: {data.get('error')}")
        else:
            print(f"❌ 快速模擬失敗: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 快速模擬錯誤: {e}")
    
    print(f"\n📈 性能總結:")
    print(f"   快速模擬: {fast_duration:.2f} 秒")
    
    if fast_duration < 2.0:
        print(f"🎉 性能優秀! (< 2秒)")
    elif fast_duration < 5.0:
        print(f"✅ 性能良好 (< 5秒)")
    else:
        print(f"⚠️  性能需要改進 (> 5秒)")

if __name__ == "__main__":
    print("🧪 快速模擬功能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 執行測試
    test_fast_simulation()
    test_performance_comparison()
    
    print(f"\n✨ 測試完成!")
