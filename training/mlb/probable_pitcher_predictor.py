#!/usr/bin/env python3
"""
可能先發投手預測器
為尚未開始的比賽預測先發投手
"""

import sys
sys.path.append('.')

from datetime import datetime, timedelta
from typing import Dict, Optional, List, Tuple
from models.database import db, Game, Team, Player, PlayerStats, PlayerGameStats
from sqlalchemy import text, func, and_, desc
import logging

logger = logging.getLogger(__name__)

class ProbablePitcherPredictor:
    """可能先發投手預測器"""
    
    def __init__(self):
        self.logger = logger
    
    def predict_starting_pitcher(self, game: Game, is_home: bool) -> Optional[Dict]:
        """預測先發投手"""
        try:
            team_code = game.home_team if is_home else game.away_team
            team_name = "主隊" if is_home else "客隊"
            
            self.logger.info(f"預測 {team_name} ({team_code}) 先發投手")
            
            # 方法1: 基於投手輪值週期
            pitcher_by_rotation = self.predict_by_rotation(team_code, game.date)
            if pitcher_by_rotation:
                self.logger.info(f"輪值預測成功: {pitcher_by_rotation['name']}")
                return pitcher_by_rotation
            
            # 方法2: 基於休息日數
            pitcher_by_rest = self.predict_by_rest_days(team_code, game.date)
            if pitcher_by_rest:
                self.logger.info(f"休息日預測成功: {pitcher_by_rest['name']}")
                return pitcher_by_rest
            
            # 方法3: 使用最佳投手
            pitcher_best = self.get_best_available_pitcher(team_code)
            if pitcher_best:
                self.logger.info(f"最佳投手預測: {pitcher_best['name']}")
                return pitcher_best
            
            # 方法4: 回退到團隊平均
            self.logger.warning(f"無法預測 {team_name} 先發投手，使用團隊平均")
            return self.get_team_average_with_prediction_note(team_code)
            
        except Exception as e:
            self.logger.error(f"預測先發投手失敗 ({team_code}): {e}")
            return self.get_team_average_with_prediction_note(team_code)
    
    def predict_by_rotation(self, team_code: str, game_date) -> Optional[Dict]:
        """基於投手輪值預測"""
        try:
            # 獲取該隊最近5次先發投手
            recent_starts = db.session.execute(text("""
                SELECT pgs.player_id, p.full_name, g.date, 
                       pgs.innings_pitched,
                       ROW_NUMBER() OVER (ORDER BY g.date DESC) as start_order
                FROM player_game_stats pgs
                JOIN games g ON pgs.game_id = g.game_id
                JOIN players p ON pgs.player_id = p.player_id
                JOIN teams t ON pgs.team_id = t.team_id
                WHERE t.team_code = :team_code
                AND pgs.innings_pitched > 3.0
                AND g.date < :game_date
                AND g.game_status = 'completed'
                ORDER BY g.date DESC
                LIMIT 10
            """), {
                "team_code": team_code,
                "game_date": game_date.strftime('%Y-%m-%d')
            }).fetchall()
            
            if len(recent_starts) >= 3:
                # 分析投手輪值模式
                rotation_pattern = self.analyze_rotation_pattern(recent_starts)
                next_pitcher = self.predict_next_in_rotation(rotation_pattern, game_date)
                
                if next_pitcher:
                    return self.format_predicted_pitcher(next_pitcher, 'rotation_analysis')
            
            return None
            
        except Exception as e:
            self.logger.error(f"輪值預測失敗: {e}")
            return None
    
    def predict_by_rest_days(self, team_code: str, game_date) -> Optional[Dict]:
        """基於休息日數預測"""
        try:
            # 查找該隊所有先發投手的最後登板日期
            pitcher_rest_days = db.session.execute(text("""
                SELECT 
                    p.player_id,
                    p.full_name,
                    MAX(g.date) as last_start_date,
                    JULIANDAY(:game_date) - JULIANDAY(MAX(g.date)) as rest_days,
                    COUNT(*) as starts_count,
                    AVG(pgs.innings_pitched) as avg_innings
                FROM player_game_stats pgs
                JOIN games g ON pgs.game_id = g.game_id
                JOIN players p ON pgs.player_id = p.player_id
                JOIN teams t ON pgs.team_id = t.team_id
                WHERE t.team_code = :team_code
                AND pgs.innings_pitched > 3.0
                AND g.date < :game_date
                AND g.date > date(:game_date, '-30 days')
                AND g.game_status = 'completed'
                GROUP BY p.player_id, p.full_name
                HAVING starts_count >= 2
                ORDER BY rest_days DESC, avg_innings DESC
            """), {
                "team_code": team_code,
                "game_date": game_date.strftime('%Y-%m-%d')
            }).fetchall()
            
            # 選擇休息日數最多且有先發經驗的投手
            for pitcher_data in pitcher_rest_days:
                rest_days = pitcher_data[3]
                # 一般先發投手需要4-5天休息
                if rest_days >= 4:
                    return self.format_predicted_pitcher({
                        'player_id': pitcher_data[0],
                        'player_name': pitcher_data[1],
                        'last_start': pitcher_data[2],
                        'rest_days': rest_days,
                        'avg_innings': pitcher_data[5]
                    }, 'rest_day_analysis')
            
            return None
            
        except Exception as e:
            self.logger.error(f"休息日預測失敗: {e}")
            return None
    
    def get_best_available_pitcher(self, team_code: str) -> Optional[Dict]:
        """獲取最佳可用投手"""
        try:
            # 查找該隊ERA最低的先發投手
            best_pitcher = db.session.execute(text("""
                SELECT 
                    ps.player_id,
                    ps.player_name,
                    ps.era,
                    ps.wins,
                    ps.losses,
                    ps.innings_pitched,
                    ps.strikeouts_pitching,
                    ps.whip
                FROM player_stats ps
                JOIN players p ON ps.player_id = p.player_id
                JOIN teams t ON p.current_team_id = t.team_id
                WHERE t.team_code = :team_code
                AND ps.position = 'P'
                AND ps.era IS NOT NULL
                AND ps.innings_pitched > 50
                ORDER BY ps.era ASC, ps.wins DESC
                LIMIT 1
            """), {"team_code": team_code}).fetchone()
            
            if best_pitcher:
                return self.format_predicted_pitcher({
                    'player_id': best_pitcher[0],
                    'player_name': best_pitcher[1],
                    'era': best_pitcher[2],
                    'wins': best_pitcher[3],
                    'losses': best_pitcher[4],
                    'innings_pitched': best_pitcher[5],
                    'strikeouts': best_pitcher[6],
                    'whip': best_pitcher[7]
                }, 'best_pitcher_analysis')
            
            return None
            
        except Exception as e:
            self.logger.error(f"最佳投手查詢失敗: {e}")
            return None
    
    def format_predicted_pitcher(self, pitcher_data: Dict, prediction_method: str) -> Dict:
        """格式化預測投手數據"""
        try:
            player_name = pitcher_data.get('player_name', 'Unknown')
            
            # 獲取詳細統計數據
            detailed_stats = self.get_pitcher_season_stats(
                pitcher_data.get('player_id'),
                player_name
            )
            
            return {
                'name': f"{player_name} (預測)",
                'player_id': pitcher_data.get('player_id'),
                'era': detailed_stats.get('era', pitcher_data.get('era', 4.50)),
                'wins': detailed_stats.get('wins', pitcher_data.get('wins', 0)),
                'losses': detailed_stats.get('losses', pitcher_data.get('losses', 0)),
                'saves': detailed_stats.get('saves', 0),
                'strikeouts': detailed_stats.get('strikeouts', pitcher_data.get('strikeouts', 0)),
                'walks': detailed_stats.get('walks', 0),
                'innings_pitched': float(detailed_stats.get('innings_pitched', pitcher_data.get('innings_pitched', 0))),
                'whip': detailed_stats.get('whip', pitcher_data.get('whip', 1.50)),
                'record': f"{detailed_stats.get('wins', 0)}-{detailed_stats.get('losses', 0)}",
                'strength': self.get_pitcher_strength_level(detailed_stats.get('era', 4.50)),
                'team': '',
                'season': 2024,
                'data_source': f'predicted_by_{prediction_method}',
                'prediction_info': {
                    'method': prediction_method,
                    'confidence': self.calculate_confidence(prediction_method, pitcher_data),
                    'note': self.get_prediction_note(prediction_method, pitcher_data)
                }
            }
            
        except Exception as e:
            self.logger.error(f"格式化預測投手失敗: {e}")
            return self.get_team_average_with_prediction_note('')
    
    def get_pitcher_season_stats(self, player_id: Optional[int], player_name: str) -> Dict:
        """獲取投手賽季統計"""
        try:
            if player_id:
                stats = PlayerStats.query.filter(
                    PlayerStats.player_id == player_id,
                    PlayerStats.position == 'P'
                ).order_by(PlayerStats.season.desc()).first()
            else:
                stats = PlayerStats.query.filter(
                    PlayerStats.player_name.like(f'%{player_name}%'),
                    PlayerStats.position == 'P'
                ).order_by(PlayerStats.season.desc()).first()
            
            if stats:
                return {
                    'era': float(stats.era) if stats.era else 4.50,
                    'wins': stats.wins or 0,
                    'losses': stats.losses or 0,
                    'saves': stats.saves or 0,
                    'strikeouts': stats.strikeouts_pitching or 0,
                    'walks': stats.walks_pitching or 0,
                    'innings_pitched': float(stats.innings_pitched) if stats.innings_pitched else 0,
                    'whip': float(stats.whip) if stats.whip else 1.50
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"查詢賽季統計失敗: {e}")
            return {}
    
    def analyze_rotation_pattern(self, recent_starts: List) -> List[Dict]:
        """分析投手輪值模式"""
        # 簡化的輪值分析 - 實際應用可以更複雜
        rotation = []
        for start in recent_starts:
            rotation.append({
                'player_id': start[0],
                'player_name': start[1],  # 這裡實際上是 full_name
                'date': start[2],
                'innings': start[3],
                'order': start[4]
            })
        return rotation
    
    def predict_next_in_rotation(self, rotation_pattern: List[Dict], game_date) -> Optional[Dict]:
        """預測輪值中的下一位投手"""
        # 簡化邏輯：選擇最久沒有先發的投手
        if rotation_pattern:
            # 按照最後先發日期排序，選擇最久沒有投球的
            rotation_pattern.sort(key=lambda x: x['date'])
            return rotation_pattern[0]
        return None
    
    def calculate_confidence(self, method: str, pitcher_data: Dict) -> float:
        """計算預測信心度"""
        if method == 'rotation_analysis':
            return 0.8  # 較高信心度
        elif method == 'rest_day_analysis':
            rest_days = pitcher_data.get('rest_days', 0)
            if rest_days >= 4:
                return 0.7
            return 0.5
        elif method == 'best_pitcher_analysis':
            return 0.6  # 中等信心度
        return 0.3
    
    def get_prediction_note(self, method: str, pitcher_data: Dict) -> str:
        """獲取預測說明"""
        if method == 'rotation_analysis':
            return "基於投手輪值分析預測"
        elif method == 'rest_day_analysis':
            rest_days = pitcher_data.get('rest_days', 0)
            return f"基於休息 {rest_days:.0f} 天分析預測"
        elif method == 'best_pitcher_analysis':
            return "基於投手能力評估預測"
        return "預測分析"
    
    def get_pitcher_strength_level(self, era: float) -> str:
        """根據 ERA 判斷投手等級"""
        if era <= 2.50:
            return '王牌'
        elif era <= 3.50:
            return '優秀'
        elif era <= 4.50:
            return '普通'
        else:
            return '掙扎中'
    
    def get_team_average_with_prediction_note(self, team_code: str) -> Dict:
        """獲取帶預測說明的團隊平均數據"""
        try:
            team_obj = Team.query.filter_by(team_code=team_code).first()
            team_name = team_obj.team_name if team_obj else team_code
            
            return {
                'name': f'{team_name} 可能先發',
                'player_id': None,
                'era': 4.50,
                'wins': 0,
                'losses': 0,
                'saves': 0,
                'strikeouts': 0,
                'walks': 0,
                'innings_pitched': 0.0,
                'whip': 1.50,
                'record': '0-0',
                'strength': '普通',
                'team': team_code,
                'season': 2024,
                'data_source': 'predicted_team_average',
                'prediction_info': {
                    'method': 'team_average',
                    'confidence': 0.2,
                    'note': '比賽開始前無法確定具體先發投手'
                }
            }
            
        except Exception as e:
            self.logger.error(f"獲取團隊平均失敗: {e}")
            return {
                'name': '待確定先發投手',
                'player_id': None,
                'era': 4.50,
                'wins': 0,
                'losses': 0,
                'saves': 0,
                'strikeouts': 0,
                'walks': 0,
                'innings_pitched': 0.0,
                'whip': 1.50,
                'record': '0-0',
                'strength': '普通',
                'team': team_code,
                'season': 2024,
                'data_source': 'unknown',
                'prediction_info': {
                    'method': 'fallback',
                    'confidence': 0.1,
                    'note': '無法預測先發投手'
                }
            }

def test_probable_pitcher_prediction():
    """測試可能先發投手預測"""
    print("🔮 測試可能先發投手預測系統")
    print("=" * 50)
    
    from app import app
    with app.app_context():
        try:
            # 測試 2025-09-28 的比賽
            game = Game.query.filter_by(game_id='776149').first()
            if game:
                print(f"測試比賽: {game.away_team} @ {game.home_team}")
                print(f"日期: {game.date}")
                print(f"狀態: {game.game_status}")
                
                predictor = ProbablePitcherPredictor()
                
                # 預測主隊投手
                home_pitcher = predictor.predict_starting_pitcher(game, True)
                print(f"\n主隊預測投手: {home_pitcher['name']}")
                print(f"預測方法: {home_pitcher['prediction_info']['method']}")
                print(f"信心度: {home_pitcher['prediction_info']['confidence']:.1%}")
                print(f"說明: {home_pitcher['prediction_info']['note']}")
                
                # 預測客隊投手
                away_pitcher = predictor.predict_starting_pitcher(game, False)
                print(f"\n客隊預測投手: {away_pitcher['name']}")
                print(f"預測方法: {away_pitcher['prediction_info']['method']}")
                print(f"信心度: {away_pitcher['prediction_info']['confidence']:.1%}")
                print(f"說明: {away_pitcher['prediction_info']['note']}")
                
            else:
                print("找不到測試比賽")
                
        except Exception as e:
            print(f"測試失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_probable_pitcher_prediction()