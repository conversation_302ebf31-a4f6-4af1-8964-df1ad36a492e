#!/usr/bin/env python3
"""
完整測試模擬系統
驗證所有修復和新功能
"""

import sys
import os
import json
import requests
from datetime import date, datetime

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simulation_page():
    """測試模擬頁面訪問"""
    print("🌐 測試模擬頁面訪問")
    print("=" * 80)
    
    base_url = "http://localhost:5500"
    page_url = f"{base_url}/simulation/date_simulation"
    
    try:
        response = requests.get(page_url, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ 頁面訪問成功: {page_url}")
            
            # 檢查關鍵元素
            content = response.text
            key_elements = [
                'optimized-simulate-btn',
                '優化預測',
                'showOptimizedResults',
                'optimized_simulate'
            ]
            
            for element in key_elements:
                if element in content:
                    print(f"   ✅ 找到關鍵元素: {element}")
                else:
                    print(f"   ❌ 缺少元素: {element}")
            
            return True
        else:
            print(f"❌ 頁面訪問失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 頁面訪問錯誤: {e}")
        return False

def test_regular_simulation():
    """測試常規模擬功能"""
    print(f"\n🧪 測試常規模擬功能")
    print("=" * 80)
    
    api_url = "http://localhost:5500/simulation/api/simulate_date"
    test_data = {"date": "2025-06-30"}
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print(f"✅ 常規模擬成功")
                print(f"   日期: {data.get('date')}")
                
                sim_result = data.get('simulation_result', {})
                print(f"   狀態: {sim_result.get('status')}")
                print(f"   比賽數: {sim_result.get('games_count')}")
                
                summary = sim_result.get('summary', {})
                if summary:
                    print(f"   預測覆蓋率: {summary.get('prediction_coverage', 0):.1f}%")
                
                return True
            else:
                print(f"❌ 常規模擬失敗: {data.get('error')}")
                return False
        else:
            print(f"❌ API請求失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 常規模擬錯誤: {e}")
        return False

def test_optimized_simulation():
    """測試優化預測功能"""
    print(f"\n🚀 測試優化預測功能")
    print("=" * 80)
    
    api_url = "http://localhost:5500/simulation/api/optimized_simulate"
    test_data = {"date": "2025-07-04"}
    
    try:
        print(f"📡 發送優化預測請求...")
        response = requests.post(
            api_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=60  # 優化預測可能需要更長時間
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print(f"✅ 優化預測成功")
                print(f"   日期: {data.get('date')}")
                print(f"   總比賽: {data.get('total_games')} 場")
                
                training_stats = data.get('training_stats', {})
                if training_stats:
                    print(f"   訓練統計:")
                    print(f"     勝負模型: {training_stats.get('win_training_games')} 場")
                    print(f"     得分模型: {training_stats.get('score_training_games')} 場")
                
                predictions = data.get('predictions', [])
                print(f"   預測結果: {len(predictions)} 場比賽")
                
                # 顯示前3個預測示例
                for i, pred in enumerate(predictions[:3], 1):
                    prediction = pred.get('prediction', {})
                    print(f"     [{i}] {pred.get('teams')}: "
                          f"{prediction.get('away_score'):.1f}-{prediction.get('home_score'):.1f} "
                          f"({prediction.get('predicted_winner')}, {prediction.get('confidence_level')})")
                
                return True
            else:
                print(f"❌ 優化預測失敗: {data.get('error')}")
                return False
        else:
            print(f"❌ 優化預測API失敗: {response.status_code}")
            print(f"   響應內容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 優化預測錯誤: {e}")
        return False

def test_simulation_index():
    """測試模擬首頁功能"""
    print(f"\n📋 測試模擬首頁功能")
    print("=" * 80)
    
    api_url = "http://localhost:5500/simulation/api/simulate_date"
    test_data = {"date": "2025-06-29"}
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            # 檢查數據結構是否符合修復後的格式
            required_fields = ['success', 'date', 'simulation_result']
            missing_fields = []
            
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 缺少必要字段: {missing_fields}")
                return False
            
            sim_result = data['simulation_result']
            if 'summary' in sim_result:
                print(f"✅ 模擬首頁數據結構正確")
                print(f"   包含summary字段: {list(sim_result['summary'].keys())}")
                return True
            else:
                print(f"⚠️  缺少summary字段，但有基本信息")
                print(f"   狀態: {sim_result.get('status')}")
                print(f"   比賽數: {sim_result.get('games_count')}")
                return True
        else:
            print(f"❌ 模擬首頁測試失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 模擬首頁測試錯誤: {e}")
        return False

def generate_test_report():
    """生成測試報告"""
    print(f"\n📊 完整測試報告")
    print("=" * 80)
    
    # 執行所有測試
    tests = [
        ("頁面訪問", test_simulation_page),
        ("常規模擬", test_regular_simulation),
        ("模擬首頁", test_simulation_index),
        ("優化預測", test_optimized_simulation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 執行測試: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 測試異常: {e}")
            results[test_name] = False
    
    # 生成總結
    print(f"\n📋 測試總結:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name:<12}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} 測試通過 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print(f"\n🎉 所有測試通過！模擬系統完全正常！")
        print(f"🌐 訪問地址: http://localhost:5500/simulation/date_simulation")
        print(f"🚀 新功能: 優化預測按鈕已可用")
    else:
        print(f"\n⚠️  部分測試失敗，請檢查相關功能")
    
    return passed == total

def main():
    """主函數"""
    print("🔧 MLB模擬系統完整測試")
    print("=" * 80)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 檢查Flask是否運行
    try:
        response = requests.get("http://localhost:5500", timeout=5)
        print(f"✅ Flask應用正在運行")
    except:
        print(f"❌ Flask應用未運行，請先啟動應用")
        print(f"   命令: python app.py")
        return
    
    # 執行完整測試
    success = generate_test_report()
    
    if success:
        print(f"\n🎯 測試完成！系統狀態良好。")
        print(f"📝 修復總結:")
        print(f"   ✅ JavaScript錯誤已修復")
        print(f"   ✅ 數據結構已統一")
        print(f"   ✅ 優化預測功能已整合")
        print(f"   ✅ 容錯處理已完善")
    else:
        print(f"\n⚠️  測試發現問題，請檢查日誌。")

if __name__ == "__main__":
    main()
