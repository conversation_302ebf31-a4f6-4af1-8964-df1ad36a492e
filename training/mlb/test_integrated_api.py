#!/usr/bin/env python3
"""
測試整合的 API 系統
包括付費 API 和免費 API 的自動切換
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from models.reliable_odds_fetcher import ReliableOddsFetcher
from models.free_api_fetcher import FreeAPIFetcher

def print_separator(title: str):
    """打印分隔線"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_game_summary(games: list, source: str):
    """打印比賽摘要"""
    if not games:
        print(f"❌ {source} 沒有找到比賽數據")
        return
    
    print(f"✅ {source} 找到 {len(games)} 場比賽:")
    print("-" * 40)
    
    for i, game in enumerate(games[:5]):  # 只顯示前5場
        home_team = game.get('home_team', {})
        away_team = game.get('away_team', {})
        
        home_name = home_team.get('abbreviation', home_team.get('name', 'N/A'))
        away_name = away_team.get('abbreviation', away_team.get('name', 'N/A'))
        
        status = game.get('status', 'N/A')
        
        print(f"{i+1:2d}. {away_name} @ {home_name}")
        print(f"     狀態: {status}")
        
        # 顯示比分（如果有）
        home_score = home_team.get('score', '')
        away_score = away_team.get('score', '')
        if home_score and away_score:
            print(f"     比分: {away_score} - {home_score}")
        
        # 顯示賠率（如果有）
        odds = game.get('odds')
        if odds:
            print(f"     賠率: {odds.get('provider', 'N/A')}")
        
        # 顯示投手（如果有）
        pitchers = game.get('probable_pitchers')
        if pitchers:
            home_pitcher = pitchers.get('home', {}).get('name', '')
            away_pitcher = pitchers.get('away', {}).get('name', '')
            if home_pitcher or away_pitcher:
                print(f"     投手: {away_pitcher} vs {home_pitcher}")
        
        print()
    
    if len(games) > 5:
        print(f"... 還有 {len(games) - 5} 場比賽")

def test_free_apis_only():
    """測試純免費 API"""
    print_separator("測試免費 API")
    
    fetcher = FreeAPIFetcher()
    
    # 測試今天的數據
    print("🔍 獲取今天的比賽數據...")
    today_data = fetcher.get_combined_free_data()
    
    if today_data.get('success'):
        print(f"✅ 成功獲取數據")
        print(f"📊 數據源: {', '.join(today_data.get('sources', []))}")
        print_game_summary(today_data.get('games', []), "免費 API")
    else:
        print(f"❌ 獲取失敗: {today_data.get('error', '未知錯誤')}")
    
    # 測試昨天的數據
    yesterday = date.today() - timedelta(days=1)
    print(f"\n🔍 獲取 {yesterday} 的比賽數據...")
    yesterday_data = fetcher.get_combined_free_data(yesterday)
    
    if yesterday_data.get('success'):
        print(f"✅ 成功獲取數據")
        print(f"📊 數據源: {', '.join(yesterday_data.get('sources', []))}")
        print_game_summary(yesterday_data.get('games', []), f"免費 API ({yesterday})")
    else:
        print(f"❌ 獲取失敗: {yesterday_data.get('error', '未知錯誤')}")

def test_integrated_system():
    """測試整合系統"""
    print_separator("測試整合 API 系統")
    
    fetcher = ReliableOddsFetcher()
    
    # 獲取狀態報告
    print("📊 系統狀態報告:")
    status = fetcher.get_status_report()
    
    print(f"時間戳: {status['timestamp']}")
    print(f"付費端點: {status['active_endpoints']}/{status['total_endpoints']} 活躍")
    print(f"可用端點: {status['available_endpoints']}")
    
    print("\n🔗 付費 API 狀態:")
    for endpoint in status['endpoints']:
        status_icon = "✅" if endpoint['is_active'] else "❌"
        rate_icon = "✅" if endpoint['rate_limit_ok'] else "⏰"
        
        print(f"   {status_icon} {endpoint['name']}")
        print(f"      請求: {endpoint['request_count']}/{endpoint['rate_limit']}")
        print(f"      速率: {rate_icon}")
        
        if endpoint['last_error']:
            print(f"      錯誤: {endpoint['last_error'][:50]}...")
    
    print("\n🆓 免費 API 狀態:")
    for api in status.get('free_apis', []):
        status_icon = "✅" if api['status'] == "可用" else "❌"
        print(f"   {status_icon} {api['name']}: {api['status']}")
        
        if 'games_count' in api:
            print(f"      比賽數: {api['games_count']}")
        if 'error' in api and api['error']:
            print(f"      錯誤: {api['error'][:50]}...")
    
    # 測試數據獲取
    print("\n🔍 測試數據獲取...")
    odds_data = fetcher.fetch_mlb_odds()
    
    if odds_data.get('success'):
        print(f"✅ 成功獲取數據")
        
        if odds_data.get('is_free_api'):
            print("📝 注意: 數據來自免費 API，不包含博彩賠率")
            print(f"📊 數據源: {', '.join(odds_data.get('sources', []))}")
        else:
            print(f"📊 數據源: {odds_data.get('source', 'N/A')}")
            print(f"🏢 提供商: {odds_data.get('provider', 'N/A')}")
        
        games = odds_data.get('games', [])
        print_game_summary(games, "整合系統")
        
        # 顯示 API 使用統計
        api_info = odds_data.get('api_info', {})
        if api_info:
            print(f"\n📈 API 使用統計:")
            print(f"   剩餘請求: {api_info.get('requests_remaining', 'N/A')}")
            print(f"   已使用請求: {api_info.get('requests_used', 'N/A')}")
    
    else:
        print(f"❌ 獲取失敗: {odds_data.get('error', '未知錯誤')}")

def test_different_dates():
    """測試不同日期的數據獲取"""
    print_separator("測試不同日期數據獲取")
    
    fetcher = ReliableOddsFetcher()
    
    # 測試過去幾天的數據
    test_dates = [
        date.today(),
        date.today() - timedelta(days=1),
        date.today() - timedelta(days=2),
        date.today() - timedelta(days=3)
    ]
    
    for test_date in test_dates:
        print(f"\n📅 測試日期: {test_date}")
        
        result = fetcher.fetch_mlb_odds(test_date)
        
        if result.get('success'):
            games_count = len(result.get('games', []))
            source_info = ""
            
            if result.get('is_free_api'):
                source_info = f" (免費 API: {', '.join(result.get('sources', []))})"
            else:
                source_info = f" ({result.get('source', 'N/A')})"
            
            print(f"   ✅ 找到 {games_count} 場比賽{source_info}")
        else:
            print(f"   ❌ 失敗: {result.get('error', '未知錯誤')}")

def main():
    """主測試函數"""
    print_separator("整合 API 系統測試")
    
    print("🎯 測試目標:")
    print("1. 驗證免費 API 功能")
    print("2. 測試付費 API 自動切換到免費 API")
    print("3. 檢查不同日期的數據獲取")
    print("4. 驗證系統狀態監控")
    
    # 測試免費 API
    test_free_apis_only()
    
    # 測試整合系統
    test_integrated_system()
    
    # 測試不同日期
    test_different_dates()
    
    print_separator("測試完成")
    print("🎉 整合 API 系統測試完成！")
    
    print("\n💡 系統優勢:")
    print("✅ 付費 API 失敗時自動切換到免費 API")
    print("✅ 多個免費數據源確保數據可用性")
    print("✅ 詳細的狀態監控和錯誤報告")
    print("✅ 支援歷史數據查詢")
    print("✅ 統一的數據格式和接口")
    
    print("\n📝 使用建議:")
    print("- 免費 API 提供基本比賽數據，但不包含博彩賠率")
    print("- 如需博彩賠率，建議設置有效的付費 API 密鑰")
    print("- 系統會自動選擇最佳可用的數據源")
    print("- 可以通過狀態報告監控各 API 的健康狀況")

if __name__ == "__main__":
    main()
