#!/usr/bin/env python3
"""
測試增強的 Box Score 功能
檢查球員統計數據的完整性和顯示
"""

from models.database import Game, Team, PlayerGameStats, Player, db
from app import app
import sys

def test_boxscore_data():
    """測試 Box Score 數據完整性"""
    with app.app_context():
        print("=== Box Score 增強功能測試 ===\n")
        
        # 查找一個有球員統計的已完成比賽
        completed_games = Game.query.filter_by(game_status='completed').limit(5).all()
        
        for game in completed_games:
            print(f"檢查比賽: {game.game_id}")
            print(f"比賽: {game.away_team} @ {game.home_team}")
            print(f"日期: {game.date}")
            print(f"比分: {game.away_score} - {game.home_score}")
            
            # 獲取球隊信息
            home_team = Team.query.filter_by(team_code=game.home_team).first()
            away_team = Team.query.filter_by(team_code=game.away_team).first()
            
            if not home_team or not away_team:
                print("❌ 缺少球隊信息")
                continue
                
            # 檢查球員統計
            home_stats = PlayerGameStats.query.filter_by(
                game_id=game.game_id,
                team_id=home_team.team_id
            ).all()
            
            away_stats = PlayerGameStats.query.filter_by(
                game_id=game.game_id,
                team_id=away_team.team_id
            ).all()
            
            print(f"主隊球員統計: {len(home_stats)} 筆")
            print(f"客隊球員統計: {len(away_stats)} 筆")
            
            # 檢查打擊統計
            home_batters = [s for s in home_stats if s.at_bats > 0 or s.walks > 0]
            away_batters = [s for s in away_stats if s.at_bats > 0 or s.walks > 0]
            
            print(f"主隊打者: {len(home_batters)} 人")
            print(f"客隊打者: {len(away_batters)} 人")
            
            # 檢查投手統計
            home_pitchers = [s for s in home_stats if s.innings_pitched > 0]
            away_pitchers = [s for s in away_stats if s.innings_pitched > 0]
            
            print(f"主隊投手: {len(home_pitchers)} 人")
            print(f"客隊投手: {len(away_pitchers)} 人")
            
            # 檢查守備統計
            home_fielders = [s for s in home_stats if s.putouts > 0 or s.assists > 0 or s.errors > 0]
            away_fielders = [s for s in away_stats if s.putouts > 0 or s.assists > 0 or s.errors > 0]
            
            print(f"主隊守備: {len(home_fielders)} 人")
            print(f"客隊守備: {len(away_fielders)} 人")
            
            # 如果有數據，顯示詳細信息
            if home_batters or away_batters:
                print("\n✅ 此比賽有完整的球員統計數據")
                
                # 顯示一些樣本數據
                if home_batters:
                    batter = home_batters[0]
                    player = Player.query.get(batter.player_id)
                    print(f"樣本打者: {player.full_name if player else 'Unknown'}")
                    print(f"  打數: {batter.at_bats}, 安打: {batter.hits}, 打點: {batter.rbi}")
                    print(f"  位置: {batter.position}, 打序: {batter.batting_order}")
                
                if home_pitchers:
                    pitcher = home_pitchers[0]
                    player = Player.query.get(pitcher.player_id)
                    print(f"樣本投手: {player.full_name if player else 'Unknown'}")
                    print(f"  投球局數: {pitcher.innings_pitched}")
                    print(f"  被安打: {pitcher.hits_allowed}, 三振: {pitcher.strikeouts_pitched}")
                    print(f"  投球數: {pitcher.pitches_thrown}, 好球數: {pitcher.strikes_thrown}")
                
                print(f"\n🌐 測試 URL: http://127.0.0.1:5500/games/{game.game_id}")
                break
            else:
                print("❌ 此比賽沒有球員統計數據")
            
            print("-" * 50)
        
        print("\n=== 功能增強總結 ===")
        print("✅ 新增打擊統計標籤頁:")
        print("   - 打序顯示")
        print("   - 詳細打擊數據 (2B, 3B, HR, BB, SO, SB)")
        print("   - 即時打擊率計算")
        
        print("✅ 增強投手統計標籤頁:")
        print("   - 投球局數")
        print("   - 投球數和好球數")
        print("   - 被全壘打數")
        print("   - 即時 ERA 計算")
        
        print("✅ 新增守備統計標籤頁:")
        print("   - 刺殺、助殺、失誤")
        print("   - 守備率計算")
        
        print("✅ 界面改進:")
        print("   - Bootstrap 5 標籤頁設計")
        print("   - 響應式表格")
        print("   - 數據高亮顯示")
        print("   - 球隊徽章區分")

if __name__ == "__main__":
    test_boxscore_data()
