#!/usr/bin/env python3
"""
訓練改進的MLB預測模型
目標：將準確率從50%提升至65%+
"""

import logging
import sys
from datetime import date, timedelta
from app import create_app
from models.improved_predictor import ImprovedMLBPredictor
from models.prediction_service import PredictionService

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def train_improved_model():
    """訓練改進的預測模型"""
    
    app = create_app()
    with app.app_context():
        try:
            logger.info("🚀 開始訓練改進的MLB預測模型...")
            
            # 創建改進的預測器
            predictor = ImprovedMLBPredictor()
            
            # 準備訓練數據（使用更多歷史數據）
            logger.info("📊 準備增強訓練數據...")
            start_date = date.today() - timedelta(days=365*2)  # 2年數據
            end_date = date.today() - timedelta(days=1)
            
            X, y = predictor.prepare_training_data(start_date, end_date)
            
            logger.info(f"✅ 訓練數據準備完成:")
            logger.info(f"   - 樣本數: {len(X)}")
            logger.info(f"   - 特徵數: {len(X.columns)}")
            logger.info(f"   - 時間範圍: {start_date} 到 {end_date}")
            
            # 顯示新增的增強特徵
            enhanced_features = [col for col in X.columns if any(keyword in col for keyword in [
                'elo', 'fatigue', 'weather', 'injury', 'pitcher', 'travel'
            ])]
            
            if enhanced_features:
                logger.info(f"🔧 新增增強特徵 ({len(enhanced_features)} 個):")
                for feature in enhanced_features[:10]:  # 顯示前10個
                    logger.info(f"   - {feature}")
                if len(enhanced_features) > 10:
                    logger.info(f"   ... 還有 {len(enhanced_features) - 10} 個特徵")
            
            # 訓練集成模型
            logger.info("🤖 開始訓練增強集成模型...")
            performance = predictor.train_ensemble_models(X, y)
            
            logger.info("✅ 模型訓練完成！性能指標:")
            for metric, value in performance.items():
                if 'accuracy' in metric:
                    logger.info(f"   - {metric}: {value:.1%}")
                else:
                    logger.info(f"   - {metric}: {value:.3f}")
            
            # 保存模型
            logger.info("💾 保存增強模型...")
            predictor.save_models('models/enhanced')
            
            # 測試預測功能
            logger.info("🧪 測試增強預測功能...")
            # 重新載入模型以測試
            predictor.load_models('models/enhanced')
            test_prediction = predictor.predict_game('NYY', 'BOS', date.today())
            
            logger.info("✅ 測試預測結果:")
            logger.info(f"   - 比賽: {test_prediction['away_team']} @ {test_prediction['home_team']}")
            logger.info(f"   - 預測比分: {test_prediction['predicted_away_score']:.1f} - {test_prediction['predicted_home_score']:.1f}")
            logger.info(f"   - 主隊勝率: {test_prediction['home_win_probability']:.1%}")
            logger.info(f"   - 信心度: {test_prediction['confidence']:.1%}")
            logger.info(f"   - 使用特徵: {test_prediction['features_used']} 個")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 訓練改進模型失敗: {e}")
            import traceback
            traceback.print_exc()
            return False

def compare_models():
    """比較原始模型和改進模型的性能"""
    
    app = create_app()
    with app.app_context():
        try:
            logger.info("📈 比較模型性能...")
            
            # 獲取當前預測統計
            service = PredictionService()
            current_stats = service.get_prediction_accuracy_stats(days=30)
            
            logger.info("📊 當前模型性能:")
            if 'overall_accuracy' in current_stats:
                logger.info(f"   - 整體準確率: {current_stats['overall_accuracy']:.1f}%")
                logger.info(f"   - 平均得分誤差: {current_stats.get('average_score_error', 0):.2f}")
                logger.info(f"   - 總預測數: {current_stats.get('total_predictions', 0)}")
            else:
                logger.info(f"   - {current_stats.get('message', '無數據')}")
            
            # 載入改進模型進行測試
            logger.info("🔄 載入改進模型進行測試...")
            improved_predictor = ImprovedMLBPredictor()
            
            try:
                improved_predictor.load_models('models/enhanced')
                logger.info("✅ 改進模型載入成功")
                
                # 顯示模型性能
                if hasattr(improved_predictor, 'model_performance'):
                    logger.info("🎯 改進模型訓練性能:")
                    for metric, value in improved_predictor.model_performance.items():
                        if 'accuracy' in metric:
                            logger.info(f"   - {metric}: {value:.1%}")
                        else:
                            logger.info(f"   - {metric}: {value:.3f}")
                
            except Exception as e:
                logger.warning(f"⚠️ 改進模型載入失敗: {e}")
                logger.info("💡 請先運行訓練程序")
            
        except Exception as e:
            logger.error(f"❌ 模型比較失敗: {e}")

def main():
    """主函數"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'train':
            logger.info("🎯 執行模型訓練...")
            success = train_improved_model()
            if success:
                logger.info("🎉 模型訓練成功完成！")
                logger.info("💡 建議接下來:")
                logger.info("   1. 運行 'python train_improved_model.py compare' 比較性能")
                logger.info("   2. 在生產環境中測試新模型")
                logger.info("   3. 監控預測準確率變化")
            else:
                logger.error("❌ 模型訓練失敗")
                sys.exit(1)
                
        elif command == 'compare':
            logger.info("📊 執行模型比較...")
            compare_models()
            
        else:
            logger.error(f"❌ 未知命令: {command}")
            logger.info("💡 可用命令:")
            logger.info("   - train: 訓練改進模型")
            logger.info("   - compare: 比較模型性能")
            sys.exit(1)
    else:
        logger.info("🎯 MLB預測模型改進程序")
        logger.info("💡 使用方法:")
        logger.info("   python train_improved_model.py train    # 訓練改進模型")
        logger.info("   python train_improved_model.py compare  # 比較模型性能")
        
        # 默認執行訓練
        logger.info("\n🚀 開始執行默認訓練...")
        success = train_improved_model()
        if success:
            logger.info("\n📊 執行性能比較...")
            compare_models()

if __name__ == "__main__":
    main()
