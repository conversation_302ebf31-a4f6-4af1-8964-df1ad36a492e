#!/usr/bin/env python3
"""
端口管理工具
檢測端口佔用、清理進程、找到可用端口
"""

import subprocess
import socket
import time
import sys
import os
import signal
from typing import List, Dict, Optional, Tuple

class PortManager:
    """端口管理器"""
    
    def __init__(self):
        self.default_ports = [5500, 5501, 5502, 5503, 5504, 5505]
        self.app_name = "MLB預測系統"
    
    def is_port_in_use(self, port: int) -> bool:
        """檢查端口是否被佔用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result == 0
        except Exception:
            return False
    
    def find_process_using_port(self, port: int) -> Optional[Dict]:
        """找到佔用端口的進程信息"""
        try:
            # macOS/Linux 使用 lsof 命令
            result = subprocess.run(['lsof', '-i', f':{port}'], 
                                 capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:  # 跳過標題行
                    process_line = lines[1].split()
                    if len(process_line) >= 2:
                        return {
                            'command': process_line[0],
                            'pid': int(process_line[1]),
                            'user': process_line[2] if len(process_line) > 2 else 'unknown'
                        }
            return None
        except Exception as e:
            print(f"⚠️ 檢查端口進程失敗: {e}")
            return None
    
    def kill_process_on_port(self, port: int) -> bool:
        """終止佔用端口的進程"""
        process_info = self.find_process_using_port(port)
        if not process_info:
            return False
        
        try:
            pid = process_info['pid']
            command = process_info['command']
            
            print(f"🔍 發現進程: {command} (PID: {pid}) 佔用端口 {port}")
            
            # 檢查是否是我們自己的Flask應用
            if 'python' in command.lower() or 'flask' in command.lower():
                print(f"💀 終止進程 {pid}...")
                
                # 先嘗試優雅關閉 (SIGTERM)
                os.kill(pid, signal.SIGTERM)
                time.sleep(2)
                
                # 檢查進程是否還在運行
                if self.is_process_running(pid):
                    print(f"🔨 強制終止進程 {pid}...")
                    os.kill(pid, signal.SIGKILL)
                    time.sleep(1)
                
                # 驗證端口是否已釋放
                if not self.is_port_in_use(port):
                    print(f"✅ 端口 {port} 已釋放")
                    return True
                else:
                    print(f"❌ 端口 {port} 仍被佔用")
                    return False
            else:
                print(f"⚠️ 端口被其他應用佔用 ({command})，請手動處理")
                return False
                
        except PermissionError:
            print(f"❌ 權限不足，無法終止進程 {pid}")
            return False
        except ProcessLookupError:
            print(f"✅ 進程 {pid} 已不存在")
            return True
        except Exception as e:
            print(f"❌ 終止進程失敗: {e}")
            return False
    
    def is_process_running(self, pid: int) -> bool:
        """檢查進程是否還在運行"""
        try:
            os.kill(pid, 0)  # 發送信號0檢查進程存在性
            return True
        except ProcessLookupError:
            return False
        except PermissionError:
            return True  # 進程存在但無權限訪問
        except Exception:
            return False
    
    def find_available_port(self, start_port: int = 5500, max_attempts: int = 20) -> Optional[int]:
        """找到可用端口"""
        for port in range(start_port, start_port + max_attempts):
            if not self.is_port_in_use(port):
                return port
        return None
    
    def cleanup_ports(self, ports: List[int] = None) -> Dict[int, bool]:
        """清理指定端口"""
        if ports is None:
            ports = self.default_ports
        
        results = {}
        print(f"🧹 開始清理端口: {ports}")
        
        for port in ports:
            print(f"\n🔍 檢查端口 {port}...")
            if self.is_port_in_use(port):
                print(f"⚠️ 端口 {port} 被佔用")
                results[port] = self.kill_process_on_port(port)
            else:
                print(f"✅ 端口 {port} 可用")
                results[port] = True
        
        return results
    
    def get_port_status(self, ports: List[int] = None) -> Dict[int, Dict]:
        """獲取端口狀態信息"""
        if ports is None:
            ports = self.default_ports
        
        status = {}
        for port in ports:
            port_info = {
                'in_use': self.is_port_in_use(port),
                'process': None
            }
            
            if port_info['in_use']:
                port_info['process'] = self.find_process_using_port(port)
            
            status[port] = port_info
        
        return status
    
    def display_port_status(self, ports: List[int] = None):
        """顯示端口狀態"""
        if ports is None:
            ports = self.default_ports
        
        print("="*60)
        print(f"🔌 {self.app_name} - 端口狀態檢查")
        print("="*60)
        
        status = self.get_port_status(ports)
        
        available_ports = []
        occupied_ports = []
        
        for port, info in status.items():
            if info['in_use']:
                occupied_ports.append(port)
                process_info = info['process']
                if process_info:
                    print(f"❌ 端口 {port}: 被佔用 - {process_info['command']} (PID: {process_info['pid']})")
                else:
                    print(f"❌ 端口 {port}: 被佔用 - 無法獲取進程信息")
            else:
                available_ports.append(port)
                print(f"✅ 端口 {port}: 可用")
        
        print("\n" + "="*60)
        print(f"📊 統計: {len(available_ports)} 個可用, {len(occupied_ports)} 個被佔用")
        
        if available_ports:
            print(f"🟢 可用端口: {', '.join(map(str, available_ports))}")
        
        if occupied_ports:
            print(f"🔴 被佔用端口: {', '.join(map(str, occupied_ports))}")
        
        return available_ports, occupied_ports
    
    def recommend_action(self) -> Tuple[Optional[int], str]:
        """推薦操作"""
        available_ports, occupied_ports = self.display_port_status()
        
        if available_ports:
            recommended_port = min(available_ports)
            action = f"使用端口 {recommended_port} 啟動服務"
            return recommended_port, action
        else:
            action = "所有預設端口都被佔用，建議執行端口清理"
            return None, action

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description="MLB系統端口管理工具")
    parser.add_argument('--check', '-c', action='store_true', help='檢查端口狀態')
    parser.add_argument('--cleanup', '-k', action='store_true', help='清理被佔用的端口')
    parser.add_argument('--port', '-p', type=int, help='指定單個端口進行操作')
    parser.add_argument('--find', '-f', action='store_true', help='尋找可用端口')
    parser.add_argument('--start-port', type=int, default=5500, help='開始搜尋的端口號')
    
    args = parser.parse_args()
    
    manager = PortManager()
    
    if args.port:
        # 單個端口操作
        ports = [args.port]
    else:
        # 預設端口
        ports = manager.default_ports
    
    if args.cleanup:
        print("🧹 執行端口清理...")
        results = manager.cleanup_ports(ports)
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        print(f"\n📊 清理結果: {success_count}/{total_count} 個端口已清理")
        
    elif args.find:
        print("🔍 尋找可用端口...")
        available_port = manager.find_available_port(args.start_port)
        if available_port:
            print(f"✅ 找到可用端口: {available_port}")
        else:
            print("❌ 沒有找到可用端口")
    
    elif args.check or len(sys.argv) == 1:
        # 默認顯示狀態和建議
        recommended_port, action = manager.recommend_action()
        
        print(f"\n💡 建議操作: {action}")
        
        if recommended_port:
            print(f"\n🚀 啟動命令:")
            print(f"   export FLASK_PORT={recommended_port}")
            print(f"   ./start.sh")

if __name__ == "__main__":
    main()