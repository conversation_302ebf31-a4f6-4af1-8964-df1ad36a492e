#!/usr/bin/env python3
"""
添加is_real和data_source字段到BettingOdds表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db

def add_is_real_field():
    """添加is_real和data_source字段"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔧 添加is_real和data_source字段到BettingOdds表...")
            
            # 添加字段
            with db.engine.connect() as conn:
                conn.execute(db.text("""
                    ALTER TABLE betting_odds
                    ADD COLUMN is_real BOOLEAN DEFAULT TRUE
                """))

                conn.execute(db.text("""
                    ALTER TABLE betting_odds
                    ADD COLUMN data_source VARCHAR(50) DEFAULT 'covers.com'
                """))

                conn.commit()

            print("✅ 字段添加成功")

            # 更新現有記錄
            print("🔄 更新現有記錄...")

            # 將所有現有記錄標記為真實數據（來自covers.com）
            with db.engine.connect() as conn:
                result = conn.execute(db.text("""
                    UPDATE betting_odds
                    SET is_real = TRUE, data_source = 'covers.com'
                    WHERE is_real IS NULL OR data_source IS NULL
                """))

                print(f"✅ 更新了 {result.rowcount} 筆記錄")

                # 驗證更新
                count_result = conn.execute(db.text("""
                    SELECT COUNT(*) as total,
                           SUM(CASE WHEN is_real = TRUE THEN 1 ELSE 0 END) as real_count,
                           COUNT(DISTINCT data_source) as source_count
                    FROM betting_odds
                """)).fetchone()

                conn.commit()
            
            print(f"📊 驗證結果:")
            print(f"  總記錄數: {count_result[0]}")
            print(f"  真實數據: {count_result[1]}")
            print(f"  數據源數量: {count_result[2]}")

            print("✅ 數據庫更新完成")
            
        except Exception as e:
            print(f"❌ 更新失敗: {e}")
            db.session.rollback()

if __name__ == "__main__":
    add_is_real_field()
