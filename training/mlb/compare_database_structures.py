#!/usr/bin/env python3
"""
比對兩個MLB資料庫的結構差異
檢查 instance/mlb_data.db 和 data/mlb_data.db 的表結構和數據差異
"""

import sqlite3
import os
from collections import defaultdict

def get_database_structure(db_path):
    """獲取資料庫的完整結構信息"""
    
    if not os.path.exists(db_path):
        return None
    
    # 檢查文件大小，如果是0則返回空結構
    file_size = os.path.getsize(db_path)
    if file_size == 0:
        return {
            'tables': {},
            'indexes': [],
            'file_size': 0
        }
    
    structure = {
        'tables': {},
        'indexes': [],
        'file_size': os.path.getsize(db_path)
    }
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 獲取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        for (table_name,) in tables:
            # 獲取表結構
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            # 獲取記錄數
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            
            structure['tables'][table_name] = {
                'columns': columns,
                'row_count': row_count
            }
        
        # 獲取索引信息
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
        indexes = cursor.fetchall()
        structure['indexes'] = indexes
        
        conn.close()
        return structure
        
    except Exception as e:
        print(f"   ❌ 讀取 {db_path} 失敗: {e}")
        return None

def compare_database_structures():
    """比對兩個資料庫的結構"""
    
    print("🔍 MLB 資料庫結構比對")
    print("=" * 70)
    
    db1_path = 'instance/mlb_data.db'
    db2_path = 'data/mlb_data.db'
    
    print(f"📊 比對對象:")
    print(f"   資料庫A: {db1_path}")
    print(f"   資料庫B: {db2_path}")
    print()
    
    # 獲取兩個資料庫的結構
    print("📋 正在分析資料庫結構...")
    structure1 = get_database_structure(db1_path)
    structure2 = get_database_structure(db2_path)
    
    if structure1 is None:
        print(f"❌ 無法讀取資料庫A: {db1_path}")
        return False
    
    if structure2 is None:
        print(f"❌ 無法讀取資料庫B: {db2_path}")
        return False
    
    # 1. 文件大小比較
    print(f"\n📁 文件大小比較:")
    size1 = structure1['file_size']
    size2 = structure2['file_size']
    print(f"   資料庫A: {size1:,} bytes ({size1/1024/1024:.1f} MB)")
    print(f"   資料庫B: {size2:,} bytes ({size2/1024/1024:.1f} MB)")
    print(f"   差異: {abs(size1-size2):,} bytes")
    
    # 2. 表數量比較
    print(f"\n📋 表數量比較:")
    tables1 = set(structure1['tables'].keys())
    tables2 = set(structure2['tables'].keys())
    
    print(f"   資料庫A: {len(tables1)} 個表")
    print(f"   資料庫B: {len(tables2)} 個表")
    
    # 3. 表差異分析
    print(f"\n🔍 表差異分析:")
    
    common_tables = tables1 & tables2
    only_in_1 = tables1 - tables2
    only_in_2 = tables2 - tables1
    
    print(f"   🤝 共同表: {len(common_tables)} 個")
    if common_tables:
        for table in sorted(common_tables)[:10]:  # 顯示前10個
            count1 = structure1['tables'][table]['row_count']
            count2 = structure2['tables'][table]['row_count']
            print(f"      📊 {table}: A={count1:,}, B={count2:,}")
        if len(common_tables) > 10:
            print(f"      ... 還有 {len(common_tables)-10} 個共同表")
    
    if only_in_1:
        print(f"   🅰️ 僅在資料庫A: {len(only_in_1)} 個")
        for table in sorted(only_in_1):
            count = structure1['tables'][table]['row_count']
            print(f"      📋 {table}: {count:,} 條記錄")
    
    if only_in_2:
        print(f"   🅱️ 僅在資料庫B: {len(only_in_2)} 個")
        for table in sorted(only_in_2):
            count = structure2['tables'][table]['row_count']
            print(f"      📋 {table}: {count:,} 條記錄")
    
    # 4. 重要表的詳細比較
    important_tables = ['games', 'box_scores', 'betting_odds', 'predictions']
    
    print(f"\n🎯 重要表詳細比較:")
    
    for table in important_tables:
        if table in common_tables:
            print(f"\n   📊 {table} 表比較:")
            
            cols1 = structure1['tables'][table]['columns']
            cols2 = structure2['tables'][table]['columns']
            count1 = structure1['tables'][table]['row_count']
            count2 = structure2['tables'][table]['row_count']
            
            print(f"      記錄數: A={count1:,}, B={count2:,}")
            
            # 比較欄位
            col_names1 = set(col[1] for col in cols1)
            col_names2 = set(col[2] for col in cols2)
            
            common_cols = col_names1 & col_names2
            only_in_a = col_names1 - col_names2
            only_in_b = col_names2 - col_names1
            
            print(f"      欄位數: A={len(col_names1)}, B={len(col_names2)}")
            
            if only_in_a:
                print(f"      🅰️ A獨有欄位: {sorted(only_in_a)}")
            if only_in_b:
                print(f"      🅱️ B獨有欄位: {sorted(only_in_b)}")
            if not only_in_a and not only_in_b:
                print(f"      ✅ 欄位結構相同")
        
        elif table in tables1:
            count1 = structure1['tables'][table]['row_count']
            print(f"\n   🅰️ {table} 僅存在於資料庫A: {count1:,} 條記錄")
        elif table in tables2:
            count2 = structure2['tables'][table]['row_count']
            print(f"\n   🅱️ {table} 僅存在於資料庫B: {count2:,} 條記錄")
        else:
            print(f"\n   ❌ {table} 在兩個資料庫中都不存在")
    
    # 5. 索引比較
    print(f"\n🔗 索引比較:")
    indexes1 = set(idx[0] for idx in structure1['indexes'])
    indexes2 = set(idx[0] for idx in structure2['indexes'])
    
    print(f"   資料庫A索引: {len(indexes1)} 個")
    print(f"   資料庫B索引: {len(indexes2)} 個")
    
    common_indexes = indexes1 & indexes2
    if common_indexes:
        print(f"   🤝 共同索引: {len(common_indexes)} 個")
    
    only_idx_1 = indexes1 - indexes2
    only_idx_2 = indexes2 - indexes1
    
    if only_idx_1:
        print(f"   🅰️ A獨有索引: {sorted(only_idx_1)}")
    if only_idx_2:
        print(f"   🅱️ B獨有索引: {sorted(only_idx_2)}")
    
    # 6. 總結
    print(f"\n🎯 比對總結:")
    
    if size1 > size2 * 2:
        print(f"   📈 資料庫A明顯更大，可能包含更多數據")
    elif size2 > size1 * 2:
        print(f"   📈 資料庫B明顯更大，可能包含更多數據")
    else:
        print(f"   ⚖️ 兩個資料庫大小相近")
    
    if only_in_1 or only_in_2:
        print(f"   ⚠️ 發現表結構差異，可能需要同步")
    else:
        print(f"   ✅ 表結構基本一致")
    
    # 建議動作
    print(f"\n💡 建議動作:")
    
    if size1 > size2 and len(only_in_1) > 0:
        print(f"   🔄 建議將資料庫A的數據同步到資料庫B")
    elif size2 > size1 and len(only_in_2) > 0:
        print(f"   🔄 建議將資料庫B的數據同步到資料庫A")
    
    if structure1['tables'].get('games', {}).get('row_count', 0) > structure2['tables'].get('games', {}).get('row_count', 0):
        print(f"   🎯 資料庫A的比賽數據更完整，建議作為主要資料庫")
    elif structure2['tables'].get('games', {}).get('row_count', 0) > structure1['tables'].get('games', {}).get('row_count', 0):
        print(f"   🎯 資料庫B的比賽數據更完整，建議作為主要資料庫")
    
    return True

if __name__ == "__main__":
    print("🔍 MLB 資料庫結構比對工具")
    print("比較 instance/mlb_data.db 和 data/mlb_data.db 的差異")
    print("")
    
    success = compare_database_structures()
    
    if success:
        print(f"\n✅ 比對完成")
        print(f"💡 請根據比對結果決定是否需要進行資料庫同步")
    else:
        print(f"\n❌ 比對失敗")