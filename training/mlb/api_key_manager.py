#!/usr/bin/env python3
"""
API 密鑰管理工具
用於驗證、測試和管理多個 API 密鑰
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import json

class APIKeyManager:
    """API 密鑰管理器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 10
    
    def verify_odds_api_key(self, api_key: str) -> Tuple[bool, str, Dict]:
        """驗證 The Odds API 密鑰"""
        try:
            url = "https://api.the-odds-api.com/v4/sports"
            params = {"apiKey": api_key}
            
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                remaining = response.headers.get('x-requests-remaining', 'N/A')
                used = response.headers.get('x-requests-used', 'N/A')
                
                return True, "API 密鑰有效", {
                    "requests_remaining": remaining,
                    "requests_used": used,
                    "sports_count": len(data) if isinstance(data, list) else 0
                }
            elif response.status_code == 401:
                return False, "API 密鑰無效或已過期", {}
            elif response.status_code == 429:
                return False, "API 請求限制已達到", {}
            else:
                return False, f"HTTP {response.status_code}: {response.text}", {}
                
        except Exception as e:
            return False, f"驗證失敗: {e}", {}
    
    def verify_rapidapi_key(self, api_key: str) -> Tuple[bool, str, Dict]:
        """驗證 RapidAPI 密鑰"""
        try:
            # 使用一個簡單的 RapidAPI 端點進行測試
            url = "https://major-league-baseball-mlb.p.rapidapi.com/sports"
            headers = {
                "X-RapidAPI-Key": api_key,
                "X-RapidAPI-Host": "major-league-baseball-mlb.p.rapidapi.com"
            }
            
            response = self.session.get(url, headers=headers)
            
            if response.status_code == 200:
                return True, "RapidAPI 密鑰有效", {"status": "active"}
            elif response.status_code == 401:
                return False, "RapidAPI 密鑰無效", {}
            elif response.status_code == 403:
                return False, "RapidAPI 密鑰無權限訪問此 API", {}
            elif response.status_code == 429:
                return False, "RapidAPI 請求限制已達到", {}
            else:
                return False, f"HTTP {response.status_code}: {response.text}", {}
                
        except Exception as e:
            return False, f"驗證失敗: {e}", {}
    
    def test_free_apis(self) -> List[Dict]:
        """測試免費的體育數據 API"""
        free_apis = []
        
        # 1. ESPN API (免費)
        try:
            url = "https://site.api.espn.com/apis/site/v2/sports/baseball/mlb/scoreboard"
            response = self.session.get(url)
            if response.status_code == 200:
                data = response.json()
                free_apis.append({
                    "name": "ESPN API",
                    "url": url,
                    "status": "可用",
                    "description": "免費的 MLB 比分和基本數據",
                    "games_count": len(data.get('events', [])) if 'events' in data else 0
                })
        except Exception as e:
            free_apis.append({
                "name": "ESPN API",
                "status": "不可用",
                "error": str(e)
            })
        
        # 2. MLB Stats API (免費)
        try:
            url = "https://statsapi.mlb.com/api/v1/schedule"
            response = self.session.get(url)
            if response.status_code == 200:
                data = response.json()
                free_apis.append({
                    "name": "MLB Stats API",
                    "url": url,
                    "status": "可用",
                    "description": "MLB 官方統計 API，免費",
                    "games_count": len(data.get('dates', [])) if 'dates' in data else 0
                })
        except Exception as e:
            free_apis.append({
                "name": "MLB Stats API",
                "status": "不可用",
                "error": str(e)
            })
        
        return free_apis
    
    def get_api_recommendations(self) -> Dict[str, List[Dict]]:
        """獲取 API 推薦"""
        return {
            "betting_odds": [
                {
                    "name": "The Odds API",
                    "url": "https://the-odds-api.com/",
                    "free_tier": "每月 500 請求",
                    "paid_plans": "從 $30/月 開始",
                    "features": ["實時賠率", "多個博彩公司", "歷史數據"],
                    "pros": ["數據質量高", "更新及時", "支援多種運動"],
                    "cons": ["免費額度有限"]
                },
                {
                    "name": "RapidAPI Sports APIs",
                    "url": "https://rapidapi.com/search/sports",
                    "free_tier": "各 API 不同",
                    "paid_plans": "各 API 不同",
                    "features": ["多種體育數據", "統一接口"],
                    "pros": ["選擇多樣", "統一管理"],
                    "cons": ["質量參差不齊"]
                }
            ],
            "game_data": [
                {
                    "name": "ESPN API",
                    "url": "https://site.api.espn.com/",
                    "free_tier": "完全免費",
                    "features": ["比分", "賽程", "基本統計"],
                    "pros": ["完全免費", "數據可靠"],
                    "cons": ["無博彩賠率", "功能有限"]
                },
                {
                    "name": "MLB Stats API",
                    "url": "https://statsapi.mlb.com/",
                    "free_tier": "完全免費",
                    "features": ["官方統計", "詳細數據", "歷史記錄"],
                    "pros": ["官方數據", "完全免費", "數據詳細"],
                    "cons": ["無博彩賠率"]
                }
            ]
        }
    
    def save_api_key(self, api_type: str, api_key: str) -> bool:
        """保存 API 密鑰"""
        try:
            if api_type.lower() == "odds":
                file_path = "models/odds-api.txt"
            elif api_type.lower() == "rapidapi":
                file_path = "models/rapidapi-key.txt"
            else:
                print(f"❌ 不支援的 API 類型: {api_type}")
                return False
            
            # 確保目錄存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w') as f:
                f.write(api_key.strip())
            
            print(f"✅ API 密鑰已保存到: {file_path}")
            return True
            
        except Exception as e:
            print(f"❌ 保存 API 密鑰失敗: {e}")
            return False
    
    def load_and_verify_all_keys(self) -> Dict[str, Dict]:
        """載入並驗證所有 API 密鑰"""
        results = {}
        
        # 驗證 The Odds API
        try:
            with open('models/odds-api.txt', 'r') as f:
                odds_key = f.read().strip()
                if odds_key and not odds_key.startswith('#'):
                    is_valid, message, info = self.verify_odds_api_key(odds_key)
                    results['odds_api'] = {
                        "key": odds_key[:10] + "..." if len(odds_key) > 10 else odds_key,
                        "valid": is_valid,
                        "message": message,
                        "info": info
                    }
                else:
                    results['odds_api'] = {
                        "key": "未設置",
                        "valid": False,
                        "message": "API 密鑰文件為空或包含註釋",
                        "info": {}
                    }
        except FileNotFoundError:
            results['odds_api'] = {
                "key": "文件不存在",
                "valid": False,
                "message": "API 密鑰文件不存在",
                "info": {}
            }
        
        # 驗證 RapidAPI
        try:
            with open('models/rapidapi-key.txt', 'r') as f:
                rapid_key = f.read().strip()
                if rapid_key and not rapid_key.startswith('#'):
                    is_valid, message, info = self.verify_rapidapi_key(rapid_key)
                    results['rapidapi'] = {
                        "key": rapid_key[:10] + "..." if len(rapid_key) > 10 else rapid_key,
                        "valid": is_valid,
                        "message": message,
                        "info": info
                    }
                else:
                    results['rapidapi'] = {
                        "key": "未設置",
                        "valid": False,
                        "message": "API 密鑰文件為空或包含註釋",
                        "info": {}
                    }
        except FileNotFoundError:
            results['rapidapi'] = {
                "key": "文件不存在",
                "valid": False,
                "message": "API 密鑰文件不存在",
                "info": {}
            }
        
        return results

def print_separator(title: str):
    """打印分隔線"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_verification_results(results: Dict[str, Dict]):
    """打印驗證結果"""
    print("\n🔑 API 密鑰驗證結果:")
    print("-" * 40)
    
    for api_name, result in results.items():
        status_icon = "✅" if result['valid'] else "❌"
        print(f"\n{status_icon} {api_name.upper()}:")
        print(f"   密鑰: {result['key']}")
        print(f"   狀態: {result['message']}")
        
        if result['info']:
            print("   詳情:")
            for key, value in result['info'].items():
                print(f"      {key}: {value}")

def print_free_apis(free_apis: List[Dict]):
    """打印免費 API 測試結果"""
    print("\n🆓 免費 API 測試結果:")
    print("-" * 40)
    
    for api in free_apis:
        status_icon = "✅" if api['status'] == "可用" else "❌"
        print(f"\n{status_icon} {api['name']}:")
        print(f"   狀態: {api['status']}")
        
        if 'description' in api:
            print(f"   描述: {api['description']}")
        if 'games_count' in api:
            print(f"   比賽數: {api['games_count']}")
        if 'error' in api:
            print(f"   錯誤: {api['error']}")

def print_recommendations(recommendations: Dict[str, List[Dict]]):
    """打印 API 推薦"""
    print("\n💡 API 推薦:")
    print("-" * 40)
    
    for category, apis in recommendations.items():
        print(f"\n📊 {category.upper()}:")
        
        for api in apis:
            print(f"\n   🔗 {api['name']}:")
            print(f"      網址: {api['url']}")
            print(f"      免費額度: {api['free_tier']}")
            
            if 'paid_plans' in api:
                print(f"      付費方案: {api['paid_plans']}")
            
            print(f"      功能: {', '.join(api['features'])}")
            print(f"      優點: {', '.join(api['pros'])}")
            print(f"      缺點: {', '.join(api['cons'])}")

def interactive_key_setup():
    """互動式 API 密鑰設置"""
    print_separator("互動式 API 密鑰設置")
    
    manager = APIKeyManager()
    
    print("請選擇要設置的 API 密鑰:")
    print("1. The Odds API")
    print("2. RapidAPI")
    print("3. 跳過設置")
    
    choice = input("\n請輸入選擇 (1-3): ").strip()
    
    if choice == "1":
        print("\n🔑 設置 The Odds API 密鑰:")
        print("1. 訪問 https://the-odds-api.com/")
        print("2. 註冊免費帳戶")
        print("3. 獲取 API 密鑰")
        
        api_key = input("\n請輸入您的 The Odds API 密鑰: ").strip()
        if api_key:
            print("\n🔍 驗證 API 密鑰...")
            is_valid, message, info = manager.verify_odds_api_key(api_key)
            
            if is_valid:
                manager.save_api_key("odds", api_key)
                print(f"✅ {message}")
                if info:
                    print(f"   剩餘請求: {info.get('requests_remaining', 'N/A')}")
            else:
                print(f"❌ {message}")
    
    elif choice == "2":
        print("\n🔑 設置 RapidAPI 密鑰:")
        print("1. 訪問 https://rapidapi.com/")
        print("2. 註冊帳戶")
        print("3. 搜索 MLB 或 Baseball API")
        print("4. 訂閱免費方案")
        print("5. 獲取 API 密鑰")
        
        api_key = input("\n請輸入您的 RapidAPI 密鑰: ").strip()
        if api_key:
            print("\n🔍 驗證 API 密鑰...")
            is_valid, message, info = manager.verify_rapidapi_key(api_key)
            
            if is_valid:
                manager.save_api_key("rapidapi", api_key)
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
    
    print("\n✅ 設置完成！")

def main():
    """主函數"""
    print_separator("API 密鑰管理工具")
    
    manager = APIKeyManager()
    
    # 1. 驗證現有 API 密鑰
    print("🔍 正在驗證現有 API 密鑰...")
    verification_results = manager.load_and_verify_all_keys()
    print_verification_results(verification_results)
    
    # 2. 測試免費 API
    print("\n🔍 正在測試免費 API...")
    free_apis = manager.test_free_apis()
    print_free_apis(free_apis)
    
    # 3. 顯示推薦
    recommendations = manager.get_api_recommendations()
    print_recommendations(recommendations)
    
    # 4. 互動式設置
    setup_choice = input("\n❓ 是否要設置新的 API 密鑰？(y/n): ").strip().lower()
    if setup_choice == 'y':
        interactive_key_setup()
    
    print_separator("管理完成")
    print("🎉 API 密鑰管理完成！")
    
    # 檢查是否有可用的 API
    valid_apis = [name for name, result in verification_results.items() if result['valid']]
    available_free_apis = [api for api in free_apis if api['status'] == "可用"]
    
    if valid_apis:
        print(f"\n✅ 可用的付費 API: {', '.join(valid_apis)}")
    if available_free_apis:
        print(f"✅ 可用的免費 API: {', '.join([api['name'] for api in available_free_apis])}")
    
    if not valid_apis and not available_free_apis:
        print("\n⚠️  目前沒有可用的 API，建議:")
        print("1. 設置有效的 API 密鑰")
        print("2. 使用免費的 ESPN 或 MLB Stats API 獲取基本數據")

if __name__ == "__main__":
    main()
