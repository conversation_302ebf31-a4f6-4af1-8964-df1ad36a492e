#!/usr/bin/env python3
"""
修復特定的異常記錄
"""

import sqlite3
import os
from datetime import datetime

def fix_specific_record():
    """修復NYM@BAL的異常記錄"""
    print("🔧 修復特定異常記錄: NYM@BAL (2025-07-08)")
    print("=" * 50)
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查找NYM@BAL的記錄
        cursor.execute("""
            SELECT 
                p.game_id,
                p.predicted_total_runs,
                p.over_under_line,
                p.predicted_home_score,
                p.predicted_away_score
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date = '2025-07-08'
            AND g.away_team = 'NYM' AND g.home_team = 'BAL'
        """)
        
        result = cursor.fetchone()
        
        if not result:
            print("❌ 沒有找到NYM@BAL的記錄")
            conn.close()
            return
        
        game_id, total_runs, current_line, home_score, away_score = result
        
        print(f"找到記錄: game_id={game_id}")
        print(f"修復前: {away_score}-{home_score} (總分: {total_runs}, 盤口: {current_line})")
        
        # 計算新的合理預測
        estimated_line = 8.0  # BAL主場，合理盤口
        target_total = estimated_line * 1.08  # 略高於盤口
        
        # 重新分配得分
        if total_runs and total_runs > 0:
            home_ratio = home_score / total_runs if home_score else 0.52
            away_ratio = away_score / total_runs if away_score else 0.48
        else:
            home_ratio = 0.52  # 主場稍微優勢
            away_ratio = 0.48
        
        new_home_score = max(2.0, min(8.0, target_total * home_ratio))
        new_away_score = max(2.0, min(8.0, target_total * away_ratio))
        new_total = new_home_score + new_away_score
        
        print(f"修復後: {new_away_score:.1f}-{new_home_score:.1f} (總分: {new_total:.1f}, 盤口: {estimated_line})")
        
        # 更新記錄
        cursor.execute("""
            UPDATE predictions 
            SET predicted_home_score = ?,
                predicted_away_score = ?,
                predicted_total_runs = ?,
                over_under_line = ?,
                over_probability = CASE WHEN ? > ? THEN 0.6 ELSE 0.4 END,
                under_probability = CASE WHEN ? > ? THEN 0.4 ELSE 0.6 END,
                over_under_confidence = 0.7,
                updated_at = ?
            WHERE game_id = ?
        """, (
            round(new_home_score, 1),
            round(new_away_score, 1),
            round(new_total, 1),
            estimated_line,
            new_total, estimated_line,
            new_total, estimated_line,
            datetime.now().isoformat(),
            game_id
        ))
        
        # 添加博彩盤口
        cursor.execute("""
            INSERT OR REPLACE INTO betting_odds 
            (game_id, bookmaker, market_type, total_point, odds_time, created_at, updated_at)
            VALUES (?, 'estimated', 'totals', ?, ?, ?, ?)
        """, (
            game_id,
            estimated_line,
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        
        conn.commit()
        
        # 驗證更新
        cursor.execute("""
            SELECT 
                p.predicted_total_runs,
                p.over_under_line,
                p.predicted_home_score,
                p.predicted_away_score,
                p.updated_at
            FROM predictions p
            WHERE p.game_id = ?
        """, (game_id,))
        
        updated_result = cursor.fetchone()
        if updated_result:
            total, line, home, away, updated_at = updated_result
            print(f"✅ 更新成功: {away}-{home} (總分: {total}, 盤口: {line})")
            print(f"更新時間: {updated_at}")
        
        conn.close()
        
        print("🔄 請刷新網頁查看更新")
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")

def check_all_remaining_issues():
    """檢查所有剩餘的異常記錄"""
    print("\n🔍 檢查所有剩餘異常記錄...")
    
    basedir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(basedir, "instance", "mlb_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查找所有可能的異常記錄
        cursor.execute("""
            SELECT 
                g.date,
                g.away_team || '@' || g.home_team as matchup,
                p.predicted_total_runs,
                p.over_under_line,
                p.game_id
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date >= '2025-07-01'
            AND (p.predicted_total_runs > 12 
                 OR p.predicted_total_runs < 4
                 OR p.over_under_line = 0 
                 OR p.over_under_line IS NULL
                 OR p.predicted_total_runs IS NULL)
            ORDER BY g.date, p.predicted_total_runs DESC
        """)
        
        issues = cursor.fetchall()
        
        if issues:
            print(f"發現 {len(issues)} 個潛在問題:")
            for date, matchup, total, line, game_id in issues:
                print(f"  {date} {matchup}: 總分={total}, 盤口={line} (ID: {game_id})")
        else:
            print("✅ 沒有發現異常記錄")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

if __name__ == '__main__':
    fix_specific_record()
    check_all_remaining_issues()
