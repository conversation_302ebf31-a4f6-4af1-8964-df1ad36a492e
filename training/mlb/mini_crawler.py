#!/usr/bin/env python3
"""
迷你抓取器 - 每次處理少量日期，確保完成並檢查結果
"""

import logging
from datetime import date
from integrated_odds_system import IntegratedOddsSystem

def crawl_recent_days():
    """抓取最近幾天的數據並檢查數據庫狀態"""
    logging.basicConfig(level=logging.INFO)
    
    from app import create_app
    app = create_app()
    
    with app.app_context():
        system = IntegratedOddsSystem()
        
        # 測試幾個關鍵日期
        test_dates = [
            date(2025, 6, 1),   # 6月第一天
            date(2025, 6, 15),  # 6月中旬
            date(2025, 6, 30),  # 6月最後一天
            date(2025, 8, 1),   # 8月第一天
            date(2025, 8, 25)   # 8月最後一天
        ]
        
        total_saved = 0
        
        for target_date in test_dates:
            print(f"\n📅 處理 {target_date}")
            
            try:
                result = system.fetch_and_save_odds(target_date, 'both')
                
                if result.get('success'):
                    saved = result.get('total_saved', 0)
                    total_saved += saved
                    print(f"✅ {target_date}: 成功保存 {saved} 條記錄")
                    print(f"   - 處理比賽: {result.get('games_processed', 0)}")
                    print(f"   - 傳統格式: {result.get('legacy_saved', 0)}")
                    print(f"   - 增強格式: {result.get('enhanced_saved', 0)}")
                else:
                    print(f"❌ {target_date}: {result.get('message', '失敗')}")
                    
            except Exception as e:
                print(f"❌ {target_date} 異常: {e}")
        
        # 檢查數據庫狀態
        from models.database import BettingOdds, db
        from sqlalchemy import func
        
        # 讓分盤統計
        spreads_count = db.session.query(func.count(BettingOdds.id)).filter(
            (BettingOdds.market_type == 'spreads') | 
            ((BettingOdds.market_type == 'both') & 
             ((BettingOdds.home_spread_point.isnot(None)) | (BettingOdds.away_spread_point.isnot(None))))
        ).scalar()
        
        # 大小分統計
        totals_count = db.session.query(func.count(BettingOdds.id)).filter(
            (BettingOdds.market_type == 'totals') | 
            ((BettingOdds.market_type == 'both') & (BettingOdds.total_point.isnot(None)))
        ).scalar()
        
        # 總記錄數
        total_records = BettingOdds.query.count()
        
        print(f"\n📊 數據庫狀態:")
        print(f"   總記錄: {total_records}")
        print(f"   讓分盤: {spreads_count}")
        print(f"   大小分: {totals_count}")
        print(f"   本次新增: {total_saved}")
        
        # 檢查最新記錄
        latest = BettingOdds.query.order_by(BettingOdds.created_at.desc()).first()
        if latest:
            print(f"   最新記錄: {latest.created_at} ({latest.bookmaker}, {latest.market_type})")
        
        return {
            'total_records': total_records,
            'spreads_count': spreads_count,
            'totals_count': totals_count,
            'newly_saved': total_saved
        }

if __name__ == "__main__":
    print("🔥 迷你抓取器 - 測試關鍵日期")
    crawl_recent_days()