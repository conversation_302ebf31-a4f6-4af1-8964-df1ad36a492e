#!/usr/bin/env python3
"""
綜合修復預測系統 - 解決球隊得分能力差異問題
"""

import sys
import os
from datetime import date, timedelta
from collections import defaultdict

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, TeamStats, Team, Prediction
from models.ml_predictor import MLBPredictor

def comprehensive_fix():
    """綜合修復預測系統"""
    print("🔧 綜合修復預測系統")
    print("=" * 60)
    
    app = create_app()
    with app.app_context():
        
        # 步驟1: 檢查和修復TeamStats數據
        print("📊 步驟1: 檢查TeamStats數據")
        print("-" * 30)
        
        team_count = Team.query.count()
        stats_count = TeamStats.query.count()
        print(f"球隊數量: {team_count}")
        print(f"統計數量: {stats_count}")
        
        if stats_count == 0:
            print("🔄 重新生成TeamStats數據...")
            generate_team_stats()
        else:
            print("✅ TeamStats數據存在，檢查質量...")
            check_team_stats_quality()
        
        # 步驟2: 測試特徵提取
        print("\n🔍 步驟2: 測試特徵提取")
        print("-" * 30)
        
        test_feature_extraction()
        
        # 步驟3: 測試預測差異
        print("\n🎯 步驟3: 測試預測差異")
        print("-" * 30)
        
        test_prediction_differences()
        
        # 步驟4: 重新生成今日預測（如果需要）
        print("\n🔄 步驟4: 重新生成預測")
        print("-" * 30)
        
        regenerate_predictions_if_needed()

def generate_team_stats():
    """生成球隊統計數據"""
    print("📊 生成球隊統計數據...")
    
    # 獲取最近90天的比賽數據
    end_date = date.today()
    start_date = end_date - timedelta(days=90)
    
    games = Game.query.filter(
        Game.date >= start_date,
        Game.date < end_date,
        Game.home_score.isnot(None),
        Game.away_score.isnot(None),
        Game.game_status == 'completed'
    ).all()
    
    print(f"分析 {len(games)} 場比賽")
    
    if len(games) == 0:
        print("❌ 沒有找到已完成的比賽數據")
        return
    
    # 初始化統計字典
    team_stats = defaultdict(lambda: {
        'games': 0, 'wins': 0, 'losses': 0,
        'home_games': 0, 'home_wins': 0, 'home_losses': 0,
        'away_games': 0, 'away_wins': 0, 'away_losses': 0,
        'runs_scored': 0, 'runs_allowed': 0,
    })
    
    # 計算統計數據
    for game in games:
        home_team = game.home_team
        away_team = game.away_team
        home_score = game.home_score
        away_score = game.away_score
        
        # 主隊統計
        team_stats[home_team]['games'] += 1
        team_stats[home_team]['home_games'] += 1
        team_stats[home_team]['runs_scored'] += home_score
        team_stats[home_team]['runs_allowed'] += away_score
        
        if home_score > away_score:
            team_stats[home_team]['wins'] += 1
            team_stats[home_team]['home_wins'] += 1
        else:
            team_stats[home_team]['losses'] += 1
            team_stats[home_team]['home_losses'] += 1
        
        # 客隊統計
        team_stats[away_team]['games'] += 1
        team_stats[away_team]['away_games'] += 1
        team_stats[away_team]['runs_scored'] += away_score
        team_stats[away_team]['runs_allowed'] += home_score
        
        if away_score > home_score:
            team_stats[away_team]['wins'] += 1
            team_stats[away_team]['away_wins'] += 1
        else:
            team_stats[away_team]['losses'] += 1
            team_stats[away_team]['away_losses'] += 1
    
    # 清除現有數據並保存新數據
    print("💾 保存統計數據...")
    TeamStats.query.delete()
    
    saved_count = 0
    for team_code, stats in team_stats.items():
        # 獲取球隊ID
        team = Team.query.filter_by(team_code=team_code).first()
        if not team:
            print(f"⚠️ 找不到球隊: {team_code}")
            continue
        
        # 計算平均值
        games = stats['games']
        if games > 0:
            runs_per_game = stats['runs_scored'] / games
            runs_allowed_per_game = stats['runs_allowed'] / games
            win_pct = stats['wins'] / games
            
            # 創建TeamStats記錄
            team_stat = TeamStats(
                team_id=team.team_id,
                season=2025,
                games_played=games,
                wins=stats['wins'],
                losses=stats['losses'],
                win_percentage=win_pct,
                runs_scored=runs_per_game,
                runs_allowed=runs_allowed_per_game,
                batting_avg=0.250 + (runs_per_game - 5.0) * 0.02,  # 基於得分調整
                era=runs_allowed_per_game,  # 簡化ERA
                home_wins=stats['home_wins'],
                home_losses=stats['home_losses'],
                away_wins=stats['away_wins'],
                away_losses=stats['away_losses']
            )
            
            db.session.add(team_stat)
            saved_count += 1
    
    db.session.commit()
    print(f"✅ 生成完成! 總計 {saved_count} 個球隊統計")

def check_team_stats_quality():
    """檢查TeamStats數據質量"""
    print("🔍 檢查TeamStats數據質量...")
    
    # 檢查幾個關鍵球隊
    test_teams = ['LAD', 'NYY', 'OAK', 'MIA', 'HOU']
    
    for team_code in test_teams:
        team = Team.query.filter_by(team_code=team_code).first()
        if team:
            stats = TeamStats.query.filter_by(team_id=team.team_id).first()
            if stats:
                print(f"  {team_code}: 得分={stats.runs_scored:.2f}, ERA={stats.era:.2f}, 勝率={stats.win_percentage:.3f}")
            else:
                print(f"  {team_code}: ❌ 無統計數據")
        else:
            print(f"  {team_code}: ❌ 找不到球隊")

def test_feature_extraction():
    """測試特徵提取"""
    print("🔍 測試特徵提取...")
    
    try:
        predictor = MLBPredictor()
        
        # 測試不同球隊組合的特徵提取
        test_games = [
            ('LAD', 'OAK'),  # 強隊 vs 弱隊
            ('NYY', 'MIA'),  # 強隊 vs 弱隊
        ]
        
        for home, away in test_games:
            try:
                features = predictor._extract_simple_features_for_game(home, away, date.today())
                if features:
                    print(f"  {away} @ {home}: ✅ 特徵提取成功")
                    print(f"    主隊得分: {features.get('home_runs_scored', 'N/A')}")
                    print(f"    客隊得分: {features.get('away_runs_scored', 'N/A')}")
                    print(f"    主隊ERA: {features.get('home_era', 'N/A')}")
                    print(f"    客隊ERA: {features.get('away_era', 'N/A')}")
                else:
                    print(f"  {away} @ {home}: ❌ 特徵提取失敗")
            except Exception as e:
                print(f"  {away} @ {home}: ❌ 錯誤 - {e}")
                
    except Exception as e:
        print(f"❌ 預測器初始化失敗: {e}")

def test_prediction_differences():
    """測試預測差異"""
    print("🎯 測試預測差異...")
    
    try:
        predictor = MLBPredictor()
        
        # 測試不同球隊組合
        test_games = [
            ('LAD', 'OAK'),  # 強隊 vs 弱隊
            ('NYY', 'MIA'),  # 強隊 vs 弱隊
            ('HOU', 'COL'),  # 強隊 vs 弱隊
        ]
        
        predictions = []
        for home, away in test_games:
            try:
                pred = predictor.predict_game(home, away, date.today())
                predictions.append(pred)
                print(f"  {away} @ {home}: {pred['predicted_away_score']:.1f} - {pred['predicted_home_score']:.1f}")
            except Exception as e:
                print(f"  {away} @ {home}: ❌ 錯誤 - {e}")
        
        # 分析差異
        if predictions:
            home_scores = [p['predicted_home_score'] for p in predictions]
            away_scores = [p['predicted_away_score'] for p in predictions]
            
            home_diff = max(home_scores) - min(home_scores)
            away_diff = max(away_scores) - min(away_scores)
            
            print(f"\n📈 差異分析:")
            print(f"主隊得分差異: {home_diff:.1f}")
            print(f"客隊得分差異: {away_diff:.1f}")
            
            if home_diff > 0.5 or away_diff > 0.5:
                print("✅ 預測有合理差異!")
            else:
                print("❌ 預測差異太小，需要進一步修復")
                
    except Exception as e:
        print(f"❌ 預測測試失敗: {e}")

def regenerate_predictions_if_needed():
    """如果需要，重新生成預測"""
    print("🔄 檢查是否需要重新生成預測...")
    
    # 檢查今日預測
    today = date.today()
    today_predictions = Prediction.query.filter(
        Prediction.prediction_date >= today
    ).all()
    
    if today_predictions:
        # 檢查預測是否都是4.0-5.0
        home_scores = [p.predicted_home_score for p in today_predictions]
        away_scores = [p.predicted_away_score for p in today_predictions]
        
        all_scores = home_scores + away_scores
        if all(4.0 <= score <= 5.0 for score in all_scores):
            print("❌ 發現4.0-5.0問題，需要重新生成預測")
            
            # 刪除今日預測
            for pred in today_predictions:
                db.session.delete(pred)
            db.session.commit()
            
            print("✅ 已刪除有問題的預測，請手動重新生成")
        else:
            print("✅ 預測看起來正常")
    else:
        print("ℹ️ 沒有找到今日預測")

if __name__ == "__main__":
    comprehensive_fix()
