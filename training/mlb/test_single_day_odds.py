#!/usr/bin/env python3
"""
測試單日賠率下載
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from models.covers_scraper import CoversMLBScraper
from models.database import db, BettingOdds, Game
from app import create_app
import logging

# 設置日誌級別
logging.basicConfig(level=logging.INFO)

def test_single_day_odds():
    """測試單日賠率下載"""
    print("=== 測試單日賠率下載 ===")
    
    app = create_app()
    
    with app.app_context():
        scraper = CoversMLBScraper()
        target_date = date(2024, 7, 7)
        
        print(f"測試日期: {target_date}")
        
        # 抓取數據
        result = scraper.fetch_mlb_games_for_date(target_date)
        
        if result['success']:
            games = result['games']
            print(f"抓取到 {len(games)} 場比賽")
            
            for i, game in enumerate(games[:5]):  # 只處理前5場
                print(f"\n比賽 {i+1}: {game['away_team']} @ {game['home_team']}")
                print(f"  賠率數據: {game['odds']}")
                
                # 檢查是否有總分線
                if game['odds'].get('total_line'):
                    total = float(game['odds']['total_line'])
                    if 5.0 <= total <= 15.0:
                        print(f"  ✅ 正常總分線: {total}")
                        
                        # 查找對應的比賽記錄
                        db_game = Game.query.filter_by(
                            date=target_date,
                            away_team=game['away_team'],
                            home_team=game['home_team']
                        ).first()
                        
                        if db_game:
                            # 創建賠率記錄
                            betting_odds = BettingOdds(
                                game_id=db_game.game_id,
                                bookmaker='covers.com',
                                market_type='totals',
                                total_point=total,
                                last_update=db.func.now()
                            )
                            
                            db.session.add(betting_odds)
                            print(f"  ➕ 已添加賠率記錄")
                        else:
                            print(f"  ⚠️  未找到對應的比賽記錄")
                    else:
                        print(f"  ❌ 異常總分線: {total}")
                else:
                    print(f"  ⚪ 無總分線數據")
            
            # 保存更改
            db.session.commit()
            print(f"\n✅ 已保存所有更改")
            
            # 驗證結果
            new_odds_count = BettingOdds.query.filter_by(bookmaker='covers.com').count()
            print(f"新增賠率記錄數: {new_odds_count}")
            
        else:
            print(f"抓取失敗: {result.get('error')}")

if __name__ == "__main__":
    test_single_day_odds()
