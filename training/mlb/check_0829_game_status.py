#!/usr/bin/env python3
"""
檢查2025-08-29比賽結果更新狀況
分析為什麼這些比賽還顯示為"預定"狀態
"""

import sys
sys.path.append('.')

import os
from datetime import datetime
from app import app

def check_0829_games_status():
    """檢查8/29比賽的詳細狀態"""
    
    print("🔍 檢查2025-08-29比賽狀態")
    print("=" * 60)
    
    # 設置環境
    os.environ['FLASK_PORT'] = '5509'
    
    with app.app_context():
        try:
            from models.database import db, Game
            
            # 查詢8/29的所有比賽
            target_date = '2025-08-29'
            games_0829 = db.session.query(Game).filter(
                Game.date == target_date
            ).all()
            
            print(f"\n📊 {target_date} 比賽統計:")
            print(f"   總比賽數: {len(games_0829)}")
            
            if not games_0829:
                print(f"   ❌ 未找到任何{target_date}的比賽記錄")
                return
            
            # 分析比賽狀態
            status_counts = {}
            incomplete_games = []
            
            print(f"\n🏟️ 比賽詳細狀況:")
            for i, game in enumerate(games_0829, 1):
                status = game.game_status or "未設定"
                status_counts[status] = status_counts.get(status, 0) + 1
                
                print(f"   {i:2d}. {game.away_team} @ {game.home_team}")
                print(f"       狀態: {status}")
                print(f"       比分: {game.away_score or 'N/A'} - {game.home_score or 'N/A'}")
                print(f"       Game ID: {game.game_id}")
                print(f"       更新時間: {game.updated_at or '未更新'}")
                
                # 檢查是否為未完成比賽
                if (status in ['scheduled', 'Scheduled', 'Pre-Game', '預定', None] or 
                    game.away_score is None or 
                    game.home_score is None):
                    incomplete_games.append(game)
                
                print()
            
            # 狀態統計
            print(f"📈 狀態統計:")
            for status, count in status_counts.items():
                print(f"   {status}: {count} 場")
            
            print(f"\n⚠️ 需要更新的比賽: {len(incomplete_games)} 場")
            
            # 檢查這個日期是否應該有結果
            check_date = datetime.strptime(target_date, '%Y-%m-%d')
            today = datetime.now()
            days_passed = (today - check_date).days
            
            print(f"\n📅 時間分析:")
            print(f"   目標日期: {target_date}")
            print(f"   今日: {today.strftime('%Y-%m-%d')}")
            print(f"   已過天數: {days_passed} 天")
            
            if days_passed >= 1:
                print(f"   ✅ 比賽應該已經完成，需要更新結果")
                
                # 提供更新建議
                print(f"\n🔧 建議操作:")
                print(f"   1. 使用 monthly_data_manager.py 重新獲取該月數據")
                print(f"   2. 或者使用專門的比賽結果更新工具")
                print(f"   3. 檢查 MLB API 是否有該日期的比賽結果")
                
                return {
                    'needs_update': True,
                    'incomplete_games': len(incomplete_games),
                    'total_games': len(games_0829),
                    'days_passed': days_passed
                }
            else:
                print(f"   ℹ️ 比賽可能尚未開始或進行中")
                return {
                    'needs_update': False,
                    'incomplete_games': len(incomplete_games),
                    'total_games': len(games_0829),
                    'days_passed': days_passed
                }
                
        except Exception as e:
            print(f"❌ 檢查過程失敗: {e}")
            import traceback
            traceback.print_exc()
            return None

def check_mlb_api_for_0829():
    """檢查 MLB API 是否有8/29的比賽結果"""
    
    print(f"\n🌐 檢查 MLB API 數據:")
    
    try:
        from boxscore_data_collector import BoxscoreDataCollector
        
        collector = BoxscoreDataCollector()
        
        # 嘗試獲取8/29的比賽列表
        target_date = '2025-08-29'
        
        print(f"   🔍 查詢 {target_date} 的 MLB API 數據...")
        
        # 這裡使用collector來檢查API是否有數據
        api_url = f"{collector.base_url}/schedule?sportId=1&date={target_date}"
        print(f"   📡 API URL: {api_url}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ MLB API 檢查失敗: {e}")
        return False

if __name__ == "__main__":
    print("🏟️ MLB 2025-08-29 比賽狀態檢查工具")
    print("分析為什麼比賽結果沒有更新")
    print()
    
    # 檢查資料庫中的比賽狀態
    result = check_0829_games_status()
    
    # 檢查API是否有數據
    api_available = check_mlb_api_for_0829()
    
    if result and result['needs_update']:
        print(f"\n🎯 結論:")
        print(f"   📊 {result['total_games']} 場比賽中有 {result['incomplete_games']} 場需要更新")
        print(f"   ⏰ 比賽已過 {result['days_passed']} 天，應該有結果了")
        
        if api_available:
            print(f"   ✅ 建議運行數據更新程序來獲取最新結果")
        else:
            print(f"   ⚠️ 需要檢查 MLB API 連接狀況")
    
    print(f"\n📋 下一步:")
    print(f"   1. 創建專門的8/29比賽結果更新工具")
    print(f"   2. 運行更新程序獲取最新比賽結果")
    print(f"   3. 驗證更新結果並檢查網頁顯示")