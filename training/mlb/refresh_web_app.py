#!/usr/bin/env python3
"""
刷新Web應用，確保顯示最新的預測數據
"""

import sys
import os
import signal
import time
import subprocess
from pathlib import Path

def kill_existing_flask_apps():
    """終止現有的Flask應用"""
    print("🔄 終止現有的Flask應用...")
    
    try:
        # 查找運行中的Flask應用進程
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        flask_pids = []
        for line in lines:
            if 'python' in line and ('app.py' in line or 'web_app.py' in line):
                parts = line.split()
                if len(parts) > 1:
                    try:
                        pid = int(parts[1])
                        flask_pids.append(pid)
                        print(f"  找到Flask進程: PID {pid}")
                    except ValueError:
                        continue
        
        # 終止進程
        for pid in flask_pids:
            try:
                os.kill(pid, signal.SIGTERM)
                print(f"  終止進程 {pid}")
                time.sleep(1)
            except ProcessLookupError:
                print(f"  進程 {pid} 已經終止")
            except PermissionError:
                print(f"  無權限終止進程 {pid}")
        
        if flask_pids:
            print(f"✅ 終止了 {len(flask_pids)} 個Flask進程")
        else:
            print("ℹ️  沒有找到運行中的Flask應用")
            
    except Exception as e:
        print(f"❌ 終止進程時出錯: {e}")

def clear_cache():
    """清除Python緩存"""
    print("🧹 清除Python緩存...")
    
    # 清除__pycache__目錄
    cache_dirs = []
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            cache_dirs.append(os.path.join(root, '__pycache__'))
    
    for cache_dir in cache_dirs:
        try:
            import shutil
            shutil.rmtree(cache_dir)
            print(f"  清除: {cache_dir}")
        except Exception as e:
            print(f"  清除失敗 {cache_dir}: {e}")
    
    print(f"✅ 清除了 {len(cache_dirs)} 個緩存目錄")

def verify_database_updates():
    """驗證數據庫更新"""
    print("🔍 驗證數據庫更新...")
    
    try:
        import sqlite3
        
        conn = sqlite3.connect('instance/mlb_data.db')
        cursor = conn.cursor()
        
        # 檢查2025-07-01的預測數據
        cursor.execute("""
            SELECT 
                g.away_team || '@' || g.home_team as matchup,
                p.predicted_total_runs,
                p.over_under_line,
                p.updated_at
            FROM predictions p
            JOIN games g ON p.game_id = g.game_id
            WHERE g.date = '2025-07-01'
            ORDER BY p.updated_at DESC
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        
        if results:
            print("  最新的預測數據:")
            for row in results:
                matchup, total, line, updated = row
                print(f"    {matchup}: 總分={total}, 盤口={line}, 更新時間={updated}")
        else:
            print("  ❌ 沒有找到2025-07-01的預測數據")
        
        conn.close()
        print("✅ 數據庫驗證完成")
        
    except Exception as e:
        print(f"❌ 數據庫驗證失敗: {e}")

def start_flask_app():
    """啟動Flask應用"""
    print("🚀 啟動Flask應用...")
    
    try:
        # 檢查是否有虛擬環境
        venv_python = Path('.venv/bin/python')
        if venv_python.exists():
            python_cmd = str(venv_python)
            print("  使用虛擬環境Python")
        else:
            python_cmd = 'python'
            print("  使用系統Python")
        
        # 啟動Flask應用
        print("  啟動命令: python app.py")
        print("  應用將在 http://localhost:5500 運行")
        print("  按 Ctrl+C 停止應用")
        
        # 在後台啟動
        subprocess.Popen([python_cmd, 'app.py'])
        time.sleep(3)  # 等待應用啟動
        
        print("✅ Flask應用已啟動")
        
    except Exception as e:
        print(f"❌ 啟動Flask應用失敗: {e}")

def main():
    """主函數"""
    print("🔄 刷新MLB預測Web應用")
    print("=" * 50)
    
    # 步驟1: 終止現有應用
    kill_existing_flask_apps()
    print()
    
    # 步驟2: 清除緩存
    clear_cache()
    print()
    
    # 步驟3: 驗證數據庫
    verify_database_updates()
    print()
    
    # 步驟4: 啟動應用
    start_flask_app()
    print()
    
    print("✅ 刷新完成！")
    print("🌐 請訪問: http://localhost:5500/predictions/over_under?date=2025-07-01")
    print("📊 查看更新後的大小分預測")

if __name__ == '__main__':
    main()
