#!/usr/bin/env python3
"""
直接測試管理端點的響應
"""

import sys
import logging
from app import create_app

# 設置詳細日誌
logging.basicConfig(level=logging.DEBUG)

def test_admin_endpoint():
    """測試管理介面端點"""
    app = create_app()
    
    with app.test_client() as client:
        print("🔍 測試管理介面端點")
        print("=" * 50)
        
        try:
            # 直接測試歷史盤口儀表板端點
            response = client.get('/admin/historical-odds-dashboard')
            
            print(f"📊 HTTP狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                # 檢查響應內容
                content = response.get_data(as_text=True)
                
                # 檢查是否包含數據
                if '0</span>' in content:
                    print("❌ 介面顯示0值")
                    
                    # 檢查是否有錯誤消息
                    if 'error' in content.lower():
                        print("⚠️ 可能包含錯誤消息")
                else:
                    print("✅ 介面可能顯示正確數據")
                
                # 檢查特定數值
                if '5200' in content or '3591' in content:
                    print("✅ 發現正確的數據值")
                else:
                    print("❌ 未發現預期的數據值")
                    
            else:
                print(f"❌ 請求失敗: {response.status_code}")
                print(response.get_data(as_text=True))
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_admin_endpoint()