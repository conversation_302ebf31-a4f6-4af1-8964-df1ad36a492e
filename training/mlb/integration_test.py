#!/usr/bin/env python3
"""
MLB 預測系統整合測試
綜合測試所有增強模組的協同運作效果

目標：
- 驗證所有模組正常運作
- 測試端對端預測流程
- 評估整體預測準確率提升
- 生成詳細測試報告
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import sqlite3
import warnings
warnings.filterwarnings('ignore')

# 添加模組路徑
project_root = os.path.dirname(__file__)
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, 'models'))
sys.path.append(os.path.join(project_root, 'models', 'enhanced_prediction'))

# 導入增強預測模組
try:
    from models.enhanced_prediction.score_calibration import ScoreCalibrationModule
    from models.enhanced_prediction.confidence_calculator import ConfidenceCalculator
    from models.enhanced_prediction.win_prediction import EnhancedWinPrediction
    from models.enhanced_prediction.pitcher_analyzer import PitcherImpactAnalyzer
    from models.enhanced_prediction.feature_weights import DynamicFeatureWeights
    from models.enhanced_prediction.ensemble_model import EnsemblePredictionModel
except ImportError:
    # 備用導入方式
    from score_calibration import ScoreCalibrationModule
    from confidence_calculator import ConfidenceCalculator
    from win_prediction import EnhancedWinPrediction
    from pitcher_analyzer import PitcherImpactAnalyzer
    from feature_weights import DynamicFeatureWeights
    from ensemble_model import EnsemblePredictionModel

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MLBPredictionIntegrationTest:
    """MLB 預測系統整合測試器"""
    
    def __init__(self, db_path: str = None):
        """初始化測試器"""
        self.db_path = db_path or "data/mlb_games.db"
        
        # 初始化所有增強模組
        self.score_calibrator = ScoreCalibrationModule()
        self.confidence_calculator = ConfidenceCalculator()
        self.win_predictor = EnhancedWinPrediction()
        self.pitcher_analyzer = PitcherImpactAnalyzer()
        self.feature_weights = DynamicFeatureWeights()
        self.ensemble_model = EnsemblePredictionModel()
        self.validation_system = ValidationSystem()
        
        # 測試結果儲存
        self.test_results = {
            'module_tests': {},
            'integration_tests': {},
            'performance_metrics': {},
            'comparison_results': {},
            'test_summary': {}
        }
        
        logger.info("MLB 預測系統整合測試器初始化完成")
    
    def run_full_test_suite(self) -> Dict:
        """執行完整測試套件"""
        try:
            logger.info("開始執行完整測試套件...")
            
            # 步驟 1: 準備測試數據
            test_data = self._prepare_test_data()
            logger.info(f"準備測試數據完成：{len(test_data)} 筆比賽數據")
            
            # 步驟 2: 單模組測試
            self._test_individual_modules(test_data)
            
            # 步驟 3: 整合測試
            self._test_integration_workflow(test_data)
            
            # 步驟 4: 性能比較測試
            self._test_performance_comparison(test_data)
            
            # 步驟 5: 壓力測試
            self._test_system_resilience()
            
            # 步驟 6: 生成測試報告
            final_report = self._generate_test_report()
            
            # 步驟 7: 儲存測試結果
            self._save_test_results(final_report)
            
            logger.info("完整測試套件執行完成")
            return final_report
            
        except Exception as e:
            logger.error(f"測試套件執行失敗: {e}")
            return {'error': str(e), 'status': 'failed'}
    
    def _prepare_test_data(self) -> List[Dict]:
        """準備測試數據"""
        try:
            # 模擬測試數據 (實際應用中從資料庫讀取)
            test_data = []
            
            # 生成多樣化的測試案例
            for i in range(100):
                game_data = {
                    'game_id': f'test_game_{i:03d}',
                    'home_team': np.random.choice(['LAD', 'NYY', 'HOU', 'ATL', 'SF']),
                    'away_team': np.random.choice(['BOS', 'TB', 'CLE', 'MIN', 'SEA']),
                    'game_date': date(2024, 6, 1) + timedelta(days=i % 30),
                    
                    # 模擬原始分數預測
                    'original_home_score': np.random.normal(4.8, 1.2),
                    'original_away_score': np.random.normal(4.3, 1.1),
                    
                    # 模擬實際結果
                    'actual_home_score': np.random.normal(4.5, 1.5),
                    'actual_away_score': np.random.normal(4.2, 1.4),
                    
                    # 比賽特徵
                    'features': {
                        'home_team': np.random.choice(['LAD', 'NYY', 'HOU', 'ATL', 'SF']),
                        'away_team': np.random.choice(['BOS', 'TB', 'CLE', 'MIN', 'SEA']),
                        'game_date': date(2024, 6, 1) + timedelta(days=i % 30),
                        'game_importance': np.random.choice(['regular', 'division_critical', 'playoff']),
                        'pitcher_data_complete': np.random.choice([True, False], p=[0.8, 0.2]),
                        'recent_form_available': np.random.choice([True, False], p=[0.9, 0.1])
                    },
                    
                    # 詳細數據
                    'detailed_data': self._generate_detailed_game_data()
                }
                
                # 計算實際獲勝隊伍
                game_data['actual_winner'] = (game_data['features']['home_team'] 
                                            if game_data['actual_home_score'] > game_data['actual_away_score'] 
                                            else game_data['features']['away_team'])
                
                test_data.append(game_data)
            
            return test_data
            
        except Exception as e:
            logger.error(f"測試數據準備失敗: {e}")
            return []
    
    def _generate_detailed_game_data(self) -> Dict:
        """生成詳細比賽數據"""
        return {
            'pitcher_data': {
                'home_pitcher': {
                    'era': np.random.normal(3.8, 0.8),
                    'whip': np.random.normal(1.25, 0.15),
                    'k9': np.random.normal(9.2, 1.5),
                    'mlb_innings': np.random.randint(50, 200),
                    'recent_starts': [{'era': np.random.normal(3.8, 1.0)} for _ in range(5)]
                },
                'away_pitcher': {
                    'era': np.random.normal(3.9, 0.9),
                    'whip': np.random.normal(1.28, 0.18),
                    'k9': np.random.normal(8.8, 1.3),
                    'mlb_innings': np.random.randint(50, 200),
                    'recent_starts': [{'era': np.random.normal(3.9, 1.1)} for _ in range(5)]
                }
            },
            'home_recent_games': [
                {
                    'won': np.random.choice([True, False]),
                    'runs_differential': np.random.randint(-8, 9)
                } for _ in range(10)
            ],
            'away_recent_games': [
                {
                    'won': np.random.choice([True, False]),
                    'runs_differential': np.random.randint(-8, 9)
                } for _ in range(10)
            ],
            'weather': {
                'temperature': np.random.normal(72, 15),
                'wind_speed': np.random.normal(8, 5),
                'precipitation_chance': np.random.randint(0, 50)
            },
            'team_stats': {
                'home': {
                    'home_runs_per_game': np.random.normal(1.2, 0.3),
                    'stolen_bases_per_game': np.random.normal(0.8, 0.2),
                    'era': np.random.normal(4.0, 0.5)
                },
                'away': {
                    'home_runs_per_game': np.random.normal(1.1, 0.3),
                    'stolen_bases_per_game': np.random.normal(0.7, 0.2),
                    'era': np.random.normal(4.1, 0.6)
                }
            }
        }
    
    def _test_individual_modules(self, test_data: List[Dict]):
        """測試個別模組"""
        try:
            logger.info("開始單模組測試...")
            
            # 測試分數校正模組
            logger.info("測試分數校正模組...")
            score_results = []
            for data in test_data[:20]:  # 測試20筆
                try:
                    calibrated = self.score_calibrator.calibrate_scores(
                        data['original_home_score'],
                        data['original_away_score'],
                        data['features']['home_team'],
                        data['features']['away_team']
                    )
                    score_results.append(calibrated is not None)
                except Exception as e:
                    logger.warning(f"分數校正測試失敗: {e}")
                    score_results.append(False)
            
            self.test_results['module_tests']['score_calibration'] = {
                'success_rate': np.mean(score_results),
                'total_tests': len(score_results)
            }
            
            # 測試信心度計算器
            logger.info("測試信心度計算器...")
            confidence_results = []
            for data in test_data[:20]:
                try:
                    confidence = self.confidence_calculator.calculate_confidence(
                        {'home_score': data['original_home_score'],
                         'away_score': data['original_away_score']},
                        {'accuracy': 0.6}
                    )
                    confidence_results.append(0 <= confidence <= 1)
                except Exception as e:
                    logger.warning(f"信心度計算測試失敗: {e}")
                    confidence_results.append(False)
            
            self.test_results['module_tests']['confidence_calculator'] = {
                'success_rate': np.mean(confidence_results),
                'total_tests': len(confidence_results)
            }
            
            # 測試勝負預測器
            logger.info("測試勝負預測器...")
            win_prediction_results = []
            for data in test_data[:20]:
                try:
                    prediction = self.win_predictor.predict_winner(
                        data['original_home_score'],
                        data['original_away_score'],
                        data['features'],
                        data['detailed_data']
                    )
                    win_prediction_results.append('predicted_winner' in prediction)
                except Exception as e:
                    logger.warning(f"勝負預測測試失敗: {e}")
                    win_prediction_results.append(False)
            
            self.test_results['module_tests']['win_prediction'] = {
                'success_rate': np.mean(win_prediction_results),
                'total_tests': len(win_prediction_results)
            }
            
            # 測試動態權重系統
            logger.info("測試動態權重系統...")
            weight_results = []
            for data in test_data[:20]:
                try:
                    weights = self.feature_weights.adjust_weights({
                        'home_team': data['features']['home_team'],
                        'away_team': data['features']['away_team'],
                        'game_date': data['features']['game_date'],
                        'pitcher_data': data['detailed_data']['pitcher_data'],
                        'weather': data['detailed_data']['weather']
                    })
                    weight_results.append(abs(sum(weights.values()) - 1.0) < 0.01)
                except Exception as e:
                    logger.warning(f"動態權重測試失敗: {e}")
                    weight_results.append(False)
            
            self.test_results['module_tests']['dynamic_weights'] = {
                'success_rate': np.mean(weight_results),
                'total_tests': len(weight_results)
            }
            
            logger.info("單模組測試完成")
            
        except Exception as e:
            logger.error(f"單模組測試失敗: {e}")
    
    def _test_integration_workflow(self, test_data: List[Dict]):
        """測試整合工作流程"""
        try:
            logger.info("開始整合工作流程測試...")
            
            integration_results = []
            prediction_accuracies = []
            
            for data in test_data:
                try:
                    # 完整預測流程
                    # 1. 分數校正
                    calibrated_scores = self.score_calibrator.calibrate_scores(
                        data['original_home_score'],
                        data['original_away_score'],
                        data['features']['home_team'],
                        data['features']['away_team']
                    )
                    
                    # 2. 動態權重調整
                    adjusted_weights = self.feature_weights.adjust_weights({
                        'home_team': data['features']['home_team'],
                        'away_team': data['features']['away_team'],
                        'game_date': data['features']['game_date'],
                        'pitcher_data': data['detailed_data']['pitcher_data']
                    })
                    
                    # 3. 勝負預測
                    win_prediction = self.win_predictor.predict_winner(
                        calibrated_scores['calibrated_home_score'],
                        calibrated_scores['calibrated_away_score'],
                        data['features'],
                        data['detailed_data']
                    )
                    
                    # 4. 信心度計算
                    confidence = self.confidence_calculator.calculate_confidence(
                        {
                            'home_score': calibrated_scores['calibrated_home_score'],
                            'away_score': calibrated_scores['calibrated_away_score'],
                            'win_probability': win_prediction['home_win_probability']
                        },
                        {'accuracy': 0.6}
                    )
                    
                    # 檢查預測準確性
                    predicted_winner = win_prediction['predicted_winner']
                    actual_winner = data['actual_winner']
                    is_correct = predicted_winner == actual_winner
                    
                    integration_results.append(True)
                    prediction_accuracies.append(is_correct)
                    
                except Exception as e:
                    logger.warning(f"整合流程測試失敗: {e}")
                    integration_results.append(False)
                    prediction_accuracies.append(False)
            
            self.test_results['integration_tests'] = {
                'workflow_success_rate': np.mean(integration_results),
                'prediction_accuracy': np.mean(prediction_accuracies),
                'total_tests': len(integration_results)
            }
            
            logger.info(f"整合測試完成 - 工作流程成功率: {np.mean(integration_results):.3f}")
            logger.info(f"預測準確率: {np.mean(prediction_accuracies):.3f}")
            
        except Exception as e:
            logger.error(f"整合工作流程測試失敗: {e}")
    
    def _test_performance_comparison(self, test_data: List[Dict]):
        """測試性能比較"""
        try:
            logger.info("開始性能比較測試...")
            
            # 基準預測 (原始方法)
            baseline_predictions = []
            enhanced_predictions = []
            
            for data in test_data:
                try:
                    # 基準預測：簡單基於分數
                    baseline_home_prob = data['original_home_score'] / (
                        data['original_home_score'] + data['original_away_score']
                    )
                    baseline_winner = (data['features']['home_team'] 
                                     if baseline_home_prob > 0.5 
                                     else data['features']['away_team'])
                    baseline_correct = baseline_winner == data['actual_winner']
                    baseline_predictions.append(baseline_correct)
                    
                    # 增強預測：完整流程
                    calibrated = self.score_calibrator.calibrate_scores(
                        data['original_home_score'],
                        data['original_away_score'],
                        data['features']['home_team'],
                        data['features']['away_team']
                    )
                    
                    enhanced_prediction = self.win_predictor.predict_winner(
                        calibrated['calibrated_home_score'],
                        calibrated['calibrated_away_score'],
                        data['features'],
                        data['detailed_data']
                    )
                    
                    enhanced_winner = enhanced_prediction['predicted_winner']
                    enhanced_correct = enhanced_winner == data['actual_winner']
                    enhanced_predictions.append(enhanced_correct)
                    
                except Exception as e:
                    logger.warning(f"性能比較測試失敗: {e}")
                    baseline_predictions.append(False)
                    enhanced_predictions.append(False)
            
            baseline_accuracy = np.mean(baseline_predictions)
            enhanced_accuracy = np.mean(enhanced_predictions)
            improvement = enhanced_accuracy - baseline_accuracy
            
            self.test_results['performance_metrics'] = {
                'baseline_accuracy': baseline_accuracy,
                'enhanced_accuracy': enhanced_accuracy,
                'accuracy_improvement': improvement,
                'improvement_percentage': (improvement / baseline_accuracy * 100) if baseline_accuracy > 0 else 0,
                'total_comparisons': len(baseline_predictions)
            }
            
            logger.info(f"性能比較完成:")
            logger.info(f"基準準確率: {baseline_accuracy:.3f}")
            logger.info(f"增強準確率: {enhanced_accuracy:.3f}")
            logger.info(f"改善幅度: {improvement:+.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
            
        except Exception as e:
            logger.error(f"性能比較測試失敗: {e}")
    
    def _test_system_resilience(self):
        """測試系統韌性"""
        try:
            logger.info("開始系統韌性測試...")
            
            resilience_tests = {
                'missing_data': self._test_missing_data_handling(),
                'extreme_values': self._test_extreme_value_handling(),
                'concurrent_load': self._test_concurrent_processing(),
                'memory_usage': self._test_memory_efficiency()
            }
            
            self.test_results['resilience_tests'] = resilience_tests
            
            logger.info("系統韌性測試完成")
            
        except Exception as e:
            logger.error(f"系統韌性測試失敗: {e}")
    
    def _test_missing_data_handling(self) -> Dict:
        """測試缺失數據處理"""
        try:
            # 測試各種缺失數據情況
            test_cases = [
                {'original_home_score': 5.0, 'original_away_score': None},
                {'original_home_score': None, 'original_away_score': 4.0},
                {'features': {}},
                {'detailed_data': None}
            ]
            
            success_count = 0
            for case in test_cases:
                try:
                    if case.get('original_home_score') and case.get('original_away_score'):
                        calibrated = self.score_calibrator.calibrate_scores(
                            case['original_home_score'], 
                            case['original_away_score'], 
                            'LAD', 'BOS'
                        )
                        if calibrated:
                            success_count += 1
                except:
                    pass
            
            return {
                'success_rate': success_count / len(test_cases),
                'tested_scenarios': len(test_cases)
            }
            
        except Exception as e:
            logger.error(f"缺失數據測試失敗: {e}")
            return {'success_rate': 0.0, 'error': str(e)}
    
    def _test_extreme_value_handling(self) -> Dict:
        """測試極端值處理"""
        try:
            extreme_cases = [
                {'home_score': 0.0, 'away_score': 20.0},
                {'home_score': 15.0, 'away_score': 0.0},
                {'home_score': -1.0, 'away_score': 5.0}
            ]
            
            success_count = 0
            for case in extreme_cases:
                try:
                    calibrated = self.score_calibrator.calibrate_scores(
                        case['home_score'], case['away_score'], 'LAD', 'BOS'
                    )
                    # 檢查結果是否合理
                    if (calibrated and 
                        calibrated['calibrated_home_score'] >= 0 and
                        calibrated['calibrated_away_score'] >= 0):
                        success_count += 1
                except:
                    pass
            
            return {
                'success_rate': success_count / len(extreme_cases),
                'tested_scenarios': len(extreme_cases)
            }
            
        except Exception as e:
            logger.error(f"極端值測試失敗: {e}")
            return {'success_rate': 0.0, 'error': str(e)}
    
    def _test_concurrent_processing(self) -> Dict:
        """測試並發處理"""
        try:
            import threading
            import time
            
            results = []
            
            def worker():
                try:
                    for _ in range(10):
                        calibrated = self.score_calibrator.calibrate_scores(5.0, 4.0, 'LAD', 'BOS')
                        results.append(calibrated is not None)
                except:
                    results.append(False)
            
            # 創建多個執行緒
            threads = []
            for _ in range(5):
                t = threading.Thread(target=worker)
                threads.append(t)
                t.start()
            
            # 等待完成
            for t in threads:
                t.join()
            
            return {
                'success_rate': np.mean(results) if results else 0.0,
                'total_operations': len(results),
                'concurrent_threads': 5
            }
            
        except Exception as e:
            logger.error(f"並發處理測試失敗: {e}")
            return {'success_rate': 0.0, 'error': str(e)}
    
    def _test_memory_efficiency(self) -> Dict:
        """測試記憶體效率"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
            
            # 執行大量預測
            for i in range(1000):
                try:
                    self.score_calibrator.calibrate_scores(5.0 + i*0.01, 4.0 + i*0.01, 'LAD', 'BOS')
                except:
                    pass
            
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = memory_after - memory_before
            
            return {
                'memory_before_mb': memory_before,
                'memory_after_mb': memory_after,
                'memory_increase_mb': memory_increase,
                'efficiency_rating': 'excellent' if memory_increase < 50 else 'good' if memory_increase < 100 else 'needs_optimization'
            }
            
        except Exception as e:
            logger.error(f"記憶體效率測試失敗: {e}")
            return {'error': str(e)}
    
    def _generate_test_report(self) -> Dict:
        """生成測試報告"""
        try:
            # 計算總體測試分數
            module_scores = []
            for module, result in self.test_results['module_tests'].items():
                module_scores.append(result.get('success_rate', 0))
            
            overall_module_score = np.mean(module_scores) if module_scores else 0
            
            integration_score = self.test_results['integration_tests'].get('workflow_success_rate', 0)
            
            performance_metrics = self.test_results['performance_metrics']
            performance_score = min(1.0, max(0.0, performance_metrics.get('enhanced_accuracy', 0)))
            
            # 計算總分
            total_score = (overall_module_score * 0.3 + 
                          integration_score * 0.3 + 
                          performance_score * 0.4)
            
            # 評定等級
            if total_score >= 0.9:
                grade = 'A (優秀)'
            elif total_score >= 0.8:
                grade = 'B (良好)'
            elif total_score >= 0.7:
                grade = 'C (及格)'
            else:
                grade = 'D (需改善)'
            
            report = {
                'test_summary': {
                    'overall_score': total_score,
                    'grade': grade,
                    'test_timestamp': datetime.now().isoformat(),
                    'total_test_cases': sum(
                        result.get('total_tests', 0) 
                        for result in self.test_results['module_tests'].values()
                    )
                },
                'detailed_results': self.test_results,
                'recommendations': self._generate_recommendations(total_score),
                'next_steps': self._generate_next_steps()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"測試報告生成失敗: {e}")
            return {'error': str(e), 'status': 'failed'}
    
    def _generate_recommendations(self, score: float) -> List[str]:
        """生成改善建議"""
        recommendations = []
        
        if score < 0.8:
            recommendations.append("建議加強模組測試覆蓋率")
        
        if self.test_results['performance_metrics'].get('enhanced_accuracy', 0) < 0.6:
            recommendations.append("需要改善預測準確率，考慮調整模型參數")
        
        module_tests = self.test_results['module_tests']
        for module, result in module_tests.items():
            if result.get('success_rate', 0) < 0.9:
                recommendations.append(f"加強 {module} 模組的錯誤處理")
        
        if not recommendations:
            recommendations.append("系統表現良好，建議持續監控和優化")
        
        return recommendations
    
    def _generate_next_steps(self) -> List[str]:
        """生成下一步行動"""
        return [
            "部署到測試環境進行實際數據驗證",
            "設置監控系統追蹤預測準確率",
            "建立 A/B 測試框架比較新舊系統",
            "準備生產環境部署計劃",
            "訓練團隊使用新的預測系統",
            "建立定期性能評估機制"
        ]
    
    def _save_test_results(self, report: Dict):
        """儲存測試結果"""
        try:
            # 儲存詳細報告
            report_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"測試報告已儲存至: {report_file}")
            
            # 儲存簡要摘要
            summary = {
                'timestamp': datetime.now().isoformat(),
                'overall_score': report['test_summary']['overall_score'],
                'grade': report['test_summary']['grade'],
                'enhanced_accuracy': self.test_results['performance_metrics'].get('enhanced_accuracy', 0),
                'accuracy_improvement': self.test_results['performance_metrics'].get('accuracy_improvement', 0)
            }
            
            with open('latest_test_summary.json', 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info("測試摘要已更新")
            
        except Exception as e:
            logger.error(f"測試結果儲存失敗: {e}")

def main():
    """主程式"""
    try:
        print("=" * 60)
        print("MLB 預測系統整合測試")
        print("=" * 60)
        
        # 初始化測試器
        tester = MLBPredictionIntegrationTest()
        
        # 執行完整測試
        report = tester.run_full_test_suite()
        
        # 顯示結果
        if 'error' not in report:
            print(f"\n✅ 測試完成!")
            print(f"總體評分: {report['test_summary']['overall_score']:.3f}")
            print(f"評定等級: {report['test_summary']['grade']}")
            
            performance = report['detailed_results']['performance_metrics']
            print(f"增強準確率: {performance.get('enhanced_accuracy', 0):.3f}")
            print(f"準確率改善: {performance.get('accuracy_improvement', 0):+.3f}")
            
            print("\n建議事項:")
            for rec in report['recommendations']:
                print(f"  • {rec}")
        else:
            print(f"❌ 測試失敗: {report['error']}")
            
    except Exception as e:
        print(f"❌ 程式執行失敗: {e}")
        
if __name__ == "__main__":
    main()