#!/usr/bin/env python3
"""
檢查延賽和重賽情況
分析重複game_id是否因為延賽導致
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.database import db, Game, Prediction
from datetime import date, datetime
from sqlalchemy import text

def check_duplicate_game_ids():
    """檢查重複的game_id情況"""
    print("🔍 檢查重複game_id情況...")
    
    app = create_app()
    with app.app_context():
        # 查找重複的game_id
        query = text("""
            SELECT game_id, COUNT(*) as count,
                   GROUP_CONCAT(DISTINCT date) as dates,
                   GROUP_CONCAT(DISTINCT home_team || ' vs ' || away_team) as matchups,
                   GROUP_CONCAT(DISTINCT COALESCE(home_score, 'N') || '-' || COALESCE(away_score, 'N')) as scores
            FROM games
            GROUP BY game_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC, game_id
        """)
        
        results = db.session.execute(query).fetchall()
        
        print(f"📊 找到 {len(results)} 個重複的game_id:")
        print("-" * 100)
        print("Game ID   重複數  日期                    對戰                    比分")
        print("-" * 100)

        postponed_games = []
        for row in results:
            game_id, count, dates, matchups, scores = row
            print(f"{game_id:8}  {count:4}    {dates:20}  {matchups:20}  {scores:12}")

            # 檢查是否是延賽情況
            date_list = dates.split(',') if dates else []
            if len(date_list) > 1:
                postponed_games.append({
                    'game_id': game_id,
                    'dates': date_list,
                    'matchups': matchups,
                    'scores': scores
                })
        
        return postponed_games

def analyze_postponed_games(postponed_games):
    """分析延賽比賽的詳細情況"""
    print(f"\n📅 分析延賽比賽詳情...")
    
    app = create_app()
    with app.app_context():
        for game_info in postponed_games:
            game_id = game_info['game_id']
            print(f"\n🏟️  Game ID: {game_id}")
            print("-" * 60)
            
            # 獲取該game_id的所有記錄
            games = Game.query.filter_by(game_id=game_id).order_by(Game.date).all()
            
            for i, game in enumerate(games, 1):
                score_text = f"{game.away_score}-{game.home_score}" if game.home_score is not None else "未完成"

                print(f"  記錄{i}: {game.date} | {game.away_team} @ {game.home_team} | {score_text}")

                # 檢查是否有預測記錄
                predictions = Prediction.query.filter_by(game_id=game_id).all()
                if predictions:
                    print(f"    預測記錄: {len(predictions)} 個")

def identify_correct_game_date():
    """識別正確的比賽日期並建議修正"""
    print(f"\n🔧 識別需要修正的比賽日期...")
    
    app = create_app()
    with app.app_context():
        # 查找重複game_id的詳細信息
        query = text("""
            SELECT game_id, date, home_team, away_team, home_score, away_score,
                   ROW_NUMBER() OVER (PARTITION BY game_id ORDER BY date) as row_num
            FROM games
            WHERE game_id IN (
                SELECT game_id
                FROM games
                GROUP BY game_id
                HAVING COUNT(*) > 1
            )
            ORDER BY game_id, date
        """)
        
        results = db.session.execute(query).fetchall()
        
        corrections = []
        current_game_id = None
        game_records = []
        
        for row in results:
            game_id, game_date, home_team, away_team, home_score, away_score, row_num = row

            if current_game_id != game_id:
                if current_game_id and len(game_records) > 1:
                    corrections.extend(analyze_game_records(current_game_id, game_records))
                current_game_id = game_id
                game_records = []

            game_records.append({
                'game_id': game_id,
                'date': game_date,
                'home_team': home_team,
                'away_team': away_team,
                'home_score': home_score,
                'away_score': away_score,
                'row_num': row_num
            })
        
        # 處理最後一組
        if current_game_id and len(game_records) > 1:
            corrections.extend(analyze_game_records(current_game_id, game_records))
        
        return corrections

def analyze_game_records(game_id, records):
    """分析單個game_id的記錄並建議修正"""
    corrections = []
    
    print(f"\n🎯 分析 Game ID: {game_id}")
    
    # 找出有實際比分的記錄（比賽已完成）
    completed_games = [r for r in records if r['home_score'] is not None and r['away_score'] is not None]
    scheduled_games = [r for r in records if r['home_score'] is None or r['away_score'] is None]
    
    if len(completed_games) == 1 and len(scheduled_games) >= 1:
        # 典型的延賽情況：一個完成的比賽，一個或多個未完成的記錄
        completed_game = completed_games[0]
        
        print(f"  ✅ 已完成: {completed_game['date']} | {completed_game['away_score']}-{completed_game['home_score']}")
        
        for scheduled in scheduled_games:
            print(f"  ⏳ 原定: {scheduled['date']} | 未完成")
            
            # 建議刪除原定日期的記錄
            corrections.append({
                'action': 'delete',
                'game_id': game_id,
                'date': scheduled['date'],
                'reason': f"延賽，實際比賽日期為 {completed_game['date']}"
            })
    
    elif len(completed_games) > 1:
        # 多個完成的記錄，可能是數據錯誤
        print(f"  ⚠️  多個完成記錄，需要人工檢查:")
        for completed in completed_games:
            print(f"    {completed['date']} | {completed['away_score']}-{completed['home_score']}")
    
    elif len(completed_games) == 0:
        # 都是未完成的記錄
        print(f"  ⏳ 所有記錄都未完成，可能是未來比賽")
        
        # 保留最新日期的記錄
        latest_record = max(records, key=lambda x: x['date'])
        for record in records:
            if record['date'] != latest_record['date']:
                corrections.append({
                    'action': 'delete',
                    'game_id': game_id,
                    'date': record['date'],
                    'reason': f"保留最新日期 {latest_record['date']}"
                })
    
    return corrections

def apply_corrections(corrections):
    """應用修正建議"""
    print(f"\n🔧 應用修正建議...")
    
    if not corrections:
        print("✅ 沒有需要修正的記錄")
        return
    
    app = create_app()
    with app.app_context():
        deleted_count = 0
        
        for correction in corrections:
            if correction['action'] == 'delete':
                try:
                    # 刪除指定日期的比賽記錄
                    games_to_delete = Game.query.filter_by(
                        game_id=correction['game_id'],
                        date=correction['date']
                    ).all()
                    
                    for game in games_to_delete:
                        print(f"🗑️  刪除: {game.game_id} | {game.date} | {game.away_team} @ {game.home_team}")
                        db.session.delete(game)
                        deleted_count += 1
                        
                except Exception as e:
                    print(f"❌ 刪除失敗 {correction['game_id']} {correction['date']}: {e}")
        
        if deleted_count > 0:
            try:
                db.session.commit()
                print(f"✅ 成功刪除 {deleted_count} 個重複記錄")
            except Exception as e:
                db.session.rollback()
                print(f"❌ 提交失敗: {e}")

def main():
    """主要檢查流程"""
    print("🔧 延賽和重賽檢查工具")
    print("=" * 60)
    
    # 1. 檢查重複game_id
    postponed_games = check_duplicate_game_ids()
    
    # 2. 分析延賽比賽
    if postponed_games:
        analyze_postponed_games(postponed_games)
    
    # 3. 識別需要修正的記錄
    corrections = identify_correct_game_date()
    
    # 4. 顯示修正建議
    if corrections:
        print(f"\n📋 修正建議:")
        print("-" * 80)
        for correction in corrections:
            print(f"  {correction['action'].upper()}: {correction['game_id']} | {correction['date']} | {correction['reason']}")
        
        # 詢問是否應用修正
        print(f"\n❓ 是否應用這些修正？(y/n)")
        # 在腳本中自動應用修正
        print("🤖 自動應用修正...")
        apply_corrections(corrections)
    
    print(f"\n📋 檢查總結:")
    print(f"  重複game_id數: {len(postponed_games)}")
    print(f"  建議修正數: {len(corrections) if corrections else 0}")

if __name__ == "__main__":
    main()
