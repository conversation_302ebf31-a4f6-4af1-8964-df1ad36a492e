#!/usr/bin/env python3
"""
更新預測記錄的實際比賽結果
為準確性分析做準備
"""

from app import create_app
from models.database import db, PredictionHistory, Game
from datetime import datetime, date
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_prediction_results():
    """更新預測記錄的實際比賽結果"""
    app = create_app()
    
    with app.app_context():
        print("🔄 開始更新預測記錄的實際比賽結果...")
        
        # 找到所有沒有實際結果的預測記錄
        predictions_without_results = PredictionHistory.query.filter(
            PredictionHistory.actual_home_score.is_(None)
        ).all()
        
        print(f"   找到 {len(predictions_without_results)} 個沒有實際結果的預測")
        
        updated_count = 0
        matched_count = 0
        
        for prediction in predictions_without_results:
            try:
                # 尋找對應的比賽記錄
                game = Game.query.filter_by(game_id=prediction.game_id).first()
                
                if game and game.home_score is not None and game.away_score is not None:
                    # 更新實際結果
                    prediction.actual_home_score = game.home_score
                    prediction.actual_away_score = game.away_score
                    
                    # 計算準確度評分
                    prediction.prediction_accuracy_score = prediction.calculate_accuracy()
                    
                    # 更新時間戳
                    prediction.updated_at = datetime.utcnow()
                    
                    matched_count += 1
                    
                    if matched_count % 50 == 0:
                        print(f"   已匹配 {matched_count} 條記錄...")
                
            except Exception as e:
                print(f"   更新預測 {prediction.game_id} 時出錯: {e}")
                continue
        
        # 提交更改
        try:
            db.session.commit()
            updated_count = matched_count
            print(f"✅ 成功更新 {updated_count} 條預測記錄的實際結果")
            
            # 統計更新後的結果
            predictions_with_results = PredictionHistory.query.filter(
                PredictionHistory.actual_home_score.isnot(None)
            ).count()
            
            print(f"📊 現在總共有 {predictions_with_results} 條有完整結果的預測記錄")
            
            if predictions_with_results > 0:
                print("🎉 可以開始進行預測準確性分析了！")
                return True
            else:
                print("⚠️ 仍然沒有完整的預測結果數據")
                return False
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ 提交更改時出錯: {e}")
            return False

def analyze_game_id_patterns():
    """分析game_id格式，找出匹配問題"""
    app = create_app()
    
    with app.app_context():
        print("\n🔍 分析game_id格式模式...")
        
        # 獲取預測記錄中的game_id樣本
        pred_samples = db.session.query(PredictionHistory.game_id).distinct().limit(10).all()
        print("   預測記錄中的game_id樣本:")
        for sample in pred_samples:
            print(f"      {sample[0]}")
        
        # 獲取比賽記錄中的game_id樣本
        game_samples = db.session.query(Game.game_id).filter(
            Game.home_score.isnot(None)
        ).distinct().limit(10).all()
        print("\n   比賽記錄中的game_id樣本:")
        for sample in game_samples:
            print(f"      {sample[0]}")
        
        # 檢查是否有直接匹配
        print("\n🔗 檢查直接匹配...")
        direct_matches = db.session.query(PredictionHistory.game_id).filter(
            PredictionHistory.game_id.in_(
                db.session.query(Game.game_id).filter(Game.home_score.isnot(None))
            )
        ).count()
        
        print(f"   直接匹配的game_id數量: {direct_matches}")

def main():
    """主函數"""
    # 首先分析game_id格式
    analyze_game_id_patterns()
    
    # 嘗試更新預測結果
    success = update_prediction_results()
    
    if success:
        print("\n下一步：執行 python analyze_prediction_accuracy.py 來分析預測準確性")
    else:
        print("\n需要進一步調查為什麼無法匹配預測記錄和比賽記錄")

if __name__ == "__main__":
    main()