#!/usr/bin/env python3
"""
專門修復Unknown博彩盤口問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from app import create_app
from models.database import db, Game, BettingOdds
from models.enhanced_odds_fetcher import <PERSON>hanced<PERSON>ddsFetcher

def fix_unknown_odds_for_date(target_date):
    """修復特定日期的Unknown盤口"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔧 修復 {target_date} 的Unknown盤口...")
        
        # 獲取該日期的所有比賽
        games = Game.query.filter(Game.date == target_date).all()
        
        if not games:
            print(f"  ❌ 沒有找到 {target_date} 的比賽")
            return 0
        
        print(f"  📊 找到 {len(games)} 場比賽")
        
        # 嘗試獲取當日的博彩盤口
        odds_fetcher = EnhancedOddsFetcher()
        odds_data = odds_fetcher.get_mlb_odds_today(target_date)
        
        fixed_count = 0
        
        for game in games:
            # 檢查是否缺少大小分盤口
            existing_odds = BettingOdds.query.filter_by(
                game_id=game.game_id,
                market_type='totals'
            ).first()
            
            if existing_odds and existing_odds.total_point:
                continue  # 已有盤口，跳過
            
            print(f"    🔧 修復 {game.away_team} @ {game.home_team}")
            
            # 嘗試從API獲取的數據中匹配
            game_odds = None
            if odds_data.get('success') and odds_data.get('games'):
                for api_game in odds_data['games']:
                    if (match_team_names(game.home_team, api_game.get('home_team', '')) and
                        match_team_names(game.away_team, api_game.get('away_team', ''))):
                        game_odds = api_game.get('odds', {})
                        break
            
            # 創建或更新盤口記錄
            if game_odds and game_odds.get('totals'):
                totals = game_odds['totals']
                
                if existing_odds:
                    # 更新現有記錄
                    existing_odds.total_point = totals.get('total_point', 8.5)
                    existing_odds.over_price = totals.get('over_odds', -110)
                    existing_odds.under_price = totals.get('under_odds', -110)
                    existing_odds.bookmaker = totals.get('bookmaker', 'bet365')
                    existing_odds.is_real = True
                    existing_odds.data_source = 'api'
                    existing_odds.updated_at = datetime.now()
                else:
                    # 創建新記錄
                    new_odds = BettingOdds(
                        game_id=game.game_id,
                        bookmaker=totals.get('bookmaker', 'bet365'),
                        market_type='totals',
                        total_point=totals.get('total_point', 8.5),
                        over_price=totals.get('over_odds', -110),
                        under_price=totals.get('under_odds', -110),
                        is_real=True,
                        data_source='api',
                        odds_time=datetime.now(),
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    db.session.add(new_odds)
                
                print(f"      ✅ 真實盤口: {totals.get('total_point', 8.5)}")
                fixed_count += 1
            else:
                # 使用合理的默認值
                default_total = calculate_default_total(game)
                
                if existing_odds:
                    existing_odds.total_point = default_total
                    existing_odds.over_price = -110
                    existing_odds.under_price = -110
                    existing_odds.bookmaker = 'estimated'
                    existing_odds.is_real = False
                    existing_odds.data_source = 'estimated'
                    existing_odds.updated_at = datetime.now()
                else:
                    new_odds = BettingOdds(
                        game_id=game.game_id,
                        bookmaker='estimated',
                        market_type='totals',
                        total_point=default_total,
                        over_price=-110,
                        under_price=-110,
                        is_real=False,
                        data_source='estimated',
                        odds_time=datetime.now(),
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    db.session.add(new_odds)
                
                print(f"      ⚠️  估計盤口: {default_total}")
                fixed_count += 1
        
        try:
            db.session.commit()
            print(f"  ✅ 修復完成，共修復 {fixed_count} 個盤口")
            return fixed_count
        except Exception as e:
            db.session.rollback()
            print(f"  ❌ 修復失敗: {e}")
            return 0

def match_team_names(team1, team2):
    """匹配球隊名稱"""
    if not team1 or not team2:
        return False
    
    # 簡單的名稱匹配
    team1_clean = team1.upper().strip()
    team2_clean = team2.upper().strip()
    
    # 直接匹配
    if team1_clean == team2_clean:
        return True
    
    # 球隊縮寫映射
    team_mapping = {
        'LAA': ['ANGELS', 'LOS ANGELES ANGELS'],
        'ATH': ['ATHLETICS', 'OAKLAND ATHLETICS'],
        'CWS': ['WHITE SOX', 'CHICAGO WHITE SOX'],
        'NYY': ['YANKEES', 'NEW YORK YANKEES'],
        'NYM': ['METS', 'NEW YORK METS'],
        'LAD': ['DODGERS', 'LOS ANGELES DODGERS'],
        'SF': ['GIANTS', 'SAN FRANCISCO GIANTS'],
        'TB': ['RAYS', 'TAMPA BAY RAYS'],
        'CWS': ['WHITE SOX', 'CHICAGO WHITE SOX'],
        'CHC': ['CUBS', 'CHICAGO CUBS']
    }
    
    # 檢查映射
    for abbr, full_names in team_mapping.items():
        if team1_clean == abbr and any(name in team2_clean for name in full_names):
            return True
        if team2_clean == abbr and any(name in team1_clean for name in full_names):
            return True
    
    return False

def calculate_default_total(game):
    """計算默認的大小分盤口"""
    # 基於球隊歷史數據計算合理的默認值
    # 這裡使用簡單的邏輯，可以後續改進
    
    # MLB平均總分約8.5分
    base_total = 8.5
    
    # 可以根據球隊特性調整
    high_scoring_teams = ['COL', 'TEX', 'BOS', 'NYY']  # 高得分球隊
    low_scoring_teams = ['SD', 'MIA', 'SEA', 'OAK']   # 低得分球隊
    
    if game.home_team in high_scoring_teams or game.away_team in high_scoring_teams:
        base_total += 0.5
    
    if game.home_team in low_scoring_teams and game.away_team in low_scoring_teams:
        base_total -= 0.5
    
    return base_total

def fix_recent_unknown_odds():
    """修復最近幾天的Unknown盤口"""
    print("🚀 開始修復最近的Unknown盤口...")
    
    # 修復最近7天的數據
    recent_dates = [date.today() - timedelta(days=i) for i in range(7)]
    
    total_fixed = 0
    for target_date in recent_dates:
        fixed_count = fix_unknown_odds_for_date(target_date)
        total_fixed += fixed_count
    
    print(f"\n✅ 總共修復了 {total_fixed} 個Unknown盤口")
    return total_fixed

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='修復Unknown博彩盤口')
    parser.add_argument('--date', type=str, help='指定日期 (YYYY-MM-DD)')
    parser.add_argument('--recent', action='store_true', help='修復最近7天')
    
    args = parser.parse_args()
    
    if args.date:
        try:
            target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
            fix_unknown_odds_for_date(target_date)
        except ValueError:
            print("❌ 日期格式錯誤，請使用 YYYY-MM-DD 格式")
    elif args.recent:
        fix_recent_unknown_odds()
    else:
        # 默認修復今天
        fix_unknown_odds_for_date(date.today())
