#!/usr/bin/env python3
"""
測試修復後的API調用
"""

from datetime import date, timedelta
from app import create_app
from models.data_fetcher import MLBDataFetcher

def test_fixed_api():
    print("🔧 測試修復後的API調用...")
    
    app = create_app()
    with app.app_context():
        fetcher = MLBDataFetcher()
        
        # 測試最近幾天的數據
        test_dates = [
            date(2025, 6, 24),
            date(2025, 6, 23),
            date(2025, 6, 22)
        ]
        
        for test_date in test_dates:
            print(f"\n📅 測試日期: {test_date}")
            
            try:
                games = fetcher.get_games_for_date(test_date)
                print(f"✅ 成功獲取 {len(games)} 場比賽")
                
                for game in games[:3]:  # 只顯示前3場
                    status_icon = "🏁" if game['game_status'] == 'completed' else "⏳"
                    score_info = ""
                    if game['home_score'] is not None and game['away_score'] is not None:
                        score_info = f"({game['away_score']}-{game['home_score']})"
                    
                    print(f"   {status_icon} {game['away_team']} @ {game['home_team']} {score_info}")
                    print(f"      ID: {game['game_id']}, 狀態: {game['game_status']}")
                
            except Exception as e:
                print(f"❌ 錯誤: {e}")

def test_direct_update():
    """測試直接更新6月24日數據"""
    print("\n🔄 測試直接更新6月24日數據...")
    
    app = create_app()
    with app.app_context():
        fetcher = MLBDataFetcher()
        
        try:
            target_date = date(2025, 6, 24)
            success = fetcher.fetch_games_by_date(target_date)
            
            if success:
                print("✅ 數據更新成功")
                
                # 檢查更新後的數據
                from models.database import Game
                games = Game.query.filter(Game.date == target_date).all()
                completed_games = [g for g in games if g.game_status == 'completed']
                
                print(f"📊 更新後狀態: {len(games)} 場比賽, {len(completed_games)} 場已完成")
                
                for game in games[:5]:
                    status_icon = "🏁" if game.game_status == 'completed' else "⏳"
                    score_info = ""
                    if game.home_score is not None and game.away_score is not None:
                        score_info = f"({game.away_score}-{game.home_score})"
                    
                    print(f"   {status_icon} {game.away_team} @ {game.home_team} {score_info}")
                    
            else:
                print("❌ 數據更新失敗")
                
        except Exception as e:
            print(f"❌ 更新錯誤: {e}")

if __name__ == "__main__":
    test_fixed_api()
    test_direct_update()
